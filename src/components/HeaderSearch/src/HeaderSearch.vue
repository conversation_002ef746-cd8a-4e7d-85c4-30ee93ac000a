<script lang="ts" setup>
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import Fuse from 'fuse.js'
import path from 'path-browserify'
interface FuseSearchOptions {
  item: searchOption
  refIndex: number
}
interface searchOption {
  path: string
  title: string[]
}
const search = ref('')
const options = ref([] as FuseSearchOptions[])
const show = ref(false)
// 获取有权限的路由
const permissionStore = usePermissionStoreWithOut()
const routes = computed(() => {
  return permissionStore.getRouters
})
const generateRoutes = (routes, basePath = '/', prefixTitle = [] as string[]) => {
  let res = [] as searchOption[]
  for (const router of unref(routes)) {
    // skip hidden router
    if (router.meta.hidden) {
      continue
    }
    const data = {
      path: path.resolve(basePath, router.path),
      title: [...prefixTitle] as string[]
    }
    if (router.meta?.title) {
      data.title = [...data.title, router.meta.title]
      if (router.redirect !== 'noRedirect') {
        // only push the routes with title
        // special case: need to exclude parent router without redirect
        res.push(data)
      }
    }
    // recursive child routes
    if (router.children) {
      const tempRoutes = generateRoutes(router.children, data.path, data.title)
      if (tempRoutes.length >= 1) {
        res = [...res, ...tempRoutes]
      }
    }
  }
  return res
}
// 扁平化可搜索的路由数据
const searchPool = ref([] as any)
onMounted(() => {
  searchPool.value = generateRoutes(routes)
})
watch(
  () => routes.value,
  () => {
    searchPool.value = generateRoutes(routes)
  }
)
// 模糊搜索
const fuse = ref()
const initFuse = (list) => {
  fuse.value = new Fuse(list, {
    shouldSort: true,
    threshold: 0.4,
    keys: [
      {
        name: 'title',
        weight: 0.7
      },
      {
        name: 'path',
        weight: 0.3
      }
    ]
  })
}
const querySearch = (query) => {
  if (query !== '') {
    options.value = unref(fuse).search(query)
  } else {
    options.value = []
  }
}
watch(
  () => searchPool.value,
  (val) => {
    initFuse(val)
  }
)
// 点击打开搜索
const click = () => {
  show.value = !show.value
  if (show.value) {
    unref(headerSearchSelect) && unref(headerSearchSelect).focus()
  }
}
// 关闭搜索
const headerSearchSelect = ref()
const close = () => {
  unref(headerSearchSelect) && unref(headerSearchSelect).blur()
  options.value = []
  show.value = false
}
watch(
  () => show.value,
  (val: boolean) => {
    if (val) {
      document.body.addEventListener('click', close)
    } else {
      document.body.removeEventListener('click', close)
    }
  }
)
// 路由跳转
const { push } = useRouter()
const change = async (path: string) => {
  push(path)
  search.value = ''
  options.value = []
  await nextTick()
  show.value = false
}
</script>
<template>
  <div :class="{ show: show }" class="header-search">
    <Icon
      icon="ant-design:search-outlined"
      color="#000"
      :size="18"
      class="search-icon"
      @click.stop="click"
    />
    <ElSelect
      ref="headerSearchSelect"
      v-model="search"
      filterable
      default-first-option
      remote
      :remote-method="querySearch"
      placeholder="Search"
      class="header-search-select"
      @change="change"
    >
      <ElOption
        v-for="option in options"
        :key="option.item.path"
        :value="option.item.path"
        :label="option.item.title.join(' > ')"
      />
    </ElSelect>
  </div>
</template>
<style lang="less" scoped>
.header-search {
  font-size: 0 !important;

  .search-icon {
    font-size: 18px;
    vertical-align: middle;
    cursor: pointer;
  }

  .header-search-select {
    display: inline-block;
    width: 0;
    overflow: hidden;
    font-size: 18px;
    vertical-align: middle;
    background: transparent;
    border-radius: 0;
    transition: width 0.2s;

    :deep(.el-input__wrapper) {
      padding-right: 0;
      padding-left: 0;
      vertical-align: middle;
      border: 0;
      border-bottom: 1px solid #d9d9d9;
      border-radius: 0;
      box-shadow: none !important;
    }
  }

  &.show {
    .header-search-select {
      width: 210px;
      margin-left: 10px;
    }
  }
}
</style>
