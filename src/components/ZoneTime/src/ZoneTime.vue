<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'

defineOptions({
  name: 'Paging'
})

const emits = defineEmits([
  'update:modelValue',
  'blurfrom',
  'focusfrom',
  'blurto',
  'changefrom',
  'changeto',
  'changefrom',
  'input',
  'focusto',
  'inputto',
  'inputfrom'
])
const props = defineProps({
  modelValue: propTypes.array.def([]),
  disabled: propTypes.bool.def(false),
  precision: propTypes.number.def(0)
})
const inputRange = ref([...props.modelValue])
const userInputForm: any = ref(inputRange.value ? inputRange.value[0] : null)
const userInputTo: any = ref(inputRange.value ? inputRange.value[1] : null)
const toPrecision = (num, precision) => {
  if (precision === undefined) precision = 0
  return parseFloat(Math.round(num * Math.pow(10, precision)) / Math.pow(10, precision) + '')
}
watch(
  () => props.modelValue,
  (val) => {
    if (val?.length) {
      userInputForm.value = val[0]
      userInputTo.value = val[1]
    } else {
      userInputForm.value = null
      userInputTo.value = null
    }
  }
)
const handleBlurFrom = (event) => {
  emits('blurfrom', event)
}

const handleFocusFrom = (event) => {
  emits('focusfrom', event)
}

const handleBlurTo = (event) => {
  emits('blurto', event)
}

const handleFocusTo = (event) => {
  emits('focusto', event)
}

const handleInputFrom = (value) => {
  emits('inputfrom', value)
}

const handleInputTo = (value) => {
  emits('inputto', value)
}

// // from输入框change事件
const handleInputChangeFrom = (value) => {
  // 如果是非数字空返回null
  if (isNaN(value) || value === '') {
    emits('input', [null, userInputTo.value])
    emits('changefrom', userInputForm.value)
    emits('update:modelValue', [userInputForm.value, userInputTo.value])
    return
  }

  //   // 初始化数字精度
  userInputForm.value = setPrecisionValue(value)

  //   // 如果from > to 将from值替换成to
  if (typeof userInputTo.value === 'number') {
    userInputForm.value =
      parseFloat(userInputForm.value) <= parseFloat(userInputTo.value)
        ? userInputForm.value
        : userInputTo.value
  }
  emits('input', [userInputForm.value, userInputTo.value])
  emits('changefrom', userInputForm.value)
  emits('update:modelValue', [userInputForm.value, userInputTo.value])
}

// // to输入框change事件
const handleInputChangeTo = (value) => {
  // 如果是非数字空返回null
  if (isNaN(value) || value === '') {
    emits('input', [userInputForm.value, null])
    emits('changefrom', userInputTo.value)
    emits('update:modelValue', [userInputForm.value, userInputTo.value])
    return
  }

  //   // 初始化数字精度
  userInputTo.value = setPrecisionValue(value)

  //   // 如果to < tfrom 将to值替换成from
  if (typeof userInputForm.value === 'number') {
    userInputTo.value =
      parseFloat(userInputTo.value) >= parseFloat(userInputForm.value)
        ? userInputTo.value
        : userInputForm.value
  }
  emits('input', [userInputForm.value, userInputTo.value])
  emits('changeto', userInputTo.value)
  emits('update:modelValue', [userInputForm.value, userInputTo.value])
}

// // 设置成精度数字
const setPrecisionValue = (value) => {
  if (props.precision !== undefined) {
    const val = toPrecision(value, props.precision)
    return val
  }
  return null
}
</script>
<template>
  <div class="input-number-range" :class="{ 'is-disabled': disabled }">
    <div class="flex">
      <div class="from">
        <el-input
          ref="input_from"
          v-model="userInputForm"
          type="text"
          oninput="value=value.replace(/[^0-9.]/g,'')"
          :disabled="disabled"
          @blur="handleBlurFrom"
          @focus="handleFocusFrom"
          @input="handleInputFrom"
          @change="handleInputChangeFrom"
        />
      </div>
      <div class="center">
        <span>~</span>
      </div>
      <div class="to">
        <el-input
          type="text"
          oninput="value=value.replace(/[^0-9.]/g,'')"
          ref="input_to"
          v-model="userInputTo"
          :disabled="disabled"
          @blur="handleBlurTo"
          @focus="handleFocusTo"
          @input="handleInputTo"
          @change="handleInputChangeTo"
        />
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
:deep(.el-input__wrapper) {
  box-shadow: none !important;
}

:deep(.el-input__inner) {
  padding: 0 2px;
  margin: 0;
  text-align: center !important;
  background-color: transparent;
  border: 0px !important;
}

.input-number-range {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.flex {
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: center;
  align-items: center;
  text-align: center;

  .center {
    margin-top: 1px;
  }
}

.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
  background-color: #eef0f6;
  border-color: #e4e7ed;
}

:deep(input[type='number'])::-webkit-inner-spin-button,
:deep(input[type='number'])::-webkit-outer-spin-button {
  margin: 0 !important;
  -webkit-appearance: none !important;
}
</style>
