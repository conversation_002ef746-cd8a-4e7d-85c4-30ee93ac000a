import type { FormInstance } from 'element-plus'
import { Attribute } from '@/components/PlmBase/type'

// 表单项配置接口
export interface FormFieldConfig extends Attribute {}

// 高级搜索条件接口
export interface AdvancedCondition {
  categoryName: string
  searchType: string
  value: any
  dataType: string
  type: string
  isBasicField?: boolean // 是否为基础字段
  fieldLabel?: string // 字段显示标签
}

// 组件实例接口
export interface AdvancedSearchFormInstance {
  formRef: FormInstance | undefined
  validate: () => Promise<boolean>
  resetFields: () => void
}
