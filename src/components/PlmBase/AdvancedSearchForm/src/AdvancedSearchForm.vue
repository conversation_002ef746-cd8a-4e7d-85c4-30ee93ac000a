<script setup lang="ts">
import { LayoutForm } from '@/components/LayoutForm'
import { Icon } from '@/components/Icon'
import { AdvancedCondition, FormFieldConfig } from '../src/types'
import { getPageAttributeList } from '@/api/systemConfiguration/type'
import { keyMap, tableSearchType, fieldTypeSearchOptions } from '@/components/PlmBase/const'
import FormItemRender from '@/components/PlmBase/component/FormItemRender.vue'
import { constraintMap } from '@/components/PlmBase/const'
import { Attribute } from '@/components/PlmBase/type'
import { FormatFormDataValue, saveFormDataValue } from '@/components/PlmBase/help'
import { ElSelect, ElOption } from 'element-plus'
const { t } = useI18n()
// 搜索配置缓存接口
interface SearchConfig {
  categoryName: string
  searchType: string
  dataType: string
}

// 缓存键名生成
const getCacheKey = (typeId: string) => `advanced_search_config_${typeId}`

defineOptions({
  name: 'AdvancedSearchForm'
})

// 组件属性
const props = withDefaults(
  defineProps<{
    typeId: string
    // 表单数据
    modelValue: Record<string, any>
    // 加载状态
    loading?: boolean
    // 是否显示高级搜索
    showAdvancedSearch?: boolean
  }>(),
  {
    formConfig: () => [],
    modelValue: () => ({}),
    loading: false,
    showAdvancedSearch: true
  }
)

// 事件
const emit = defineEmits<{
  (e: 'update:modelValue', val: Record<string, any>): void
  (e: 'search', conditions: AdvancedCondition[]): void
  (e: 'reset'): void
  (e: 'advanced-search', conditions: AdvancedCondition[]): void
}>()

const layoutFormRef = ref<InstanceType<typeof LayoutForm>>()

// 表单数据
const formData = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
// 高级搜索面板可见性
const advancedSearchVisible = ref(false)

const basicField = ref<FormFieldConfig[]>([])
const deepSearchField = ref<FormFieldConfig[]>()
// 高级搜索条件
const searchConditions = ref<AdvancedCondition[]>([])
// 初始化高级搜索条件
const initAdvancedConditions = async () => {
  if (!props.typeId) {
    return false
  }
  const [error, result] = await getPageAttributeList({ typeCategoryId: props.typeId })
  if (error === null && result?.data) {
    //默认搜索
    basicField.value = result.data.filter(
      (item) => item.tableSearch == tableSearchType.defaultSearch
    )
    //高级搜索
    deepSearchField.value = result.data.filter((item) => item.tableSearch == tableSearchType.search)
    formatFormData([...basicField.value])

    // 初始化高级搜索条件，包含基础字段
    initAdvancedSearchConditions()

    // 在字段加载完成后，尝试加载缓存的搜索配置
    nextTick(() => {
      loadSearchConfig()
    })
  }
}

// 初始化高级搜索条件（包含基础字段）
const initAdvancedSearchConditions = () => {
  // 保留非基础字段的条件
  const nonBasicConditions = searchConditions.value.filter((c) => !c.isBasicField)
  searchConditions.value = []
  // 添加基础字段作为固定条件
  basicField.value.forEach((field) => {
    const dataType = getFieldDataType(field.value!, true) // 传入true表示查找基础字段
    const currentFormValue = formData.value[field.value!]

    // 检查是否为区间类型，设置默认搜索类型
    let defaultSearchType = 'EQUAL'
    try {
      const constraintList = JSON.parse(field.constraintValue || '{}')
      const isRange = constraintList?.[keyMap.isRange]?.value === true
      if (isRange) {
        defaultSearchType = 'FROMTO'
      } else {
        // 根据字段类型设置默认搜索类型
        const searchOptions = getSearchTypeOptions(field.value!, true)
        defaultSearchType = searchOptions.length > 0 ? searchOptions[0].value : 'EQUAL'
      }
    } catch (error) {
      // 使用默认值
    }

    searchConditions.value.push({
      categoryName: field.value!,
      searchType: defaultSearchType, // 根据字段类型设置默认搜索类型
      value:
        currentFormValue !== undefined
          ? currentFormValue
          : getDefaultValueByField(field.value!, true),
      dataType: dataType,
      isBasicField: true, // 标记为基础字段
      type: field.type,
      fieldLabel: field.label || field.name
    })
  })

  // 恢复非基础字段的条件
  searchConditions.value.push(...nonBasicConditions)

  // 如果没有高级字段条件且有可用的高级字段，添加一个空条件
  if (
    nonBasicConditions.length === 0 &&
    deepSearchField.value &&
    deepSearchField.value.length > 0
  ) {
    addCondition()
  }
}
const formatFormData = (list: Attribute[]) => {
  // 创建新的formData对象，确保响应式
  const newFormData = { ...props.modelValue }
  list.forEach((item: Attribute) => {
    if (!item.value) return
    // 使用FormatFormDataValue进一步处理
    if (item.type) {
      const formDataRef = ref(newFormData)
      const data = JSON.parse(item.constraintValue || '{}')
      FormatFormDataValue(constraintMap.typeMap?.[item.type], formDataRef, data, item.value)
    }
  })
  // 通过emit更新formData，确保响应式
  emit('update:modelValue', newFormData)
}

// 获取字段的约束配置
const getFieldConstraints = (fieldValue: string) => {
  // 先从基础字段中查找
  let field = basicField.value?.find((f) => f.value === fieldValue)

  // 如果基础字段中没有，再从高级搜索字段中查找
  if (!field) {
    field = deepSearchField.value?.find((f) => f.value === fieldValue)
  }

  if (!field) return {}
  try {
    return JSON.parse(field.constraintValue || '{}')
  } catch (error) {
    console.warn('解析字段约束配置失败:', error)
    return {}
  }
}

// 获取字段的数据类型
const getFieldDataType = (fieldValue: string, isBasic = false) => {
  // 如果是基础字段，从basicField中查找
  if (isBasic) {
    const field = basicField.value?.find((f) => f.value === fieldValue)
    return (field?.type && constraintMap.componentTypeMap?.[field.type]) || ''
  }

  // 否则从deepSearchField中查找
  const field = deepSearchField.value?.find((f) => f.value === fieldValue)
  return (field?.type && constraintMap.componentTypeMap?.[field.type]) || ''
}
// 来处理数据类型，能够更准确地根据字段约束配置确定默认值类型
const getDefaultValueByField = (fieldValue: string, isBasic = false) => {
  if (!fieldValue) return ''

  // 获取字段配置
  let field = isBasic
    ? basicField.value?.find((f) => f.value === fieldValue)
    : deepSearchField.value?.find((f) => f.value === fieldValue)

  if (!field) {
    console.warn(`字段 ${fieldValue} 未找到配置信息`)
    return ''
  }

  try {
    // 创建临时的formData ref用于FormatFormDataValue处理
    const tempFormData = ref({ [fieldValue]: '' })
    const constraintType = field.type && constraintMap.typeMap?.[field.type]
    const constraintList = JSON.parse(field.constraintValue || '{}')

    if (constraintType) {
      // 使用FormatFormDataValue来确定正确的默认值类型
      // 这样可以处理区间、多选、附件等复杂类型的默认值
      FormatFormDataValue(constraintType, tempFormData, constraintList, fieldValue)
      return tempFormData.value[fieldValue]
    }

    // 如果没有约束类型，返回空字符串
    return ''
  } catch (error) {
    console.warn(`处理字段 ${fieldValue} 默认值时出错:`, error)
    return ''
  }
}

// 根据字段类型获取搜索类型选项
const getSearchTypeOptions = (fieldValue: string, isBasic = false) => {
  if (!fieldValue) return []

  // 获取字段配置
  let field = isBasic
    ? basicField.value?.find((f) => f.value === fieldValue)
    : deepSearchField.value?.find((f) => f.value === fieldValue)

  if (!field) return []

  try {
    const constraintList = JSON.parse(field.constraintValue || '{}')
    const isRange = constraintList?.[keyMap.isRange]?.value === true

    // 如果是区间类型，只返回区间选项
    if (isRange) {
      return [{ label: '自至', value: 'FROMTO' }]
    }

    // 根据字段类型返回对应的搜索类型选项
    const fieldType = field.type
    if (fieldType && fieldTypeSearchOptions[fieldType]) {
      return fieldTypeSearchOptions[fieldType]
    }

    // 如果没有匹配的字段类型，返回字符串类型的选项作为默认值
    return fieldTypeSearchOptions.STRING || []
  } catch (error) {
    console.warn(`获取字段 ${fieldValue} 搜索类型选项时出错:`, error)
    return fieldTypeSearchOptions.STRING || []
  }
}

// 检查条件是否重复
const isDuplicateCondition = (columnName: string, searchType: string, excludeIndex?: number) => {
  return searchConditions.value.some((condition, index) => {
    if (excludeIndex !== undefined && index === excludeIndex) {
      return false
    }
    return condition.categoryName === columnName && condition.searchType === searchType
  })
}

// 获取可用的字段选项（排除已选择的）
const getAvailableFields = (currentIndex: number) => {
  const currentCondition = searchConditions.value[currentIndex]
  const arrList = Array.isArray(deepSearchField.value)
    ? [...deepSearchField.value, ...basicField.value]
    : basicField.value
  return (
    arrList?.filter((field) => {
      // 如果是当前条件的字段，允许选择
      if (field.value === currentCondition.categoryName) {
        return true
      }
      // 检查是否已被其他条件使用
      return !searchConditions.value.some(
        (condition, index) => index !== currentIndex && condition.categoryName === field.value
      )
    }) || []
  )
}
// 字段变化处理
const handleFieldChange = (condition: AdvancedCondition) => {
  const dataType = getFieldDataType(condition.categoryName)
  const field = deepSearchField.value?.find((f) => f.value === condition.categoryName) || {}
  condition.type = field.type

  // 检查是否为区间类型
  try {
    const constraintList = JSON.parse(field.constraintValue || '{}')
    const isRange = constraintList?.[keyMap.isRange]?.value === true

    // 重置搜索类型和值
    // 如果是区间类型，默认设置为FROMTO；否则根据字段类型设置默认搜索类型
    if (isRange) {
      condition.searchType = 'FROMTO'
    } else {
      // 获取该字段类型的搜索选项，设置第一个作为默认值
      const searchOptions = getSearchTypeOptions(condition.categoryName, false)
      condition.searchType = searchOptions.length > 0 ? searchOptions[0].value : 'EQUAL'
    }
  } catch (error) {
    condition.searchType = 'EQUAL' // 默认为等于
  }

  condition.value = getDefaultValueByField(condition.categoryName, false)
  condition.dataType = dataType
}

// 搜索类型变化处理
const handleSearchTypeChange = (condition: AdvancedCondition, index: number) => {
  // 检查是否重复
  if (condition.categoryName && condition.searchType) {
    if (isDuplicateCondition(condition.categoryName, condition.searchType, index)) {
      ElMessage.warning('该字段的此搜索类型已存在，请选择其他搜索类型')
      condition.searchType = ''
      return
    }
  }

  // 如果是基础字段，保持当前值，否则重置值
  if (!condition.isBasicField) {
    condition.value = getDefaultValueByField(condition.categoryName, false)
  }
}

// 处理基础字段值变化
const handleBasicFieldValueChange = (condition: AdvancedCondition) => {
  if (condition.isBasicField && condition.categoryName) {
    // 同步到formData
    const newFormData = { ...formData.value }
    newFormData[condition.categoryName] = condition.value
    emit('update:modelValue', newFormData)
  }
}

// 添加搜索条件
const addCondition = () => {
  // 检查是否还有可用字段
  const usedFields = searchConditions.value.map((c) => c.categoryName).filter(Boolean)
  const availableFields = deepSearchField.value?.filter((f) => !usedFields.includes(f.value!)) || []

  if (availableFields.length === 0) {
    ElMessage.warning('所有可搜索字段都已添加')
    return
  }

  searchConditions.value.push({
    categoryName: '',
    searchType: '',
    type: '',
    value: '',
    dataType: ''
  })
}

// 移除搜索条件
const removeCondition = (index: number) => {
  const condition = searchConditions.value[index]

  // 基础字段不能删除
  if (condition.isBasicField) {
    ElMessage.warning('基础搜索字段不能删除')
    return
  }

  searchConditions.value.splice(index, 1)

  // 确保至少有一个非基础字段的条件
  const nonBasicConditions = searchConditions.value.filter((c) => !c.isBasicField)
  if (
    nonBasicConditions.length === 0 &&
    deepSearchField.value &&
    deepSearchField.value.length > 0
  ) {
    addCondition()
  }
}

// 切换高级搜索面板
const toggleAdvancedSearch = () => {
  advancedSearchVisible.value = !advancedSearchVisible.value
  if (advancedSearchVisible.value && searchConditions.value.length === 0) {
    initAdvancedConditions()
  }
}

// 执行高级搜索
const handleAdvancedSearch = () => {
  const conditionFormat: AdvancedCondition[] = getDeepSearchParams()
  emit('advanced-search', conditionFormat)
  //保存搜索配置到缓存
  saveSearchConfig()
  advancedSearchVisible.value = false
}
// 重置高级搜索
const resetAdvancedSearch = () => {
  // 清空所有搜索条件
  searchConditions.value = []

  // 重新初始化基础字段，使用默认值
  basicField.value.forEach((field) => {
    const dataType = getFieldDataType(field.value!, true)

    // 检查是否为区间类型，设置默认搜索类型
    let defaultSearchType = 'EQUAL'
    try {
      const constraintList = JSON.parse(field.constraintValue || '{}')
      const isRange = constraintList?.[keyMap.isRange]?.value === true
      if (isRange) {
        defaultSearchType = 'FROMTO'
      } else {
        // 根据字段类型设置默认搜索类型
        const searchOptions = getSearchTypeOptions(field.value!, true)
        defaultSearchType = searchOptions.length > 0 ? searchOptions[0].value : 'EQUAL'
      }
    } catch (error) {
      // 使用默认值
    }

    searchConditions.value.push({
      categoryName: field.value!,
      searchType: defaultSearchType,
      value: getDefaultValueByField(field.value!, true), // 使用默认值而不是当前表单值
      dataType: dataType,
      isBasicField: true,
      type: field.type,
      fieldLabel: field.label || field.name
    })
  })

  // 添加一个空的高级搜索条件
  if (deepSearchField.value && deepSearchField.value.length > 0) {
    addCondition()
  }

  // 同时重置基础表单
  if (layoutFormRef.value?.formRef) {
    layoutFormRef.value.formRef.resetFields()
  }

  // 触发重置事件
  emit('reset')
}

// 重置并清除缓存
const resetAndClearCache = () => {
  ElMessageBox.confirm('是否同时清除已保存的搜索配置？', '重置确认', {
    confirmButtonText: '清除缓存',
    cancelButtonText: '仅重置',
    type: 'warning',
    distinguishCancelAndClose: true
  })
    .then(() => {
      // 清除缓存并重置
      clearSearchConfig()
      resetAdvancedSearch()
      ElMessage.success('已重置并清除搜索配置缓存')
    })
    .catch((action) => {
      if (action === 'cancel') {
        // 仅重置，不清除缓存
        resetAdvancedSearch()
        ElMessage.success('已重置搜索条件')
      }
    })
}
// 保存搜索配置到缓存（不包含value值）
const saveSearchConfig = () => {
  if (!props.typeId) return

  const config: SearchConfig[] = searchConditions.value
    .filter((condition) => condition.categoryName && condition.searchType)
    .map((condition) => ({
      categoryName: condition.categoryName,
      searchType: condition.searchType,
      dataType: constraintMap.componentTypeMap?.[condition.dataType] || ''
    }))

  if (config.length > 0) {
    const cacheKey = getCacheKey(props.typeId)
    try {
      localStorage.setItem(cacheKey, JSON.stringify(config))
    } catch (error) {
      console.warn('保存搜索配置失败:', error)
    }
  }
}

// 从缓存加载搜索配置
const loadSearchConfig = () => {
  if (!props.typeId) return

  const cacheKey = getCacheKey(props.typeId)
  try {
    const cachedConfig = localStorage.getItem(cacheKey)
    if (cachedConfig) {
      const config: SearchConfig[] = JSON.parse(cachedConfig)

      // 验证缓存的配置是否仍然有效（字段是否还存在）
      const validConfig = config.filter((item) => {
        return deepSearchField.value?.some((field) => field.value === item.categoryName)
      })

      if (validConfig.length > 0) {
        // 先重新初始化基础字段
        initAdvancedSearchConditions()

        // 根据缓存配置创建高级搜索条件
        validConfig.forEach((configItem) => {
          // 检查是否为基础字段
          const isBasicField = basicField.value?.some((f) => f.value === configItem.categoryName)

          if (isBasicField) {
            // 如果是基础字段，更新现有条件的searchType
            const existingCondition = searchConditions.value.find(
              (c) => c.categoryName === configItem.categoryName && c.isBasicField
            )
            if (existingCondition) {
              existingCondition.searchType = configItem.searchType
              existingCondition.value = getDefaultValueByField(configItem.categoryName, true)
            }
          } else {
            // 如果是高级字段，添加新条件
            const dataType = getFieldDataType(configItem.categoryName)
            searchConditions.value.push({
              categoryName: configItem.categoryName,
              searchType: configItem.searchType,
              value: getDefaultValueByField(configItem.categoryName, false),
              dataType: constraintMap.componentTypeMap?.[dataType] || '',
              isBasicField: false,
              type: dataType
            })
          }
        })
        ElMessage.success(`已恢复 ${validConfig.length} 个搜索条件配置`)
      }
    }
  } catch (error) {
    console.warn('加载搜索配置失败:', error)
  }
}

// 清除搜索配置缓存
const clearSearchConfig = () => {
  if (!props.typeId) return

  const cacheKey = getCacheKey(props.typeId)
  try {
    localStorage.removeItem(cacheKey)
  } catch (error) {
    console.warn('清除搜索配置缓存失败:', error)
  }
}
// 执行基本搜索
const handleSearch = async () => {
  if (!layoutFormRef.value) return
  await layoutFormRef.value.validate()
  handleAdvancedSearch()
}

// 重置表单
const handleReset = () => {
  if (!layoutFormRef.value) return
  layoutFormRef.value?.formRef?.resetFields()
  emit('reset')
}

// 获取高级搜索的参数
const getDeepSearchParams = () => {
  // 验证条件是否有效
  const validConditions = searchConditions.value.filter(
    (condition) =>
      condition.categoryName &&
      condition.searchType &&
      condition.value !== undefined &&
      condition.value !== ''
  )
  // 检查重复条件
  const duplicates = validConditions.filter(
    (condition, index) =>
      validConditions.findIndex(
        (c) => c.categoryName === condition.categoryName && c.searchType === condition.searchType
      ) !== index
  )

  if (duplicates.length > 0) {
    ElMessage.error('存在重复的搜索条件，请检查')
    return
  }
  // 需要处理
  let conditionFormat = JSON.parse(JSON.stringify(validConditions))
  const dataFormat = {}
  conditionFormat.forEach((item) => {
    dataFormat[item.categoryName] = item.value
  })
  //搜索特殊处理
  const arrList = Array.isArray(deepSearchField.value)
    ? [...deepSearchField.value, ...basicField.value]
    : basicField.value
  saveFormDataValue(arrList, dataFormat)
  conditionFormat.forEach((item) => {
    const constraintList = getFieldConstraints(item.categoryName)
    if (
      item.dataType === constraintMap.componentTypeMap.OBJECT ||
      constraintList?.[keyMap.isRange]?.value
    ) {
      item.value =
        dataFormat[item.categoryName]?.length > 0
          ? JSON.stringify(dataFormat[item.categoryName])
          : null
    } else if (item.dataType === constraintMap.componentTypeMap.DATE_TIME) {
      item.value = item.value ? item.value.toString() : null
    } else {
      item.value = dataFormat[item.categoryName]
    }
  })
  return conditionFormat
}
// 监听高级搜索条件变化，同步基础字段到formData
watch(
  () => searchConditions.value,
  (newConditions) => {
    // 只处理基础字段的变化
    newConditions.forEach((condition) => {
      if (condition.isBasicField && condition.categoryName) {
        // 如果基础字段有值，同步到formData
        const currentFormValue = formData.value[condition.categoryName]
        if (condition.value !== currentFormValue) {
          const newFormData = { ...formData.value }
          newFormData[condition.categoryName] = condition.value
          emit('update:modelValue', newFormData)
        }
      }
    })
  },
  { deep: true }
)
// 监听formData变化，同步到高级搜索的基础字段
watch(
  () => formData.value,
  (newFormData) => {
    // 同步formData的变化到高级搜索的基础字段
    searchConditions.value.forEach((condition) => {
      if (condition.isBasicField && condition.categoryName) {
        condition.value = newFormData[condition.categoryName]
      }
    })
  },
  { deep: true }
)

// 组件挂载时初始化（不自动搜索）
onMounted(() => {
  initAdvancedConditions()
})
// 监听typeId变化，重新初始化
watch(
  () => props.typeId,
  (newTypeId) => {
    if (newTypeId) {
      initAdvancedConditions()
    }
  }
)
// 暴露方法
defineExpose({
  layoutFormRef,
  validate: async () => {
    if (!layoutFormRef.value) return false
    return await layoutFormRef.value.validate()
  },
  resetFields: () => {
    layoutFormRef.value?.formRef?.resetFields()
  },
  getDeepSearchParams,
  handleSearch
})
</script>

<template>
  <div class="advanced-search-form">
    <!-- 基本搜索表单 -->
    <LayoutForm
      ref="layoutFormRef"
      :model="formData"
      :loading="loading"
      query-form
      :span="6"
      @reset="handleReset"
      @search="handleSearch"
    >
      <template v-for="(attribute, index) in basicField" :key="index">
        <ElFormItem :label="attribute.label" :prop="attribute.value">
          <FormItemRender
            :constraintType="(attribute.type && constraintMap.typeMap?.[attribute.type]) || ''"
            :constraintList="JSON.parse(attribute.constraintValue || '{}')"
            :field="attribute"
            v-model="formData[attribute.value]"
          />
        </ElFormItem>
      </template>
      <!-- 高级搜索按钮 -->
      <template #button v-if="showAdvancedSearch">
        <ElButton type="primary" @click="toggleAdvancedSearch"> 高级搜索 </ElButton>
      </template>
    </LayoutForm>
    <!-- 高级搜索抽屉 -->
    <ElDrawer
      v-model="advancedSearchVisible"
      title="高级搜索"
      size="600px"
      :with-header="true"
      :destroy-on-close="false"
      close-on-click-modal
    >
      <div class="drawer-content">
        <div class="conditions">
          <div
            v-for="(condition, index) in searchConditions"
            :key="index"
            class="condition-item"
            :class="{ 'basic-field-item': condition.isBasicField }"
          >
            <ElRow :gutter="10" style="width: 100%">
              <ElCol :span="5">
                <ElSelect
                  v-model="condition.categoryName"
                  placeholder="选择字段"
                  class="field-select"
                  @change="handleFieldChange(condition, index)"
                  :clearable="!condition.isBasicField"
                  :disabled="condition.isBasicField"
                >
                  <ElOption
                    v-for="field in getAvailableFields(index)"
                    :key="field.value"
                    :label="field.label || field.name"
                    :value="field.value"
                    :disabled="field.value === condition.categoryName ? false : isDuplicateCondition(field.value!, '', index)"
                  />
                </ElSelect>
              </ElCol>
              <ElCol :span="4">
                <ElSelect
                  v-model="condition.searchType"
                  clearable
                  :disabled="!condition.categoryName"
                  :placeholder="condition.categoryName ? '选择搜索类型' : '请先选择字段'"
                  @change="handleSearchTypeChange(condition, index)"
                >
                  <ElOption
                    v-for="option in getSearchTypeOptions(
                      condition.categoryName,
                      condition.isBasicField
                    )"
                    :key="option.value"
                    :value="option.value"
                    >{{ t(option.label) }}</ElOption
                  >
                </ElSelect>
              </ElCol>
              <ElCol :span="12">
                <template v-if="condition.categoryName">
                  <FormItemRender
                    :constraintType="condition.type"
                    :constraintList="getFieldConstraints(condition.categoryName)"
                    :field="condition.categoryName"
                    v-model="condition.value"
                    :disabled="!condition.searchType"
                    :placeholder="condition.searchType ? '请输入搜索值' : '请先选择搜索类型'"
                    @update:model-value="
                      condition.isBasicField ? handleBasicFieldValueChange(condition) : undefined
                    "
                  />
                </template>

                <ElInput v-else disabled placeholder="请先选择字段" />
              </ElCol>
              <ElCol :span="3">
                <div class="condition-actions">
                  <ElButton
                    v-if="!condition.isBasicField"
                    type="danger"
                    circle
                    plain
                    size="small"
                    @click="removeCondition(index)"
                    :disabled="searchConditions.filter((c) => !c.isBasicField).length === 1"
                  >
                    <Icon icon="ep:delete" />
                  </ElButton>
                  <ElTooltip v-else content="基础字段不能删除" placement="top">
                    <ElButton type="info" circle plain size="small" disabled>
                      <Icon icon="ep:lock" />
                    </ElButton>
                  </ElTooltip>
                </div>
              </ElCol>
            </ElRow>
          </div>
        </div>

        <div class="add-condition">
          <ElButton type="primary" @click="addCondition">
            <Icon icon="ep:plus" />
            添加条件
          </ElButton>
        </div>

        <ElDivider />

        <div class="drawer-footer">
          <ElButton @click="resetAdvancedSearch">重置</ElButton>
          <ElButton @click="resetAndClearCache">清除缓存</ElButton>
          <ElButton type="primary" @click="handleAdvancedSearch">查询</ElButton>
          <ElButton @click="toggleAdvancedSearch">取消</ElButton>
        </div>
      </div>
    </ElDrawer>
  </div>
</template>

<style scoped lang="less">
.advanced-search-form {
  position: relative;
  width: 100%;
}

.drawer-content {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.conditions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  overflow-y: auto;
}

.basic-field-label {
  display: flex;
  margin-bottom: 8px;
  align-items: center;
  gap: 8px;
}

.field-display-name {
  margin-top: 4px;
  font-size: 12px;
  font-style: italic;
  color: var(--el-text-color-secondary);
}

.condition-actions {
  display: flex;
  justify-content: center;
  align-items: center;
}

.operator-select {
  width: 120px;
}

.value-input {
  flex: 1;
}

.add-condition {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  margin-bottom: 16px;

  .el-button {
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}
</style>
