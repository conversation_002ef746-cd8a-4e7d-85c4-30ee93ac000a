# AdvancedSearchForm 高级搜索表单组件

这是一个结合了 LayoutForm 和高级搜索功能的组件，支持动态配置表单项和高级查询条件。高级搜索使用 Drawer 抽屉组件实现，提供更好的用户体验。

## 特性

- 基于 LayoutForm 组件，支持响应式布局
- 支持基本搜索和高级搜索两种模式
- 高级搜索使用 Drawer 抽屉组件，界面更清晰
- 高级搜索支持多条件组合查询
- 支持动态配置表单项
- 支持自定义表单组件
- TypeScript 类型支持

## 使用方法

### 基本用法

```vue
<template>
  <AdvancedSearchForm
    v-model="formData"
    :form-config="formConfig"
    :loading="loading"
    @search="handleSearch"
    @advanced-search="handleAdvancedSearch"
    @reset="handleReset"
  ></AdvancedSearchForm>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  AdvancedSearchForm,
  type FormFieldConfig,
  type AdvancedCondition
} from '@/components/AdvancedSearchForm'
// 表单配置
const formConfig: (
  | {
      label: string
      field: string
      component: string
      span: number
      props: { placeholder: string }
    }
  | {
      label: string
      field: string
      component: string
      span: number
      options: ({ label: string; value: string } | { label: string; value: string })[]
    }
)[] = [
  {
    label: '名称',
    field: 'name',
    component: 'ElInput',
    span: 6,
    props: {
      placeholder: '请输入名称'
    }
  },
  {
    label: '类型',
    field: 'type',
    component: 'ElSelect',
    span: 6,
    options: [
      { label: '类型A', value: 'A' },
      { label: '类型B', value: 'B' }
    ]
  }
]

// 表单数据
const formData = reactive({
  name: '',
  type: ''
})

// 加载状态
const loading = ref(false)

// 处理基本搜索
const handleSearch = (data: Record<string, any>) => {
  console.log('基本搜索:', data)
  // 执行搜索逻辑
}

// 处理高级搜索
const handleAdvancedSearch = (conditions: AdvancedCondition[]) => {
  console.log('高级搜索条件:', conditions)
  // 执行高级搜索逻辑
}

// 重置搜索
const handleReset = () => {
  console.log('重置搜索')
  // 重置逻辑
}
</script>
```

### 配置项说明

#### 组件属性

| 属性名             | 类型                | 默认值 | 说明                              |
| ------------------ | ------------------- | ------ | --------------------------------- |
| formConfig         | FormFieldConfig[]   | []     | 表单配置项数组                    |
| modelValue         | Record<string, any> | {}     | 表单数据对象（使用 v-model 绑定） |
| advancedConditions | AdvancedCondition[] | []     | 高级搜索条件数组                  |
| loading            | boolean             | false  | 加载状态                          |
| showAdvancedSearch | boolean             | true   | 是否显示高级搜索按钮              |

#### 事件

| 事件名            | 参数                            | 说明             |
| ----------------- | ------------------------------- | ---------------- |
| update:modelValue | formData: Record<string, any>   | 表单数据更新事件 |
| search            | formData: Record<string, any>   | 基本搜索事件     |
| reset             | -                               | 重置事件         |
| advanced-search   | conditions: AdvancedCondition[] | 高级搜索事件     |

#### FormFieldConfig 类型

```typescript
interface FormFieldConfig {
  label: string // 表单项标签
  field: string // 表单项字段名
  component: any // 表单项组件
  span?: number // 栅格宽度
  props?: Record<string, any> // 组件属性
  options?: Array<{ label: string; value: any; key?: string }> // 选项（用于下拉框等）
  isAdvanced?: boolean // 是否为高级搜索项
}
```

#### AdvancedCondition 类型

```typescript
interface AdvancedCondition {
  field: string // 字段名
  operator: string // 操作符（eq, ne, like, gt, lt, ge, le）
  value: any // 值
}
```

### 方法

可以通过 ref 获取组件实例，调用以下方法：

```typescript
interface AdvancedSearchFormInstance {
  formRef: FormInstance | undefined // Element Plus 表单实例
  validate: () => Promise<boolean> // 验证表单
  resetFields: () => void // 重置表单字段
}
```

## 示例

完整示例请参考 `src/views/system-configuration/components/AdvancedSearchDemo.vue`。
