<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { constraintMap, keyMap } from '@/components/PlmBase/const'
import {
  ElCol,
  ElForm,
  ElFormItem,
  ElRow,
  ElMessage,
  type FormInstance,
  type FormRules
} from 'element-plus'
import FormItemRender from '@/components/PlmBase/component/FormItemRender.vue'
import { Attribute, AttributeData } from '@/components/PlmBase/type'
import { getSizeAndStandardValueByCode } from '@/api/NewProductLibraryManage/detail'
const { typeMap } = constraintMap
defineOptions({
  name: 'FormRender'
})
interface Props {
  modelValue: AttributeData
  gridItems?: Attribute[][]
  disabled?: boolean
  readonly?: boolean
  showValidateMessage?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  readonly: false,
  showValidateMessage: true
})

const emit = defineEmits<{
  'update:modelValue': [value: AttributeData]
  validate: [valid: boolean, errors?: any]
  change: [value: AttributeData]
  sizeChange: [value: any[]]
}>()

// 表单引用和字段引用
const formRef = ref<FormInstance>()
const fieldRefs = ref<Record<string, any>>({})

// 本地值管理
const localFormData = computed({
  get: () => props.modelValue || {},
  set: (value: AttributeData) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

// 解析约束配置
const parseConstraintValue = (constraintValue: string): Record<string, any> => {
  try {
    return JSON.parse(constraintValue || '{}')
  } catch {
    return {}
  }
}

// 解析默认值
const parseDefaultValue = (defaultValue?: string): any => {
  if (!defaultValue) return undefined

  try {
    // 尝试解析为 JSON 对象
    const parsed = JSON.parse(defaultValue)
    return parsed
  } catch {
    // 如果解析失败，直接返回字符串值
    return defaultValue
  }
}

// 生成表单验证规则
const formRules = computed<FormRules>(() => {
  const rules: FormRules = {}
  if (!props.gridItems) return rules
  props.gridItems.forEach((attributeList) => {
    attributeList.forEach((attribute) => {
      const constraints = parseConstraintValue(attribute.constraintValue || '{}')
      const fieldRules: any[] = []

      // 必填验证
      if (constraints[keyMap.required]?.value) {
        fieldRules.push({
          required: true,
          message: `请输入${attribute.name}`,
          trigger: ['blur', 'change']
        })
      }

      if (fieldRules.length > 0) {
        rules[attribute.value!] = fieldRules
      }
    })
  })
  return rules
})

// 表单验证方法
const validate = (): Promise<boolean> => {
  return new Promise((resolve) => {
    if (!formRef.value) {
      resolve(false)
      return
    }

    formRef.value.validate((valid) => {
      emit('validate', valid)
      if (!valid && props.showValidateMessage) {
        ElMessage.error('表单验证失败，请检查输入内容')
      }
      resolve(valid)
    })
  })
}

// 其他表单方法
const clearValidate = (fields?: string | string[]) => formRef.value?.clearValidate(fields)
const resetFields = () => formRef.value?.resetFields()

// 尺码选项数据
const sizeOptions = ref<Record<string, any[]>>({})

// 获取ApiSelect组件的选项数据
const getApiSelectOptions = (fieldName: string): any[] => {
  return fieldRefs.value[fieldName]?.formatOptions || []
}

// 尺码联动处理
const handleSizeRangeChange = (sizeRangeId: string | string[]) => {
  // 重置数据
  if (!sizeRangeId || (Array.isArray(sizeRangeId) && sizeRangeId.length === 0)) {
    localFormData.value.selectedSize = []
    localFormData.value.standardSizeId = []
    sizeOptions.value = {}
    emit('sizeChange', [])
    return
  }

  // 延迟获取数据，确保ApiSelect已加载
  setTimeout(() => {
    const options = getApiSelectOptions('sizeRangeId')
    if (!options.length) return

    const ids = Array.isArray(sizeRangeId) ? sizeRangeId : [sizeRangeId]
    const selectedOptions = options.filter((option) => ids.includes(option.value))
    if (selectedOptions.length > 0) {
      const standardList = selectedOptions.flatMap((e) => e.standardList || [])
      const sizeValueList = selectedOptions.flatMap((e) => e.sizeValueList || [])
      sizeValueList.forEach((item) => {
        // 防止多次赋值产生的问题
        if (!item.label.includes('【')) {
          item.label = `${item.sizeValue}【${item.label}】`
        }
      })

      // 更新选项数据
      sizeOptions.value = {
        selectedSize: sizeValueList,
        standardSizeId: standardList
      }

      // 更新表单数据
      localFormData.value.standardSizeId = standardList.map((e) => e.value)
      localFormData.value.selectedSize = sizeValueList.map((e) => e.value)
      emit('sizeChange', sizeValueList)
    } else {
      emit('sizeChange', [])
    }
  }, 300)
}

// 处理字段变化
const handleFieldChange = (fieldName: string, value: any) => {
  if (fieldName === 'sizeRangeId') {
    handleSizeRangeChange(value)
  }
}

// 监听尺码段变化
watch(
  () => localFormData.value.sizeRangeId,
  (newValue) => newValue && handleSizeRangeChange(newValue),
  { immediate: true }
)

// 监听ApiSelect组件加载完成
watch(
  () => fieldRefs.value.sizeRangeId,
  (newRef) => {
    if (newRef && localFormData.value.sizeRangeId) {
      handleSizeRangeChange(localFormData.value.sizeRangeId)
    }
  }
)
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue?.productCode) {
      getSizeColor()
    }
  },
  { immediate: true }
)
const getSizeColor = async () => {
  if (props.modelValue?.productCode) {
    const result = await getSizeAndStandardValueByCode({
      code: localFormData.value.productCode
    })
    const sizeValueList = (result.data || []).flatMap((e) => e.sizeValueList || [])
    sizeOptions.value = {
      selectedSize: sizeValueList.map((e) => ({
        label: `${e.sizeValue}【${e.label}】`,
        value: e.value,
        disabled: e.disabled
      }))
    }
  }
}
// 暴露方法给父组件
defineExpose({
  validate,
  clearValidate,
  resetFields,
  formRef
})
</script>
<template>
  <div class="form-render">
    <ElForm
      ref="formRef"
      :model="localFormData"
      :rules="formRules"
      :label-width="100"
      :disabled="disabled"
      :validate-on-rule-change="false"
      v-if="gridItems"
    >
      <ElRow :span="24" v-for="(attributeList, attributeIndex) in gridItems" :key="attributeIndex">
        <!-- 处理属性数组 -->
        <ElCol
          v-for="attribute in attributeList"
          :key="attribute.value"
          :span="attribute.span || 6"
        >
          <template v-if="attribute.visibility !== 'HIDDEN'">
            <ElFormItem
              :label="attribute.constraintDefaultName || attribute.name"
              :prop="attribute.value"
              :required="
                parseConstraintValue(attribute.constraintValue || '{}')[keyMap.required]?.value
              "
              v-if="localFormData.hasOwnProperty(attribute.value!)"
            >
              <FormItemRender
                :ref="(el) => { if (el) fieldRefs[attribute.value!] = el }"
                :constraintType="constraintMap.typeMap?.[attribute.type] || ''"
                :constraintList="parseConstraintValue(attribute.constraintValue || '{}')"
                v-model="localFormData[attribute.value!]"
                :options="sizeOptions[attribute.value!]"
                :formData="localFormData"
                :field="attribute.value"
                :disabled="
                  disabled ||
                  parseConstraintValue(attribute.constraintValue || '{}')[keyMap.disabled]?.value ||
                  attribute.visibility === 'READONLY'
                "
                :readonly="readonly || attribute.visibility === 'READONLY'"
                :placeholder="
                  parseConstraintValue(attribute.constraintValue || '{}')[keyMap.placeholder]
                    ?.value || `请输入${attribute.name}`
                "
                :defaultValue="parseDefaultValue(attribute.constraintDefaultValue)"
                @update:model-value="handleFieldChange(attribute.value!, $event)"
              />
            </ElFormItem>
          </template>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<style scoped lang="less">
.form-render {
  width: 100%;

  :deep(.el-form) {
    .el-form-item {
      margin-bottom: 18px;

      .el-input__inner {
        line-height: 32px;
      }

      .el-cascader,
      .el-select,
      .el-autocomplete,
      .el-input {
        width: 100%;
      }

      .el-form-item__label {
        font-weight: 500;
        color: var(--el-text-color-primary);

        &::before {
          margin-right: 4px;
        }
      }

      .el-form-item__content {
        .el-form-item__error {
          padding-top: 2px;
          font-size: 12px;
          line-height: 1.4;
          color: var(--el-color-danger);
        }
      }

      // 必填字段样式
      &.is-required {
        .el-form-item__label::before {
          margin-right: 4px;
          color: var(--el-color-danger);
          content: '*';
        }
      }

      // 验证错误状态样式
      &.is-error {
        .el-form-item__content {
          .el-input__wrapper,
          .el-select__wrapper,
          .el-textarea__inner {
            border-color: var(--el-color-danger);
            box-shadow: 0 0 0 1px var(--el-color-danger) inset;
          }
        }
      }

      // 验证成功状态样式
      &.is-success {
        .el-form-item__content {
          .el-input__wrapper,
          .el-select__wrapper,
          .el-textarea__inner {
            border-color: var(--el-color-success);
          }
        }
      }
    }

    // 禁用状态样式
    &.is-disabled {
      .el-form-item {
        .el-form-item__label {
          color: var(--el-text-color-disabled);
        }
      }
    }
  }
}
</style>
