import { constraintMap, keyMap } from '@/components/PlmBase/const'
import type { Attribute } from '@/components/PlmBase/type'
const { typeMap } = constraintMap
/***
 初始化时，数据需要转换
     constraintType: 约束类型
     formData: 表单数据
     contraintFormData: 约束数据
     key: 表单数据的key
 1、如果是区间类型，需要转换为数组
 2、如果是多选类型，需要转换为数组
 3、如果是单选类型，需要转换为字符串
 4、如果是数字类型，需要转换为数字
 5、如果是布尔类型，需要转换为布尔值
 6、如果是附件类型，需要转换为数组
 ***/
export const FormatFormDataValue = (
  constraintType,
  formData: Ref<Record<string, any>>,
  contraintFormData: Record<string, any>,
  key: string
) => {
  // 如果为区间、附件则为数组
  if (constraintType === typeMap.ATTACHMENT || contraintFormData?.[keyMap.isRange]) {
    formData.value[key] = Array.isArray(formData.value[key]) ? formData.value[key] : []
    return // 提前返回，避免被后续逻辑覆盖
  }

  const arrayComponentTypes = ['CASCADER', 'CHECKBOXGROUP']

  // 如果是多选或者组件类型为cascader、ElCheckbox、ElRadioGroup 则为数组
  if (
    contraintFormData?.[keyMap.multiple] ||
    arrayComponentTypes.includes(contraintFormData?.[keyMap.component])
  ) {
    formData.value[key] = Array.isArray(formData.value[key]) ? formData.value[key] : []
    return // 提前返回，避免被后续逻辑覆盖
  }
  // 如果组件类型为单选，必定为字符串
  if (contraintFormData?.[keyMap.component] === 'RADIOGROUP') {
    formData.value[key] = ''
    return
  }
  // 其他情况根据约束类型进行转换
  switch (constraintType) {
    case typeMap.INT:
      // 数字类型转换为数字
      formData.value[key] = Number(formData.value[key])
      break
    case typeMap.FLOAT:
      break
    case typeMap.BOOLEAN:
      // 布尔类型转换为布尔值
      formData.value[key] = Boolean(formData.value[key])
      break
    default:
      // 默认转换为字符串
      formData.value[key] = String(formData.value[key] || '')
  }
}

/***
 保存数据时，需要做转换
 1、如果是对象单选，传参需要是数组的形式
 **/
export const saveFormDataValue = (attributes: Attribute[], formData: Record<string, any>) => {
  attributes.forEach((item: Attribute) => {
    const { type, value } = item
    const typeName = (type && constraintMap.typeMap?.[type]) || ''

    if (typeName === 'OBJECT' && value) {
      const current = formData[value]
      formData[value] = Array.isArray(current)
        ? current
        : current ?? '' // 保持兼容旧值的存在判断
        ? [current]
        : []
    }
  })
}
/***
 查询时，需要做转换
 1、如果是对象单选，传参需要是字符串
 **/
export const queryFormDataValue = (attributes: Attribute[], formData: Record<string, any>) => {
  attributes.forEach((item: Attribute) => {
    const { type, value, constraintValue } = item
    const typeName = (type && constraintMap.typeMap?.[type]) || ''
    const constraintList = JSON.parse(constraintValue || '{}')
    if (
      typeName === constraintMap.typeMap.OBJECT &&
      value &&
      !constraintList?.[keyMap.multiple]?.value
    ) {
      const current = formData[value]
      formData[value] = Array.isArray(current) ? current[0] : current ?? ''
    }
    if (typeName == constraintMap.typeMap.ATTACHMENT && value) {
      const current = formData[value]
      formData[value] = Array.isArray(current) ? current : []
    }
  })
}
