//布局
export interface layoutAttribute {
  id: string
  name: string
  type: string
  value: string
  gridItems?: Attribute[]
}
// 多品类相关的属性
export interface Attribute {
  /**
   * 属性约束默认名称
   */
  constraintDefaultName?: string
  /**
   * 属性约束默认值
   */
  constraintDefaultValue?: string
  /**
   * 约束值
   */
  constraintValue?: string
  /**
   * 是否为默认的属性
   */
  defaultProperty?: boolean
  /**
   * 是否为继承的属性
   */
  isExtends?: boolean
  /**
   * 属性名称
   */
  name?: string
  label?: string
  /**
   * 产品分类id
   */
  productCategoryId?: number
  /**
   * 属性id
   */
  propertyId?: number
  /**
   * 产品分类-属性关联关系id
   */
  relationId?: number
  /**
   * 数据类型
   */
  type: string
  /**
   * 属性内部值
   */
  value?: string
  tableSearch?: string
  tableFixedType?: string
  tableWidth?: string
  createByIdName?: string
  createTime?: string
  delFlag?: number
  id?: number
  modifyById?: number
  [property: string]: any
}

// 对应属性返回的data
export interface AttributeData {
  attributeId?: number
  attributeValue?: string
  id?: number
  plmBaseId?: number
  [property: string]: any
}
export interface tableColumn {
  field: string
  title: string
  slots?: { default: string }
}
