<script lang="tsx">
import { ElBreadcrumb, ElBreadcrumbItem } from 'element-plus'
import { computed, defineComponent, ref, TransitionGroup, unref, watch } from 'vue'
import type { RouteLocationNormalizedLoaded, RouteMeta } from 'vue-router'
import { useRoute, useRouter } from 'vue-router'
import { usePermissionStore } from '@/store/modules/permission'
import { filterBreadcrumb } from './helper'
import { filter, treeToList } from '@/utils/tree'
import { Icon } from '@/components/Icon'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import { initial } from 'lodash-es'
import { pageTypeToLabel } from '@/utils/enum'
import { TooltipClamp } from '@/components/TooltipClamp'
import { useI18nTitle } from '@/hooks/web/useTitle'

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('breadcrumb')

const appStore = useAppStore()

// 面包屑图标
const breadcrumbIcon = computed(() => appStore.getBreadcrumbIcon)

export default defineComponent({
  name: 'Breadcrumb',
  setup() {
    const { currentRoute } = useRouter()

    const levelList = ref<AppRouteRecordRaw[]>([])

    const permissionStore = usePermissionStore()

    const menuRouters = computed(() => {
      const routers = permissionStore.getRouters
      return filterBreadcrumb(routers)
    })

    const nodePathCurrentPathCompare = (nodePath: string, currentPath: string): boolean => {
      if (nodePath.includes('pageType')) {
        return (
          nodePath.replaceAll('/:' + 'pageType', '') === initial(currentPath.split('/')).join('/')
        )
      } else {
        return nodePath === currentPath
      }
    }
    const getBreadcrumb = () => {
      const currentPath = currentRoute.value.path
      levelList.value = filter<AppRouteRecordRaw>(unref(menuRouters), (node: AppRouteRecordRaw) => {
        return nodePathCurrentPathCompare(node.path, currentPath)
      })
    }
    const handleBreadTitle = (v) => {
      const pageType = useRoute().params.pageType || ''
      return (v.path.includes(':pageType') && pageTypeToLabel[pageType as string]) || ''
    }
    const renderBreadcrumb = () => {
      const breadcrumbList = treeToList<AppRouteRecordRaw[]>(unref(levelList)).filter(
        (item) => item.meta?.breadcrumb !== false
      )
      return breadcrumbList.map((v) => {
        const disabled = v.redirect === 'noredirect'
        const meta = v.meta as RouteMeta
        const title = useI18nTitle(meta)
        return (
          <ElBreadcrumbItem
            to={{ path: disabled ? '' : v.path }}
            class={{ disabled: disabled }}
            key={v.name}
          >
            {meta?.icon && breadcrumbIcon.value ? (
              <>
                <Icon icon={meta.icon} class="mr-[5px]"></Icon>
                <TooltipClamp maxLines={1} autoresize textContent={title}>
                  {title}
                </TooltipClamp>
              </>
            ) : (
              <TooltipClamp maxLines={1} autoresize textContent={handleBreadTitle(v) + title}>
                {handleBreadTitle(v) + title}
              </TooltipClamp>
            )}
          </ElBreadcrumbItem>
        )
      })
    }

    watch(
      () => currentRoute.value,
      (route: RouteLocationNormalizedLoaded) => {
        if (route.path.startsWith('/redirect/')) {
          return
        }
        getBreadcrumb()
      },
      {
        immediate: true
      }
    )

    return () => (
      <ElBreadcrumb separator="/" class={`${prefixCls} flex items-center h-full ml-[10px]`}>
        <TransitionGroup appear enter-active-class="animate__animated animate__fadeInRight">
          {renderBreadcrumb()}
        </TransitionGroup>
      </ElBreadcrumb>
    )
  }
})
</script>

<style lang="less" scoped>
@prefix-cls: ~'@{elNamespace}-breadcrumb';

.@{prefix-cls} {
  :deep(&__item) {
    display: flex;
    .@{prefix-cls}__inner {
      display: flex;
      align-items: center;
      color: var(--top-header-text-color);

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }

  :deep(&__item):not(:last-child) {
    .@{prefix-cls}__inner {
      color: var(--top-header-text-color);

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }

  :deep(&__item):last-child {
    .@{prefix-cls}__inner {
      color: var(--el-text-color-placeholder);

      &:hover {
        color: var(--el-text-color-placeholder);
      }
    }
  }
}
</style>
