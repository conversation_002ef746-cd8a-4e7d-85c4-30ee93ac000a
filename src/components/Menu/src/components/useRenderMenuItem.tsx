import { ElSubMenu, ElMenu<PERSON>tem, ElPopover } from 'element-plus'
import type { RouteMeta } from 'vue-router'
import { hasOneShowingChild } from '../helper'
import { isUrl } from '@/utils/is'
import { useRenderMenuTitle } from './useRenderMenuTitle'
import { useDesign } from '@/hooks/web/useDesign'
import { pathResolve } from '@/utils/routerHelper'
import { Icon } from '@/components/Icon'

export const useRenderMenuItem = (
  // allRouters: AppRouteRecordRaw[] = [],
  menuMode: 'vertical' | 'horizontal'
) => {
  // 渲染三级菜单弹出层
  const renderThirdLevelPopover = (
    children: AppRouteRecordRaw[],
    parentPath: string,
    title: any
  ) => {
    const { renderMenuTitle } = useRenderMenuTitle()

    return (
      <ElPopover
        placement="right-start"
        trigger="hover"
        width="200"
        popperClass="third-level-menu-popover"
        showArrow={false}
        offset={0}
        hideAfter={100}
        showAfter={200}
      >
        {{
          reference: () => (
            <div class="second-level-menu-item">
              <span class="menu-title">{title}</span>
              <Icon icon="ep:arrow-right" class="third-level-arrow" />
            </div>
          ),
          default: () => (
            <div class="third-level-menu-container">
              {children
                .filter((child) => !(child.meta as RouteMeta)?.hidden)
                .map((child, index) => {
                  const childPath = pathResolve(parentPath, child.path)
                  const childMeta = child.meta as RouteMeta

                  // 如果子项有children，显示为分组
                  if (child.children && child.children.length > 0) {
                    const visibleGrandChildren = child.children.filter(
                      (grandChild) => !(grandChild.meta as RouteMeta)?.hidden
                    )
                    if (visibleGrandChildren.length === 0) return null

                    return (
                      <div key={`group-${index}`} class="third-level-group">
                        <div class="third-level-group-title">
                          {childMeta?.title || childMeta?.i18n?.['zh-CN'] || child.name}
                        </div>
                        <div class="third-level-items">
                          {visibleGrandChildren.map((grandChild, grandIndex) => {
                            const grandChildPath = pathResolve(childPath, grandChild.path)
                            return (
                              <ElMenuItem
                                key={`item-${index}-${grandIndex}`}
                                index={grandChildPath}
                                class="third-level-menu-item"
                              >
                                {{
                                  default: () => renderMenuTitle(grandChild.meta as RouteMeta)
                                }}
                              </ElMenuItem>
                            )
                          })}
                        </div>
                      </div>
                    )
                  } else {
                    // 如果子项没有children，直接显示为菜单项
                    return (
                      <ElMenuItem
                        key={`direct-${index}`}
                        index={childPath}
                        class="third-level-menu-item"
                      >
                        {{
                          default: () => renderMenuTitle(childMeta)
                        }}
                      </ElMenuItem>
                    )
                  }
                })}
            </div>
          )
        }}
      </ElPopover>
    )
  }

  const renderMenuItem = (routers: AppRouteRecordRaw[], parentPath = '/', level = 1) => {
    return routers.map((v) => {
      const meta = (v.meta ?? {}) as RouteMeta
      if (!meta.hidden) {
        const { oneShowingChild, onlyOneChild } = hasOneShowingChild(v.children, v)
        const fullPath = isUrl(v.path) ? v.path : pathResolve(parentPath, v.path) // getAllParentPath<AppRouteRecordRaw>(allRouters, v.path).join('/')

        const { renderMenuTitle } = useRenderMenuTitle()

        if (
          oneShowingChild &&
          (!onlyOneChild?.children || onlyOneChild?.noShowingChildren) &&
          !meta?.alwaysShow
        ) {
          return (
            <ElMenuItem index={onlyOneChild ? pathResolve(fullPath, onlyOneChild.path) : fullPath}>
              {{
                default: () => renderMenuTitle(onlyOneChild ? onlyOneChild?.meta : meta)
              }}
            </ElMenuItem>
          )
        } else {
          const { getPrefixCls } = useDesign()
          const preFixCls = getPrefixCls('menu-popper')

          // 如果是二级菜单且有三级菜单，使用特殊渲染
          if (level === 2 && v.children && v.children.length > 0) {
            return renderThirdLevelPopover(v.children, fullPath, renderMenuTitle(meta))
          }

          return (
            <ElSubMenu
              index={fullPath}
              popperClass={
                menuMode === 'vertical' ? `${preFixCls}--vertical` : `${preFixCls}--horizontal`
              }
            >
              {{
                title: () => renderMenuTitle(meta),
                default: () => renderMenuItem(v.children!, fullPath, level + 1)
              }}
            </ElSubMenu>
          )
        }
      }
    })
  }

  return {
    renderMenuItem
  }
}
