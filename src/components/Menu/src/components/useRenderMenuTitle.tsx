import type { RouteMeta } from 'vue-router'
import { Icon } from '@/components/Icon'
import { useI18nTitle } from '@/hooks/web/useTitle'
import { TooltipClamp } from '@/components/TooltipClamp'

export const useRenderMenuTitle = () => {
  const renderMenuTitle = (meta: RouteMeta) => {
    const { icon } = meta
    const title = useI18nTitle(meta)
    return icon ? (
      <>
        <Icon icon={meta.icon}></Icon>
        <TooltipClamp maxLines={1} textContent={title} autoresize>
          {title}
        </TooltipClamp>
      </>
    ) : (
      <TooltipClamp maxLines={1} textContent={title} autoresize>
        {title}
      </TooltipClamp>
    )
  }

  return {
    renderMenuTitle
  }
}
