import type { ExtractPropTypes, InjectionKey } from 'vue'
import IndexAnchor from './IndexAnchor.vue'

export type AnchorIndex = number | string

export const indexBarProps = {
  /**
   * @description 索引是否吸顶
   */
  sticky: {
    type: Boolean,
    default: false
  }
}

export type IndexBarProps = ExtractPropTypes<typeof indexBarProps>

export type InderBarProvide = {
  props: { sticky?: boolean }
  anchorState: {
    activeIndex: AnchorIndex | null // 当前激活的索引
  }
  children: InstanceType<typeof IndexAnchor>[]
}

export const indexBarInjectionKey: InjectionKey<InderBarProvide> = Symbol('wd-index-bar')

export const indexAnchorProps = {
  customStyle: {
    type: String,
    default: ''
  },
  /**
   * 自定义根节点样式类
   */
  customClass: {
    type: String,
    default: ''
  },
  index: {
    type: [String, Number],
    required: true
  }
}

export type IndexAnchorProps = ExtractPropTypes<typeof indexAnchorProps>
