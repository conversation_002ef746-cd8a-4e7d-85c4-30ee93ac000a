<template>
  <div
    :id="indexAnchorId"
    :class="`index-anchor ${isSticky ? 'is-sticky' : ''} ${props.customClass}`"
    :style="props.customStyle"
  >
    <slot>
      {{ props.index }}
    </slot>
  </div>
</template>

<script lang="ts" setup>
import { indexAnchorProps, indexBarInjectionKey } from './type'
import { computed, onMounted, ref } from 'vue'
import { uuid } from './utils'
import { IndexAnchor } from '@/components/Anchor'

const props = defineProps(indexAnchorProps)

const indexBar = inject(indexBarInjectionKey)

const { proxy } = getCurrentInstance()!
if (isDefined(indexBar) && isDefined(proxy)) {
  indexBar.children.push(proxy as InstanceType<typeof IndexAnchor>)
}

const indexAnchorId = ref<string>(`indexBar${uuid()}`)

const top = ref<number>(0)

const isSticky = computed(() => {
  return indexBar && indexBar.props.sticky && indexBar.anchorState.activeIndex === props.index
})

function getInfo() {
  const res = document.querySelector(`#${indexAnchorId.value}`)
  if (isDefined(indexBar)) {
    top.value = Math.floor(res?.getBoundingClientRect().top || 0)
  }
}

onMounted(() => {
  getInfo()
})

defineExpose({
  top
})
</script>

<style lang="less" scoped>
@import './index-anchor.less';
</style>
