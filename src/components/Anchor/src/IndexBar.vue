<template>
  <div :id="indexBarId" class="index-bar">
    <el-scrollbar ref="elScrollbarRef" class="index-bar__content" @scroll="handleScroll">
      <slot></slot>
    </el-scrollbar>
    <div
      class="index-bar__sidebar"
      @touchstart.stop.prevent="handleTouchStart"
      @touchmove.stop.prevent="handleTouchMove"
      @touchend.stop.prevent="handleTouchEnd"
      @touchcancel.stop.prevent="handleTouchEnd"
      @click.stop.prevent="handleClick"
    >
      <div
        v-for="item in children"
        :key="item.index"
        :class="{ 'is-active': item.index === state.activeIndex }"
        class="index-bar__index"
      >
        {{ item.index }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { AnchorIndex } from './type'
import { indexBarInjectionKey, indexBarProps } from './type'
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { uuid } from './utils'
import IndexAnchor from './IndexAnchor.vue'
import { ElScrollbar } from 'element-plus'

const props = defineProps(indexBarProps)

const indexBarId = ref<string>(`indexBar${uuid()}`)

const state = reactive({
  activeIndex: null as AnchorIndex | null
})

const elScrollbarRef = ref<InstanceType<typeof ElScrollbar>>()

const children = reactive<InstanceType<typeof IndexAnchor>[]>([])

provide(indexBarInjectionKey, {
  props,
  anchorState: state,
  children
})

watch(
  children,
  (newValue) => {
    if (!newValue.length) {
      state.activeIndex = null // 或者设置为一个默认值，如第一个子项的索引
      return
    }

    if (
      !isDefined(state.activeIndex) ||
      !newValue.find((item) => item.index === state.activeIndex)
    ) {
      state.activeIndex = newValue[0].index!
    }
  },
  { deep: true, immediate: true }
)

const scrollState = reactive({
  scrollTop: 0, // 即将滚动到的位置
  prevScrollTop: 0, // 上次记录的位置
  // 滚动距离
  touching: false
})

watch(
  () => scrollState.scrollTop,
  (val) => {
    elScrollbarRef.value?.setScrollTop(val)
  },
  {
    deep: true,
    immediate: true
  }
)

// 组件距离页面顶部的高度
let offsetTop = 0
let sidebarInfo = {
  // 侧边栏距离顶部的高度
  offsetTop: 0,
  // 高度固定24px
  indexHeight: 24
}

function init() {
  setTimeout(() => {
    Promise.all([
      document.querySelector(`#${indexBarId.value}`),
      document.querySelector('.index-bar__sidebar'),
      document.querySelector('.index-bar__index')
    ]).then(([bar, sidebar, index]) => {
      offsetTop = bar?.getBoundingClientRect().top || 0
      sidebarInfo.offsetTop = sidebar?.getBoundingClientRect().top || 0
      sidebarInfo.indexHeight = index?.getBoundingClientRect().height || 0
    })
  }, 100)
}

onMounted(() => {
  init()
})

function handleScroll(scrollEvent: { scrollTop: number }) {
  if (scrollState.touching) {
    return
  }
  const scrollTop = Math.floor(scrollEvent.scrollTop)
  const anchor = children.find((item, index) => {
    if (!isDefined(children[index + 1])) return true
    return (
      item.$.exposed!.top.value - offsetTop <= scrollTop &&
      children[index + 1].$.exposed!.top.value - offsetTop > scrollTop
    )
  })
  if (isDefined(anchor) && state.activeIndex !== anchor.index) {
    state.activeIndex = anchor.index!
  }
  scrollState.prevScrollTop = scrollTop
}

function getAnchorByPageY(pageY: number) {
  const y = pageY - sidebarInfo.offsetTop
  let idx = Math.floor(y / sidebarInfo.indexHeight)
  if (idx < 0) idx = 0
  else if (idx > children.length - 1) idx = children.length - 1
  return children[idx]
}

function handleTouchStart() {
  scrollState.touching = true
}

function handleTouchMove(e: TouchEvent) {
  const clientY = e.touches[0].pageY
  if (state.activeIndex === getAnchorByPageY(clientY).index) {
    return
  }
  state.activeIndex = getAnchorByPageY(clientY).index!
  setScrollTop(getAnchorByPageY(clientY).$.exposed!.top.value - offsetTop)
}

function handleTouchEnd(e: TouchEvent) {
  const clientY = e.changedTouches[0].pageY
  state.activeIndex = getAnchorByPageY(clientY).index!
  setScrollTop(getAnchorByPageY(clientY).$.exposed!.top.value - offsetTop)
  requestAnimationFrame(() => {
    scrollState.touching = false
  })
}

function handleClick(e: MouseEvent) {
  const clientY = e.pageY
  state.activeIndex = getAnchorByPageY(clientY).index!
  setScrollTop(getAnchorByPageY(clientY).$.exposed!.top.value - offsetTop)
  requestAnimationFrame(() => {
    scrollState.touching = false
  })
}

function setScrollTop(top: number) {
  if (scrollState.scrollTop === top) {
    scrollState.scrollTop = scrollState.prevScrollTop
    nextTick(() => {
      scrollState.scrollTop = top
    })
  } else {
    scrollState.scrollTop = top
  }
}
</script>

<style lang="less" scoped>
@import './index-bar.less';
</style>
