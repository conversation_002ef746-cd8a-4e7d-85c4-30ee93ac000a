<script lang="ts" setup>
import { computed, nextTick, onBeforeUnmount, PropType, ref, shallowRef, unref, watch } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { i18nChangeLanguage, IDomEditor, IEditorConfig } from '@wangeditor/editor'
import { propTypes } from '@/utils/propTypes'
import { isNumber } from '@/utils/is'
import { ElMessage } from 'element-plus'
import { useLocaleStore } from '@/store/modules/locale'
import { uploadByPreUrlApi } from '@/api/system'
import axios from 'axios'

const localeStore = useLocaleStore()

const currentLocale = computed(() => localeStore.getCurrentLocale)

i18nChangeLanguage(unref(currentLocale).lang)

const props = defineProps({
  editorId: propTypes.string.def('wangeEditor-1'),
  height: propTypes.oneOfType([Number, String]).def('500px'),
  editorConfig: {
    type: Object as PropType<IEditorConfig>,
    default: () => undefined
  },
  modelValue: propTypes.string.def('')
})
const fileImg = ref(null)

const emit = defineEmits(['change', 'update:modelValue'])

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef<IDomEditor>()

const valueHtml = ref('')
const fileFormImg = reactive({})

watch(
  () => props.modelValue,
  (val: string) => {
    if (val === unref(valueHtml)) return
    valueHtml.value = val
  },
  {
    immediate: true
  }
)

// 监听
watch(
  () => valueHtml.value,
  (val: string) => {
    emit('update:modelValue', val)
  }
)

const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor
}
const uploadImg = async (file, insertFn) => {
  fileImg.value = file
  const { name: fileName, size: kbSize } = file
  Object.assign(fileFormImg, {
    fileName,
    kbSize: Math.floor(kbSize / 1024),
    fileType: fileName.substring(fileName.lastIndexOf('.') + 1),
    configCode: '0000100101'
  })
  const { datas: res } = await uploadByPreUrlApi(fileFormImg)
  const {
    originName: name,
    objectName: key,
    policy,
    accessid: OSSAccessKeyId,
    success_action_status = 200,
    callback,
    signature,
    host: url
  } = res
  const form = Object.assign(
    {},
    { name, key, policy, OSSAccessKeyId, success_action_status, callback, signature },
    { file: fileImg.value }
  )

  const formData = new FormData()
  Object.keys(form).forEach((key) => formData.set(`${key}`, form[key]))
  const response = await axios({
    method: 'post',
    url,
    data: formData
  })
  if (response.data.code + '' === '0') {
    //插入后端返回的url
    insertFn(response.data.datas.url)
  } else {
  }
}
// 编辑器配置
const editorConfig = computed((): IEditorConfig => {
  return Object.assign(
    {
      readOnly: false,
      customAlert: (s: string, t: string) => {
        switch (t) {
          case 'success':
            ElMessage.success(s)
            break
          case 'info':
            ElMessage.info(s)
            break
          case 'warning':
            ElMessage.warning(s)
            break
          case 'error':
            ElMessage.error(s)
            break
          default:
            ElMessage.info(s)
            break
        }
      },
      autoFocus: false,
      scroll: true,
      uploadImgShowBase64: false,
      MENU_CONF: {
        //配置上传图片
        uploadImage: {
          customUpload: uploadImg
        }
      }
    },
    props.editorConfig || {}
  )
})

const editorStyle = computed(() => {
  return {
    height: isNumber(props.height) ? `${props.height}px` : props.height
  }
})

// 回调函数
const handleChange = (editor: IDomEditor) => {
  emit('change', editor)
}

// 组件销毁时，及时销毁编辑器
onBeforeUnmount(() => {
  const editor = unref(editorRef.value)
  if (editor === null) return

  // 销毁，并移除 editor
  editor?.destroy()
})

const getEditorRef = async (): Promise<IDomEditor> => {
  await nextTick()
  return unref(editorRef.value) as IDomEditor
}

defineExpose({
  getEditorRef
})
</script>

<template>
  <div class="border-1 border-solid border-[var(--tags-view-border-color)] z-[3000]">
    <!-- 工具栏 -->
    <Toolbar
      :defaultConfig="{ excludeKeys: ['group-video'] }"
      :editor="editorRef"
      :editorId="editorId"
      class="border-bottom-1 border-solid border-[var(--tags-view-border-color)]"
    />
    <!-- 编辑器 -->
    <Editor
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :editorId="editorId"
      :style="editorStyle"
      @on-change="handleChange"
      @on-created="handleCreated"
    />
  </div>
</template>

<style src="@wangeditor/editor/dist/css/style.css"></style>
