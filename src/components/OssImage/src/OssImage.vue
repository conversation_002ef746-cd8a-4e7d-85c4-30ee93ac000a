<script setup lang="ts">
import { ElUpload, ElMessage, ElImage } from 'element-plus'
import { propTypes } from '@/utils/propTypes'
import { getToken } from '@/utils/auth'
import { uploadByPreUrlApi } from '@/api/system'
import axios from 'axios'

const props = defineProps({
  configCode: propTypes.string.def('0001'),
  multiple: propTypes.bool.def(true),
  showDelete: propTypes.bool.def(true),
  disabled: propTypes.bool.def(false),
  limit: propTypes.number.def(1),
  size: propTypes.number.def(100),
  fileList: propTypes.array.def([])
})
const emits = defineEmits(['success', 'remove'])

const uploadRef = ref(null)

const uploadHeaders = reactive({
  authorization: getToken()
})
const beforeAvatarUpload = (file) => {
  const isJPG = ['image/jpg', 'image/jpeg', 'image/png'].includes(file.type)
  const isLt5M = file.size / 1024 / 1024 < props.size
  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  if (!isLt5M) {
    ElMessage.error(`上传图片大小不能超过 ${props.size}MB!`)
  }
  return isJPG && isLt5M
}
const handleFileChange = () => {}
// 上传接口
const handleUploadFile = async (options) => {
  const { name: fileName, size: kbSize, uid } = options.file
  const params = {
    fileName,
    kbSize: Math.floor(kbSize / 1024),
    fileType: fileName.substring(fileName.lastIndexOf('.') + 1),
    configCode: props.configCode
  }
  try {
    const { datas: res } = await uploadByPreUrlApi(params)
    const {
      originName: name,
      objectName: key,
      policy,
      accessid: OSSAccessKeyId,
      callback,
      signature,
      host: url
    } = res
    const form = Object.assign(
      {},
      { name, key, policy, OSSAccessKeyId, callback, signature },
      { file: options?.file }
    )
    const formData = new FormData()
    Object.keys(form).forEach((key) => formData.set(`${key}`, form[key]))
    const result = await axios({
      method: 'post',
      url,
      data: formData
    })
    if (!result) return ElMessage.error('上传失败')
    result?.data?.datas && options.onSuccess({ datas: result.data.datas, uid })
  } catch (e) {
    ElMessage.error(e?.message || '上传失败')
  }
}
const dialogImageUrl = ref('')
const dialogVisible = ref(false)

const handlePictureCardPreview = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url
  dialogVisible.value = true
}
const handleRemove = (uploadFile, uploadFiles) => {
  if (uploadFile) {
    emits('remove', { uploadFile, uploadFiles })
  }
}
const handlePictureSuccess = (res) => {
  if (res) {
    emits('success', res)
  }
}
const handleExceed = () => {
  ElMessage.warning(`最多上传${props.limit}张图片`)
}
defineExpose({
  uploadRef
})
</script>
<template>
  <ElImageViewer v-if="dialogVisible" :url-list="[dialogImageUrl]" @close="dialogVisible = false" />
  <ElUpload
    :file-list="props.fileList"
    ref="uploadRef"
    :headers="uploadHeaders"
    :on-change="handleFileChange"
    :http-request="handleUploadFile"
    list-type="picture-card"
    :limit="props.limit"
    accept=".jpg,.jpeg,.png"
    drag
    :style="{ 'pointer-events': disabled ? 'none' : 'auto' }"
    :on-exceed="handleExceed"
    :on-success="handlePictureSuccess"
    :multiple="props.multiple"
    :before-upload="beforeAvatarUpload"
    class="upload_files"
    show-overflow
    :disabled="disabled"
    :on-remove="handleRemove"
    :on-preview="handlePictureCardPreview"
  >
    <Icon icon="ep:plus" size="20" />
  </ElUpload>
</template>

<style lang="less" scoped>
.upload_files {
  :deep(.el-upload-list) {
    .is-success {
      width: 100px;
      height: 100px;
      margin-right: 8px;
      overflow: hidden;
      border: 0;
      border-radius: 0;

      img,
      .el-upload-list__item-actions {
        height: 100%;
      }
    }

    .is-ready,
    .is-uploading {
      display: none;
    }

    > .el-upload {
      --el-upload-picture-card-size: 0px !important;
      display: block;
      display: flex;
      width: 100px;
      height: 100px;
      background-color: transparent;
      justify-content: center;

      .el-upload-dragger {
        display: flex;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>
