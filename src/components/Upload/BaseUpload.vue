<script lang="ts" setup>
import {
  ElMessage,
  ElMessageBox,
  UploadFile,
  UploadFiles,
  UploadInstance,
  UploadProgressEvent,
  UploadProps,
  UploadRawFile,
  type UploadRequestHandler,
  UploadUserFile
} from 'element-plus'
import { Icon } from '@/components/Icon'
import { getToken } from '@/utils/auth'
import { isFunction } from '@/utils/is'

interface Props {
  class?: string
  modelValue?: BaseFileDTO[]
  limit?: number
  accept?: string
  sizeLimit?: number
  hideOnExceed?: boolean
  autoUpload?: boolean
  beforeUpload?: UploadProps['beforeUpload']
  onSuccess?: UploadProps['onSuccess']
  headers?: UploadProps['headers']
  listType?: 'text' | 'picture' | 'picture-card'
  disabled?: boolean
  httpRequest?: UploadRequestHandler
}

interface ResponseData {
  isSuccess?: boolean
  data?: BaseFileDTO[]
  datas?: BaseFileDTO[]
  error?: string
  msg?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: (): BaseFileDTO[] => [],
  limit: 0,
  accept: '',
  beforeUpload: () => () => true,
  autoUpload: true,
  sizeLimit: 0,
  hideOnExceed: true,
  headers: () => ({
    'x-sso-auth': getToken()
  }),

  listType: 'picture-card',
  disabled: false
})

const emit = defineEmits<{
  (e: 'update:modelValue', val: BaseFileDTO[]): void
  (e: 'error', error: Error, uploadFile: UploadFile, uploadFiles: UploadFiles): void
  (
    e: 'success',
    res: BaseFileDTO[] | undefined,
    uploadFile: UploadFile,
    uploadFiles: UploadFiles
  ): void
  (
    e: 'progress',
    event: UploadProgressEvent,
    uploadFile: UploadFile,
    uploadFiles: UploadFiles
  ): void
  (e: 'remove', file: UploadFile, uploadFiles: UploadFiles, delIndex: number): void
  (e: 'preview', file: UploadFile): void
}>()

const uploadRef = ref<UploadInstance>()
const fileList = ref<UploadUserFile[]>([])

// 这个变量是为了解决在同时上传多个文件时，修改了fileList会导致只上传一次问题
let innerFlag = false
watch(
  () => props.modelValue,
  (val) => {
    if (!innerFlag) {
      fileList.value =
        val?.map((item) => ({
          name: item.fileName!,
          url: item.signatureUrl,
          status: 'success'
        })) || []
    } else {
      innerFlag = false
    }
  },
  {
    deep: true,
    immediate: true
  }
)

const formatFileSize = (size: number) => {
  const kb = 1024
  const mb = kb * 1024
  let msg: string
  if (size < kb) {
    msg = `${size} B`
  } else if (size < mb) {
    msg = `${(size / kb).toFixed(2)} KB`
  } else {
    msg = `${(size / mb).toFixed(2)} MB`
  }
  return msg
}

const validateFileType = (file: File) => {
  if (!props.accept) {
    return true
  }
  const acceptArr = props.accept.split(',')
  const type = file.name.split('.').pop() || file.type || ''
  return acceptArr.some((item) => {
    if (item === 'image/*') {
      const imgExt = [
        'svgz',
        'pjp',
        'png',
        'ico',
        'avif',
        'tiff',
        'tif',
        'jfif',
        'svg',
        'xbm',
        'pjpeg',
        'webp',
        'jpg',
        'jpeg',
        'bmp',
        'gif'
      ]
      return imgExt.includes(type.toLowerCase())
    }
    if (item === 'video/*') {
      const videoExt = [
        'mp4',
        'm4v',
        'mov',
        'mkv',
        'webm',
        'flv',
        'avi',
        'rmvb',
        'rm',
        '3gp',
        '3g2',
        'mpg',
        'mpeg',
        'wmv',
        'asf',
        'asx'
      ]
      return videoExt.includes(type.toLowerCase())
    }
    if (item === 'audio/*') {
      const audioExt = ['mp3', 'wav', 'wma', 'ogg', 'flac', 'ape', 'aac', 'amr', 'm4a', 'm4b', 'wv']
      return audioExt.includes(type.toLowerCase())
    }
    return item.toLowerCase() === '.' + type.toLowerCase()
  })
}

const beforeUpload = (rawFile: UploadRawFile) => {
  return new Promise<File>((resolve, reject) => {
    if (!validateFileType(rawFile)) {
      ElMessage.warning(`请上传正确格式文件!`)
      reject()
    }
    if (props.sizeLimit && rawFile.size > props.sizeLimit) {
      const msg = `文件大小不能超过${formatFileSize(props.sizeLimit)}`
      ElMessage.warning(msg)
      reject()
    }
    const customBeforeUpload = props.beforeUpload
    if (customBeforeUpload) {
      if (customBeforeUpload instanceof Promise) {
        customBeforeUpload.then(() => resolve(rawFile), reject)
      } else {
        customBeforeUpload(rawFile) ? resolve(rawFile) : reject()
      }
    } else {
      resolve(rawFile)
    }
  })
}

const hideUpload = computed(() => {
  return (
    props.disabled || (props.hideOnExceed && props.limit && fileList.value.length >= props.limit)
  )
})

const handleExceed = (files: File[]) => {
  if (props.limit === 1 && !props.autoUpload) {
    ElMessageBox.alert(`只能上传一个文件，是否覆盖之前的文件${files.at(-1)?.name}？`, '提示', {
      type: 'warning',
      showCancelButton: true,
      draggable: true
    })
      .then(() => {
        uploadRef.value!.clearFiles()
        const file = files[0] as UploadRawFile
        //        file.uid = genFileId()
        uploadRef.value!.handleStart(file)
      })
      .catch(() => {})
  } else {
    ElMessage.warning(`最多选择${props.limit}个文件!`)
  }
}

const handleError = (error: Error, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  fileList.value = fileList.value.filter((item) => item.status === 'success')
  ElMessage.error(error.message)
  emit('error', error, uploadFile, uploadFiles)
}

const handleProgress = (
  event: UploadProgressEvent,
  uploadFile: UploadFile,
  uploadFiles: UploadFiles
) => {
  emit('progress', event, uploadFile, uploadFiles)
}

const handleSuccess = (res: ResponseData, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  if (isFunction(props.onSuccess)) {
    props.onSuccess(res, uploadFile, uploadFiles)
  } else if (res.isSuccess && (res.data || res.datas)) {
    const data = res.data || res.datas
    emit('success', data, uploadFile, uploadFiles)
    const list = props.modelValue ? [...props.modelValue] : []
    if (data && Array.isArray(data)) {
      list.push(...data.map((e) => ({ ...e, uid: uploadFile.uid })))
    }
    emit('update:modelValue', list)
  } else {
    fileList.value = fileList.value.filter((item) => item.uid !== uploadFile.uid)
    handleError(new Error(res.error || res.msg), uploadFile, uploadFiles)
  }
  innerFlag = true
}

const handleRemove = (file: UploadFile, uploadFiles: UploadFiles) => {
  const list = [...props.modelValue]
  const url = file.url
  const index = list.findIndex((item) => file.uid === item.uid || item.signatureUrl === url)
  if (index !== -1) {
    list.splice(index, 1)
    emit('update:modelValue', list)
  }
  emit('remove', file, uploadFiles, index)
}

const attrs = useAttrs()

const viewImgDialogVisible = ref(false)
const handlePreview = (file: UploadFile) => {
  emit('preview', file)
  if (props.listType === 'picture-card' || props.accept?.includes('image')) {
    viewImgDialogVisible.value = true
    return
  }
  if (attrs.onPreview && isFunction(attrs.onPreview)) {
    attrs.onPreview(file)
  } else {
    window.open(file.url, '_blank')
  }
}

const { isOutside } = useMouseInElement(uploadRef)

const handlePaste = () => {
  document.addEventListener('paste', (e: ClipboardEvent) => {
    const items = e.clipboardData?.items
    if (!items) {
      ElMessage.warning('当前浏览器不支持粘贴图片')
      return
    }
    if (isOutside.value) {
      return
    }
    const existLength = fileList.value.length
    const length = items.length
    if (props.limit) {
      if (existLength >= props.limit) {
        ElMessage.warning(`最多选择${props.limit}个文件!`)
        return
      }
      if (length + existLength > props.limit) {
        ElMessage.warning(`最多选择${props.limit}个文件!`)
        return
      }
    }
    for (let i = 0; i < length; i++) {
      const item = items[i]
      if (item.kind === 'file') {
        const file = item.getAsFile()
        if (file) {
          if (file.size > props.sizeLimit) {
            ElMessage.warning(`文件大小不能超过${formatFileSize(props.sizeLimit)}`)
            continue
          }
          //          file.uid = genFileId()
          uploadRef.value?.handleStart(file as UploadRawFile)
        }
      }
    }
    uploadRef.value?.submit()
  })
}

onMounted(handlePaste)

defineExpose({
  uploadRef,
  fileList
})
</script>

<template>
  <div :class="props.class">
    <ElUpload
      ref="uploadRef"
      v-model:file-list="fileList"
      :accept="accept"
      :auto-upload="autoUpload"
      :before-upload="beforeUpload"
      :class="hideUpload ? 'hide-upload ' : props.class"
      :disabled="disabled"
      :headers="headers"
      :http-request="props.httpRequest"
      :limit="limit"
      :list-type="listType"
      :on-error="handleError"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
      :on-progress="handleProgress"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      v-bind="$attrs"
    >
      <template v-if="!hideUpload" #trigger>
        <slot name="trigger">
          <Icon v-if="listType === 'picture-card'" :size="24" icon="ep:plus" />
          <ElButton v-else>
            <Icon icon="mdi:upload" />
            上传文件
          </ElButton>
        </slot>
      </template>
      <template v-if="!hideUpload" #tip>
        <slot name="tip"></slot>
      </template>
      <template #file="{ file }">
        <slot :file="file" name="file"></slot>
      </template>
    </ElUpload>
    <ElImageViewer
      v-if="viewImgDialogVisible"
      hide-on-click-modal
      :url-list="modelValue.map((item) => item.signatureUrl!)"
      @close="viewImgDialogVisible = false"
    />
  </div>
</template>

<style lang="less" scoped>
.hide-upload :deep(.el-upload) {
  display: none;
}

:deep(.is-disabled > .el-upload) {
  display: none;
}

:deep(.el-upload-list--text) {
  max-height: 300px;
  overflow: auto;
}

:deep(.el-upload-dragger) {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 0;
  background-color: transparent !important;
  border: none;
  align-items: center;
  justify-content: flex-start;

  > i {
    margin: auto;
  }

  &.is-dragover {
    padding: calc(var(--el-upload-dragger-padding-horizontal) - 1px)
      calc(var(--el-upload-dragger-padding-vertical) - 1px);
    background-color: var(--el-color-primary-light-9);
    border: 2px dashed var(--el-color-primary);
  }
}
</style>
