import type { ApiMapList } from './types/index'
import { getApiTypeItem } from './localOptions'
import {
  VendorListApi,
  getListBrandApi,
  getSupplierManagerListApi,
  getProductCenterListApi
} from '@/api/common'
import { getAddressListApi } from '@/api/config'
const apiMaps: ApiMapList = {
  enableVendorList: {
    api: VendorListApi,
    configuration: {
      key: 'vendorId',
      label: 'vendorName',
      value: 'vendorName'
    }
  },
  brandList: {
    api: getListBrandApi,
    configuration: {
      key: 'id',
      label: 'brandName',
      value: 'id'
    }
  },
  supplierManagerList: {
    api: getSupplierManagerListApi,
    configuration: {
      key: 'feishuUserId',
      label: 'userName',
      value: 'userName',
      valueKey: 'feishuUserId'
    }
  },
  addressList: {
    api: getAddressListApi,
    configuration: {
      key: 'cityDict',
      label: 'cityDict_zh',
      value: 'cityDict'
    }
  },
  getProductCenterListApi: {
    api: getProductCenterListApi,
    configuration: {
      key: 'feishuUserId',
      label: 'userName',
      value: 'userName',
      valueKey: 'feishuUserId'
    }
  },
  SEEKING_CATEGORY: { api: getApiTypeItem('SEEKING_CATEGORY') },
  SEEKING_STAGE: { api: getApiTypeItem('SEEKING_STAGE') },
  STYLE_BODY: { api: getApiTypeItem('STYLE_BODY') },
  SHOES_TREE: { api: getApiTypeItem('SHOES_TREE') },
  MODEL: { api: getApiTypeItem('MODEL') },
  DEVELOP_SEASON: { api: getApiTypeItem('DEVELOP_SEASON') },
  POSITION: { api: getApiTypeItem('POSITION') },
  SEEKING_CLOSE_REASON: { api: getApiTypeItem('SEEKING_CLOSE_REASON') },
  NOTICE_STATE: { api: getApiTypeItem('NOTICE_STATE') },
  AREA_CODE: { api: getApiTypeItem('PDM_AREA') },
  SOURCE_FROM: { api: getApiTypeItem('SOURCE_FROM') },
  YES_NO: { api: getApiTypeItem('YES_NO') },
  EXPORT_AREA: { api: getApiTypeItem('EXPORT_AREA') },
  UN_SELECT_REASON: { api: getApiTypeItem('UN_SELECT_REASON') },
  RETURN_REASON: { api: getApiTypeItem('RETURN_REASON') }
}

export default apiMaps
