export type PropsObjectType = Record<string, any>
import apiMaps from '../apiMapList'

export interface Props {
  /**
   * 双向绑定的值
   */
  modelValue: any
  /**
   *选中的 Item
   */
  itemValue?: PropsObjectType
  /**
   * 是否为虚拟滚动 使用时 value 初始值 为 null 或 ref()
   * https://github.com/element-plus/element-plus/issues/9810#issuecomment-1250208868
   */
  virtualized?: boolean
  /**
   * 组件大小
   */
  size?: string
  /**
   * 是否禁用
   */
  disabled?: boolean
  /**
   * 自动化测试需要的 KEY
   */
  seleniumKey?: string
  /**
   * 传入的options 有则使用
   */
  selectOptions?: PropsObjectType[]
  // 需要调用的接口
  apiKey: keyof typeof apiMaps
  /**
   * {value: 选项的值, label: 选项的标签，若不设置则默认与 value 相同, key: 唯一值}
   */
  configuration?: PropsObjectType
  /**
   * 默认展示的长度
   */
  findLength?: number
  /**
   * 调用接口所需参数
   */
  params?: PropsObjectType
  /**
   * 是否显示清空按钮
   */
  clearable?: boolean
  /**
   * 是否需要级联
   */
  ganged?: boolean
  /**
   * 级联需要忽略的参数
   */
  gangedIgnore?: string[]
  /**
   * 级联前置条件不足时显示的消息
   */
  information?: string
  /**
   * 是否多选
   */
  multiple?: boolean
  /**
   * 多选是否折叠
   */
  collapseTags?: boolean
  /**
   * 无需确认是否传入参数即可调用 （ganged 为 true 时 没有参数也直接调用）
   */
  notRequiredParams?: boolean
  /**
   * 是否需要默认选择
   */
  isDefault?: boolean
  /**
   * 忽略的选项
   */
  ignoreList?: string[]
  /**
   * 数据处理
   */
  dataMethod?: Function
  /**
   * 提示 默认请选择
   */
  placeholder?: string
  /**
   * 宽度 可传递单位默认 px
   */
  width?: string
  /**
   * class
   */
  class?: string
  /**
   * 联动是是否清空数据
   */
  gangedClear?: boolean
}

export interface SelectReactive {
  options: any[]
  requestData: any[]
  selectConfiguration: Configuration
  loading: boolean
}
export interface Configuration {
  key: string | number
  label: string
  value: string | number
  valueKey?: string
}

interface ApiMapListItem {
  api: Function
  configuration?: Configuration
}

export type ApiMapList = Record<string, ApiMapListItem>
