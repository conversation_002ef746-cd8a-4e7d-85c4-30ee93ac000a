<script setup lang="ts">
// 如何导入定义配置文件的接口
// https://github.com/vuejs/core/issues/4294
import type { SelectReactive } from './types'
import { isObject, isEqual, isArray } from 'lodash-es'
import apiMapList from './apiMapList'
// import { ElMessage } from 'element-plus'

export type PropsObjectType = Record<string, any>
export interface SetionProps {
  /**
   * 双向绑定的值
   */
  modelValue: any
  /**
   *选中的 Item
   */
  itemValue?: PropsObjectType
  /**
   * 是否为虚拟滚动 使用时 value 初始值 为 null 或 ref()
   * https://github.com/element-plus/element-plus/issues/9810#issuecomment-1250208868
   */
  virtualized?: boolean
  /**
   * 组件大小
   */
  size?: string
  /**
   * 是否禁用
   */
  disabled?: boolean
  /**
   * 自动化测试需要的 KEY
   */
  seleniumKey?: string
  /**
   * 传入的options 有则使用
   */
  selectOptions?: PropsObjectType[]
  // 需要调用的接口 keyof typeof apiMapList
  apiKey?: keyof typeof apiMapList
  /**
   * {value: 选项的值, label: 选项的标签，若不设置则默认与 value 相同, key: 唯一值}
   */
  configuration?: PropsObjectType
  /**
   * 默认展示的长度
   */
  findLength?: number
  /**
   * 调用接口所需参数
   */
  params?: PropsObjectType
  /**
   * 是否显示清空按钮
   */
  clearable?: boolean
  /**
   * 是否需要级联
   */
  ganged?: boolean
  /**
   * 级联需要忽略的参数
   */
  gangedIgnore?: string[]
  /**
   * 级联前置条件不足时显示的消息
   */
  information?: string
  /**
   * 是否多选
   */
  multiple?: boolean
  /**
   * 多选是否折叠
   */
  collapseTags?: boolean
  /**
   * 无需确认是否传入参数即可调用 （ganged 为 true 时 没有参数也直接调用）
   */
  notRequiredParams?: boolean
  /**
   * 是否需要默认选择
   */
  isDefault?: boolean
  /**
   * 忽略的选项
   */
  ignoreList?: string[]
  /**
   * 数据处理
   */
  dataMethod?: Function
  isDisabled?: Function
  /**
   * 提示 默认请选择
   */
  placeholder?: string
  /**
   * 宽度 可传递单位默认 px
   */
  width?: string
  /**
   * class
   */
  class?: string
  // 过滤重复对象
  removeDuplicateKey?: string
  /**
   * 联动是是否清空数据
   */
  gangedClear?: boolean
}

defineOptions({
  name: 'Selection'
})
const elSelect = ref()
const props = withDefaults(defineProps<SetionProps>(), {
  modelValue: '',
  removeDuplicateKey: '',
  selectOptions: () => [],
  clearable: true,
  gangedClear: true,
  isDisabled: () => {}
})

const emits = defineEmits([
  'update:modelValue',
  'responseData',
  'visibleChange',
  'update:itemValue',
  'handleChange'
])

const dataRef: SelectReactive = reactive({
  options: [],
  requestData: [],
  selectConfiguration: {
    key: 'key',
    value: 'value',
    label: 'label'
  },
  loading: false
})
const showInformation = ref<boolean>(false)

const optionsKey = computed(() => (props.seleniumKey ? props.seleniumKey + '--option' : ''))
// 获取当前 Configuration
const optionConfiguration = computed<Recordable>(() =>
  isObject(props.configuration)
    ? Object.assign(dataRef.selectConfiguration, props.configuration)
    : dataRef.selectConfiguration
)
// 是否显示 information
const isVisibleToast = computed(() => {
  return (
    props.ganged &&
    dataRef.requestData.length === 0 &&
    dataRef.options.length === 0 &&
    props.information &&
    !isCommitParams.value
  )
})
// 是否传入了参数
const isCommitParams = computed(() => {
  const params = props.params
  if (!isObject(params)) return false
  return Object.keys(params).every((key) => {
    // 是否是级联需要忽略的参数
    const isGangedIgnore = props.gangedIgnore?.includes(key)
    //  是否存在
    const isExist = Array.isArray(params[key]) ? params[key].length !== 0 : !!params[key]
    return isGangedIgnore || isExist
  })
})
// 含默认选择的select list
const defaultSelectOptions = computed(() => {
  if (!props.isDefault) return []
  const selectValue = props.modelValue
  const configValue = dataRef.selectConfiguration.value
  const defaultItems = dataRef.requestData.filter((item) => {
    if (Array.isArray(selectValue)) {
      return selectValue.some((val) => Object.is(String(val), String(item[configValue])))
    } else {
      return Object.is(String(selectValue), String(item[configValue]))
    }
  })
  return defaultItems.concat(dataRef.requestData).reduce((acc, cur) => {
    if (acc.some((item) => item[configValue] === cur[configValue])) {
      return acc
    } else {
      acc.push(cur)
      return acc
    }
  }, [])
})

const virtualizedOptions = computed(() => {
  if (!props.virtualized || dataRef.options.length === 0) return []
  return dataRef.options.map((item) => ({
    value: item[optionConfiguration.value?.value],
    label: item[optionConfiguration.value?.label]
  }))
})

const placeholderText = computed(() => {
  // console.log(
  //   showInformation.value ? props.information : props.placeholder ? props.placeholder : '请选择'
  // )
  return showInformation.value
    ? props.information
    : props.placeholder
    ? props.placeholder
    : '请选择'
})

watch(
  () => props.params,
  (val, lodVal) => {
    // newValue === oldValue
    if (isEqual(val, lodVal)) return
    if (props.ganged) {
      // 传入了参数（除去忽略）或 无需传入参数
      if (isCommitParams.value || props.notRequiredParams) {
        queryOptions()
      } else {
        dataRef.requestData = []
        dataRef.options = []
      }
      if (props.gangedClear) emits('update:modelValue', isArray(props.modelValue) ? [] : '')
      if (isVisibleToast.value) showInformation.value = false
    }
  },
  { deep: true }
)
watch(
  () => props.selectOptions,
  (list) => {
    if (isArray(list)) {
      dataRef.requestData = props.selectOptions
      const options = Array.isArray(dataRef.requestData) ? dataRef.requestData : []
      dataRef.options = props.dataMethod ? props.dataMethod(options) : options
    }
  },
  { deep: true }
)
const visibleChangeEvent = (visible: boolean) => {
  emits('visibleChange', visible)
  if (visible) {
    if (isVisibleToast.value) {
      showInformation.value = true
      // props.placeholder = props.information
      // ElMessage.warning(props.information)
    }
  } else {
    showInformation.value = false
  }
}

const handleChangeEvent = (value) => {
  emits('handleChange', value)
}

const queryOptions = async () => {
  const { api, configuration }: any = props.apiKey ? apiMapList[props.apiKey] : {}
  if (props.selectOptions.length || !api) {
    dataRef.requestData = props.selectOptions
    const options = dataRef.requestData
    dataRef.options = props.dataMethod ? props.dataMethod(options) : options
  } else {
    dataRef.selectConfiguration = configuration || dataRef.selectConfiguration
    try {
      dataRef.loading = true
      const { datas: data } = await api(props.params)
      dataRef.requestData = data.filter(
        (item) => !props.ignoreList?.includes(item[dataRef.selectConfiguration.value])
      )
      //
      props?.removeDuplicateKey &&
        (dataRef.requestData = removeDuplicateObj(dataRef.requestData, props.removeDuplicateKey))
      emits('responseData', data)
      const options = props.isDefault ? defaultSelectOptions.value : dataRef.requestData
      dataRef.options = props.dataMethod ? props.dataMethod(data) : options
    } finally {
      dataRef.loading = false
    }
  }
}

// 数组中的对象去重
const removeDuplicateObj = (arr, key) => {
  const obj = {}
  arr = arr.reduce((newArr, next) => {
    obj[next[key]] ? '' : (obj[next[key]] = true && newArr.push(next))
    return newArr
  }, [])
  return arr
}

onMounted(() => {
  if (isCommitParams.value || props.notRequiredParams || !props.ganged) {
    queryOptions()
  }
  if (elSelect.value && props.width) {
    const symbols = ['px', 'vh', 'vw', 'em', 'rem']
    const width = symbols.some((item) => props.width?.includes(item))
      ? props.width
      : props.width + 'px'
    elSelect.value.$el.style.width = width
  }
})
const updateModelValue = (val: unknown) => {
  emits('update:modelValue', val)
  if (props.itemValue && isObject(optionConfiguration)) {
    const item = dataRef.options.find((item) => item[optionConfiguration.value.value] === val)
    emits('update:itemValue', item)
  }
}
</script>

<template>
  <ElSelectV2
    v-if="props.virtualized"
    ref="elSelect"
    :class="props.class"
    :modelValue="modelValue"
    :clearable="props.clearable"
    filterable
    :disabled="props.disabled"
    :multiple="props.multiple"
    :collapse-tags="props.collapseTags"
    :loading="dataRef.loading"
    @update:model-value="updateModelValue"
    @visible-change="visibleChangeEvent"
    :selenium-key="seleniumKey"
    :placeholder="placeholderText"
    :options="virtualizedOptions"
  />
  <ElSelect
    v-else
    ref="elSelect"
    :modelValue="modelValue"
    :clearable="props.clearable"
    :class="[props.class, { 'warning-select': showInformation }, 'w-full']"
    filterable
    :disabled="props.disabled"
    :multiple="props.multiple"
    :collapse-tags="props.collapseTags"
    :loading="dataRef.loading"
    @update:model-value="updateModelValue"
    @visible-change="visibleChangeEvent"
    @change="handleChangeEvent"
    :selenium-key="seleniumKey"
    :placeholder="placeholderText"
    :value-key="optionConfiguration.valueKey"
  >
    <ElOption
      v-for="item in dataRef.options"
      :key="item[optionConfiguration.key]"
      :options-key="optionsKey"
      :disabled="item.disabled || isDisabled(item)"
      :value-key="item[optionConfiguration.key]"
      :label="item[optionConfiguration.label] || item[optionConfiguration.value]"
      :value="optionConfiguration.valueKey ? item : item[optionConfiguration.value]"
    />
  </ElSelect>
</template>

<style lang="less" scoped>
.warning-select :deep(.el-input.is-focus .el-input__wrapper) {
  background-color: #fab6b6;
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;

  ::placeholder {
    color: #fff;
  }
}
</style>
