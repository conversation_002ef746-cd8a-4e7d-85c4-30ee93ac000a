import { useDictStoreWithOut } from '@/store/modules/dict'
import { useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()
const dictStore = useDictStoreWithOut()
const localOptions: dictArray = dictStore.getDictObj
interface SelectItem<T> {
  value: T
  label: string
}
interface dictItem<T> {
  dictValue: T
  dictName: string
  [key: string]: any
}
interface dictArray {
  [key: string]: dictItem<number | string>[]
}
// 字典

export function getLabel(key: keyof typeof localOptions, value?: number | string): string {
  const LangObj = {
    'zh-CN': 'zh-CN',
    en: 'en-US'
  }
  const langKey = LangObj[wsCache.get('lang')]
  const Arr: SelectItem<number | string>[] = []
  localOptions[key]?.map((v) => {
    if (v.dictName) Arr.push({ label: JSON.parse(v.dictName)[langKey], value: v.dictValue })
  })
  return Arr.find((item) => item.value === value)?.label || ''
}

// api
export const getApiTypeItem = (key: keyof typeof localOptions) => () =>
  new Promise((resolve) => {
    resolve({ datas: dictStore.getDictionariesOpt(dictStore.getDictObj[key]) })
  })
