/*  虚拟下拉列表 */
import { FormSchema } from '@/types/form'

export const useRenderSelectV2 = () => {
  // 渲染 select options
  const renderSelectV2Options = (item: FormSchema) => {
    // 如果有别名，就取别名
    const labelAlias = item?.componentProps?.optionsAlias?.labelField
    const valueAlias = item?.componentProps?.optionsAlias?.valueField

    item?.componentProps?.options?.map((option) => {
      const { label, value } = option

      Object.assign(option, {
        label: labelAlias ? option[labelAlias] : label,
        value: valueAlias ? option[valueAlias] : value
      })
    })

    return item?.componentProps?.options || []
  }

  return {
    renderSelectV2Options
  }
}
