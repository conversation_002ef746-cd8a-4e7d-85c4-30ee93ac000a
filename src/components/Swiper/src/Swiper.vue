<template>
  <div class="thumb-example">
    <swiper
      v-if="list.length"
      class="top-swiper"
      :style="{
        '--swiper-navigation-color': '#999',
        '--swiper-pagination-color': '#000'
      }"
      :modules="modules"
      :space-between="10"
      :navigation="true"
      :thumbs="{ swiper: thumbsSwiper, slideThumbActiveClass: 'swiper-slide-thumb-active' }"
      :loop="true"
      :observer="swiperOptions.observer"
      :observeParents="swiperOptions.observeParents"
      :autoplay="swiperOptions.autoplay"
    >
      <swiper-slide class="slide" v-for="(item, index) in list" :key="index">
        <el-image :src="(item as any).url" :preview-src-list="[(item as any).url]" />
      </swiper-slide>
    </swiper>
    <swiper
      class="thumbs-swiper"
      :modules="modules"
      :space-between="10"
      slides-per-view="auto"
      :watch-slides-progress="true"
      :prevent-clicks="false"
      :watchSlidesVisibility="true"
      :prevent-clicks-propagation="false"
      @swiper="setThumbsSwiper"
    >
      <swiper-slide class="slide" v-for="(item, index) in list" :key="index">
        <el-image :src="(item as any).url" />
      </swiper-slide>
    </swiper>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { Autoplay, Navigation, Thumbs, Mousewheel, A11y } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'
// 引入swiper组件
// 引入swiper样式（按需导入）
import 'swiper/css'
import 'swiper/css/navigation' // 轮播图两边的左右箭头
import 'swiper/css/autoplay'
import 'swiper/css/thumbs'

import type SwiperClass from 'swiper'

export default defineComponent({
  name: 'SwiperExampleThumbsGallery',
  title: 'Thumbs gallery with Two-way control',
  url: import.meta.url,
  components: {
    Swiper,
    SwiperSlide
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  setup() {
    const modules = [Autoplay, Navigation, Thumbs, Mousewheel, A11y]
    const swiperOptions = {
      autoplay: {
        delay: 3000,
        pauseOnMouseEnter: false,
        disableOnInteraction: false
      },
      observer: true,
      observeParents: true
    }
    const thumbsSwiper = ref<SwiperClass>()
    const setThumbsSwiper = (swiper: SwiperClass) => {
      thumbsSwiper.value = swiper
    }

    return {
      modules,
      setThumbsSwiper,
      thumbsSwiper,
      swiperOptions
    }
  }
})
</script>

<style lang="less" scoped>
.thumb-example {
  // width: 100%;
  width: 500px;
  background-color: transparent;
}

.top-swiper {
  display: flex;
  // justify-content: center;
  // width: 80%;
  // height: 250px;
  .swiper-slide-active {
    // display: flex;
    // justify-content: center;
  }

  :deep(.el-image__inner) {
    width: 500px;
    height: 300px;
  }
}

:deep(.thumbs-swiper) {
  display: flex;
  width: auto;
  overflow: visible !important;
  justify-content: center;

  .slide {
    width: 50px !important;
    height: 50px !important;
    cursor: pointer;
    border-radius: 10px;
    opacity: 1;
    -webkit-filter: initial; /* Safari 6.0 - 9.0 */
    filter: initial;
    object-fit: contain;

    &:not(.swiper-slide-thumb-active) {
      opacity: 0.4;
    }

    .el-image {
      height: 100% !important;

      .el-image__inner {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}
</style>
