<template>
  <el-upload
    :file-list="props.fileList"
    ref="upload"
    :action="action"
    :limit="limit"
    class="upload-demo"
    multiple
    :on-exceed="handleExceed"
    :auto-upload="autoUpload"
    :on-success="handleSuccess"
    :before-upload="beforeUpload"
    :before-remove="handleBeforeRemove"
    :on-change="handleFileChange"
    :show-file-list="showFileList"
    :on-remove="handleFileRemove"
    :on-preview="handleFilePreview"
    :accept="accept"
    :disabled="disabled"
    :http-request="handleUploadFile"
    v-bind="$attrs"
  >
    <template #trigger>
      <Icon icon="ep:paperclip" color="#409eff" v-if="showIcon" />
      <el-button type="primary" :disabled="disabled" link :loading="loading">{{
        buttonName
      }}</el-button>
    </template>
  </el-upload>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { uploadByPreUrlApi } from '@/api/system'
import type {
  UploadInstance,
  UploadProps,
  UploadRawFile,
  UploadFile,
  UploadFiles
} from 'element-plus'
import { propTypes } from '@/utils/propTypes'

const upload = ref<UploadInstance>()
const props = defineProps({
  onSuccess: propTypes.func.def(() => {}),
  autoUpload: propTypes.bool.def(true),
  clearFile: propTypes.bool.def(true),
  disabled: propTypes.bool.def(false),
  buttonName: propTypes.string.def('上传文件'),
  accept: propTypes.string.def('.xlsx, .xls'),
  types: propTypes.array.def([]),
  showFileList: propTypes.bool.def(true),
  showIcon: propTypes.bool.def(false),
  ossUpload: propTypes.bool.def(false),
  action: propTypes.string.def(''),
  limit: propTypes.number.def(1),
  uploadTip: propTypes.string.def(
    `文件后缀名必须为xls 或xlsx （即Excel格式），文件大小不得大于10M`
  ),
  fileList: propTypes.array.def([]) as any,
  size: propTypes.number.def(10),
  configCode: propTypes.string.def('0001'),
  fileAddName: propTypes.string.def('')
})
const loading = ref(false)
const emits = defineEmits(['handleFileChange', 'handleFileRemove', 'success', 'setLoading'])
const handleExceed: UploadProps['onExceed'] = (files) => {
  if (props.limit === 1) {
    upload.value!.clearFiles()
    const file = files[0] as UploadRawFile
    upload.value!.handleStart(file)
  } else {
    ElMessage.warning(`至多选择${props.limit}个文件!`)
  }
}
const handleFilePreview: UploadProps['onPreview'] = (uploadFile) => {
  if (uploadFile?.url) return window.open(uploadFile.url, '_blank')
  const url = props.fileList.find((v) => v.uid === uploadFile.uid)?.url
  url && window.open(url, '_blank')
}

const handleData = (val) => {
  return JSON.parse(JSON.stringify(val))
}
const handleClearFiles = () => {
  upload.value!.clearFiles()
}
const handleFileChange = (uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  if (beforeUpload(uploadFile?.raw as UploadRawFile)) {
    emits('handleFileChange', {
      uploadFile: uploadFile.raw,
      uploadFiles: handleData(uploadFiles)
    })
  }
}
const handleSuccess = (response) => {
  response && emits('success', response)
}
const setLoad = (boolean: boolean) => {
  loading.value = boolean
  emits('setLoading', boolean)
}
const handleBeforeRemove = (uploadFile: UploadFile) => {
  return loading.value ? uploadFile.status === 'success' : true
}
// 上传接口
const handleUploadFile = async (options) => {
  let addName = ''
  const { name: fileName, size: kbSize, uid } = options.file
  if (props.fileAddName) {
    const [name, type] = fileName.split('.')
    addName = `${props.fileAddName}-${name}.${type}`
  }
  const params = {
    fileName: props.fileAddName ? addName : fileName,
    kbSize: Math.floor(kbSize / 1024),
    fileType: fileName.substring(fileName.lastIndexOf('.') + 1),
    configCode: props.configCode
  }
  setLoad(true)
  try {
    const { datas: res } = await uploadByPreUrlApi(params)
    const {
      originName: name,
      objectName: key,
      policy,
      accessid: OSSAccessKeyId,
      callback,
      signature,
      host: url
    } = res
    const form = Object.assign(
      {},
      { name, key, policy, OSSAccessKeyId, callback, signature },
      { file: options?.file }
    )
    const formData = new FormData()
    Object.keys(form).forEach((key) => formData.set(`${key}`, form[key]))
    const result = await axios({
      method: 'post',
      url,
      data: formData
    })
    let emitObj = Object.assign(result.data.datas, { uid })
    result?.data?.datas && options.onSuccess(emitObj)
    setLoad(false)
  } finally {
    setLoad(false)
  }
}
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const type = rawFile.name.split('.').pop()
  const isType = props.types.includes(type)
  const errorSize = rawFile.size / 1024 / 1024 > props.size
  if ((props.types.length && !isType) || errorSize) {
    ElMessage.warning(`请上传正确格式文件,且文件大小不得大于${props.size}MB!`)
    props.clearFile && upload.value!.clearFiles()
    return false
  }
  return true
}
const handleFileRemove = (uploadFiles) => {
  emits('handleFileRemove', { uploadFiles: handleData(uploadFiles) })
}
defineExpose({
  handleClearFiles
})
</script>
