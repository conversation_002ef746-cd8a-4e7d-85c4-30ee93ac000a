<script lang="ts" setup>
import { ElDialog, ElScrollbar } from 'element-plus'
import { propTypes } from '@/utils/propTypes'
import { computed, nextTick, ref, unref, useAttrs, useSlots, watch } from 'vue'
import { isNumber } from '@/utils/is'
import { omit } from 'lodash-es'

const slots = useSlots()
const emit = defineEmits<{
  (e: 'fullscreen', val: boolean): void
}>()

const props = defineProps({
  modelValue: propTypes.bool.def(false),
  title: propTypes.string.def('Dialog'),
  fullscreen: propTypes.bool.def(true),
  width: propTypes.oneOfType([String, Number]).def('90vw'),
  maxHeight: propTypes.oneOfType([String, Number]).def('80vh'),
  loading: propTypes.bool.def(false),
  parentScroll: propTypes.bool.def(true)
})

const getBindValue = computed(() => {
  const delArr = ['fullscreen', 'title', 'maxHeight']
  const attrs = useAttrs()
  const obj = { ...attrs, ...props }

  // 使用 omit 删除 delArr 中的键
  return omit(obj, delArr)
})

const isFullscreen = ref(false)

const toggleFull = () => {
  isFullscreen.value = !unref(isFullscreen)
}

const dialogHeight = ref(isNumber(props.maxHeight) ? `${props.maxHeight}px` : props.maxHeight)

watch(
  () => isFullscreen.value,
  async (val: boolean) => {
    await nextTick()
    if (val) {
      const windowHeight = document.documentElement.offsetHeight
      dialogHeight.value = `${windowHeight - 55 - 60 - (slots.footer ? 63 : 0)}px`
    } else {
      dialogHeight.value = isNumber(props.maxHeight) ? `${props.maxHeight}px` : props.maxHeight
    }
    emit('fullscreen', val)
  },
  {
    immediate: true
  }
)

const dialogStyle = computed(() => {
  return {
    height: unref(dialogHeight)
  }
})
</script>

<template>
  <ElDialog
    :close-on-click-modal="false"
    :fullscreen="isFullscreen"
    :width="width"
    destroy-on-close
    draggable
    lock-scroll
    v-bind="getBindValue"
  >
    <template #header>
      <div class="flex justify-between">
        <slot name="title">
          {{ title }}
        </slot>
        <Icon
          v-if="fullscreen"
          :icon="isFullscreen ? 'zmdi:fullscreen-exit' : 'zmdi:fullscreen'"
          class="cursor-pointer is-hover mt-1 z-10"
          color="var(--el-color-info)"
          @click="toggleFull"
        />
      </div>
    </template>
    <template v-if="props.parentScroll">
      <ElScrollbar v-loading="loading" :max-height="dialogStyle.height">
        <slot></slot>
      </ElScrollbar>
    </template>
    <template v-else>
      <slot></slot>
    </template>

    <template v-if="slots.footer" #footer>
      <div class="flex justify-end">
        <slot name="footer"></slot>
      </div>
    </template>
  </ElDialog>
</template>

<style lang="less">
.@{elNamespace}-dialog__header {
  margin-right: 0 !important;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid var(--tags-view-border-color);

  .el-dialog__headerbtn {
    margin-top: 8px;
  }
}

.@{elNamespace}-dialog__footer {
  border-top: 1px solid var(--tags-view-border-color);
}

.is-hover {
  &:hover {
    color: var(--el-color-primary) !important;
  }
}

.dark {
  .@{elNamespace}-dialog__header {
    border-bottom: 1px solid var(--el-border-color);
  }

  .@{elNamespace}-dialog__footer {
    border-top: 1px solid var(--el-border-color);
  }
}
</style>
