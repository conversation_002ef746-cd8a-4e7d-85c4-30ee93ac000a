import { ElCascader } from 'element-plus'
import 'element-plus/theme-chalk/el-cascader.css'

export default {
  extends: ElCascader,
  setup: ElCascader.setup,
  props: {
    filterable: {
      type: Boolean,
      default: true
    },
    filterMethod: {
      type: Function,
      default: (node, keyword) => {
        return node.text.toLowerCase().indexOf(keyword.toLowerCase()) !== -1
      }
    }
  }
}
