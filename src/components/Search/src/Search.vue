<script lang="ts" setup>
import { Form } from '@/components/Form'
import { PropType, ref, unref } from 'vue'
import { propTypes } from '@/utils/propTypes'
import { ElButton } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useForm } from '@/hooks/web/useForm'
import { FormSchema } from '@/types/form'
import { cloneDeep } from 'lodash-es'
import { Icon } from '@/components/Icon'
import { watchDebounced } from '@vueuse/core'

const TOTAL_COL_PER_ROW = 24

const { t } = useI18n()

const props = defineProps({
  // 生成Form的布局结构数组
  schema: {
    type: Array as PropType<FormSchema[]>,
    default: () => []
  },
  // 是否需要栅格布局
  isCol: propTypes.bool.def(true),
  // 每行form-item个数，优先级低于每个form-item内的colProps
  colNum: propTypes.number.def(3),
  // 表单label宽度
  labelWidth: propTypes.oneOfType([String, Number]).def('auto'),
  showSearch: propTypes.bool.def(true),
  showReset: propTypes.bool.def(true),
  // 是否显示伸缩
  expand: propTypes.bool.def(true),
  inline: propTypes.bool.def(false),
  loading: propTypes.bool.def(false),
  defaultCollapse: propTypes.bool.def(true),
  queryLoading: propTypes.bool.def(false),
  showExport: propTypes.bool.def(false),
  showDetailExport: propTypes.bool.def(false),
  exportPermission: propTypes.array.def(['*']),
  exportDetailPermission: propTypes.array.def(['*']),
  searchAfterChange: propTypes.bool.def(true)
})

const emit = defineEmits(['search', 'reset', 'handleExport', 'handleDetailExport'])

const visible = ref(!props.defaultCollapse)

const newSchema = computed(() => {
  let schema: FormSchema[] = []
  if (props.isCol && props.colNum) {
    const span = Math.floor(TOTAL_COL_PER_ROW / props.colNum)
    props.schema.map((v) => {
      schema.push({
        colProps: {
          span
        },
        ...v
      })
    })
  } else {
    schema = cloneDeep(props.schema)
  }
  if (props.expand && !unref(visible)) {
    const length = schema.length
    schema.splice(props.colNum, length)
  }
  return schema
})

const { register, elFormRef, methods } = useForm()

const search = async () => {
  await unref(elFormRef)?.validate(async (isValid) => {
    if (isValid) {
      const { getFormData } = methods
      const model = await getFormData()
      emit('search', model)
    }
  })
}

const reset = async () => {
  unref(elFormRef)?.resetFields()
  const { getFormData } = methods
  const model = await getFormData()
  emit('reset', model)
}
const handleExport = async () => {
  const { getFormData } = methods
  const model = await getFormData()
  emit('handleExport', model)
}
const setVisible = () => {
  visible.value = !unref(visible)
}
const handleDetailExport = async () => {
  const { getFormData } = methods
  const model = await getFormData()
  emit('handleDetailExport', model)
}

const formRef = ref()
const formModel = computed(() => {
  return formRef.value.formModel
})

onMounted(() => {
  watchDebounced(
    () => [props.searchAfterChange, formModel],
    async ([flag]) => {
      if (flag) {
        await search()
      }
    },
    { deep: true, debounce: 400 }
  )
})

const { setProps, setValues, setSchema, addSchema, delSchema, getFormData } = methods
defineExpose({
  setProps,
  setValues,
  setSchema,
  addSchema,
  delSchema,
  getFormData
})
</script>

<template>
  <div class="flex justify-content-center">
    <Form
      ref="formRef"
      :inline="inline"
      :is-col="isCol"
      :is-custom="false"
      :label-width="labelWidth"
      :schema="newSchema"
      class="flex-1"
      hide-required-asterisk
      label-position="left"
      @register="register"
    />
    <ElButton v-if="showSearch" :loading="queryLoading" type="primary" @click="search">
      <Icon class="mr-1" icon="ep:search" />
      {{ t('common.query') }}
    </ElButton>
    <ElButton v-if="showReset" @click="reset">
      <Icon class="mr-1" icon="ep:refresh-right" />
      {{ t('common.reset') }}
    </ElButton>
    <ElButton
      v-if="showExport"
      v-hasPermi="props.exportPermission"
      :loading="loading"
      type="primary"
      @click="handleExport"
    >
      <Icon class="mr-1" icon="ep:upload-filled" />
      导出
    </ElButton>
    <ElButton
      v-if="showDetailExport"
      v-hasPermi="props.exportDetailPermission"
      :loading="loading"
      type="primary"
      @click="handleDetailExport"
    >
      <Icon class="mr-1" icon="ep:upload-filled" />
      导出明细
    </ElButton>
    <ElButton v-if="expand" text @click="setVisible">
      {{ t(visible ? 'common.shrink' : 'common.expand') }}
      <Icon :icon="visible ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
    </ElButton>
  </div>
</template>
