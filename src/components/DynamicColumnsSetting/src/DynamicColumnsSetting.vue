<script lang="ts" setup>
import { VxeGridInstance, VxeTableDefines, VxeTableInstance } from 'vxe-table'
import { cloneDeep } from 'lodash-es'

defineOptions({
  name: 'DynamicColumnsSetting'
})

interface Props {
  modelValue: boolean
  title?: string
  tableRef?: VxeTableInstance | VxeGridInstance
  localStorageKey?: string
  useStore?: boolean
  disabledColumns: VxeTableDefines.ColumnInfo[]
}

interface Emits {
  (e: 'update:modelValue', val: boolean): void
  (e: 'change', val: VxeTableDefines.ColumnInfo[]): void
}

const defaultLocalStorageKey = 'column-visibility'

const props = withDefaults(defineProps<Props>(), {
  title: '自定义列配置',
  localStorageKey: defaultLocalStorageKey
})
const emit = defineEmits<Emits>()

const visible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

const localStorageKey = props.localStorageKey
const tableData = ref<VxeTableDefines.ColumnInfo[]>([])

const storage = useLocalStorage<VxeTableDefines.ColumnInfo[]>(localStorageKey, [])

if (props.useStore && localStorageKey === defaultLocalStorageKey) {
  console.warn(
    `[DynamicColumnsSetting]: localStorageKey is not set, use default value ${defaultLocalStorageKey}`
  )
}

const allSelected = computed({
  get() {
    return tableData.value.every((column) => column.visible)
  },
  set(val) {
    tableData.value = tableData.value.map((column) => {
      const isDisabled = props.disabledColumns.find((e) => e.field === column.field)
      if (isDisabled) {
        return column
      }
      return {
        ...column,
        visible: val
      }
    })
  }
})

const someSelected = computed(() => {
  return tableData.value.some((column) => column.visible) && !allSelected.value
})

const initializeTableData = () => {
  if (!props.tableRef) return

  const { fullColumn } = props.tableRef.getTableColumn()
  const savedStates = storage.value
  if (savedStates.length && props.useStore) {
    tableData.value = cloneDeep(savedStates)
    props.tableRef?.reloadColumn(tableData.value)
  } else {
    tableData.value = cloneDeep(fullColumn).map((e) => {
      if (e.type === 'seq') {
        return {
          ...e,
          title: e.title || '序号'
        } as VxeTableDefines.ColumnInfo
      }
      if (e.type === 'checkbox') {
        return {
          ...e,
          title: e.title || '多选'
        } as VxeTableDefines.ColumnInfo
      }
      if (e.type === 'radio') {
        return {
          ...e,
          title: e.title || '单选'
        } as VxeTableDefines.ColumnInfo
      }
      if (e.type === 'expand') {
        return {
          ...e,
          title: e.title || '展开'
        } as VxeTableDefines.ColumnInfo
      }
      return e
    })
  }
}

const saveColumnStates = () => {
  storage.value = tableData.value
  props.tableRef?.reloadColumn(tableData.value)
  emit('change', tableData.value)
}

const handleCancel = () => {
  visible.value = false
}

const handleClose = () => {
  const { fullColumn } = props.tableRef?.getTableColumn() || {}
  tableData.value = cloneDeep(fullColumn || [])
}

const handleConfirm = () => {
  saveColumnStates()
  visible.value = false
}

watch(visible, (val) => {
  if (val) {
    initializeTableData()
  }
})
</script>

<template>
  <ElDrawer
    v-model="visible"
    :title="props.title"
    class="dynamic-columns-setting"
    v-bind="$attrs"
    @close="handleClose"
  >
    <ElTable :data="tableData" border max-height="calc(100vh - 62px - 80px - 40px)">
      <ElTableColumn align="center" width="40">
        <template #header>
          <ElCheckbox v-model="allSelected" :indeterminate="someSelected" />
        </template>
        <template #default="{ row }">
          <ElCheckbox
            v-model="row.visible"
            :disabled="!!props.disabledColumns.find((e) => e.field === row.field)"
          />
        </template>
      </ElTableColumn>
      <ElTableColumn align="center" label="列名" prop="title" />
    </ElTable>
    <template #footer>
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton type="primary" @click="handleConfirm">确定</ElButton>
    </template>
  </ElDrawer>
</template>

<style lang="less" scoped>
:deep(.el-drawer__header) {
  margin-bottom: 0;
}
</style>
