<script lang="ts" setup>
import echarts from '@/plugins/echarts'
import * as echart from 'echarts'
import { debounce, isString } from 'lodash-es'
import 'echarts-wordcloud'
import { propTypes } from '@/utils/propTypes'
import { computed, onActivated, onBeforeUnmount, onMounted, PropType, ref, unref, watch } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'

const { getPrefixCls, variables } = useDesign()

const prefixCls = getPrefixCls('echart')

const appStore = useAppStore()

const props = defineProps({
  options: {
    type: Object as PropType<echart.EChartsOption>,
    required: true
  },
  width: propTypes.oneOfType([Number, String]).def(''),
  height: propTypes.oneOfType([Number, String]).def('500px'),
  isClick: propTypes.bool.def(false)
})

const isDark = computed(() => appStore.getIsDark)

const theme = computed(() => {
  return unref(isDark) ? true : 'auto'
})

const options = computed(() => {
  return Object.assign(props.options, {
    darkMode: unref(theme)
  })
})

const elRef = ref<ElRef>()

const echartRef = shallowRef<echarts.ECharts>()

const contentEl = ref<Element>()

const styles = computed(() => {
  const width = isString(props.width) ? props.width : `${props.width}px`
  const height = isString(props.height) ? props.height : `${props.height}px`

  return {
    width,
    height
  }
})
const emit = defineEmits(['sonData'])
const initChart = () => {
  if (unref(elRef) && props.options) {
    echartRef.value = echarts.init(unref(elRef) as HTMLElement)
    echartRef.value?.setOption(unref(options))
    if (props.isClick) {
      echartRef.value.on('click', function (params) {
        emit('sonData', params)
      })
    }
  }
}

watch(
  () => options.value,
  (val) => {
    echartRef.value?.setOption(val, { notMerge: true })
  },
  {
    deep: true
  }
)

const resizeHandler = debounce(() => {
  if (echartRef.value) {
    echartRef.value.resize()
  }
}, 100)

const contentResizeHandler = async (e: TransitionEvent) => {
  if (e.propertyName === 'width') {
    resizeHandler()
  }
}

onMounted(() => {
  initChart()

  window.addEventListener('resize', resizeHandler)

  contentEl.value = document.getElementsByClassName(`${variables.namespace}-layout-content`)[0]
  unref(contentEl) &&
    (unref(contentEl) as Element).addEventListener('transitionend', contentResizeHandler)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeHandler)
  unref(contentEl) &&
    (unref(contentEl) as Element).removeEventListener('transitionend', contentResizeHandler)
})

onActivated(() => {
  if (echartRef.value) {
    echartRef.value.resize()
  }
})

defineExpose({
  echartRef
})
</script>

<template>
  <div ref="elRef" :class="[$attrs.class, prefixCls]" :style="styles"></div>
</template>
