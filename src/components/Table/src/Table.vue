<script lang="tsx">
import { ElPagination } from 'element-plus'
import { PropType } from 'vue'
import { propTypes } from '@/utils/propTypes'
import { getSlot } from '@/utils/tsxHelper'
import type { TableProps } from './types'
import { Pagination, TableColumn, TableSlotDefault } from '@/types/table'
import { VXETable } from 'vxe-table'

export default defineComponent({
  name: 'Table',
  props: {
    // 表头
    columns: {
      type: Array as PropType<TableColumn[]>,
      default: () => []
    },
    // 数据
    data: {
      type: Array as PropType<Recordable[]>,
      default: () => []
    },
    // 省略
    showOverflow: propTypes.bool.def(true),

    // 加载状态
    loading: propTypes.bool.def(false),
    expand: propTypes.bool.def(false),
    // 是否展示分页
    pagination: {
      type: Object as PropType<Pagination>,
      default: (): Pagination | undefined => undefined
    },
    pageSize: propTypes.number.def(10),
    currentPage: propTypes.number.def(1),

    // 对齐方式
    align: propTypes.string
      .validate((v: string) => ['left', 'center', 'right'].includes(v))
      .def('center'),
    // 表头对齐方式
    headerAlign: propTypes.string
      .validate((v: string) => ['left', 'center', 'right'].includes(v))
      .def('center')
  },
  emits: [
    'update:pageSize',
    'update:currentPage',
    'update:radioChange', // 更新单选
    'update:checkboxChange', // 更新多选
    'register',
    'update:sortChange',
    'update:toggleRowExpand'
  ],
  setup(props, { attrs, slots, emit, expose }) {
    const vxeTableRef = ref<ComponentRef<typeof VXETable>>()
    // 注册
    onMounted(() => {
      const tableRef = unref(vxeTableRef)
      emit('register', tableRef?.$parent, vxeTableRef)
    })

    // useVxeTable传入的props
    const outsideProps = ref<TableProps>({})
    const mergeProps = ref<TableProps>({})
    const getProps = computed(() => {
      const propsObj = { ...props }
      Object.assign(propsObj, unref(mergeProps))
      return propsObj
    })

    // 动态设置表单属性
    const setProps = (props: TableProps = {}) => {
      mergeProps.value = Object.assign(unref(mergeProps), props)
      outsideProps.value = props
    }

    // 多选
    const selections = ref<Recordable[]>([])
    const selectionChange = (selection) => {
      if (!selection) return
      selections.value = selection
      emit('update:checkboxChange', selection?.records?.length ? selections.value : { records: [] })
    }
    const toggleRowExpand = ({ expanded, row, rowIndex }) => {
      emit('update:toggleRowExpand', { expanded, row, rowIndex })
    }
    // 单选
    const selected = ref<Recordable[]>([])
    const radioChange = (select: Recordable[]) => {
      selected.value = select
      emit('update:radioChange', selected.value)
    }
    const sortChange = (val) => {
      emit('update:sortChange', val)
    }
    expose({
      setProps,
      selected,
      selections,
      vxeTableRef,
      selectionChange
    })

    const pagination = computed(() => {
      return Object.assign(
        {
          small: false,
          background: false,
          pagerCount: 7,
          layout: 'sizes, prev, pager, next, jumper, ->, total',
          pageSizes: [10, 20, 30, 40, 50, 100, 500, 1000],
          disabled: false,
          hideOnSinglePage: false,
          total: 10
        },
        unref(getProps).pagination
      )
    })
    // 页码
    const pageSizeRef = ref(props.pageSize)
    watch(
      () => unref(getProps).pageSize,
      (val: number) => {
        pageSizeRef.value = val
      }
    )
    watch(
      () => pageSizeRef.value,
      (val: number) => {
        emit('update:pageSize', val)
      }
    )
    // 当前页数
    const currentPageRef = ref(props.currentPage)
    watch(
      () => unref(getProps).currentPage,
      (val: number) => {
        currentPageRef.value = val
      }
    )
    watch(
      () => currentPageRef.value,
      (val: number) => {
        emit('update:currentPage', val)
      }
    )
    // 参数透传
    const getBindValue = computed(() => {
      const bindValue: Recordable = { ...attrs, ...props }
      Reflect.deleteProperty(bindValue, 'columns')
      Reflect.deleteProperty(bindValue, 'data')
      Reflect.deleteProperty(bindValue, 'type')
      return bindValue
    })

    // 渲染表头
    const rnderTableColumn = (columnsChildren?: TableColumn[]) => {
      const { columns, align, headerAlign } = unref(getProps)
      return [...[renderTableExpand()]].concat(
        (columnsChildren || columns).map((v) => {
          const props = { ...v }
          if (props.children) {
            Reflect.deleteProperty(props, 'children')
          }
          if (slots[v.field] || slots[`${v.field}-header`] || slots[`${v.field}-edit`]) {
            return (
              <vxe-column align={align} headerAlign={headerAlign} {...props} prop={v.field}>
                {{
                  default: (data: TableSlotDefault) =>
                    getSlot(slots, v.field, data) ||
                    v?.formatter?.(data.row, data.column, data.row[v.field], data.$index) ||
                    data.row[v.field],
                  header: () => getSlot(slots, `${v.field}-header`) || v.label,
                  edit: (data: TableSlotDefault) =>
                    v.editRender ? getSlot(slots, `${v.field}-edit`, data) : undefined
                }}
              </vxe-column>
            )
          }
          return <vxe-column prop={v.field} title={props.label} {...props}></vxe-column>
        })
      )
    }

    const renderTableExpand = () => {
      const { align, headerAlign, expand } = unref(getProps)
      // 渲染展开行
      return expand ? (
        <vxe-column type="expand" align={align} headerAlign={headerAlign}>
          {{
            content: (data: TableSlotDefault) => getSlot(slots, 'expand', data)
          }}
        </vxe-column>
      ) : undefined
    }

    return () => (
      <div>
        <vxe-table
          ref={vxeTableRef}
          data={unref(getProps).data}
          emptyText={'暂无数据'}
          onCheckbox-change={selectionChange}
          onCheckbox-all={selectionChange}
          onRadio-change={radioChange}
          onSort-change={sortChange}
          auto-resize
          resizable
          show-overflow={unref(getProps).showOverflow}
          highlight-current-row
          highlight-hover-row
          onToggle-row-expand={toggleRowExpand}
          border
          max-height="600px"
          align="center"
          {...unref(getBindValue)}
        >
          {{
            default: () => rnderTableColumn()
          }}
        </vxe-table>
        {unref(getProps).pagination ? (
          <ElPagination
            v-model:pageSize={pageSizeRef.value}
            v-model:currentPage={currentPageRef.value}
            class="mt-[10px]"
            {...unref(pagination)}
          ></ElPagination>
        ) : undefined}
      </div>
    )
  }
})
</script>
