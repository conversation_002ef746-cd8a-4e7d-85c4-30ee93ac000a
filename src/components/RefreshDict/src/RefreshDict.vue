<script lang="ts" setup>
import { Icon } from '@/components/Icon'
import { propTypes } from '@/utils/propTypes'
import { ElMessage } from 'element-plus'
import { useDesign } from '@/hooks/web/useDesign'
import { refreshDict } from '@/api/common'

defineProps({
  color: propTypes.string.def('')
})

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('refresh-dict')

const handleRefreshDict = async () => {
  const [error, result] = await refreshDict()
  if (error) {
    return
  }
  ElMessage.success(result?.msg || '刷新成功')
}
</script>

<template>
  <div :class="prefixCls" @click="handleRefreshDict">
    <ElTooltip content="刷新字典缓存">
      <Icon :color="color" :size="18" icon="ep:refresh-right" />
    </ElTooltip>
  </div>
</template>

<style lang="less" scoped></style>
