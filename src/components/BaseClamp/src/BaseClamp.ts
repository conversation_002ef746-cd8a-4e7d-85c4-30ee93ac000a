import { addListener, removeListener } from 'resize-detector'
import { CSSProperties } from 'vue'

export default defineComponent({
  name: 'BaseClamp',
  props: {
    tag: {
      type: String,
      default: 'div'
    },
    autoresize: {
      type: Boolean,
      default: false
    },
    maxLines: Number,
    maxHeight: [String, Number],
    ellipsis: {
      type: String,
      default: '…'
    },
    location: {
      type: String,
      default: 'end',
      validator(value: string) {
        return ['start', 'middle', 'end'].indexOf(value) !== -1
      }
    },
    expanded: Boolean
  },
  data() {
    return {
      offset: 0,
      unregisterResizeCallback: null,
      text: this.getText(),
      localExpanded: this.expanded
    }
  },
  computed: {
    clampedText() {
      if (this.location === 'start') {
        return this.ellipsis + (this.text.slice(-Number(this.offset)) || '').trim()
      } else if (this.location === 'middle') {
        const split = Math.floor(Number(this.offset) / 2)
        return (
          (this.text.slice(0, split) || '').trim() +
          this.ellipsis +
          (this.text.slice(-split) || '').trim()
        )
      }

      return (this.text.slice(0, this.offset) || '').trim() + this.ellipsis
    },
    isClamped() {
      if (!this.text) {
        return false
      }
      return this.offset !== this.text.length
    },
    realText() {
      return this.isClamped ? this.clampedText : this.text
    },
    realMaxHeight() {
      if (this.localExpanded) {
        return null
      }
      const { maxHeight } = this
      if (!maxHeight) {
        return null
      }
      return typeof maxHeight === 'number' ? `${maxHeight}px` : maxHeight
    }
  },
  watch: {
    expanded(val) {
      this.localExpanded = val
    },
    localExpanded(val) {
      if (val) {
        this.clampAt(this.text.length)
      } else {
        this.update()
      }
      if (this.expanded !== val) {
        this.$emit('update:expanded', val)
      }
    },
    isClamped: {
      handler(val) {
        this.$nextTick(() => this.$emit('clampchange', val))
      },
      immediate: true
    }
  },
  mounted() {
    this.init()

    this.$watch(
      (vm) => [vm.maxLines, vm.maxHeight, vm.ellipsis, vm.isClamped, vm.location].join(),
      this.update
    )
    this.$watch((vm) => [vm.tag, vm.text, vm.autoresize].join(), this.init)
  },
  updated() {
    this.text = this.getText()
    this.applyChange()
  },
  beforeUnmount() {
    this.cleanUp()
  },
  methods: {
    init() {
      const contents = this.$slots.default
      if (!contents) {
        return
      }

      this.offset = this.text.length

      this.cleanUp()

      if (this.autoresize) {
        addListener(this.$el, this.update)
        //@ts-ignore
        this.unregisterResizeCallback = () => {
          removeListener(this.$el, this.update)
        }
      }
      this.update()
    },
    update() {
      if (this.localExpanded) {
        return
      }
      this.applyChange()
      if (this.isOverflow() || this.isClamped) {
        this.search()
      }
    },
    expand() {
      this.localExpanded = true
    },
    collapse() {
      this.localExpanded = false
    },
    toggle() {
      this.localExpanded = !this.localExpanded
    },
    getLines() {
      return Object.keys(
        Array.prototype.slice
          .call((this.$refs.content as HTMLElement).getClientRects())
          .reduce((prev, { top, bottom }) => {
            const key = `${top}/${bottom}`
            if (!prev[key]) {
              prev[key] = true
            }
            return prev
          }, {})
      ).length
    },
    isOverflow() {
      if (!this.maxLines && !this.maxHeight) {
        return false
      }

      if (this.maxLines) {
        if (this.getLines() > this.maxLines) {
          return true
        }
      }

      if (this.maxHeight) {
        if (this.$el.scrollHeight > this.$el.offsetHeight) {
          return true
        }
      }
      return false
    },
    getText() {
      // Look for the first non-empty text node
      const [content] = (this.$slots.default?.() || []).filter(
        (node) => typeof node.children === 'string'
      )
      return content ? (content.children as string).trim() : ''
    },
    moveEdge(steps: number) {
      this.clampAt(Number(this.offset) + steps)
    },
    clampAt(offset: number) {
      this.offset = offset
      this.applyChange()
    },
    applyChange() {
      ;(this.$refs.text as HTMLElement).textContent = this.realText
    },
    stepToFit() {
      this.fill()
      this.clamp()
    },
    fill() {
      while (
        (!this.isOverflow() || this.getLines() < 2) &&
        Number(this.offset) < this.text.length
      ) {
        this.moveEdge(1)
      }
    },
    clamp() {
      while (this.isOverflow() && this.getLines() > 1 && Number(this.offset) > 0) {
        this.moveEdge(-1)
      }
    },
    search(...range: number[]) {
      const [from = 0, to = this.offset] = range
      if (Number(to) - from <= 3) {
        this.stepToFit()
        return
      }
      const target = Math.floor((Number(to) + from) / 2)
      this.clampAt(target)
      if (this.isOverflow()) {
        this.search(from, target)
      } else {
        this.search(target, Number(to))
      }
    },
    cleanUp() {
      if (this.unregisterResizeCallback && typeof this.unregisterResizeCallback === 'function') {
        //@ts-ignore
        this.unregisterResizeCallback()
      }
    }
  },
  render() {
    const contents = [
      h(
        'span',
        {
          ref: 'text',
          attrs: {
            'aria-label': this.text.trim()
          }
        },
        this.realText
      )
    ]

    const { expand, collapse, toggle } = this
    const scope = {
      expand,
      collapse,
      toggle,
      clamped: this.isClamped,
      expanded: this.localExpanded
    }
    const before = this.$slots.before ? this.$slots.before(scope) : this.$slots.before
    if (before) {
      contents.unshift(...(Array.isArray(before) ? before : [before]))
    }
    const after = this.$slots.after ? this.$slots.after(scope) : this.$slots.after
    if (after) {
      contents.push(...(Array.isArray(after) ? after : [after]))
    }
    const lines = [
      h(
        'span',
        {
          style: {
            boxShadow: 'transparent 0 0'
          },
          ref: 'content'
        },
        contents
      )
    ]
    return h(
      this.tag,
      {
        ...this.$attrs,
        style: {
          maxHeight: this.realMaxHeight,
          overflow: 'hidden',
          wordWrap: 'break-word',
          whiteSpace: 'normal',
          width: '100%',
          ...(this.$attrs.style as CSSProperties)
        },
        onMouseover: () => {
          this.$emit('mouseover', {
            text: this.text,
            currentTarget: this.$el,
            clamped: this.isClamped
          })
        }
      },
      lines
    )
  }
})
