<script lang="ts" setup>
import type { CascaderNode } from 'element-plus'
import type { ElCascaderInstance } from './types.ts'
import { ElCascader } from 'element-plus'

type Props = /* @vue-ignore */ ElCascaderInstance['$props']
defineOptions({
  name: 'ElCascader'
})
const props = defineProps<Props>()
const cascaderRef = ref<InstanceType<typeof ElCascader>>()
function defaultFilterMethod(node: CascaderNode, keyword: string) {
  return node.text.toLowerCase().includes(keyword.toLowerCase())
}

const attrs: Record<string, unknown> = useAttrs()
const bindProps = computed(() => {
  const { filterMethod, ...rest } = props

  return {
    ...attrs,
    ...rest,
    filterMethod: filterMethod || defaultFilterMethod
  }
})
defineExpose({
  cascaderRef
})
</script>

<template>
  <ElCascader ref="cascaderRef" class="w-full" v-bind="bindProps" popper-class="is-light" />
</template>

<style lang="scss" scoped></style>
