<script lang="ts" setup>
import { useI18n } from '@/hooks/web/useI18n'
import { propTypes } from '@/utils/propTypes'
import type { FormInstance } from 'element-plus'
import { cloneDeep } from 'lodash-es'
let cloneFormData: null | Record<string, any> = null
const { t } = useI18n()
const props = defineProps({
  formValue: propTypes.object.def({}),
  rules: propTypes.object.def({}),
  labelWidth: propTypes.string.def(''),
  loading: propTypes.bool.def(false)
})
const formRef = ref<FormInstance>()
const search = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      // 校验成功
      emit('search')
    } else {
      // 校验失败
      console.error('error submit!', fields)
    }
  })
}
const emit = defineEmits(['search', 'reset', 'update:formValue'])
const reset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  if (Object.keys(props.rules).length > 0) {
    formEl.resetFields()
  }
  emit('reset')
  emit('update:formValue', Object.assign(props.formValue, cloneFormData))
}
onMounted(() => {
  cloneFormData = cloneDeep(props.formValue)
})
</script>
<template>
  <el-form inline :model="formValue" :label-width="labelWidth" ref="formRef" :rules="rules">
    <div>
      <slot></slot>
      <el-form-item>
        <el-button type="primary" @click="search(formRef)" :loading="loading">
          {{ t('common.query') }}
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button @click="reset(formRef)" :loading="loading">
          {{ t('common.reset') }}
        </el-button>
      </el-form-item>
    </div>
  </el-form>
</template>
<style lang="less" scoped></style>
