<script lang="ts" setup>
import Editor from '@tinymce/tinymce-vue'
import { Editor as TinyMCEEditor, RawEditorOptions } from 'tinymce'
import { uploadFilesWithoutExpire } from '@/views/basic-library-manage/api/upload'
import { PropType } from 'vue'
import { ElLoading, ElMessage } from 'element-plus'

defineOptions({
  name: 'Tinymce'
})

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  plugins: {
    type: String,
    default:
      'preview importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen image link media codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount help charmap quickbars emoticons accordion'
  },
  toolbar: {
    type: String,
    default:
      'undo redo | forecolor backcolor removeformat | link image table media | blocks fontfamily fontsize | bold italic underline strikethrough | align numlist bullist | accordion accordionremove | lineheight outdent indent | charmap emoticons | code fullscreen preview | save print | pagebreak anchor codesample | ltr rtl'
  },
  menubar: {
    type: String,
    default: 'file edit view insert format tools table help'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  height: {
    type: [Number, String],
    default: 600
  },
  width: {
    type: [Number, String],
    default: '100%'
  },
  initOptions: {
    type: Object as PropType<RawEditorOptions>,
    default: () => ({})
  },
  useOss: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'uploading', 'uploaded'])

const tinymceRef = shallowRef<InstanceType<typeof Editor>>()
const editorInstance = shallowRef<TinyMCEEditor>()

const useDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
// const isSmallScreen = window.matchMedia('(max-width: 1023.5px)').matches

let uploadQueue: Promise<string>[] = []
let isUploading: ReturnType<typeof ElLoading.service> | null = null
const setImageWidth = (url: string) => {
  setTimeout(() => {
    const editor = editorInstance.value
    if (editor) {
      const doc = editor.getDoc()
      const img = doc.querySelector(`img[src="${url}"]`) as HTMLImageElement
      if (img && img.naturalWidth > 200) {
        img.setAttribute('width', '200px')
      } else {
        setImageWidth(url)
      }
    }
  }, 200)
}

const handleUploadImage: RawEditorOptions['images_upload_handler'] = (blobInfo) => {
  const uploadTask = async () => {
    const file = new File([blobInfo.blob()], blobInfo.filename())
    const [error, result] = await uploadFilesWithoutExpire([file])
    if (!error && result?.datas) {
      const imgUrl = result.datas[0]?.signatureUrl || ''
      setImageWidth(imgUrl)
      return imgUrl
    } else {
      setTimeout(() => {
        ElMessage.error('图片上传OSS失败，请重新上传')
      }, 3200)
      return ''
    }
  }
  const taskPromise = uploadTask()
  uploadQueue.push(taskPromise)
  if (!isUploading) {
    isUploading = ElLoading.service({
      fullscreen: true,
      lock: true,
      text: '上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    emit('uploading')
  }
  taskPromise.finally(() => {
    // 从队列中移除已完成的任务
    uploadQueue = uploadQueue.filter((task) => task !== taskPromise)

    // 如果队列为空，表示所有文件都上传完成，emit 'uploading: false'
    if (uploadQueue.length === 0) {
      isUploading?.close()
      isUploading = null
      emit('uploaded')
    }
  })
  return taskPromise
}

const defaultOptions: RawEditorOptions = {
  promotion: false,
  branding: false,
  menubar: 'file edit view insert format tools table help',
  autosave_ask_before_unload: true,
  autosave_interval: '30s',
  autosave_prefix: 'tinymce-autosave-{path}{query}-{id}-',
  autosave_restore_when_empty: false,
  autosave_retention: '2m',
  image_advtab: true,
  importcss_append: true,
  height: props.height || 600,
  width: props.width || 800,
  image_caption: true,
  quickbars_selection_toolbar: 'bold italic | quicklink h2 h3 blockquote quickimage quicktable',
  noneditable_class: 'mceNonEditable',
  toolbar_mode: 'sliding',
  contextmenu: 'link image table',
  skin: useDarkMode ? 'oxide-dark' : 'oxide',
  content_css: useDarkMode ? 'dark' : 'default',
  images_upload_handler: props.useOss ? handleUploadImage : undefined,
  automatic_uploads: props.useOss,
  license_key: 'gpl',
  init_instance_callback(editor) {
    editorInstance.value = editor
  }
}

const init = computed<RawEditorOptions>(() => ({
  ...defaultOptions,
  ...props.initOptions
}))

const content = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

defineExpose({
  editor: editorInstance
})
</script>

<template>
  <editor
    ref="tinymceRef"
    v-model="content"
    :disabled="disabled"
    :init="init"
    :plugins="plugins"
    :toolbar="toolbar"
    tinymce-script-src="/tinymce/js/tinymce/tinymce.min.js"
    v-bind="$attrs"
  />
</template>

<style scoped></style>
