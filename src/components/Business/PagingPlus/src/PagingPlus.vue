<script setup lang="ts">
defineOptions({
  name: 'PagingPlus'
})
interface Props {
  pager: Partial<BasicPage>
  pageSizes?: number[]
  config?: string[]
}
const emits = defineEmits(['update'])
const props = withDefaults(defineProps<Props>(), {
  pager: () => ({
    current: 1,
    size: 20,
    total: 0
  }),
  pageSizes: () => [20, 40, 100, 200, 300, 400, 500],
  config: () => ['current', 'size', 'total']
})

const pagerConfig = computed(() => {
  const [currentPage, pageSize, total] = props.config
  return {
    currentPage,
    pageSize,
    total
  }
})
const sizeChange = (val: number) => {
  const config = unref(pagerConfig)
  emits(
    'update',
    Object.assign({}, props.pager, {
      [config.pageSize]: val,
      [config.currentPage]: 1
    })
  )
}
const currentChange = (val: number) => {
  const config = unref(pagerConfig)
  emits('update', Object.assign({}, props.pager, { [config.currentPage]: val }))
}
</script>
<template>
  <div class="flex justify-end mt-2 mr-4">
    <ElPagination
      :page-sizes="pageSizes"
      @update:current-page="currentChange"
      @update:page-size="sizeChange"
      :current-page="pager[pagerConfig.currentPage]"
      :page-size="pager[pagerConfig.pageSize]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pager[pagerConfig.total]"
    />
  </div>
</template>
