export namespace AllUsersAPI {
  export interface Data {
    /**
     * 子类
     */
    child?: Data[] | null
    /**
     * 用户Id
     */
    id?: number | null
    /**
     * 用户名称 只有在最后一级才是用户名称 其他的都是组织架构名称
     */
    name?: null | string
  }
  export type Response = ResponseData<Data[]>
}

export namespace MaterialCategoryAPI {
  export interface Data {
    childList: Data[]
    parentKey: number
    selectorKey: number
    selectorValue: string
  }
  export type Response = ResponseData<Data[]>
}
export namespace ProductCategoryDropAPI {
  export interface Data {
    childList?: Data[]
    selectorEnValue?: string
    selectorKey?: number
  }
  export type Response = ResponseData<Data[]>
}
