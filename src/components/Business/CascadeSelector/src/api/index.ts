import request from '@/config/fetch'
import { AllUsersAPI, MaterialCategoryAPI, ProductCategoryDropAPI } from './types'

/**
 * 所有用户信息
 */
export const allUsers = (): Promise<AllUsersAPI.Response> => {
  return request.get({ url: '/pdm-base/base/allUsers' })
}

/**
 * 材料分类
 */
export const materialCategory = (): Promise<MaterialCategoryAPI.Response> => {
  return request.get({ url: '/pdm-base/material/category/tree' })
}

/**
 * 产品分类树型下拉框
 */
export const productCategoryDrop = (): Promise<ProductCategoryDropAPI.Response> => {
  return request.get({ url: '/pdm-base/product/category/drop' })
}
