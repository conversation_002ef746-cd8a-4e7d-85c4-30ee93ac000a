export type PropsObjectType = Record<string, any>
import { CascaderValue } from 'element-plus'

export interface IConfiguration {
  label: string
  value: string
  children?: string
  childrenConfiguration?: IConfiguration
}
export interface IApi {
  api: Function | Promise<any> | any
  configuration: IConfiguration
  loop?: boolean
}
export type CascadeSelector = Record<string, IApi>
export interface Props {
  // 是否开启缓存
  cache?: boolean
  // 双向绑定的值
  modelValue: CascaderValue | undefined
  // 需要调用的接口
  apiKey?: string
  // 多选是否折叠
  collapseTags?: boolean
  // 是否显示清空按钮
  clearable?: boolean
  // 提示 默认请选择
  placeholder?: string
  // 无需确认是否传入参数即可调用 （ganged 为 true 时 没有参数也直接调用）
  notRequiredParams?: boolean
  // 是否禁用
  disabled?: boolean
  // 输入框中是否显示选中值的完整路径
  showAllLevels?: boolean
  // 用于分隔选项的字符
  separator?: string
  // 搜索关键词输入的去抖延迟，毫秒
  debounce?: number
  // 自定义浮层类名
  popperClass?: string
  // 无匹配选项时的内容
  empty?: string
  // 级联需要忽略的参数
  gangedIgnore?: string[]
  // 是否需要级联
  ganged?: boolean
  // 参数
  params?: PropsObjectType
  // 搜索
  filterable?: boolean
  // 忽略的选项
  ignoreList?: unknown[]
  needList?: unknown[]
  props?: PropsObjectType
}
