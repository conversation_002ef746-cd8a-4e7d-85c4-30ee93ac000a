<script lang="ts" setup>
import { isEqual, isObject, omit } from 'lodash-es'
import type { IApi, IConfiguration, Props, PropsObjectType } from './types'
import type { CascaderNode } from 'element-plus'
import apiMaps from './apiMaps'
import { cascadeStore } from './store'

defineOptions({
  name: 'CascadeSelector'
})
const emits = defineEmits(['update:modelValue', 'change', 'responseData'])
const attrs = useAttrs()

const defaultProps = {
  // 缓存
  cache: false,
  // 	次级菜单的展开方式 click / hover
  expandTrigger: 'click',
  // 是否多选
  multiple: false,
  // 是否严格的遵守父子节点不互相关联
  checkStrictly: false,
  // 在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值
  emitPath: true,
  value: 'value',
  label: 'label',
  children: 'children',
  // 指定选项的禁用为选项对象的某个属性值
  disabled: 'disabled'
}
const props = withDefaults(defineProps<Props>(), {
  cache: true,
  modelValue: () => [],
  clearable: true,
  placeholder: '',
  showAllLevels: true,
  filterable: true
})
const options = ref<PropsObjectType[] & { loading?: boolean }>([])

const getBindValue = computed(() => {
  const delArr = ['showAllLevels', 'filterable', 'class', 'modelValue']
  return omit({ ...attrs, ...props }, delArr)
})

const mergeProps = computed<PropsObjectType>(() => {
  return Object.assign(defaultProps, props.props)
})

const isCommitParams = computed<boolean>(() => {
  const params = props.params as PropsObjectType
  if (!isObject(params)) return false
  return Object.keys(params).every((key) => {
    // 是否是级联需要忽略的参数
    const isGangedIgnore = props.gangedIgnore?.includes(key)
    //  是否存在
    const isExist = Array.isArray(params[key]) ? params[key].length !== 0 : !!params[key]
    return isGangedIgnore || isExist
  })
})

watch(
  () => props.params,
  (val, lodVal) => {
    // newValue === oldValue
    if (isEqual(val, lodVal)) return
    if (props.ganged) {
      // 传入了参数（除去忽略）或 无需传入参数
      if (isCommitParams.value || props.notRequiredParams) {
        getOptions()
      } else {
        options.value = []
      }
    }
  },
  { deep: true }
)
const formation = (
  data: Record<string, string | number>[],
  configuration: IConfiguration,
  loop = false
) => {
  const options: PropsObjectType[] = []
  data.forEach((item) => {
    const current: PropsObjectType = { ...item }
    if (loop) {
      if (current[configuration.children!]) {
        current.children = formation(current[configuration.children!], configuration, loop)
      }
    } else {
      if (configuration.childrenConfiguration && current[configuration.children!]) {
        current.children = formation(
          current[configuration.children!],
          configuration.childrenConfiguration
        )
      } else {
        delete current[configuration.children!]
      }
    }

    current.value = current[configuration.value]
    current.label = current[configuration.label]
    options.push(current)
  })
  return options
}

const isCache = computed(() => {
  return props.cache && props.apiKey
})

const getOptions = async () => {
  if (props.apiKey && !apiMaps[props.apiKey]) {
    console.error(`apiKey ${props.apiKey} is not exist`)
  }
  const {
    api,
    configuration,
    loop = false
  }: Partial<IApi> = props.apiKey ? apiMaps[props.apiKey] : {}
  if (isCache.value) {
    const cacheOptions = cascadeStore.value[props.apiKey!]
    watch(
      () => cascadeStore.value[props.apiKey!],
      (val) => {
        options.value = val
        emits('responseData', options.value)
      }
    )
    if (cacheOptions?.loading) {
      return
    }
  }
  if (isCache.value) {
    options.value.loading = true
    cascadeStore.value[props.apiKey!] = options.value
  }
  const { datas = [] } = await api(props.params)
  let responseData = datas
  if (props.ignoreList?.length) {
    responseData = responseData?.filter(
      (item: IConfiguration) => !props.ignoreList?.includes(item[configuration!.value])
    )
  }
  if (props.needList?.length) {
    responseData = responseData?.filter((item: IConfiguration) =>
      props.needList?.includes(item[configuration!.value])
    )
  }
  options.value = formation(responseData, configuration!, loop)
  options.value.loading = false
  if (isCache.value) cascadeStore.value[props.apiKey!] = options.value
  emits('responseData', options.value)
}
const filterMethod = (node: CascaderNode, keyword: string) =>
  node.text.toLocaleUpperCase().includes(keyword.toLocaleUpperCase())
onMounted(() => {
  if ((!isCommitParams.value && !props.ganged) || props.notRequiredParams) {
    getOptions()
  }
})

const placeholderText = computed(() => {
  return props.placeholder || '请选择'
})
</script>

<template>
  <ElCascader
    ref="cascader"
    :filter-method="filterMethod"
    :filterable="filterable"
    :modelValue="modelValue"
    :options="options"
    :placeholder="placeholderText"
    :props="mergeProps"
    :show-all-levels="showAllLevels"
    class="cascader"
    popper-class="cascader-popper"
    v-bind="getBindValue"
    @change="emits('change', $event)"
    @update:model-value="emits('update:modelValue', $event)"
  />
</template>

<style lang="less">
.cascader-popper {
  .el-cascader-node__postfix {
    position: absolute;
  }
}
</style>
