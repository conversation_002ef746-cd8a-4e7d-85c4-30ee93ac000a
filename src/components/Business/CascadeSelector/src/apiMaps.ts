import type { CascadeSelector } from './types'
import { allUsers, materialCategory, productCategoryDrop } from './api'

const apiMaps: CascadeSelector = {
  allUsers: {
    api: allUsers,
    configuration: {
      children: 'child',
      label: 'name',
      value: 'id'
    },
    loop: true
  },
  materialCategory: {
    api: materialCategory,
    configuration: {
      children: 'childList',
      label: 'selectorValue',
      value: 'selectorKey'
    },
    loop: true
  },
  productCategoryDrop: {
    api: productCategoryDrop,
    configuration: {
      children: 'childList',
      label: 'selectorEnValue',
      value: 'selectorKey'
    },
    loop: true
  }
}
export default apiMaps
