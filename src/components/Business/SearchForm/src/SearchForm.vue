<script lang="tsx">
import { propTypes } from '@/utils/propTypes'
import { resetObject } from '@/utils'
import type { FormInstance } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { Icon } from '@/components/Icon'
import type { VNode } from 'vue'
import { watchDebounced } from '@vueuse/core'

export default defineComponent({
  name: 'SearchForm',
  props: {
    modelValue: propTypes.object.def({}),
    rules: propTypes.object.def({}),
    labelWidth: propTypes.string.def('7em'),
    loading: propTypes.bool.def(false),
    expand: propTypes.bool.def(true),
    export: propTypes.bool.def(false),
    handleExport: propTypes.func.def(() => {}),
    exportLoading: propTypes.bool.def(false),
    searchAfterChange: propTypes.bool.def(true)
  },
  emits: ['search', 'reset', 'update:modelValue', 'update'],
  setup(props, { emit, slots }) {
    const { t } = useI18n()
    const formRef = ref<FormInstance>()
    const visible = ref(false)
    const defaultFormData = cloneDeep(props.modelValue)

    onMounted(() => {
      watchDebounced(
        () => [props.searchAfterChange, props.modelValue],
        async ([flag]) => {
          if (!flag) {
            return
          }
          await validateAndEmit()
        },
        { deep: true, debounce: 400 }
      )
    })

    const validateAndEmit = async () => {
      if (!formRef.value) {
        console.error('formRef is undefined')
        return
      }
      const valid = await formRef.value.validate()
      if (valid) {
        emit('search')
        emit('update')
      }
    }

    const resetForm = () => {
      if (formRef.value) {
        formRef.value.resetFields()
        emit('update:modelValue', Object.assign(resetObject(props.modelValue), defaultFormData))
        emit('reset')
        emit('update')
      }
    }

    const toggleVisible = () => {
      visible.value = !visible.value
    }

    const renderSlotContent = computed(() => {
      const EXPAND_SPAN = 48
      const DEFAULT_SPAN = 12
      const slotContent = { default: [] as VNode[], expand: [] as VNode[], totalSpan: 0 }
      if (slots.default) {
        slots.default().forEach((cur) => {
          const { span = DEFAULT_SPAN, offset = 0, push = 0, pull = 0 } = cur.props || {}
          slotContent.totalSpan += span
          const col = (
            <ElCol span={span} offset={offset} push={push} pull={pull}>
              {cur}
            </ElCol>
          )
          slotContent[slotContent.totalSpan <= EXPAND_SPAN ? 'default' : 'expand'].push(col)
        })
      }
      return slotContent
    })

    return () => (
      <ElForm
        ref={formRef}
        model={props.modelValue}
        rules={props.rules}
        label-width={props.labelWidth}
        v-on:submit={(e: Event) => {
          e.preventDefault()
        }}
      >
        <div class="search-form-content">
          <ElRow gutter={20}>
            <ElCol span={16}>
              <ElRow gutter={20}>{renderSlotContent.value?.default}</ElRow>
              <ElCollapse-transition>
                <ElRow v-show={visible.value} gutter={20}>
                  {renderSlotContent.value?.expand}
                </ElRow>
              </ElCollapse-transition>
            </ElCol>
            <ElCol span={8}>
              <ElFormItem label-width="0" class="search-form-button">
                {renderSlotContent.value?.expand.length ? (
                  <ElButton onClick={toggleVisible} text class="w-16">
                    {t(visible.value ? 'common.shrink' : 'common.expand')}
                    <Icon
                      icon="ant-design:down-outlined"
                      class={`transform transition duration-400 ${
                        visible.value ? 'rotate-90' : ''
                      }`}
                    />
                  </ElButton>
                ) : null}
                <ElButton
                  type="primary"
                  onClick={validateAndEmit}
                  loading={props.loading}
                  native-type="submit"
                  class="w-16"
                >
                  <Icon icon="ep:search" class="mr-1" v-show={!props.loading} />
                  {t('common.query')}
                </ElButton>
                <ElButton
                  onClick={resetForm}
                  loading={props.loading}
                  native-type="reset"
                  class="w-16"
                >
                  <Icon icon="ep:refresh-right" class="mr-1" v-show={!props.loading} />
                  {t('common.reset')}
                </ElButton>
                {props.export ? (
                  <ElButton
                    type="primary"
                    onClick={props.handleExport}
                    loading={props.exportLoading}
                    class="w-16"
                  >
                    <Icon v-show={!props.exportLoading} icon="ep:upload-filled" class="mr-1" />
                    {t('common.export')}
                  </ElButton>
                ) : null}
              </ElFormItem>
            </ElCol>
          </ElRow>
        </div>
      </ElForm>
    )
  }
})
</script>

<style lang="less" scoped>
.search-form-button {
  > :deep(.el-form-item__content) {
    flex-wrap: nowrap;
  }
}
</style>
