import apiMaps from '../apiMapList'

export type ValueType = 'default' | 'string' | 'number'
export type OptionsType = Record<string, string | number>
export type ResponseOptionsDataType = string[] | Record<string, string | number | null | boolean>[]
export interface Props {
  /**
   * 双向绑定的值
   */
  modelValue: any
  /**
   *选中的 Item
   */
  itemValue?: OptionsType | unknown
  bindItem?: boolean
  /**
   * 是否为虚拟滚动 使用时 value 初始值 为 null 或 ref()
   * https://github.com/element-plus/element-plus/issues/9810#issuecomment-1250208868
   */
  virtualized?: boolean
  radio?: boolean
  radioButton?: boolean
  checkbox?: boolean
  checkboxButton?: boolean
  /**
   * 传入的options 有则使用
   */
  selectOptions?: OptionsType[]
  /**
   * 需要调用的接口
   */
  apiKey: keyof typeof apiMaps.value
  /**
   * {value: 选项的值, label: 选项的标签，若不设置则默认与 value 相同, key: 唯一值}
   */
  configuration?: OptionsType
  /**
   * 调用接口所需参数
   */
  params?: unknown
  /**
   * 是否显示清空按钮
   */
  clearable?: boolean
  /**
   * 是否需要级联
   */
  ganged?: boolean
  /**
   * 级联需要忽略的参数
   */
  gangedIgnore?: string[]
  /**
   * 级联前置条件不足时显示的消息
   */
  information?: string
  /**
   * 是否多选
   */
  multiple?: boolean
  /**
   * 多选是否折叠
   */
  collapseTags?: boolean
  /**
   * 无需确认是否传入参数即可调用 （ganged 为 true 时 没有参数也直接调用）
   */
  notRequiredParams?: boolean
  /**
   * 忽略的选项
   */
  ignoreList?: string[]
  /**
   * 只展示的选项
   */
  pickList?: string[]
  /**
   * 数据处理
   */
  dataMethod?: Function
  /**
   * 提示 默认请选择
   */
  placeholder?: string
  /**
   * 联动是是否清空数据
   */
  cascadeClear?: boolean
  /**
   * value 类型
   */
  valueType?: ValueType
  /**
   * 是否缓存，默认开启
   */
  cache?: boolean
  /**
   * 父元素是否是el-scroll
   */
  parentScroll?: boolean
}

export interface SelectReactive {
  /**
   * 下拉框的数据
   */
  options: OptionsType[] & { loading?: boolean }
  /**
   * config
   */
  selectConfiguration: Configuration
  loading: boolean
}
/**
 * 下拉选项的配置属性
 */
export interface Configuration {
  key: string | number
  label: string
  value: string | number
  disabled?: string
}

export interface ApiMapListItem<
  T = (
    params?: unknown,
    data?: unknown
  ) => Promise<ResponseData<ResponseOptionsDataType | unknown[]>>
> {
  api: T
  configuration?: Configuration
  oneDimension?: boolean
}
