<script setup lang="ts">
// 如何导入定义配置文件的接口
// https://github.com/vuejs/core/issues/4294

import type { OptionsType, Props, SelectReactive } from './types'
import apiMapList from './apiMapList'
import {
  oneDimensionCreateOption,
  processPickOmitOption,
  valueTypeChange,
  GetBindValue,
  getBindValueDelList
} from './helper'
import { cloneDeep, isArray, isEqual, isObject } from 'lodash-es'
import { isString } from 'xe-utils'
defineOptions({
  name: 'Selection'
})

const attrs = useAttrs()

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  selectOptions: () => [],
  clearable: true,
  cascadeClear: true,
  valueType: 'default'
})

const showInformation = ref<boolean>(false)

const dataRef = reactive<SelectReactive>({
  options: [],
  selectConfiguration: {
    key: 'selectorKey',
    value: 'selectorValue',
    label: 'selectorKey'
  },
  loading: false
})

const getBindValue = computed<GetBindValue<typeof attrs>>(() => {
  const delArr: string[] = getBindValueDelList
  const obj = { ...attrs, ...props }
  for (const key in obj) {
    if (delArr.indexOf(key) !== -1) {
      delete obj[key]
    }
  }
  return obj
})

const selectComponent = computed(() => {
  // 根据条件返回不同的组件
  if (props.virtualized) return 'ElSelectV2'
  if (props.radio) return 'ElRadioGroup'
  if (props.checkbox) return 'ElCheckboxGroup'
  return 'ElSelect'
})
/**
 * 获取当前 Configuration
 */
const optionConfiguration = computed<OptionsType>(() =>
  isObject(props.configuration) ? props.configuration : dataRef.selectConfiguration
)

/**
 * 是否显示联动提示
 */
const isPromptVisible = computed(() => {
  const { ganged, information } = props
  const { options } = dataRef
  return ganged && options.length === 0 && information && !unref(hasParameters)
})

/**
 * 是否传入了参数
 */
const hasParameters = computed(() => {
  const { params } = props

  if (!isObject(params)) return false

  return Object.keys(params).every((key) => {
    // 是否是级联需要忽略的参数
    const isIgnoredCascadeParameter = props.gangedIgnore?.includes(key)
    //  是否存在
    const isExist = Array.isArray(params[key]) ? params[key].length !== 0 : !!params[key]
    return isIgnoredCascadeParameter || isExist
  })
})

const virtualizedOptions = computed(() => {
  if (!props.virtualized || dataRef.options.length === 0) return []
  return dataRef.options.map((item: Recordable) => ({
    value: item[unref(optionConfiguration)?.value],
    label: item[unref(optionConfiguration)?.label]
  }))
})

const placeholderText = computed(() => {
  return unref(showInformation) ? props.information : props.placeholder || '请选择'
})

watch(
  () => props.params,
  (val, lodVal) => {
    // newValue === oldValue
    if (isEqual(val, lodVal)) return
    if (props.ganged) {
      // 传入了参数（除去忽略）或 无需传入参数
      if (unref(hasParameters) || props.notRequiredParams) {
        queryOptions()
      } else {
        dataRef.options = []
      }
      if (props.cascadeClear) emits('update:modelValue', isArray(props.modelValue) ? [] : '')
      if (unref(isPromptVisible)) showInformation.value = false
    }
  },
  { deep: true }
)

const visibleChangeEvent = (visible: boolean) => {
  emits('visibleChange', visible)
  if (visible) {
    if (isPromptVisible.value) {
      showInformation.value = true
    }
  } else {
    showInformation.value = false
  }
}

const change = (val: unknown) => {
  emits('change', val)
}

const processOptions = (options: typeof dataRef.options) => {
  return props.dataMethod ? props.dataMethod(options) : options
}

const queryOptions = async () => {
  const { api, configuration, oneDimension = false } = apiMapList[props.apiKey]

  dataRef.selectConfiguration = configuration ?? dataRef.selectConfiguration

  if (props.selectOptions.length || !api) {
    dataRef.options = processOptions(props.selectOptions)
    return
  }

  try {
    dataRef.loading = true

    const { data } = await api(props.params)
    let selectOptions = cloneDeep(data)
    // 处理一维数组
    if (oneDimension && isString(selectOptions.at(0))) {
      selectOptions = oneDimensionCreateOption(
        selectOptions as string[],
        dataRef.selectConfiguration
      )
    }
    // 处理需要忽略的选项，或者需要展示的选项
    if (props.ignoreList?.length || props.pickList?.length) {
      selectOptions = processPickOmitOption(
        selectOptions as OptionsType[],
        dataRef.selectConfiguration,
        props.ignoreList,
        props.pickList
      )
    }
    // 修改类型
    if (props.valueType !== 'default') {
      selectOptions = valueTypeChange(
        props.valueType,
        selectOptions as OptionsType[],
        dataRef.selectConfiguration
      )
    }

    dataRef.options = processOptions(selectOptions as OptionsType[])

    emits('responseData', data)
  } finally {
    dataRef.loading = false
  }
}
const updateModelValue = (val: unknown) => {
  emits('update:modelValue', val)

  // 当 bindItem 属性为真时，处理 itemValue 的更新
  if (props.bindItem) {
    if (!props.multiple) {
      // 单选模式
      const selectedItem = dataRef.options.find(
        (item: Recordable) => item[unref(optionConfiguration).value] === val
      )
      emits('update:itemValue', selectedItem as OptionsType)
    } else {
      // 多选模式
      const selectedItems = dataRef.options.filter((item: Recordable) =>
        (val as string[]).includes(item[unref(optionConfiguration).value])
      )
      emits('update:itemValue', selectedItems as OptionsType[])
    }
  }
}

const virtualizedClear = () => {
  emits('update:modelValue', props.multiple ? [] : null)
}

watch(
  () => props.selectOptions,
  (list) => {
    if (isArray(list) && list.length > 0) {
      const options = Array.isArray(props.selectOptions) ? props.selectOptions : []
      dataRef.options = processOptions(options)
    }
  }
)
watch(
  () => [props.apiKey, props.pickList],
  (val) => {
    if (val) {
      queryOptions()
      if (props.cascadeClear) emits('update:modelValue', isArray(props.modelValue) ? [] : '')
    }
  }
)
onMounted(() => {
  if (hasParameters.value || props.notRequiredParams || !props.ganged) {
    queryOptions()
  }
})

const emits = defineEmits([
  'update:modelValue',
  'responseData',
  'visibleChange',
  'change',
  'update:itemValue'
])
</script>

<template>
  <component
    :is="selectComponent"
    :options="selectComponent === 'ElSelectV2' && virtualizedOptions"
    :placeholder="placeholderText"
    :loading="dataRef.loading"
    v-bind="getBindValue"
    @update:model-value="updateModelValue"
    @clear="virtualizedClear"
    @visible-change="visibleChangeEvent"
    @change="change"
  >
    <!-- 根据选定的组件渲染相应的选项或内容 -->
    <template v-if="selectComponent === 'ElSelect'">
      <!-- 为 ElSelect 组件渲染选项 -->
      <ElOption
        v-for="item in dataRef.options"
        :key="item[optionConfiguration.key]"
        :label="item[optionConfiguration.label] || item[optionConfiguration.value]"
        :value="item[optionConfiguration.value]"
      />
    </template>

    <template v-else-if="selectComponent === 'ElRadioGroup'">
      <!-- 为 ElRadioGroup 组件渲染选项 -->
      <ElRadio
        v-for="item in dataRef.options"
        :key="item[optionConfiguration.key]"
        :label="item[optionConfiguration.value]"
      >
        {{ item[optionConfiguration.label] || item[optionConfiguration.value] }}
      </ElRadio>
    </template>

    <template v-else-if="selectComponent === 'ElCheckboxGroup'">
      <!-- 为 ElCheckboxGroup 组件渲染选项 -->
      <template v-if="props.checkboxButton">
        <ElCheckboxButton
          v-for="item in dataRef.options"
          :key="item[optionConfiguration.key]"
          :label="item[optionConfiguration.value]"
        >
          {{ item[optionConfiguration.label] || item[optionConfiguration.value] }}
        </ElCheckboxButton>
      </template>
      <template v-else>
        <ElCheckbox
          v-for="item in dataRef.options"
          :key="item[optionConfiguration.key]"
          :label="item[optionConfiguration.value]"
        >
          {{ item[optionConfiguration.label] || item[optionConfiguration.value] }}
        </ElCheckbox>
      </template>
    </template>
  </component>
</template>

<style lang="less" scoped>
.warning-select :deep(.el-input.is-focus .el-input__wrapper) {
  background-color: #fab6b6;
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;

  ::placeholder {
    color: #fff;
  }
}
</style>
