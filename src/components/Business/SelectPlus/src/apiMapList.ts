import type { ApiMapListItem, Configuration } from './types/index'
import * as selectModule from './api'
import { DictKey } from '@/hooks/autoImport/useDictAll'

type SelectModule = keyof typeof selectModule
export type SelectAPI = SelectModule | DictKey

type ApiMapList = {
  [key in SelectAPI]: ApiMapListItem
}

const configurations: Partial<Record<SelectAPI, Configuration>> = {
  sizeList: {
    key: 'id',
    label: 'name',
    value: 'id',
    disabled: 'disabled'
  },
  colorDrop: {
    key: 'id',
    label: 'englishName',
    value: 'id'
  },
  lastDrop: {
    key: 'code',
    label: 'code',
    value: 'id'
  },
  packageCode: {
    key: 'id',
    label: 'simplifyWord',
    value: 'id'
  },
  getEffectProductIdList: {
    key: 'id',
    label: 'value',
    value: 'id'
  },
  getRiskCountryList: {
    key: 'siteName',
    label: 'siteName',
    value: 'siteName'
  },
  getMouldNumberList: {
    key: 'selectorValue',
    label: 'selectorKey',
    value: 'selectorValue',
    disabled: 'disabled'
  },
  getProductNumberWithStatusList: {
    key: 'selectorValue',
    label: 'selectorKey',
    value: 'selectorValue',
    disabled: 'disabled'
  },
  getSupplierList: {
    key: 'vendorCode',
    label: 'vendorName',
    value: 'vendorId',
    disabled: 'disabled'
  },
  getSampleSupplierList: {
    key: 'vendorCode',
    label: 'vendorName',
    value: 'vendorId',
    disabled: 'disabled'
  }
}

/**
 * 表示选择模块 API 到其对应配置的映射。
 * @property {ApiMapListItem} api - 选择模块的 API。
 * @property {Configuration | undefined} configuration - 选择模块的配置（如果有）。
 */
const selectModuleApiMapList = Object.keys(selectModule).reduce((acc, key) => {
  acc[key] = {
    api: selectModule[key],
    configuration: Object.keys(configurations).indexOf(key) === -1 ? undefined : configurations[key]
  }
  return acc
}, {} as { [key in SelectModule]: ApiMapListItem })

/**
 * API映射列表
 */
const apiMaps = computed<ApiMapList>(() => ({
  ...selectModuleApiMapList,
  ...dictSelectOptions.value,
  getProductNumberList: {
    api: selectModule.getProductNumberList,
    oneDimension: true
  },
  getMouldNumberList: {
    api: selectModule.getMouldNumberList,
    oneDimension: true,
    configuration: {
      key: 'selectorValue',
      label: 'selectorKey',
      value: 'selectorValue',
      disabled: 'disabled'
    }
  },
  getStyleWmsList: {
    api: selectModule.getStyleWmsList,
    oneDimension: true
  },
  getProofList: {
    api: selectModule.getProofCodeList,
    oneDimension: true
  },
  getBanPlatformList: {
    api: selectModule.getBanPlatformList,
    oneDimension: true
  },
  getStyleNumberList: {
    api: selectModule.getStyleNumberList,
    oneDimension: true
  },
  getEnumeratedClassificationList: {
    api: selectModule.getEnumeratedClassificationList
  },
  getCategoryListApi: {
    api: selectModule.getCategoryData
  }
}))
export default apiMaps
