import type { Configuration, ValueType, OptionsType, Props } from './types/index'
import { toNumber, toString } from 'lodash-es'

/**
 * 将选项数据的值类型转换为指定的值类型。
 * @param valueType 值类型，可以是 'number' 或 'string'。
 * @param data 选项数据数组。
 * @param selectConfiguration 选择配置对象。
 * @returns 转换后的选项数据数组。
 */
export const valueTypeChange = (
  valueType: ValueType,
  data: OptionsType[],
  selectConfiguration: Configuration
) => {
  return data.map((item: Recordable) => ({
    ...item,
    value:
      valueType === 'number'
        ? toNumber(item[selectConfiguration.value])
        : toString(item[selectConfiguration.value])
  }))
}

/**
 * 根据一维数据数组创建选择组件的选项数组。
 * 每个选项由一个包含键值对的记录表示。
 *
 * @param data - 数据数组。
 * @param selectConfiguration - 选择组件的配置对象。
 * @returns 选择组件的选项数组。
 */
export const oneDimensionCreateOption = (
  data: string[],
  selectConfiguration: Configuration
): Record<string, string>[] => {
  return data?.map((item) => ({
    [selectConfiguration.key]: item,
    [selectConfiguration.value]: item,
    [selectConfiguration.label]: item
  }))
}

/**
 * 用于处理选项数据，根据配置进行过滤。
 *
 * @param data 选项数据数组
 * @param selectConfiguration 选择配置对象
 * @param ignoreList 忽略列表，默认为空数组
 * @param pickList 选择列表，默认为空数组
 * @returns 过滤后的选项数据数组
 */
export const processPickOmitOption = (
  data: OptionsType[],
  selectConfiguration: Configuration,
  ignoreList: string[] = [],
  pickList: string[] = []
) => {
  const ignoreSet = new Set(ignoreList)
  const pickSet = new Set(pickList)

  return data.filter((item) => {
    const value = item[selectConfiguration.value]
    return !(ignoreSet.has(toString(value)) || (pickSet.size && !pickSet.has(toString(value))))
  })
}

export type GetBindValue<T> = Omit<T & Props, 'modelValue'>

export const getBindValueDelList = ['modelValue']
