import { SizeValueAPI } from '@/views/basic-library-manage/size-library/api/sizeInfo'

/**
 * @description 品牌列表
 */
export namespace BaseBrandAPI {
  export interface Data {
    selectorKey: string
    selectorValue: number
  }
  export type Response = ResponseData<Data[]>
}

/**
 * @description 尺码值列表
 */
export namespace SizeListAPI {
  export interface Data {
    id?: number
    /**
     * 尺码类型
     */
    sizeType?: string
    /**
     * 名称
     */
    name?: string
    /**
     * 楦型标准
     */
    lastsStandard?: string
    /**
     * 楦头类别
     */
    toeStandard?: string
    /**
     * 尺码值
     */
    sizeValueList?: SizeValueAPI.List
    /**
     * 标准码
     */
    standardValue?: string
    /**
     * 状态
     */
    status?: string
  }
  export type Response = ResponseData<Data[]>
}

/**
 * @description FORMATE尺码值列表
 */
export namespace FormatSizeListAPI {
  export interface Data {
    label?: string
    sizeId?: number
    sizeType?: string
    sizeValueList?: SizeAndStandardValueResp[]
    standardList?: SizeAndStandardValueResp[]
    status?: string
    value?: number
    [property: string]: any
  }
  export type Response = ResponseData<Data[]>
}

/**
 * @description 颜色下拉框
 */
export namespace ColorDropAPI {
  export interface Params {
    productNumber?: string[]
  }
  export interface Data {
    /**
     * 颜色英文名称
     */
    englishName?: null | string
    /**
     * id
     */
    id?: number | null
  }
  export type Response = ResponseData<Data[]>
}

/**
 * @description 楦型下拉框
 */
export namespace LastDropAPI {
  export interface Data {
    /**
     * 楦型编码
     */
    code?: null | string
    /**
     * id
     */
    id?: number | null
  }
  export type Response = ResponseData<Data[]>
}

/**
 * @description 包装代码
 */
export namespace PackageCodeAPI {
  export interface Data {
    brandId?: number | null
    brandName?: null | string
    id?: number | null
    simplifyWord?: null | string
  }
  export type Response = ResponseData<Data[]>
}

/**
 * @description 风险国家
 */
export namespace RiskCountryAPI {
  export interface Data {
    siteCode?: string
    siteFullName?: string
    siteId?: number
    siteName?: string
  }
  export type Response = ResponseData<Data[]>
}

/**
 * 带状态的编码下拉-统一
 */
export namespace CodeListAPI {
  export interface Data {
    selectorKey?: string
    selectorValue?: string
    /**
     * 状态
     */
    status?: number
    /**
     * 设计图
     */
    designUrl?: BaseFileDTO[]
    fileDTO?: BaseFileDTO
  }
  export type Response = ResponseData<Data[]>
}

/**
 * 供应商
 */
export namespace FactoryListAPI {
  export type Data = {
    /**
     * 地区名称
     */
    areaCode?: string
    /**
     * 地区名称
     */
    areaName?: string
    vendorId?: number
    /**
     * 工厂名称
     */
    vendorCode?: string
    /**
     * 工厂名称
     */
    vendorName?: string
    /**
     * 跟进技术人员id
     */
    tecUserId?: number
    /**
     * 跟进开发人员id
     */
    devUserId?: number
    /**
     * 工厂全称
     */
    vendorFullName?: string
  }
  export type Response = ResponseData<Data[]>
}
