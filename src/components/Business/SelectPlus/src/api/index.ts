import request from '@/config/fetch'
import {
  BaseBrandAPI,
  CodeListAPI,
  ColorDropAPI,
  FactoryListAPI,
  FormatSizeListAPI,
  LastDropAPI,
  PackageCodeAPI,
  RiskCountryAPI,
  SizeListAPI
} from './types'

/**
 * 品牌下拉框
 */
export const baseBrand = (): Promise<BaseBrandAPI.Response> => {
  return request.get({ url: '/pdm-base/base/brand' })
}
/**
 * 尺码值下拉框
 */
export const getSizeAndStandardValue = (): Promise<FormatSizeListAPI.Response> => {
  return request.get({ url: '/pdm-base/size/getSizeAndStandardValue' })
}
/**
 * 尺码值下拉框
 */
export const sizeList = (): Promise<SizeListAPI.Response> => {
  return request.get({ url: '/pdm-base/size/sizeAndStandardValue' })
}

/**
 * 颜色下拉框
 */
export const colorDrop = (params: ColorDropAPI.Params): Promise<ColorDropAPI.Response> => {
  return request.get({ url: '/pdm-base/color/drop', params })
}

/**
 * 产品编号
 */
export const getProductNumberList = (): Promise<ResponseData<string[]>> => {
  return request.get({ url: '/pdm-base/pdmProductInfo/numberList' })
}

/**
 * Style Number 查询
 */
export const getStyleNumberList = (): Promise<ResponseData<string[]>> => {
  return request.get({ url: '/pdm-base/pdmProductInfo/styleNumberList' })
}

/**
 * Style WMS
 */
export const getStyleWmsList = (): Promise<ResponseData<string[]>> => {
  return request.get({ url: '/pdm-base/pdmProductInfo/wmsNumberList' })
}
/**
 * 打样单编号
 */
export const getProofCodeList = () => {
  return request.get({ url: '/pdm-base/proof/proofCodeList' })
}

/**
 * 生效产品id
 */
export const getEffectProductIdList = (): Promise<
  ResponseData<Array<{ id: number; value: string }>>
> => {
  return request.get({ url: '/pdm-base/pdmProductInfo/idList' })
}

/**
 * 楦型编号
 */
export const lastDrop = (): Promise<LastDropAPI.Response> => {
  return request.get({ url: '/pdm-base/last/drop' })
}

/**
 * 包装代码
 */
export const packageCode = (): Promise<PackageCodeAPI.Response> => {
  return request.get({ url: '/pdm-base/base/availablePackingInfo' })
}

/**
 * 带状态模具编号
 */
export const getMouldNumberList = (): Promise<CodeListAPI.Response> => {
  return request.get({ url: '/pdm-base/mold/codeStatusList' })
}

/**
 * 带状态产品编号
 */
export const getProductNumberWithStatusList = (): Promise<CodeListAPI.Response> => {
  return request.get({ url: '/pdm-base/pdmProductInfo/numberStatusList' })
}

/**
 * 供应商
 */
export const getSupplierList = (): Promise<FactoryListAPI.Response> => {
  return request.get({ url: '/pdm-base/base/getFactory' })
}

/**
 * 打样供应商
 */
export const getSampleSupplierList = (id: number): Promise<FactoryListAPI.Response> => {
  return request.get({ url: `/pdm-base/proof/getFactory/${id}` })
}

/**
 * 风险国家
 */
export const getRiskCountryList = (): Promise<RiskCountryAPI.Response> => {
  return request.get({ url: '/pdm-base/infringement/riskCountryList' })
}

/**
 * 禁售平台
 */
export const getBanPlatformList = (): Promise<ResponseData<string[]>> => {
  return request.get({ url: '/pdm-base/infringement/sellPlatformList' })
}
/***
 *枚举分类
 **/
export const getEnumeratedClassificationList = (): Promise<ResponseData<string[]>> => {
  return request.get({ url: '/pdm-base/pdmEnumeratedClassification/listClassification' })
}
export const getCategoryData = (): Promise<ResponseData<string[]>> => {
  return request.post({ url: `/pdm-base/plm/property/categoryData` })
}
