<script lang="ts" setup>
// 如何导入定义配置文件的接口
// https://github.com/vuejs/core/issues/4294

import type { OptionsType, Props, SelectReactive } from './types'
import apiMapList from './apiMapList'
import {
  GetBindValue,
  getBindValueDelList,
  oneDimensionCreateOption,
  processPickOmitOption,
  valueTypeChange
} from './helper'
import { cloneDeep, isArray, isEqual, isObject } from 'lodash-es'
import { isString } from 'xe-utils'
import { ElCheckboxGroup, ElRadioGroup, ElSelect, ElSelectV2 } from 'element-plus'
import { selectStore } from './store'
import { isNumber, isUnDef } from '@/utils/is'
import { hasValue } from '@/utils'

defineOptions({
  name: 'SelectPlus'
})

const attrs = useAttrs()
const props = withDefaults(defineProps<Props>(), {
  cache: undefined,
  modelValue: '',
  selectOptions: () => [],
  clearable: true,
  cascadeClear: true,
  valueType: 'default',
  parentScroll: true
})

const showInformation = ref<boolean>(false)

const dataRef = reactive<SelectReactive>({
  options: [],
  selectConfiguration: {
    key: 'selectorKey',
    value: 'selectorValue',
    label: 'selectorKey'
  },
  loading: false
})

const getBindValue = computed<GetBindValue<typeof attrs>>(() => {
  const delArr: string[] = getBindValueDelList
  const obj = { ...attrs, ...props }
  for (const key in obj) {
    if (delArr.indexOf(key) !== -1) {
      delete obj[key]
    }
  }
  return obj
})

// 根据条件返回不同的组件
const selectComponent = computed(() => {
  if (props.virtualized) return ElSelectV2
  if (props.radio) return ElRadioGroup
  if (props.checkbox) return ElCheckboxGroup
  return ElSelect
})
/**
 * 获取当前 Configuration
 */
const optionConfiguration = computed<OptionsType>(() =>
  isObject(props.configuration) ? props.configuration : dataRef.selectConfiguration
)

/**
 * 是否显示联动提示
 */
const isPromptVisible = computed(() => {
  const { ganged, information } = props
  const { options } = dataRef
  return ganged && options.length === 0 && information && !unref(hasParameters)
})

/**
 * 是否传入了参数
 */
const hasParameters = computed(() => {
  const { params } = props

  return hasValue(params)
})

const virtualizedOptions = computed(() => {
  if (!props.virtualized || dataRef.options.length === 0) return []
  return dataRef.options.map((item: Recordable) => ({
    value: item[unref(optionConfiguration)?.value],
    label: item[unref(optionConfiguration)?.label],
    disabled: item[unref(optionConfiguration)?.disabled]
  }))
})

const placeholderText = computed(() => {
  return unref(showInformation) ? props.information : props.placeholder || '请选择'
})
/**
 * 粘贴后选中
 * @param event
 */
const handlePaste = (event: ClipboardEvent) => {
  // 加载中
  if (dataRef.loading) {
    event.preventDefault()
    return
  }
  const clipboardData = event.clipboardData?.getData('text/plain')
  if (!clipboardData) return

  event.preventDefault() // 阻止默认粘贴行为

  // 解析粘贴的内容为数组，并过滤空值
  const pastedValues = clipboardData
    .split(/[\s,，]+/)
    .map((v) => v.trim())
    .filter(Boolean)
  if (pastedValues.length === 0) return

  // 获取当前选中的值（确保是数组）
  const currentValues = isArray(props.modelValue) ? [...props.modelValue] : []

  // 验证粘贴的值是否存在选项中
  const validValues = pastedValues.filter((value) =>
    dataRef.options.some(
      (option) => String(option[unref(optionConfiguration).value]) === String(value)
    )
  )

  // 合并并去重
  const newValues = [...new Set([...currentValues, ...validValues])]
  // 处理单选复制的问题
  if (!props.multiple && newValues.length > 0) {
    updateModelValue(newValues[0])
  } else {
    // 更新模型值
    updateModelValue(props.multiple ? newValues : '')
  }
}

watch(
  () => props.params,
  (val, lodVal) => {
    // newValue === oldValue
    if (isEqual(val, lodVal)) return
    if (props.ganged) {
      // 传入了参数（除去忽略）或 无需传入参数
      if (unref(hasParameters) || props.notRequiredParams) {
        queryOptions()
      } else {
        dataRef.options = []
      }
      if (props.cascadeClear) {
        const value = isArray(props.modelValue) ? [] : ''
        emits('update:modelValue', value)
        emits('change', value)
      }
      if (unref(isPromptVisible)) showInformation.value = false
    }
  },
  { deep: true }
)

const visibleChangeEvent = (visible: boolean) => {
  emits('visibleChange', visible)
  if (visible) {
    if (isPromptVisible.value) {
      showInformation.value = true
    }
  } else {
    showInformation.value = false
  }
}

const processOptions = (options: typeof dataRef.options) => {
  return props.dataMethod ? props.dataMethod(options) : options
}

const isDict = ref(false)

const isCache = computed(() => {
  return (props.cache || isDict.value) && props.apiKey
})

const queryOptions = async () => {
  if (!apiMapList.value[props.apiKey]) {
    console.error(`apiKey ${props.apiKey} is not exist`)
    return
  }
  const { api, configuration, oneDimension = false } = apiMapList.value[props.apiKey]
  dataRef.selectConfiguration = configuration ?? dataRef.selectConfiguration

  if (props.selectOptions.length || !api) {
    dataRef.options = processOptions(props.selectOptions)
    return
  }

  if (isUnDef(props.cache)) {
    if (props.apiKey in dictSelectOptions.value) {
      isDict.value = true
    }
  }

  if (isCache.value) {
    const cacheOptions = selectStore.value[props.apiKey]
    watch(
      () => selectStore.value[props.apiKey],
      (val) => {
        dataRef.options = val || []
        emits('responseData', dataRef.options)
      }
    )
    if (cacheOptions?.loading) {
      return
    }
  }

  try {
    dataRef.loading = true
    if (isCache.value) {
      dataRef.options.loading = true
      selectStore.value[props.apiKey] = dataRef.options
    }

    const result = await api(props.params)
    let data
    if ('datas' in result) {
      data = result.datas
    } else {
      data = result?.data
    }
    let selectOptions = cloneDeep(data)
    // 处理一维数组
    if (oneDimension && (isString(selectOptions.at(0)) || isNumber(selectOptions.at(0)))) {
      selectOptions = oneDimensionCreateOption(
        selectOptions as string[],
        dataRef.selectConfiguration
      )
    }
    // 处理需要忽略的选项，或者需要展示的选项
    if (props.ignoreList?.length || props.pickList?.length) {
      selectOptions = processPickOmitOption(
        selectOptions as OptionsType[],
        dataRef.selectConfiguration,
        props.ignoreList,
        props.pickList
      )
    }
    // 修改类型
    if (props.valueType !== 'default') {
      selectOptions = valueTypeChange(
        props.valueType,
        selectOptions as OptionsType[],
        dataRef.selectConfiguration
      )
    }

    dataRef.options = processOptions(selectOptions as OptionsType[])
    dataRef.options.loading = false
    if (isCache.value) selectStore.value[props.apiKey] = dataRef.options

    if (props.bindItem) {
      if (!props.multiple) {
        // 单选模式
        const selectedItem = dataRef.options.find(
          (item: Recordable) => item[unref(optionConfiguration).value] === props.modelValue
        )
        emits('update:itemValue', selectedItem as OptionsType)
      } else {
        // 多选模式
        const selectedItems = dataRef.options.filter((item: Recordable) =>
          (props.modelValue as string[]).includes(item[unref(optionConfiguration).value])
        )
        emits('update:itemValue', selectedItems as OptionsType[])
      }
    }

    emits('responseData', data)
  } finally {
    dataRef.loading = false
  }
}
const updateModelValue = (val: unknown) => {
  emits('update:modelValue', val)
  emits('change', val)
  // 当 bindItem 属性为真时，处理 itemValue 的更新
  if (props.bindItem) {
    if (!props.multiple) {
      // 单选模式
      const selectedItem = dataRef.options.find(
        (item: Recordable) => item[unref(optionConfiguration).value] === val
      )
      emits('update:itemValue', selectedItem as OptionsType)
    } else {
      // 多选模式
      const selectedItems = dataRef.options.filter((item: Recordable) =>
        (val as string[]).includes(item[unref(optionConfiguration).value])
      )
      emits('update:itemValue', selectedItems as OptionsType[])
    }
  }
}

const virtualizedClear = () => {
  const value = props.multiple ? [] : null
  emits('update:modelValue', value)
  emits('change', value)
}

watch(
  () => props.selectOptions,
  (list) => {
    if (isArray(list) && list.length > 0) {
      const options = Array.isArray(props.selectOptions) ? props.selectOptions : []
      dataRef.options = processOptions(options)
    }
  },
  {
    deep: true,
    immediate: true
  }
)
watch(
  () => [props.apiKey, props.pickList],
  (val) => {
    if (val) {
      queryOptions()
      if (props.cascadeClear) {
        const value = isArray(props.modelValue) ? [] : ''
        emits('update:modelValue', value)
        emits('change', value)
      }
    }
  }
)
onMounted(() => {
  if (hasParameters.value || props.notRequiredParams || !props.ganged) {
    queryOptions()
  }
})

const emits = defineEmits([
  'update:modelValue',
  'responseData',
  'visibleChange',
  'update:itemValue',
  'change'
])

type SelectInstance =
  | InstanceType<typeof ElSelect>
  | InstanceType<typeof ElSelectV2>
  | InstanceType<typeof ElCheckboxGroup>
  | InstanceType<typeof ElRadioGroup>

const selectRef = ref<SelectInstance>()

defineExpose({
  selectRef,
  queryOptions
})
</script>

<template>
  <template v-if="props.parentScroll">
    <ElScrollbar
      :class="{
        'mb-2': ['ElCheckboxGroup', 'ElRadioGroup'].includes(selectComponent.name!)
      }"
      class="select-plus !inline-flex"
    >
      <Component
        :is="selectComponent"
        ref="selectRef"
        :model-value="props.modelValue"
        :options="selectComponent.name === 'ElSelectV2' && virtualizedOptions"
        :placeholder="placeholderText"
        class="flex !flex-nowrap"
        v-bind="getBindValue"
        v-on="{
          'update:modelValue': updateModelValue,
          clear: virtualizedClear,
          'visible-change': visibleChangeEvent,
          paste: handlePaste
        }"
      >
        <!-- 根据选定的组件渲染相应的选项或内容 -->
        <template v-if="selectComponent.name === 'ElSelect'">
          <!-- 为 ElSelect 组件渲染选项 -->
          <ElOption
            v-for="item in dataRef.options"
            :key="item[optionConfiguration.key]"
            :disabled="!!item[optionConfiguration.disabled]"
            :label="item[optionConfiguration.label] || item[optionConfiguration.value]"
            :value="item[optionConfiguration.value]"
          />
        </template>

        <template v-else-if="selectComponent.name === 'ElRadioGroup'">
          <!-- 为 ElRadioGroup 组件渲染选项 -->
          <template v-if="props.radioButton">
            <ElRadioButton
              v-for="item in dataRef.options"
              :key="item[optionConfiguration.key]"
              :disabled="!!item[optionConfiguration.disabled]"
              :label="item[optionConfiguration.label] || item[optionConfiguration.value]"
              :value="item[optionConfiguration.value]"
            />
          </template>
          <template v-else>
            <ElRadio
              v-for="item in dataRef.options"
              :key="item[optionConfiguration.key]"
              :disabled="!!item[optionConfiguration.disabled]"
              :label="item[optionConfiguration.label] || item[optionConfiguration.value]"
              :value="item[optionConfiguration.value]"
            />
          </template>
        </template>

        <template v-else-if="selectComponent.name === 'ElCheckboxGroup'">
          <!-- 为 ElCheckboxGroup 组件渲染选项 -->
          <template v-if="props.checkboxButton">
            <ElCheckboxButton
              v-for="item in dataRef.options"
              :key="item[optionConfiguration.key]"
              :disabled="!!item[optionConfiguration.disabled]"
              :label="item[optionConfiguration.label] || item[optionConfiguration.value]"
              :value="item[optionConfiguration.value]"
            />
          </template>
          <template v-else>
            <ElCheckbox
              v-for="item in dataRef.options"
              :key="item[optionConfiguration.key]"
              :disabled="!!item[optionConfiguration.disabled]"
              :label="item[optionConfiguration.label] || item[optionConfiguration.value]"
              :value="item[optionConfiguration.value]"
            />
          </template>
        </template>
      </Component>
    </ElScrollbar>
  </template>
  <template v-else>
    <Component
      :is="selectComponent"
      ref="selectRef"
      :model-value="props.modelValue"
      :options="selectComponent.name === 'ElSelectV2' && virtualizedOptions"
      :placeholder="placeholderText"
      class="select-plus flex flex-nowrap"
      v-bind="getBindValue"
      v-on="{
        'update:modelValue': updateModelValue,
        clear: virtualizedClear,
        'visible-change': visibleChangeEvent,
        paste: handlePaste
      }"
    >
      <!-- 根据选定的组件渲染相应的选项或内容 -->
      <template v-if="selectComponent.name === 'ElSelect'">
        <!-- 为 ElSelect 组件渲染选项 -->
        <ElOption
          v-for="item in dataRef.options"
          :key="item[optionConfiguration.key]"
          :disabled="!!item[optionConfiguration.disabled]"
          :label="item[optionConfiguration.label] || item[optionConfiguration.value]"
          :value="item[optionConfiguration.value]"
        />
      </template>

      <template v-else-if="selectComponent.name === 'ElRadioGroup'">
        <!-- 为 ElRadioGroup 组件渲染选项 -->
        <ElRadio
          v-for="item in dataRef.options"
          :key="item[optionConfiguration.key]"
          :disabled="!!item[optionConfiguration.disabled]"
          :label="item[optionConfiguration.label] || item[optionConfiguration.value]"
          :value="item[optionConfiguration.value]"
        />
      </template>

      <template v-else-if="selectComponent.name === 'ElCheckboxGroup'">
        <!-- 为 ElCheckboxGroup 组件渲染选项 -->
        <template v-if="props.checkboxButton">
          <ElCheckboxButton
            v-for="item in dataRef.options"
            :key="item[optionConfiguration.key]"
            :disabled="!!item[optionConfiguration.disabled]"
            :label="item[optionConfiguration.label] || item[optionConfiguration.value]"
            :value="item[optionConfiguration.value]"
          />
        </template>
        <template v-else>
          <ElCheckbox
            v-for="item in dataRef.options"
            :key="item[optionConfiguration.key]"
            :disabled="!!item[optionConfiguration.disabled]"
            :label="item[optionConfiguration.label] || item[optionConfiguration.value]"
            :value="item[optionConfiguration.value]"
          />
        </template>
      </template>
    </Component>
  </template>
</template>

<style lang="less" scoped>
.warning-select :deep(.el-input.is-focus .el-input__wrapper) {
  background-color: #fab6b6;
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;

  ::placeholder {
    color: #fff;
  }
}

// 解决应为 component动态渲染,导致el-select的el-tag max-width无法正常赋值产生的样式问题
.select-plus {
  :deep(.el-select__wrapper) {
    width: 100%;
  }

  :deep(.el-select__selected-item .el-tag) {
    max-width: initial !important;
  }
}

// 在table row-cell中的select-plus不需要mb
:global(.vxe-cell--wrapper .select-plus) {
  margin-bottom: initial !important;
}
</style>
