<script lang="ts" setup>
import { Plus } from '@element-plus/icons-vue'
import { Props, UploadOmitList, OssResponse } from './types'
import type {
  UploadUserFile,
  UploadFile,
  UploadFiles,
  UploadInstance,
  UploadRawFile
} from 'element-plus'
import { ajaxUpload } from './helper'
import { ElMessage, ElMessageBox } from 'element-plus'

defineOptions({
  name: 'UploadPlus'
})

const attrs = useAttrs()

const uploadRef = ref<UploadInstance>()

const previewVisible = ref(false)
const activeIndex = ref(0)

const props = withDefaults(defineProps<Props>(), {
  modelValue: (): Props['modelValue'] => []
})

const getBindValue = computed<Omit<Props, UploadOmitList>>(() => {
  const delArr: string[] = ['class', 'modelValue', 'fileList', 'httpRequest']
  const obj = { ...attrs, ...props }
  for (const key in obj) {
    if (delArr.indexOf(key) !== -1) {
      delete obj[key]
    }
  }
  return obj
})
const handleChange = (uploadFile: UploadUserFile, uploadFiles: UploadUserFile[]) => {}
const handleExceed = (files: File[]) => {
  if (props.limit === 1 && !props.autoUpload) {
    ElMessageBox.alert(`只能上传一个文件，是否覆盖之前的文件${files.at(-1)?.name}？`, '提示', {
      type: 'warning',
      showCancelButton: true,
      draggable: true
    })
      .then(() => {
        uploadRef.value!.clearFiles()
        const file = files[0] as UploadRawFile
        uploadRef.value!.handleStart(file)
      })
      .catch(() => {})
  } else {
    ElMessage.warning(`至多选择${props.limit}个文件!`)
  }
}
const handleError = (error: Error, _uploadFile: UploadFile, _uploadFiles: UploadFiles) => {
  ElMessage.error(error.message)
}
const handleBeforeUpload: Props['beforeUpload'] = async (rawFile) => {
  if (props.sizeLimit && rawFile.size > props.sizeLimit * 1024 * 1024) {
    const msg = `文件大小不能超过 ${props.sizeLimit} M`
    ElMessage.warning(msg)
    return false
  }
  return rawFile
}
const handleSuccess: Props['onSuccess'] = async (
  res: ResponseData<OssResponse>,
  uploadFile,
  _uploadFiles
) => {
  const userRowFile = res.datas
  fileList.value = fileList.value.concat({
    name: userRowFile.originFileName!,
    url: userRowFile.downLoadUrl,
    response: userRowFile.objectName,
    status: 'success',
    uid: uploadFile.uid
  })
}

const handlePreview: Props['onPreview'] = (uploadFile) => {
  if (props.listType === 'picture-card') {
    activeIndex.value = fileList.value.findIndex((item) => item.uid === uploadFile.uid)
    previewVisible.value = true
  }
}
const fileList = computed<UploadUserFile[]>({
  get: (): UploadUserFile[] => {
    return props.modelValue.map((item) => {
      return {
        name: item.fileName!,
        url: item.signatureUrl,
        status: 'success',
        uid: item.id
      }
    })
  },
  set: (val: UploadUserFile[]) => {
    const uploadData = val.map((item) => {
      return {
        id: item.uid,
        fileName: item.name,
        fileUrl: item.response as string,
        signatureUrl: item.url
      }
    })
    emit('update:modelValue', uploadData)
  }
})

const emit = defineEmits<{
  (e: 'update:modelValue', val: BaseFileDTO[]): void
}>()
</script>
<template>
  <ElUpload
    :action="'#'"
    :http-request="ajaxUpload"
    :file-list="fileList"
    v-bind="getBindValue"
    :on-change="handleChange"
    :on-error="handleError"
    :before-upload="handleBeforeUpload"
    :on-success="handleSuccess"
    :on-exceed="handleExceed"
    :on-preview="handlePreview"
  >
    <ElImageViewer
      v-if="previewVisible"
      :url-list="fileList.map((item) => item.url!)"
      :active-index="activeIndex"
      @close="previewVisible = false"
    />
    <ElIcon v-if="getBindValue.listType === 'picture-card'"><Plus /></ElIcon>
  </ElUpload>
</template>
