import type { UploadProps } from 'element-plus'
export type UploadOmitList = 'fileList' | 'action' | 'httpRequest' | 'max'
export interface OssResponse {
  /**
   * 文件内型
   */
  contentType: string
  /**
   * 文件下载地址
   */
  downLoadUrl: string
  /**
   * 相对文件路径
   */
  objectName: string
  /**
   * 文件名
   */
  originFileName: string
  /**
   * 文件大小
   */
  size: number
}
/**
 * Props
 */
export interface Props extends Partial<Omit<UploadProps, UploadOmitList>> {
  /**
   * 绑定的文件数据
   */
  modelValue: BaseFileDTO[]
  /**
   * 文件最大size 单位：MB
   */
  sizeLimit?: number
  /**
   * 文件类型
   */
  fileType?: string[]
}
