<script lang="tsx">
import { propTypes } from '@/utils/propTypes'
import type { VxeTableEvents, VxeTableInstance } from 'vxe-table'
import { PagingPlus } from '@/components/Business/PagingPlus'
import { isString, omit } from 'xe-utils'
import { computed, defineComponent, onBeforeUnmount, onMounted, ref } from 'vue'

export default defineComponent({
  name: 'VTable',
  props: {
    loading: propTypes.bool.def(false),
    data: propTypes.array.def([]),
    pager: propTypes.object.def({}),
    maxHeight: propTypes.oneOfType([propTypes.number, propTypes.string]).def('')
  },
  emits: ['update', 'register', 'checkboxChange'],
  setup(props, { emit, slots, attrs, expose }) {
    const vxeTableRef = ref<VxeTableInstance>()

    let controller: AbortController | null = null // 初始化为null
    controller = new AbortController()

    window.addEventListener('resize', () => {}, { signal: controller.signal })
    onBeforeUnmount(() => {
      controller?.abort()
      controller = null
    })

    onMounted(() => {
      const tableRef = unref(vxeTableRef)
      emit('register', tableRef?.$parent, vxeTableRef)
    })

    const checkboxChange: VxeTableEvents.CheckboxChange = (selection) => {
      emit('checkboxChange', selection)
    }

    const pageUpdate = (val: BasicPage) => {
      emit('update', val)
    }

    expose({ vxeTableRef })

    const getBindValue = computed(() => {
      return omit({ ...attrs, ...props }, ['data', 'pager', 'loading', 'maxHeight'])
    })

    const vTableHeight = computed(() => {
      return props.maxHeight ? { maxHeight: props.maxHeight } : { height: 550 }
    })

    const renderCardView = () => (
      <el-scrollbar v-if={slots.card}>
        <div
          v-if={props.data.length !== 0}
          style={vTableHeight.value}
          class="grid justify-center min-h-[140px] grid-gap-[10px] grid-cols-[repeat(auto-fit,minmax(300px,1fr))]"
        >
          {props.data.map((item) => slots.card?.({ row: item }))}
        </div>
        <el-empty v-else image-size={200} />
      </el-scrollbar>
    )

    const renderTableView = () => (
      <vxe-table
        ref={vxeTableRef}
        border
        {...unref(getBindValue)}
        {...vTableHeight.value}
        onCheckbox-all={checkboxChange}
        onCheckbox-change={checkboxChange}
        data={props.data}
        row-config={{ isHover: true, ...getBindValue.value?.['row-config'] }}
      >
        {slots.default?.()?.map((item) => {
          if (isString(item.props?.dict)) {
            item.props!.cellRender = {
              name: 'dict',
              props: { dict: item.props!.dict || item.props!.field }
            }
          }
          return item
        })}
      </vxe-table>
    )

    return () => (
      <div class="table-container" v-loading={props.loading}>
        {slots.card ? renderCardView() : renderTableView()}
        {Object.keys(props.pager).length > 0 && (
          <PagingPlus pager={props.pager} onUpdate={pageUpdate} />
        )}
      </div>
    )
  }
})
</script>

<style lang="less" scoped>
.table-container :deep(.el-button.is-text) {
  padding: 8px 10px;
  margin-left: 0;
}
</style>
