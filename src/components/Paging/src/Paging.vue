<script lang="ts" setup>
import { ElPagination } from 'element-plus'

defineOptions({
  name: 'Paging'
})
interface Props {
  pager: Record<string, number>
  pageSizes?: number[]
  config?: string[]
}
const emits = defineEmits(['update'])
const props = withDefaults(defineProps<Props>(), {
  pager: () => ({
    current: 1,
    size: 20,
    total: 0
  }),
  pageSizes: () => [10, 20, 30, 40, 50, 100, 500, 1000],
  config: () => ['current', 'size', 'total']
})

const pagerConfig = computed(() => {
  const [current, size, total] = props.config
  return {
    current,
    size,
    total
  }
})
const sizeChange = (val: number) => {
  const config = unref(pagerConfig)
  emits(
    'update',
    Object.assign({}, props.pager, {
      [config.size]: val,
      [config.current]: 1
    })
  )
}
const currentChange = (val: number) => {
  const config = unref(pagerConfig)
  emits('update', Object.assign({}, props.pager, { [config.current]: val }))
}
</script>
<template>
  <div class="mt-[20px]">
    <ElPagination
      :current-page="pager[pagerConfig.current]"
      :hideOnSinglePage="false"
      :page-size="pager[pagerConfig.size]"
      :page-sizes="pageSizes"
      :total="pager[pagerConfig.total]"
      layout="sizes, prev, pager, next, jumper,
    ->, total"
      @update:current-page="currentChange"
      @update:page-size="sizeChange"
    />
  </div>
</template>
