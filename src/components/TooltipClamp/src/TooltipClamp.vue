<script lang="ts" setup>
import { BaseClamp, MouseoverEvent, Props } from '@/components/BaseClamp'
import { ElTooltip } from 'element-plus'
import { omit } from 'lodash-es'

defineOptions({
  name: 'TooltipClamp'
})

const props = defineProps<Props>()
const text = props.textContent || ''

const attrs = omit(props, ['textContent'])

const contentRef = ref<HTMLElement>()
const content = ref<string>('')
const tooltipRef = ref<InstanceType<typeof ElTooltip>>()
const handleMouseover = (e: MouseoverEvent) => {
  if (!e.clamped) {
    return
  }
  contentRef.value = e.currentTarget
  content.value = e.text
}
</script>

<template>
  <base-clamp :text-content="text" v-bind="{ ...$attrs, ...attrs }" @mouseover="handleMouseover">
    {{ text }}
  </base-clamp>
  <el-tooltip
    ref="tooltipRef"
    :content="content"
    :hide-after="0"
    :popper-options="{
      modifiers: [
        {
          name: 'computeStyles',
          options: {
            adaptive: false,
            enabled: false
          }
        }
      ]
    }"
    :virtual-ref="contentRef"
    popper-class="singleton-tooltip"
    virtual-triggering
  >
    <template #content>
      <slot name="content"></slot>
    </template>
  </el-tooltip>
</template>
