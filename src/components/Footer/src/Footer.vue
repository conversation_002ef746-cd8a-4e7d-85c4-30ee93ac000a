<script setup lang="ts">
import { useDesign } from '@/hooks/web/useDesign'
import { CustomerService } from '@/components/CustomerService'

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('footer')
</script>

<template>
  <div
    :class="prefixCls"
    class="text-center text-[var(--el-text-color-placeholder)] bg-[var(--app-content-bg-color)] h-[var(--app-footer-height)] leading-[var(--app-footer-height)] dark:bg-[var(--el-bg-color)]"
  >
    wangoon©弯弓信息科技有限公司
    <CustomerService />
  </div>
</template>
