{"common": {"export": "Export", "inputText": "Please enter", "selectText": "Please select", "startTimeText": "Start time", "endTimeText": "End time", "login": "<PERSON><PERSON>", "required": "This item is required", "loginOut": "Log out", "reminder": "<PERSON> Reminder", "loginOutMessage": "Do you want to log out the system?", "back": "Back", "ok": "Confirm", "cancel": "Cancel", "reload": "Reload", "closeTab": "Close label page", "closeTheLeftTab": "Close left label page", "closeTheRightTab": "Close right label page", "closeOther": "Close other label pages", "closeAll": "Close all label pages", "prevLabel": "Last step", "nextLabel": "Next step", "skipLabel": "<PERSON><PERSON>", "doneLabel": "End", "menu": "<PERSON><PERSON>", "menuDes": "Menu bar rendered with routes", "collapse": "Expand&collapse", "collapseDes": "Expand and collapse the menu bar", "tagsView": "Label page", "tagsViewDes": "Used to record route history", "tool": "Tool", "toolDes": "Used to configure custom systems", "query": "Search", "reset": "Reset", "shrink": "Fold", "expand": "Unfold", "delMessage": "Whether to delete the selected data?", "delWarning": "Reminder", "delOk": "Confirm", "delCancel": "Cancel", "delNoData": "Please select the data to be deleted", "delSuccess": "Delete successful", "No": "Number", "time": "Date", "type": "Type", "detail": "Details"}, "error": {"noPermission": "Sorry, you do not have permission to access this page.", "pageError": "Sorry, the page you visited does not exist.", "networkError": "Sorry, the server reported an error.", "returnToHome": "Return to Home"}, "size": {"default": "<PERSON><PERSON><PERSON>", "large": "Large", "small": "Small"}, "router": {"login": "<PERSON><PERSON>", "home": "工作台", "vendorRecommendedManagement": "Vendor Recommended Management", "styleSelectionAnnouncementManagement": "Style selection announcement management", "recommendedProductsManagement": "Recommended Products Management", "generalConfig": "General Config", "consigneeAddressConfiguration": "Consignee address configuration", "regionalSupplyManagementConfiguration": "Regional supply management configuration", "dictionaryManagement": "Dictionary Management", "fileExport": "File Export", "library": "Library", "colorLibrary": "Color Library", "sizeLibrary": "Size Library", "materialLibrary": "Material Library", "materialCategoryManagement": "Material Category Management", "materialListManagement": "Material List Management", "lastLibrary": "Last Library", "moldLibrary": "Mold Library", "heelAndSoleLibrary": "Heel and sole Library", "product": "Product", "planningManagement": "Planning Management", "productManagement": "Product Management", "productCategoryManagement": "Product Category Management", "sKCListManagement": "SKC List Management", "infringementInspection": "Infringement Inspection", "socialMediaTrendAnalysis": "Social media trend analysis", "socialMediaPopularityTrend": "Social media popularity trend", "accountContentTrend": "Account content trend", "tagContentTrend": "Tag content trend", "accountManagement": "Account Management", "tagManagement": "Tag Management", "trendStyleLibrary": "Trend style library", "socialMediaStyleLibrary": "Social media style library", "buyerStyleLibrary": "Buyer style library", "statisticalReports": "Statistical reports"}, "permission": {"hasPermission": "Please set the operation permission value"}}