<script lang="ts" setup>
import { Icon } from '@/components/Icon'
import { ref } from 'vue'
import type { VxeTableInstance } from 'vxe-table'
import { useHelpStore } from '@/views/basic-library-manage/store'
import { ElPagination, FormInstance, type FormRules } from 'element-plus'
import { Pager } from '@/views/basic-library-manage/api/common'
import dayjs from 'dayjs'
import { isEqual } from 'lodash-es'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { ColorSampleProgressListApi, getColorSampleProgressList } from './api'
import { SelectPlus } from '@/components/Business/SelectPlus'
import { hasValue } from '@/utils'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { scrollProp } from '@/plugins/vxeTable'
import { watchDebounced } from '@vueuse/core'

defineOptions({
  name: 'ColorSampleProgress'
})

const router = useRouter()
const route = useRoute()

const useConst = () => {
  const productNumberRef = ref<InstanceType<typeof SelectPlus>>()
  const associateStyleRef = ref<InstanceType<typeof SelectPlus>>()
  onActivated(() => {
    productNumberRef.value?.queryOptions()
    associateStyleRef.value?.queryOptions()
  })
  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '120px'
    },
    {
      formLabelLength: '200px'
    }
  ])

  return {
    productNumberRef,
    associateStyleRef,
    formLabelLength
  }
}

const { productNumberRef, associateStyleRef, formLabelLength } = useConst()

const useOperation = () => {
  const tableRef = ref<VxeTableInstance>()
  const { setProductFormData } = useHelpStore()
  const selectedRows = ref<ColorSampleProgressListApi.List>([])
  const handleSelectChange = () => {
    selectedRows.value = tableRef.value?.getCheckboxRecords() as ColorSampleProgressListApi.List
  }

  const handleDetail = (row: ColorSampleProgressListApi.Row) => {
    setProductFormData(formData.value)
    router.push({
      name: 'ViewProduct',
      query: {
        id: row.id
      }
    })
  }

  return {
    tableRef,
    selectedRows,
    handleDetail,
    handleSelectChange
  }
}

const { tableRef, selectedRows, handleDetail, handleSelectChange } = useOperation()

const useQuery = () => {
  type FormModel = ColorSampleProgressListApi.Request
  const formRef = ref<FormInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const formRules = ref<FormRules<FormModel>>({
    launchSeason: [{ required: true, message: '请选择开发季节', trigger: 'change' }],
    brand: [{ required: true, message: '请选择品牌', trigger: 'change' }]
  })
  const defaultFormData: FormModel & {
    date: [string, string]
  } = {
    assignedFactory: [],
    brand: [],
    chooseChannel: [],
    dataStatus: [],
    designPersonId: [],
    designerId: [],
    developStage: [],
    developmentContactPersonId: [],
    developmentType: [],
    date: ['', ''],
    launchSeason: [],
    productNumber: [],
    region: [],
    styleWms: [],
    taskNode: [],
    technicalContactPersonId: [],
    vocStyle: []
  }
  let lastFormData = ref({
    ...defaultFormData
  })
  const formData = ref({
    ...defaultFormData
  })

  const handleDetail = (row: ColorSampleProgressListApi.Row) => {
    router.push({ name: 'ViewProduct', query: { id: row.id } })
  }

  const tableData = ref<ColorSampleProgressListApi.List>([])
  const pager = ref<Pager>({
    current: 1,
    size: 50,
    total: 0
  })
  const queryLoading = ref(false)
  const queryParams = computed<ColorSampleProgressListApi.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1],
      date: undefined
    }
  })
  const defaultTime: [Date, Date] = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ]

  let controller: AbortController | null = null
  const handleQuery = async () => {
    const valid = await formRef.value?.validate().catch(() => {})
    if (!valid) {
      return
    }
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData.value, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await getColorSampleProgressList(queryParams.value, controller.signal)
    queryLoading.value = false
    selectedRows.value = []
    if (error === null && result?.datas) {
      lastFormData.value = { ...formData.value }
      const { records } = result.datas
      tableData.value = records || []
      await nextTick()
      tableRef.value?.loadData([]).then(() => {
        tableRef.value?.loadData(tableData.value)
      })
      pager.value.total = result.datas?.pager?.total || 0
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
  }

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = () => {
    formRef.value
      ?.validate((valid) => {
        if (!valid) {
          return
        }
        let reqParam: string
        if (selectedRows && selectedRows.value?.length > 0) {
          const idList = selectedRows.value.map((item) => item.id)
          reqParam = JSON.stringify({ idList })
        } else {
          reqParam = JSON.stringify(queryParams.value)
        }
        exportFn({
          exportType: 'skc-sample-progress-report',
          reqParam
        })
      })
      .catch(() => {})
  }

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef
  })

  return {
    formRef,
    pagerRef,
    formRules,
    formData,
    lastFormData,
    tableData,
    pager,
    queryLoading,
    defaultTime,
    queryParams,
    handleQuery,
    handleReset,
    handleExport,
    exportLoading,
    visible,
    setVisible,
    maxHeight,
    handleDetail
  }
}

const {
  formRef,
  pagerRef,
  formRules,
  formData,
  tableData,
  pager,
  queryLoading,
  defaultTime,
  handleQuery,
  handleReset,
  handleExport,
  exportLoading,
  visible,
  setVisible,
  maxHeight
} = useQuery()

async function initQueryByRoute() {
  const query = route.query
  if (hasValue(query)) {
    const { productNumber, launchSeason, brand } = query
    formData.value.productNumber = productNumber ? [productNumber as string] : []
    formData.value.launchSeason = launchSeason ? [launchSeason as string] : []
    formData.value.brand = brand ? [+brand] : []
  }
  await handleQuery()
  formRef.value?.clearValidate(['brand', 'launchSeason'])
}
onMounted(() => {
  initQueryByRoute()
})

onActivated(() => {
  initQueryByRoute()
})

onMounted(() => {
  watchDebounced(
    formData,
    async () => {
      await handleQuery()
    },
    { deep: true, debounce: 400 }
  )
})
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_250px)] overflow-x-hidden overflow-y-auto">
        <ElForm ref="formRef" :label-width="formLabelLength" :model="formData" :rules="formRules">
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="产品编号" prop="productNumber">
                <SelectPlus
                  ref="productNumberRef"
                  v-model="formData.productNumber"
                  api-key="getProductNumberList"
                  filterable
                  multiple
                  virtualized
                  @change="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="开发季节" prop="launchSeason">
                <SelectPlus
                  v-model="formData.launchSeason"
                  api-key="COMMON_MARKET_SEASON"
                  cache
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  multiple
                  placeholder="请选择"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="" label-width="0">
                <ElButton text @click="setVisible">
                  {{ visible ? '收起' : '展开' }}
                  <Icon
                    :class="visible ? 'rotate-90' : ''"
                    class="transform transition duration-400"
                    icon="ant-design:down-outlined"
                  />
                </ElButton>
                <ElButton :loading="queryLoading" type="primary" @click="handleQuery">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
                  查询
                </ElButton>
                <ElButton :loading="queryLoading" @click="handleReset">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
                  重置
                </ElButton>
                <ElButton :loading="exportLoading" type="primary" @click="handleExport">
                  <Icon class="mr-1" icon="ep:upload-filled" />
                  导出
                </ElButton>
              </ElFormItem>
            </ElCol>
            <ElCol :span="16">
              <ElFormItem label="品牌" prop="brand">
                <SelectPlus
                  v-model="formData.brand"
                  api-key="baseBrand"
                  cache
                  checkbox
                  checkbox-button
                />
              </ElFormItem>
            </ElCol>
            <ElCollapseTransition>
              <div v-show="visible" class="flex flex-wrap w-full">
                <ElCol :span="8">
                  <ElFormItem label="区域" prop="region">
                    <SelectPlus
                      v-model="formData.region"
                      api-key="COMMON_REGION"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="供应商" prop="assignedFactory">
                    <SelectPlus
                      v-model="formData.assignedFactory"
                      api-key="getSupplierList"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="创建时间" prop="date">
                    <ElDatePicker
                      v-model="formData.date"
                      :default-time="defaultTime"
                      class="max-w-96"
                      clearable
                      end-placeholder="结束时间"
                      range-separator="~"
                      start-placeholder="开始时间"
                      type="daterange"
                      unlink-panels
                      value-format="YYYY-MM-DD"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="产品阶段" prop="developStage">
                    <SelectPlus
                      v-model="formData.developStage"
                      api-key="PRODUCT_STAGE"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="母Style" prop="vocStyle">
                    <SelectPlus
                      ref="associateStyleRef"
                      v-model="formData.vocStyle"
                      api-key="getProductNumberList"
                      filterable
                      multiple
                      virtualized
                      @change="handleQuery"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="Style（WMS）" prop="styleWms">
                    <SelectPlus
                      v-model="formData.styleWms"
                      api-key="getStyleWmsList"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                      virtualized
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="设计师" prop="designerId">
                    <CascadeSelector
                      v-model="formData.designerId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="产品企划" prop="designPersonId">
                    <CascadeSelector
                      v-model="formData.designPersonId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="开发跟进人员" prop="developmentContactPersonId">
                    <CascadeSelector
                      v-model="formData.developmentContactPersonId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="技术跟进人员" prop="technicalContactPersonId">
                    <CascadeSelector
                      v-model="formData.technicalContactPersonId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="开发类型" prop="developmentType">
                    <SelectPlus
                      v-model="formData.developmentType"
                      api-key="PRODUCT_DEV_TYPE"
                      clearable
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="任务节点" prop="taskNode">
                    <SelectPlus
                      v-model="formData.taskNode"
                      api-key="PRODUCT_TASK_NOTE"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="数据状态" prop="dataStatus">
                    <SelectPlus
                      v-model="formData.dataStatus"
                      api-key="PRODUCT_DATA_STATUS"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
              </div>
            </ElCollapseTransition>
          </ElRow>
        </ElForm>
        <VxeTable
          ref="tableRef"
          :cellConfig="{ height: 110 }"
          :data="tableData"
          :loading="queryLoading"
          :maxHeight="maxHeight - 75"
          :minHeight="100"
          showOverflow="tooltip"
          v-bind="{ ...scrollProp }"
          @checkbox-change="handleSelectChange"
          @checkbox-all="handleSelectChange"
        >
          <VxeColgroup fixed="left" title="产品基本信息">
            <VxeColumn type="checkbox" width="40" />
            <VxeColumn title="序号" type="seq" width="60" />
            <VxeColumn
              :cell-render="{ name: 'Link', props: { clickFn: ({ row }) => handleDetail(row) } }"
              :show-overflow="false"
              field="productNumber"
              title="产品编号"
              width="120"
            />
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="thumbnail"
              title="缩略图"
              width="80"
            />
            <VxeColumn field="brandItemName" title="品牌" width="100" />
            <VxeColumn field="categoryItemName" title="产品类目" width="100" />
            <VxeColumn field="launchSeason" title="开发季节" width="100" />
          </VxeColgroup>
          <VxeColgroup title="SKC信息">
            <VxeColumn field="skcCode" fixed="left" title="SKC编号" width="100" />
            <VxeColumn field="mainFabricItemName" fixed="left" title="主要面料" width="100" />
            <VxeColumn field="colorIdItemName" fixed="left" title="产品配色" width="100" />
            <VxeColumn field="colorTypeItemName" title="配色类型" width="100" />
            <VxeColumn field="wmsColorName" title="WMS配色名称" width="140" />
            <VxeColumn field="skcStatusItemName" title="状态" width="100" />
            <VxeColumn field="createTime" title="创建时间" width="100" />
            <VxeColumn field="effectTime" title="生效时间" width="100" />
            <VxeColumn field="sizeRangeIdItemName" title="尺码段" width="100" />
            <VxeColumn field="selectedSizeItemName" title="选中尺码" width="100" />
            <VxeColumn field="estimatedOrderVolume" title="渠道订单量汇总" width="140" />
            <VxeColumn field="velvetAppliedItemName" title="是否植绒" width="100" />
            <VxeColumn field="velvetRequirements" title="植绒要求" width="100" />
            <VxeColumn field="velvetTariff" title="植绒/植皮关税率" width="100" />
            <VxeColumn field="unVelvetTariff" title="不植关税率" width="100" />
          </VxeColgroup>
          <VxeColgroup title="材质信息">
            <VxeColumn field="fabric1ItemName" title="面料1" width="100" />
            <VxeColumn field="fabric2ItemName" title="面料2" width="100" />
            <VxeColumn field="fabric3ItemName" title="面料3" width="100" />
            <VxeColumn field="fabric4ItemName" title="面料4" width="100" />
            <VxeColumn field="liningMaterialCodeItemName" title="里材料" width="100" />
            <VxeColumn field="liningSituationItemName" title="里绒情况" width="100" />
            <VxeColumn field="paddingMaterialSurfaceCodeItemName" title="垫材料" width="100" />
            <VxeColumn field="soleMaterialOutsoleCodeItemName" title="底材料" width="100" />
          </VxeColgroup>
          <VxeColgroup title="材质占比信息">
            <VxeColumn field="percentPu" title="橡胶/塑料%" width="100" />
            <VxeColumn field="percentCorium" title="真皮%" width="100" />
            <VxeColumn field="percentTextile" title="纺织物%" width="100" />
            <VxeColumn field="percentOther" title="其他%" width="100" />
          </VxeColgroup>
          <VxeColgroup title="初样信息">
            <VxeColumn field="initialSampleCode" title="初样单编号" width="100" />
            <VxeColumn field="initialSupplierItemName" title="工厂" width="100" />
            <VxeColumn field="initialRegionalSupplyPersonIdItemName" title="供管" width="100" />
            <VxeColumn
              field="initialDevelopmentContactPersonIdItemName"
              title="跟进开发人员"
              width="100"
            />
            <VxeColumn
              field="initialTechnicalContactPersonIdItemName"
              title="跟进技术人员"
              width="100"
            />
            <VxeColumn field="initialSampleColorItemName" title="初样颜色" width="140" />
            <VxeColumn field="initialSampleDate" title="初样下发时间" width="100" />
            <VxeColumn field="initialRequiredNumber" title="需求数量（双）" width="100" />
            <VxeColumn field="initialFinishDate" title="需求完成日期" width="120" />
            <VxeColumn
              field="initialOutsourcingIssueDate"
              title="开发工程师外发打样日期"
              width="160"
            />
            <VxeColumn
              field="initialOutsourcingAcceptDate"
              title="开发工程师工厂接收日期"
              width="160"
            />
            <VxeColumn
              field="initialSampleCompletionDate"
              title="开发工程师打样完成日期"
              width="160"
            />
            <VxeColumn field="initialSampleNumber" title="打样数量（双）" width="120" />
            <VxeColumn field="initialSampleSendDate" title="样品寄送日期" width="100" />
            <VxeColumn field="initialExpectedCompletionDate" title="工厂预计完成时间" width="160" />
            <VxeColumn
              field="initialSampleResultDictItemName"
              title="开发工程师打样跟进结果"
              width="180"
            />
            <VxeColumn
              field="initialDevelopmentEvaluationOpinions"
              show-overflow
              title="开发工程师开发评估意见"
              width="180"
            />
            <VxeColumn
              field="initialTechnicalEvaluationOpinions"
              show-overflow
              title="技术工程师技术评估意见"
              width="180"
            />
            <!-- <VxeColumn field="initialAppearanceOpinions" title="开发工程师外观意见" width="180" /> -->
            <VxeColumn field="initialNewMoldDictItemName" title="是否新开模" width="100" />
            <VxeColumn field="initialLastBottomTypeItemName" title="楦底类型" width="100" />
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="initialSampleImage"
              title="初样样品图"
              width="100"
            />
            <VxeColumn field="initialSampleAcceptDate" title="设计师样品接收日期" width="160" />
            <VxeColumn
              field="initialPreliminaryConclusionItemName"
              title="设计师初样评审结果"
              width="160"
            />
            <VxeColumn
              field="initialSampleReviewOpinions"
              show-overflow
              title="设计师初样评审意见"
              width="180"
            />
            <VxeColumn field="initialSampleAge" title="初样时效（D）" width="120" />
          </VxeColgroup>
          <VxeColgroup title="齐色样信息">
            <VxeColumn field="colorSampleCode" title="齐色样单编号" width="100" />
            <VxeColumn field="colorSupplierItemName" title="工厂" width="100" />
            <VxeColumn field="colorRegionalSupplyPersonIdItemName" title="供管" width="100" />
            <VxeColumn
              field="colorDevelopmentContactPersonIdItemName"
              title="跟进开发人员"
              width="100"
            />
            <VxeColumn
              field="colorTechnicalContactPersonIdItemName"
              title="跟进技术人员"
              width="100"
            />
            <VxeColumn field="colorSampleDate" title="齐色样下发时间" width="160" />
            <VxeColumn field="colorRequiredNumber" title="需求数量（双）" width="120" />
            <VxeColumn field="colorFinishDate" title="需求完成日期" width="100" />
            <VxeColumn field="colorOutsourcingIssueDate" title="外发打样日期" width="100" />
            <VxeColumn field="colorOutsourcingAcceptDate" title="工厂接收日期" width="100" />
            <VxeColumn field="colorSampleCompletionDate" title="打样完成日期" width="100" />
            <VxeColumn field="colorSampleSendDate" title="样品寄送日期" width="100" />
            <VxeColumn field="colorSampleNumber" title="打样数量（双）" width="120" />
            <VxeColumn field="colorExpectedCompletionDate" title="工厂预计完成时间" width="160" />
            <VxeColumn field="colorSampleResultDictItemName" title="打样跟进结果" width="180" />
            <VxeColumn
              field="colorDevelopmentEvaluationOpinions"
              show-overflow
              title="开发工程师开发评估意见"
              width="180"
            />
            <VxeColumn
              field="colorTechnicalEvaluationOpinions"
              show-overflow
              title="技术工程师技术评估意见"
              width="180"
            />
            <!-- <VxeColumn field="colorAppearanceOpinions" title="开发工程师外观意见" width="180" /> -->
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="colorSampleImage"
              title="齐色样样品图"
              width="100"
            />
            <VxeColumn
              field="colorPreliminaryConclusionItemName"
              title="设计师齐色样评审结果"
              width="160"
            />
            <VxeColumn field="colorSampleReviewOpinions" title="设计师齐色样评审意见" width="180" />
            <VxeColumn
              field="colorDesignerProcessingOpinions"
              title="设计师齐色样处理结果"
              width="180"
            />
            <VxeColumn field="colorSampleAge" title="齐色样时效（D）" width="140" />
          </VxeColgroup>
          <VxeColgroup title="确认样信息">
            <VxeColumn field="confirmSampleCode" title="确认样单编号" width="100" />
            <VxeColumn field="confirmSupplierItemName" title="工厂" width="100" />
            <VxeColumn field="confirmRegionalSupplyPersonIdItemName" title="供管" width="100" />
            <VxeColumn
              field="confirmDevelopmentContactPersonIdItemName"
              title="跟进开发人员"
              width="100"
            />
            <VxeColumn
              field="confirmTechnicalContactPersonIdItemName"
              title="跟进技术人员"
              width="100"
            />
            <VxeColumn field="confirmSampleDate" title="确认样下发时间" width="140" />
            <VxeColumn field="confirmRequiredNumber" title="需求数量（双）" width="120" />
            <VxeColumn field="confirmFinishDate" title="需求完成日期" width="100" />
            <VxeColumn field="confirmOutsourcingIssueDate" title="外发打样日期" width="100" />
            <VxeColumn field="confirmOutsourcingAcceptDate" title="工厂接收日期" width="100" />
            <VxeColumn field="confirmSampleCompletionDate" title="打样完成日期" width="100" />
            <VxeColumn field="confirmSampleSendDate" title="样品寄送日期" width="100" />
            <VxeColumn field="confirmSampleNumber" title="打样数量（双）" width="120" />
            <VxeColumn
              field="confirmMaterialColorSupplyDate"
              title="材料色卡提供日期"
              width="160"
            />
            <VxeColumn
              field="confirmMaterialColorConfirmationDate"
              title="材料色卡确认日期"
              width="160"
            />
            <VxeColumn field="confirmExpectedCompletionDate" title="工厂预计完成时间" width="160" />
            <VxeColumn field="confirmSampleResultDictItemName" title="打样跟进结果" width="180" />
            <VxeColumn
              field="confirmSampleMakingInfo"
              show-overflow
              title="打样信息记录"
              width="180"
            />
            <VxeColumn
              field="confirmDevelopmentEvaluationOpinions"
              show-overflow
              title="开发工程师开发评估意见"
              width="180"
            />
            <VxeColumn
              field="confirmTechnicalEvaluationOpinions"
              show-overflow
              title="技术工程师技术评估意见"
              width="180"
            />
            <!-- <VxeColumn field="confirmAppearanceOpinions" title="开发工程师外观意见" width="180" /> -->
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="confirmSampleImage"
              title="确认样样品图"
              width="100"
            />
            <VxeColumn
              field="confirmPreliminaryConclusionItemName"
              title="设计师确认样评审结果"
              width="160"
            />
            <VxeColumn
              field="confirmSampleReviewOpinions"
              title="设计师确认样评审意见"
              width="180"
            />
            <VxeColumn field="confirmSampleAge" title="确认样时效（D）" width="140" />
          </VxeColgroup>
        </VxeTable>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @change="handleQuery"
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped></style>
