import to from 'await-to-js'
import { service } from '@/config/fetch/service'

export namespace ColorSampleProgressListApi {
  export type Request = Partial<
    {
      assignedFactory: string[]
      brand: number[]
      chooseChannel: string[]
      dataStatus: string[]
      designerId: number[]
      designPersonId: number[]
      developmentContactPersonId: number[]
      developmentType: string[]
      developStage: string[]
      endTime: string
      launchSeason: string[]
      productNumber: string[]
      region: string[]
      startTime: string
      styleWms: string[]
      taskNode: string[]
      technicalContactPersonId: number[]
      vocStyle: string[]
      [key: string]: any
    } & PageParams
  >
  export interface Record {
    ankleCoverage?: string
    ankleCoverageItemName?: string
    applicableSeason?: string
    applicableSeasonItemName?: string
    assignedFactory?: string
    assignedFactoryItemName?: string
    brand?: string
    brandItemName?: string
    categoryItemName?: string
    closureMethod?: string
    closureMethodItemName?: string
    colorAppearanceOpinions?: string
    colorDesignerProcessingOpinions?: string
    colorDevelopmentContactPersonId?: string[]
    colorDevelopmentContactPersonIdItemName?: string
    colorDevelopmentEvaluationOpinions?: string
    colorExpectedCompletionDate?: string
    colorFinishDate?: string
    colorId?: number
    colorIdDummy?: number
    colorIdItemName?: string
    colorOutsourcingAcceptDate?: string
    colorOutsourcingIssueDate?: string
    colorPreliminaryConclusion?: string
    colorPreliminaryConclusionItemName?: string
    colorProofId?: number
    colorProofResultId?: number
    colorRecordCode?: string
    colorRegionalSupplyPersonId?: string[]
    colorRegionalSupplyPersonIdItemName?: string
    colorRequiredNumber?: number
    colorSampleAge?: number
    colorSampleCode?: string
    colorSampleCompletionDate?: string
    colorSampleDate?: string
    colorSampleImage?: ColorSampleImage
    colorSampleNumber?: number
    colorSampleReport?: ColorSampleReport
    colorSampleResultDict?: string
    colorSampleResultDictItemName?: string
    colorSampleReviewOpinions?: string
    colorSampleSendDate?: string
    colorSupplier?: number
    colorSupplierItemName?: string
    colorTechnicalContactPersonId?: string[]
    colorTechnicalContactPersonIdItemName?: string
    colorTechnicalEvaluationOpinions?: string
    colorType?: string
    colorTypeItemName?: string
    confirmAppearanceOpinions?: string
    confirmDevelopmentContactPersonId?: string[]
    confirmDevelopmentContactPersonIdItemName?: string
    confirmDevelopmentEvaluationOpinions?: string
    confirmExpectedCompletionDate?: string
    confirmFinishDate?: string
    confirmMaterialColorConfirmationDate?: string
    confirmMaterialColorSupplyDate?: string
    confirmOutsourcingAcceptDate?: string
    confirmOutsourcingIssueDate?: string
    confirmPreliminaryConclusion?: string
    confirmPreliminaryConclusionItemName?: string
    confirmProofId?: number
    confirmProofResultId?: number
    confirmRecordCode?: string
    confirmRegionalSupplyPersonId?: string[]
    confirmRegionalSupplyPersonIdItemName?: string
    confirmRequiredNumber?: number
    confirmSampleAge?: number
    confirmSampleCode?: string
    confirmSampleCompletionDate?: string
    confirmSampleDate?: string
    confirmSampleImage?: ConfirmSampleImage
    confirmSampleMakingInfo?: string
    confirmSampleNumber?: number
    confirmSampleReport?: ConfirmSampleReport
    confirmSampleResultDict?: string
    confirmSampleResultDictItemName?: string
    confirmSampleReviewOpinions?: string
    confirmSampleSendDate?: string
    confirmSupplier?: number
    confirmSupplierItemName?: string
    confirmTechnicalContactPersonId?: string[]
    confirmTechnicalContactPersonIdItemName?: string
    confirmTechnicalEvaluationOpinions?: string
    createTime?: string
    dataStatus?: string
    dataStatusItemName?: string
    developmentChannel?: string
    developmentModel?: string
    developmentModelItemName?: string
    dtc?: string
    dtcItemName?: string
    effectTime?: string
    estimatedOrderVolume?: number
    expectedLaunchDate?: string
    fabric1?: number
    fabric1ItemName?: string
    fabric2?: number
    fabric2ItemName?: string
    fabric3?: number
    fabric3ItemName?: string
    fabric4?: number
    fabric4ItemName?: string
    id?: number
    initialAppearanceOpinions?: string
    initialDevelopmentContactPersonId?: string[]
    initialDevelopmentContactPersonIdItemName?: string
    initialDevelopmentEvaluationOpinions?: string
    initialExpectedCompletionDate?: string
    initialFinishDate?: string
    initialLastBottomType?: string
    initialLastBottomTypeItemName?: string
    initialNewMoldDict?: string
    initialNewMoldDictItemName?: string
    initialOutsourcingAcceptDate?: string
    initialOutsourcingIssueDate?: string
    initialPreliminaryConclusion?: string
    initialPreliminaryConclusionItemName?: string
    initialProofId?: number
    initialProofResultId?: number
    initialRecordCode?: string
    initialRegionalSupplyPersonId?: string[]
    initialRegionalSupplyPersonIdItemName?: string
    initialRequiredNumber?: number
    initialSampleAcceptDate?: string
    initialSampleAge?: number
    initialSampleCode?: string
    initialSampleColor?: number
    initialSampleColorItemName?: string
    initialSampleCompletionDate?: string
    initialSampleDate?: string
    initialSampleImage?: InitialSampleImage
    initialSampleNumber?: number
    initialSampleProcessDict?: string
    initialSampleProcessDictItemName?: string
    initialSampleReport?: InitialSampleReport
    initialSampleResultDict?: string
    initialSampleResultDictItemName?: string
    initialSampleReviewOpinions?: string
    initialSampleSendDate?: string
    initialSupplier?: number
    initialSupplierItemName?: string
    initialTechnicalContactPersonId?: string[]
    initialTechnicalContactPersonIdItemName?: string
    initialTechnicalEvaluationOpinions?: string
    lastBottomType?: string
    lastBottomTypeItemName?: string
    lastsStandard?: string
    lastsStandardItemName?: string
    launchSeason?: string
    liningMaterialCode?: string[]
    liningMaterialCodeItemName?: string[]
    liningSituation?: string[]
    liningSituationItemName?: string[]
    mainFabric?: number
    mainFabricItemName?: string
    modelId?: number
    modelIdItemName?: string
    paddingMaterialSurfaceCode?: string[]
    paddingMaterialSurfaceCodeItemName?: string[]
    percentCorium?: number
    percentOther?: number
    percentPu?: number
    percentTextile?: number
    productCategory?: number
    productCategoryItemName?: string
    productCategoryParent?: number
    productCategoryParentItemName?: string
    productNumber?: string
    productStyle?: string
    productStyleItemName?: string
    region?: string
    regionItemName?: string
    safetyFeatures?: string
    safetyFeaturesItemName?: string
    selectedSize?: string[]
    selectedSizeItemName?: string[]
    sizeRangeId?: string[]
    sizeRangeIdItemName?: string[]
    sizeValue?: string[]
    skcCode?: string
    skcStatus?: string
    skcStatusItemName?: string
    soleMaterialOutsoleCode?: string[]
    soleMaterialOutsoleCodeItemName?: string[]
    styleStructure?: string
    styleStructureItemName?: string
    styleWms?: string
    targetAudience?: string
    targetAudienceItemName?: string
    taskNode?: string
    taskNodeItemName?: string
    thumbnail?: BaseFileDTO
    toeStandard?: string
    toeStandardItemName?: string
    unVelvetTariff?: string
    velvetApplied?: string
    velvetAppliedItemName?: string
    velvetRequirements?: string
    velvetTariff?: string
    waterproofLevel?: string
    waterproofLevelItemName?: string
    wmsColorName?: string
    [property: string]: any
  }

  export interface ColorSampleImage {
    bucketName: string
    businessId: number
    fileName: string
    fileType: string
    fileUrl: string
    id: number
    signatureUrl: string
    watermarkType: string
    [property: string]: any
  }

  export interface ColorSampleReport {
    bucketName: string
    businessId: number
    fileName: string
    fileType: string
    fileUrl: string
    id: number
    signatureUrl: string
    watermarkType: string
    [property: string]: any
  }

  export interface ConfirmSampleImage {
    bucketName: string
    businessId: number
    fileName: string
    fileType: string
    fileUrl: string
    id: number
    signatureUrl: string
    watermarkType: string
    [property: string]: any
  }

  export interface ConfirmSampleReport {
    bucketName: string
    businessId: number
    fileName: string
    fileType: string
    fileUrl: string
    id: number
    signatureUrl: string
    watermarkType: string
    [property: string]: any
  }

  export interface InitialSampleImage {
    bucketName: string
    businessId: number
    fileName: string
    fileType: string
    fileUrl: string
    id: number
    signatureUrl: string
    watermarkType: string
    [property: string]: any
  }

  export interface InitialSampleReport {
    bucketName: string
    businessId: number
    fileName: string
    fileType: string
    fileUrl: string
    id: number
    signatureUrl: string
    watermarkType: string
    [property: string]: any
  }

  export type Row = Record
  export type List = Row[]
  export type Response = PagedResponseData<Row>
}
export function getColorSampleProgressList(
  data: ColorSampleProgressListApi.Request,
  signal?: AbortSignal
) {
  return to<ColorSampleProgressListApi.Response>(
    service.post('/pdm-base/productCalender/skcSampleProgress/page', data, { signal })
  )
}
