import to from 'await-to-js'
import { service } from '@/config/axios/service'

export namespace ProductDevProgressListApi {
  export type Request = Partial<
    {
      assignedFactory: string[]
      brand: number[]
      chooseChannel: string[]
      dataStatus: string[]
      designerId: number[]
      designPersonId: number[]
      developmentContactPersonId: number[]
      developmentType: string[]
      developStage: string[]
      endTime: string
      launchSeason: string[]
      productNumber: string[]
      region: string[]
      startTime: string
      styleWms: string[]
      taskNode: string[]
      technicalContactPersonId: number[]
      vocStyle: string[]
      [key: string]: any
    } & PageParams
  >

  export interface Record {
    ankleCoverage?: string
    ankleCoverageItemName?: string
    applicableSeason?: string
    applicableSeasonItemName?: string
    assignedFactory?: string
    assignedFactoryItemName?: string
    brand?: string
    brandItemName?: string
    categoryItemName?: string
    chooseChannel?: string[]
    choppingKnifeConformationDate?: string
    choppingKnifeTrialReport?: string
    closureMethod?: string
    closureMethodItemName?: string
    colorConfigurationTime?: string
    colorSampleAge?: number
    colorSampleReviewTime?: string
    colorSampleTimes?: number
    confirmationSampleReviewTime?: string
    confirmSampleAge?: number
    confirmSampleTimes?: number
    copyright?: string
    copyrightInspection?: number
    copyrightInspectionItemName?: string
    copyrightItemName?: string
    costQuotationTime?: string
    dataStatus?: string
    dataStatusItemName?: string
    designerId?: number
    designerIdItemName?: string
    designPersonId?: number
    designPersonIdItemName?: string
    designStyleTime?: string
    developmentChannel?: string
    developmentContactPersonId?: string[]
    developmentContactPersonIdItemName?: string[]
    developmentModel?: string
    developmentModelItemName?: string
    dtc?: string
    dtcItemName?: string
    entryRegistrationTime?: string
    estimatedOrderVolume?: number
    expectedLaunchDate?: string
    fittingConformationDate?: string
    fittingDoubleEndDate?: string
    fittingSingleEndDate?: string
    fittingTrialReport?: string
    flockingUpdateTime?: string
    handScissorsConformationDate?: string
    handScissorsTrialReport?: string
    id?: number
    infringementCode?: string
    infringementRisk?: string
    infringementRiskItemName?: string
    initialSampleAge?: number
    initialSampleReviewTime?: string
    initialSampleTimes?: number
    lastBottomType?: string
    lastBottomTypeItemName?: string
    lastsStandard?: string
    lastsStandardItemName?: string
    launchSeason?: string
    mainChannelMark?: string
    materialInformationTime?: string
    materialPercentInformationTime?: string
    meetingResult?: string
    meetingResultItemName?: string
    modelId?: number
    modelIdItemName?: string
    predictLaunchDate?: string
    productCategory?: number
    productCategoryItemName?: string
    productCategoryParent?: number
    productCategoryParentItemName?: string
    productDocumentationTime?: string
    productNumber?: string
    productOperationImprovementTime?: string
    productStyle?: string
    productStyleItemName?: string
    purchaseOrderTime?: string
    region?: string
    regionalSupplyPersonId?: string[]
    regionalSupplyPersonIdItemName?: string[]
    regionItemName?: string
    riskAssessmentDate?: string
    riskAssessmentReport?: string
    riskCountry?: string
    safetyFeatures?: string
    safetyFeaturesItemName?: string
    sampleAllocationTime?: string
    selectedColor?: string[]
    selectedColorItemName?: string[]
    selectedSize?: string[]
    selectedSizeItemName?: string[]
    selectionAge?: number
    selectionConclusionTime?: string
    selectionMeetingReviewOpinion?: string
    sendTime?: string
    sizeRangeId?: string[]
    sizeRangeIdItemName?: string[]
    sizeValue?: string[]
    styleStructure?: string
    styleStructureItemName?: string
    styleWms?: string
    targetAudience?: string
    targetAudienceItemName?: string
    taskNode?: string
    taskNodeItemName?: string
    taskNodeStatus?: string
    technicalContactPersonId?: string[]
    technicalContactPersonIdItemName?: string[]
    thumbnail?: BaseFileDTO
    toeStandard?: string
    toeStandardItemName?: string
    waterproofLevel?: string
    waterproofLevelItemName?: string
  }
  export type Row = Record
  export type List = Row[]
  export type Response = PagedResponseData<Row>
}
export function getProductDevProgressList(
  data: ProductDevProgressListApi.Request,
  signal?: AbortSignal
) {
  return to<ProductDevProgressListApi.Response>(
    service.post('/pdm-base/productCalender/productDevelopProgress/page', data, { signal })
  )
}
