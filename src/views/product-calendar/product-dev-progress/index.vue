<script lang="ts" setup>
import { ref } from 'vue'
import { Pager } from '@/views/basic-library-manage/api/common'
import { SelectPlus } from '@/components/Business/SelectPlus'
import type { VxeTableInstance } from 'vxe-table'
import { ElPagination, FormInstance, type FormRules } from 'element-plus'
import dayjs from 'dayjs'
import { isEqual } from 'lodash-es'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { Icon } from '@/components/Icon'
import {
  getProductDevProgressList,
  ProductDevProgressListApi
} from '@/views/product-calendar/product-dev-progress/api'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { scrollProp } from '@/plugins/vxeTable'
import { watchDebounced } from '@vueuse/core'

defineOptions({
  name: 'ProductDevProgress'
})

const router = useRouter()

const useConst = () => {
  const productNumberRef = ref<InstanceType<typeof SelectPlus>>()
  const associateStyleRef = ref<InstanceType<typeof SelectPlus>>()
  onActivated(() => {
    productNumberRef.value?.queryOptions()
    associateStyleRef.value?.queryOptions()
  })
  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '120px'
    },
    {
      formLabelLength: '200px'
    }
  ])

  return {
    productNumberRef,
    associateStyleRef,
    formLabelLength
  }
}

const { productNumberRef, associateStyleRef, formLabelLength } = useConst()

const useOperation = () => {
  const tableRef = ref<VxeTableInstance>()
  const selectedRows = ref<ProductDevProgressListApi.List>([])
  const handleSelectChange = () => {
    selectedRows.value = tableRef.value?.getCheckboxRecords() as ProductDevProgressListApi.List
  }

  const handleDetail = (row: ProductDevProgressListApi.Row) => {
    const { productNumber, launchSeason, brand } = row
    router.push({
      name: 'ColorSampleProgress',
      query: {
        brand,
        productNumber,
        launchSeason
      }
    })
  }

  return {
    tableRef,
    selectedRows,
    handleDetail,
    handleSelectChange
  }
}

const { tableRef, selectedRows, handleDetail, handleSelectChange } = useOperation()

const useQuery = () => {
  type FormModel = ProductDevProgressListApi.Request
  const formRef = ref<FormInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const formRules = ref<FormRules<FormModel>>({
    launchSeason: [{ required: true, message: '请选择开发季节', trigger: 'change' }],
    brand: [{ required: true, message: '请选择品牌', trigger: 'change' }]
  })
  const defaultFormData: FormModel & {
    date: [string, string]
  } = {
    assignedFactory: [],
    brand: [],
    chooseChannel: [],
    dataStatus: [],
    designPersonId: [],
    designerId: [],
    developStage: [],
    developmentContactPersonId: [],
    developmentType: [],
    date: ['', ''],
    launchSeason: [],
    productNumber: [],
    region: [],
    styleWms: [],
    taskNode: [],
    technicalContactPersonId: [],
    vocStyle: []
  }
  let lastFormData = ref({
    ...defaultFormData
  })
  const formData = ref({
    ...defaultFormData
  })

  const handleDetail = (row: ProductDevProgressListApi.Row) => {
    router.push({ name: 'ViewProduct', query: { id: row.id } })
  }

  const tableData = ref<ProductDevProgressListApi.List>([])
  const pager = ref<Pager>({
    current: 1,
    size: 50,
    total: 0
  })
  const queryLoading = ref(false)
  const queryParams = computed<ProductDevProgressListApi.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1],
      date: undefined
    }
  })
  const defaultTime: [Date, Date] = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ]

  let controller: AbortController | null = null
  const handleQuery = async () => {
    const valid = await formRef.value?.validate().catch(() => {})
    if (!valid) {
      return
    }
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData.value, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await getProductDevProgressList(queryParams.value, controller.signal)
    queryLoading.value = false
    selectedRows.value = []
    if (error === null && result?.datas) {
      lastFormData.value = { ...formData.value }
      const { records } = result.datas
      tableData.value = records || []
      await nextTick()
      tableRef.value?.loadData([]).then(() => {
        tableRef.value?.loadData(tableData.value)
      })
      pager.value.total = result.datas?.pager?.total || 0
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
  }

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = () => {
    formRef.value
      ?.validate((valid) => {
        if (!valid) {
          return
        }
        let reqParam: string
        if (selectedRows && selectedRows.value?.length > 0) {
          const idList = selectedRows.value.map((item) => item.id)
          reqParam = JSON.stringify({ idList })
        } else {
          reqParam = JSON.stringify(queryParams.value)
        }
        exportFn({
          exportType: 'product-develop-progress-report',
          reqParam
        })
      })
      .catch(() => {})
  }

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef
  })

  return {
    formRef,
    pagerRef,
    formRules,
    formData,
    lastFormData,
    tableData,
    pager,
    queryLoading,
    defaultTime,
    queryParams,
    handleQuery,
    handleReset,
    handleExport,
    exportLoading,
    visible,
    setVisible,
    maxHeight,
    handleDetail
  }
}

const {
  formRef,
  pagerRef,
  formRules,
  formData,
  tableData,
  pager,
  queryLoading,
  defaultTime,
  handleQuery,
  handleReset,
  handleExport,
  exportLoading,
  visible,
  setVisible,
  maxHeight
} = useQuery()

onActivated(async () => {
  await handleQuery()
  formRef.value?.clearValidate(['brand', 'launchSeason'])
})

onMounted(() => {
  watchDebounced(
    formData,
    async () => {
      await handleQuery()
    },
    { deep: true, debounce: 400 }
  )
})
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_250px)] overflow-x-hidden overflow-y-auto">
        <ElForm ref="formRef" :label-width="formLabelLength" :model="formData" :rules="formRules">
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="产品编号" prop="productNumber">
                <SelectPlus
                  ref="productNumberRef"
                  v-model="formData.productNumber"
                  api-key="getProductNumberList"
                  filterable
                  multiple
                  virtualized
                  @change="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="开发季节" prop="launchSeason">
                <SelectPlus
                  v-model="formData.launchSeason"
                  api-key="COMMON_MARKET_SEASON"
                  cache
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  multiple
                  placeholder="请选择"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="" label-width="0">
                <ElButton text @click="setVisible">
                  {{ visible ? '收起' : '展开' }}
                  <Icon
                    :class="visible ? 'rotate-90' : ''"
                    class="transform transition duration-400"
                    icon="ant-design:down-outlined"
                  />
                </ElButton>
                <ElButton :loading="queryLoading" type="primary" @click="handleQuery">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
                  查询
                </ElButton>
                <ElButton :loading="queryLoading" @click="handleReset">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
                  重置
                </ElButton>
                <ElButton :loading="exportLoading" type="primary" @click="handleExport">
                  <Icon class="mr-1" icon="ep:upload-filled" />
                  导出
                </ElButton>
              </ElFormItem>
            </ElCol>
            <ElCol :span="16">
              <ElFormItem label="品牌" prop="brand">
                <SelectPlus
                  ref="brandRef"
                  v-model="formData.brand"
                  api-key="baseBrand"
                  cache
                  checkbox
                  checkbox-button
                />
              </ElFormItem>
            </ElCol>
            <ElCollapseTransition>
              <div v-show="visible" class="flex flex-wrap w-full">
                <ElCol :span="8">
                  <ElFormItem label="区域" prop="region">
                    <SelectPlus
                      v-model="formData.region"
                      api-key="COMMON_REGION"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="供应商" prop="assignedFactory">
                    <SelectPlus
                      v-model="formData.assignedFactory"
                      api-key="getSupplierList"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="创建时间" prop="date">
                    <ElDatePicker
                      v-model="formData.date"
                      :default-time="defaultTime"
                      class="max-w-96"
                      clearable
                      end-placeholder="结束时间"
                      range-separator="~"
                      start-placeholder="开始时间"
                      type="daterange"
                      unlink-panels
                      value-format="YYYY-MM-DD"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="产品阶段" prop="developStage">
                    <SelectPlus
                      v-model="formData.developStage"
                      api-key="PRODUCT_STAGE"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="母Style" prop="vocStyle">
                    <SelectPlus
                      ref="associateStyleRef"
                      v-model="formData.vocStyle"
                      api-key="getProductNumberList"
                      filterable
                      multiple
                      virtualized
                      @change="handleQuery"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="Style（WMS）" prop="styleWms">
                    <SelectPlus
                      v-model="formData.styleWms"
                      api-key="getStyleWmsList"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                      virtualized
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="设计师" prop="designerId">
                    <CascadeSelector
                      v-model="formData.designerId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="产品企划" prop="designPersonId">
                    <CascadeSelector
                      v-model="formData.designPersonId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="开发跟进人员" prop="developmentContactPersonId">
                    <CascadeSelector
                      v-model="formData.developmentContactPersonId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="技术跟进人员" prop="technicalContactPersonId">
                    <CascadeSelector
                      v-model="formData.technicalContactPersonId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="开发类型" prop="developmentType">
                    <SelectPlus
                      v-model="formData.developmentType"
                      api-key="PRODUCT_DEV_TYPE"
                      clearable
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="任务节点" prop="taskNode">
                    <SelectPlus
                      v-model="formData.taskNode"
                      api-key="PRODUCT_TASK_NOTE"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="数据状态" prop="dataStatus">
                    <SelectPlus
                      v-model="formData.dataStatus"
                      api-key="PRODUCT_DATA_STATUS"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
              </div>
            </ElCollapseTransition>
          </ElRow>
        </ElForm>
        <div>
          <VxeTable
            ref="tableRef"
            :cellConfig="{ height: 110 }"
            :data="tableData"
            :loading="queryLoading"
            :maxHeight="maxHeight - 75"
            :minHeight="100"
            showOverflow="tooltip"
            v-bind="{ ...scrollProp }"
            @checkbox-change="handleSelectChange"
            @checkbox-all="handleSelectChange"
          >
            <VxeColgroup fixed="left" title="产品基本信息">
              <VxeColumn type="checkbox" width="40" />
              <VxeColumn title="序号" type="seq" width="60" />
              <VxeColumn
                :cell-render="{ name: 'Link', props: { clickFn: ({ row }) => handleDetail(row) } }"
                :show-overflow="false"
                field="productNumber"
                title="产品编号"
                width="120"
              />
              <VxeColumn
                :cell-render="{ name: 'Image' }"
                field="thumbnail"
                title="缩略图"
                width="80"
              />
              <VxeColumn field="brandItemName" title="品牌" width="100" />
              <VxeColumn field="categoryItemName" title="产品类目" width="100" />
              <VxeColumn field="launchSeason" title="开发季节" width="100" />
            </VxeColgroup>
            <VxeColgroup title="人员信息">
              <VxeColumn field="designerIdItemName" title="产品设计师" width="100" />
              <VxeColumn field="designPersonIdItemName" title="产品企划" width="100" />
              <VxeColumn field="regionalSupplyPersonIdItemName" title="供管" width="100" />
              <VxeColumn
                field="developmentContactPersonIdItemName"
                title="跟进开发人员"
                width="100"
              />
              <VxeColumn
                field="technicalContactPersonIdItemName"
                title="跟进技术人员"
                width="100"
              />
            </VxeColgroup>
            <VxeColgroup title="任务阶段">
              <VxeColumn
                class-name="ellipsis-cell"
                :cell-render="{ name: 'Ellipsis', props: { separator: '\r\n' } }"
                field="taskNodeStatus"
                title="任务阶段和状态"
                width="140"
              />
              <VxeColumn field="productDocumentationTime" title="商品创建时间" width="140" />
              <VxeColumn field="entryRegistrationTime" title="开款录入时间" width="140" />
              <VxeColumn field="designStyleTime" title="款式设计完成时间" width="140" />
              <VxeColumn field="sampleAllocationTime" title="打样分配完成时间" width="140" />
              <VxeColumn field="initialSampleReviewTime" title="初样完成时间" width="140" />
              <VxeColumn field="colorConfigurationTime" title="齐色样配置完成时间" width="140" />
              <VxeColumn field="selectionConclusionTime" title="选品会完成时间" width="140" />
              <VxeColumn field="confirmationSampleReviewTime" title="确认样完成时间" width="140" />
              <VxeColumn field="materialInformationTime" title="材质信息完成时间" width="140" />
              <VxeColumn
                field="materialPercentInformationTime"
                title="材质占比完成时间"
                width="140"
              />
              <VxeColumn
                field="productOperationImprovementTime"
                title="产品运营完成时间"
                width="140"
              />
            </VxeColgroup>
            <VxeColgroup title="选品会信息">
              <VxeColumn field="meetingResultItemName" title="选品会结果" width="100" />
              <VxeColumn field="selectionMeetingReviewOpinion" title="选品会评审意见" width="180" />
              <VxeColumn field="chooseChannel" title="选品渠道" width="100" />
              <VxeColumn field="mainChannelMark" title="主渠道标识" width="100" />
              <VxeColumn field="estimatedOrderVolume" title="订单量汇总" width="100" />
              <VxeColumn field="predictLaunchDate" title="预估上架日期" width="100" />
              <VxeColumn
                class-name="ellipsis-cell"
                :cell-render="{ name: 'Ellipsis' }"
                field="selectedColorItemName"
                title="选择SKC"
                width="140"
              />
              <VxeColumn field="selectedSizeItemName" title="选中尺码" width="140" />
              <VxeColumn field="copyrightItemName" title="是否需要侵权排查" width="140" />
              <VxeColumn field="copyrightInspectionItemName" title="关联侵权排查" width="100" />
              <VxeColumn field="riskValue" title="风险判定" width="100" />
              <VxeColumn field="selectionAge" title="选品时效" width="100" />
              <VxeColumn field="sendTime" title="下发WMS时间" width="140" />
            </VxeColgroup>
            <VxeColgroup title="大货准备阶段">
              <VxeColumn field="fittingSingleEndDate" title="Fitting样单只完成时间" width="160" />
              <VxeColumn field="fittingDoubleEndDate" title="Fitting样成双完成时间" width="160" />
              <VxeColumn
                field="fittingConformationDate"
                title="Fitting样鞋试穿完成时间"
                width="180"
              />
              <VxeColumn
                :cell-render="{ name: 'File', props: { filename: '样品码试穿报告' } }"
                field="fittingTrialReport"
                title="样品码试穿报告"
                width="140"
              />
              <VxeColumn
                field="handScissorsConformationDate"
                title="手剪试做报告完成时间"
                width="160"
              />
              <VxeColumn
                :cell-render="{ name: 'File', props: { filename: '手剪试做报告' } }"
                field="handScissorsTrialReport"
                title="手剪试做报告"
                width="140"
              />
              <VxeColumn
                field="choppingKnifeConformationDate"
                title="斩刀试做完成时间"
                width="140"
              />
              <VxeColumn
                :cell-render="{ name: 'File', props: { filename: '斩刀试做报告' } }"
                field="choppingKnifeTrialReport"
                title="斩刀试做报告"
                width="140"
              />
            </VxeColgroup>
          </VxeTable>
        </div>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @change="handleQuery"
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped></style>
