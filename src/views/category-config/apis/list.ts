import to from 'await-to-js'
import { service } from '@/config/axios/service'

export namespace CategoryPageAPI {
  export interface PlatformCategoryQueryResp {
    /**
     * 适用季节all-四级,summer-夏季，winter-冬季
     */
    applicableSeasonName?: string
    applicableSeason?: string
    /**
     * 平台类目
     */
    categoryAllName?: string
    /**
     * 风格
     */
    categoryStyleList?: string[]
    /**
     * 类目名称
     */
    classificationName?: string
    classificationNameList?: string
    /**
     * 生效日期
     */
    effectiveDate?: string
    /**
     * 主键ID
     */
    id?: number
    /**
     * 大类
     */
    mainCategory?: string
    mainCategoryList?: string[]
    /**
     * 操作人
     */
    modifyByName?: string
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 对应链接
     */
    platformUrl?: string
    /**
     * mmt类目
     */
    productAllCategoryName?: string
    productAllCategoryNames?: string[]
    /**
     * 站点
     */
    station?: string
    /**
     * 启用状态,start-启用,ban-禁用
     */
    status?: string
    /**
     * 人群
     */
    targetAudience?: string
    /**
     * 性别
     */
    targetSexList?: string[]
  }
  export type Row = PlatformCategoryQueryResp
  export type List = Row[]

  export interface Params {
    /**
     * 季节
     */
    applicableSeasons?: string[]
    /**
     * 风格
     */
    categoryStyles?: string[]
    /**
     * 类目名称
     */
    classificationNames?: string[]
    /**
     * 平台类目id集合
     */
    ids?: number[]
    /**
     * 大类
     */
    mainCategorys?: string[]
    /**
     * mmt类目
     */
    productAllCategoryName?: string
    productAllCategoryNames?: string[]
    /**
     * 排序信息
     */
    sortInfos?: SortRule[]
    /**
     * 站点
     */
    station?: string
    /**
     * 人群
     */
    targetAudiences?: string[]
  }

  export interface SortRule {
    /**
     * 排序字段
     */
    param?: string
    /**
     * 优先级
     */
    priority?: number
    /**
     * 排序规则
     */
    rules?: string
    [property: string]: any
  }

  export type Request = Params & PageParams
  export type Response = PagedResponseData<Row>
}

export function getCategoryListByPage(data: CategoryPageAPI.Request, signal?: AbortSignal) {
  return to<CategoryPageAPI.Response>(
    service.post('/pdm-base/platform/category/queryPlatformPage', data, { signal })
  )
}
