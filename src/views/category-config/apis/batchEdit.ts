import { CategoryPageAPI } from '@/views/category-config/apis/list'
import to from 'await-to-js'
import { service } from '@/config/fetch/service'
import { UpdateStatusAPI } from '@/views/basic-library-manage/api/common'

export namespace CategoryListByIdAPI {
  export type Request = CategoryPageAPI.Params
  export type Response = ResponseData<CategoryPageAPI.List>
}

export function getCategoryListById(data: CategoryListByIdAPI.Request) {
  return to<CategoryListByIdAPI.Response>(
    service.post('/pdm-base/platform/category/queryByIds', data)
  )
}

export namespace CategoryBatchEditAPI {
  export type Request = CategoryPageAPI.List
  export type Response = BasicResponseData
}

export function batchEditCategory(data: CategoryBatchEditAPI.Request) {
  return to<CategoryBatchEditAPI.Response>(
    service.post('/pdm-base/platform/category/updatePlatformCategorys', data)
  )
}

export function changeStatus(data: UpdateStatusAPI.Request) {
  return to<UpdateStatusAPI.Response>(service.post(`/pdm-base/platform/category/startOrBan`, data))
}
