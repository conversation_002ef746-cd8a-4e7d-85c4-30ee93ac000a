<script lang="ts" setup>
import { RefreshLeft, Search, UploadFilled } from '@element-plus/icons-vue'
import { scrollProp } from '@/plugins/vxeTable'
import { BsrRankingListApi, getBsrCategoryList } from '@/views/BsrRanking/api'
import { ref } from 'vue'
import { type CascaderProps, ElPagination, FormInstance, type FormRules } from 'element-plus'
import {
  getWMSCategoryList,
  Pager,
  WMSCategoryListAPI
} from '@/views/basic-library-manage/api/common'
import { isEqual } from 'lodash-es'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { VxeTableDefines, VxeTableInstance } from 'vxe-table'
import { Icon } from '@/components/Icon'
import { CategoryPageAPI, getCategoryListByPage } from './apis/list'
import AddDialog from './components/AddDialog.vue'
import BatchEditDialog from './components/BatchEditDialog.vue'
import EditStatusDialog from './components/EditStatusDialog.vue'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { trackDialogEvent, trackSiteSearch } from '@/utils/monitor'

defineOptions({
  name: 'CategoryConfig'
})

const useConst = () => {
  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '120'
    },
    {
      formLabelLength: '200'
    }
  ])

  const cascaderProps: CascaderProps = {
    label: 'selectorEnValue',
    value: 'selectorKey',
    children: 'childList',
    expandTrigger: 'hover' as const,
    emitPath: false,
    multiple: true
  }

  return {
    formLabelLength,
    cascaderProps
  }
}

const { formLabelLength, cascaderProps } = useConst()

const useOperation = () => {
  const tableRef = ref<VxeTableInstance>()
  const selectedRows = ref<CategoryPageAPI.List>([])
  const handleSelectChange = () => {
    selectedRows.value = tableRef.value?.getCheckboxRecords() as CategoryPageAPI.List
  }

  const addDialogVisible = ref(false)
  const editDialogVisible = ref(false)
  const statusDialogVisible = ref(false)

  const handleCreate = () => {
    trackDialogEvent('add', '新增类目')
    addDialogVisible.value = true
  }

  const handleEdit = () => {
    trackDialogEvent('edit', '编辑类目')
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请至少选择1行数据')
      return
    }
    editDialogVisible.value = true
  }

  const handleChangeStatus = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请至少选择1行数据')
      return
    }
    statusDialogVisible.value = true
  }

  return {
    tableRef,
    selectedRows,
    handleCreate,
    addDialogVisible,
    editDialogVisible,
    handleEdit,
    statusDialogVisible,
    handleChangeStatus,
    handleSelectChange
  }
}

const {
  tableRef,
  selectedRows,
  editDialogVisible,
  addDialogVisible,
  handleSelectChange,
  handleCreate,
  handleEdit,
  statusDialogVisible,
  handleChangeStatus
} = useOperation()

const useQuery = () => {
  type FormModel = CategoryPageAPI.Params & {
    productCategoryName?: string[]
  }
  const formRef = ref<FormInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const formRules = ref<FormRules<FormModel>>({})
  const defaultFormData: FormModel = {}
  const lastFormData = ref({
    ...defaultFormData
  })
  const formData = ref({
    ...defaultFormData
  })

  const tableData = ref<CategoryPageAPI.List>([])
  const pager = ref<Pager>({
    current: 1,
    size: 100,
    total: 0
  })
  const queryLoading = ref(false)
  const queryParams = computed<CategoryPageAPI.Request>(() => {
    return {
      productCategoryName: undefined,
      productAllCategoryName: formData.value.productCategoryName?.join('/'),
      ...formData.value,
      ...pager.value
    }
  })

  let controller: AbortController | null = null
  const handleQuery = async (sortInfos?: CategoryPageAPI.SortRule[]) => {
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery(sortInfos)
      })
      return
    }
    trackSiteSearch('search', '类目配置清单搜索')
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData.value, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await getCategoryListByPage(
      {
        ...queryParams.value
      },
      controller.signal
    )
    queryLoading.value = false
    selectedRows.value = []
    if (error === null && result?.datas) {
      lastFormData.value = { ...formData.value }
      const { records } = result.datas
      tableData.value = records || []
      await nextTick()
      await tableRef.value?.loadData([])
      tableRef.value?.loadData(tableData.value)
      pager.value.total = result.datas?.pager?.total || 0
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
  }

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = () => {
    formRef.value
      ?.validate((valid) => {
        if (!valid) {
          return
        }
        let reqParam: string
        if (selectedRows.value?.length > 0) {
          const ids = selectedRows.value.map((item) => item.id)
          reqParam = JSON.stringify({ ids, ...pager.value })
        } else {
          reqParam = JSON.stringify({
            ...queryParams.value
          })
        }
        exportFn({
          exportType: 'platformCategory-export',
          reqParam
        })
      })
      .catch(() => {})
  }

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef
  })

  return {
    formRef,
    pagerRef,
    formRules,
    formData,
    lastFormData,
    tableData,
    pager,
    queryLoading,
    queryParams,
    handleQuery,
    handleReset,
    visible,
    setVisible,
    maxHeight,
    exportLoading,
    handleExport
  }
}

const {
  formRef,
  pagerRef,
  formRules,
  formData,
  tableData,
  pager,
  queryLoading,
  handleQuery,
  handleReset,
  maxHeight,
  exportLoading,
  handleExport
} = useQuery()

const amazonCategoryTree = ref<BsrRankingListApi.Response[]>([])
async function queryAmazonCategory() {
  if (!formData.value.station) return
  const [err, data] = await getBsrCategoryList(formData.value.station)
  if (err) return
  if (!Array.isArray(data.datas)) {
    amazonCategoryTree.value = []
    return
  }
  amazonCategoryTree.value = data?.datas
}

const wmsCategoryList = ref<WMSCategoryListAPI.Data[]>([])
const fetchCategoryList = async () => {
  const [error, result] = await getWMSCategoryList()
  if (error === null && result?.datas) {
    wmsCategoryList.value = result.datas
  }
}

Promise.all([fetchCategoryList(), handleQuery()])

async function handleSortChange(params: VxeTableDefines.SortChangeParams) {
  pager.value.current = 1
  const sortInfos = params.sortList.map((item, index) => {
    return {
      param: item.field,
      rules: item.order,
      priority: index
    }
  }) as CategoryPageAPI.SortRule[]
  await handleQuery(sortInfos)
}
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_250px)] overflow-x-hidden overflow-y-auto">
        <ElForm
          ref="formRef"
          :label-width="formLabelLength"
          :model="formData"
          :rules="formRules"
          @submit="
            (e) => {
              e.preventDefault()
            }
          "
        >
          <div class="grid grid-cols-3 items-start gap-x-4">
            <div class="col-span-2 grid grid-cols-2 items-start gap-x-4">
              <ElFormItem label="站点" prop="station">
                <SelectPlus
                  v-model="formData.station"
                  :configuration="{
                    value: 'dictValue',
                    label: 'dictEnName'
                  }"
                  :parent-scroll="false"
                  api-key="station"
                  class="!w-full"
                  filterable
                  virtualized
                  @change="queryAmazonCategory"
                />
              </ElFormItem>
              <ElFormItem label="MMT类目" prop="productCategoryName">
                <ElCascader
                  v-model="formData.productCategoryName"
                  :options="wmsCategoryList"
                  :props="{
                    children: 'sonCategory',
                    label: 'categoryName',
                    value: 'categoryName',
                    emitPath: true,
                    multiple: false
                  }"
                  class="!w-full overflow-auto"
                  clearable
                  filterable
                  placeholder="请选择商品三级分类名称"
                  separator="/"
                />
              </ElFormItem>
              <ElFormItem label="平台类目" prop="ids">
                <ElCascader
                  v-model="formData.ids"
                  :max-collapse-tags="1"
                  :options="amazonCategoryTree"
                  :props="cascaderProps"
                  class="!w-full"
                  clearable
                  collapse-tags
                  filterable
                  separator="-"
                />
              </ElFormItem>
              <ElFormItem label="人群" prop="targetAudiences">
                <SelectPlus
                  v-model="formData.targetAudiences"
                  api-key="PLATFORM_PRODUCT_PEOPLE"
                  cache
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  multiple
                  placeholder="请选择"
                />
              </ElFormItem>
              <ElFormItem label="风格" prop="categoryStyles">
                <SelectPlus
                  v-model="formData.categoryStyles"
                  api-key="PLATFORM_PRODUCT_STYLE"
                  cache
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  multiple
                  placeholder="请选择"
                />
              </ElFormItem>
              <ElFormItem label="大类" prop="mainCategorys">
                <SelectPlus
                  v-model="formData.mainCategorys"
                  api-key="PLATFORM_MAIN_CATEGORY"
                  cache
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  multiple
                  placeholder="请选择"
                />
              </ElFormItem>
              <ElFormItem label="类目名称" prop="classificationNames">
                <SelectPlus
                  v-model="formData.classificationNames"
                  api-key="CLASSIFICATION_NAME"
                  cache
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  multiple
                  placeholder="请选择"
                />
              </ElFormItem>
            </div>

            <div>
              <ElFormItem class="form-item-no-wrap" label="" label-width="0">
                <ElButton
                  :icon="Search"
                  :loading="queryLoading"
                  class="w-16"
                  native-type="submit"
                  type="primary"
                  @click="handleQuery()"
                >
                  查询
                </ElButton>
                <ElButton
                  :icon="RefreshLeft"
                  :loading="queryLoading"
                  class="w-16"
                  native-type="reset"
                  @click="handleReset"
                >
                  重置
                </ElButton>
                <ElButton
                  :icon="UploadFilled"
                  :loading="exportLoading"
                  class="w-16"
                  type="primary"
                  @click="handleExport"
                >
                  导出
                </ElButton>
              </ElFormItem>
            </div>
          </div>
        </ElForm>
        <div class="mb-[10px] min-h-8">
          <ElButton v-hasPermi="['createCategoryConfig']" type="primary" @click="handleCreate">
            <Icon icon="ep:plus" />
            <span class="text-[14px]">新增类目</span>
          </ElButton>
          <ElButton v-hasPermi="['editCategoryConfig']" type="primary" @click="handleEdit">
            <Icon icon="ep:edit" />
            <span class="text-[14px]">批量修改</span>
          </ElButton>
          <ElButton
            v-hasPermi="['categoryConfig:changeStatus']"
            type="primary"
            @click="handleChangeStatus"
          >
            <Icon icon="ep:share" />
            <span class="text-[14px]">启用/禁用</span>
          </ElButton>
        </div>
        <VxeTable
          ref="tableRef"
          :data="tableData"
          :loading="queryLoading"
          :maxHeight="maxHeight - 100"
          :minHeight="100"
          :sort-config="{
            multiple: true
          }"
          showOverflow="tooltip"
          v-bind="{ ...scrollProp }"
          @sort-change="handleSortChange"
          @checkbox-change="handleSelectChange"
          @checkbox-all="handleSelectChange"
        >
          <VxeColumn type="checkbox" width="40" />
          <VxeColumn title="序号" type="seq" width="60" />
          <VxeColumn field="station" sortable title="站点" width="120" />
          <VxeColumn field="categoryAllName" title="平台类目" width="120" />
          <VxeColumn field="productAllCategoryName" title="MMT类目" width="120" />
          <VxeColumn field="platformUrl" title="对应链接" width="100">
            <template #default="{ row }: { row: CategoryPageAPI.Row }">
              <ElLink :href="row.platformUrl" target="_blank" type="primary">
                {{ row.platformUrl }}
              </ElLink>
            </template>
          </VxeColumn>
          <VxeColumn field="targetAudience" sortable title="人群" width="100" />
          <VxeColumn field="targetSex" title="性别" width="100" />
          <VxeColumn field="categoryStyle" sortable title="风格" width="100" />
          <VxeColumn field="mainCategory" sortable title="大类" width="125" />
          <VxeColumn field="classificationName" sortable title="类目名称" width="100" />
          <VxeColumn field="applicableSeasonName" title="季节" width="100" />
          <VxeColumn field="statusName" title="状态" width="100" />
          <VxeColumn field="effectiveDate" sortable title="生效日期" width="150" />
          <VxeColumn field="modifyByName" title="操作人" width="100" />
          <VxeColumn field="modifyTime" title="操作时间" width="160" />
        </VxeTable>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @click="handleQuery()"
      />
      <AddDialog
        v-model="addDialogVisible"
        :wms-category-list="wmsCategoryList"
        @refresh="handleQuery"
      />
      <BatchEditDialog
        v-model="editDialogVisible"
        :selected-rows="selectedRows"
        :wms-category-list="wmsCategoryList"
        @refresh="handleQuery"
      />
      <EditStatusDialog
        v-model="statusDialogVisible"
        :selected-rows="selectedRows"
        @refresh="handleQuery"
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped></style>
