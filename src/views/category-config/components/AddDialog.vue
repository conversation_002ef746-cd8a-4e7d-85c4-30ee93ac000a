<script lang="ts" setup>
import { noop, omit } from 'lodash-es'
import { CascaderInstance, FormInstance, FormRules } from 'element-plus'
import { WMSCategoryListAPI } from '@/views/basic-library-manage/api/common'
import { StatusEnum } from '@/views/basic-library-manage/const'
import { addCategory, AddCategoryAPI } from '@/views/category-config/apis/add'

defineOptions({
  name: 'AddDialog'
})

const props = defineProps<{
  modelValue: boolean
  wmsCategoryList: WMSCategoryListAPI.Data[]
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const title = computed(() => {
  return '新增平台类目'
})

const productCategoryRef = ref<CascaderInstance>()
const formRef = ref<FormInstance>()
const formData = ref<AddCategoryAPI.Request & { productCategoryName?: string[][] }>({
  platformType: 'amazon',
  status: StatusEnum.START
})
const rules = computed<FormRules<typeof formData>>(() => ({
  station: [{ required: true, message: '请选择站点', trigger: 'change' }],
  categoryAllName: [{ required: true, message: '请选择平台类目', trigger: 'change' }],
  targetAudience: [{ required: true, message: '请选择人群', trigger: 'change' }],
  categoryStyleList: [{ required: true, message: '请选择风格', trigger: 'change' }],
  targetSexList: [{ required: true, message: '请选择性别', trigger: 'change' }],
  mainCategoryList: [{ required: true, message: '请选择大类', trigger: 'change' }],
  classificationNameList: [{ required: true, message: '请选择类目名称', trigger: 'change' }],
  applicableSeason: [{ required: true, message: '请选择季节', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  platformUrl: [{ required: true, message: '请输入对应链接', trigger: 'change' }]
}))

const handleClose = () => {
  formData.value = {
    platformType: 'amazon',
    status: StatusEnum.START
  }
  visible.value = false
}
const submitLoading = ref(false)
const handleSubmit = async () => {
  const valid = await formRef.value?.validate().catch(noop)
  if (!valid) return
  submitLoading.value = true
  const nodes = productCategoryRef.value?.getCheckedNodes(true)
  const [error, result] = await addCategory({
    ...omit(formData.value, ['productCategoryName']),
    productAllCategoryNames: nodes?.map((e) => e.pathLabels.join('/'))
  })
  submitLoading.value = false
  if (!error && result?.msg) {
    ElMessage.success(result.msg)
    emit('refresh')
    handleClose()
  }
}
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    width="90%"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      class="mx-4"
      label-width="auto"
      scroll-to-error
    >
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="站点" prop="station">
            <SelectPlus
              v-model="formData.station"
              :configuration="{
                value: 'dictValue',
                label: 'dictEnName'
              }"
              api-key="station"
              class="!w-full"
              filterable
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="平台类目" prop="categoryAllName">
            <ElInput
              v-model="formData.categoryAllName"
              clearable
              maxlength="200"
              placeholder="请输入完整的亚马逊类目"
              show-word-limit
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="MMT类目" prop="productCategoryName">
            <ElCascader
              ref="productCategoryRef"
              v-model="formData.productCategoryName"
              :options="wmsCategoryList"
              :props="{
                children: 'sonCategory',
                label: 'categoryName',
                value: 'categoryCode',
                emitPath: true,
                multiple: true
              }"
              class="!w-full overflow-auto"
              clearable
              filterable
              placeholder="请选择"
              separator="/"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="人群" prop="targetAudience">
            <SelectPlus
              v-model="formData.targetAudience"
              :parent-scroll="false"
              api-key="PRODUCT_PEOPLE"
              class="!w-full"
              filterable
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="风格" prop="categoryStyleList">
            <SelectPlus
              v-model="formData.categoryStyleList"
              :parent-scroll="false"
              api-key="PLATFORM_PRODUCT_STYLE"
              class="!w-full"
              filterable
              multiple
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="性别" prop="targetSexList">
            <SelectPlus
              v-model="formData.targetSexList"
              :parent-scroll="false"
              api-key="PLATFORM_CATEGORY_SEX"
              class="!w-full"
              filterable
              multiple
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="大类" prop="mainCategoryList">
            <SelectPlus
              v-model="formData.mainCategoryList"
              :parent-scroll="false"
              api-key="PLATFORM_MAIN_CATEGORY"
              class="!w-full"
              filterable
              multiple
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="类目名称" prop="classificationNameList">
            <SelectPlus
              v-model="formData.classificationNameList"
              :parent-scroll="false"
              api-key="CLASSIFICATION_NAME"
              class="!w-full"
              filterable
              multiple
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="季节" prop="applicableSeason">
            <SelectPlus
              v-model="formData.applicableSeason"
              :parent-scroll="false"
              api-key="PLATFORM_CATEGORY_SEASON"
              class="!w-full"
              filterable
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="状态" prop="status">
            <ElRadioGroup v-model="formData.status">
              <ElRadio :value="StatusEnum.START" label="启用" />
              <ElRadio :value="StatusEnum.BAN" label="禁用" />
            </ElRadioGroup>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="对应链接" prop="platformUrl">
            <ElInput
              v-model="formData.platformUrl"
              :autosize="{
                minRows: 2,
                maxRows: 4
              }"
              maxlength="500"
              show-word-limit
              type="textarea"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">关闭</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">保存</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
