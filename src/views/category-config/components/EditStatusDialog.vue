<script lang="ts" setup>
import { CategoryPageAPI } from '../apis/list'
import { changeStatus } from '../apis/batchEdit'
import { ElMessage } from 'element-plus'
import { UpdateStatusAPI } from '@/views/basic-library-manage/api/common'
import { StatusEnum } from '@/views/basic-library-manage/const'
import { scrollProp } from '@/plugins/vxeTable'

defineOptions({
  name: 'StatusDialog'
})

const props = defineProps<{
  modelValue: boolean
  selectedRows: CategoryPageAPI.List
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

type TableRow = Partial<CategoryPageAPI.Row & UpdateStatusAPI.StatusData & { updateStatus: string }>

const tableData = ref<TableRow[]>([])

watch(visible, (val) => {
  if (val) {
    tableData.value = props.selectedRows.slice()
  }
})

const handleClose = () => {
  visible.value = false
  tableData.value = []
}

const submitLoading = ref(false)
const handleSubmit = async () => {
  const hasError = tableData.value.some((item) => {
    return item.newStatus === undefined || item.newStatus === ''
  })
  if (hasError) {
    ElMessage.warning('请选择状态')
    return
  }
  submitLoading.value = true
  const [error, result] = await changeStatus({
    updateStatusReqList: tableData.value.map((e) => ({
      id: e.id!,
      oldStatus: e.status!,
      newStatus: e.newStatus!
    }))
  })
  submitLoading.value = false
  if (error === null && result) {
    ElMessage.success(result.msg || '操作成功')
    handleClose()
    emit('refresh')
  }
}
</script>

<template>
  <Dialog v-model="visible" :before-close="handleClose" title="操作确认">
    <VxeTable :data="tableData" :max-height="800" v-bind="{ ...scrollProp }">
      <VxeColumn field="station" title="站点" />
      <VxeColumn field="categoryAllName" title="平台类目" />
      <VxeColumn field="productAllCategoryName" title="MMT类目" />
      <VxeColumn field="statusName" title="当前状态" />
      <VxeColumn title="修改状态">
        <template #default="{ row }: { row: CategoryPageAPI.Row }">
          <ElSelect v-model="(row as TableRow).newStatus">
            <ElOption v-if="row.status === StatusEnum.BAN" :value="StatusEnum.START" label="启用" />
            <ElOption v-if="row.status === StatusEnum.START" :value="StatusEnum.BAN" label="禁用" />
          </ElSelect>
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确认</ElButton>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
