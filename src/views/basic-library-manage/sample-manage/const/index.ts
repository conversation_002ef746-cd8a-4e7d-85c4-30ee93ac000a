import { StatusEnum } from '@/views/basic-library-manage/const'
export enum CommandEnums {
  /**
   * 查看详情
   */
  DETAIL,
  /**
   * 编辑
   */
  EDIT,
  /**
   * 提交打样
   */
  SUBMIT,
  /**
   * 复制
   */
  COPY,
  /**
   * 删除
   */
  DELETE,
  /**
   * 评审
   */
  REVIEW,
  /**
   * 作废
   */
  INVALID,
  /**
   * 撤回
   */
  REVOKE
}

export const CommandPermission: Record<string, string[]> = {
  [CommandEnums.DETAIL]: [
    StatusEnum.DRAFT.toUpperCase(),
    StatusEnum.AGAIN.toUpperCase(),
    StatusEnum.ASSIGN.toUpperCase(),
    StatusEnum.REVIEW.toUpperCase(),
    StatusEnum.PROOF.toUpperCase(),
    StatusEnum.DEV_REVIEW.toUpperCase(),
    StatusEnum.DESIGN_REVIEW.toUpperCase(),
    StatusEnum.FINISH.toUpperCase(),
    StatusEnum.CANCEL.toUpperCase()
  ],
  [CommandEnums.EDIT]: [StatusEnum.DRAFT.toUpperCase(), StatusEnum.AGAIN.toUpperCase()],
  [CommandEnums.SUBMIT]: [StatusEnum.DRAFT.toUpperCase(), StatusEnum.AGAIN.toUpperCase()],
  [CommandEnums.COPY]: [
    StatusEnum.DRAFT.toUpperCase(),
    StatusEnum.AGAIN.toUpperCase(),
    StatusEnum.ASSIGN.toUpperCase(),
    StatusEnum.REVIEW.toUpperCase(),
    StatusEnum.PROOF.toUpperCase(),
    StatusEnum.DEV_REVIEW.toUpperCase(),
    StatusEnum.DESIGN_REVIEW.toUpperCase(),
    StatusEnum.FINISH.toUpperCase(),
    StatusEnum.CANCEL.toUpperCase()
  ],
  [CommandEnums.DELETE]: [StatusEnum.DRAFT.toUpperCase()],
  [CommandEnums.REVIEW]: [
    StatusEnum.AGAIN.toUpperCase(),
    StatusEnum.DEV_REVIEW.toUpperCase(),
    StatusEnum.DESIGN_REVIEW.toUpperCase(),
    StatusEnum.FINISH.toUpperCase(),
    StatusEnum.CANCEL.toUpperCase()
  ],
  [CommandEnums.INVALID]: [StatusEnum.AGAIN.toUpperCase()],
  [CommandEnums.REVOKE]: [StatusEnum.ASSIGN.toUpperCase(), StatusEnum.PROOF.toUpperCase()]
}

export enum SampleEnums {
  /**
   * 初样
   */
  INIT = 'initial_proof',
  /**
   * 齐色样
   */
  COLOR = 'color_proof',
  /**
   * 确认样
   */
  CONFIRM = 'confirm_proof'
}

export const SampleMap = {
  [SampleEnums.INIT]: '初样打样',
  [SampleEnums.COLOR]: '齐色样打样',
  [SampleEnums.CONFIRM]: '确认样打样'
}

export const sampleStatusMap = {
  [StatusEnum.DRAFT.toUpperCase()]: '草稿',
  [StatusEnum.ASSIGN.toUpperCase()]: '分配中',
  [StatusEnum.PROOF.toUpperCase()]: '打样中',
  [StatusEnum.DEV_REVIEW.toUpperCase()]: '开发师傅评审',
  [StatusEnum.DESIGN_REVIEW.toUpperCase()]: '设计师评审',
  [StatusEnum.FINISH.toUpperCase()]: '已完成',
  [StatusEnum.AGAIN.toUpperCase()]: '重新工作',
  [StatusEnum.CANCEL.toUpperCase()]: '已取消'
}
