<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { FormConfig } from '@/views/basic-library-manage/sample-manage/helper'
import DeepSearch from '@/views/basic-library-manage/sample-manage/components/DeepSearch.vue'
import { ref } from 'vue'
import {
  getSampleList,
  SampleListPageAPI
} from '@/views/basic-library-manage/sample-manage/api/SampleList'
import { Pager } from '@/views/basic-library-manage/api/common'
import type { VxeGridProps } from 'vxe-table'
import { ElPagination } from 'element-plus'
import { Icon } from '@/components/Icon'
import { SampleMap, sampleStatusMap } from '@/views/basic-library-manage/sample-manage/const'
import { isEqual } from 'lodash-es'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import CommonSample from '@/views/basic-library-manage/sample-manage/components/CommonSample.vue'
import { commonYesNoMap } from '@/enums'
import { watchDebounced } from '@vueuse/core'

defineOptions({
  name: 'SampleManageList'
})
const { query } = useRoute()
const columns = ref([
  {
    type: 'checkbox',
    fixed: 'left',
    width: 40
  },
  {
    type: 'seq',
    title: '序号',
    width: 60,
    fixed: 'left'
  },
  {
    fixed: 'left',
    field: 'code',
    width: 150,
    title: '打样单编号',
    slots: {
      default: 'code'
    }
  },
  {
    title: '缩略图',
    width: 120,
    field: 'thumbnail',
    cellRender: {
      name: 'Image'
    }
  },
  {
    title: '打样单类型',
    field: 'type',
    minWidth: 120,
    formatter: ({ cellValue }) => {
      return SampleMap[cellValue]
    }
  },
  {
    title: '结构化',
    width: 80,
    field: 'structured',
    formatter: ({ cellValue }) => {
      return commonYesNoMap[cellValue]
    }
  },
  {
    title: '打样单名称',
    field: 'name',
    width: 100
  },
  {
    title: '创建者',
    width: 90,
    field: 'createByIdItemName'
  },
  {
    title: '创建时间',
    width: 140,
    field: 'createTime'
  },
  {
    title: '供应商',
    width: 100,
    field: 'assignedFactoryItemName'
  },
  {
    title: '打样单',
    minWidth: 120,
    field: 'proofingSheet',
    slots: {
      default: 'proofingSheet'
    }
  },
  {
    title: '说明',
    width: 100,
    field: 'remark'
  },
  {
    title: '状态',
    width: 120,
    field: 'status',
    formatter: ({ cellValue }) => {
      return sampleStatusMap[cellValue]
    }
  },
  {
    title: '操作',
    fixed: 'right',
    width: 150,
    slots: {
      default: 'action'
    }
  }
])

const pager = ref<Pager>({
  current: 1,
  size: 10,
  total: 0
})
const queryLoading = ref<boolean>(false)
const defaultFormData: SampleListPageAPI.Params = {
  mine: false,
  productNumber: [],
  proofCode: [],
  brand: [],
  styleNumber: '',
  launchSeason: [],
  styleWms: [],
  factoryId: [],
  region: [],
  developmentContactPersonId: [],
  technicalContactPersonId: [],
  designPersonId: [],
  designerId: [],
  proofType: []
}
const tableData = ref<SampleListPageAPI.Row[]>()
const pagerRef = ref<InstanceType<typeof ElPagination>>()
const commonSample = ref()
let lastFormData = {
  ...defaultFormData
}
const formData = ref<SampleListPageAPI.Params>({
  ...defaultFormData,
  proofCode: query.code ? [query.code] : []
})
const tableOptions = computed<VxeGridProps<SampleListPageAPI.Row>>(() => ({
  minHeight: 100,
  columns: columns.value,
  data: tableData.value,
  showOverflow: 'tooltip',
  scrollY: {
    enabled: true,
    gt: 20
  },
  scrollX: {
    enabled: true,
    gt: 20
  },
  loading: queryLoading.value,
  cellConfig: {
    height: 110
  },
  pager: pagerRef
}))
let controller: AbortController | null = null
const queryParams = computed<SampleListPageAPI.Request>(() => {
  return {
    ...formData.value,
    ...pager.value
  }
})

//搜索
const handleQuery = async () => {
  if (controller) {
    controller.abort()
    controller = null
    setTimeout(() => {
      handleQuery()
    })
    return
  }
  queryLoading.value = true
  controller = new AbortController()
  if (!isEqual(lastFormData, formData.value)) {
    pager.value.current = 1
  }
  const [error, result] = await getSampleList(queryParams.value, controller.signal)
  queryLoading.value = false
  if (error === null && result?.datas) {
    lastFormData = { ...formData.value }
    const { records } = result.datas
    tableData.value = records
    pager.value.total = result.datas.pager.total
  }
}

//导出
const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
const handleExport = () => {
  const selected: SampleListPageAPI.List | undefined =
    commonSample.value?.tableRef?.getCheckboxRecords()
  let reqParam: string
  if (selected && selected.length > 0) {
    reqParam = JSON.stringify({ idList: selected.map((item) => item.id) })
  } else {
    reqParam = JSON.stringify(queryParams.value)
  }
  exportFn({
    exportType: 'proof-page-export',
    reqParam
  })
}
const handleReset = () => {
  formData.value = {
    ...defaultFormData
  }
  handleQuery()
}
onBeforeMount(() => {
  handleQuery()
  watchDebounced(
    formData,
    async () => {
      await handleQuery()
    },
    { deep: true, debounce: 400 }
  )
})
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_250px)] overflow-x-hidden overflow-y-auto">
        <deep-search
          :form-config="FormConfig"
          :queryLoading="queryLoading"
          :form-data="formData"
          @handle-query="handleQuery"
          @handle-reset="handleReset"
        >
          <template #button>
            <ElButton type="primary" :loading="exportLoading" @click="handleExport">
              <Icon class="mr-1" icon="ep:upload-filled" />
              导出
            </ElButton>
          </template>
        </deep-search>
        <div>
          <CommonSample
            ref="commonSample"
            :tableOptions="tableOptions"
            @refresh="
              () => {
                pager.current = 1
                handleQuery()
              }
            "
            :tableData="tableData"
          />
        </div>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
    </div>
  </ContentWrap>
</template>

<style scoped lang="less"></style>
