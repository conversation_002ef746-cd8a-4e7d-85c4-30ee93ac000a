<script lang="ts" setup>
import { ElPagination, type FormInstance, type FormRules } from 'element-plus'
import { Icon } from '@/components/Icon'
import { getProductListByPage, ProductListPageAPI } from '../product-library/api/product-list'
import { ref } from 'vue'
import type { VxeTableInstance } from 'vxe-table'
import { getProductCategoryList, Pager, ProductCategoryListAPI } from '../api/common'
import dayjs from 'dayjs'
import { isEqual } from 'lodash-es'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { SelectPlus } from '@/components/Business/SelectPlus'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { scrollProp } from '@/plugins/vxeTable'
import { watchDebounced } from '@vueuse/core'

defineOptions({
  name: 'SampleList'
})

const router = useRouter()
const { query } = useRoute()

const useConst = () => {
  const productCategoryList = ref<ProductCategoryListAPI.Data[]>([])
  const fetchProductCategoryList = async () => {
    const [error, result] = await getProductCategoryList()
    if (error === null && result?.datas) {
      productCategoryList.value = result?.datas
    }
  }
  fetchProductCategoryList()

  const productNumberRef = ref<InstanceType<typeof SelectPlus>>()
  onActivated(() => {
    productNumberRef.value?.queryOptions()
  })

  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '120'
    },
    {
      formLabelLength: '180'
    }
  ])
  return {
    productNumberRef,
    productCategoryList,
    formLabelLength
  }
}

const { productNumberRef, productCategoryList, formLabelLength } = useConst()

const useOperation = () => {
  const tableRef = ref<VxeTableInstance>()

  const selectedRows = ref<ProductListPageAPI.List>([])
  const handleSelectChange = () => {
    selectedRows.value = tableRef.value?.getCheckboxRecords() as ProductListPageAPI.List
  }

  const handleDetail = (row: ProductListPageAPI.Row) => {
    router.push({
      name: 'SampleInfo',
      query: {
        id: row.id
      }
    })
  }

  return {
    tableRef,
    selectedRows,
    handleDetail,
    handleSelectChange
  }
}

const { tableRef, selectedRows, handleDetail, handleSelectChange } = useOperation()

const useQuery = () => {
  type FormModel = ProductListPageAPI.Params
  const formRef = ref<FormInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const formRules = ref<FormRules<FormModel>>({})
  const defaultFormData: FormModel & {
    date: [string, string]
  } = {
    emptySupplier: undefined,
    copyrightInspection: undefined,
    brand: [],
    productNumber: [],
    launchSeason: [],
    targetAudience: [],
    designPersonId: [],
    date: ['', ''],
    designerId: [],
    region: [],
    productCategory: [],
    technicalContactPersonId: [],
    developmentContactPersonId: [],
    toeStandard: [],
    toeShape: [],
    dataStatus: []
  }
  let lastFormData = ref({
    ...defaultFormData
  })
  const formData = ref({
    ...defaultFormData
  })

  const handleDetail = (row: ProductListPageAPI.Row) => {
    router.push({ name: 'ViewProduct', query: { id: row.id } })
  }

  const tableData = ref<ProductListPageAPI.List>([])
  const pager = ref<Pager>({
    current: 1,
    size: 50,
    total: 0
  })
  const queryLoading = ref(false)
  const queryParams = computed<ProductListPageAPI.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1],
      date: undefined
    }
  })
  const defaultTime: [Date, Date] = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ]

  let controller: AbortController | null = null
  const handleQuery = async () => {
    const valid = await formRef.value?.validate().catch(() => {})
    if (!valid) {
      return
    }
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData.value, formData.value)) {
      pager.value.current = 1
    }
    const result = await getProductListByPage(queryParams.value, controller.signal)
    queryLoading.value = false
    selectedRows.value = []
    if (result?.datas) {
      lastFormData.value = { ...formData.value }
      const { records } = result.datas
      tableData.value = records || []
      await nextTick()
      tableRef.value?.loadData([]).then(() => {
        tableRef.value?.loadData(tableData.value)
      })
      pager.value.total = result.datas?.pager?.total || 0
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
  }

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = () => {
    formRef.value
      ?.validate((valid) => {
        if (!valid) {
          return
        }
        let reqParam: string
        if (selectedRows && selectedRows.value?.length > 0) {
          const idList = selectedRows.value.map((item) => item.id)
          reqParam = JSON.stringify({ idList })
        } else {
          reqParam = JSON.stringify(queryParams.value)
        }
        exportFn({
          exportType: 'proof-export',
          reqParam
        })
      })
      .catch(() => {})
  }

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef
  })

  return {
    formRef,
    pagerRef,
    formRules,
    formData,
    lastFormData,
    tableData,
    pager,
    queryLoading,
    defaultTime,
    queryParams,
    handleQuery,
    handleReset,
    handleExport,
    exportLoading,
    visible,
    setVisible,
    maxHeight,
    handleDetail
  }
}

const {
  formRef,
  pagerRef,
  formRules,
  formData,
  tableData,
  pager,
  queryLoading,
  defaultTime,
  handleQuery,
  handleReset,
  handleExport,
  exportLoading,
  visible,
  setVisible,
  maxHeight
} = useQuery()

const queryByRouter = () => {
  handleQuery()
  const { productNumber } = query
  if (productNumber && typeof productNumber === 'string') {
    formData.value.productNumber = productNumber.split(',')
    handleQuery()
  }
}
// 打样清单跳转
const goTolist = () => {
  router.push({
    name: 'SampleManageList'
  })
}

onMounted(() => {
  watchDebounced(
    formData,
    async () => {
      await handleQuery()
    },
    { deep: true, debounce: 400 }
  )
})
onMounted(queryByRouter)
onActivated(queryByRouter)
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_250px)] overflow-x-hidden overflow-y-auto">
        <ElForm ref="formRef" :label-width="formLabelLength" :model="formData" :rules="formRules">
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="产品编号" prop="productNumber">
                <SelectPlus
                  ref="productNumberRef"
                  v-model="formData.productNumber"
                  api-key="getProductNumberList"
                  filterable
                  multiple
                  virtualized
                  @change="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="开发季节" prop="launchSeason">
                <SelectPlus
                  v-model="formData.launchSeason"
                  api-key="COMMON_MARKET_SEASON"
                  cache
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  multiple
                  placeholder="请选择"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="" label-width="0">
                <ElButton text @click="setVisible">
                  {{ visible ? '收起' : '展开' }}
                  <Icon
                    :class="visible ? 'rotate-90' : ''"
                    class="transform transition duration-400"
                    icon="ant-design:down-outlined"
                  />
                </ElButton>
                <ElButton :loading="queryLoading" type="primary" @click="handleQuery">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
                  查询
                </ElButton>
                <ElButton :loading="queryLoading" @click="handleReset">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
                  重置
                </ElButton>
                <ElButton :loading="exportLoading" type="primary" @click="handleExport">
                  <Icon class="mr-1" icon="ep:upload-filled" />
                  导出
                </ElButton>
              </ElFormItem>
            </ElCol>
            <ElCol :span="16">
              <ElFormItem label="品牌" prop="brand">
                <SelectPlus
                  v-model="formData.brand"
                  api-key="baseBrand"
                  cache
                  checkbox
                  checkbox-button
                />
              </ElFormItem>
            </ElCol>
            <ElCollapseTransition>
              <div v-show="visible" class="flex flex-wrap w-full">
                <ElCol :span="8">
                  <ElFormItem label="楦头类别" prop="brand">
                    <ElScrollbar>
                      <SelectPlus
                        v-model="formData.toeStandard"
                        api-key="LAST_TYPE"
                        cache
                        checkbox
                        checkbox-button
                        class="flex flex-nowrap"
                        @change="handleQuery"
                      />
                    </ElScrollbar>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="适用人群" prop="targetAudience">
                    <SelectPlus
                      v-model="formData.targetAudience"
                      api-key="PRODUCT_PEOPLE"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="产品风格" prop="productStyle">
                    <SelectPlus
                      v-model="formData.productStyle"
                      api-key="PRODUCT_STYLE"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="设计师" prop="designerId">
                    <CascadeSelector
                      v-model="formData.designerId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="创建时间" prop="date">
                    <ElDatePicker
                      v-model="formData.date"
                      :default-time="defaultTime"
                      class="max-w-96"
                      clearable
                      end-placeholder="结束时间"
                      range-separator="~"
                      start-placeholder="开始时间"
                      type="daterange"
                      unlink-panels
                      value-format="YYYY-MM-DD"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="产品企划" prop="designPersonId">
                    <CascadeSelector
                      v-model="formData.designPersonId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="供应商" prop="assignedFactory">
                    <SelectPlus
                      v-model="formData.assignedFactory"
                      api-key="getSupplierList"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="区域" prop="region">
                    <SelectPlus
                      v-model="formData.region"
                      api-key="COMMON_REGION"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="产品类目" prop="productCategory">
                    <ElCascader
                      v-model="formData.productCategory"
                      :options="productCategoryList"
                      :props="{
                        emitPath: false,
                        checkStrictly: true,
                        value: 'selectorKey',
                        label: 'selectorEnValue',
                        children: 'childList',
                        multiple: true
                      }"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="Style（WMS）" prop="styleWms">
                    <SelectPlus
                      v-model="formData.styleWms"
                      api-key="getStyleWmsList"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                      virtualized
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="产品阶段" prop="developStage">
                    <SelectPlus
                      v-model="formData.developStage"
                      api-key="PRODUCT_STAGE"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="任务节点" prop="taskNode">
                    <SelectPlus
                      v-model="formData.taskNode"
                      api-key="PRODUCT_TASK_NOTE"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="数据状态" prop="dataStatus">
                    <SelectPlus
                      v-model="formData.dataStatus"
                      api-key="PRODUCT_DATA_STATUS"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="开发跟进人员" prop="developmentContactPersonId">
                    <CascadeSelector
                      v-model="formData.developmentContactPersonId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="技术跟进人员" prop="technicalContactPersonId">
                    <CascadeSelector
                      v-model="formData.technicalContactPersonId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="头型" prop="toeShape">
                    <SelectPlus
                      v-model="formData.toeShape"
                      api-key="HEEL_HEAD"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="产品配色" prop="colorId">
                    <SelectPlus
                      v-model="formData.colorId"
                      api-key="colorDrop"
                      filterable
                      multiple
                      virtualized
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="10">
                  <ElFormItem label="产品楦型" prop="associatedLastType">
                    <SelectPlus
                      v-model="formData.associatedLastType"
                      api-key="lastDrop"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="供应商为空" prop="associatedLastType">
                    <ElCheckbox v-model="formData.emptySupplier" />
                  </ElFormItem>
                </ElCol>
              </div>
            </ElCollapseTransition>
          </ElRow>
        </ElForm>
        <div>
          <ElButton type="primary" @click="goTolist" class="list-button">
            <Icon icon="ep:document" />
            <span class="text-[14px]"> 打样清单</span>
          </ElButton>
          <VxeTable
            ref="tableRef"
            fv
            :cellConfig="{ height: 110 }"
            :data="tableData"
            :loading="queryLoading"
            :maxHeight="maxHeight - 75"
            :minHeight="100"
            show-overflow="tooltip"
            v-bind="{ ...scrollProp }"
            @checkbox-change="handleSelectChange"
            @checkbox-all="handleSelectChange"
          >
            <VxeColumn type="checkbox" width="40" />
            <VxeColumn title="序号" type="seq" width="60" />
            <VxeColumn
              :cell-render="{ name: 'Link', props: { clickFn: ({ row }) => handleDetail(row) } }"
              :show-overflow="false"
              field="productNumber"
              title="产品编号"
              width="120"
            />
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="thumbnail"
              title="缩略图"
              width="100"
            />
            <VxeColumn field="brandItemName" title="品牌" width="100" />
            <VxeColumn field="productCategoryItemName" title="产品类目" />
            <VxeColumn field="launchSeasonItemName" title="开发季节" />
            <VxeColumn field="createTime" title="创建时间" />
            <VxeColumn field="designPersonIdItemName" title="产品企划" />
            <VxeColumn field="designerIdItemName" title="产品设计师" />
            <VxeColumn field="developStageItemName" title="产品阶段" />
            <VxeColumn
              :cell-render="{ name: 'Ellipsis', props: { separator: '\r\n' } }"
              class-name="ellipsis-cell"
              field="stageCompDesc"
              title="阶段完成说明"
            />
            <VxeColumn field="dataStatusItemName" fixed="right" title="状态" />
          </VxeTable>
        </div>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @change="handleQuery"
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
.list-button {
  margin-bottom: 10px;
}
</style>
