import to from 'await-to-js'
import { service } from '@/config/fetch/service'
import { VersionListAPI } from '@/views/basic-library-manage/api/common'
import { ProductFactoryAPI } from '@/views/basic-library-manage/product-library/api/distributeInfoDialog'

export namespace SampleInfoAPI {
  export interface ProofInfoResp {
    factoryConfigList?: ProductFactoryAPI.FactoryConfig[]
    /**
     * 适用季节
     */
    applicableSeason?: string
    /**
     * 品牌
     */
    brand?: number
    /**
     * 齐色样打样信息
     */
    colorProofList?: BaseProof[]
    /**
     * 确认样打样信息
     */
    confirmProofList?: BaseProof[]
    /**
     * 是否S2C
     */
    dtc?: string
    /**
     * 期望上架日期
     */
    expectedLaunchDate?: string
    /**
     * 初样单打样信息
     */
    initialProofList?: BaseProof[]
    /**
     * 楦型标准
     */
    lastsStandard?: string
    /**
     * 上市季节
     */
    launchSeason?: string
    /**
     * 产品类目
     */
    productCategory?: number
    /**
     * 产品id
     */
    productId?: number
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 产品风格
     */
    productStyle?: string
    /**
     * 款式结构
     */
    styleStructure?: string
    styleWms?: string
    /**
     * 适用人群
     */
    targetAudience?: string
    [property: string]: any
  }

  /**
   * BaseProof
   */
  export interface BaseProof {
    /**
     * 样单编号
     */
    code?: string
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    id?: number
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 样单名称
     */
    name?: string
    /**
     * 产品id
     */
    productId?: number
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 打样单
     */
    proofingSheet?: BaseFileDTO
    /**
     * 需求数量
     * 打样需求数量
     */
    requiredNumber?: number
    /**
     * 初样颜色
     */
    sampleColor?: number
    /**
     * 初样颜色
     */
    sampleColorItemName?: string
    /**
     * 打样日期
     */
    sampleDate?: string
    /**
     * 启用状态
     */
    status?: string
    /**
     * 启用状态
     */
    statusItemName?: string
    /**
     * 是否结构化
     */
    structured?: string
    /**
     * 供应商
     */
    supplier?: number
    /**
     * 缩略图
     */
    thumbnail?: BaseFileDTO
    /**
     * 样单类别，初样、齐色样、确认样
     * 样单类别
     */
    type?: string
    [property: string]: any
  }

  export enum Status {
    FINISH = 'FINISH',
    CANCEL = 'CANCEL',
    AGAIN = 'AGAIN',
    DESIGN_REVIEW = 'DESIGN_REVIEW'
  }

  export type Response = ResponseData<ProofInfoResp>
}
export function getSampleInfo(id: number) {
  return to<SampleInfoAPI.Response>(service.get(`/pdm-base/proof/proofInfo/${id}`))
}

export namespace CreateSampleAPI {
  export interface Params {
    /**
     * 样单名称
     */
    name?: string
    /**
     * 产品id
     */
    productId?: number
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 是否结构化
     */
    structured?: string
  }
  export type Request = Params
  export type Response = ResponseData<SampleInfoAPI.BaseProof>
}

export function createInitSample(data: CreateSampleAPI.Request) {
  return to<CreateSampleAPI.Response>(service.post('/pdm-base/proof/saveInitial', data))
}

export function createColorSample(data: CreateSampleAPI.Request) {
  return to<CreateSampleAPI.Response>(service.post('/pdm-base/proof/saveColor', data))
}

export function createConfirmSample(data: CreateSampleAPI.Request) {
  return to<CreateSampleAPI.Response>(service.post('/pdm-base/proof/saveConfirm', data))
}

export namespace InitSampleInfoAPI {
  export interface InitialProofDetailResp {
    /**
     * 样单编号
     */
    code?: string
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    /**
     * 完成日期
     */
    finishDate?: string
    id?: number
    /**
     * 鞋垫logo
     */
    insoleLogo?: string
    /**
     * 主要面料
     */
    mainFabric?: number
    mainFabricItemName?: string
    /**
     * 材料信息
     */
    materialInfo: SkuMaterialInfo[]
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 样单名称
     */
    name?: string
    /**
     * 原版鞋图
     */
    originalShoesImg?: BaseFileDTO[]
    /**
     * 原版鞋数量
     */
    originalShoesNumber?: number
    /**
     * 产品类目
     * Category（品类）
     */
    productCategory?: number
    productCategoryItemName?: string
    /**
     * 产品大底编号
     */
    productHeel?: number
    productHeelItemName?: string
    /**
     * 产品id
     */
    productId?: number
    /**
     * 产品楦体编号
     */
    productLast?: number
    productLastItemName?: string
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 尺码段id
     * Size range（尺码段）
     */
    productSizeRange?: number[]
    productSizeRangeItemName?: string
    /**
     * 打样图
     */
    proofingImg?: BaseFileDTO[]
    /**
     * 打样单
     */
    proofingSheet?: BaseFileDTO
    /**
     * 样单材料id
     */
    proofMaterialId?: number
    /**
     * 打样要求说明
     */
    requiredMark?: string
    /**
     * 需求数量
     * 打样需求数量
     */
    requiredNumber?: number
    /**
     * 是否提供原版鞋
     */
    requiredOriginalShoes?: string
    /**
     * 初样颜色
     */
    sampleColor?: number
    /**
     * 初样颜色
     */
    sampleColorItemName?: string
    /**
     * 打样日期
     */
    sampleDate?: string
    /**
     * 启用状态
     */
    status?: string
    /**
     * 启用状态
     */
    statusItemName?: string
    /**
     * 状态流转说明
     */
    statusRemark?: string
    /**
     * 是否结构化
     */
    structured?: string
    /**
     * 供应商
     */
    supplier?: number
    /**
     * 原版鞋提供日期
     */
    supplyDate?: string
    /**
     * 缩略图
     */
    thumbnail?: BaseFileDTO
    /**
     * 样单类别，初样、齐色样、确认样
     * 样单类别
     */
    type?: string
    [property: string]: any
  }

  export interface SkuMaterialInfo {
    /**
     * 颜色
     */
    color?: number
    colorItemName?: string
    /**
     * 面料的名称  如  1 ，2 ，3， 4
     */
    indexName?: string
    /**
     * 面料
     */
    material?: number
    materialItemName?: string
    materialList?: number[]
    /**
     * 面料占比%
     */
    materialRatio?: string
    /**
     * 备注
     */
    remark?: string
    [property: string]: any
  }

  export type Response = ResponseData<InitialProofDetailResp>
}
export function getInitSampleInfo(id: number) {
  return to<InitSampleInfoAPI.Response>(service.get(`/pdm-base/proof/initialDetail/${id}`))
}
export function getInitSampleHistory(id: number) {
  return to<InitSampleInfoAPI.Response>(service.get(`/pdm-base/proof/versionInitialDetail/${id}`))
}

export namespace ColorConfirmSampleInfoAPI {
  export interface BaseProofMaterial {
    /**
     * 配色图稿
     */
    colorSchemeDraftUrl?: BaseFileDTO[]
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    id?: number
    /**
     * 鞋垫logo
     */
    insoleLogo?: string
    /**
     * 材料信息
     */
    materialInfo?: InitSampleInfoAPI.SkuMaterialInfo[]
    /**
     * 材料说明
     */
    materialRemark?: string
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 样单编号
     */
    proofCode?: string
    /**
     * 样单ID
     */
    proofId?: number
    /**
     * 备注
     */
    remark?: string
    /**
     * 需求数量
     */
    requiredNumber?: number
    /**
     * skc编码
     */
    skcCode?: string
    skcColorCode?: string
    /**
     * skc选中颜色
     */
    skcColorId?: number
    /**
     * skc选中颜色
     */
    skcColorIdItemName?: string
    /**
     * 颜色缩略图
     */
    skcColorThumbnail?: BaseFileDTO
    /**
     * skc ID
     */
    skcId?: number
    /**
     * 主要面料
     */
    skcMainFabric?: number
    /**
     * 主要面料
     */
    skcMainFabricItemName?: string
    [property: string]: any
  }
  export interface ConfirmColorProofDetailResp {
    /**
     * 样单编号
     */
    code?: string
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    /**
     * 完成日期
     */
    finishDate?: string
    id?: number
    /**
     * 材料色卡日期
     */
    materialColorDate?: string
    /**
     * 材料送检说明
     */
    materialRemark?: string
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 样单名称
     */
    name?: string
    /**
     * 产品id
     */
    productId?: number
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 尺码段id
     * Size range（尺码段）
     */
    productSizeRange?: number[]
    /**
     * 打样单
     */
    proofingSheet?: BaseFileDTO
    /**
     * 打样要求说明
     */
    requiredMark?: string
    /**
     * 需求数量
     * 打样需求数量
     */
    requiredNumber?: number
    /**
     * 初样颜色
     */
    sampleColor?: number
    /**
     * 初样颜色
     */
    sampleColorItemName?: string
    /**
     * 打样日期
     */
    sampleDate?: string
    /**
     * 打样码数
     */
    sizeValue?: string[]
    /**
     * skc打样材料数据
     */
    skcProofMaterial?: BaseProofMaterial[]
    /**
     * 启用状态
     */
    status?: string
    /**
     * 启用状态
     */
    statusItemName?: string
    /**
     * 状态流转说明
     */
    statusRemark?: string
    /**
     * 是否结构化
     */
    structured?: string
    /**
     * 供应商
     */
    supplier?: string
    /**
     * 缩略图
     */
    thumbnail?: BaseFileDTO
    /**
     * 样单类别，初样、齐色样、确认样
     * 样单类别
     */
    type?: string
    [property: string]: any
  }
  export type Response = ResponseData<ConfirmColorProofDetailResp>
}
export function getColorConfirmSampleInfo(id: number) {
  return to<ColorConfirmSampleInfoAPI.Response>(
    service.get(`/pdm-base/proof/conformColorDetail/${id}`)
  )
}
export function getColorConfirmSampleHistory(id: number) {
  return to<ColorConfirmSampleInfoAPI.Response>(
    service.get(`/pdm-base/proof/versionConfirmColorDetail/${id}`)
  )
}

export namespace ColorConfirmSampleInfoStoreAPI {
  export type Request = ColorConfirmSampleInfoAPI.ConfirmColorProofDetailResp
  export type Response = BasicResponseData
}
export function colorConfirmSampleInfoStore(data: ColorConfirmSampleInfoStoreAPI.Request) {
  return to<ColorConfirmSampleInfoStoreAPI.Response>(
    service.post(`/pdm-base/proof/updateConfirmColor`, data)
  )
}

export namespace InitSampleStoreAPI {
  export type Request = InitSampleInfoAPI.InitialProofDetailResp
  export type Response = BasicResponseData
}
export function initSampleStore(data: InitSampleStoreAPI.Request) {
  return to<InitSampleStoreAPI.Response>(service.post(`/pdm-base/proof/updateInitial`, data))
}

export namespace SampleReviewInfoAPI {
  export interface InitialFollowResp {
    followUpList?: ProofFollowDetail[]
    [property: string]: any
  }
  export interface ProofFollowDetail {
    /**
     * 外观意见
     */
    appearanceOpinions?: string
    /**
     * 产品鞋跟
     */
    associatedHeelInfoCode?: string
    /**
     * 产品鞋垫
     */
    associatedInsoleInfoCode?: string
    /**
     * 产品楦型
     */
    associatedLastTypeCode?: string
    /**
     * 产品大底
     */
    associatedSoleInfoCode?: string
    /**
     * 确认样报告
     */
    confirmationReport?: BaseFileDTO[]
    confirmationReportName?: string
    /**
     * 跟进开发人员
     */
    developmentContactPersonId?: number[]
    /**
     * 跟进开发人员
     */
    developmentContactPersonIdItemName?: string
    /**
     * 开发评估意见
     */
    developmentEvaluationOpinions?: string
    /**
     * 工厂预计完成时间
     */
    expectedCompletionDate?: string
    followId?: number
    /**
     * 垫脚工艺
     */
    footbedTechnique?: string
    /**
     * 楦底类型
     */
    lastBottomType?: string
    /**
     * 楦底类型
     */
    lastBottomTypeItemName?: string
    /**
     * 是否新开模
     */
    newMoldDict?: string
    /**
     * 是否新开模
     */
    newMoldDictItemName?: string
    /**
     * 原版鞋接收日期
     */
    originalShoesAcceptDate?: string
    /**
     * 工厂接收日期
     */
    outsourcingAcceptDate?: string
    /**
     * 外发打样日期
     */
    outsourcingIssueDate?: string
    /**
     * 制作工艺
     */
    productionTechnique?: string
    /**
     * 打样跟进编号
     */
    recordCode?: string
    /**
     * 打样完成日期
     */
    sampleCompletionDate?: string
    /**
     * 打样信息记录
     */
    sampleInformationRecord?: string
    /**
     * 打样数量
     */
    sampleNumber?: number
    /**
     * 打样环节
     */
    sampleProcessDict?: string
    /**
     * 打样环节
     */
    sampleProcessDictItemName?: string
    /**
     * 打样跟进结果
     */
    sampleResultDict?: string
    /**
     * 打样跟进结果
     */
    sampleResultDictItemName?: string
    /**
     * 样品寄送日期
     */
    sampleSendDate?: string
    /**
     * 鞋底工艺
     */
    shoeSoleTechnique?: string
    shoeSoleTechniqueItemName?: string
    /**
     * 跟进技术人员
     */
    technicalContactPersonId?: number[]
    /**
     * 跟进技术人员
     */
    technicalContactPersonIdItemName?: string
    /**
     * 技术评估意见
     */
    technicalEvaluationOpinions?: string
    /**
     * 产前技术评估报告
     */
    riskFileName?: string
    riskQmsObjectName?: string
    [property: string]: any
  }

  export type Response = ResponseData<InitialFollowResp>
}
export function getSampleFollowList(id: number) {
  return to<SampleReviewInfoAPI.Response>(service.get(`/pdm-base/proof/follow/${id}`))
}

export namespace InitSampleReviewInfoAPI {
  export type Request = {
    proofId?: number
    followId?: number
  }
  export interface BaseProofResult {
    /**
     * 技术评审报告
     */
    technicalReviewReport?: BaseFileDTO[]
    /**
     * 参考产品编号
     */
    referenceProductCode?: string
    /**
     * 补充说明
     */
    addInformationImg?: BaseFileDTO[]
    /**
     * 处理结果
     */
    designerProcessingOpinions?: string
    /**
     * 打样跟进id
     */
    followId?: number
    id?: number
    /**
     * 评审意见
     */
    initialSampleReviewOpinions?: string
    /**
     * 评审结果
     */
    preliminaryConclusion?: string
    /**
     * 打样单编号
     */
    proofCode?: string
    /**
     * 打样单id
     */
    proofId?: number
    /**
     * 样品接收日期
     */
    sampleAcceptDate?: string
    /**
     * 样品图片
     */
    sampleImages?: BaseFileDTO[]
    /**
     * skc id
     */
    skcId?: number
    [property: string]: any
  }

  export type Response = ResponseData<BaseProofResult>
}
export function getInitSampleReviewInfo(params: InitSampleReviewInfoAPI.Request) {
  return to<InitSampleReviewInfoAPI.Response>(
    service.get(`/pdm-base/proof/initialResult`, { params })
  )
}

export namespace InitSampleReviewAPI {
  export type Request = InitSampleReviewInfoAPI.BaseProofResult &
    SampleReviewInfoAPI.ProofFollowDetail
  export type Response = BasicResponseData
}
export function initSampleReview(data: InitSampleReviewAPI.Request) {
  return to<InitSampleReviewAPI.Response>(service.post(`/pdm-base/proof/resultInitial`, data))
}

// 初样暂存
export function saveResultInitial(data: InitSampleReviewAPI.Request) {
  return to<InitSampleReviewAPI.Response>(service.post(`/pdm-base/proof/saveResultInitial`, data))
}

export namespace ColorConfirmSampleInfoReviewInfoAPI {
  export interface ConfirmColorResultResp {
    again?: string
    resultList?: ConfirmColorResult[]
    [property: string]: any
  }
  export interface ConfirmColorResult {
    /**
     * 打样跟进评审信息
     */
    followResult?: FollowResult
    /**
     * 设计师评审信息
     */
    proofResult?: InitSampleReviewInfoAPI.BaseProofResult
    /**
     * skc 信息
     */
    proofSkcInfo?: ProofSkcInfo
    [property: string]: any
  }
  /**
   * 打样跟进评审信息
   */
  export interface FollowResult {
    /**
     * 外观意见
     */
    appearanceOpinion?: string
    /**
     * 开发评估意见
     */
    developmentAssessment?: string
    /**
     * 打样信息记录
     */
    sampleMakingInfo?: string
    /**
     * 技术评估意见
     */
    technicalAssessment?: string
    [property: string]: any
  }
  /**
   * skc 信息
   */
  export interface ProofSkcInfo {
    /**
     * 配色类型
     */
    colorType?: string
    id?: number
    /**
     * 打样需求数量
     */
    requiredNumber?: number
    /**
     * skc编码
     */
    skcCode?: string
    /**
     * 配色id
     */
    skcColorId?: number
    /**
     * 配色id
     */
    skcColorIdItemName?: string
    /**
     * 颜色缩略图
     */
    skcColorThumbnail?: BaseFileDTO
    /**
     * skcId
     */
    skcId?: number
    /**
     * 主要面料
     */
    skcMainFabric?: number
    /**
     * 主要面料
     */
    skcMainFabricItemName?: string
    [property: string]: any
  }
  export type Response = ResponseData<ConfirmColorResultResp>
}
export function getColorConfirmSampleReviewInfo(params: InitSampleReviewInfoAPI.Request) {
  return to<ColorConfirmSampleInfoReviewInfoAPI.Response>(
    service.get('/pdm-base/proof/confirmColorResult', { params })
  )
}

export namespace ColorConfirmSampleReviewAPI {
  export type Request = {
    proofResultList: InitSampleReviewInfoAPI.BaseProofResult[]
    proofId?: number
    again?: number
  }
  export type Response = BasicResponseData
}
export function colorConfirmSampleReview(data: ColorConfirmSampleReviewAPI.Request) {
  return to<ColorConfirmSampleReviewAPI.Response>(
    service.post('/pdm-base/proof/resultConfirmColor', data)
  )
}

// 齐色样、确认样暂存
export function saveResultConfirmColorProof(data: ColorConfirmSampleReviewAPI.Request) {
  return to<ColorConfirmSampleReviewAPI.Response>(
    service.post('/pdm-base/proof/saveResultConfirmColorProof', data)
  )
}

export namespace SyncConfirmSampleAPI {
  export type Request = {
    proofIdList?: number[]
  }
  export type Response = BasicResponseData
}
export function syncConfirmSample(params: SyncConfirmSampleAPI.Request) {
  return to<SyncConfirmSampleAPI.Response>(service.get('/pdm-base/proof/oneClick', { params }))
}

export function initSampleCommit(id: number) {
  return to<BasicResponseData>(service.get(`/pdm-base/proof/commitInitial/${id}`))
}

export function colorConfirmSampleCommit(id: number) {
  return to<BasicResponseData>(service.get(`/pdm-base/proof/commitConfirmColor/${id}`))
}

export function deleteSampleInfo(id: number) {
  return to<BasicResponseData>(service.get(`/pdm-base/proof/delete/${id}`))
}

export function copySampleInfo(id: number) {
  return to<BasicResponseData>(service.get(`/pdm-base/proof/copy/${id}`))
}

export function getSampleVersionList(id: number) {
  return to<VersionListAPI.Response>(service.get(`/pdm-base/proof/version/${id}`))
}

export function invalidSample(id: number) {
  return to<BasicResponseData>(service.get(`/pdm-base/proof/invalidProof/${id}`))
}

export function revokeSample(id: number) {
  return to<BasicResponseData>(service.get(`/pdm-base/proof/withdrawProof/${id}`))
}
