import { service } from '@/config/fetch/service'
import to from 'await-to-js'
import { StatusEnum } from '@/views/basic-library-manage/const'
import type { SampleInfoAPI } from '@/views/basic-library-manage/sample-manage/api/SampleInfo'
import { SampleEnums } from '@/views/basic-library-manage/sample-manage/const'
export namespace SampleListPageAPI {
  export interface Row extends SampleInfoAPI.BaseProof {
    /**
     * 打样单编号
     */
    code?: string
    thumbnail?: BaseFileDTO
    type?: SampleEnums
    //是否结构化
    structured?: string
    name?: string
    //供应商
    assignedFactory?: string | string[]
    assignedFactoryItemName?: string
    //打样单
    proofingSheet?: BaseFileDTO
    remark?: string
    //启用状态
    status?: string
    //启用状态编码
    statusCode?: null | string
    createById?: number
    modifyById?: number
    createTime?: string
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 数据ID
     */
    id?: number
    _X_ROW_KEY?: string
  }
  export type Params = {
    /**
     * 产品编号（自动生成）
     */
    productNumber?: string[]
    /**
     * 打样单编号
     */
    proofCode?: string[]
    /**
     * 品牌
     */
    brand?: number[]
    /**
     * style编号
     */
    styleNumber?: string
    /**
     * 上市季节
     */
    launchSeason?: string[]

    styleWms?: string[]
    /**
     * 供应商id
     */
    factoryId?: string[]
    /**
     * 区域
     */
    region?: string[]
    /**
     * 跟进开发人员Id
     */
    developmentContactPersonId?: string[]
    /**
     * 跟进技术人员Id
     */
    technicalContactPersonId?: string[]
    /**
     * 产品企划人员id
     */
    designPersonId?: string[]
    /**
     * 产品设计师人员id
     */
    designerId?: string[]
    /**
     * 打样单类型
     */
    proofType?: string[]

    //我的打样单
    mine?: boolean
    /*打样单状态*/
    status?: StatusEnum[]
  }
  export type Response = PagedResponseData<Row>
  export type Request = Params & PageParams
  export type List = Row[]
}
export function getSampleList(params: SampleListPageAPI.Request, signal?: AbortSignal) {
  return to<SampleListPageAPI.Response>(
    service.get('/pdm-base/proof/page', {
      params,
      signal
    })
  )
}
