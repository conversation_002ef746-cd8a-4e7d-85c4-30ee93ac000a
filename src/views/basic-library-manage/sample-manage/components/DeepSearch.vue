<script setup lang="ts">
import { Icon } from '@/components/Icon'
import { ElButton, ElCollapseTransition } from 'element-plus'
import { ref, useSlots } from 'vue'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
defineOptions({
  name: 'DeepSearch'
})
interface FormItem {
  label: string
  component: HTMLElement
  value: string
  span?: number
  isCollapse?: boolean
  props: { [key: string]: any }
  [property: string]: any
}
const slots = useSlots()

const props = withDefaults(
  defineProps<{
    queryLoading: boolean
    formConfig: FormItem[]
    formData: Record<string, number> // Added since you're using it in defaults
  }>(),
  {
    queryLoading: false,
    formConfig: () => [],
    formData: () => ({})
  }
)
const emits = defineEmits(['update:formData', 'handleQuery', 'handleReset'])
const visible = ref(false)
const setVisible = async () => {
  visible.value = !visible.value
  await nextTick()
}
const { formLabelLength } = useLocaleConfig([
  {
    formLabelLength: '120'
  },
  {
    formLabelLength: '180'
  }
])
const formModel = computed({
  get: () => props.formData,
  set: (value) => emits('update:formData', value)
})
const handleReset = () => {
  emits('handleReset')
}
const ExpandCol = computed(() => props.formConfig.filter((item) => item.isExpand))
const CollapsedCol = computed(() => props.formConfig.filter((item) => !item.isExpand))
const handleQuery = () => {
  emits('handleQuery')
}
</script>
<template>
  <ElForm ref="formRef" :label-width="formLabelLength">
    <ElRow :gutter="20">
      <!--展开的内容-->
      <template v-for="(item, index) in ExpandCol" :key="index">
        <ElCol :span="item.span">
          <ElFormItem :label="item.label" :prop="item.value">
            <template v-if="item.component.name === 'ElSelect'">
              <!-- 为 ElSelect 组件渲染选项 -->
              <ElSelect v-model="formModel[item.value]" v-bind="{ ...item.props }">
                <ElOption
                  v-for="optionItem in item.props.options"
                  :key="optionItem.key"
                  :label="optionItem.label"
                  :value="optionItem.value"
                />
              </ElSelect>
            </template>
            <template v-else>
              <component
                :is="item.component"
                v-model="formModel[item.value]"
                v-bind="{ ...item.props }"
              />
            </template>
          </ElFormItem>
        </ElCol>
        <ElCol :span="8" v-if="index == 1">
          <ElScrollbar class="w-full" style="height: 32px">
            <ElFormItem class="form-item-no-wrap" label="" label-width="0">
              <ElButton text @click="setVisible">
                {{ visible ? '收起' : '展开' }}
                <Icon
                  :class="visible ? 'rotate-90' : ''"
                  class="transform transition duration-400"
                  icon="ant-design:down-outlined"
                />
              </ElButton>
              <ElButton @click="handleQuery" :loading="queryLoading" class="w-16" type="primary">
                <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
                查询
              </ElButton>
              <ElButton
                @click="handleReset"
                :loading="queryLoading"
                class="w-16"
                native-type="reset"
              >
                <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
                重置
              </ElButton>
              <template v-if="slots.button">
                <slot name="button"></slot>
              </template>
            </ElFormItem>
          </ElScrollbar>
        </ElCol>
      </template>
      <!--需要收拢的内容-->
      <ElCollapseTransition>
        <ElCol :span="16">
          <div v-show="visible" class="grid grid-cols-2 items-start w-full">
            <ElFormItem
              v-for="(item, index) in CollapsedCol"
              :key="index + ExpandCol.length"
              :label="item.label"
              :prop="item.value"
            >
              <template v-if="item.component.name === 'ElSelect'">
                <!-- 为 ElSelect 组件渲染选项 -->
                <ElSelect v-model="formModel[item.value]" v-bind="{ ...item.props }">
                  <ElOption
                    v-for="optionItem in item.props.options"
                    :key="optionItem.key"
                    :label="optionItem.label"
                    :value="optionItem.value"
                  />
                </ElSelect>
              </template>
              <template v-else>
                <component
                  :is="item.component"
                  v-model="formModel[item.value]"
                  v-bind="{ ...item.props }"
                />
              </template>
            </ElFormItem>
          </div>
        </ElCol>
      </ElCollapseTransition>
    </ElRow>
  </ElForm>
</template>

<style scoped lang="less"></style>
