<script lang="ts" setup>
import { CommonYesNoEnums, DialogTypeEnums } from '@/enums'
import {
  ColorConfirmSampleInfoAPI,
  colorConfirmSampleInfoStore,
  getColorConfirmSampleHistory,
  getColorConfirmSampleInfo,
  SampleInfoAPI
} from '../../api/SampleInfo'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { cloneDeep, omit, pick } from 'lodash-es'
import { commonFabricType, FabricTypeEnum, useMaterial } from '../hooks'
import ElCascader from '@/components/Cascader/src/Cascader'
import { Icon } from '@/components/Icon'
import ColorInfoDialog from '@/views/basic-library-manage/components/ColorInfoDialog.vue'
import { ColorListPageAPI } from '@/views/basic-library-manage/color-library/api/color-list'
import { VxeTable, VxeTableDefines, VxeTableInstance, VxeTablePropTypes } from 'vxe-table'
import SkcInfoDialog from '../SkcInfoDialog.vue'
import { ProductSkcInfoPageAPI } from '@/api/productSkcInfo/types'
import { hasValue } from '@/utils'
import { VersionListAPI } from '@/views/basic-library-manage/api/common'
import { CommandEnums, CommandPermission } from '../../const'
import { VXE_TABLE_ROW_KEY } from '@/constants'
import XEUtils from 'xe-utils'
import { MaterialCategoryEnum, YesNoEnum } from '@/views/basic-library-manage/const'

defineOptions({
  name: 'ColorSampleInfoDialog'
})

const props = defineProps<{
  type?: DialogTypeEnums
  isConfirm?: boolean
  currentRow?: SampleInfoAPI.BaseProof | VersionListAPI.Data
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit'): void
  (e: 'refresh'): void
}>()

const isHistory = computed(() => props.currentRow?.versionCode)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const isView = computed(() => props.type === DialogTypeEnums.VIEW)

const label = computed(() => (props.isConfirm ? '确认样' : '齐色样'))

const title = computed(() => {
  if (props.type === DialogTypeEnums.EDIT) {
    return `${label.value}打样编辑`
  }
  if (isView.value) {
    return `${label.value}打样详情`
  }
})

const initForm: ColorConfirmSampleInfoAPI.ConfirmColorProofDetailResp = {
  code: '', // 样单编号
  createById: undefined, // 创建人
  createTime: '', // 创建时间
  delFlag: undefined,
  finishDate: '', // 完成日期
  id: undefined,
  materialColorDate: '', // 材料色卡日期
  materialRemark: '', // 材料送检说明
  modifyById: undefined, // 操作人
  modifyTime: '', // 操作时间
  name: '', // 样单名称
  productId: undefined, // 产品id
  productNumber: '', // 产品编号
  productSizeRange: [], // 尺码段id
  proofingSheet: undefined, // 打样单
  requiredMark: '', // 打样要求说明
  requiredNumber: undefined, // 打样需求数量
  sampleColor: undefined, // 初样颜色
  sampleColorItemName: '', // 初样颜色
  sampleDate: '', // 打样日期
  sizeValue: [], // 打样码数
  skcProofMaterial: [], // skc打样材料数据
  status: '', // 启用状态
  statusItemName: '', // 启用状态
  statusRemark: '', // 状态流转说明
  structured: '', // 是否结构化
  supplier: '', // 供应商
  thumbnail: undefined, // 缩略图
  type: '' // 样单类别
}

const formRef = ref<FormInstance>()
const formData = ref(cloneDeep(initForm))
const formRules = ref<FormRules<ColorConfirmSampleInfoAPI.ConfirmColorProofDetailResp>>({
  name: [{ required: true, message: `请输入${label.value}单名称` }],
  sampleDate: [{ required: true, message: `请选择${label.value}打样日期` }],
  proofingSheet: [{ required: true, message: `请上传${label.value}打样单` }],
  skcProofMaterial: [
    {
      asyncValidator: async (_rule, _value, callback) => {
        if (!formData.value.skcProofMaterial?.length) {
          callback(new Error('请至少添加一条SKC数据'))
          return
        }
        const errorMsg = await tableRef.value?.validate?.(true).catch(() => false)
        if (errorMsg) {
          const { row, column, rule } = Object.values(errorMsg)[0][0]
          await tableRef.value?.setSelectCell(row, column)
          callback(new Error(rule.message))
          return
        }
        callback()
      }
    }
  ]
})
const { materialInfo, validFabricTypeList, materialCategoryList, materialCascaderProps } =
  useMaterial()

const filteredMaterialConfigList = computed(() => {
  // 面材料添加辅料
  return materialInfo.value.map((e, i) => {
    return {
      ...e,
      index: i,
      materialOption: commonFabricType.includes(e.indexName!)
        ? e.materialOption.concat(
            materialCategoryList.value.filter(
              (e) => MaterialCategoryEnum.ACC === e.selectorEnValue!
            )
          )
        : e.materialOption
    }
  })
})

const tableRef = ref<VxeTableInstance>()
const disabledColumns = ref<VxeTableDefines.ColumnInfo[]>([])
const handleColumnChange = (val: VxeTableDefines.ColumnInfo[]) => {
  const materialInfoValue = materialInfo.value
  const length = materialInfoValue.length
  for (let i = 0; i < length; i++) {
    const materialConfig = materialInfoValue[i]
    const materialField = `materialInfo.${i}.${
      materialConfig.multiple ? 'materialList' : 'material'
    }`
    const colorField = `materialInfo.${i}.color`
    const remarkField = `materialInfo.${i}.remark`
    const materialColumn = val.find((item) => item.field === materialField)
    const colorColumn = val.find((item) => item.field === colorField)
    const remarkColumn = val.find((item) => item.field === remarkField)
    const setVisible = () => {
      if (colorColumn) {
        colorColumn.visible = true
      }
      if (remarkColumn) {
        remarkColumn.visible = true
      }
    }
    if (materialColumn && materialColumn.visible) {
      setVisible()
    }
    if (
      materialColumn &&
      !materialColumn.visible &&
      (colorColumn?.visible || remarkColumn?.visible)
    ) {
      setVisible()
      materialColumn.visible = true
      formData.value.skcProofMaterial?.forEach((item) => {
        XEUtils.set(item, materialField, materialConfig.multiple ? [] : undefined)
      })
    }
  }
  tableRef.value?.reloadColumn(val)
  tableRef.value?.loadData(formData.value.skcProofMaterial || [])
}
const tableRules = computed<VxeTablePropTypes.EditRules>(() => {
  const rules: VxeTablePropTypes.EditRules = {
    materialRemark: [{ required: true, message: '请输入材料编号说明', trigger: 'manual' }],
    insoleLogo: [{ required: true, message: '请输入鞋垫Logo', trigger: 'manual' }],
    requiredNumber: [{ required: true, message: '请输入需求数量', trigger: 'manual' }]
  }
  for (let i = 0; i < materialInfo.value.length; i++) {
    const materialConfig = materialInfo.value[i]
    const validator: VxeTableDefines.ValidatorRule['validator'] = (params) => {
      const { row } = params
      if (materialConfig.multiple) {
        if (row.materialInfo[i].materialList?.length && !row.materialInfo[i].color) {
          return Promise.reject(new Error(`请选择${materialConfig.indexName}颜色`))
        }
        return Promise.resolve()
      }
      if (row.materialInfo[i].material && !row.materialInfo[i].color) {
        return Promise.reject(new Error(`请选择${materialConfig.indexName}颜色`))
      }
      return Promise.resolve()
    }
    rules[`materialInfo.${i}.${materialConfig.multiple ? 'materialList' : 'material'}`] = [
      { required: true, message: `请选择${materialConfig.indexName}`, trigger: 'manual' },
      { validator, trigger: 'manual' }
    ]
    rules[`materialInfo.${i}.color`] = [
      {
        validator,
        trigger: 'manual'
      }
    ]
  }
  return rules
})

const statusArr = computed(() => formData.value.statusRemark?.split('-') || [])
const isStructured = computed(() => formData.value.structured === CommonYesNoEnums.Yes)

const queryLoading = ref(false)
watch(
  () => visible.value,
  async (val) => {
    if (val) {
      if (isHistory.value) {
        const currentRow = props.currentRow as VersionListAPI.Data
        queryLoading.value = true
        const [error, result] = await getColorConfirmSampleHistory(currentRow.id!)
        queryLoading.value = false
        if (!error && result?.datas) {
          formData.value = result.datas
        }
      } else {
        const currentRow = props.currentRow as SampleInfoAPI.BaseProof
        queryLoading.value = true
        const [error, result] = await getColorConfirmSampleInfo(currentRow.id!)
        queryLoading.value = false
        if (!error && result?.datas) {
          formData.value = {
            ...result.datas,
            skcProofMaterial:
              result.datas.skcProofMaterial?.map((e) => ({
                ...e,
                materialInfo: e.materialInfo
                  ? e.materialInfo.map((i) => ({
                      ...i,
                      color: i.color || e.skcColorId,
                      colorItemName: i.colorItemName || e.skcColorIdItemName
                    }))
                  : cloneDeep(materialInfo.value).map((i) => ({
                      ...i,
                      color: e.skcColorId,
                      colorItemName: e.skcColorIdItemName
                    }))
              })) || []
          }
        }
        await nextTick()
        const { fullColumn } = tableRef.value?.getTableColumn() || {}

        disabledColumns.value = fullColumn?.filter((item) => item.visible) || []
        for (let i = 0; i < materialInfo.value.length; i++) {
          const materialConfig = materialInfo.value[i]
          const materialField = `materialInfo.${i}.${
            materialConfig.multiple ? 'materialList' : 'material'
          }`
          const colorField = `materialInfo.${i}.color`
          const remarkField = `materialInfo.${i}.remark`
          const materialColumn = fullColumn?.find((item) => item.field === materialField)
          const colorColumn = fullColumn?.find((item) => item.field === colorField)
          const remarkColumn = fullColumn?.find((item) => item.field === remarkField)
          const hasMaterial = formData.value.skcProofMaterial?.some((e) =>
            hasValue(XEUtils.get(e, materialField))
          )
          if (materialColumn && hasMaterial) {
            materialColumn.visible = true
            if (remarkColumn) {
              remarkColumn.visible = true
            }
            if (colorColumn) {
              colorColumn.visible = true
            }
          }
        }
        tableRef.value?.reloadColumn(fullColumn || [])
      }
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const currentMaterialRow = ref<ColorConfirmSampleInfoAPI.BaseProofMaterial>()
const currentMaterialIndex = ref<number>()
const colorInfoDialogVisible = ref(false)
const handleOpenColorInfoDialog = (
  row: ColorConfirmSampleInfoAPI.BaseProofMaterial,
  index: number
) => {
  if (isView.value) {
    return
  }
  currentMaterialRow.value = row
  currentMaterialIndex.value = index
  colorInfoDialogVisible.value = true
}

const handlePickColor = (val: ColorListPageAPI.Row) => {
  const row = currentMaterialRow.value?.materialInfo?.[currentMaterialIndex.value!]
  if (!row) return
  row.colorItemName = val.englishName || ''
  row.color = val.id
}

const addSkcDialogVisible = ref(false)
const handleValidateSKC = (val: ProductSkcInfoPageAPI.List[]) => {
  const allMaterialAndColor =
    formData.value.skcProofMaterial?.map(
      (e) => e.skcMainFabric!.toString() + e.skcColorId!.toString()
    ) || []
  const hasSameMaterialAndColor = val.some((e) => {
    return allMaterialAndColor.includes(e.mainFabric!.toString() + e.skcColorId!.toString())
  })
  if (hasSameMaterialAndColor) {
    ElMessage.warning('所选面料颜色组合已存在')
  }
  return !hasSameMaterialAndColor
}
const handleAddSkc = (val: ProductSkcInfoPageAPI.List[]) => {
  formData.value.skcProofMaterial = (formData.value.skcProofMaterial || []).concat(
    val.map((e) => ({
      skcId: e.id,
      skcColorId: e.skcColorId,
      skcColorIdItemName: e.colorName,
      skcColorThumbnail: e.thumbnail,
      colorSchemeDraftUrl: e.colorSchemeDraftUrl,
      skcMainFabric: e.mainFabric,
      skcMainFabricItemName: e.mainFabricItemName,
      materialInfo: cloneDeep(materialInfo.value)
    }))
  )
  tableRef.value?.loadData(formData.value.skcProofMaterial || [])
}

const handleRemove = () => {
  const selected = tableRef.value?.getCheckboxRecords()
  if (!selected?.length) {
    ElMessage.warning('请先勾选数据')
    return
  }
  formData.value.skcProofMaterial = formData.value.skcProofMaterial?.filter(
    (item) => !selected.includes(item)
  )
  tableRef.value?.loadData(formData.value.skcProofMaterial || [])
}
const handleSyncFirst = () => {
  const firstRow = formData.value.skcProofMaterial?.[0]
  if (!firstRow) return
  formData.value.skcProofMaterial =
    formData.value.skcProofMaterial?.map((item) => {
      const currentRow = pick(item, [
        'colorSchemeDraftUrl',
        'skcCode',
        'skcColorCode',
        'skcColorId',
        'skcColorIdItemName',
        'proofCode',
        'skcColorThumbnail',
        'skcId',
        'skcMainFabric',
        'skcMainFabricItemName'
      ])
      const row = omit(firstRow, [
        'id',
        'colorSchemeDraftUrl',
        'skcCode',
        'skcColorCode',
        'skcColorId',
        'skcColorIdItemName',
        'proofCode',
        'skcColorThumbnail',
        'skcId',
        'skcMainFabric',
        'skcMainFabricItemName'
      ])
      return {
        ...currentRow,
        ...cloneDeep(row),
        [VXE_TABLE_ROW_KEY]: undefined
      }
    }) || []
}

const handleEdit = () => {
  emit('edit')
}

const submitLoading = ref(false)

const handleSave = async () => {
  submitLoading.value = true
  const [error, result] = await colorConfirmSampleInfoStore({
    ...formData.value,
    skcProofMaterial:
      formData.value.skcProofMaterial?.map((e) => ({
        ...e,
        materialInfo: isStructured.value
          ? e.materialInfo?.map((i) => ({
              ...i,
              materialOptions: undefined
            }))
          : undefined
      })) || []
  })
  submitLoading.value = false
  if (!error && result) {
    ElMessage.success(result.msg || '保存成功')
    handleClose()
    emit('refresh')
  }
}

const editField = ref<number>(0)
const materialConfig = computed(() => materialInfo.value[editField.value])

const material = ref<number | number[]>()

const setMaterialValue = (
  selectedRows: ColorConfirmSampleInfoAPI.BaseProofMaterial[],
  val: number | number[]
) => {
  selectedRows.forEach((e) => {
    if (!e.materialInfo) return
    const materialRow = e.materialInfo.find((e) => e.indexName === materialConfig.value.indexName)
    if (!materialRow) return
    if (materialConfig.value?.multiple) {
      materialRow.materialList = val as number[]
    }
    if (!materialConfig.value?.multiple) {
      materialRow.material = val as number
    }
  })
}

const handleSetMaterial = () => {
  const selected: ColorConfirmSampleInfoAPI.BaseProofMaterial[] =
    tableRef.value?.getCheckboxRecords() || []
  if (!selected.length) {
    ElMessage.warning('请先勾选数据')
    return
  }
  if (!hasValue(material.value)) {
    ElMessage.warning('请先选择材质')
    return
  }
  setMaterialValue(selected, material.value!)
  tableRef.value?.loadData(formData.value.skcProofMaterial || [])
}

const handleResetMaterial = () => {
  const selected: ColorConfirmSampleInfoAPI.BaseProofMaterial[] =
    tableRef.value?.getCheckboxRecords() || []
  if (!selected.length) {
    ElMessage.warning('请先勾选数据')
    return
  }
  selected.forEach((e) => {
    e.materialInfo?.forEach((item) => {
      item.material = undefined
      item.materialList = []
    })
  })
  tableRef.value?.loadData(formData.value.skcProofMaterial || [])
}

const colSettingVisible = ref(false)
const activeNames = ref(['1', '2'])

const handleClose = () => {
  formData.value = cloneDeep(initForm)
  nextTick(formRef.value?.clearValidate)
  visible.value = false
}
</script>

<template>
  <SkcInfoDialog
    v-model="addSkcDialogVisible"
    :is-confirm="isConfirm"
    :product-number="[formData.productNumber!]"
    :validate="handleValidateSKC"
    @submit="handleAddSkc"
  />
  <ColorInfoDialog v-model="colorInfoDialogVisible" @submit="handlePickColor" />
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :title="title"
    append-to-body
    max-height="calc(100vh - 10vh - 120px - var(--el-dialog-padding-primary) - 50px)"
    top="5vh"
    width="1460px"
  >
    <ElForm
      ref="formRef"
      v-loading="queryLoading"
      :disabled="isView"
      :model="formData"
      :rules="formRules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      label-width="auto"
      scroll-to-error
    >
      <ElCollapse v-model="activeNames">
        <ElCollapseItem :title="`${label}打样详情`" name="1">
          <ElRow :gutter="10" class="row-container">
            <ElCol :span="8">
              <ElFormItem :label="`${label}单编号`">
                <ElText>
                  {{ formData.code }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem :label="`${label}单名称`" prop="name">
                <ElInput v-model="formData.name" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="状态">
                <span v-for="(item, index) in statusArr" :key="item">
                  <span :class="[index === statusArr.length - 1 ? 'font-bold' : '']">
                    {{ item }}
                  </span>
                  <span v-if="index !== statusArr.length - 1">-</span>
                </span>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="供应商" prop="supplier">
                <SelectPlus
                  v-model="formData.supplier"
                  :cascade-clear="false"
                  :data-method="
                    (val) => val.map((e) => ({ ...e, disabled: e.useStatus !== +YesNoEnum.Y }))
                  "
                  :params="formData.productId"
                  api-key="getSampleSupplierList"
                  filterable
                  ganged
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem :label="`${label}打样日期`" prop="sampleDate">
                <ElDatePicker
                  v-model="formData.sampleDate"
                  :placeholder="`请选择${label}打样日期`"
                  clearable
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
            </ElCol>
            <template v-if="!isStructured">
              <ElCol :span="8">
                <ElFormItem :label="`${label}打样单`" prop="proofingSheet">
                  <OssUpload
                    :limit="1"
                    :model-value="formData.proofingSheet ? [formData.proofingSheet] : []"
                    :size-limit="1024 * 1024 * 100"
                    drag
                    list-type="text"
                    multiple
                    @update:model-value="(val) => (formData.proofingSheet = val[0])"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="打样需求数量（双）" prop="requiredNumber">
                  <ElInputNumber
                    v-model="formData.requiredNumber"
                    :controls="false"
                    :min="0"
                    :step="0.5"
                    step-strictly
                  />
                </ElFormItem>
              </ElCol>
            </template>
            <ElCol v-if="isStructured" :span="8">
              <ElFormItem label="Size range（尺码段）">
                <ElText>
                  {{ formData.productSizeRangeItemName }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="需求完成日期" prop="finishDate">
                <ElDatePicker
                  v-model="formData.finishDate"
                  clearable
                  placeholder="年/月/日"
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isStructured" :span="8">
              <ElFormItem label="打样码数" prop="sizeValue">
                <ElSelect v-model="formData.sizeValue" filterable multiple>
                  <ElOption
                    v-for="item in formData.productSizeRangeItemName?.split(',')"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="打样要求说明">
                <ElInput
                  v-model="formData.requiredMark"
                  :autosize="{ minRows: 2 }"
                  maxlength="500"
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="材料送检说明">
                <ElInput
                  v-model="formData.materialRemark"
                  :autosize="{ minRows: 2 }"
                  maxlength="500"
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCollapseItem>
        <ElCollapseItem v-if="isStructured" name="2" title="打样材质详情">
          <div v-if="!isView" class="mb-2 flex items-center">
            <ElButton type="primary" @click="addSkcDialogVisible = true">添加配色</ElButton>
            <ElButton type="primary" @click="colSettingVisible = true">添加材料</ElButton>
            <ElButton type="primary" @click="handleRemove">移除</ElButton>
            <ElButton type="primary" @click="handleSyncFirst">一键设置同第一行</ElButton>
            <ElButton link>批量修改属性</ElButton>
            <ElSelect
              v-model="editField"
              :validate-event="false"
              class="ml-2 !w-48"
              filterable
              placeholder="请选择"
            >
              <ElOption
                v-for="(item, index) in materialInfo"
                :key="item.indexName"
                :label="item.indexName"
                :value="index"
              />
            </ElSelect>
            <ElCascader
              v-model="material"
              :options="materialConfig?.materialOption"
              :props="{ ...materialCascaderProps, multiple: materialConfig?.multiple }"
              :validate-event="false"
              class="ml-2"
              clearable
              filterable
              placeholder="请选择材质"
            />
            <ElButton class="ml-2" type="primary" @click="handleSetMaterial">一键设置</ElButton>
            <ElButton @click="handleResetMaterial">重置</ElButton>
            <DynamicColumnsSetting
              v-model="colSettingVisible"
              :disabled-columns="disabledColumns"
              :table-ref="tableRef"
              @change="handleColumnChange"
            />
          </div>
          <ElFormItem label-width="0" prop="skcProofMaterial">
            <VxeTable
              ref="tableRef"
              :cell-config="{ height: 80 }"
              :data="formData.skcProofMaterial"
              :edit-config="{ showIcon: false }"
              :edit-rules="tableRules"
              :highlight-current-row="false"
              :max-height="600"
              :mouse-config="{ selected: true }"
              :scroll-x="{ enabled: false }"
              :scroll-y="{ enabled: true, gt: 20 }"
              :valid-config="{ showMessage: false, autoPos: false }"
              class="w-full"
            >
              <VxeColumn key="checkbox" fixed="left" type="checkbox" width="40" />
              <VxeColumn key="seq" fixed="left" title="序号" type="seq" width="60" />
              <VxeColumn
                key="skcColorIdItemName"
                field="skcColorIdItemName"
                fixed="left"
                title="选中颜色"
                width="160"
              />
              <VxeColumn
                key="skcColorThumbnail"
                :cell-render="{ name: 'Image' }"
                field="skcColorThumbnail"
                fixed="left"
                title="颜色缩略图"
                width="160"
              />
              <VxeColumn
                v-if="!isConfirm"
                key="colorSchemeDraftUrl"
                :cell-render="{ name: 'Image' }"
                field="colorSchemeDraftUrl"
                fixed="left"
                title="配色图稿"
                width="160"
              />
              <VxeColumn
                v-else
                key="colorSchemeDraftUrlConfirm"
                class-name="upload-cell"
                field="colorSchemeDraftUrl"
                fixed="left"
                title="配色图稿"
                width="160"
              >
                <template #default="{ row }: { row: ColorConfirmSampleInfoAPI.BaseProofMaterial }">
                  <OssUpload
                    v-model="row.colorSchemeDraftUrl"
                    :limit="1"
                    :size-limit="1024 * 1024 * 100"
                    drag
                  />
                </template>
              </VxeColumn>
              <VxeColumn
                key="skcMainFabricItemName"
                field="skcMainFabricItemName"
                title="主要面料"
                width="160"
              />
              <VxeColumn :edit-render="{}" field="materialRemark" title="材料编号说明" width="160">
                <template #default="{ row }: { row: ColorConfirmSampleInfoAPI.BaseProofMaterial }">
                  <ElInput
                    v-model="row.materialRemark"
                    :autosize="{ maxRows: 2 }"
                    :validate-event="false"
                    maxlength="200"
                    show-word-limit
                    type="textarea"
                  />
                </template>
              </VxeColumn>
              <template v-for="item in filteredMaterialConfigList" :key="item.indexName">
                <VxeColumn
                  :edit-render="{}"
                  :field="`materialInfo.${item.index}.${
                    item.multiple ? 'materialList' : 'material'
                  }`"
                  :title="item.indexName"
                  :visible="validFabricTypeList.includes(item.indexName!)"
                  width="160"
                >
                  <template
                    #default="{ row }: { row: ColorConfirmSampleInfoAPI.BaseProofMaterial }"
                  >
                    <ElCascader
                      :model-value="
                        row.materialInfo![item.index][item.multiple ? 'materialList' : 'material']
                      "
                      :options="item.materialOption"
                      :props="{ ...materialCascaderProps, multiple: item.multiple }"
                      :validate-event="false"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      @change="
                        (val) => {
                          if (item.multiple) {
                            row.materialInfo![item.index].materialList = val as number[]
                          } else {
                            row.materialInfo![item.index].material = val as number
                          }
                        }
                      "
                    />
                  </template>
                </VxeColumn>
                <VxeColumn
                  :edit-render="{}"
                  :field="`materialInfo.${item.index}.color`"
                  :title="item.indexName + '-颜色'"
                  :visible="validFabricTypeList.includes(item.indexName!)"
                  width="160"
                >
                  <template
                    #default="{ row }: { row: ColorConfirmSampleInfoAPI.BaseProofMaterial }"
                  >
                    <ElInput
                      v-model="row.materialInfo![item.index].colorItemName"
                      :validate-event="false"
                      placeholder="请选择"
                      readonly
                      @click="handleOpenColorInfoDialog(row, item.index)"
                    >
                      <template #prefix>
                        <Icon
                          :size="26"
                          class="cursor-pointer"
                          color="#409EFF"
                          icon="mdi:search"
                          @click="handleOpenColorInfoDialog(row, item.index)"
                        />
                      </template>
                    </ElInput>
                  </template>
                </VxeColumn>
                <VxeColumn
                  v-if="validFabricTypeList.includes(item.indexName!) || item.indexName === FabricTypeEnum.SHOELACE || item.indexName === FabricTypeEnum.DECORATION"
                  :field="`materialInfo.${item.index}.remark`"
                  :title="item.indexName === '面料1' ? '面材料-备注' : item.indexName + '-备注'"
                  :visible="validFabricTypeList.includes(item.indexName!)"
                  width="200"
                >
                  <template
                    #default="{ row }: { row: ColorConfirmSampleInfoAPI.BaseProofMaterial }"
                  >
                    <ElInput
                      v-model="row.materialInfo![item.index].remark"
                      :autosize="{ maxRows: 2 }"
                      :validate-event="false"
                      maxlength="200"
                      show-word-limit
                      type="textarea"
                    />
                  </template>
                </VxeColumn>
              </template>
              <VxeColumn
                key="insoleLogo"
                :edit-render="{}"
                field="insoleLogo"
                title="鞋垫Logo"
                width="160"
              >
                <template #default="{ row }: { row: ColorConfirmSampleInfoAPI.BaseProofMaterial }">
                  <ElInput
                    v-model="row.insoleLogo"
                    :autosize="{ maxRows: 2 }"
                    :validate-event="false"
                    maxlength="100"
                    show-word-limit
                    type="textarea"
                  />
                </template>
              </VxeColumn>
              <VxeColumn
                key="requiredNumber"
                :edit-render="{}"
                field="requiredNumber"
                title="需求数量（双）"
                width="160"
              >
                <template #default="{ row }: { row: ColorConfirmSampleInfoAPI.BaseProofMaterial }">
                  <ElInputNumber
                    v-model="row.requiredNumber"
                    :controls="false"
                    :min="0"
                    :step="0.5"
                    :validate-event="false"
                    class="!w-24"
                    step-strictly
                  />
                </template>
              </VxeColumn>
              <VxeColumn key="remark" field="remark" title="备注" width="200">
                <template #default="{ row }: { row: ColorConfirmSampleInfoAPI.BaseProofMaterial }">
                  <ElInput
                    v-model="row.remark"
                    :autosize="{ maxRows: 2 }"
                    :validate-event="false"
                    maxlength="200"
                    show-word-limit
                    type="textarea"
                  />
                </template>
              </VxeColumn>
            </VxeTable>
          </ElFormItem>
        </ElCollapseItem>
        <ElCollapseItem v-else name="2" title="配色信息">
          <div class="mb-2">
            <ElButton type="primary" @click="addSkcDialogVisible = true">添加配色</ElButton>
            <ElButton type="primary" @click="handleRemove">移除</ElButton>
          </div>
          <VxeTable
            ref="tableRef"
            :cell-config="{ height: 80 }"
            :data="formData.skcProofMaterial"
            :edit-config="{ showIcon: false }"
            :edit-rules="tableRules"
            :max-height="600"
            :valid-config="{ showMessage: false, autoPos: false }"
          >
            <VxeColumn fixed="left" type="checkbox" width="40" />
            <VxeColumn fixed="left" title="序号" type="seq" width="60" />
            <VxeColumn field="skcColorIdItemName" fixed="left" title="选中颜色" />
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="skcColorThumbnail"
              fixed="left"
              title="颜色缩略图"
            />
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="colorSchemeDraftUrl"
              fixed="left"
              title="配色图稿"
            />
            <VxeColumn field="skcMainFabricItemName" title="主要面料" />
          </VxeTable>
        </ElCollapseItem>
      </ElCollapse>
    </ElForm>
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <template v-if="!isHistory">
        <template v-if="isView">
          <ElButton
            v-if="CommandPermission[CommandEnums.EDIT].includes(props.currentRow?.status!)"
            type="primary"
            @click="handleEdit"
          >
            编辑
          </ElButton>
        </template>
        <template v-else>
          <ElButton :loading="submitLoading" type="primary" @click="handleSave">确定</ElButton>
        </template>
      </template>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.row-container .el-col .el-form-item--default) {
  margin-bottom: 10px;
}

:deep(.upload-cell) {
  .el-upload {
    width: 60px;
    height: 60px;
  }

  .el-upload-list__item {
    width: 60px;
    height: 60px;
  }
}
</style>
