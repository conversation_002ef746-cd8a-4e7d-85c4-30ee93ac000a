<script lang="ts" setup>
import { SampleEnums } from '../../const'
import { commonYesNoMap } from '@/enums'
import { SampleInfoAPI, syncConfirmSample } from '../../api/SampleInfo'
import { type VxeGridProps } from 'vxe-table'
import { ElMessage } from 'element-plus'
import { SampleListPageAPI } from '@/views/basic-library-manage/sample-manage/api/SampleList'
import CommonSample from '@/views/basic-library-manage/sample-manage/components/CommonSample.vue'
import { ref } from 'vue'

defineOptions({
  name: 'ColorSampleInfo'
})
const props = defineProps<{
  sampleInfo: SampleInfoAPI.ProofInfoResp
}>()
const emit = defineEmits<{
  (e: 'refresh'): void
}>()
const tableData = ref()
watch(
  () => props.sampleInfo,
  () => {
    tableData.value = props.sampleInfo.colorProofList || []
  }
)
const sampleRef = ref()
const syncLoading = ref(false)
const handleSyncConfirm = async () => {
  const selectedRows: SampleInfoAPI.BaseProof[] | undefined =
    sampleRef.value?.tableRef?.getCheckboxRecords()
  if (!selectedRows?.length) {
    ElMessage.warning('请选择齐色样单')
    return
  }
  syncLoading.value = true
  const [error, result] = await syncConfirmSample({
    proofIdList: selectedRows.map((item) => item.id!)
  })
  syncLoading.value = false
  if (error === null && result) {
    ElMessage.success(result.msg || '创建成功')
    emit('refresh')
  }
}
const columns = ref([
  {
    type: 'checkbox',
    fixed: 'left',
    width: 40
  },
  {
    title: '缩略图',
    width: 100,
    field: 'thumbnail',
    cellRender: {
      name: 'Image'
    }
  },
  {
    field: 'code',
    title: '齐色样单编号',
    slots: {
      default: 'code'
    }
  },
  {
    title: '结构化',
    field: 'structured',
    cellRender: { name: 'Dict', props: { dictMap: commonYesNoMap } }
  },
  {
    title: '齐色样单名称',
    field: 'name'
  },
  {
    field: 'supplier',
    title: '供应商',
    slots: {
      default: 'supplier'
    }
  },
  {
    field: 'proofingSheet',
    title: '齐色样打样单',
    slots: {
      default: 'proofingSheet'
    }
  },
  {
    title: '齐色样打样日期',
    width: 80,
    field: 'sampleDate'
  },
  {
    title: '说明',
    field: 'remark'
  },
  {
    title: '状态',
    field: 'statusItemName'
  },
  {
    title: '操作',
    fixed: 'right',
    width: 80,
    slots: {
      default: 'action'
    }
  }
])
const tableOptions = computed<VxeGridProps<SampleListPageAPI.Row>>(() => ({
  columns: columns.value,
  data: tableData.value,
  maxHeight: 400,
  showOverflow: 'tooltip',
  cellConfig: {
    height: 80
  }
}))
</script>

<template>
  <CommonSample
    @refresh="emit('refresh')"
    :tableOptions="tableOptions"
    :tableData="tableData"
    ref="sampleRef"
    :params="{
      ...props.sampleInfo
    }"
    :type="SampleEnums.COLOR"
  >
    <template #button>
      <ElButton
        v-hasPermi="['addConfirmSample']"
        :loading="syncLoading"
        class="mb-2"
        type="primary"
        @click="handleSyncConfirm"
      >
        同步创建确认样单
      </ElButton>
    </template>
  </CommonSample>
</template>

<style lang="less" scoped></style>
