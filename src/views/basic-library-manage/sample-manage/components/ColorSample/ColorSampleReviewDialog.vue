<script lang="ts" setup>
import {
  ColorConfirmSampleInfoReviewInfoAPI,
  colorConfirmSampleReview,
  ColorConfirmSampleReviewAPI,
  getColorConfirmSampleReviewInfo,
  getSampleFollowList,
  InitSampleReviewAPI,
  SampleInfoAPI,
  SampleReviewInfoAPI,
  saveResultConfirmColorProof
} from '../../api/SampleInfo'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { cloneDeep, pick } from 'lodash-es'
import { VxeTableInstance } from 'vxe-table'
import { Icon } from '@/components/Icon'
import { CommonPassFailEnums } from '@/enums'

defineOptions({
  name: 'ColorSampleReviewDialog'
})

const props = defineProps<{
  currentRow?: SampleInfoAPI.BaseProof
  modelValue: boolean
  isConfirm?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const title = computed(() => (props.isConfirm ? '确认样' : '齐色样'))

const formRef = ref<FormInstance>()
const initForm: ColorConfirmSampleReviewAPI.Request & SampleReviewInfoAPI.ProofFollowDetail = {
  referenceProductCode: '',
  addInformationImg: [],
  again: undefined,
  designerProcessingOpinions: '',
  id: undefined,
  initialSampleReviewOpinions: '',
  preliminaryConclusion: '',
  proofCode: '',
  proofId: undefined,
  sampleAcceptDate: '',
  sampleImages: [],
  skcId: undefined,
  // appearanceOpinions: '',
  associatedHeelInfoCode: '',
  associatedInsoleInfoCode: '',
  associatedLastTypeCode: '',
  associatedSoleInfoCode: '',
  confirmationReport: undefined,
  developmentContactPersonId: [],
  developmentContactPersonIdItemName: '',
  developmentEvaluationOpinions: '',
  expectedCompletionDate: '',
  followId: undefined,
  footbedTechnique: '',
  lastBottomType: '',
  lastBottomTypeItemName: '',
  newMoldDict: '',
  newMoldDictItemName: '',
  originalShoesAcceptDate: '',
  outsourcingAcceptDate: '',
  outsourcingIssueDate: '',
  productionTechnique: '',
  recordCode: '',
  sampleCompletionDate: '',
  sampleInformationRecord: '',
  sampleNumber: undefined,
  sampleProcessDict: '',
  sampleProcessDictItemName: '',
  sampleResultDict: '',
  sampleResultDictItemName: '',
  sampleSendDate: '',
  shoeSoleTechnique: '',
  technicalContactPersonId: [],
  technicalContactPersonIdItemName: '',
  technicalEvaluationOpinions: '',
  technologyAssessmentReport: undefined,
  proofResultList: []
}
const formData = ref<InitSampleReviewAPI.Request>(cloneDeep(initForm))
const formRules = computed<FormRules<InitSampleReviewAPI.Request>>(() => ({
  again: [
    {
      required: tableData.value.every(
        (e) => e.proofResult?.preliminaryConclusion === CommonPassFailEnums.FAIL
      ),
      message: '请选择是否重新打样'
    }
  ],
  proofResultList: [
    {
      asyncValidator: async (_rule, _value, callback) => {
        if (!tableData.value) {
          callback(new Error(`请填写${title.value}评审信息`))
          return
        }
        const errorMsg = await tableRef.value?.validate?.(true).catch(() => false)
        if (errorMsg) {
          const { row, column, rule } = Object.values(errorMsg)[0][0]
          await tableRef.value?.scrollToRow(row, column)
          await tableRef.value?.setSelectCell(row, column)
          callback(new Error(rule.message))
          return
        }
        callback()
      }
    }
  ]
}))
const followList = ref<SampleReviewInfoAPI.ProofFollowDetail[]>([])
const currentFollowId = ref<number>()

const tableRef = ref<VxeTableInstance>()
const tableData = ref<ColorConfirmSampleInfoReviewInfoAPI.ConfirmColorResult[]>([])

const queryLoading = ref(false)
const handleFollowIdChange = async (val) => {
  queryLoading.value = true
  formData.value = cloneDeep(initForm)
  tableData.value = []
  const currentFollow = followList.value.find((item) => item.followId === val)
  if (currentFollow) {
    formData.value = {
      ...currentFollow
    }
  }
  const [error, result] = await getColorConfirmSampleReviewInfo({
    proofId: props.currentRow?.id,
    followId: val
  })
  queryLoading.value = false
  if (!error && result?.datas) {
    formData.value.again = result.datas.again
    tableData.value = result.datas.resultList || []
    tableRef.value?.loadData(tableData.value)
  }
}

watch(
  () => visible.value,
  async (val) => {
    if (val && props.currentRow?.id) {
      queryLoading.value = true
      const [error, result] = await getSampleFollowList(props.currentRow.id)
      queryLoading.value = false
      if (!error && result?.datas) {
        followList.value = result.datas.followUpList || []
        const lastFollow = result.datas.followUpList?.at(-1)
        currentFollowId.value = lastFollow?.followId
        await handleFollowIdChange(lastFollow?.followId)
      }
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const handleClose = () => {
  formData.value = cloneDeep(initForm)
  nextTick(formRef.value?.clearValidate)
  visible.value = false
}

const isHistoryFollow = computed(() => currentFollowId.value !== followList.value.at(-1)?.followId)

const isFinish = computed(() => {
  return props.currentRow?.status === SampleInfoAPI.Status.FINISH
})
const isCancel = computed(() => {
  return props.currentRow?.status === SampleInfoAPI.Status.CANCEL
})

const isRework = computed(() => {
  return props.currentRow?.status === SampleInfoAPI.Status.AGAIN
})

const buttonDisabled = computed(
  () => isHistoryFollow.value || isFinish.value || isCancel.value || isRework.value
)

const formDisabled = computed(() => hasReviewed.value || buttonDisabled.value)

const submitLoading = ref(false)

const submit = async (message: string, api: Function) => {
  const params = {
    proofId: props.currentRow?.id,
    again: formData.value.again,
    proofResultList: tableData.value.map((e) => {
      const params = pick(e.proofResult, [
        'id',
        'proofId',
        'proofCode',
        'preliminaryConclusion',
        'initialSampleReviewOpinions',
        'designerProcessingOpinions',
        'sampleAcceptDate',
        'addInformationImg',
        'sampleImages'
      ])
      return {
        ...params,
        followId: currentFollowId.value,
        skcId: e.proofSkcInfo?.skcId
      }
    })
  }
  submitLoading.value = true
  const [error, result] = await api(params)
  submitLoading.value = false
  if (!error && result) {
    ElMessage.success(result.msg || message)
    handleClose()
    emit('refresh')
  }
}

const handleSubmit = async () => {
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) return
  submit('提交成功', colorConfirmSampleReview)
}
const handleUpdate = async () => {
  const errorMsg = await tableRef.value?.validateField(true, ['proofResult.sampleImages'])
  if (errorMsg) {
    const { row, column } = Object.values(errorMsg)[0][0]
    await tableRef.value?.scrollToRow(row, column)
    await tableRef.value?.setSelectCell(row, column)
    return false
  }
  submit('更新成功', saveResultConfirmColorProof)
}
const handleSave = async () => {
  submit('暂存成功', saveResultConfirmColorProof)
}

const hasReviewed = computed(() => tableData.value.some((e) => e.proofResult?.commit))

const handleSyncResult = () => {
  const firstRow = tableData.value.at(0)
  if (firstRow) {
    const preliminaryConclusion = firstRow.proofResult?.preliminaryConclusion
    tableData.value = tableData.value.map((e) => ({
      ...e,
      proofResult: {
        ...e.proofResult,
        preliminaryConclusion
      }
    }))
    tableRef.value?.loadData(tableData.value)
  }
}

const activeNames = ref(['1', '2'])
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :title="title + '打样评审'"
    append-to-body
    max-height="calc(100vh - 10vh - 120px - var(--el-dialog-padding-primary) - 50px)"
    top="5vh"
    width="1460px"
  >
    <ElScrollbar v-if="followList.length">
      <ElRadioGroup v-model="currentFollowId" class="mb-2" @change="handleFollowIdChange">
        <ElRadioButton
          v-for="item in followList"
          :key="item.followId"
          :label="`打样跟进-${item.recordCode}`"
          :value="item.followId"
        />
      </ElRadioGroup>
    </ElScrollbar>
    <ElForm
      ref="formRef"
      v-loading="queryLoading"
      :model="formData"
      :rules="formRules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      label-width="auto"
      scroll-to-error
    >
      <ElCollapse v-model="activeNames">
        <ElCollapseItem :title="`${title}打样详情`" name="1">
          <ElRow :gutter="10" class="row-container">
            <ElCol :span="8">
              <ElFormItem label="跟进开发人员">
                <ElText>
                  {{ formData.developmentContactPersonIdItemName }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="跟进技术人员">
                <ElText>
                  {{ formData.technicalContactPersonIdItemName }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="外发打样日期">
                <ElText>
                  {{ formData.outsourcingIssueDate }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="工厂接收日期">
                <ElText>
                  {{ formData.outsourcingAcceptDate }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="打样跟进结果">
                <ElText>
                  {{ formData.sampleResultDictItemName }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="打样完成日期">
                <ElText>
                  {{ formData.sampleCompletionDate }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="工厂预计完成时间">
                <ElText>
                  {{ formData.expectedCompletionDate }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="样品寄送日期">
                <ElText>
                  {{ formData.sampleSendDate }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="技术评审报告">
                <OssUpload v-model="formData.technicalReviewReport" disabled list-type="text" />
              </ElFormItem>
            </ElCol>
            <template v-if="props.isConfirm">
              <ElCol :span="8">
                <ElFormItem label="制作工艺">
                  <ElText>
                    {{ formData.productionTechnique }}
                  </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="鞋底工艺">
                  <ElText>
                    {{ formData.shoeSoleTechniqueItemName }}
                  </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="垫脚工艺">
                  <ElText>
                    {{ formData.footbedTechnique }}
                  </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="确认样报告">
                  <OssUpload :model-value="formData.confirmationReport" disabled list-type="text" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="产前技术评估报告">
                  <OssUpload
                    :model-value="
                      formData.riskQmsObjectName
                        ? [
                            {
                              signatureUrl: formData.riskQmsObjectName,
                              fileName: formData.riskQmsObjectName
                            }
                          ]
                        : []
                    "
                    disabled
                    list-type="text"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="参考产品编号">
                  <ElText> {{ formData.referenceProductCode }} </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="是否新开模">
                  <ElText>
                    {{ formData.newMoldDictItemName }}
                  </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="楦底类型">
                  <ElText>
                    {{ formData.lastBottomTypeItemName }}
                  </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="产品楦型">
                  <ElText>
                    {{ formData.associatedLastTypeCode }}
                  </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="产品大底">
                  <ElText>
                    {{ formData.associatedSoleInfoCode }}
                  </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="产品鞋垫">
                  <ElText>
                    {{ formData.associatedInsoleInfoCode }}
                  </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="产品鞋跟">
                  <ElText>
                    {{ formData.associatedHeelInfoCode }}
                  </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="打样数量（只）">
                  <ElText>
                    {{ formData.sampleNumber }}
                  </ElText>
                </ElFormItem>
              </ElCol>
            </template>
          </ElRow>
        </ElCollapseItem>
        <ElCollapseItem :title="`${title}打样评审`" name="2">
          <ElRow :gutter="10">
            <ElCol :span="24">
              <ElFormItem label-width="0" prop="proofResultList">
                <VxeTable
                  ref="tableRef"
                  :data="tableData"
                  :edit-config="{ showIcon: false }"
                  :edit-rules="{
                    'proofResult.sampleImages': [{ required: true, message: '请上传样品图片' }],
                    'proofResult.preliminaryConclusion': [
                      { required: true, message: `请选择${title}评审结果` }
                    ]
                  }"
                  :highlight-current-row="false"
                  :max-height="600"
                  :mouse-config="{ selected: true }"
                  :valid-config="{ showMessage: false, autoPos: false }"
                  align="center"
                  class="w-full"
                >
                  <VxeColgroup title="产品配色基础信息">
                    <VxeColumn title="序号" type="seq" width="60" />
                    <VxeColumn field="proofSkcInfo.skcCode" title="SKC编号" width="100" />
                    <VxeColumn
                      field="proofSkcInfo.skcColorIdItemName"
                      title="产品配色"
                      width="100"
                    />
                    <VxeColumn
                      :cell-render="{ name: 'Image' }"
                      field="proofSkcInfo.skcColorThumbnail"
                      title="颜色缩略图"
                      width="100"
                    />
                    <VxeColumn
                      field="proofSkcInfo.skcMainFabricItemName"
                      title="主要面料"
                      width="100"
                    />
                    <VxeColumn
                      field="proofSkcInfo.colorTypeItemName"
                      title="配色类型"
                      width="100"
                    />
                    <VxeColumn
                      field="proofSkcInfo.requiredNumber"
                      title="打样数量（双）"
                      width="120"
                    />
                  </VxeColgroup>
                  <VxeColgroup title="打样跟进评审信息">
                    <VxeColumn
                      field="followResult.sampleMakingInfo"
                      show-overflow
                      title="打样信息记录"
                      width="140"
                    />
                    <VxeColumn
                      field="followResult.developmentAssessment"
                      show-overflow
                      title="开发评估意见"
                      width="140"
                    />
                    <VxeColumn
                      field="followResult.technicalAssessment"
                      show-overflow
                      title="技术评估意见"
                      width="140"
                    />
                    <!-- <VxeColumn
                      field="followResult.appearanceOpinion"
                      title="外观意见"
                      width="140"
                    /> -->
                  </VxeColgroup>
                  <VxeColgroup title="设计师评审信息">
                    <VxeColumn
                      :edit-render="{}"
                      field="proofResult.sampleImages"
                      title="样品图片"
                      width="120"
                    >
                      <template
                        #default="{
                          row
                        }: {
                          row: ColorConfirmSampleInfoReviewInfoAPI.ConfirmColorResult
                        }"
                      >
                        <div class="fileList">
                          <div
                            v-for="(item,index) in row.proofResult!.sampleImages"
                            :key="item.id"
                            class="flex flex-col items-center relative"
                            style="margin: 5px"
                          >
                            <div
                              class="cursor-pointer absolute top-0 right-0 w-4 h-4 z-50"
                              style="font-size: 18px; line-height: 18px; color: red"
                              @click="row.proofResult!.sampleImages?.splice(index, 1)"
                            >
                              <Icon class="mr-1" icon="ep:circle-close-filled" />
                            </div>
                            <ElImage
                              :preview-src-list="row.proofResult!.sampleImages?.map((e) => e.signatureUrl || '') || []"
                              :src="item.signatureUrl"
                              :initial-index="index"
                              hide-on-click-modal
                              fit="cover"
                              style="width: 80px; height: 80px"
                            />
                          </div>
                        </div>
                        <OssUpload
                          v-model="row.proofResult!.sampleImages"
                          :limit="20"
                          :multiple="true"
                          :show-file-list="false"
                          :size-limit="1024 * 1024 * 100"
                          accept="image/*"
                          drag
                          listType="text"
                        >
                          <template #trigger>
                            <Icon :size="22" color="var(--el-color-primary)" icon="mdi:upload" />
                          </template>
                        </OssUpload>
                      </template>
                    </VxeColumn>
                    <VxeColumn
                      :edit-render="{}"
                      :show-header-overflow="false"
                      :title="`${title}评审结果`"
                      field="proofResult.preliminaryConclusion"
                      width="140"
                    >
                      <template #header>
                        <span>{{ `${title}评审结果` }}</span>
                        <ElButton
                          link
                          type="primary"
                          :disabled="formDisabled"
                          @click="handleSyncResult"
                        >
                          一键设置
                        </ElButton>
                      </template>
                      <template
                        #default="{
                          row
                        }: {
                          row: ColorConfirmSampleInfoReviewInfoAPI.ConfirmColorResult
                        }"
                      >
                        <SelectPlus
                          v-model="row.proofResult!.preliminaryConclusion"
                          :disabled="row.proofResult?.commit || buttonDisabled"
                          api-key="COMMON_PASS_FAIL"
                        />
                      </template>
                    </VxeColumn>
                    <VxeColumn
                      :edit-render="{}"
                      :title="`${title}评审意见`"
                      field="proofResult.initialSampleReviewOpinions"
                      width="300"
                    >
                      <template
                        #default="{
                          row
                        }: {
                          row: ColorConfirmSampleInfoReviewInfoAPI.ConfirmColorResult
                        }"
                      >
                        <ElInput
                          v-model="row.proofResult!.initialSampleReviewOpinions"
                          :autosize="{ minRows: 2 }"
                          :disabled="row.proofResult?.commit || buttonDisabled"
                          maxlength="500"
                          show-word-limit
                          type="textarea"
                        />
                      </template>
                    </VxeColumn>
                    <VxeColumn
                      :edit-render="{}"
                      :title="`${title}处理结果`"
                      field="proofResult.designerProcessingOpinions"
                      width="300"
                    >
                      <template
                        #default="{
                          row
                        }: {
                          row: ColorConfirmSampleInfoReviewInfoAPI.ConfirmColorResult
                        }"
                      >
                        <ElInput
                          v-model="row.proofResult!.designerProcessingOpinions"
                          :autosize="{ minRows: 2 }"
                          :disabled="row.proofResult?.commit || buttonDisabled"
                          maxlength="500"
                          show-word-limit
                          type="textarea"
                        />
                      </template>
                    </VxeColumn>
                    <VxeColumn
                      :edit-render="{}"
                      field="proofResult.addInformationImg"
                      title="补充说明"
                      width="120"
                    >
                      <template
                        #default="{
                          row
                        }: {
                          row: ColorConfirmSampleInfoReviewInfoAPI.ConfirmColorResult
                        }"
                      >
                        <div
                          v-if="row.proofResult!.addInformationImg?.[0]"
                          class="flex flex-col items-center relative"
                          style="margin: 5px"
                        >
                          <div
                            v-if="!row.proofResult?.commit"
                            class="cursor-pointer absolute top-0 right-0 w-4 h-4"
                            style="font-size: 18px; line-height: 18px; color: red"
                            @click="row.proofResult!.addInformationImg?.splice(0, 1)"
                          >
                            <Icon class="mr-1" icon="ep:circle-close-filled" />
                          </div>
                          <ElImage
                            :preview-src-list="row.proofResult!.addInformationImg?.map((e) => e.signatureUrl || '') || []
    "
                            :src="row.proofResult!.addInformationImg?.[0].signatureUrl"
                            fit="contain"
                            style="width: 80px; height: 80px"
                            hide-on-click-modal
                          />
                        </div>
                        <OssUpload
                          v-model="row.proofResult!.addInformationImg"
                          :disabled="row.proofResult?.commit || formDisabled"
                          :limit="20"
                          :multiple="true"
                          :show-file-list="false"
                          :size-limit="1024 * 1024 * 100"
                          accept="image/*"
                          drag
                          listType="text"
                        >
                          <template #trigger>
                            <Icon :size="22" color="var(--el-color-primary)" icon="mdi:upload" />
                          </template>
                        </OssUpload>
                      </template>
                    </VxeColumn>
                  </VxeColgroup>
                </VxeTable>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="是否重新打样" prop="again">
                <SelectPlus
                  v-model="formData.again"
                  :disabled="formDisabled"
                  api-key="COMMON_YES_NO"
                  cache
                  radio
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCollapseItem>
      </ElCollapse>
    </ElForm>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton
        type="primary"
        v-if="hasReviewed || buttonDisabled"
        :loading="submitLoading"
        @click="handleUpdate"
        >更新</ElButton
      >
      <ElButton
        v-if="!hasReviewed"
        :disabled="buttonDisabled"
        :loading="submitLoading"
        type="primary"
        @click="handleSave"
      >
        保存
      </ElButton>
      <ElButton
        v-if="!hasReviewed"
        :disabled="isHistoryFollow"
        :loading="submitLoading"
        type="primary"
        @click="handleSubmit"
      >
        确定
      </ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}

:deep(.row-container .el-col .el-form-item--default) {
  margin-bottom: 10px;
}
</style>
