import {
  colorConfirmSampleCommit,
  copySampleInfo,
  deleteSampleInfo,
  initSampleCommit,
  InitSampleInfoAPI,
  invalidSample,
  revokeSample
} from '../../api/SampleInfo'
import { Ref } from 'vue'
import { VxeTableProps } from 'vxe-table'
import { CommandEnums, SampleEnums } from '../../const'
import { DialogTypeEnums } from '@/enums'
import {
  getMaterialCategoryList,
  MaterialCategoryListAPI
} from '../../../material-library/api/material-list'
import type { CascaderProps } from 'element-plus'
import { ElMessage } from 'element-plus'
import { MaterialCategoryEnum, StatusEnum } from '../../../const'
import { SampleListPageAPI } from '@/views/basic-library-manage/sample-manage/api/SampleList'

type HandleVersion = (row: SampleListPageAPI.Row) => void
type HandleCommand = (command: CommandEnums, row: SampleListPageAPI.Row) => Promise<string>
export interface HelperContent {
  currentRow: Ref<SampleListPageAPI.Row>
  dialogType: Ref<number>
  reviewDialogVisible: Ref<boolean>
  dialogVisible: Ref<boolean>
  versionDialogVisible: Ref<boolean>
  handleVersion: HandleVersion
  handleCommand: HandleCommand
  handleCreateSample: HandleVersion
  handleCreate: () => void
  createDialogVisible: boolean
}
export const useHelper = (
  tableData: WritableComputedRef<Ref<SampleListPageAPI.Row[]>>,
  tableRef: Ref<VxeTableProps | undefined>
) => {
  const currentRow = ref<SampleListPageAPI.Row>()

  /**
   * 编辑弹窗
   */
  const dialogType = ref<DialogTypeEnums>()
  const dialogVisible = ref(false)

  /**
   * 评审弹窗
   */
  const reviewDialogVisible = ref(false)

  /**
   * 添加弹窗
   */
  const createDialogVisible = ref(false)
  const handleCreate = () => {
    createDialogVisible.value = true
  }

  /**
   * 版本弹窗
   */
  const versionDialogVisible = ref(false)
  const handleVersion = (row: SampleListPageAPI.Row) => {
    currentRow.value = row
    versionDialogVisible.value = true
  }

  const handleCommand = async (command: CommandEnums, row: SampleListPageAPI.Row) => {
    if (command === CommandEnums.EDIT) {
      currentRow.value = row
      dialogType.value = DialogTypeEnums.EDIT
      dialogVisible.value = true
      return
    }
    if (command === CommandEnums.DETAIL) {
      currentRow.value = row
      dialogType.value = DialogTypeEnums.VIEW
      dialogVisible.value = true
      return
    }
    if (command === CommandEnums.DELETE) {
      const [error, result] = await deleteSampleInfo(row.id!)
      if (!error && result) {
        ElMessage.success(result.msg || '删除成功')
      }
      return
    }
    if (command === CommandEnums.COPY) {
      const [error, result] = await copySampleInfo(row.id!)
      if (!error && result) {
        ElMessage.success(result.msg || '复制成功')
      }
      return
    }
    if (command === CommandEnums.REVIEW) {
      currentRow.value = row
      reviewDialogVisible.value = true
      return
    }
    if (command === CommandEnums.SUBMIT) {
      const commitFn = row.type === SampleEnums.INIT ? initSampleCommit : colorConfirmSampleCommit
      const [error, result] = await commitFn(row.id!)
      if (!error && result) {
        ElMessage.success(result.msg || '提交成功')
      }
      return
    }
    if (command === CommandEnums.INVALID) {
      const [error, result] = await invalidSample(row.id!)
      if (!error && result) {
        ElMessage.success(result.msg || '提交成功')
      }
      return
    }
    if (command === CommandEnums.REVOKE) {
      const [error, result] = await revokeSample(row.id!)
      if (!error && result) {
        ElMessage.success(result.msg || '提交成功')
      }
    }
  }

  const handleCreateSample = (data: SampleListPageAPI.Row) => {
    tableData.value.push(data)
    tableRef.value?.loadData(tableData.value)
  }

  return {
    currentRow,
    dialogType,
    dialogVisible,
    reviewDialogVisible,
    versionDialogVisible,
    handleVersion,
    handleCommand,
    handleCreateSample,
    handleCreate,
    createDialogVisible
  }
}
export enum FabricTypeEnum {
  FABRIC1 = '面料1',
  FABRIC2 = '面料2',
  FABRIC3 = '面料3',
  FABRIC4 = '面料4',
  INSOLE_SURFACE = '鞋垫面',
  HEAD_HOLE = '头洞',
  EDGE_BAND = '包条',
  WATER_PLATFORM = '包水台',
  HEEL_COVER = '包跟',
  SEWING_THREAD = '车线',
  DECORATION = '装饰件',
  ZIPPER = '拉链',
  MIDSOLE_COVER = '包中底',
  SHOELACE = '鞋带',
  LINING_MATERIAL = '里材料',
  LINING_PLUSH = '里绒材料',
  OUTSOLE_MATERIAL = '底材料',
  HEEL = '跟',
  OUTSOLE = '大底',
  FOREFOOT = '掌面',
  INSOLE_MATERIAL = '垫材料',
  MIDSOLE = '中底',
  MOLDED_MIDSOLE = '成型中底',
  WOOD_FIBER_SOLE = '木糠底',
  INSOLE = '鞋垫'
}

export const commonFabricType: string[] = [
  FabricTypeEnum.FABRIC1,
  FabricTypeEnum.FABRIC2,
  FabricTypeEnum.FABRIC3,
  FabricTypeEnum.FABRIC4,
  FabricTypeEnum.INSOLE_SURFACE,
  FabricTypeEnum.HEAD_HOLE,
  FabricTypeEnum.EDGE_BAND,
  FabricTypeEnum.WATER_PLATFORM,
  FabricTypeEnum.HEEL_COVER,
  FabricTypeEnum.SEWING_THREAD,
  FabricTypeEnum.DECORATION,
  FabricTypeEnum.ZIPPER,
  FabricTypeEnum.MIDSOLE_COVER,
  FabricTypeEnum.SHOELACE
]

export const liningFabricType = [FabricTypeEnum.LINING_MATERIAL]

export const outsoleFabricType = [
  FabricTypeEnum.OUTSOLE_MATERIAL,
  FabricTypeEnum.HEEL,
  FabricTypeEnum.OUTSOLE,
  FabricTypeEnum.FOREFOOT
]

export const insoleFabricType = [
  FabricTypeEnum.INSOLE_MATERIAL,
  FabricTypeEnum.INSOLE,
  FabricTypeEnum.MIDSOLE,
  FabricTypeEnum.MOLDED_MIDSOLE,
  FabricTypeEnum.WOOD_FIBER_SOLE
]

export type MaterialConfig = InitSampleInfoAPI.SkuMaterialInfo & {
  multiple: boolean
  materialOption: MaterialCategoryListAPI.Data[]
}

function processMaterialList(items: MaterialCategoryListAPI.Data) {
  return items.map((e) => ({
    ...e,
    childList: e.childList ? processMaterialList(e.childList) : [],
    disabled: e.status !== StatusEnum.START
  }))
}

let queryMaterialCategoryLoading = false
const materialCategoryList = ref<MaterialCategoryListAPI.Data[]>([])
const fetchMaterialCategoryList = async () => {
  if (queryMaterialCategoryLoading) return
  queryMaterialCategoryLoading = true
  const [error, result] = await getMaterialCategoryList()
  queryMaterialCategoryLoading = false
  if (error === null && result?.datas) {
    materialCategoryList.value = processMaterialList(result.datas)
  }
}
const materialCascaderProps: CascaderProps = {
  label: 'selectorValue',
  value: 'selectorKey',
  disabled: 'disabled',
  children: 'childList',
  expandTrigger: 'hover' as const,
  emitPath: false
}

export const useMaterial = () => {
  // 面料1、2、3、4
  const commonMaterialCategoryList = computed(() => {
    const commonList: string[] = [
      MaterialCategoryEnum.PLASTIC,
      MaterialCategoryEnum.KPU,
      MaterialCategoryEnum.OTHERS,
      MaterialCategoryEnum.LEATHER,
      MaterialCategoryEnum.PU,
      MaterialCategoryEnum.PVC,
      MaterialCategoryEnum.TPU,
      MaterialCategoryEnum.TEXTILE
    ]
    return materialCategoryList.value.filter((e) => commonList.includes(e.selectorEnValue!))
  })
  // 里材料
  const liningMaterialCategoryList = computed(() => {
    const commonList: string[] = [MaterialCategoryEnum.LINING]
    return materialCategoryList.value.filter((e) => commonList.includes(e.selectorEnValue!))
  })
  // 垫材料
  const insoleMaterialCategoryList = computed(() => {
    const commonList: string[] = [MaterialCategoryEnum.INSOLE]
    return materialCategoryList.value.filter((e) => commonList.includes(e.selectorEnValue!))
  })
  // 底材料
  const outsoleMaterialCategoryList = computed(() => {
    const commonList: string[] = [MaterialCategoryEnum.OUTSOLE]
    return materialCategoryList.value.filter((e) => commonList.includes(e.selectorEnValue!))
  })
  const materialInfo = computed<MaterialConfig[]>(() => {
    return Object.values(FabricTypeEnum).map((e, index) => {
      if (commonFabricType.includes(e)) {
        return {
          color: undefined,
          colorItemName: '',
          indexName: e,
          index,
          material: undefined,
          materialItemName: '',
          materialRatio: '',
          remark: '',
          multiple: false,
          materialOption: commonMaterialCategoryList.value
        }
      }
      if (liningFabricType.includes(e)) {
        return {
          color: undefined,
          colorItemName: '',
          indexName: e,
          index,
          materialList: [],
          materialItemName: '',
          materialRatio: '',
          remark: '',
          multiple: true,
          materialOption: liningMaterialCategoryList.value
        }
      }
      if (outsoleFabricType.includes(e)) {
        return {
          color: undefined,
          colorItemName: '',
          indexName: e,
          index,
          materialList: [],
          materialItemName: '',
          materialRatio: '',
          remark: '',
          multiple: true,
          materialOption: outsoleMaterialCategoryList.value
        }
      }
      if (insoleFabricType.includes(e)) {
        return {
          color: undefined,
          colorItemName: '',
          indexName: e,
          index,
          materialList: [],
          materialItemName: '',
          materialRatio: '',
          remark: '',
          multiple: true,
          materialOption: insoleMaterialCategoryList.value
        }
      }
      return {
        color: undefined,
        colorItemName: '',
        indexName: e,
        index,
        material: undefined,
        materialItemName: '',
        materialRatio: '',
        remark: '',
        multiple: e === FabricTypeEnum.LINING_PLUSH,
        materialOption: materialCategoryList.value
      }
    })
  })

  // 需要校验的
  const validFabricTypeList: string[] = [
    FabricTypeEnum.FABRIC1,
    FabricTypeEnum.LINING_MATERIAL,
    FabricTypeEnum.OUTSOLE_MATERIAL,
    FabricTypeEnum.INSOLE_MATERIAL
  ]

  const validFabricConfig = computed(() => {
    return validFabricTypeList.map((e) => {
      const index = materialInfo.value.findIndex((i) => i.indexName === e)
      const materialConfig = materialInfo.value[index]
      return { index, materialConfig }
    })
  })

  fetchMaterialCategoryList()

  return {
    materialInfo,
    validFabricConfig,
    validFabricTypeList,
    materialCascaderProps,
    materialCategoryList,
    commonMaterialCategoryList,
    liningMaterialCategoryList,
    outsoleMaterialCategoryList,
    insoleMaterialCategoryList
  }
}
