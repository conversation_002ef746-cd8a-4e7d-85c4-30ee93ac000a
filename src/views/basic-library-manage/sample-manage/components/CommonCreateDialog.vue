<script lang="ts" setup>
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { SampleEnums } from '../const'
import {
  createColorSample,
  createConfirmSample,
  createInitSample,
  CreateSampleAPI,
  SampleInfoAPI
} from '../api/SampleInfo'
import { CommonYesNoEnums } from '@/enums'

defineOptions({
  name: 'CommonCreateDialog'
})

const props = defineProps<{
  type: SampleEnums
  currentRow?: SampleInfoAPI.ProofInfoResp
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', value: SampleInfoAPI.BaseProof): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const title = computed(() => {
  if (props.type === SampleEnums.INIT) {
    return '创建初样'
  }
  if (props.type === SampleEnums.COLOR) {
    return '创建齐色样'
  }
  if (props.type === SampleEnums.CONFIRM) {
    return '创建确认样'
  }
})

const formRef = ref<FormInstance>()
const formData = ref<CreateSampleAPI.Request>({
  name: '',
  productId: undefined,
  productNumber: '',
  structured: CommonYesNoEnums.No
})
const formRules = ref<FormRules<CreateSampleAPI.Request>>({
  name: [
    {
      required: true,
      message: '请输入打样单名称'
    }
  ],
  structured: [
    {
      required: true,
      message: '请选择是否结构化'
    }
  ]
})

const handleClose = () => {
  formData.value = {
    name: '',
    productId: undefined,
    productNumber: '',
    structured: CommonYesNoEnums.No
  }
  nextTick(formRef.value?.clearValidate)
  visible.value = false
}

const submitFn = computed(() => {
  if (props.type === SampleEnums.INIT) {
    return createInitSample
  }
  if (props.type === SampleEnums.COLOR) {
    return createColorSample
  }
  return createConfirmSample
})
const submitLoading = ref(false)
const handleSubmit = async () => {
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) return
  submitLoading.value = true
  const [error, result] = await submitFn.value({
    ...formData.value,
    productNumber: props.currentRow?.productNumber,
    productId: props.currentRow?.productId
  })
  submitLoading.value = false
  if (!error && result?.datas) {
    emit('submit', result.datas)
    ElMessage.success(result.msg || '创建成功')
    handleClose()
  }
}
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :title="title"
    width="700"
  >
    <ElForm
      class="m-4"
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      label-width="auto"
      scroll-to-error
    >
      <ElFormItem label="名称" prop="name">
        <ElInput v-model="formData.name" placeholder="请输入打样单名称" />
      </ElFormItem>
      <ElFormItem label="结构化" prop="structured">
        <SelectPlus v-model="formData.structured" api-key="COMMON_YES_NO" radio />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
