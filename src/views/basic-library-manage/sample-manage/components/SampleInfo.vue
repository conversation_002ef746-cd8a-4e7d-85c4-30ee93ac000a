<script lang="ts" setup>
import { DescriptionsSchema } from '@/types/descriptions'
import { InitSampleInfo } from './InitSample/'
import { ColorSampleInfo } from './ColorSample/'
import { getSampleInfo, SampleInfoAPI } from '../api/SampleInfo'
import { ConfirmSampleInfo } from './ConfirmSample'
import { InfringementRiskEnums } from '@/enums'

defineOptions({
  name: 'SampleInfo'
})

const router = useRouter()
const route = useRoute()

const id = computed(() => {
  return route.query.id
})

const formData = ref<SampleInfoAPI.ProofInfoResp>({
  applicableSeason: '',
  brand: undefined,
  colorProofList: [],
  confirmProofList: [],
  dtc: '',
  expectedLaunchDate: '',
  initialProofList: [],
  lastsStandard: '',
  launchSeason: '',
  productCategory: undefined,
  productId: undefined,
  productNumber: '',
  productStyle: '',
  styleStructure: '',
  styleWms: '',
  targetAudience: ''
})
const colorList = computed<string[]>(() => {
  return formData.value.colorUrlList?.map((e) => e.signatureUrl!) || []
})

const fetchSampleInfo = async () => {
  if (id.value) {
    const [error, result] = await getSampleInfo(+id.value)
    if (!error && result?.datas) {
      formData.value = result.datas
    }
  }
}

const productInfoSchema: DescriptionsSchema[] = [
  { field: 'productNumber', label: '产品编号' },
  { field: 'styleWms', label: 'Style（WMS）' },
  { field: 'brandItemName', label: '品牌' },
  { field: 'productCategoryItemName', label: '产品类目' },
  { field: 'launchSeasonItemName', label: '开发季节' },
  { field: 'productStyleItemName', label: '产品风格' },
  { field: 'targetAudienceItemName', label: '适用人群' },
  { field: 'styleStructureItemName', label: '款式结构' },
  { field: 'applicableSeasonItemName', label: '适用季节' },
  { field: 'expectedLaunchDate', label: '期望上架日期' },
  { field: 'lastsStandardItemName', label: '楦型标准' },
  { field: 'dtcItemName', label: '是否S2C' }
]

enum ActiveNames {
  PRODUCT_INFO = 'productInfo',
  FACTORY_INFO = 'factoryInfo',
  INIT_SAMPLE_INFO = 'initSampleInfo',
  COLOR_SAMPLE_INFO = 'colorSampleInfo',
  CONFIRM_SAMPLE_INFO = 'confirmSampleInfo'
}

const activeNames = ref([
  ActiveNames.PRODUCT_INFO,
  ActiveNames.FACTORY_INFO,
  ActiveNames.INIT_SAMPLE_INFO,
  ActiveNames.COLOR_SAMPLE_INFO,
  ActiveNames.CONFIRM_SAMPLE_INFO
])

const handleViewProductDetail = () => {
  router.push({ name: 'ViewProduct', query: { id: id.value } })
}

Promise.all([fetchSampleInfo()])
</script>

<template>
  <div class="reset-height">
    <ElCard body-class="!py-2" class="sticky top-0 mb-2">
      <ElScrollbar>
        <div class="flex flex-nowrap items-center w-full min-h-28 h-28 whitespace-nowrap">
          <ElImage
            hide-on-click-modal
            :preview-src-list="
              formData.thumbnail ? [formData.thumbnail]?.map((e) => e.signatureUrl!) : []
            "
            :src="formData.thumbnail?.signatureUrl"
            class="w-24 h-24 min-w-24 min-h-24 shadow-md shadow-gray-500/50"
            fit="contain"
            loading="lazy"
            preview-teleported
          />
          <div class="h-1/2 ml-4 mr-2 flex flex-col justify-between">
            <div class="text-center">
              {{ formData.productNumber }}
            </div>
            <div>
              <ElTag class="mr-2" effect="dark" round size="large">
                {{ formData.launchSeasonItemName }}
              </ElTag>
              <ElTag v-if="formData.designerIdItemName" effect="plain" round size="large">
                {{ formData.designerIdItemName }}
              </ElTag>
            </div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
            direction="vertical"
          />
          <div
            class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-xs leading-[24px] text-gray-500"
          >
            <div>当前阶段</div>
            <div>{{ formData.developStageItemName }} {{ formData.stageCompDesc }}</div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
            direction="vertical"
          />
          <div class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500">
            <div>打样供应商</div>
            <div>
              {{ formData.assignedFactoryItemName }}
            </div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
            direction="vertical"
          />
          <div
            class="flex-1 flex-grow-[2] flex-shrink-[2] h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500"
          >
            <div>产品颜色</div>
            <div class="flex flex-nowrap">
              <div v-for="item in colorList" :key="item" class="mr-2 w-6 h-6">
                <ElImage
                  :preview-src-list="colorList"
                  :src="item"
                  class="w-6 h-6"
                  fit="contain"
                  hide-on-click-modal
                  loading="lazy"
                  preview-teleported
                />
              </div>
            </div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
            direction="vertical"
          />
          <div class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500">
            <div>风险判定</div>
            <div
              :class="[
                formData.infringementRisk === InfringementRiskEnums.H ||
                formData.infringementRisk === InfringementRiskEnums.M
                  ? '!text-red-500'
                  : null
              ]"
              class="text-xs leading-[24px] text-gray-500"
            >
              {{ formData.infringementRiskItemName }}
              {{ formData.riskCountry }}
            </div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
            direction="vertical"
          />
          <div class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500">
            <div>当前状态</div>
            <div>{{ formData.dataStatusItemName }}</div>
          </div>
          <div class="flex flex-col ml-2">
            <ElButton size="large" @click="router.push({ name: 'ProductLibrary' })">
              返回产品清单
            </ElButton>
            <div class="h-1"></div>
            <ElButton class="!ml-0" size="large">下发数据到WMS</ElButton>
          </div>
        </div>
      </ElScrollbar>
    </ElCard>
    <ElCard :style="{ height: `calc(100% - 130px - 1rem)` }" body-class="h-full !py-0 !pb-2 !pr-0">
      <ElScrollbar>
        <ElCollapse v-model="activeNames">
          <ElCollapseItem :name="ActiveNames.PRODUCT_INFO" title="产品信息">
            <Descriptions :data="formData" :schema="productInfoSchema">
              <template #productNumber="{ row }">
                <ElButton link type="primary" @click="handleViewProductDetail">
                  {{ row.productNumber }}
                </ElButton>
              </template>
            </Descriptions>
          </ElCollapseItem>
          <ElCollapseItem :name="ActiveNames.FACTORY_INFO" title="打样工厂信息">
            <VxeTable :data="formData.factoryConfigList" :max-height="400" align="center">
              <VxeColumn title="序号" type="seq" width="60" />
              <VxeColumn field="assignedFactoryItemName" min-width="120" title="分配工厂" />
              <VxeColumn field="regionalSupplyPersonIdItemName" min-width="120" title="区域供管" />
              <VxeColumn
                field="developmentContactPersonIdItemName"
                min-width="120"
                title="跟进开发人员"
              />
              <VxeColumn
                field="technicalContactPersonIdItemName"
                min-width="120"
                title="跟进技术人员"
              />
              <VxeColumn field="regionItemName" min-width="100" title="区域" />
              <VxeColumn field="remark" title="备注" width="200" />
              <VxeColumn
                :cell-render="{ name: 'File' }"
                field="addInformationImg"
                title="补充说明"
                width="140"
              />
              <VxeColumn field="factoryQuotation" title="工厂报价(￥)含税" width="130" />
              <VxeColumn field="singleColorMoq" title="单色MOQ" width="100" />
              <VxeColumn field="singleStyleMoq" title="单款MOQ" width="100" />
              <VxeColumn field="modifyByIdItemName" title="操作人" width="120" />
              <VxeColumn field="modifyTime" title="操作时间" width="140" />
            </VxeTable>
          </ElCollapseItem>
          <ElCollapseItem :name="ActiveNames.INIT_SAMPLE_INFO" title="初样打样信息">
            <InitSampleInfo :sample-info="formData" @refresh="fetchSampleInfo" />
          </ElCollapseItem>
          <ElCollapseItem :name="ActiveNames.COLOR_SAMPLE_INFO" title="齐色样打样信息">
            <ColorSampleInfo :sample-info="formData" @refresh="fetchSampleInfo" />
          </ElCollapseItem>
          <ElCollapseItem :name="ActiveNames.CONFIRM_SAMPLE_INFO" title="确认样打样信息">
            <ConfirmSampleInfo :sample-info="formData" @refresh="fetchSampleInfo" />
          </ElCollapseItem>
        </ElCollapse>
      </ElScrollbar>
    </ElCard>
  </div>
</template>

<style lang="less" scoped>
.reset-height {
  height: calc(
    100vh - var(--app-footer-height) - var(--app-content-padding) * 2 - var(--top-tool-height) -
      var(--tags-view-height)
  );
}

:deep(.el-dropdown-link) {
  display: flex;
  font-size: 13px;
  color: var(--el-color-primary);
  cursor: pointer;
  align-items: center;
}

:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}
</style>
