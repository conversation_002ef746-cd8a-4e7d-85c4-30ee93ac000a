<script lang="ts" setup>
import { SampleEnums } from '../../const'
import { commonYesNoMap } from '@/enums'
import { SampleInfoAPI } from '../../api/SampleInfo'
import { type VxeGridProps } from 'vxe-table'
import CommonSample from '@/views/basic-library-manage/sample-manage/components/CommonSample.vue'
import { SampleListPageAPI } from '@/views/basic-library-manage/sample-manage/api/SampleList'

import { ref } from 'vue'
defineOptions({
  name: 'InitSampleInfo'
})
const props = defineProps<{
  sampleInfo: SampleInfoAPI.ProofInfoResp
}>()

const emit = defineEmits<{
  (e: 'refresh'): void
}>()

const tableData = ref()
watch(
  () => props.sampleInfo,
  () => {
    tableData.value = props.sampleInfo.initialProofList || []
  }
)
const columns = ref([
  {
    type: 'checkbox',
    fixed: 'left',
    width: 40
  },
  {
    title: '缩略图',
    width: 100,
    field: 'thumbnail',
    cellRender: {
      name: 'Image'
    }
  },
  {
    field: 'code',
    title: '初样单编号',
    slots: {
      default: 'code'
    }
  },
  {
    title: '结构化',
    field: 'structured',
    cellRender: { name: 'Dict', props: { dictMap: commonYesNoMap } }
  },
  {
    title: '初样单名称',
    field: 'name'
  },
  {
    title: '初样颜色',
    field: 'sampleColorItemName',
    width: 180
  },
  {
    field: 'supplier',
    title: '供应商',
    slots: {
      default: 'supplier'
    }
  },
  {
    field: 'proofingSheet',
    title: '初样打样单',
    slots: {
      default: 'proofingSheet'
    }
  },
  {
    title: '下发打样日期',
    width: 80,
    field: 'sampleDate'
  },
  {
    title: '说明',
    field: 'remark'
  },
  {
    title: '状态',
    field: 'statusItemName'
  },
  {
    title: '操作',
    fixed: 'right',
    width: 80,
    slots: {
      default: 'action'
    }
  }
])
const tableOptions = computed<VxeGridProps<SampleListPageAPI.Row>>(() => ({
  columns: columns.value,
  data: tableData.value,
  maxHeight: 400,
  showOverflow: 'tooltip',
  cellConfig: {
    height: 80
  }
}))
</script>

<template>
  <CommonSample
    @refresh="emit('refresh')"
    :tableOptions="tableOptions"
    :tableData="tableData"
    :params="{
      ...props.sampleInfo
    }"
    :type="SampleEnums.INIT"
  />
</template>

<style lang="less" scoped></style>
