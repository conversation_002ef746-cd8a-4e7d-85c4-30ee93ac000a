<script lang="ts" setup>
import { CommonYesNoEnums } from '@/enums'
import {
  getInitSampleReviewInfo,
  getSampleFollowList,
  initSampleReview,
  InitSampleReviewAPI,
  SampleInfoAPI,
  SampleReviewInfoAPI,
  saveResultInitial
} from '../../api/SampleInfo'
import { cloneDeep, pick } from 'lodash-es'
import { ElMessage, FormInstance, FormRules } from 'element-plus'

defineOptions({
  name: 'InitSampleReviewDialog'
})

const props = defineProps<{
  currentRow?: SampleInfoAPI.BaseProof
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const hasReviewed = computed(() => formData.value.commit)

const formRef = ref<FormInstance>()
const formRefImg = ref<FormInstance>()
const initForm: InitSampleReviewAPI.Request = {
  addInformationImg: [],
  again: undefined,
  designerProcessingOpinions: '',
  id: undefined,
  initialSampleReviewOpinions: '',
  preliminaryConclusion: '',
  proofCode: '',
  proofId: undefined,
  sampleAcceptDate: '',
  sampleImages: [],
  skcId: undefined,
  // appearanceOpinions: '',
  associatedHeelInfoCode: '',
  associatedInsoleInfoCode: '',
  associatedLastTypeCode: '',
  associatedSoleInfoCode: '',
  confirmationReport: undefined,
  developmentContactPersonId: [],
  developmentContactPersonIdItemName: '',
  developmentEvaluationOpinions: '',
  expectedCompletionDate: '',
  followId: undefined,
  footbedTechnique: '',
  lastBottomType: '',
  lastBottomTypeItemName: '',
  newMoldDict: '',
  newMoldDictItemName: '',
  originalShoesAcceptDate: '',
  outsourcingAcceptDate: '',
  outsourcingIssueDate: '',
  productionTechnique: '',
  recordCode: '',
  sampleCompletionDate: '',
  sampleInformationRecord: '',
  sampleNumber: undefined,
  sampleProcessDict: '',
  sampleProcessDictItemName: '',
  sampleResultDict: '',
  sampleResultDictItemName: '',
  sampleSendDate: '',
  shoeSoleTechnique: '',
  technicalContactPersonId: [],
  technicalContactPersonIdItemName: '',
  technicalEvaluationOpinions: '',
  technologyAssessmentReport: undefined
}
const formData = ref<InitSampleReviewAPI.Request>(cloneDeep(initForm))
const formRules = computed<FormRules<InitSampleReviewAPI.Request>>(() => ({
  sampleImages: [{ required: true, message: '请上传样品图片', trigger: 'change' }],
  preliminaryConclusion: [{ required: true, message: '请选择初样评审结果', trigger: 'change' }],
  initialSampleReviewOpinions: [
    {
      required: formData.value.preliminaryConclusion === CommonYesNoEnums.No,
      message: '请输入初样评审意见',
      trigger: 'blur'
    }
  ],
  again: [
    {
      required: formData.value.preliminaryConclusion === CommonYesNoEnums.No,
      message: '请选择是否重新打样',
      trigger: 'change'
    }
  ]
}))
const followList = ref<SampleReviewInfoAPI.ProofFollowDetail[]>([])
const currentFollowId = ref<number>()

const isHistoryFollowId = computed(
  () => currentFollowId.value !== followList.value.at(-1)?.followId
)
const isFinish = computed(() => {
  return props.currentRow?.status === SampleInfoAPI.Status.FINISH
})

const isCancel = computed(() => {
  return props.currentRow?.status === SampleInfoAPI.Status.CANCEL
})

const isRework = computed(() => {
  return props.currentRow?.status === SampleInfoAPI.Status.AGAIN
})

const buttonDisabled = computed(
  () => isHistoryFollowId.value || isFinish.value || isCancel.value || isRework.value
)

const formDisabled = computed(() => hasReviewed.value || buttonDisabled.value)

const queryLoading = ref(false)
const handleFollowIdChange = async (val) => {
  queryLoading.value = true
  formData.value = cloneDeep(initForm)
  const currentFollow = followList.value.find((item) => item.followId === val)
  if (currentFollow) {
    formData.value = {
      ...currentFollow
    }
  }
  const [error, result] = await getInitSampleReviewInfo({
    proofId: props.currentRow?.id,
    followId: val
  })
  queryLoading.value = false
  if (!error && result?.datas) {
    formData.value = {
      ...currentFollow,
      ...result.datas
    }
  }
}

watch(
  () => visible.value,
  async (val) => {
    if (val && props.currentRow?.id) {
      queryLoading.value = true
      const [error, result] = await getSampleFollowList(props.currentRow.id)
      queryLoading.value = false
      if (!error && result?.datas) {
        followList.value = result.datas.followUpList || []
        const lastFollow = result.datas.followUpList?.at(-1)
        currentFollowId.value = lastFollow?.followId
        await handleFollowIdChange(lastFollow?.followId)
      }
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const handleClose = () => {
  formData.value = cloneDeep(initForm)
  nextTick(formRef.value?.clearValidate)
  visible.value = false
}

const submitLoading = ref(false)

const submit = async (message: string, api: Function) => {
  submitLoading.value = true
  const params = pick(formData.value, [
    'id',
    'skcId',
    'preliminaryConclusion',
    'initialSampleReviewOpinions',
    'designerProcessingOpinions',
    'sampleAcceptDate',
    'addInformationImg',
    'sampleImages',
    'again',
    'sampleDate'
  ])

  const [error, result] = await api({
    ...params,
    proofId: props.currentRow?.id,
    proofCode: props.currentRow?.code,
    followId: currentFollowId.value
  })

  submitLoading.value = false
  if (!error && result) {
    ElMessage.success(result.msg || message)
    handleClose()
    emit('refresh')
  }
}

const handleSubmit = async () => {
  const valid =
    (await formRef.value?.validate().catch(() => false)) ||
    (await formRefImg.value?.validate().catch(() => false))
  if (!valid) return
  submit('初样打样评审成功', initSampleReview)
}
const handleUpdate = async () => {
  const valid = await formRefImg.value?.validate().catch(() => false)
  if (!valid) return
  submit('更新成功', saveResultInitial)
}

const handleSave = async () => {
  submit('初样打样暂存成功', saveResultInitial)
}

const activeNames = ref(['1', '2'])
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    append-to-body
    max-height="calc(100vh - 10vh - 120px - var(--el-dialog-padding-primary) - 50px)"
    title="初样打样评审"
    top="5vh"
    width="1460px"
  >
    <ElScrollbar v-if="followList.length">
      <ElRadioGroup v-model="currentFollowId" class="mb-2" @change="handleFollowIdChange">
        <ElRadioButton
          v-for="item in followList"
          :key="item.followId"
          :label="`打样跟进-${item.recordCode}`"
          :value="item.followId"
        />
      </ElRadioGroup>
    </ElScrollbar>
    <ElForm
      ref="formRef"
      v-loading="queryLoading"
      :disabled="formDisabled"
      :model="formData"
      :rules="formRules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      label-width="auto"
      scroll-to-error
    >
      <ElCollapse v-model="activeNames">
        <ElCollapseItem name="1" title="初样打样详情">
          <ElRow :gutter="10">
            <ElCol :span="12">
              <ElFormItem label="跟进开发人员">
                <ElText>
                  {{ formData.developmentContactPersonIdItemName }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="跟进技术人员">
                <ElText>
                  {{ formData.technicalContactPersonIdItemName }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="外发打样日期">
                <ElText>
                  {{ formData.outsourcingIssueDate }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="工厂接收日期">
                <ElText>
                  {{ formData.outsourcingAcceptDate }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="打样环节">
                <ElText>
                  {{ formData.sampleProcessDictItemName }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="原版鞋接收日期">
                <ElText>
                  {{ formData.originalShoesAcceptDate }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="打样跟进结果">
                <ElText>
                  {{ formData.sampleResultDictItemName }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="打样完成日期">
                <ElText>
                  {{ formData.sampleCompletionDate }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="打样数量（双）">
                <ElText>
                  {{ formData.initialSampleNumber }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="样品寄送日期">
                <ElText>
                  {{ formData.sampleSendDate }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="打样信息记录">
                <ElText>
                  {{ formData.sampleInformationRecord }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="开发评估意见">
                <ElText>
                  {{ formData.developmentEvaluationOpinions }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <!-- <ElCol :span="12">
              <ElFormItem label="外观意见">
                <ElText>
                  {{ formData.appearanceOpinions }}
                </ElText>
              </ElFormItem>
            </ElCol> -->
            <ElCol :span="12">
              <ElFormItem label="技术评估意见">
                <ElText>
                  {{ formData.technicalEvaluationOpinions }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="是否新开模">
                <ElText>
                  {{ formData.newMoldDictItemName }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="技术评审报告">
                <OssUpload v-model="formData.technicalReviewReport" disabled list-type="text" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="楦底类型">
                <ElText>
                  {{ formData.lastBottomTypeItemName }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="工厂预计完成时间">
                <ElText>
                  {{ formData.expectedCompletionDate }}
                </ElText>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCollapseItem>
        <ElCollapseItem name="2" title="初样评审">
          <ElRow :gutter="10">
            <ElCol :span="12">
              <ElForm
                ref="formRefImg"
                v-loading="queryLoading"
                :model="formData"
                :rules="formRules"
                label-width="auto"
              >
                <ElFormItem label="样品图片" prop="sampleImages">
                  <OssUpload
                    v-model="formData.sampleImages"
                    :limit="10"
                    :size-limit="1024 * 1024 * 100"
                    accept="image/*"
                    hide-on-click-modal
                    drag
                    listType="picture-card"
                    multiple
                  />
                </ElFormItem>
              </ElForm>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="初样评审结果" prop="preliminaryConclusion">
                <SelectPlus
                  v-model="formData.preliminaryConclusion"
                  api-key="COMMON_PASS_FAIL"
                  filterable
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="样品接收日期" prop="sampleAcceptDate">
                <ElDatePicker
                  v-model="formData.sampleAcceptDate"
                  clearable
                  placeholder="请选择样品接收日期"
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="初样评审意见" prop="initialSampleReviewOpinions">
                <ElInput
                  v-model="formData.initialSampleReviewOpinions"
                  :autosize="{ minRows: 4 }"
                  maxlength="500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="是否重新打样" prop="again">
                <SelectPlus v-model="formData.again" api-key="COMMON_YES_NO" cache radio />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="补充说明" prop="addInformationImg">
                <OssUpload
                  v-model="formData.addInformationImg"
                  :limit="10"
                  :size-limit="1024 * 1024 * 100"
                  accept="image/*"
                  drag
                  listType="picture-card"
                  multiple
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCollapseItem>
      </ElCollapse>
    </ElForm>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton
        type="primary"
        v-if="hasReviewed || buttonDisabled"
        :loading="submitLoading"
        @click="handleUpdate"
        >更新</ElButton
      >
      <ElButton
        v-if="!hasReviewed"
        :disabled="buttonDisabled"
        :loading="submitLoading"
        type="primary"
        @click="handleSave"
      >
        保存
      </ElButton>
      <ElButton
        v-if="!hasReviewed"
        :disabled="buttonDisabled"
        :loading="submitLoading"
        type="primary"
        @click="handleSubmit"
      >
        确定
      </ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}
</style>
