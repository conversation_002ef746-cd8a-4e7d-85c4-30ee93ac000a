<script lang="ts" setup>
import { CommonYesNoEnums, DialogTypeEnums } from '@/enums'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import {
  getInitSampleHistory,
  getInitSampleInfo,
  InitSampleInfoAPI,
  initSampleStore,
  SampleInfoAPI
} from '../../api/SampleInfo'
import ColorInfoDialog from '../../../components/ColorInfoDialog.vue'
import { ColorListPageAPI } from '../../../color-library/api/color-list'
import { Icon } from '@/components/Icon'
import { cloneDeep } from 'lodash-es'
import { FabricTypeEnum, useMaterial } from '../hooks'
import { VersionListAPI } from '@/views/basic-library-manage/api/common'
import { hasValue } from '@/utils'
import { CommandEnums, CommandPermission } from '@/views/basic-library-manage/sample-manage/const'
import { FactoryListAPI } from '@/components/Business/SelectPlus/src/api/types'
import { YesNoEnum } from '@/views/basic-library-manage/const'

defineOptions({
  name: 'InitSampleInfoDialog'
})

const props = defineProps<{
  type?: DialogTypeEnums
  currentRow?: SampleInfoAPI.BaseProof | VersionListAPI.Data
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit'): void
  (e: 'refresh'): void
}>()

const isHistory = computed(() => props.currentRow?.versionCode)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const isView = computed(() => props.type === DialogTypeEnums.VIEW)

const title = computed(() => {
  if (props.type === DialogTypeEnums.EDIT) {
    return '初样打样编辑'
  }
  if (isView.value) {
    return '初样打样详情'
  }
})

const validFabricTypeShow = ref({
  [FabricTypeEnum.FABRIC1]: false,
  [FabricTypeEnum.LINING_MATERIAL]: false,
  [FabricTypeEnum.OUTSOLE_MATERIAL]: false,
  [FabricTypeEnum.INSOLE_MATERIAL]: false
})

const {
  materialInfo,
  validFabricConfig,
  validFabricTypeList,
  materialCascaderProps,
  commonMaterialCategoryList
} = useMaterial()

const formRef = ref<FormInstance>()
const initForm: InitSampleInfoAPI.InitialProofDetailResp = {
  code: '',
  createById: undefined,
  createTime: '',
  delFlag: undefined,
  finishDate: '',
  id: undefined,
  insoleLogo: '',
  mainFabric: undefined,
  mainFabricItemName: '',
  materialInfo: [],
  modifyById: undefined,
  modifyTime: '',
  name: '',
  originalShoesImg: [],
  originalShoesNumber: undefined,
  productCategory: undefined,
  productCategoryItemName: '',
  productHeel: undefined,
  productHeelItemName: '',
  productId: undefined,
  productLast: undefined,
  productLastItemName: '',
  productNumber: '',
  productSizeRange: [],
  proofingImg: [],
  proofingSheet: undefined,
  proofMaterialId: undefined,
  requiredMark: '',
  requiredNumber: undefined,
  requiredOriginalShoes: '',
  sampleColor: undefined,
  sampleColorItemName: '',
  sampleDate: '',
  status: '',
  statusItemName: '',
  statusRemark: '',
  structured: '',
  supplier: undefined,
  supplyDate: '',
  thumbnail: undefined,
  type: ''
}
const formData = ref<InitSampleInfoAPI.InitialProofDetailResp>(cloneDeep(initForm))
const supplierList = ref<FactoryListAPI.Data[]>([])
const formRules = computed<FormRules<InitSampleInfoAPI.InitialProofDetailResp>>(() => ({
  name: [{ required: true, message: '请输入打样单名称', trigger: 'blur' }],
  sampleColor: [{ required: true, message: '请选择初样颜色', trigger: 'change' }],
  requiredNumber: [{ required: true, message: '请输入打样需求数量', trigger: 'blur' }],
  sampleDate: [{ required: true, message: '请选择下发打样日期', trigger: 'change' }],
  finishDate: [{ required: true, message: '请选择需求完成日期', trigger: 'change' }],
  proofingImg: [{ required: true, message: '请上传打样图', trigger: 'change' }],
  proofingSheet: [{ required: true, message: '请上传初样打样单', trigger: 'change' }],
  mainFabric: [{ required: true, message: '请选择主要面料', trigger: 'change' }],
  insoleLogo: [{ required: true, message: '请输入鞋垫Logo', trigger: 'change' }]
}))

const isStructured = computed(() => formData.value.structured === CommonYesNoEnums.Yes)
const statusArr = computed(() => formData.value.statusRemark?.split('-') || [])

const queryLoading = ref(false)
watch(
  () => visible.value,
  async (val) => {
    if (val) {
      if (isHistory.value) {
        const currentRow = props.currentRow as VersionListAPI.Data
        queryLoading.value = true
        const [error, result] = await getInitSampleHistory(currentRow.id)
        queryLoading.value = false
        if (!error && result?.datas) {
          formData.value = {
            ...result.datas,
            materialInfo: result.datas.materialInfo || cloneDeep(materialInfo.value)
          }
        }
      } else {
        const currentRow = props.currentRow as SampleInfoAPI.BaseProof
        queryLoading.value = true
        const [error, result] = await getInitSampleInfo(currentRow.id!)
        queryLoading.value = false
        if (!error && result?.datas) {
          formData.value = {
            ...result.datas,
            materialInfo:
              result.datas.materialInfo ||
              cloneDeep(materialInfo.value).map((e) => ({
                ...e,
                colorItemName: e.colorItemName || result.datas.sampleColorItemName,
                color: e.color || result.datas.sampleColor
              }))
          }
        }
      }
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const handleClose = () => {
  formData.value = cloneDeep(initForm)
  nextTick(formRef.value?.clearValidate)
  visible.value = false
}

const colorInfoDialogVisible = ref(false)
const currentIndex = ref()
const handleOpenColorInfoDialog = (index?: number) => {
  if (isView.value) {
    return
  }
  colorInfoDialogVisible.value = true
  currentIndex.value = index
}

const handlePickColor = (val: ColorListPageAPI.Row) => {
  if (currentIndex.value !== undefined) {
    formData.value.materialInfo[currentIndex.value].colorItemName = val.englishName || ''
    formData.value.materialInfo[currentIndex.value].color = val.id
  } else {
    formData.value.sampleColorItemName = val.englishName || ''
    formData.value.sampleColor = val.id
    formData.value.thumbnail = val.thumbnail
    formData.value.materialInfo = formData.value.materialInfo?.map((e) => ({
      ...e,
      colorItemName: val.englishName || '',
      color: val.id
    }))
  }
  currentIndex.value = undefined
}

const activeNames = ref(['1', '2'])

const loading = ref(false)
const handleEdit = () => {
  emit('edit')
}

const handleSave = async () => {
  loading.value = true
  const [error, result] = await initSampleStore({
    ...formData.value,
    materialInfo: formData.value.materialInfo.map((e) => ({
      ...e,
      materialOption: undefined
    }))
  })
  loading.value = false
  if (!error && result) {
    emit('refresh')
    ElMessage.success(result?.msg || '保存成功')
    handleClose()
  }
}
</script>

<template>
  <ColorInfoDialog v-model="colorInfoDialogVisible" @submit="handlePickColor" />
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :title="title"
    max-height="calc(100vh - 10vh - 120px - var(--el-dialog-padding-primary) - 50px)"
    top="5vh"
    width="1460px"
  >
    <ElForm
      ref="formRef"
      v-loading="queryLoading"
      :disabled="isView"
      :model="formData"
      :rules="formRules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      label-width="auto"
      scroll-to-error
    >
      <ElCollapse v-model="activeNames">
        <ElCollapseItem name="1" title="初样打样详情">
          <ElRow :gutter="10">
            <ElCol :span="12">
              <ElFormItem label="打样单编号">
                <ElText>
                  {{ formData.code }}
                </ElText>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="打样单名称" prop="name">
                <ElInput v-model="formData.name" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="状态">
                <span v-for="(item, index) in statusArr" :key="item + index">
                  <span :class="[index === statusArr.length - 1 ? 'font-bold' : '']">
                    {{ item }}
                  </span>
                  <span v-if="index !== statusArr.length - 1">-</span>
                </span>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="供应商" prop="supplier">
                <SelectPlus
                  v-model="formData.supplier"
                  :cascade-clear="false"
                  :data-method="
                    (val) => val.map((e) => ({ ...e, disabled: e.useStatus !== +YesNoEnum.Y }))
                  "
                  :params="formData.productId"
                  api-key="getSampleSupplierList"
                  filterable
                  ganged
                  @response-data="(val) => (supplierList = val)"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="下发打样日期" prop="sampleDate">
                <ElDatePicker
                  v-model="formData.sampleDate"
                  clearable
                  placeholder="请选择下发打样日期"
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="初样颜色" prop="sampleColor">
                <ElInput
                  v-model="formData.sampleColorItemName"
                  placeholder="请选择"
                  readonly
                  @click="handleOpenColorInfoDialog()"
                >
                  <template #prefix>
                    <Icon
                      :size="26"
                      class="cursor-pointer"
                      color="#409EFF"
                      icon="mdi:search"
                      @click="handleOpenColorInfoDialog()"
                    />
                  </template>
                </ElInput>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="打样需求数量（双）" prop="requiredNumber">
                <ElInputNumber
                  v-model="formData.requiredNumber"
                  :controls="false"
                  :min="0"
                  :step="0.5"
                  step-strictly
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="需求完成日期（DDL）" prop="finishDate">
                <ElDatePicker
                  v-model="formData.finishDate"
                  clearable
                  placeholder="年/月/日"
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
            </ElCol>
            <template v-if="isStructured">
              <ElCol :span="12">
                <ElFormItem label="Category（品类）">
                  <ElText>
                    {{ formData.productCategoryItemName }}
                  </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="打样图（至多6张）" prop="proofingImg">
                  <OssUpload
                    v-model="formData.proofingImg"
                    :limit="6"
                    :size-limit="1024 * 1024 * 100"
                    accept="image/*"
                    drag
                    listType="picture-card"
                    multiple
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="Size range（尺码段）">
                  <ElText>
                    {{ formData.productSizeRangeItemName }}
                  </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="大底编号">
                  <ElText>
                    {{ formData.productHeelItemName }}
                  </ElText>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="楦体编号">
                  <ElText>
                    {{ formData.productLastItemName }}
                  </ElText>
                </ElFormItem>
              </ElCol>
            </template>
            <template v-else>
              <ElCol :span="12">
                <ElFormItem label="初样打样单" prop="proofingSheet">
                  <OssUpload
                    :limit="1"
                    :model-value="formData.proofingSheet ? [formData.proofingSheet] : []"
                    :size-limit="1024 * 1024 * 100"
                    drag
                    list-type="text"
                    multiple
                    @update:model-value="(val) => (formData.proofingSheet = val[0])"
                  />
                </ElFormItem>
              </ElCol>
            </template>
            <ElCol :span="12">
              <ElFormItem label="是否提供原版鞋">
                <SelectPlus
                  v-model="formData.requiredOriginalShoes"
                  api-key="COMMON_YES_NO"
                  radio
                  @change="formData.originalShoesImg = []"
                />
              </ElFormItem>
            </ElCol>
            <template v-if="formData.requiredOriginalShoes === CommonYesNoEnums.Yes">
              <ElCol :span="12">
                <ElFormItem label="原版鞋数量（双）">
                  <ElInputNumber
                    v-model="formData.originalShoesNumber"
                    :controls="false"
                    :min="0"
                    :step="0.5"
                    step-strictly
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="提供日期">
                  <ElDatePicker
                    v-model="formData.supplyDate"
                    clearable
                    format="YYYY-MM-DD"
                    placeholder="年/月/日"
                    value-format="YYYY-MM-DD"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="原版鞋图">
                  <OssUpload
                    v-model="formData.originalShoesImg"
                    :limit="10"
                    :size-limit="1024 * 1024 * 100"
                    accept="image/*"
                    drag
                    list-type="picture-card"
                    multiple
                  />
                </ElFormItem>
              </ElCol>
            </template>
            <ElCol :span="24">
              <ElFormItem label="打样要求说明">
                <ElInput
                  v-model="formData.requiredMark"
                  :autosize="{ minRows: 4 }"
                  maxlength="500"
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCollapseItem>
        <ElCollapseItem v-if="isStructured" name="2" title="材料信息">
          <ElRow :gutter="10">
            <ElCol :span="24">
              <ElFormItem label="主要面料" prop="mainFabric">
                <ElCascader
                  v-model="formData.mainFabric"
                  :options="commonMaterialCategoryList"
                  :props="materialCascaderProps"
                  clearable
                  filterable
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElRow :gutter="10">
                <template
                  v-for="(item, index) in validFabricConfig"
                  :key="item.materialConfig.indexName"
                >
                  <ElCol :span="8">
                    <ElFormItem
                      :label="item.materialConfig.indexName"
                      :prop="`materialInfo.${item.index}.${
                        item.materialConfig.multiple ? 'materialList' : 'material'
                      }`"
                      :rules="[
                        {
                          required: true,
                          message: `请选择${item.materialConfig.indexName}`
                        }
                      ]"
                    >
                      <ElCascader
                        :model-value="
                          formData.materialInfo[item.index][
                            item.materialConfig.multiple ? 'materialList' : 'material'
                          ]
                        "
                        :options="item.materialConfig.materialOption"
                        :props="{
                          ...materialCascaderProps,
                          multiple: item.materialConfig.multiple
                        }"
                        clearable
                        filterable
                        @change="(val) => {
                          if (item.materialConfig.multiple) {
                            formData.materialInfo![item.index].materialList = val as number[]
                          } else {
                            formData.materialInfo![item.index].material = val as number
                          }
                        }"
                      />
                      <Icon
                        :class="
                          validFabricTypeShow[item.materialConfig.indexName!] ? 'rotate-90' : ''
                        "
                        class="transform transition duration-400 cursor-pointer"
                        icon="ep:arrow-down"
                        @click="
                          validFabricTypeShow[item.materialConfig.indexName!] =
                            !validFabricTypeShow[item.materialConfig.indexName!]
                        "
                      />
                    </ElFormItem>
                  </ElCol>
                  <ElCol :span="8">
                    <ElFormItem
                      :label="`${item.materialConfig.indexName}-颜色`"
                      :prop="`materialInfo.${item.index}.color`"
                      :rules="[
                        {
                          required: validFabricTypeList.includes(item.materialConfig.indexName!),
                          message: `请选择${item.materialConfig.indexName}颜色`
                        }
                      ]"
                    >
                      <ElInput
                        v-model="formData.materialInfo[item.index].colorItemName"
                        placeholder="请选择"
                        readonly
                        @click="handleOpenColorInfoDialog(item.index)"
                      >
                        <template #prefix>
                          <Icon
                            :size="26"
                            class="cursor-pointer"
                            color="#409EFF"
                            icon="mdi:search"
                            @click="handleOpenColorInfoDialog(item.index)"
                          />
                        </template>
                      </ElInput>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :span="8">
                    <ElFormItem
                      :label="`${
                        item.materialConfig.indexName === '面料1'
                          ? '面材料'
                          : item.materialConfig.indexName
                      }-备注`"
                    >
                      <ElInput
                        v-model="formData.materialInfo[item.index].remark"
                        :autosize="{ minRows: 4 }"
                        maxlength="200"
                        show-word-limit
                        type="textarea"
                      />
                    </ElFormItem>
                  </ElCol>
                  <ElCollapseTransition>
                    <ElCol v-show="validFabricTypeShow[item.materialConfig.indexName!]" :span="24">
                      <template
                        v-for="materialConfig in materialInfo.slice(
                          item.index,
                          validFabricConfig[index + 1]?.index
                        )"
                        :key="materialConfig.indexName"
                      >
                        <ElRow
                          v-if="!validFabricTypeList.includes(materialConfig.indexName!)"
                          :gutter="10"
                        >
                          <ElCol :span="8">
                            <ElFormItem :label="materialConfig.indexName">
                              <ElCascader
                                :model-value="
                                  formData.materialInfo[materialConfig.index][
                                    materialConfig.multiple ? 'materialList' : 'material'
                                  ]
                                "
                                :options="materialConfig.materialOption"
                                :props="{
                                  ...materialCascaderProps,
                                  multiple: materialConfig.multiple
                                }"
                                clearable
                                filterable
                                @change="(val) => {
                                  if (materialConfig.multiple) {
                                    formData.materialInfo![materialConfig.index].materialList = val as number[]
                                  } else {
                                    formData.materialInfo![materialConfig.index].material = val as number
                                  }
                                }"
                              />
                            </ElFormItem>
                          </ElCol>
                          <ElCol :span="8">
                            <ElFormItem
                              :label="`${materialConfig.indexName}-颜色`"
                              :prop="`materialInfo.${materialConfig.index}.color`"
                              :rules="[
                                {
                                  required: hasValue(
                                    formData.materialInfo[materialConfig.index][
                                      materialConfig.multiple ? 'materialList' : 'material'
                                    ]
                                  ),
                                  message: `请选择${materialConfig.indexName}颜色`
                                }
                              ]"
                            >
                              <ElInput
                                v-model="formData.materialInfo[materialConfig.index].colorItemName"
                                placeholder="请选择"
                                readonly
                                @click="handleOpenColorInfoDialog(materialConfig.index)"
                              >
                                <template #prefix>
                                  <Icon
                                    :size="26"
                                    class="cursor-pointer"
                                    color="#409EFF"
                                    icon="mdi:search"
                                    @click="handleOpenColorInfoDialog(materialConfig.index)"
                                  />
                                </template>
                              </ElInput>
                            </ElFormItem>
                          </ElCol>
                          <ElCol :span="8">
                            <ElFormItem
                              v-if="([FabricTypeEnum.SHOELACE, FabricTypeEnum.DECORATION] as string[]).includes(materialConfig.indexName!)"
                              :label="`${materialConfig.indexName}-备注`"
                            >
                              <ElInput
                                v-model="formData.materialInfo[materialConfig.index].remark"
                                :autosize="{ minRows: 4 }"
                                maxlength="200"
                                show-word-limit
                                type="textarea"
                              />
                            </ElFormItem>
                          </ElCol>
                        </ElRow>
                      </template>
                    </ElCol>
                  </ElCollapseTransition>
                </template>
              </ElRow>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="鞋垫Logo" prop="insoleLogo">
                <ElInput
                  v-model="formData.insoleLogo"
                  :autosize="{ minRows: 4 }"
                  maxlength="100"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCollapseItem>
      </ElCollapse>
    </ElForm>
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <template v-if="!isHistory">
        <template v-if="isView">
          <ElButton
            v-if="CommandPermission[CommandEnums.EDIT].includes(props.currentRow?.status!)"
            type="primary"
            @click="handleEdit"
          >
            编辑
          </ElButton>
        </template>
        <template v-else>
          <ElButton :loading="loading" type="primary" @click="handleSave">确定</ElButton>
        </template>
      </template>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}

:deep(.el-dialog__body) {
  height: calc(100vh - 10vh - 120px);
}
</style>
