<script lang="ts" setup>
import SkcList from '@/views/ProductSkcInfo/index.vue'
import { ElMessage } from 'element-plus'
import { ProductSkcInfoPageAPI } from '@/api/productSkcInfo/types'

defineOptions({
  name: 'SkcInfoDialog'
})

const props = defineProps<{
  modelValue: boolean
  isConfirm?: boolean
  isMaterial?: boolean
  productNumber?: string[]
  validate?: (val: ProductSkcInfoPageAPI.List[]) => boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', val: ProductSkcInfoPageAPI.List[]): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const skcList = ref<InstanceType<typeof SkcList>>()

const handleSubmit = () => {
  const selectedRows: ProductSkcInfoPageAPI.List[] | undefined =
    skcList.value?.tableRef?.getCheckboxRecords()
  if (!selectedRows?.length) {
    ElMessage.warning('请选择数据')
    return
  }
  if (props.validate && !props.validate(selectedRows)) {
    return
  }
  emit('submit', selectedRows)
  handleClose()
}

const handleClose = () => {
  skcList.value?.tableRef?.clearCheckboxRow()
  visible.value = false
}
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    append-to-body
    title="SKC搜索"
    top="5vh"
    width="1200"
  >
    <SkcList
      ref="skcList"
      :is-confirm="isConfirm"
      :is-material="isMaterial"
      :product-number="productNumber"
      is-embed
    />
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
