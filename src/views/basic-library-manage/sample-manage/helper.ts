import { SelectPlus } from '@/components/Business/SelectPlus'
import { CascadeSelector } from '@/components/Business/CascadeSelector'
import { ElCheckbox, ElSelect } from 'element-plus'
import { SampleMap, sampleStatusMap } from '@/views/basic-library-manage/sample-manage/const'
export const FormConfig = [
  {
    label: '产品编号',
    component: SelectPlus,
    value: 'productNumber',
    span: 8,
    isExpand: true, //是否在外面不折叠
    props: {
      'api-key': 'getProductNumberList',
      'collapse-tags': true,
      'collapse-tags-tooltip': true,
      multiple: true,
      filterable: true,
      virtualized: true
    }
  },
  {
    label: '打样单编号',
    component: SelectPlus,
    value: 'proofCode',
    isExpand: true, //是否在外面不折叠
    span: 8,
    props: {
      'api-key': 'getProofList',
      'collapse-tags': true,
      'collapse-tags-tooltip': true,
      multiple: true,
      filterable: true,
      virtualized: true
    }
  },
  {
    label: '品牌',
    component: SelectPlus,
    value: 'brand',
    span: 16,
    isExpand: true, //是否在外面不折叠
    props: {
      'api-key': 'baseBrand',
      checkbox: true,
      'checkbox-button': true,
      cache: true,
      filterable: true,
      multiple: true
    }
  },
  {
    label: '开发季节',
    component: SelectPlus,
    value: 'launchSeason',
    span: 8,
    props: {
      'api-key': 'COMMON_MARKET_SEASON',
      cache: true,
      clearable: true,
      'collapse-tags': true,
      'collapse-tags-tooltip': true,
      filterable: true,
      multiple: true,
      placeholder: '请选择'
    }
  },
  {
    label: '打样单状态',
    component: ElSelect,
    value: 'status',
    span: 8,
    props: {
      options: Object.keys(sampleStatusMap).map((item, index) => {
        return {
          value: item,
          label: sampleStatusMap[item],
          key: index
        }
      }),
      multiple: true,
      placeholder: '请选择'
    }
  },
  {
    label: 'Style(WMS)',
    component: SelectPlus,
    value: 'styleWms',
    props: {
      'api-key': 'getStyleWmsList',
      cache: true,
      clearable: true,
      checkbox: true,
      filterable: true,
      multiple: true,
      placeholder: '请选择',
      'collapse-tags': true,
      'collapse-tags-tooltip': true,
      virtualized: true
    }
  },
  {
    label: '供应商',
    component: SelectPlus,
    value: 'factoryId',
    props: {
      'api-key': 'getSupplierList',
      clearable: true,
      'collapse-tags': true,
      'collapse-tags-tooltip': true,
      filterable: true,
      multiple: true,
      placeholder: '请选择'
    }
  },
  {
    label: '区域',
    component: SelectPlus,
    value: 'region',
    props: {
      'api-key': 'COMMON_REGION',
      cache: true,
      class: 'w-full',
      clearable: true,
      'collapse-tags': true,
      'collapse-tags-tooltip': true,
      filterable: true
    }
  },
  {
    label: '产品企划',
    component: CascadeSelector,
    value: 'designPersonId',
    props: {
      props: { multiple: true, emitPath: false },
      'api-key': 'allUsers',
      cache: true,
      clearable: true,
      'collapse-tags': true,
      'collapse-tags-tooltip': true,
      placeholder: '请选择'
    }
  },
  {
    label: '产品设计师',
    component: CascadeSelector,
    value: 'designerId',
    props: {
      props: { emitPath: false },
      'api-key': 'allUsers',
      cache: true,
      clearable: true,
      placeholder: '请选择'
    }
  },
  {
    label: '跟进开发人员',
    component: CascadeSelector,
    value: 'developmentContactPersonId',
    props: {
      props: { emitPath: false },
      'api-key': 'allUsers',
      cache: true,
      clearable: true,
      placeholder: '请选择'
    }
  },
  {
    label: '跟进技术人员',
    component: CascadeSelector,
    value: 'technicalContactPersonId',
    props: {
      props: { emitPath: false },
      'api-key': 'allUsers',
      cache: true,
      clearable: true,
      placeholder: '请选择'
    }
  },
  {
    label: '打样单类型',
    component: ElSelect,
    value: 'proofType',
    props: {
      options: Object.keys(SampleMap).map((item, index) => {
        return {
          value: item,
          label: SampleMap[item],
          key: index
        }
      }),
      multiple: true,
      placeholder: '请选择'
    }
  },
  {
    label: '我的打样单',
    component: ElCheckbox,
    value: 'mine'
  }
]
