<script lang="ts" setup>
import HeelList from '../heel-library/heel-list.vue'
import { ElMessage } from 'element-plus'
import { HeelListPageAPI } from '../heel-library/api/heel-list'
import { useBasicLibraryDictStore } from '../store/dict'
import { HeelBottomTypeEnum } from '../const'

defineOptions({
  name: 'HeelInfoDialog'
})

const dictStore = useBasicLibraryDictStore()
const heelBottomTypeMap = computed(() => dictStore.heelBottomTypeMap)

const props = defineProps<{
  modelValue: boolean
  type?: HeelBottomTypeEnum[]
  multiple?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', val?: HeelListPageAPI.Row | HeelListPageAPI.List): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const heelList = ref<InstanceType<typeof HeelList>>()

const handleClose = () => {
  heelList.value?.tableRef?.clearCheckboxRow()
  visible.value = false
}

const handleSubmit = () => {
  const selectedRows: HeelListPageAPI.List | undefined =
    heelList.value?.tableRef?.getCheckboxRecords()
  if (!selectedRows?.length) {
    ElMessage.warning('请选择数据')
    return
  }
  if (selectedRows.length > 1 && !props.multiple) {
    ElMessage.warning('只能选择一条数据')
    return
  }
  const matchedType = selectedRows.every((e) =>
    props.type?.includes(e.typeCode! as HeelBottomTypeEnum)
  )
  if (props.type && !matchedType) {
    ElMessage.warning(
      `只能选择${props.type.map((e) => heelBottomTypeMap.value[e]).join('、')}的数据`
    )
    return
  }
  emit('submit', props.multiple ? selectedRows : selectedRows[0])
  handleClose()
}
</script>

<template>
  <Dialog v-model="visible" :before-close="handleClose" title="跟底库搜索" top="5vh" width="1200">
    <HeelList ref="heelList" :init-type="type" is-embed />
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
