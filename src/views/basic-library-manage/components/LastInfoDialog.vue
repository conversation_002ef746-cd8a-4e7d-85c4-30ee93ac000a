<script setup lang="ts">
import { ElMessage } from 'element-plus'
import LastList from '../last-library/last-list.vue'
import { LastListPageAPI } from '../last-library/api/last-list'

defineOptions({
  name: 'LastInfoDialog'
})

const props = defineProps<{
  modelValue: boolean
  multiple?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', val: LastListPageAPI.Row | undefined | LastListPageAPI.List): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const lastList = ref<InstanceType<typeof LastList>>()

const handleClose = () => {
  lastList.value?.tableRef?.clearCheckboxRow()
  visible.value = false
}

const handleSubmit = () => {
  const selectedRows: LastListPageAPI.List | undefined =
    lastList.value?.tableRef?.getCheckboxRecords()
  if (!selectedRows?.length) {
    ElMessage.warning('请选择数据')
    return
  }
  if (selectedRows.length > 1 && !props.multiple) {
    ElMessage.warning('只能选择一条数据')
    return
  }
  emit('submit', props.multiple ? selectedRows : selectedRows[0])
  handleClose()
}
</script>

<template>
  <Dialog v-model="visible" title="楦型库搜索" :before-close="handleClose" top="5vh" width="1200">
    <LastList ref="lastList" is-embed />
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>
