<script lang="ts" setup>
import ColorList from '../color-library/color-list.vue'
import { ColorListPageAPI } from '../color-library/api/color-list'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'ColorInfoDialog'
})

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', val: ColorListPageAPI.Row): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const colorList = ref<InstanceType<typeof ColorList>>()

const handleSubmit = () => {
  const selectedRows: ColorListPageAPI.List | undefined =
    colorList.value?.tableRef?.getCheckboxRecords()
  if (!selectedRows?.length) {
    ElMessage.warning('请选择数据')
    return
  }
  if (selectedRows.length > 1) {
    ElMessage.warning('只能选择一条数据')
    return
  }
  emit('submit', selectedRows[0])
  handleClose()
}

const handleClose = () => {
  colorList.value?.tableRef?.clearCheckboxRow()
  visible.value = false
}
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    append-to-body
    title="颜色库搜索"
    top="5vh"
    width="1200"
  >
    <ColorList ref="colorList" is-embed />
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
