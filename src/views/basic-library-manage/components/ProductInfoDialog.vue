<script setup lang="ts">
import { ElMessage } from 'element-plus'
import ProductList from '../product-library/product-list.vue'
import { ProductListPageAPI } from '../product-library/api/product-list'

defineOptions({
  name: 'ProductInfoDialog'
})

const props = defineProps<{
  modelValue: boolean
  multiple?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', val: ProductListPageAPI.List | ProductListPageAPI.Row): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const productList = ref<InstanceType<typeof ProductList>>()

const handleClose = () => {
  productList.value?.tableRef?.clearCheckboxRow()
  visible.value = false
}

const handleSubmit = () => {
  const selectedRows: ProductListPageAPI.List | undefined =
    productList.value?.tableRef?.getCheckboxRecords()
  if (!selectedRows?.length) {
    ElMessage.warning('请选择数据')
    return
  }
  if (selectedRows.length > 1 && !props.multiple) {
    ElMessage.warning('只能选择一条数据')
    return
  }
  emit('submit', props.multiple ? selectedRows : selectedRows[0])
  handleClose()
}
</script>

<template>
  <Dialog v-model="visible" title="产品库搜索" :before-close="handleClose" top="5vh" width="1200">
    <ProductList ref="productList" is-embed />
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>
