<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import InfringementInvestigation from '@/views/InfringementInvestigation/index.vue'
import type { InfringementPageAPI } from '@/api/infringementInvestigation/types'

defineOptions({
  name: 'InfringementInvestigationDialog'
})

const props = defineProps<{
  modelValue: boolean
  embedType?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', val: InfringementPageAPI.List | InfringementPageAPI.List[]): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const productList = ref<InstanceType<typeof InfringementInvestigation>>()

const handleClose = () => {
  productList.value?.refTable?.clearCheckboxRow()
  visible.value = false
}

const handleSubmit = () => {
  if (props.embedType !== 'productInfo') {
    const selectedRows: InfringementPageAPI.List = productList.value?.refTable?.getRadioRecord()
    if (!selectedRows) {
      ElMessage.warning('请选择数据')
      return
    }
    const valid = selectedRows.status !== '作废'
    if (!valid) {
      ElMessage.warning('已作废的数据不可关联')
      return
    }
    emit('submit', selectedRows as InfringementPageAPI.List)
  } else {
    const selectedRows = productList.value?.refTable?.getCheckboxRecords()
    if (!selectedRows?.length) {
      ElMessage.warning('请选择数据')
      return
    }
    const valid = selectedRows.every((e) => e.status !== '作废')
    if (!valid) {
      ElMessage.warning('已作废的数据不可关联')
      return
    }
    emit('submit', selectedRows as InfringementPageAPI.List[])
  }
  handleClose()
}
</script>

<template>
  <Dialog v-model="visible" :before-close="handleClose" title="侵权排查" top="5vh" width="1200">
    <InfringementInvestigation ref="productList" :embed-type="embedType" is-embed />
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
