<script setup lang="ts">
import ModelList from '../model-library/model-list.vue'
import { ElMessage } from 'element-plus'
import { ModelListPageAPI } from '../model-library/api/model-list'

defineOptions({
  name: 'ModelInfoDialog'
})

const props = defineProps<{
  modelValue: boolean
  multiple?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', val?: ModelListPageAPI.Row | ModelListPageAPI.List): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const modelList = ref<InstanceType<typeof ModelList>>()

const handleClose = () => {
  modelList.value?.tableRef?.clearCheckboxRow()
  visible.value = false
}

const handleSubmit = () => {
  const selectedRows: ModelListPageAPI.List | undefined =
    modelList.value?.tableRef?.getCheckboxRecords()
  if (!selectedRows?.length) {
    ElMessage.warning('请选择数据')
    return
  }
  if (selectedRows.length > 1 && !props.multiple) {
    ElMessage.warning('只能选择一条数据')
    return
  }
  emit('submit', props.multiple ? selectedRows : selectedRows[0])
  handleClose()
}
</script>

<template>
  <Dialog v-model="visible" title="型体库搜索" :before-close="handleClose" top="5vh" width="1200">
    <ModelList ref="modelList" is-embed />
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>
