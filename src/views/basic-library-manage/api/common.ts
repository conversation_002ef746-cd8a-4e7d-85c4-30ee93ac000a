import { service } from '@/config/axios/service'
import to from 'await-to-js'
import { ModelInfoApi } from '../model-library/api/modelInfo'
import { get } from '@/config/fetch'
import { ProductDerivativeTypeEnum } from '@/views/basic-library-manage/product-library/const'

export interface Pager {
  current: number
  size: number
  total: number
}

/**
 * 根据字典类型获取字典列表
 */
export namespace DictValueAPI {
  export interface Data {
    /**
     * 展示值-中文
     */
    dictCnName?: string
    /**
     * 展示值-英文
     */
    dictEnName?: string
    /**
     * 传值
     */
    dictValue?: string | number
    id?: number
    [key: string]: string | number | undefined
  }
  export type Response = ResponseData<Data[]>
}
export function getDictValueList(dictType: string) {
  return to<DictValueAPI.Response>(service.get(`/pdm-base/dict/value/list?dictType=${dictType}`))
}

export namespace DictListAPI {
  export interface Row {
    dictItem?: string
    dictValueList?: DictValueAPI.Data[]
    id?: number
    typeName?: string
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}
export function getDictList() {
  return to<DictListAPI.Response>(service.get('/pdm-base/dict/value/all'))
}

export namespace SaveDictListAPI {
  export interface Request {
    /**
     * 类型代码
     */
    typeCode?: string
    /**
     * 类型名称
     */
    typeName?: string
    dictItemList?: DictListAPI.List
  }
  export type Response = BasicResponseData
}
export function saveDictList(data: SaveDictListAPI.Request) {
  return to<SaveDictListAPI.Response>(service.post('/pdm-base/dict/value/save', data))
}

/**
 * 获取品牌列表
 */
export namespace BrandListAPI {
  export interface Data {
    selectorKey?: string
    selectorValue?: number
    [key: string]: string | number | undefined
  }
  export type Response = ResponseData<Data[]>
}
export function getBrandList() {
  return to<BrandListAPI.Response>(service.get('/pdm-base/base/brand'))
}

/**
 * 根据楦型id获取引用的跟底列表
 */
export namespace HeelReferencedListAPI {
  export interface Data {
    /**
     * 品牌
     */
    brand?: string
    /**
     * 跟底编码
     */
    code?: string
    /**
     * 开发季节编码
     */
    developmentSeasonCode?: string
    id?: number
    /**
     * 状态
     */
    status?: string
    /**
     * 状态code
     */
    statusCode?: string
    thumbnail?: BaseFileDTO
    /**
     * 缩略图下载地址
     */
    thumbnailDownloadUrl?: string
    /**
     * 跟底分类编码
     */
    typeCode?: string
  }
  export type Response = ResponseData<Data[]>
}
export function getHeelReferencedListById(lastId: number) {
  return to<HeelReferencedListAPI.Response>(service.get(`/pdm-base/heel/queryByLastId/${lastId}`))
}

/**
 * 根据跟底id/模具id获取引用的楦型列表
 */
export namespace LastReferencedListAPI {
  export interface Data {
    /**
     * 品牌
     */
    brand?: string[]
    /**
     * 楦型编码
     */
    code?: string
    /**
     * 开发季节code
     */
    developmentSeasonCode?: string
    /**
     * 楦头帮高code
     */
    heightCode?: string
    id?: number
    /**
     * 市场code
     */
    marketCode?: string
    /**
     * 关联的style
     */
    productName?: string
    /**
     * 性别code
     */
    sexCode?: string
    thumbnail?: BaseFileDTO
    /**
     * 缩略图下载地址
     */
    thumbnailDownloadUrl?: string
    /**
     * 楦头类别code
     */
    typeCode?: string
  }
  export type Response = ResponseData<Data[]>
}
export function getLastReferencedListByIdList(idList: number[]) {
  return to<LastReferencedListAPI.Response>(
    service.get(`/pdm-base/last/queryByIdList`, { params: { id: idList } })
  )
}

/**
 * wms商品类目级联选项
 */
export namespace WMSCategoryListAPI {
  export interface Data {
    categoryCode?: null | string
    categoryLevel?: number | null
    categoryName?: null | string
    CategoryParentId?: null | string
    createByName?: null | string
    createTime?: null | string
    id?: number | null
    sonCategory?: Data[] | null
    useStatus?: number | null
    useStatusI18?: null | string
    [key: string]: unknown
  }
  export type Response = ResponseData<Data[]>
}
export function getWMSCategoryList() {
  return to<WMSCategoryListAPI.Response>(service.get('/pdm-base/base/wms/category/tree'))
}

export namespace ProductCategoryListAPI {
  export interface Data {
    childList?: Data[]
    parentKey?: number
    selectorCode?: string
    selectorEnValue?: string
    selectorKey?: number
    selectorValue?: string
    selectorAdditionList?: string[]
  }
  export type Response = ResponseData<Data[]>
}

/**
 * 产品类目级联选项
 */
export function getProductCategoryList() {
  return to<ProductCategoryListAPI.Response>(service.get('/pdm-base/product/category/tree'))
}

/**
 * 更新状态
 */
export namespace UpdateStatusAPI {
  export interface StatusData {
    id: number
    oldStatus: string
    newStatus: string
  }
  export interface Params {
    updateStatusReqList: StatusData[]
  }
  export type Request = Params
  export type Response = BasicResponseData
}

/**
 * 版本记录
 */
export namespace VersionListAPI {
  export interface Data {
    /**
     * 数据变更时间
     */
    dataTime: string
    id: number
    /**
     * 操作人
     */
    operator: string
    /**
     * 状态
     */
    status: string
    /**
     * 版本号
     */
    versionCode: string
    /**
     * 版本说明
     */
    versionRemark: string
    /**
     * 版本生效时间
     */
    versionTime: string
    /**
     * 版本变更类型
     */
    versionType: string
  }
  export type Response = ResponseData<Data[]>
}

export namespace ModelInfoWithProductNumberAPI {
  export type Data = ModelInfoApi.ModelDetailResp
  export type Response = ResponseData<Data>
}

export function getModelInfoByProductNumber(code: string) {
  return to<ModelInfoWithProductNumberAPI.Response>(
    service.get(`/pdm-base/model/modelByProduct/${code}`)
  )
}

export namespace CommonOptionsAPI {
  export interface Options {
    key: string
    values: Value[]
  }

  export interface Value {
    code: string
    name: string
  }
  export type Response = ResponseData<Options>
}

export function getMultiMatchedOptions() {
  return get<CommonOptionsAPI.Response>({
    url: `/pdm-base/tempProductInfo/getCommonOption/${ProductDerivativeTypeEnum.MULTIPLE_ACCESSORIES}`
  })
}
