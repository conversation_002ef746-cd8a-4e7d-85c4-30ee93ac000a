import { service } from '@/config/axios/service'
import to from 'await-to-js'

export const UPLOAD_URL = '/pdm-base/file/batchUpload'

export namespace UploadAPI {
  export type Response = ResponseData<BaseFileDTO[]>
}

export function uploadFiles(files: File[]) {
  const formData = new FormData()
  for (let i = 0; i < files.length; i++) {
    formData.append('files', files[i])
  }
  return to<UploadAPI.Response>(
    service.post(UPLOAD_URL, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  )
}

export function uploadFilesWithoutExpire(files: File[]) {
  const formData = new FormData()
  for (let i = 0; i < files.length; i++) {
    formData.append('files', files[i])
  }
  return to<UploadAPI.Response>(
    service.post('/pdm-base/file/batchUploadNoExpire', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  )
}
