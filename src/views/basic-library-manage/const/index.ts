import { useConstants } from '@/hooks/web/useConstants'
import { ignoreAutoI18n } from '@higgins-mmt/vite-plugin-i18n-transformer/utils'

export enum ApprovalStatusEnum {
  /**
   * 草稿
   */
  DRAFT = 'draft',
  /**
   * 审批中
   */
  APPROVING = 'approving',
  /**
   * 审批拒绝
   */
  REJECTION = 'rejection',
  /**
   * 审批通过
   */
  PASSED = 'passed'
}

const approvalStatusMap = {
  [ApprovalStatusEnum.DRAFT]: ignoreAutoI18n('草稿'),
  [ApprovalStatusEnum.APPROVING]: ignoreAutoI18n('审批中'),
  [ApprovalStatusEnum.REJECTION]: ignoreAutoI18n('审批拒绝'),
  [ApprovalStatusEnum.PASSED]: ignoreAutoI18n('审批通过')
}

const { list: approvalStatusList } = useConstants(approvalStatusMap)

export const approvalStatusConst = {
  approvalStatusMap,
  approvalStatusList
}

export enum StatusEnum {
  /**
   * 草稿
   */
  DRAFT = 'draft',
  /**
   * 启用
   */
  START = 'start',
  /**
   * 禁用
   */
  BAN = 'ban',
  /**
   * 审批中
   */
  APPROVING = 'approving',
  /**
   * 审批拒绝
   */
  REJECTION = 'rejection',
  /**
   * 审批通过
   */
  PASSED = 'passed',
  /**
   * 分配中
   */
  ASSIGN = 'assign',

  /**
   * 审核中
   */
  REVIEW = 'review',

  /**
   * 打样中
   */
  PROOF = 'proof',

  /**
   * 开发工程师评审
   */
  DEV_REVIEW = 'dev_review',

  /**
   * 设计师评审
   */
  DESIGN_REVIEW = 'design_review',

  /**
   * 已完成
   */
  FINISH = 'finish',

  /**
   * 已取消
   */
  CANCEL = 'cancel',

  /**
   * 重新工作
   */
  AGAIN = 'again'
}

const statusMap = {
  [StatusEnum.DRAFT]: ignoreAutoI18n('草稿'),
  [StatusEnum.START]: ignoreAutoI18n('启用'),
  [StatusEnum.BAN]: ignoreAutoI18n('禁用'),
  [StatusEnum.APPROVING]: ignoreAutoI18n('审批中'),
  [StatusEnum.REJECTION]: ignoreAutoI18n('审批拒绝'),
  [StatusEnum.PASSED]: ignoreAutoI18n('审批通过'),
  [StatusEnum.CANCEL]: ignoreAutoI18n('已取消')
}

const { list: statusList } = useConstants(statusMap)

export const statusConst = {
  statusMap,
  statusList: statusList.filter(
    (e) => e.value !== StatusEnum.REJECTION && e.value !== StatusEnum.PASSED
  ),
  fullStatusList: statusList
}

// 跟底类型
export enum HeelBottomTypeEnum {
  /**
   * 成品底
   */
  FB = 'FB',
  /**
   * 跟
   */
  HL = 'HL',
  /**
   * 中底
   */
  IS = 'IS',
  /**
   * 大底
   */
  OS = 'OS',
  /**
   * 防水台
   */
  PF = 'PF',
  /**
   * 鞋垫
   */
  SL = 'SL',
  /**
   * 包头
   */
  TC = 'TC',
  /**
   * 天皮
   */
  TL = 'TL',
  /**
   * 沿条
   */
  WL = 'WL'
}

// 产品数据状态
export enum ProductDataStatusEnum {
  /**
   * 草稿
   */
  DRAFT = 'draft',
  /**
   * 生效
   */
  EFFECT = 'effective',
  /**
   * 作废
   */
  INVALID = 'invalid'
}

export enum YesNoEnum {
  /**
   * 是
   */
  Y = '1',
  /**
   * 否
   */
  N = '0'
}

/**
 * 产品类目枚举
 */
export enum ProductCategoryEnum {
  /**
   * 运动鞋
   */
  SPORTS = 37,
  /**
   * 非运动鞋
   */
  NON_SPORTS = 36,
  /**
   * 鞋类
   */
  SHOES = 34,
  /**
   * 配饰
   */
  ACCESSORIES = 35
}

/**
 * 材料枚举
 */
export enum MaterialCategoryEnum {
  /**
   * KPU
   */
  KPU = 'KPU',
  /**
   * 其他材料
   */
  OTHERS = 'OTHERS',
  /**
   * 皮料
   */
  LEATHER = 'LEATHER',
  /**
   * PU
   */
  PU = 'PU',
  /**
   * PVC
   */
  PVC = 'PVC',
  /**
   * TPU
   */
  TPU = 'TPU',
  /**
   * 纺织物
   */
  TEXTILE = 'TEXTILE',
  /**
   * 大底
   */
  OUTSOLE = 'OUTSOLE',
  /**
   * 鞋垫
   */
  INSOLE = 'INSOLE',
  /**
   * 内里
   */
  LINING = 'LINING',
  /**
   * 塑胶
   */
  PLASTIC = 'PLASTIC',
  /**
   * 辅料
   */
  ACC = 'ACC'
}

/**
 * 楦头标准枚举
 */
export enum HeadStandardEnum {
  /**
   * 美楦
   */
  U = 'U',
  /**
   * 欧楦
   */
  E = 'E',
  /**
   * 配件
   */
  A = 'A',
  /**
   * 欧楦专供
   */
  D = 'D'
}
export const shoesLastStandardList: string[] = [
  HeadStandardEnum.U,
  HeadStandardEnum.E,
  HeadStandardEnum.D
]

/**
 * 楦头类别枚举
 */
export enum HeadCategoryEnum {
  /**
   * 宽楦
   */
  W = 'W',
  /**
   * 窄楦
   */
  N = 'N',
  /**
   * 正常楦
   */
  M = 'M',
  /**
   * 配件
   */
  A = 'A'
}

export const shoesToeCategoryList: string[] = [
  HeadCategoryEnum.W,
  HeadCategoryEnum.N,
  HeadCategoryEnum.M
]
export const accessoriesCategoryList: string[] = [HeadCategoryEnum.A]

/**
 * 模具枚举
 */
export enum MoldCategoryEnum {
  /**
   * 底模具
   */
  BOTTOM = 'O'
}

/**
 * 品牌枚举
 */
export enum BrandEnum {
  /**
   * DP
   */
  DP = 2,
  /**
   * DPK
   */
  DPK = 8,
  /**
   * SHOEDAZZLE
   */
  SHOEDAZZLE = 10,
  /**
   * JUSTFAB
   */
  JUSTFAB = 11,
  /**
   * FABKIDS
   */
  FABKIDS = 13,
  /**
   * JUSTFAB 3rd Party
   */
  JUSTFAB_3RD_PARTY = 14
}

/**
 * 适用人群枚举
 */
export enum TargetGroupEnum {
  /**
   * WOMEN
   */
  WOMEN = 'WOMEN',
  /**
   * MEN
   */
  MEN = 'MEN',
  /**
   * KIDS
   */
  KIDS = 'KIDS',
  /**
   * UNION
   */
  UNION = 'UNION'
}
