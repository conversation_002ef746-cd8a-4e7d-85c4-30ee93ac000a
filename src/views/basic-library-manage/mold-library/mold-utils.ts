import { SizeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import { MoldProcessListAPI } from '@/views/basic-library-manage/mold-library/api/moldProcess'
import { SizeValueAPI } from '@/views/basic-library-manage/size-library/api/sizeInfo'
import { MoldListPageAPI } from '@/views/basic-library-manage/mold-library/api/moldList'
import { groupBy } from 'lodash-es'
import { HeelListPageAPI } from '@/views/basic-library-manage/heel-library/api/heel-list'

/**
 * 根据sizeValueId,查询sizeList
 */
export function formatSizeLabel(size: SizeValueAPI.Row, sizeList: SizeListAPI.Data[]) {
  let sizeLabel = size.sizeValue || ''
  const sizeId = size.id
  if (sizeId) {
    const find = sizeList.find((item) => {
      return !!item.sizeValueList?.find((item2) => sizeId === item2.id!)
    })
    if (find) {
      sizeLabel += `【${find.name}】`
    }
  }
  return sizeLabel
}

export function formatRowSizeText(
  row: MoldProcessListAPI.Row | MoldListPageAPI.Row | HeelListPageAPI.Row,
  sizeList: SizeListAPI.Data[]
) {
  let sizeGroup = {}
  if ('sizeValueId' in row && Array.isArray(row.sizeValueId)) {
    const selectedSizeList = sizeList.filter((e) => row.sizeId?.includes(e.id!))
    sizeGroup = groupBy(
      row.sizeValueId.map((id) => {
        const sizeObj = selectedSizeList.find((item) =>
          item.sizeValueList?.find((size) => size.id === id)
        )
        return {
          name: sizeObj?.name,
          sizeValue: sizeObj?.sizeValueList?.find((size) => size.id === id)?.sizeValue
        }
      }),
      'name'
    )
  }
  if ('standardSizeId' in row && Array.isArray(row.standardSizeId)) {
    const selectedSizeList = sizeList.filter((e) => row.sizeId?.includes(e.id!))
    sizeGroup = groupBy(
      row.standardSizeId.map((id) => {
        const sizeObj = selectedSizeList.find((item) =>
          item.sizeValueList?.find((size) => size.id === id)
        )
        return {
          name: sizeObj?.name,
          sizeValue: sizeObj?.sizeValueList?.find((size) => size.id === id)?.sizeValue
        }
      }),
      'name'
    )
  }
  return Object.keys(sizeGroup)
    .map((key) => {
      const sizeValueList = sizeGroup[key].map((item) => item.sizeValue).join(', ')
      return `${sizeValueList}【${key}】`
    })
    .join('、')
}
