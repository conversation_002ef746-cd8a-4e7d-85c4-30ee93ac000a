<script lang="ts" setup>
import { Icon } from '@/components/Icon'
import { ElCollapseTransition, ElMessage, ElPagination, FormInstance } from 'element-plus'
import { ref } from 'vue'
import { VxeTableInstance } from 'vxe-table'
import { Pager } from '@/views/basic-library-manage/api/common'
import dayjs from 'dayjs'
import { isEqual } from 'lodash-es'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { statusConst, StatusEnum } from '@/views/basic-library-manage/const'
import {
  getMoldProcessListApi,
  MoldProcessListAPI
} from '@/views/basic-library-manage/mold-library/api/moldProcess'
import VersionDialog from '@/views/basic-library-manage/mold-library/components/MoldProcessVersionDialog.vue'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { watchDebounced } from '@vueuse/core'
import { formatRowSizeText } from '@/views/basic-library-manage/mold-library/mold-utils'
import { sizeList } from '@/components/Business/SelectPlus/src/api'
import { SizeListAPI } from '@/components/Business/SelectPlus/src/api/types'

defineOptions({
  name: 'MoldProcess'
})

const router = useRouter()

const useQuery = () => {
  type FormMode = MoldProcessListAPI.Params & {
    date?: [string, string]
  }
  const defaultFormData: FormMode = {
    brand: [],
    code: '',
    date: ['', ''],
    region: [],
    statusCode: [],
    type: [],
    designer: []
  }
  let lastFormData = {
    ...defaultFormData
  }
  const formData = ref<FormMode>({
    ...defaultFormData
  })
  const formRef = ref<FormInstance>()
  const tableRef = ref<VxeTableInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const tableData = ref<MoldProcessListAPI.List>([])
  const queryLoading = ref(false)
  const pager = ref<Pager>({
    current: 1,
    size: 10,
    total: 0
  })

  const queryParams = computed<MoldProcessListAPI.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1],
      date: undefined
    }
  })
  const defaultTime: [Date, Date] = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ]

  let controller: AbortController | null = null
  const handleQuery = async () => {
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await getMoldProcessListApi(queryParams.value, controller.signal)
    queryLoading.value = false
    if (error === null && result?.datas) {
      lastFormData = { ...formData.value }
      const { records } = result.datas
      tableData.value = records.map((row) => {
        return {
          ...row,
          sizeValueIdText: formatRowSizeText(row, sizeOptions.value)
        }
      })
      pager.value.total = result.datas.pager.total
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
    handleQuery()
  }

  onActivated(handleQuery)

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = () => {
    const selected: MoldProcessListAPI.List | undefined = tableRef.value?.getCheckboxRecords()
    let reqParam: string
    if (selected && selected?.length > 0) {
      reqParam = JSON.stringify({ idList: selected.map((e) => e.id) })
    } else {
      reqParam = JSON.stringify(queryParams.value)
    }
    exportFn({
      exportType: 'moldApply-export',
      reqParam
    })
  }

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef
  })

  return {
    formRef,
    tableRef,
    pagerRef,
    formData,
    tableData,
    queryLoading,
    pager,
    defaultTime,
    handleQuery,
    handleReset,
    handleExport,
    exportLoading,
    visible,
    setVisible,
    maxHeight
  }
}

const {
  formRef,
  tableRef,
  pagerRef,
  formData,
  tableData,
  queryLoading,
  pager,
  defaultTime,
  handleQuery,
  handleReset,
  handleExport,
  exportLoading,
  visible,
  setVisible,
  maxHeight
} = useQuery()

const useOperation = () => {
  const currentRow = ref<MoldProcessListAPI.Row>()

  // 修改模具
  const handleEditMoldProcess = () => {
    const selectedRows: MoldProcessListAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    const status = selectedRows[0].statusCode
    if (status === StatusEnum.APPROVING || status === StatusEnum.PASSED) {
      ElMessage.warning('审批中/审批通过数据不允许修改')
      return
    }
    router.push({
      name: 'EditMoldProcess',
      query: {
        id: selectedRows[0].id
      }
    })
  }
  // 新增模具
  const handleCreateMoldProcess = () => {
    router.push({
      name: 'CreateMoldProcess'
    })
  }

  // 版本记录
  const versionDialogVisible = ref(false)
  const handleOpenVersionDialog = (row: MoldProcessListAPI.Row) => {
    currentRow.value = row
    versionDialogVisible.value = true
  }

  return {
    currentRow,
    handleEditMoldProcess,
    handleCreateMoldProcess,
    versionDialogVisible,
    handleOpenVersionDialog
  }
}

const {
  handleEditMoldProcess,
  handleCreateMoldProcess,
  versionDialogVisible,
  handleOpenVersionDialog,
  currentRow
} = useOperation()

const statusList = statusConst.fullStatusList.filter(
  (e) => e.value !== StatusEnum.START && e.value !== StatusEnum.BAN
)
const statusMap = statusConst.statusMap

const { formLabelLength } = useLocaleConfig([
  {
    formLabelLength: '100'
  },
  {
    formLabelLength: '180'
  }
])

const sizeOptions = ref<SizeListAPI.Data[]>([])

async function querySizeList() {
  const { datas } = await sizeList()
  if (datas) {
    sizeOptions.value = datas
  }
}

onMounted(async () => {
  await Promise.all([querySizeList(), handleQuery()])
  watchDebounced(
    formData,
    async () => {
      await handleQuery()
    },
    { deep: true, debounce: 400 }
  )
})
</script>

<template>
  <div>
    <div class="max-h-[calc(100vh_-_250px)] overflow-x-hidden overflow-y-auto">
      <ElForm ref="formRef" :label-width="formLabelLength" :model="formData">
        <div class="grid grid-cols-3 items-center">
          <ElFormItem label="申请单编号" prop="code">
            <ElInput
              v-model="formData.code"
              clearable
              placeholder="请输入申请单编号，支持模糊查询"
              @change="handleQuery"
            />
          </ElFormItem>
          <ElFormItem label="模具类型" prop="type">
            <SelectPlus
              v-model="formData.type"
              api-key="MOLD_TYPE"
              cache
              clearable
              collapse-tags
              collapse-tags-tooltip
              multiple
              placeholder="请选择模具类型"
            />
          </ElFormItem>
          <ElFormItem label="" label-width="0">
            <ElButton text @click="setVisible">
              {{ visible ? '收起' : '展开' }}
              <Icon
                :class="visible ? 'rotate-90' : ''"
                class="transform transition duration-400"
                icon="ant-design:down-outlined"
              />
            </ElButton>
            <ElButton :loading="queryLoading" type="primary" @click="handleQuery">
              <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
              查询
            </ElButton>
            <ElButton :loading="queryLoading" @click="handleReset">
              <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
              重置
            </ElButton>
            <ElButton :loading="exportLoading" type="primary" @click="handleExport">
              <Icon v-show="!exportLoading" class="mr-1" icon="ep:upload-filled" />
              导出
            </ElButton>
          </ElFormItem>
          <ElFormItem class="col-span-2" label="品牌" prop="brand">
            <SelectPlus
              v-model="formData.brand"
              api-key="baseBrand"
              cache
              checkbox
              checkbox-button
            />
          </ElFormItem>
          <ElCollapseTransition>
            <div v-show="visible" class="col-span-2 grid grid-cols-2 items-center">
              <ElFormItem label="操作时间段" prop="date">
                <ElDatePicker
                  v-model="formData.date"
                  :default-time="defaultTime"
                  class="max-w-96"
                  clearable
                  end-placeholder="结束时间"
                  range-separator="~"
                  start-placeholder="开始时间"
                  type="daterange"
                  unlink-panels
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
              <ElFormItem label="区域" prop="region">
                <SelectPlus
                  v-model="formData.region"
                  api-key="COMMON_REGION"
                  cache
                  checkbox
                  checkbox-button
                />
              </ElFormItem>
              <ElFormItem label="设计师" prop="designerId">
                <CascadeSelector
                  v-model="formData.designer"
                  :props="{ multiple: true, emitPath: false }"
                  api-key="allUsers"
                  cache
                  clearable
                  placeholder="请选择设计师"
                />
              </ElFormItem>
              <ElFormItem label="状态" prop="status">
                <ElScrollbar>
                  <ElCheckboxGroup
                    v-model="formData.statusCode"
                    class="flex flex-nowrap"
                    @change="handleQuery"
                  >
                    <ElCheckboxButton
                      v-for="item in statusList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </ElCheckboxGroup>
                </ElScrollbar>
              </ElFormItem>
            </div>
          </ElCollapseTransition>
        </div>
      </ElForm>
      <div class="mb-[10px] min-h-8">
        <ElButton v-hasPermi="['applyMold']" type="primary" @click="handleCreateMoldProcess">
          <Icon icon="ep:plus" />
          <span class="text-[14px]">模具开具申请</span>
        </ElButton>
        <ElButton v-hasPermi="['editApplyMold']" type="primary" @click="handleEditMoldProcess">
          <Icon icon="ep:edit" />
          <span class="text-[14px]">修改申请单</span>
        </ElButton>
      </div>
      <div>
        <VxeTable
          ref="tableRef"
          :data="tableData"
          :loading="queryLoading"
          :max-height="maxHeight - 75"
          :show-header-overflow="false"
        >
          <VxeColumn fixed="left" type="checkbox" width="40" />
          <VxeColumn title="序号" fixed="left" type="seq" width="60" />
          <VxeColumn field="code" fixed="left" show-overflow title="申请单编号" width="90">
            <template #default="{ row }: { row: MoldProcessListAPI.Row }">
              <router-link :to="{ name: 'ViewMoldProcess', query: { id: row.id } }">
                <span class="p-0 max-w-full cursor-pointer text-blue-500 hover:underline">
                  {{ row.code }}
                </span>
              </router-link>
            </template>
          </VxeColumn>
          <VxeColumn
            :cell-render="{ name: 'Image' }"
            fixed="left"
            field="developmentDrawings"
            title="缩略图"
            width="100"
          />
          <VxeColumn field="linkMoldCode" min-width="125" title="模具编码">
            <template #default="{ row }: { row: MoldProcessListAPI.Row }">
              <template v-for="{ id, code } in row.linkMold" :key="id">
                <router-link :to="{ name: 'ViewMold', query: { id: id } }">
                  <span class="cursor-pointer text-blue-500 hover:underline">
                    {{ code }}
                  </span>
                </router-link>
              </template>
            </template>
          </VxeColumn>
          <VxeColumn field="typeItemName" min-width="100" title="模具类型" />
          <VxeColumn field="stageItemName" min-width="100" title="模具开制阶段" />
          <VxeColumn field="brandItemName" min-width="100" title="品牌" />
          <VxeColumn field="headItemName" title="楦头类别" width="80" />
          <VxeColumn field="targetAudienceItemName" title="适用人群" width="80" />
          <VxeColumn field="designerItemName" min-width="100" title="设计师" />
          <VxeColumn field="regionItemName" min-width="100" title="区域" />
          <VxeColumn field="linkProductNumber" min-width="100" title="关联产品编号" />
          <VxeColumn
            :cell-render="{ name: 'Ellipsis', props: { maxRow: 2, separator: ',' } }"
            :show-overflow="false"
            field="sizeIdItemName"
            min-width="100"
            title="模具尺码段"
          />
          <VxeColumn
            :cell-render="{ name: 'Ellipsis', props: { maxRow: 2, separator: '、' } }"
            field="sizeValueIdText"
            min-width="120"
            title="尺码值"
          />
          <VxeColumn field="sampleMoldCode" min-width="100" title="关联模具编号" />
          <VxeColumn field="createTime" min-width="100" title="创建时间" />
          <VxeColumn field="feishuSerialNumber" min-width="140" title="关联飞书审批流程ID" />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: statusMap } }"
            field="statusCode"
            fixed="right"
            min-width="100"
            title="状态"
          />
          <VxeColumn field="modifyByIdItemName" title="操作人" width="120" />
          <VxeColumn field="modifyTime" title="操作时间" width="80" />
          <VxeColumn :show-overflow="false" fixed="right" title="操作" width="100">
            <template #default="{ row }">
              <ElButton size="small" text type="primary" @click="handleOpenVersionDialog(row)">
                版本记录
              </ElButton>
            </template>
          </VxeColumn>
        </VxeTable>
      </div>
    </div>
    <ElPagination
      ref="pagerRef"
      v-model:current-page="pager.current"
      v-model:page-size="pager.size"
      :total="pager.total"
      background
      class="mt-4"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleQuery"
      @current-change="handleQuery"
    />
    <VersionDialog v-model="versionDialogVisible" :current-row="currentRow" />
  </div>
</template>

<style lang="less" scoped></style>
