<script lang="ts" setup>
import {
  CascaderInstance,
  ElLoading,
  ElMessage,
  ElMessageBox,
  FormInstance,
  FormRules
} from 'element-plus'
import { CodeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import {
  createProcess,
  editProcess,
  getMoldNumberListWithProductNumberAndType,
  getMoldProcessInfo,
  getMoldProcessInfoByVersionId,
  MoldProcessInfoAPI
} from '@/views/basic-library-manage/mold-library/api/moldProcessInfo'
import { SelectPlus } from '@/components/Business/SelectPlus'
import { ContentWrap } from '@/components/ContentWrap'
import { MoldCategoryEnum, StatusEnum, YesNoEnum } from '@/views/basic-library-manage/const'
import { cloneDeep, groupBy } from 'lodash-es'
import { Icon } from '@/components/Icon'
import { useSizeOptions } from '@/utils/useSizeOptions'
import ElCascader from '@/components/Cascader/src/Cascader'
import { SizeCodeTypeEnums } from '@/enums'

defineOptions({
  name: 'MoldProcessInfo'
})

const route = useRoute()

const id = computed(() => {
  return route.query.id
})

const versionId = computed(() => {
  return route.query.versionId
})

const useType = () => {
  const isCreate = computed(() => {
    return route.name === 'CreateMoldProcess'
  })

  const isEdit = computed(() => {
    return route.name === 'EditMoldProcess'
  })

  const isView = computed(() => {
    return route.name === 'ViewMoldProcess'
  })
  return {
    isCreate,
    isEdit,
    isView
  }
}

const { isCreate, isEdit, isView } = useType()

const useQueryInfo = () => {
  const formRef = ref<FormInstance>()
  const formData = ref<MoldProcessInfoAPI.QueryMoldApplyDetailResp>({
    developmentDrawings: [],
    originalHeelMap: [],
    developShape: [],
    feishuSerialNumber: '',
    brand: undefined,
    region: '',
    linkProductNumber: [],
    stage: '',
    sizeId: [],
    type: '',
    standardSizeValue: [],
    head: '',
    soleMaterial: [],
    targetAudience: '',
    referMoldCode: '',
    sampleMoldCode: [],
    estimatedOrderVolume: undefined,
    supplierMoldNumber: '',
    designer: undefined,
    remark: '',
    useNewMold: +YesNoEnum.N
  })
  const formRules = computed<FormRules<MoldProcessInfoAPI.QueryMoldApplyDetailResp>>(() => {
    return {
      developmentDrawings: [
        {
          required: true,
          message: '请上传模具开制图纸'
        }
      ],
      originalHeelMap: [
        {
          required: formData.value.type === MoldCategoryEnum.BOTTOM,
          message: '请上传原版大底图'
        }
      ],
      developShape: [
        {
          required: true,
          message: '请上传开发面款'
        }
      ],
      brand: [
        {
          required: true,
          message: '请选择品牌',
          trigger: 'change'
        }
      ],
      region: [
        {
          required: true,
          message: '请选择区域',
          trigger: 'change'
        }
      ],
      sampleMoldCode: [
        {
          required: formData.value.useNewMold === +YesNoEnum.Y,
          message: '请选择模具编号',
          trigger: 'change'
        }
      ],
      linkProductNumber: [
        {
          required: formData.value.useNewMold === +YesNoEnum.N,
          message: '请选择关联产品编号',
          trigger: 'change'
        }
      ],
      stage: [
        {
          required: true,
          message: '请选择模具开制阶段',
          trigger: 'change'
        }
      ],
      type: [
        {
          required: formData.value.useNewMold === +YesNoEnum.N,
          message: '请选择模具类型',
          trigger: 'change'
        }
      ],
      sizeValueId: [
        {
          required: true,
          message: '请选择模具尺码段',
          trigger: 'change'
        }
      ],
      head: [
        {
          required: true,
          message: '请选择楦头类别',
          trigger: 'change'
        }
      ],
      soleMaterial: [
        {
          required: formData.value.type === MoldCategoryEnum.BOTTOM,
          message: '请选择鞋底材质',
          trigger: 'change'
        }
      ],
      targetAudience: [
        {
          required: true,
          message: '请选择适用人群',
          trigger: 'change'
        }
      ],
      estimatedOrderVolume: [
        {
          required: true,
          message: '请输入预测订单量',
          trigger: 'blur'
        }
      ],
      designer: [
        {
          required: true,
          message: '请选择设计师',
          trigger: 'change'
        }
      ]
    }
  })

  const fetchMoldProcessInfo = async () => {
    if (isCreate.value) {
      return
    }
    const loading = ElLoading.service({
      fullscreen: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    const [error, result] = await (id.value
      ? getMoldProcessInfo(+id.value)
      : getMoldProcessInfoByVersionId(+versionId.value!))
    loading.close()
    if (error === null && result?.datas) {
      formData.value = cloneDeep(result.datas)
    }
  }

  return {
    formRef,
    formData,
    formRules,
    fetchMoldProcessInfo
  }
}

const { formRef, formData, formRules, fetchMoldProcessInfo } = useQueryInfo()

const handleEditMoldProcess = () => {
  const status = formData.value.statusCode
  if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
    ElMessage.warning('审批中/禁用数据不允许修改')
    return
  }
  router.push({
    name: 'EditMoldProcess',
    query: {
      id: formData.value.id
    }
  })
}
onActivated(async () => {
  if (isView.value) {
    await fetchMoldProcessInfo()
  }
})

const moldNumberList = ref<Omit<CodeListAPI.Data, 'designUrl' | 'fileDTO'>[]>([])

const handleQuerySampleMoldCode = async () => {
  const { linkProductNumber, type } = unref(formData)
  const [error, result] = await getMoldNumberListWithProductNumberAndType({
    linkProductNumber,
    type
  })
  if (error === null && result?.datas) {
    moldNumberList.value = result.datas.map((e) => ({
      ...e,
      disabled: e.status !== +YesNoEnum.Y
    }))
    if (linkProductNumber?.length && type) {
      formData.value.sampleMoldCode = result.datas.map((e) => e.selectorValue!)
    }
  }
}

const { sizeOptions, fetchSizeList } = useSizeOptions(isView.value ? '' : SizeCodeTypeEnums.MOLD)
const sizeIdRef = ref<CascaderInstance>()
const sizeValue = ref('')
const handleSizeIdChange = (value?: number[]) => {
  if (!value?.length) {
    sizeValue.value = ''
    return
  }
  const nodes = sizeIdRef.value?.getCheckedNodes(true)

  const sizeValues = groupBy(
    nodes?.map((node) => {
      const [name, value] = node.pathLabels
      return {
        name,
        value
      }
    }),
    'name'
  )
  sizeValue.value = Object.keys(sizeValues)
    .map((key) => {
      const values = sizeValues[key]
      return `${values.map((e) => e.value).join(', ')} 【${key}】`
    })
    .join('、')
}
Promise.all([fetchSizeList(), fetchMoldProcessInfo()]).then(() => {
  handleSizeIdChange(formData.value.sizeValueId)
})

const submitFn = computed(() => (isEdit.value ? editProcess : createProcess))

const router = useRouter()

async function handleConfirm() {
  if (isView.value) {
    router.push({
      name: 'EditMoldProcess',
      query: {
        id: id.value
      }
    })
    return
  }
  await handleSubmit()
}

const handleSubmit = async () => {
  if (isEdit.value) {
    formData.value.id = Number(id.value)
  }
  const valid = await formRef.value?.validate()
  if (!valid) return
  ElMessageBox.confirm('操作确认', '是否确认提交?', {
    confirmButtonText: '确认发起飞书审批',
    cancelButtonText: '保存为草稿状态',
    distinguishCancelAndClose: true,
    type: 'warning',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm' || action === 'cancel') {
        instance.confirmButtonLoading = instance.cancelButtonLoading = true
        const [error, result] = await submitFn.value({
          ...formData.value,
          statusCode: action === 'confirm' ? StatusEnum.APPROVING : StatusEnum.DRAFT
        })
        instance.confirmButtonLoading = instance.cancelButtonLoading = false
        if (error === null && result) {
          ElMessage.success(result.msg || '提交成功')
          done()
          useClosePage('MoldLibrary')
        }
        return
      }
      if (action === 'close') {
        done()
        return
      }
    }
  }).catch(() => {})
}

const handleClose = () => {
  useClosePage('MoldLibrary')
}

const handleSortOptions = (options: CodeListAPI.Data[]) => {
  // 根据options中的status字段排序，将status === YesNoEnums.N的放到 status === YesNoEnums.Y的后面，不要影响原始数据
  options.sort((a, b) => b.status! - a.status!)
  return options.map((e) => ({
    ...e,
    disabled: e.status === +YesNoEnum.N
  }))
}

/**
 * 勾选确认
 *   关联旧模具编号 供应商公模编号/追加模参考编号 清空且都只读
 *   关联产品编号  模具类型 不能修改，
 *   模具编码 可以下拉选择
 * 勾选取消：
 *    根据关联产品编号与模具类型重新计算模具编号
 *    关联产品编码 模具类型 可以修改
 */
async function handleUseNewMold(e: number) {
  if (e === +YesNoEnum.Y) {
    formData.value.referMoldCode = ''
    formData.value.supplierMoldNumber = ''
    await handleQuerySampleMoldCode()
    return
  }
  await handleQuerySampleMoldCode()
}
</script>

<template>
  <ContentWrap class="info-wrapper">
    <ElForm
      ref="formRef"
      :disabled="isView"
      :model="formData"
      :rules="formRules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      labelWidth="auto"
      scroll-to-error
    >
      <div class="grid grid-cols-2 items-center">
        <ElFormItem label="模具开制图纸" prop="developmentDrawings">
          <OssUpload
            v-model="formData.developmentDrawings"
            :limit="20"
            :size-limit="1024 * 1024 * 100"
            accept="image/*"
            drag
            list-type="picture-card"
            multiple
          />
        </ElFormItem>
        <ElFormItem label="原版大底图" prop="originalHeelMap">
          <OssUpload
            v-model="formData.originalHeelMap"
            :limit="20"
            :size-limit="1024 * 1024 * 100"
            accept="image/*"
            drag
            list-type="picture-card"
            multiple
          />
        </ElFormItem>
        <ElFormItem :class="isView ? '' : 'col-span-2'" label="开发面款" prop="developShape">
          <OssUpload
            v-model="formData.developShape"
            :limit="20"
            :size-limit="1024 * 1024 * 100"
            accept="image/*"
            drag
            list-type="picture-card"
            multiple
          />
        </ElFormItem>
        <ElFormItem v-if="isView" label="关联飞书审批流程ID" prop="feishuSerialNumber">
          <ElInput v-model="formData.feishuSerialNumber" class="w-48" disabled />
        </ElFormItem>
        <ElFormItem label="品牌" prop="brand">
          <SelectPlus
            v-model="formData.brand"
            api-key="baseBrand"
            cache
            clearable
            filterable
            placeholder="请选择品牌"
          />
        </ElFormItem>
        <ElFormItem label="区域" prop="region">
          <SelectPlus
            v-model="formData.region"
            api-key="COMMON_REGION"
            cache
            clearable
            filterable
            placeholder="请选择区域"
          />
        </ElFormItem>
        <ElFormItem label="关联产品编号" prop="linkProductNumber">
          <SelectPlus
            v-model="formData.linkProductNumber"
            :data-method="handleSortOptions"
            :disabled="formData.useNewMold === +YesNoEnum.Y"
            api-key="getProductNumberWithStatusList"
            clearable
            collapse-tags
            collapse-tags-tooltip
            filterable
            multiple
            placeholder="请选择关联产品编号"
            virtualized
            @update:model-value="handleQuerySampleMoldCode"
          />
        </ElFormItem>
        <ElFormItem label="模具开制阶段" prop="stage">
          <SelectPlus
            v-model="formData.stage"
            api-key="MOLD_STAGE"
            cache
            clearable
            filterable
            placeholder="请选择模具开制阶段"
          />
        </ElFormItem>
        <ElFormItem label="模具尺码段" prop="sizeValueId">
          <ElCascader
            ref="sizeIdRef"
            v-model="formData.sizeValueId"
            :options="sizeOptions"
            :props="{
              value: 'id',
              emitPath: false,
              multiple: true
            }"
            clearable
            collapse-tags
            collapse-tags-tooltip
            filterable
            placeholder="请选择尺码段"
            @change="handleSizeIdChange"
          />
        </ElFormItem>
        <ElFormItem label="模具类型" prop="type">
          <SelectPlus
            v-model="formData.type"
            :disabled="formData.useNewMold === +YesNoEnum.Y"
            api-key="MOLD_TYPE"
            cache
            clearable
            placeholder="请选择模具类型"
            @update:model-value="handleQuerySampleMoldCode"
          />
        </ElFormItem>
        <ElFormItem label="尺码值" prop="standardSizeValue">
          <ElInput
            v-model="sizeValue"
            :autosize="{
              minRows: 2,
              maxRows: 4
            }"
            disabled
            placeholder="请选择尺码值"
            type="textarea"
          />
        </ElFormItem>

        <ElFormItem label="楦头类别" prop="head">
          <SelectPlus
            v-model="formData.head"
            api-key="LAST_TYPE"
            cache
            clearable
            filterable
            placeholder="请选择楦头类别"
          />
        </ElFormItem>
        <ElFormItem label="鞋底材质" prop="soleMaterial">
          <SelectPlus
            v-model="formData.soleMaterial"
            api-key="SOLE_MATERIAL"
            cache
            clearable
            filterable
            multiple
            placeholder="请选择鞋底材质"
          />
        </ElFormItem>
        <ElFormItem label="适用人群" prop="targetAudience">
          <SelectPlus
            v-model="formData.targetAudience"
            api-key="PRODUCT_PEOPLE"
            clearable
            filterable
            placeholder="请选择适用人群"
          />
        </ElFormItem>
        <ElFormItem label="关联旧模具编号">
          <template #label>
            <span>关联旧模具编号</span>
            <ElTooltip
              class="!h-full"
              content="Eg：增开新底片，比如开制完成的MD+RB, 后面设计师想新增RB底花共用旧MD"
              enterable
            >
              <Icon icon="ep:question-filled" root-class="h-full" />
            </ElTooltip>
          </template>
          <SelectPlus
            v-model="formData.referMoldCode"
            :data-method="handleSortOptions"
            :disabled="!!formData.supplierMoldNumber || formData.useNewMold === +YesNoEnum.Y"
            api-key="getMouldNumberList"
            clearable
            filterable
            placeholder="请选择关联旧模具编号"
          />
        </ElFormItem>
        <ElFormItem label="模具编号" prop="sampleMoldCode">
          <div class="flex items-center">
            <!-- 用户手动选择的情况下,只能单选, 后台查询可以返回多个 -->
            <SelectPlus
              v-model="formData.sampleMoldCode"
              :disabled="+YesNoEnum.N === +formData.useNewMold!"
              :multiple-limit="1"
              :select-options="moldNumberList"
              api-key="getMouldNumberList"
              multiple
              placeholder="需新开模具"
            />
            <div class="ml-2">
              <ElCheckbox
                v-model="formData.useNewMold"
                :disabled="isView"
                :false-value="+YesNoEnum.N"
                :true-value="+YesNoEnum.Y"
                @change="handleUseNewMold"
              />
              使用现有模具
            </div>
          </div>
        </ElFormItem>
        <ElFormItem label="预测订单量" prop="estimatedOrderVolume">
          <ElInputNumber
            v-model="formData.estimatedOrderVolume"
            :controls="false"
            :min="0"
            :precision="0"
            class="min-w-48 number-input"
          />
        </ElFormItem>
        <ElFormItem label="供应商公模编号/追加模参考编号">
          <ElInput
            v-model="formData.supplierMoldNumber"
            :disabled="!!formData.referMoldCode?.length || formData.useNewMold === +YesNoEnum.Y"
            class="w-48"
            clearable
            maxlength="100"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="设计师" prop="designer">
          <CascadeSelector
            v-model="formData.designer"
            :props="{ emitPath: false }"
            :show-all-levels="false"
            api-key="allUsers"
            cache
            clearable
            placeholder="请选择设计师"
          />
        </ElFormItem>
        <ElFormItem label="备注">
          <ElInput
            v-model="formData.remark"
            :autosize="{ minRows: 4 }"
            :resize="isView ? 'none' : undefined"
            class="w-48"
            clearable
            maxlength="500"
            show-word-limit
            type="textarea"
          />
        </ElFormItem>
      </div>
    </ElForm>
  </ContentWrap>
  <ContentWrap class="mt-2">
    <div class="text-center">
      <ElButton @click="handleClose">返回</ElButton>
      <ElButton v-if="!isView" type="primary" @click="handleSubmit"> 确定 </ElButton>
      <ElButton
        v-if="
          formData.statusCode !== StatusEnum.APPROVING &&
          formData.statusCode !== StatusEnum.PASSED &&
          isView
        "
        v-hasPermi="['editApplyMold']"
        type="primary"
        @click="handleEditMoldProcess"
      >
        <Icon size="20" icon="ep:edit" />
        修改申请单
      </ElButton>
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
.info-wrapper {
  max-height: calc(100vh - 250px);
  overflow: auto;
}

.number-input {
  :deep(.el-input__wrapper) {
    padding-left: 12px;
  }

  :deep(.el-input__inner) {
    text-align: left;
  }
}
</style>
