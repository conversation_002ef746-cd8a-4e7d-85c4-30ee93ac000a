<script lang="ts" setup>
import { statusConst, StatusEnum } from '@/views/basic-library-manage/const'
import {
  getMoldListByPage,
  MoldListPageAPI
} from '@/views/basic-library-manage/mold-library/api/moldList'
import { ref } from 'vue'
import { ElCollapseTransition, ElMessage, ElPagination, FormInstance } from 'element-plus'
import { VxeTableInstance } from 'vxe-table'
import { Pager } from '@/views/basic-library-manage/api/common'
import dayjs from 'dayjs'
import { isEqual } from 'lodash-es'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { Icon } from '@/components/Icon'
import VersionDialog from '@/views/basic-library-manage/mold-library/components/MoldVersionDialog.vue'
import StatusDialog from '@/views/basic-library-manage/mold-library/components/MoldStatusDialog.vue'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { watchDebounced } from '@vueuse/core'
import { formatRowSizeText } from '@/views/basic-library-manage/mold-library/mold-utils'
import { SizeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import { sizeList } from '@/components/Business/SelectPlus/src/api'

defineOptions({
  name: 'MoldList'
})

const router = useRouter()

const useConst = () => {
  // 状态枚举
  const statusMap = statusConst.statusMap
  const statusList = statusConst.statusList
  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '130'
    },
    {
      formLabelLength: '180'
    }
  ])
  return {
    statusList,
    statusMap,
    formLabelLength
  }
}

const { statusList, statusMap, formLabelLength } = useConst()

const useQuery = () => {
  type FormMode = MoldListPageAPI.Params & {
    date?: [string, string]
  }
  const defaultFormData: FormMode = {
    brand: [],
    code: '',
    devSeason: [],
    date: ['', ''],
    nature: [],
    region: [],
    status: [],
    supplierMoldNumber: '',
    type: []
  }
  let lastFormData = {
    ...defaultFormData
  }
  const formData = ref<FormMode>({
    ...defaultFormData
  })
  const formRef = ref<FormInstance>()
  const tableRef = ref<VxeTableInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const tableData = ref<MoldListPageAPI.List>([])
  const queryLoading = ref(false)
  const pager = ref<Pager>({
    current: 1,
    size: 10,
    total: 0
  })
  const queryParams = computed<MoldListPageAPI.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1],
      date: undefined
    }
  })
  const defaultTime: [Date, Date] = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ]

  let controller: AbortController | null = null
  const handleQuery = async () => {
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await getMoldListByPage(queryParams.value, controller.signal)
    queryLoading.value = false
    if (error === null && result?.datas) {
      lastFormData = { ...formData.value }
      const { records } = result.datas
      tableData.value = records.map((row) => {
        return {
          ...row,
          sizeValueIdText: formatRowSizeText(row, sizeOptions.value)
        }
      })
      pager.value.total = result.datas.pager.total
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
    handleQuery()
  }

  onActivated(handleQuery)

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = () => {
    const selected: MoldListPageAPI.List | undefined = tableRef.value?.getCheckboxRecords()
    let reqParam: string
    if (selected && selected?.length > 0) {
      reqParam = JSON.stringify({ idList: selected.map((e) => e.id) })
    } else {
      reqParam = JSON.stringify(queryParams.value)
    }
    exportFn({
      exportType: 'mold-export',
      reqParam
    })
  }

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef
  })

  return {
    formRef,
    tableRef,
    pagerRef,
    formData,
    tableData,
    queryLoading,
    pager,
    defaultTime,
    handleQuery,
    handleReset,
    handleExport,
    exportLoading,
    visible,
    setVisible,
    maxHeight
  }
}

const {
  formRef,
  tableRef,
  pagerRef,
  formData,
  tableData,
  queryLoading,
  pager,
  defaultTime,
  handleQuery,
  handleReset,
  handleExport,
  exportLoading,
  visible,
  setVisible,
  maxHeight
} = useQuery()

// 版本记录弹窗
const useVersionDialog = () => {
  const versionDialogVisible = ref(false)
  const setVersionDialogVisible = () => {
    versionDialogVisible.value = !unref(versionDialogVisible)
  }
  return {
    versionDialogVisible,
    setVersionDialogVisible
  }
}

// 启用/禁用弹窗
const useStatusDialog = () => {
  const statusDialogVisible = ref(false)
  const setStatusDialogVisible = () => {
    statusDialogVisible.value = !unref(statusDialogVisible)
  }
  return {
    statusDialogVisible,
    setStatusDialogVisible
  }
}

const useOperation = () => {
  const currentRow = ref<MoldListPageAPI.Row>()
  const selectedRows = ref<MoldListPageAPI.List>([])

  // 修改模具
  const handleEditMold = () => {
    const selectedRows: MoldListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    const status = selectedRows[0].statusCode
    if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
      ElMessage.warning('审批中/禁用数据不允许修改')
      return
    }
    router.push({
      name: 'EditMold',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 启用/禁用
  const { statusDialogVisible, setStatusDialogVisible } = useStatusDialog()
  const handleChangeStatus = () => {
    const checkedRecords: MoldListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!checkedRecords?.length) {
      ElMessage.warning('请选择数据')
      return
    }
    const hasApproving = checkedRecords.some((item) => item.status === statusMap['approving'])
    if (hasApproving) {
      ElMessage.warning('存在审批中的数据')
      return
    }
    selectedRows.value = checkedRecords
    setStatusDialogVisible()
  }

  // 新增模具
  const handleCreateMold = () => {
    router.push({
      name: 'CreateMold'
    })
  }

  // 保管书
  const handleMoldBook = () => {
    const selectedRows: MoldListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    router.push({
      name: 'MoldBook',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 版本记录
  const { versionDialogVisible, setVersionDialogVisible } = useVersionDialog()
  const handleOpenVersionDialog = (row: MoldListPageAPI.Row) => {
    currentRow.value = row
    setVersionDialogVisible()
  }

  return {
    currentRow,
    selectedRows,
    handleMoldBook,
    handleEditMold,
    handleCreateMold,
    versionDialogVisible,
    handleOpenVersionDialog,
    handleChangeStatus,
    statusDialogVisible
  }
}

const {
  handleMoldBook,
  handleEditMold,
  versionDialogVisible,
  handleOpenVersionDialog,
  currentRow,
  selectedRows,
  handleChangeStatus,
  statusDialogVisible
} = useOperation()

const sizeOptions = ref<SizeListAPI.Data[]>([])

async function querySizeList() {
  const { datas } = await sizeList()
  if (datas) {
    sizeOptions.value = datas
  }
}

onMounted(async () => {
  await Promise.all([querySizeList(), handleQuery()])
  watchDebounced(
    formData,
    async () => {
      await handleQuery()
    },
    { deep: true, debounce: 400 }
  )
})
</script>

<template>
  <div>
    <div class="max-h-[calc(100vh_-_250px)] overflow-x-hidden overflow-y-auto">
      <ElForm ref="formRef" :label-width="formLabelLength" :model="formData">
        <div class="grid grid-cols-3 items-center">
          <ElFormItem label="模具编码" prop="code">
            <ElInput
              v-model="formData.code"
              clearable
              placeholder="请输入模具编码，支持模糊查询"
              @change="handleQuery"
            />
          </ElFormItem>
          <ElFormItem label="模具类型" prop="type">
            <SelectPlus
              v-model="formData.type"
              api-key="MOLD_TYPE"
              clearable
              collapse-tags
              collapse-tags-tooltip
              multiple
              placeholder="请选择模具类型"
            />
          </ElFormItem>
          <ElFormItem label="" label-width="0">
            <ElButton text @click="setVisible">
              {{ visible ? '收起' : '展开' }}
              <Icon
                :class="visible ? 'rotate-90' : ''"
                class="transform transition duration-400"
                icon="ant-design:down-outlined"
              />
            </ElButton>
            <ElButton :loading="queryLoading" type="primary" @click="handleQuery">
              <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
              查询
            </ElButton>
            <ElButton :loading="queryLoading" @click="handleReset">
              <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
              重置
            </ElButton>
            <ElButton :loading="exportLoading" type="primary" @click="handleExport">
              <Icon v-show="!exportLoading" class="mr-1" icon="ep:upload-filled" />
              导出
            </ElButton>
          </ElFormItem>
          <ElFormItem class="col-span-2" label="品牌" prop="brand">
            <SelectPlus v-model="formData.brand" api-key="baseBrand" checkbox checkbox-button />
          </ElFormItem>
          <ElCollapseTransition>
            <div v-show="visible" class="col-span-2 grid grid-cols-2 items-center">
              <ElFormItem label="状态" prop="status">
                <ElScrollbar>
                  <ElCheckboxGroup
                    v-model="formData.status"
                    class="flex flex-nowrap"
                    @change="handleQuery"
                  >
                    <ElCheckboxButton
                      v-for="item in statusList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </ElCheckboxGroup>
                </ElScrollbar>
              </ElFormItem>
              <ElFormItem label="区域" prop="region">
                <SelectPlus
                  v-model="formData.region"
                  api-key="COMMON_REGION"
                  checkbox
                  checkbox-button
                  class="flex flex-nowrap"
                />
              </ElFormItem>
              <ElFormItem label="模具性质" prop="nature">
                <SelectPlus
                  v-model="formData.nature"
                  api-key="MOLD_NATURE"
                  checkbox
                  checkbox-button
                  class="flex flex-nowrap"
                />
              </ElFormItem>
              <ElFormItem label="操作时间段" prop="date">
                <ElDatePicker
                  v-model="formData.date"
                  :default-time="defaultTime"
                  class="max-w-96"
                  clearable
                  end-placeholder="结束时间"
                  range-separator="~"
                  start-placeholder="开始时间"
                  type="daterange"
                  unlink-panels
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
              <ElFormItem label="供应商模具编号" prop="supplierMoldNumber">
                <ElInput
                  v-model="formData.supplierMoldNumber"
                  clearable
                  placeholder="请输入供应商模具编号, 支持模糊查询"
                  @change="handleQuery"
                />
              </ElFormItem>
              <ElFormItem label="开发季节" prop="devSeason">
                <SelectPlus
                  v-model="formData.devSeason"
                  api-key="COMMON_DEV_SEASON"
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  multiple
                  placeholder="请选择开发季节"
                />
              </ElFormItem>
            </div>
          </ElCollapseTransition>
        </div>
      </ElForm>
      <div class="mb-[10px] min-h-8">
        <ElButton v-hasPermi="['editMold']" type="primary" @click="handleEditMold">
          <Icon icon="ep:edit" />
          <span class="text-[14px]">修改模具</span>
        </ElButton>
        <ElButton v-hasPermi="['mold:changeStatus']" type="primary" @click="handleChangeStatus">
          <Icon icon="ep:share" />
          <span class="text-[14px]">启用/禁用</span>
        </ElButton>
        <ElButton v-hasPermi="['mold:manageBook']" type="primary" @click="handleMoldBook">
          <Icon icon="carbon:book" />
          <span class="text-[14px]">管理保管书</span>
        </ElButton>
      </div>
      <div>
        <VxeTable
          ref="tableRef"
          :data="tableData"
          :loading="queryLoading"
          :max-height="maxHeight - 75"
          :show-header-overflow="false"
        >
          <VxeColumn fixed="left" type="checkbox" width="40" />
          <VxeColumn title="序号" fixed="left" type="seq" width="60" />
          <VxeColumn field="code" fixed="left" show-overflow title="模具编码" width="90">
            <template #default="{ row }: { row: MoldListPageAPI.Row }">
              <router-link :to="{ name: 'ViewMold', query: { id: row.id } }">
                <span class="p-0 max-w-full cursor-pointer text-blue-500">
                  {{ row.code }}
                </span>
              </router-link>
            </template>
          </VxeColumn>
          <VxeColumn
            fixed="left"
            :cell-render="{ name: 'Image' }"
            field="thumbnail"
            title="缩略图"
            width="100"
          />
          <VxeColumn field="typeCodeItemName" min-width="100" title="模具类型" />
          <VxeColumn field="brandItemName" min-width="100" title="品牌" />
          <VxeColumn field="headItemName" title="楦头类别" width="80" />
          <VxeColumn field="targetAudienceItemName" min-width="100" title="适用人群" />
          <VxeColumn field="designerItemName" min-width="100" title="设计师" />
          <VxeColumn field="regionCodeItemName" min-width="100" title="区域" />
          <VxeColumn field="linkProductNumber" min-width="100" title="关联产品编号" />
          <VxeColumn
            :cell-render="{ name: 'Ellipsis', props: { maxRow: 2, separator: ',' } }"
            field="sizeIdItemName"
            min-width="100"
            title="模具尺码段"
          />
          <VxeColumn
            :cell-render="{ name: 'Ellipsis', props: { maxRow: 2, separator: '、' } }"
            field="sizeValueIdText"
            min-width="100"
            title="尺码值"
          />
          <VxeColumn field="supplier" min-width="120" title="模具供应商" />
          <VxeColumn field="moldOpeningTime" min-width="100" title="开模时间" />
          <VxeColumn field="moldFinishTime" min-width="100" title="模具完成时间" />
          <VxeColumn field="quantity" min-width="100" title="模具数量(套)" />
          <VxeColumn field="amount" min-width="100" title="模具金额(¥)" />
          <VxeColumn field="linkMoldApply" min-width="130" title="关联的申请单编号">
            <template #default="{ row }: { row: MoldListPageAPI.Row }">
              <router-link
                v-for="item in row.linkMoldApply"
                :key="item.code"
                :to="{ name: 'ViewMoldProcess', query: { id: item.id } }"
              >
                <div class="p-0 max-w-full cursor-pointer text-blue-500 hover:underline">
                  {{ item.code }}
                </div>
              </router-link>
            </template>
          </VxeColumn>
          <VxeColumn field="status" fixed="right" title="状态" width="80" />
          <VxeColumn field="modifyByIdItemName" title="操作人" width="120" />
          <VxeColumn field="modifyTime" title="操作时间" width="80" />
          <VxeColumn :show-overflow="false" fixed="right" title="操作" width="120">
            <template #default="{ row }">
              <ElButton size="small" text type="primary" @click="handleOpenVersionDialog(row)">
                版本记录
              </ElButton>
            </template>
          </VxeColumn>
        </VxeTable>
      </div>
    </div>
    <ElPagination
      ref="pagerRef"
      v-model:current-page="pager.current"
      v-model:page-size="pager.size"
      :total="pager.total"
      background
      class="mt-4"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleQuery"
      @current-change="handleQuery"
    />
    <VersionDialog v-model="versionDialogVisible" :current-row="currentRow" />
    <StatusDialog
      v-model="statusDialogVisible"
      :selected-rows="selectedRows"
      @refresh="handleQuery"
    />
  </div>
</template>

<style lang="less" scoped></style>
