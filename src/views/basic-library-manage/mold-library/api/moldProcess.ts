import to from 'await-to-js'
import { service } from '@/config/fetch/service'

export namespace MoldProcessListAPI {
  export interface Params {
    /**
     * 品牌
     */
    brand?: string[]
    /**
     * 申请单编号
     */
    code?: string
    /**
     * 设计师
     */
    designer?: string[]
    /**
     * 结束时间
     */
    endTime?: string
    idList?: string[]
    /**
     * 区域
     */
    region?: string[]
    /**
     * 开始时间
     */
    startTime?: string
    /**
     * 状态
     */
    statusCode?: string[]
    /**
     * 模具类型
     */
    type?: string[]
  }
  export interface Row {
    sizeValueId?: number[]
    /**
     * 品牌
     */
    brand?: number | null
    /**
     * 申请单编号
     */
    code?: null | string
    /**
     * 创建时间
     */
    createTime?: null | string
    /**
     * 设计师
     */
    designer?: number | null
    /**
     * 模具开制图纸
     */
    developmentDrawings: BaseFileDTO[]
    /**
     * 飞书审批流程ID
     */
    feishuSerialNumber?: null | string
    /**
     * 楦头类别
     */
    head?: null | string
    id?: number | null
    /**
     * 关联产品编号
     */
    linkProductNumber?: string[] | null
    /**
     * 操作人
     */
    modifyById?: number | null
    /**
     * 操作时间
     */
    modifyTime?: null | string
    /**
     * 区域
     */
    region?: null | string
    /**
     * 关联模具编号
     */
    sampleMoldCode?: string[] | null
    /**
     * 尺码段id
     */
    sizeId?: number[] | null
    sizeIdItemName?: string[] | null
    /**
     * 模具开制阶段
     */
    stage?: null | string
    /**
     * 样品码
     */
    standardSizeValue?: string[] | null
    /**
     * 启用状态
     */
    status?: null | string
    /**
     * 启用状态code
     */
    statusCode?: null | string
    /**
     * 适用人群
     */
    targetAudience?: null | string
    /**
     * 模具类型
     */
    type?: null | string
    [property: string]: any
  }
  export type List = Row[]
  export type Response = PagedResponseData<Row>
  export type Request = Params & PageParams
}

export function getMoldProcessListApi(params: MoldProcessListAPI.Request, signal?: AbortSignal) {
  return to<MoldProcessListAPI.Response>(
    service.get('/pdm-base/mold/apply/page', { params, signal })
  )
}
