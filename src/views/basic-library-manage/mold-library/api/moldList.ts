import { service } from '@/config/axios/service'
import to from 'await-to-js'

export namespace MoldListPageAPI {
  export interface Params {
    /**
     * 品牌
     */
    brand?: string[]
    /**
     * 模具编码
     */
    code?: string
    /**
     * 开发季节
     */
    devSeason?: string[]
    /**
     * 结束时间
     */
    endTime?: string
    idList?: string[]
    /**
     * 模具性质
     */
    nature?: string[]
    /**
     * 区域
     */
    region?: string[]
    /**
     * 开始时间
     */
    startTime?: string
    /**
     * 状态
     */
    status?: string[]
    /**
     * 供应商模具编号
     */
    supplierMoldNumber?: string
    /**
     * 模具类型
     */
    type?: string[]
  }
  export interface Row {
    sizeValueId?: number[]
    /**
     * 模具金额
     */
    amount?: number
    /**
     * 品牌
     */
    brand?: number
    /**
     * 模具编码
     */
    code?: string
    /**
     * 设计师
     */
    designer?: number[]
    /**
     * 楦头类别
     */
    head?: string
    id?: number
    /**
     * 关联的申请单
     */
    linkApplyCode?: string[]
    linkMoldApply?: {
      id?: number
      code?: string
    }[]
    /**
     * 关联产品编号
     */
    linkProductNumber?: string[]
    /**
     * 操作人
     */
    modifyById?: string
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 模具完成时间
     */
    moldFinishTime?: string
    /**
     * 开模时间
     */
    moldOpeningTime?: string
    /**
     * 模具数量
     */
    quantity?: number
    /**
     * 区域code
     */
    regionCode?: string
    /**
     * 尺码段id数组
     */
    sizeId?: number[]
    sizeIdItemName?: string[]
    /**
     * 标准码数组
     */
    standardSizeValue?: string[]
    /**
     * 状态
     */
    status?: string
    /**
     * 状态code
     */
    statusCode?: string
    /**
     * 模具供应商
     */
    supplier?: string
    /**
     * 适用人群
     */
    targetAudience?: string
    /**
     * 模具缩略图oss地址
     * 模具缩略图
     */
    thumbnail?: BaseFileDTO
    /**
     * 模具类型code
     */
    typeCode?: string
  }
  export type List = Row[]
  export type Request = Params & PageParams
  export type Response = PagedResponseData<Row>
}

export function getMoldListByPage(params: MoldListPageAPI.Request, signal?: AbortSignal) {
  return to<MoldListPageAPI.Response>(service.get('/pdm-base/mold/page', { params, signal }))
}
