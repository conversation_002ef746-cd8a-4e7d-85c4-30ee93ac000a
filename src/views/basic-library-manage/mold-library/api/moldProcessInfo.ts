import to from 'await-to-js'
import { service } from '@/config/fetch/service'
import { CodeListAPI } from '@/components/Business/SelectPlus/src/api/types'

export namespace MoldProcessInfoAPI {
  export interface QueryMoldApplyDetailResp {
    /**
     * 品牌
     */
    brand?: number
    /**
     * 申请单编号
     */
    code?: string
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    /**
     * 设计师
     */
    designer?: number
    /**
     * 模具开制图纸
     */
    developmentDrawings?: BaseFileDTO[]
    /**
     * 开发面款
     */
    developShape?: BaseFileDTO[]
    /**
     * 预测订单量
     */
    estimatedOrderVolume?: number
    /**
     * 飞书审批流程ID
     */
    feishuSerialNumber?: string
    /**
     * 楦头类别
     */
    head?: string
    id?: number
    /**
     * 关联产品编号
     */
    linkProductNumber?: string[]
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 原版大底图
     */
    originalHeelMap?: BaseFileDTO[]
    /**
     * 参考模具编号
     */
    referMoldCode?: string
    /**
     * 区域
     */
    region?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 模具编号
     */
    sampleMoldCode?: string[]
    /**
     * 尺码段id
     */
    sizeId?: number[]
    /**
     * 鞋底材质
     */
    soleMaterial?: string[]
    /**
     * 模具开制阶段
     */
    stage?: string
    /**
     * 样品码
     */
    standardSizeValue?: string[]
    /**
     * 启用状态
     */
    statusCode?: string
    /**
     * 供应商模具编号
     */
    supplierMoldNumber?: string
    /**
     * 适用人群
     */
    targetAudience?: string
    /**
     * 模具类型
     */
    type?: string
    /**
     * 使用现有模具 1使用 0不使用
     */
    useNewMold?: number
    sizeValueId?: number[]
  }

  export type Response = ResponseData<QueryMoldApplyDetailResp>
}

export function getMoldProcessInfo(id: number) {
  return to<MoldProcessInfoAPI.Response>(service.get(`/pdm-base/mold/apply/detail/${id}`))
}

export function getMoldProcessInfoByVersionId(id: number) {
  return to<MoldProcessInfoAPI.Response>(service.get(`/pdm-base/mold/apply/versionDetail/${id}`))
}

export namespace CreateProcessAPI {
  export type Request = MoldProcessInfoAPI.QueryMoldApplyDetailResp
  export type Response = BasicResponseData
}

export function createProcess(data: CreateProcessAPI.Request) {
  return to<CreateProcessAPI.Response>(service.post('/pdm-base/mold/apply/save', data))
}

export function editProcess(data: CreateProcessAPI.Request) {
  return to<CreateProcessAPI.Response>(service.post('/pdm-base/mold/apply/update', data))
}

export namespace MoldNumberListWithProductNumberAndTypeAPI {
  export interface Request {
    /**
     * 关联产品编号
     */
    linkProductNumber?: string[]
    /**
     * 模具类型
     */
    type?: string
  }
  export type Response = ResponseData<CodeListAPI.Data[]>
}

export function getMoldNumberListWithProductNumberAndType(
  params: MoldNumberListWithProductNumberAndTypeAPI.Request
) {
  return to<MoldNumberListWithProductNumberAndTypeAPI.Response>(
    service.get('/pdm-base/mold/codeStatusListByType', { params })
  )
}
