import { service } from '@/config/axios/service'
import to from 'await-to-js'
import { UpdateStatusAPI } from '../../api/common'

export interface StatusData {
  id: number
  oldStatus: string
  newStatus: string
}

export interface IReqChangeStatus {
  updateStatusReqList: StatusData[]
}

export interface IResChangeStatus {
  isSuccess: boolean
  msg: string
}

export function changeStatus(data: UpdateStatusAPI.Request) {
  return to<UpdateStatusAPI.Response>(service.post(`/pdm-base/mold/startOrBan`, data))
}
