import to from 'await-to-js'
import { service } from '@/config/axios/service'

export namespace MoldInfoAPI {
  export interface QueryMoldDetailResp {
    /**
     * 模具金额
     */
    amount?: number
    /**
     * 模具归属
     */
    attribution?: string
    /**
     * 品牌
     */
    brand?: number
    /**
     * 模具编码
     */
    code?: string
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    /**
     * 设计师
     */
    designer?: number[]
    /**
     * 模具开制图纸
     */
    developmentDrawings?: BaseFileDTO[]
    /**
     * 开发季节
     */
    developmentSeason?: string
    /**
     * 开发面款
     */
    developShape?: BaseFileDTO[]
    /**
     * 楦头类别
     */
    head?: string
    id?: number
    /**
     * 楦型id数组
     */
    lastId?: number[]
    /**
     * 关联的申请单
     */
    linkApplyCode?: string[]
    /**
     * 关联产品编号
     */
    linkProductNumber?: string[]
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 模具保管书
     */
    moldCustodyList?: MoldCustodyResp[]
    /**
     * 模具完成时间
     */
    moldFinishTime?: string
    /**
     * 模具图纸文件
     */
    moldImgFile?: BaseFileDTO[]
    /**
     * 开模时间
     */
    moldOpeningTime?: string
    /**
     * 开模类型
     */
    moldOpeningType?: string[]
    /**
     * 模具性质
     */
    nature?: string
    /**
     * 原版大底图
     */
    originalHeelMap?: BaseFileDTO[]
    /**
     * 物性要求
     */
    physicalRequirements?: string
    /**
     * 模具数量
     */
    quantity?: number
    /**
     * 参考模具编号
     */
    referMoldCode?: string
    /**
     * 区域
     */
    region?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 尺码段id数组
     */
    sizeId?: number[]
    /**
     * 鞋底材质
     */
    soleMaterial?: string[]
    /**
     * 模具开制阶段
     */
    stage?: string
    /**
     * 标准码数组
     */
    standardSizeValue?: string[]
    /**
     * 状态
     */
    status?: string
    /**
     * 状态code
     */
    statusCode?: string
    /**
     * 模具结构
     */
    structure?: string
    /**
     * 模具供应商
     */
    supplier?: string
    /**
     * 供应商模具编号
     */
    supplierMoldNumber?: string
    /**
     * 适用人群
     */
    targetAudience?: string
    /**
     * 模具缩略图oss地址
     * 模具缩略图
     */
    thumbnail?: BaseFileDTO
    /**
     * 模具类型
     */
    type: string
    sizeValueId: number[]
  }

  export interface MoldCustodyResp {
    /**
     * 存放地点
     */
    address?: string
    /**
     * 保管书文件oss地址
     */
    custodianFile?: BaseFileDTO[]
    /**
     * 保管人id
     */
    custodianId?: number
    /**
     * 下单工厂
     */
    factory?: string
    /**
     * id
     */
    id?: number
    /**
     * 操作人id
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 状态
     */
    status?: string
    /**
     * 状态code
     */
    statusCode?: string
    /**
     * 生成时间
     */
    time?: string
  }

  export type Response = ResponseData<QueryMoldDetailResp>
}
export function getMoldInfo(id: number) {
  return to<MoldInfoAPI.Response>(service.get(`/pdm-base/mold/detail/${id}`))
}

export function getMoldInfoByVersionId(id: number) {
  return to<MoldInfoAPI.Response>(service.get(`/pdm-base/mold/versionDetail/${id}`))
}

export namespace CreateMoldAPI {
  export type Params = Omit<MoldInfoAPI.QueryMoldDetailResp, 'id' | 'status' | 'statusCode'> & {
    updateType: number
  }
  export type Request = Params
  export type Response = BasicResponseData
}
export function createMold(data: CreateMoldAPI.Request) {
  return to<CreateMoldAPI.Response>(service.post('/pdm-base/mold/save', data))
}

export namespace UpdateMoldAPI {
  export type Params = Omit<MoldInfoAPI.QueryMoldDetailResp, 'status' | 'statusCode'>
  export type Request = Params
  export type Response = BasicResponseData
}
export function updateMold(data: UpdateMoldAPI.Request) {
  return to<UpdateMoldAPI.Response>(service.post('/pdm-base/mold/update', data))
}
