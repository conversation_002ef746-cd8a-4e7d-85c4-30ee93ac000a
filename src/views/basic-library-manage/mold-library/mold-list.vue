<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { ref } from 'vue'
import MoldCheckList from '@/views/basic-library-manage/mold-library/components/MoldCheckList.vue'
import MoldProcess from '@/views/basic-library-manage/mold-library/components/MoldProcess.vue'
import { hasPermission } from '@/directives/permission/hasPermi'

defineOptions({
  name: 'MoldLibrary'
})

const hasMoldProcessPermission = hasPermission(['moldProcessList'])

const activeName = ref(hasMoldProcessPermission ? '1' : '2')
</script>

<template>
  <ContentWrap class="mold-library">
    <ElTabs v-model="activeName">
      <ElTabPane v-if="hasPermission(['moldProcessList'])" label="模具开制流程管理" name="1">
        <MoldProcess v-if="activeName === '1'" />
      </ElTabPane>
      <ElTabPane v-if="hasPermission(['moldList'])" label="模具清单管理" name="2">
        <MoldCheckList v-if="activeName === '2'" />
      </ElTabPane>
    </ElTabs>
  </ContentWrap>
</template>

<style lang="less" scoped>
.mold-library {
  > :deep(.el-card__body) {
    padding-top: 10px;
  }
}
</style>
