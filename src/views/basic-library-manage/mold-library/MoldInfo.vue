<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { CascaderInstance, ElLoading, ElMessage, FormInstance, FormRules } from 'element-plus'
import {
  createMold,
  getMoldInfo,
  getMoldInfoByVersionId,
  MoldInfoAPI,
  updateMold
} from '@/views/basic-library-manage/mold-library/api/moldInfo'
import { useBasicLibraryDictStore } from '@/views/basic-library-manage/store/dict'
import { Icon } from '@/components/Icon'
import { MoldCategoryEnum, statusConst, StatusEnum } from '@/views/basic-library-manage/const'
import {
  getLastReferencedListByIdList,
  LastReferencedListAPI
} from '@/views/basic-library-manage/api/common'
import LastInfoDialog from '@/views/basic-library-manage/components/LastInfoDialog.vue'
import { LastListPageAPI } from '@/views/basic-library-manage/last-library/api/last-list'
import { VxeTableInstance } from 'vxe-table'
import { SelectPlus } from '@/components/Business/SelectPlus'
import { groupBy, omit } from 'lodash-es'
import ElCascader from '@/components/Cascader/src/Cascader'
import { useSizeOptions } from '@/utils/useSizeOptions'
import { SizeCodeTypeEnums } from '@/enums'

defineOptions({
  name: 'MoldInfo'
})

const activeNames = ref(['1', '2', '3', '4'])

const route = useRoute()

const id = computed(() => {
  return route.query.id
})

const versionId = computed(() => {
  return route.query.versionId
})

const useType = () => {
  const isEdit = computed(() => {
    return route.name === 'EditMold'
  })

  const isView = computed(() => {
    return route.name === 'ViewMold'
  })

  const isMoldBook = computed(() => {
    return route.name === 'MoldBook'
  })

  return {
    isEdit,
    isView,
    isMoldBook
  }
}

const { isEdit, isView, isMoldBook } = useType()

const useConst = () => {
  const dictStore = useBasicLibraryDictStore()
  const store = computed(() => ({
    brandMap: dictStore.brandMap,
    lastSexMap: dictStore.lastSexMap,
    lastTypeMap: dictStore.lastTypeMap,
    lastMarketMap: dictStore.lastMarketMap,
    lastHeightMap: dictStore.lastHeightMap,
    commonSeasonMap: dictStore.commonDevSeasonMap
  }))

  const statusList = statusConst.statusList.filter((e) => e.value !== StatusEnum.APPROVING)

  return {
    store,
    statusList
  }
}

const { store, statusList } = useConst()

const useQueryInfo = () => {
  const formRef = ref<FormInstance>()
  const formData = ref<MoldInfoAPI.QueryMoldDetailResp>({
    thumbnail: undefined,
    developmentDrawings: [],
    developShape: [],
    originalHeelMap: [],
    type: '',
    brand: undefined,
    stage: '',
    region: '',
    head: '',
    designer: undefined,
    targetAudience: '',
    referMoldCode: '',
    nature: '',
    soleMaterial: [],
    linkProductNumber: [],
    supplierMoldNumber: '',
    statusCode: '',
    remark: '',
    sizeId: [],
    standardSizeValue: [],
    supplier: '',
    moldOpeningTime: '',
    attribution: '美迈',
    moldFinishTime: '',
    moldOpeningType: [],
    quantity: undefined,
    amount: undefined,
    moldImgFile: [],
    physicalRequirements: '',
    sizeValueId: []
  })
  const formRules = computed<FormRules<MoldInfoAPI.QueryMoldDetailResp>>(() => {
    return {
      thumbnail: [
        {
          required: true,
          message: '请上传缩略图'
        }
      ],
      type: [
        {
          required: true,
          message: '请选择模具类型',
          trigger: 'change'
        }
      ],
      brand: [
        {
          required: true,
          message: '请选择品牌',
          trigger: 'change'
        }
      ],
      stage: [
        {
          required: true,
          message: '请选择模具开制阶段',
          trigger: 'change'
        }
      ],
      region: [
        {
          required: true,
          message: '请选择区域',
          trigger: 'change'
        }
      ],
      head: [
        {
          required: true,
          message: '请选择楦头类别',
          trigger: 'change'
        }
      ],
      designer: [
        {
          required: true,
          message: '请选择设计师',
          trigger: 'change'
        }
      ],
      targetAudience: [
        {
          required: true,
          message: '请选择适用人群',
          trigger: 'change'
        }
      ],
      nature: [
        {
          required: true,
          message: '请选择模具性质',
          trigger: 'change'
        }
      ],
      soleMaterial: [
        {
          required: formData.value.type === MoldCategoryEnum.BOTTOM,
          message: '请选择鞋底材质',
          trigger: 'change'
        }
      ],
      linkProductNumber: [
        {
          required: true,
          message: '请选择关联产品编号',
          trigger: 'change'
        }
      ],
      sizeId: [
        {
          required: true,
          message: '请选择模具尺码段',
          trigger: 'change'
        }
      ],
      sizeValueId: [
        {
          required: true,
          message: '请选择尺码值',
          trigger: 'change'
        }
      ]
    }
  })

  const { sizeOptions, fetchSizeList } = useSizeOptions(isView.value ? '' : SizeCodeTypeEnums.MOLD)
  const sizeIdRef = ref<CascaderInstance>()
  const sizeValue = ref('')
  const handleSizeIdChange = (value?: number[]) => {
    if (!value?.length) {
      sizeValue.value = ''
      return
    }
    const nodes = sizeIdRef.value?.getCheckedNodes(true)

    const sizeValues = groupBy(
      nodes?.map((node) => {
        const [name, value] = node.pathLabels
        return {
          name,
          value
        }
      }),
      'name'
    )
    sizeValue.value = Object.keys(sizeValues)
      .map((key) => {
        const values = sizeValues[key]
        return `${values.map((e) => e.value).join(', ')} 【${key}】`
      })
      .join('、')
  }

  const referencedList = ref<LastReferencedListAPI.Data[]>([])

  const fetchMoldInfo = async () => {
    const loading = ElLoading.service({
      fullscreen: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    const [error, result] = await (id.value
      ? getMoldInfo(+id.value)
      : getMoldInfoByVersionId(+versionId.value!))
    loading.close()
    if (error === null && result?.datas) {
      formData.value = omit(result.datas, 'standardSizeValue')
      if (result.datas.lastId) {
        const [referenceError, referenceResult] = await getLastReferencedListByIdList(
          result.datas.lastId
        )
        if (referenceError === null && referenceResult?.datas) {
          referencedList.value = referenceResult.datas
        }
      }
    }
  }

  return {
    formRef,
    formData,
    formRules,
    fetchMoldInfo,
    referencedList,
    sizeOptions,
    fetchSizeList,
    sizeIdRef,
    sizeValue,
    handleSizeIdChange
  }
}

const {
  formRef,
  formData,
  formRules,
  fetchMoldInfo,
  referencedList,
  sizeOptions,
  fetchSizeList,
  sizeIdRef,
  sizeValue,
  handleSizeIdChange
} = useQueryInfo()

onActivated(() => {
  if (isView.value) {
    fetchMoldInfo()
  }
})

Promise.all([fetchMoldInfo(), fetchSizeList()]).then(() => {
  handleSizeIdChange(formData.value.sizeValueId)
})

const router = useRouter()
const useOperation = () => {
  const submitLoading = ref(false)
  const submitFn = computed(() => {
    if (isEdit.value || isMoldBook.value) {
      return updateMold
    }
    return createMold
  })

  async function handleConfirm() {
    if (isView.value) {
      router.push({
        name: 'EditMold',
        query: {
          id: id.value
        }
      })
      return
    }
    await handleSubmit()
  }

  const handleSubmit = async () => {
    if (isEdit.value) {
      formData.value.id = Number(id.value)
    }
    const valid = await formRef.value?.validate()
    if (valid) {
      if (isMoldBook.value) {
        if (!formData.value.moldCustodyList?.length) {
          ElMessage.warning('请添加模具保管书')
          return
        }
        const validField = formData.value.moldCustodyList.every(
          (e) => e.factory && e.time && e.custodianId && e.custodianFile?.length
        )
        if (!validField) {
          ElMessage.warning('模具保管书信息不完整')
          return
        }
      }
      submitLoading.value = true
      const [error, result] = await submitFn.value({
        ...formData.value,
        updateType: isMoldBook.value ? 1 : 0
      })
      submitLoading.value = false
      if (error === null && result) {
        useClosePage('MoldLibrary')
        ElMessage.success(result.msg || '保存成功')
      } else {
        ElMessage.error(error?.message || '保存失败')
      }
    }
  }

  const handleClose = () => {
    useClosePage('MoldLibrary')
  }

  // 关联楦型
  const lastTableRef = ref<VxeTableInstance | null>(null)
  const lastInfoDialogVisible = ref(false)
  const handleOpenLastInfoDialog = () => {
    lastInfoDialogVisible.value = true
  }
  const handlePickLast = (lastList: LastListPageAPI.List) => {
    if (!formData.value.lastId) {
      formData.value.lastId = []
    }
    referencedList.value = referencedList.value.concat(
      lastList.filter((e) => !formData.value.lastId?.includes(e.id!))
    )
    formData.value.lastId = [...new Set(formData.value.lastId.concat(lastList.map((e) => e.id!)))]
    lastTableRef.value?.loadData(referencedList.value)
  }
  const handleRemoveLast = (index: number) => {
    formData.value.lastId?.splice(index, 1)
    referencedList.value?.splice(index, 1)
    lastTableRef.value?.loadData(referencedList.value)
  }

  // 模具保管书
  const moldCustodyRef = ref<VxeTableInstance | null>(null)
  const handleAddMoldCustody = () => {
    if (!formData.value.moldCustodyList) {
      formData.value.moldCustodyList = []
    }
    formData.value.moldCustodyList?.push({
      statusCode: StatusEnum.DRAFT as string
    })
    moldCustodyRef.value?.loadData(formData.value.moldCustodyList)
  }
  const handleDelMoldCustody = (index: number) => {
    formData.value.moldCustodyList?.splice(index, 1)
    moldCustodyRef.value?.loadData(formData.value.moldCustodyList || [])
  }

  return {
    submitLoading,
    handleClose,
    handleConfirm,
    lastTableRef,
    handlePickLast,
    handleRemoveLast,
    lastInfoDialogVisible,
    handleOpenLastInfoDialog,
    moldCustodyRef,
    handleAddMoldCustody,
    handleDelMoldCustody
  }
}
const handleEditMold = () => {
  const status = formData.value.statusCode
  if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
    ElMessage.warning('审批中/禁用数据不允许修改')
    return
  }
  router.push({
    name: 'EditMold',
    query: {
      id: formData.value.id
    }
  })
}
const handleMoldBook = () => {
  router.push({
    name: 'MoldBook',
    query: {
      id: formData.value.id
    }
  })
}
const {
  submitLoading,
  handleClose,
  handleConfirm,
  lastTableRef,
  handlePickLast,
  handleRemoveLast,
  lastInfoDialogVisible,
  handleOpenLastInfoDialog,
  moldCustodyRef,
  handleAddMoldCustody,
  handleDelMoldCustody
} = useOperation()
</script>

<template>
  <LastInfoDialog v-model="lastInfoDialogVisible" multiple @submit="handlePickLast" />
  <ContentWrap class="info-wrapper">
    <ElCollapse v-model="activeNames">
      <ElForm
        ref="formRef"
        :disabled="isView || isMoldBook"
        :model="formData"
        :rules="formRules"
        :scroll-into-view-options="{ behavior: 'smooth' }"
        labelWidth="auto"
        scroll-to-error
      >
        <ElCollapseItem name="1" title="基础信息">
          <template #title>
            <div class="font-bold text-base">基础信息</div>
          </template>
          <div class="grid grid-cols-2 items-center">
            <ElFormItem label="模具缩略图" prop="thumbnail">
              <OssUpload
                :limit="1"
                :modelValue="formData.thumbnail ? [formData.thumbnail] : []"
                :size-limit="1024 * 1024 * 10"
                accept="image/*"
                disabled
                drag
                list-type="picture-card"
                multiple
                @update:model-value="(val) => (formData.thumbnail = val[0])"
              />
            </ElFormItem>
            <ElFormItem label="模具开制图纸" prop="developmentDrawings">
              <OssUpload
                v-model="formData.developmentDrawings"
                :limit="20"
                :size-limit="1024 * 1024 * 100"
                accept="image/*"
                disabled
                drag
                list-type="picture-card"
                multiple
              />
            </ElFormItem>
            <ElFormItem label="开发面款" prop="developShape">
              <OssUpload
                v-model="formData.developShape"
                :limit="20"
                :size-limit="1024 * 1024 * 100"
                accept="image/*"
                disabled
                drag
                list-type="picture-card"
                multiple
              />
            </ElFormItem>
            <ElFormItem label="原版大底图" prop="originalHeelMap">
              <OssUpload
                v-model="formData.originalHeelMap"
                :limit="20"
                :size-limit="1024 * 1024 * 100"
                accept="image/*"
                disabled
                drag
                list-type="picture-card"
                multiple
              />
            </ElFormItem>
            <ElFormItem label="模具类型" prop="type">
              <SelectPlus
                v-model="formData.type"
                api-key="MOLD_TYPE"
                cache
                clearable
                disabled
                placeholder="请选择模具类型"
              />
            </ElFormItem>
            <ElFormItem label="品牌" prop="brand">
              <SelectPlus
                v-model="formData.brand"
                api-key="baseBrand"
                cache
                clearable
                disabled
                filterable
                placeholder="请选择品牌"
              />
            </ElFormItem>
            <ElFormItem label="模具开制阶段" prop="stage">
              <SelectPlus
                v-model="formData.stage"
                api-key="MOLD_STAGE"
                cache
                clearable
                disabled
                filterable
                placeholder="请选择模具开制阶段"
              />
            </ElFormItem>
            <ElFormItem label="区域" prop="region">
              <SelectPlus
                v-model="formData.region"
                api-key="COMMON_REGION"
                cache
                clearable
                disabled
                filterable
                placeholder="请选择区域"
              />
            </ElFormItem>
            <ElFormItem label="楦头类别" prop="head">
              <SelectPlus
                v-model="formData.head"
                api-key="LAST_TYPE"
                cache
                clearable
                disabled
                filterable
                placeholder="请选择楦头类别"
              />
            </ElFormItem>
            <ElFormItem label="设计师" prop="designer">
              <CascadeSelector
                v-model="formData.designer"
                :props="{ emitPath: false }"
                :show-all-levels="false"
                api-key="allUsers"
                cache
                clearable
                disabled
                placeholder="请选择设计师"
              />
            </ElFormItem>
            <ElFormItem label="适用人群" prop="targetAudience">
              <SelectPlus
                v-model="formData.targetAudience"
                api-key="PRODUCT_PEOPLE"
                clearable
                disabled
                filterable
                placeholder="请选择适用人群"
              />
            </ElFormItem>
            <ElFormItem label="参考模具编号">
              <SelectPlus
                v-model="formData.referMoldCode"
                api-key="PRODUCT_PEOPLE"
                clearable
                disabled
                filterable
                placeholder="请选择参考模具编号"
              />
            </ElFormItem>
            <ElFormItem label="模具性质" prop="nature">
              <SelectPlus
                v-model="formData.nature"
                api-key="MOLD_NATURE"
                clearable
                filterable
                placeholder="请选择模具性质"
              />
            </ElFormItem>
            <ElFormItem label="鞋底材质" prop="soleMaterial">
              <SelectPlus
                v-model="formData.soleMaterial"
                api-key="SOLE_MATERIAL"
                cache
                clearable
                collapse-tags
                collapse-tags-tooltip
                disabled
                filterable
                multiple
                placeholder="请选择鞋底材质"
              />
            </ElFormItem>
            <ElFormItem label="关联产品编号" prop="linkProductNumber">
              <ElSelect
                v-model="formData.linkProductNumber"
                clearable
                collapse-tags
                collapse-tags-tooltip
                disabled
                filterable
                multiple
                placeholder="请选择关联产品编号"
              />
            </ElFormItem>
            <ElFormItem label="供应商模具编号">
              <ElInput
                v-model="formData.supplierMoldNumber"
                class="w-48"
                clearable
                disabled
                maxlength="100"
                show-word-limit
              />
            </ElFormItem>
            <ElFormItem label="状态" prop="statusCode">
              <ElRadioGroup v-model="formData.statusCode" disabled>
                <ElRadio v-for="e in statusList" :key="e.value" :label="e.label" :value="e.value" />
              </ElRadioGroup>
            </ElFormItem>
            <ElFormItem label="备注" prop="remark">
              <ElInput
                v-model="formData.remark"
                :autosize="{ minRows: 4 }"
                :resize="isView ? 'none' : undefined"
                class="w-48"
                disabled
                maxlength="500"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
          </div>
        </ElCollapseItem>
        <ElCollapseItem name="2" title="模具详细信息">
          <template #title>
            <div class="font-bold text-base">模具详细信息</div>
          </template>
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="模具尺码段" prop="sizeId">
                <ElCascader
                  ref="sizeIdRef"
                  v-model="formData.sizeValueId"
                  :options="sizeOptions"
                  :props="{
                    value: 'id',
                    emitPath: false,
                    multiple: true
                  }"
                  clearable
                  disabled
                  filterable
                  placeholder="请选择尺码段"
                  @change="handleSizeIdChange"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="尺码值">
                <ElInput
                  v-model="sizeValue"
                  :autosize="{
                    minRows: 2,
                    maxRows: 4
                  }"
                  disabled
                  placeholder="请选择尺码值"
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="模具供应商" prop="supplier">
                <ElInput
                  v-model="formData.supplier"
                  class="w-48"
                  clearable
                  maxlength="100"
                  placeholder="请输入模具供应商"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="开模时间" prop="moldOpeningTime">
                <ElDatePicker
                  v-model="formData.moldOpeningTime"
                  class="min-w-48"
                  clearable
                  format="YYYY-MM-DD"
                  placeholder="年/月/日"
                  type="date"
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="模具归属" prop="attribution">
                <ElInput
                  v-model="formData.attribution"
                  class="w-48"
                  clearable
                  maxlength="100"
                  placeholder="请输入模具归属"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="模具完成时间" prop="moldFinishTime">
                <ElDatePicker
                  v-model="formData.moldFinishTime"
                  class="min-w-48"
                  clearable
                  format="YYYY-MM-DD"
                  placeholder="年/月/日"
                  type="date"
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="模具开模方式" prop="moldOpeningType">
                <SelectPlus
                  v-model="formData.moldOpeningType"
                  api-key="MOLD_OPEN_TYPE"
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  multiple
                  placeholder="请选择模具开模方式"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="模具数量(套)" prop="quantity">
                <ElInputNumber
                  v-model="formData.quantity"
                  :controls="false"
                  :min="1"
                  :step="1"
                  :value-on-clear="null"
                  class="min-w-48 number-input"
                  placeholder="请输入模具数量(套)"
                  step-strictly
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="模具金额(¥)" prop="amount">
                <ElInputNumber
                  v-model="formData.amount"
                  :controls="false"
                  :min="0"
                  :precision="2"
                  :step="1"
                  :value-on-clear="null"
                  class="min-w-48 number-input"
                  placeholder="请输入模具金额(¥)"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="模具图纸文件" prop="moldImgFile">
                <OssUpload
                  v-model="formData.moldImgFile"
                  :limit="20"
                  :size-limit="1024 * 1024 * 100"
                  drag
                  list-type="text"
                  multiple
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="物性要求" prop="physicalRequirements">
                <ElInput
                  v-model="formData.physicalRequirements"
                  :autosize="{ minRows: 4 }"
                  :resize="isView ? 'none' : undefined"
                  class="w-48"
                  maxlength="500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCollapseItem>
        <ElCollapseItem name="3" title="关联的楦型信息">
          <template #title>
            <div class="font-bold text-base">关联的楦型信息</div>
          </template>
          <ElButton
            v-if="!isView && !isMoldBook"
            class="mb-2"
            text
            type="primary"
            @click="handleOpenLastInfoDialog"
          >
            搜索楦型信息
          </ElButton>
          <VxeTable
            ref="lastTableRef"
            :cell-config="{ height: 80 }"
            :data="referencedList"
            :max-height="500"
          >
            <VxeColumn title="序号" type="seq" />
            <VxeColumn field="code" title="楦型编码" />
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="thumbnail"
              title="缩略图"
              width="100"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.commonSeasonMap } }"
              field="developmentSeasonCode"
              title="开发季节"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.brandMap } }"
              field="brand"
              title="品牌"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.lastSexMap } }"
              field="sexCode"
              title="性别"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.lastTypeMap } }"
              field="typeCode"
              title="楦头类别"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.lastMarketMap } }"
              field="marketCode"
              title="楦头市场"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.lastHeightMap } }"
              field="heightCode"
              title="楦头帮高"
            />
            <VxeColumn field="productName" title="关联的产品信息" />
            <VxeColumn v-if="!isView" :show-overflow="false" title="操作">
              <template #default="{ rowIndex }">
                <ElButton text type="primary" @click="handleRemoveLast(rowIndex)"> 删除 </ElButton>
              </template>
            </VxeColumn>
          </VxeTable>
        </ElCollapseItem>
      </ElForm>
      <ElCollapseItem v-if="isView || isMoldBook" name="4" title="管理模具保管书">
        <template #title>
          <div class="font-bold text-base">管理模具保管书</div>
        </template>
        <ElButton
          v-if="!isView"
          :disabled="isView"
          class="mb-2"
          text
          type="primary"
          @click="handleAddMoldCustody"
        >
          添加
        </ElButton>
        <VxeTable
          ref="moldCustodyRef"
          :data="formData.moldCustodyList"
          :max-height="500"
          :show-overflow="false"
        >
          <VxeColumn title="序号" type="seq" />
          <VxeColumn field="factory" title="下单工厂">
            <template #header>
              <span class="text-red-500">* </span>
              <span>下单工厂</span>
            </template>
            <template #default="{ row }: { row: MoldInfoAPI.MoldCustodyResp }">
              <span v-if="isView">{{ row.factory }}</span>
              <ElInput
                v-else
                v-model="row.factory"
                class="w-48"
                clearable
                placeholder="请输入下单工厂"
              />
            </template>
          </VxeColumn>
          <VxeColumn field="time" title="生成日期" width="265">
            <template #header>
              <span class="text-red-500">* </span>
              <span>生成日期</span>
            </template>
            <template #default="{ row }: { row: MoldInfoAPI.MoldCustodyResp }">
              <span v-if="isView">{{ row.time }}</span>
              <ElDatePicker
                v-else
                v-model="row.time"
                class="min-w-48"
                clearable
                format="YYYY-MM-DD"
                placeholder="年/月/日"
                value-format="YYYY-MM-DD"
              />
            </template>
          </VxeColumn>
          <VxeColumn field="address" title="存放地点">
            <template #default="{ row }: { row: MoldInfoAPI.MoldCustodyResp }">
              <span v-if="isView">{{ row.address }}</span>
              <ElInput
                v-else
                v-model="row.address"
                class="w-48"
                clearable
                placeholder="请输入存放地点"
              />
            </template>
          </VxeColumn>
          <VxeColumn field="custodianId" title="保管人">
            <template #header>
              <span class="text-red-500">* </span>
              <span>保管人</span>
            </template>
            <template #default="{ row }: { row: MoldInfoAPI.MoldCustodyResp }">
              <CascadeSelector
                v-model="row.custodianId"
                :disabled="isView"
                :props="{ emitPath: false }"
                :show-all-levels="!isView"
                api-key="allUsers"
                cache
                clearable
                filterable
                placeholder="请选择保管人"
              />
            </template>
          </VxeColumn>
          <VxeColumn field="custodianFile" title="保管书">
            <template #header>
              <span class="text-red-500">* </span>
              <span>保管书</span>
            </template>
            <template #default="{ row }: { row: MoldInfoAPI.MoldCustodyResp }">
              <OssUpload
                v-model="row.custodianFile"
                :disabled="isView"
                :limit="20"
                :size-limit="1024 * 1024 * 100"
                drag
                listType="text"
                multiple
              >
                <template #trigger>
                  <Icon :size="22" color="var(--el-color-primary)" icon="mdi:upload" />
                </template>
              </OssUpload>
            </template>
          </VxeColumn>
          <VxeColumn field="statusCode" title="状态">
            <template #default="{ row }: { row: MoldInfoAPI.MoldCustodyResp }">
              <span v-if="isView">{{ row.status }}</span>
              <ElSelect
                v-else
                v-model="row.statusCode"
                clearable
                filterable
                placeholder="请选择状态"
              >
                <ElOption
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </template>
          </VxeColumn>
          <VxeColumn field="modifyById" title="操作人">
            <template #default="{ row }: { row: MoldInfoAPI.MoldCustodyResp }">
              <CascadeSelector
                v-model="row.modifyById"
                :show-all-levels="false"
                api-key="allUsers"
                cache
                clearable
                disabled
                filterable
              />
            </template>
          </VxeColumn>
          <VxeColumn field="modifyTime" title="操作时间" />
          <VxeColumn v-if="isMoldBook" :show-overflow="false" title="操作">
            <template #default="{ rowIndex }">
              <ElButton text type="primary" @click="handleDelMoldCustody(rowIndex)">
                删除
              </ElButton>
            </template>
          </VxeColumn>
        </VxeTable>
      </ElCollapseItem>
    </ElCollapse>
  </ContentWrap>
  <ContentWrap class="mt-2">
    <div class="text-center">
      <ElButton @click="handleClose">返回</ElButton>
      <ElButton v-if="!isView" :loading="submitLoading" type="primary" @click="handleConfirm">
        确定
      </ElButton>
      <ElButton
        v-if="
          formData.statusCode !== StatusEnum.APPROVING &&
          formData.statusCode !== StatusEnum.BAN &&
          isView
        "
        v-hasPermi="['editMold']"
        type="primary"
        @click="handleEditMold"
      >
        <Icon size="20" icon="ep:edit" />
        修改模具
      </ElButton>
      <ElButton
        v-hasPermi="['mold:manageBook']"
        v-if="formData.status !== StatusEnum.APPROVING"
        type="primary"
        @click="handleMoldBook"
      >
        <Icon icon="carbon:book" />
        管理保管书
      </ElButton>
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
.info-wrapper {
  max-height: calc(100vh - 250px);
  overflow: auto;
}

.number-input {
  :deep(.el-input__wrapper) {
    padding-left: 12px;
  }

  :deep(.el-input__inner) {
    text-align: left;
  }
}

:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}
</style>
