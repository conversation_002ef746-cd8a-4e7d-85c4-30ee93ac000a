<script setup lang="ts">
import {
  getBoxGaugeByProductId,
  GetBoxGaugeByProductIdAPI,
  updateBoxGauge
} from '../api/boxGaugeDialog'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'BoxGaugeDialog'
})

const props = defineProps<{
  modelValue: boolean
  currentRow?: GetBoxGaugeByProductIdAPI.Row
  isView?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const queryLoading = ref(false)
const tableData = ref<GetBoxGaugeByProductIdAPI.List>([])
const handleClose = () => {
  visible.value = false
  tableData.value = []
}

const getBoxGaugeList = async () => {
  if (!props.currentRow?.id) return
  queryLoading.value = true
  const [error, result] = await getBoxGaugeByProductId(props.currentRow.id)
  queryLoading.value = false
  if (!error && result?.datas) {
    tableData.value = result.datas || []
  }
}

watch(
  () => visible.value,
  (val) => {
    if (val) {
      getBoxGaugeList()
    }
  }
)

const submitLoading = ref(false)
const handleSubmit = async () => {
  if (!props.currentRow?.id) return
  const hasQuantity = tableData.value.some((e) => !e.cartonQuantity)
  if (hasQuantity) {
    ElMessage.error('请填写箱规')
    return
  }
  submitLoading.value = true
  const [error, result] = await updateBoxGauge({
    list: tableData.value
  })
  submitLoading.value = false
  if (!error && result) {
    ElMessage.success(result.msg || '操作成功')
    handleClose()
  }
}

const sync = () => {
  const firstRow = tableData.value[0]
  if (!firstRow?.cartonQuantity) {
    ElMessage.error('请填写第一行数据的箱规')
    return
  }
  tableData.value = tableData.value.map((e) => {
    return {
      ...e,
      cartonQuantity: firstRow.cartonQuantity
    }
  })
}
</script>

<template>
  <Dialog v-model="visible" append-to-body title="箱规信息" :before-close="handleClose">
    <VxeTable
      ref="table"
      :loading="queryLoading"
      :data="tableData"
      :max-height="500"
      :show-header-overflow="false"
    >
      <VxeColumn title="序号" type="seq" width="60" />
      <VxeColumn field="productNumber" title="产品编码" />
      <VxeColumn field="brandItemName" title="品牌" />
      <VxeColumn field="skuCode" title="SKU编码" />
      <VxeColumn field="colorName" title="产品配色" />
      <VxeColumn field="sizeValue" title="产品尺码" />
      <VxeColumn :show-header-overflow="false">
        <template #header>
          <span class="text-red-500">*</span>
          <span>装箱数(箱规)</span>
          <span v-if="!isView" class="font-normal cursor-pointer text-blue-500" @click="sync">
            一键赋值
          </span>
        </template>
        <template #default="{ row }: { row: GetBoxGaugeByProductIdAPI.Row }">
          <SelectPlus
            v-model="row.cartonQuantity"
            cache
            :disabled="isView"
            api-key="PRODUCT_BOX_NUMBER"
            size="small"
            filterable
            clearable
          />
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElButton :loading="queryLoading || submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="queryLoading || submitLoading" type="primary" @click="handleSubmit">
        确定
      </ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>
