<script lang="ts" setup>
import { BaseClamp } from '@/components/BaseClamp'
import type { Measurable } from 'element-plus'
import { ElTooltip } from 'element-plus'
import type { ProductListPageAPI } from '../api/product-list'
import { ProductDataStatusEnum } from '../../const'

defineOptions({
  name: 'CardView'
})

defineProps<{
  tableData: ProductListPageAPI.List
  loading: boolean
  maxHeight?: number
}>()

const emit = defineEmits<{
  (e: 'change-status', val: ProductListPageAPI.Row): void
  (e: 'edit-product', val: ProductListPageAPI.Row): void
  (e: 'copy-product', val: ProductListPageAPI.Row): void
  (e: 'view-product', val: ProductListPageAPI.Row): void
  (e: 'checkbox-change'): void
}>()

const getColorList = (row: ProductListPageAPI.Row) => {
  return row.colorUrlList?.map((e) => e?.signatureUrl || '').filter((e) => e) || []
}

const contentRef = ref<HTMLElement | null>(null)
const content = ref<string>('')
const tooltipRef = ref<InstanceType<typeof ElTooltip>>()

const handleMouseover = (e: { clamped: boolean; text: string; currentTarget: HTMLElement }) => {
  if (!e.clamped) {
    return
  }
  contentRef.value = e.currentTarget
  content.value = e.text
}

const checkedList = ref<ProductListPageAPI.List>([])
const toggleCheck = (item: ProductListPageAPI.Row) => {
  const index = checkedList.value.findIndex((e) => e === item)
  if (index !== -1) {
    checkedList.value = checkedList.value.splice(index, 1)
  } else {
    checkedList.value.push(item)
  }
  emit('checkbox-change')
}

const getCheckboxRecords = () => {
  return checkedList.value
}

defineExpose({
  getCheckboxRecords
})
</script>

<template>
  <ElScrollbar>
    <div
      v-loading="loading"
      :style="{ maxHeight: maxHeight + 'px' }"
      class="grid justify-center min-h-[140px] grid-gap-[10px] grid-cols-[repeat(auto-fit,minmax(300px,1fr))]"
    >
      <div v-for="item in tableData" :key="item.id!" class="p-2">
        <ElCard
          :body-style="{ padding: '0px', width: '100%', height: '100%' }"
          class="card-container"
          shadow="always"
          @click="emit('view-product', item)"
        >
          <div class="card-body">
            <div class="card-content">
              <div class="card-img-container">
                <ElImage
                  :src="item.thumbnail?.signatureUrl || ''"
                  class="card-img"
                  fit="cover"
                  hide-on-click-modal
                  loading="lazy"
                />
                <ElTag class="card-status" effect="dark">
                  {{ item.developStageItemName }}
                </ElTag>
                <ElCheckbox class="!absolute !h-6 right-2 top-0" @click.stop="toggleCheck(item)" />
              </div>
              <div class="card-button-container">
                <ElButton
                  v-hasPermi="['product:editProduct']"
                  class="card-button"
                  size="small"
                  text
                  type="primary"
                  @click.stop="emit('edit-product', item)"
                >
                  <span class="card-button-content">修改</span>
                  <span class="card-button-content">产品</span>
                </ElButton>
                <ElButton
                  class="card-button"
                  size="small"
                  text
                  type="primary"
                  @click.stop="emit('copy-product', item)"
                >
                  <span class="card-button-content">VOC</span>
                  <span class="card-button-content">改善</span>
                </ElButton>
                <ElButton
                  class="card-button"
                  size="small"
                  text
                  type="primary"
                  @click.stop="emit('change-status', item)"
                >
                  <span class="card-button-content">版本</span>
                  <span class="card-button-content">记录</span>
                </ElButton>
                <ElSwitch
                  v-model="item.dataStatus"
                  v-hasPermi="['product:changeStatus']"
                  :active-value="ProductDataStatusEnum.EFFECT"
                  :inactive-value="ProductDataStatusEnum.INVALID"
                  size="small"
                  @click.stop="emit('change-status', item)"
                />
              </div>
            </div>
            <div class="card-content">
              <div class="card-info-container">
                <div class="flex flex-nowrap items-center w-full h-full">
                  <ElScrollbar>
                    <ElImage
                      v-for="img in getColorList(item)"
                      :key="img"
                      :preview-src-list="getColorList(item)"
                      :src="img"
                      hide-on-click-modal
                      fit="contain"
                      loading="lazy"
                      style="width: 30px; height: 30px"
                    />
                  </ElScrollbar>
                </div>
              </div>
              <div class="card-info-container">
                <BaseClamp
                  :max-lines="1"
                  :text-content="item.productNumber"
                  autoresize
                  @mouseover="handleMouseover"
                >
                  {{ item.productNumber }}
                </BaseClamp>
              </div>
              <div class="card-info-container !flex-row flex-wrap">
                <div class="w-1/2">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.launchSeasonItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.launchSeasonItemName }}
                  </BaseClamp>
                </div>
                <div class="w-1/2">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.developmentTypeItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.developmentTypeItemName }}
                  </BaseClamp>
                </div>
                <div class="w-full">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.designerIdItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.designerIdItemName }}
                  </BaseClamp>
                </div>
              </div>
              <div class="card-info-container !border-bottom-0 !flex-row">
                <div class="w-full">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.brandItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.brandItemName }}
                  </BaseClamp>
                </div>
              </div>
              <div
                class="card-info-container !border-bottom-0 border-top-1 border-left-1 border-solid mt-[-2px] ml-[-1px]"
              >
                <div class="flex items-center h-1/2 w-full">
                  <div class="w-1/2">
                    <BaseClamp
                      :max-lines="1"
                      :text-content="item.lastStandardItemName"
                      autoresize
                      @mouseover="handleMouseover"
                    >
                      {{ item.lastStandardItemName }}
                    </BaseClamp>
                  </div>
                  <div class="w-1/2">
                    <BaseClamp
                      :max-lines="1"
                      :text-content="item.toeStandardItemName"
                      autoresize
                      @mouseover="handleMouseover"
                    >
                      {{ item.toeStandardItemName }}
                    </BaseClamp>
                  </div>
                </div>
                <div class="flex text-left h-1/2 w-full">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="`状态: ${item.dataStatusItemName}`"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    状态: {{ item.dataStatusItemName }}
                  </BaseClamp>
                </div>
              </div>
            </div>
          </div>
        </ElCard>
      </div>
      <ElTooltip
        ref="tooltipRef"
        :content="content"
        :popper-options="{
          modifiers: [
            {
              name: 'computeStyles',
              options: {
                adaptive: false,
                enabled: false
              }
            }
          ]
        }"
        :virtual-ref="contentRef as Measurable"
        popper-class="singleton-tooltip"
        virtual-triggering
      />
    </div>
  </ElScrollbar>
</template>

<style lang="less" scoped>
.card-container {
  height: 200px;
  max-width: 330px;

  &:hover {
    cursor: pointer;
  }

  .card-body {
    display: flex;
    height: 100%;
    justify-content: space-between;

    > .card-content {
      width: 50%;
      font-size: 12px;

      > .card-img-container {
        position: relative;
        width: 100%;
        height: 80%;
        overflow: hidden;

        > .card-img {
          width: 100%;
          height: 100%;
        }

        > .card-status {
          position: absolute;
          top: 0;
          left: 0;
          border-radius: 0 var(--el-card-border-radius) var(--el-card-border-radius)
            var(--el-card-border-radius);
        }
      }

      > .card-button-container {
        height: 20%;

        > .card-button {
          width: 25%;
          height: 100%;

          > :deep(span) {
            flex-direction: column;
          }

          > .card-button-content {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
        }

        > .card-button + .card-button {
          margin-left: 0;
        }
      }

      > .card-info-container {
        display: flex;
        width: 100%;
        height: 20%;
        padding: 0 8px;
        text-align: center;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        :deep(.el-image + .el-image) {
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
