<script lang="ts" setup>
import { ElCascader, ElMessage, FormRules } from 'element-plus'
import { useBasicLibraryDictStore } from '@/views/basic-library-manage/store/dict'
import { storeToRefs } from 'pinia'
import {
  DictValueAPI,
  getModelInfoByProductNumber,
  getProductCategoryList,
  ProductCategoryListAPI
} from '@/views/basic-library-manage/api/common'
import { SizeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import {
  BaseUnitEnum,
  DevelopModeEnum,
  PFLevelEnum,
  ProductSelectResultEnum,
  ProductStageEnum,
  ProductTaskNodeEnum
} from '@/views/basic-library-manage/product-library/const'
import {
  createProductInfo,
  Emit,
  FormModel,
  getStyleImage,
  Props,
  SaveTypeEnum
} from '@/views/basic-library-manage/product-library/api/productInfo'
import {
  BrandEnum,
  HeadCategoryEnum,
  HeadStandardEnum,
  ProductCategoryEnum,
  ProductDataStatusEnum,
  shoesLastStandardList,
  shoesToeCategoryList,
  StatusEnum,
  YesNoEnum
} from '@/views/basic-library-manage/const'
import { useUserInfoStore } from '@/store/modules/userInfo'
import { Icon } from '@/components/Icon'
import ModelInfoDialog from '@/views/basic-library-manage/components/ModelInfoDialog.vue'
import { ModelListPageAPI } from '@/views/basic-library-manage/model-library/api/model-list'
import { sizeList as getSizeList } from '@/components/Business/SelectPlus/src/api'
import { useProductInfo } from '@/views/basic-library-manage/product-library/components/helper'
import { groupBy } from 'lodash-es'
import { useSizeOptions } from '@/utils/useSizeOptions'
import { SizeCodeTypeEnums } from '@/enums'
import { getDictLeafOpen } from '@/api/common'

defineOptions({
  name: 'BillingInfo'
})

const router = useRouter()

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const { formData, formRef, getCommonParams } = useProductInfo(props, emit)

const route = useRoute()
const isCopy = computed(() => {
  return route.query.type === 'voc'
})

const store = useBasicLibraryDictStore()
const useConst = () => {
  const styleStructureList = computed(() => store.commonStyleStructureList)
  const toeStandardList = computed(() => store.lastTypeList)
  const productLastStandardList = computed(() => store.productLastStandardList)

  const filteredToeStandardList = ref<DictValueAPI.Data[]>(toeStandardList.value)
  const filteredLastStandardList = ref<DictValueAPI.Data[]>(productLastStandardList.value)

  const { userInfo } = storeToRefs(useUserInfoStore())
  const productCategoryList = ref<ProductCategoryListAPI.Data[]>([])
  const fetchProductCategoryList = async () => {
    const [error, result] = await getProductCategoryList()
    if (error === null && result?.datas) {
      productCategoryList.value = result?.datas
    }
  }
  fetchProductCategoryList()

  return {
    userInfo,
    styleStructureList,
    productCategoryList,
    toeStandardList,
    productLastStandardList,
    filteredToeStandardList,
    filteredLastStandardList
  }
}

const {
  userInfo,
  productCategoryList,
  styleStructureList,
  toeStandardList,
  productLastStandardList,
  filteredToeStandardList,
  filteredLastStandardList
} = useConst()

const activeNames = ref(['1', '2', '3'])

const formRules = computed<FormRules<Omit<FormModel, 'skcInfoDetailResp'>>>(() => {
  const developModeList: string[] = [
    DevelopModeEnum.FULL_SEASON,
    DevelopModeEnum.QUICK_REACTION,
    DevelopModeEnum.SCROLL
  ]

  return {
    brand: [{ required: true, message: '请选择品牌', trigger: 'change' }],
    productCategory: [{ required: true, message: '请选择产品类目', trigger: 'change' }],
    launchSeason: [{ required: true, message: '请选择开发季节', trigger: 'change' }],
    productStyle: [{ required: true, message: '请选择产品风格', trigger: 'change' }],
    productStyleSubCode: [{ required: true, message: '请选择产品子风格', trigger: 'change' }],
    targetAudience: [{ required: true, message: '请选择适用人群', trigger: 'change' }],
    styleStructure: [{ required: true, message: '请选择款式结构', trigger: 'change' }],
    applicableSeason: [{ required: true, message: '请选择适用季节', trigger: 'change' }],
    expectedLaunchDate: [{ required: true, message: '请选择期望上架日期', trigger: 'change' }],
    lastsStandard: [{ required: true, message: '请选择楦型标准', trigger: 'change' }],
    dtc: [{ required: true, message: '请选择是否S2C', trigger: 'change' }],
    toeStandard: [{ required: true, message: '请选择楦头类别', trigger: 'change' }],
    designPersonId: [{ required: true, message: '请选择产品企划', trigger: 'change' }],
    designerId: [{ required: true, message: '请选择产品设计师', trigger: 'change' }],
    sizeRangeId: [{ required: true, message: '请选择尺码段', trigger: 'change' }],
    designUrl: [{ required: true, message: '请上传设计图', trigger: 'blur' }],
    closureMethod: [{ required: true, message: '请选择穿脱方式', trigger: 'change' }],
    safetyFeatures: [{ required: true, message: '请选择是否含金属鞋头', trigger: 'change' }],
    ankleCoverage: [{ required: true, message: '请选择是否过踝/过膝', trigger: 'change' }],
    retailPrice: [{ required: true, message: '请输入吊牌价', trigger: 'blur' }],
    productPositioning: [{ required: true, message: '请输入产品目标定级', trigger: 'blur' }],
    developmentDirection: [{ required: true, message: '请选择产品系列', trigger: 'change' }],
    developmentDirectionSubCode: [
      { required: true, message: '请选择产品子系列', trigger: 'change' }
    ],
    developmentChannel: [{ required: true, message: '请选择开发渠道', trigger: 'change' }],
    developmentModel: [{ required: true, message: '请选择开发模式', trigger: 'change' }],
    costRangeMin: [{ required: true, message: '请输入成本区间', trigger: 'change' }],
    costRangeMax: [{ required: true, message: '请输入成本区间', trigger: 'change' }],
    // estimatedPrice: [{ required: true, message: '请输入预估售价', trigger: 'change' }],
    modelId: [
      {
        required: developModeList.includes(formData.value.developmentModel!),
        message: '请选择关联型体库',
        trigger: 'change'
      }
    ],
    stylePartnerCode: [
      {
        required: [
          BrandEnum.FABKIDS,
          BrandEnum.JUSTFAB,
          BrandEnum.SHOEDAZZLE,
          BrandEnum.JUSTFAB_3RD_PARTY
        ].includes(formData.value.brand!),
        message: '请输入Style Partner Code',
        trigger: 'change'
      }
    ]
  }
})

const disabled = computed(() => {
  return (
    props.isView ||
    props.initPane !== props.currentPane ||
    formData.value.developStage === ProductStageEnum.PRODUCTION ||
    props.selectionDisabled ||
    props.confirmDisabled ||
    props.isOldData
  )
})
const sizeList = ref<(SizeListAPI.Data & { disabled?: boolean })[]>([])

const filteredSizeList = computed(() => {
  const { lastsStandard, toeStandard } = unref(formData)

  return sizeList.value
    .map((e) => ({
      ...e,
      disabled:
        e.disabled ||
        (lastsStandard === HeadStandardEnum.U && e.lastsStandard !== HeadStandardEnum.U) ||
        (lastsStandard === HeadStandardEnum.E && e.lastsStandard !== HeadStandardEnum.E) ||
        (toeStandard === HeadCategoryEnum.W && e.toeStandard !== HeadCategoryEnum.W)
    }))
    .sort((a, b) => +a.disabled! - +b.disabled!)
})

const getAllSizeList = async () => {
  const { datas } = await getSizeList()
  if (datas) {
    sizeList.value = datas
      .filter((e) => e.sizeType === SizeCodeTypeEnums.PRODUCT)
      .map((e) => ({
        ...e,
        disabled: e.status !== StatusEnum.START
      }))
  }
}

watchEffect(async () => {
  if (!userInfo.value) return
  if (disabled.value) {
    return
  }
  await nextTick()
  if (!formData.value.designerId) {
    formData.value.designerId = userInfo.value.id
  }
})

const styleStructureOptions = ref<DictValueAPI.Data[] | null>(null)
const productCategoryRef = ref<InstanceType<typeof ElCascader>>()
const getFilterOptionsByCategory = () => {
  if (!productCategoryRef.value) return
  const selected = productCategoryRef.value.getCheckedNodes(true)
  if (selected?.length) {
    const firstLevel = selected[0].pathValues[0] as number
    if (firstLevel === ProductCategoryEnum.SHOES) {
      filteredToeStandardList.value = toeStandardList.value.filter((e) =>
        shoesToeCategoryList.includes(e.dictValue as string)
      )
      filteredLastStandardList.value = productLastStandardList.value.filter((e) =>
        shoesLastStandardList.includes(e.dictValue as string)
      )
    } else {
      filteredToeStandardList.value = toeStandardList.value.filter(
        (e) => e.dictValue === HeadCategoryEnum.A
      )
      filteredLastStandardList.value = productLastStandardList.value.filter(
        (e) => e.dictValue === HeadStandardEnum.A
      )
    }
  } else {
    filteredToeStandardList.value = toeStandardList.value
    filteredLastStandardList.value = productLastStandardList.value
  }
}
const handleProductCategoryChange = () => {
  formData.value.lastsStandard = ''
  formData.value.toeStandard = ''
  formData.value.styleStructure = ''
  getFilterOptionsByCategory()
  const selected = productCategoryRef.value?.getCheckedNodes(true)
  if (selected?.length) {
    const selectorKeyList = (selected[0].data?.selectorAdditionList as string[]) || []
    styleStructureOptions.value = (styleStructureList.value || [])?.filter((e) =>
      selectorKeyList?.includes(e.dictValue!.toString())
    )
    const list: number[] = [ProductCategoryEnum.SPORTS, ProductCategoryEnum.NON_SPORTS]
    const label = selected[0].pathValues[1] as number
    if (list.includes(label)) {
      formData.value.waterproofLevel = PFLevelEnum.NONE
    }
    if (label !== ProductCategoryEnum.SPORTS) {
      formData.value.specialTestingRequirements = '无'
    }
  } else {
    styleStructureOptions.value = null
  }
}

const handleEditProduct = () => {
  if (props.selectionDisabled || props.confirmDisabled) {
    ElMessage.warning('该操作已被锁定')
    return
  }
  router.push({
    name: 'EditProduct',
    query: {
      id: formData.value.id,
      pane: props.currentPane
    }
  })
  // location.href = router.resolve({
  //   name: 'EditProduct',
  //   query: {
  //     id: formData.value.id,
  //     pane: props.currentPane
  //   }
  // }).href
}

const selectedColor = computed(() => {
  return (
    formData.value.skcInfoDetailResp?.filter(
      (e) => e.selectable === ProductSelectResultEnum.SELECTED
    ) || []
  )
})

const stylePartnerCodeDisabled = ref(false)

const onMountedFn = async () => {
  stylePartnerCodeDisabled.value = !!(
    formData.value.sendWms === (YesNoEnum.Y as string) &&
    formData.value.stylePartnerCode &&
    !isCopy.value
  )
  formData.value.basicUnitOfMeasure = formData.value.basicUnitOfMeasure || BaseUnitEnum.PAIR
  const selectedSize = formData.value.selectedSize
  if (!sizeList.value.length) {
    await getAllSizeList()
    // await handleSizeRangeIdChange()
    formData.value.selectedSize = selectedSize
  } else {
    // await handleSizeRangeIdChange()
    formData.value.selectedSize = selectedSize
  }
  getFilterOptionsByCategory()
}

// 在【大货生产阶段】 产品企划修改完成数据后，下发WMS对接成功后，锁定信息，不允许二次修改
watchOnce(() => formData.value, onMountedFn)

onMounted(onMountedFn)

watchEffect(() => {
  if (props.isCreate) {
    if (!formData.value.designPersonId) {
      formData.value.designPersonId = userInfo.value.id
    }
  }
})

// 产品编码的生成需要加一个逻辑判断， 开款阶段已经完成后， 涉及到编码生成的字段，不能让用户修改
const codeDisabled = computed(() => {
  return !!formData.value.taskTimeRecord?.[ProductTaskNodeEnum.OPENING_STAGE]
})

const taskNode = computed(() => {
  return formData.value.developStage === ProductStageEnum.PRODUCTION
    ? ProductTaskNodeEnum.PRODUCT_OPERATION_IMPROVEMENT
    : ProductTaskNodeEnum.OPENING_STAGE
})
const submit = async (type: SaveTypeEnum) => {
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) {
    return false
  }
  if (formData.value.developStage === ProductStageEnum.PRODUCTION) {
    const validVelvet =
      selectedColor.value.length && selectedColor.value.every((e) => e.velvetApplied)
    if (!validVelvet) {
      ElMessage.warning('请填写植绒信息')
      return false
    }
  }
  if (formData.value.quicklyAble === 1) {
    const developModeList: string[] = [
      DevelopModeEnum.FULL_SEASON,
      DevelopModeEnum.QUICK_REACTION,
      DevelopModeEnum.SCROLL
    ]
    const validQuickly = developModeList.includes(formData.value.developmentModel!)
    if (validQuickly) {
      ElMessage.warning('不能修改开发模式为：正季、快反、滚动')
      return false
    }
  }
  const [error, result] = await createProductInfo({
    ...getCommonParams(),
    operateBottom: type,
    taskNode: taskNode.value,
    autoSendWms: true
  })
  if (error === null && result) {
    ElMessage.success(result.msg)
    useClosePage('ProductLibrary', {
      state: {
        productNumber: formData.value.productNumber
      }
    })
    return true
  }
  return false
}

const { sizeOptions, fetchSizeList } = useSizeOptions(SizeCodeTypeEnums.PRODUCT)
fetchSizeList()
const sizeValueList = computed(() => {
  if (!formData.value.sizeRangeId?.length) {
    return []
  }
  const sizeObj = sizeOptions.value.filter((e) => formData.value.sizeRangeId?.includes(e.id!))

  return sizeObj
    .filter((e) => e.children?.length)
    .map((e) =>
      e.children!.map((size) => ({
        label: `${size.label}【${e.label}】`,
        value: size.id,
        disabled: size.disabled
      }))
    )
    .flat()
})

const sizeLabel = computed(() => {
  const sizeRangeId: number[] | undefined = formData.value.sizeRangeId
  const sizeGroup = groupBy(
    filteredSizeList.value
      .filter((e) => sizeRangeId?.includes(e.id!))
      .map((e) => ({
        name: e.name,
        sizeValue: e.sizeValueList?.map((size) => size.sizeValue).join(', ')
      })),
    'name'
  )
  return Object.keys(sizeGroup)
    .map((key) => {
      const sizeValueList = sizeGroup[key].map((item) => item.sizeValue).join(', ')
      return `${sizeValueList}【${key}】`
    })
    .join('、')
})

const handleSizeRangeIdChange = async () => {
  const sizeRangeId: number[] | undefined = formData.value.sizeRangeId
  if (!Array.isArray(sizeRangeId) || sizeRangeId.length < 1) {
    formData.value.standardSizeId = []
    formData.value.selectedSize = []
    return
  }
  // 根据rangeId.standardValue对应的sizeId
  formData.value.standardSizeId = sizeRangeId
    .map((rangeId) => {
      const findRange = sizeList.value.find((item) => item.id === rangeId)
      if (!findRange) {
        return
      }
      const fineSize = findRange.sizeValueList?.find(
        (item) => item.sizeValue === findRange.standardValue
      )
      if (!fineSize) {
        return
      }
      return fineSize.id
    })
    .filter(Boolean) as number[]
}

getAllSizeList()

const modelInfoDialogVisible = ref(false)
const handleOpenModelInfoDialog = () => {
  if (disabled.value) return
  modelInfoDialogVisible.value = true
}
const handlePickModel = (val: ModelListPageAPI.Row) => {
  formData.value.modelId = val.id
  formData.value.modelIdItemName = val.code
  formData.value.toeShape = val.head
  formData.value.heelType = val.pattern
  formData.value.heelHeight = val.high
  formData.value.heelHeightMm = val.heelHigh
}

const vocStyleThumbnail = ref<BaseFileDTO[]>([])
const handleVocOriginalStyleNumberChange = async () => {
  const productNumber = formData.value.vocOriginalStyleNumber
  if (productNumber) {
    const [error, result] = await getStyleImage(productNumber)
    if (error === null && result?.datas) {
      vocStyleThumbnail.value = [result.datas]
    }
  }
  if (disabled.value) {
    return
  }
  if (productNumber) {
    const [error, result] = await getModelInfoByProductNumber(productNumber)
    if (error === null && result?.datas) {
      formData.value.modelId = result.datas.id
      formData.value.modelIdItemName = result.datas.code
      formData.value.toeShape = result.datas.head
      formData.value.heelType = result.datas.pattern
      formData.value.heelHeight = result.datas.high
      formData.value.heelHeightMm = result.datas.heelHigh
    }
  }
}

watch(() => formData.value.vocOriginalStyleNumber, handleVocOriginalStyleNumberChange)

const clearSizeRange = (name: string) => {
  const sizeRangeName: number[] | undefined = formData.value?.[name]
  if (!Array.isArray(sizeRangeName) || sizeRangeName.length < 1) {
    formData.value.sizeRangeId = []
    formData.value.standardSizeId = []
    formData.value.selectedSize = []
  }
}
const handleRenderOption = (options) => {
  if (options && options.length == 1) formData.value.productStyleSubCode = options[0].value
}
defineExpose({
  submit
})
</script>

<template>
  <ModelInfoDialog v-model="modelInfoDialogVisible" @submit="handlePickModel" />
  <ElCollapse v-model="activeNames">
    <ElForm
      ref="formRef"
      :disabled="disabled"
      :model="formData"
      :rules="formRules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      label-width="140px"
      scroll-to-error
    >
      <ElCollapseItem disabled name="1">
        <template #title>
          <div class="font-bold text-base text-[var(--el-collapse-header-text-color)]">
            企划信息
          </div>
        </template>

        <div class="grid grid-cols-2 items-start gap-x-4">
          <ElFormItem class="col-span-2" label="设计图" prop="designUrl">
            <OssUpload
              v-model="formData.designUrl"
              :limit="20"
              :size-limit="100 * 1024 * 1024"
              accept="image/*"
              drag
              multiple
            />
          </ElFormItem>
          <ElFormItem label="Style（WMS）" prop="styleWms">
            <ElInput v-model="formData.styleWms" class="w-48" clearable disabled />
          </ElFormItem>
          <ElFormItem label="品牌" prop="brand">
            <SelectPlus
              v-model="formData.brand"
              :disabled="codeDisabled"
              api-key="baseBrand"
              cache
              clearable
              filterable
              placeholder="请选择品牌"
            />
          </ElFormItem>
          <ElFormItem label="产品类目" prop="productCategory">
            <ElCascader
              ref="productCategoryRef"
              v-model="formData.productCategory"
              :disabled="codeDisabled"
              :options="productCategoryList"
              :props="{
                emitPath: false,
                value: 'selectorKey',
                label: 'selectorEnValue',
                children: 'childList'
              }"
              clearable
              filterable
              placeholder="请选择"
              @change="handleProductCategoryChange"
            />
          </ElFormItem>
          <ElFormItem label="开发季节" prop="launchSeason">
            <SelectPlus
              v-model="formData.launchSeason"
              :disabled="codeDisabled"
              api-key="COMMON_MARKET_SEASON"
              cache
              clearable
              filterable
              placeholder="请选择开发季节"
            />
          </ElFormItem>
          <ElFormItem label="产品风格" prop="productStyle">
            <SelectPlus
              v-model="formData.productStyle"
              api-key="PRODUCT_STYLE"
              cache
              clearable
              filterable
              placeholder="请选择产品风格"
              @change="
                () => {
                  formData.productStyleSubCode = ''
                }
              "
            />
          </ElFormItem>
          <ElFormItem label="适用人群" prop="targetAudience">
            <SelectPlus
              v-model="formData.targetAudience"
              :disabled="codeDisabled"
              api-key="PRODUCT_PEOPLE"
              cache
              clearable
              filterable
              placeholder="请选择适用人群"
            />
          </ElFormItem>
          <ElFormItem label="产品子风格" prop="productStyleSubCode">
            <ApiSelect
              v-model="formData.productStyleSubCode"
              :childComponent="ElSelect"
              :api-config="{
                api: getDictLeafOpen,
                config: {
                  label: 'nameCn',
                  value: 'leafCode'
                }
              }"
              :key="formData.productStyle"
              :params="{ typeCode: 'PRODUCT_STYLE', itemValue: formData.productStyle }"
              cache
              clearable
              filterable
              placeholder="请选择产品子风格"
              @render-option="handleRenderOption"
            />
          </ElFormItem>
          <ElFormItem label="期望上架日期" prop="expectedLaunchDate">
            <ElDatePicker
              v-model="formData.expectedLaunchDate"
              class="min-w-48"
              clearable
              placeholder="请选择期望上架日期"
              value-format="YYYY-MM-DD"
            />
          </ElFormItem>
          <ElFormItem label="款式结构" prop="styleStructure">
            <ElSelect
              v-model="formData.styleStructure"
              clearable
              filterable
              placeholder="请选择款式结构"
            >
              <ElOption
                v-for="item in styleStructureOptions || styleStructureList"
                :key="item.dictValue"
                :label="item.dictCnName"
                :value="item.dictValue!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="适用季节" prop="applicableSeason">
            <SelectPlus
              v-model="formData.applicableSeason"
              api-key="PRODUCT_SUITABLE_SEASON"
              cache
              clearable
              filterable
              placeholder="请选择适用季节"
            />
          </ElFormItem>
          <ElFormItem label="楦型标准" prop="lastsStandard">
            <ElSelect
              v-model="formData.lastsStandard"
              :disabled="codeDisabled"
              clearable
              filterable
              placeholder="请选择楦型标准"
              @change="clearSizeRange('lastsStandard')"
            >
              <ElOption
                v-for="item in filteredLastStandardList"
                :key="item.dictValue"
                :label="item.dictCnName"
                :value="item.dictValue!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="是否S2C" prop="dtc">
            <SelectPlus v-model="formData.dtc" api-key="COMMON_YES_NO" cache radio />
          </ElFormItem>
          <ElFormItem label="楦头类别" prop="toeStandard">
            <ElSelect
              v-model="formData.toeStandard"
              :disabled="codeDisabled"
              clearable
              filterable
              placeholder="请选择楦头类别"
              @change="clearSizeRange('toeStandard')"
            >
              <ElOption
                v-for="item in filteredToeStandardList"
                :key="item.dictValue"
                :label="item.dictCnName"
                :value="item.dictValue!"
              />
            </ElSelect>
          </ElFormItem>
          <div class="!flex flex-nowrap">
            <ElFormItem label="成本区间($)" prop="costRangeMin">
              <ElInputNumber
                v-model="formData.costRangeMin"
                :controls="false"
                :max="formData.costRangeMax || Infinity"
                :min="0"
                :precision="2"
                :value-on-clear="null"
              />
            </ElFormItem>
            <ElFormItem
              class="const-range-max-form-item"
              label="~"
              label-width="32"
              prop="costRangeMax"
            >
              <ElInputNumber
                v-model="formData.costRangeMax"
                :controls="false"
                :min="formData.costRangeMin || 0"
                :precision="2"
                :value-on-clear="null"
              />
            </ElFormItem>
          </div>
          <ElFormItem label="产品企划" prop="designPersonId">
            <CascadeSelector
              v-model="formData.designPersonId"
              :props="{ emitPath: false }"
              :show-all-levels="!disabled"
              api-key="allUsers"
              cache
              placeholder="请选择产品企划"
            />
          </ElFormItem>
          <ElFormItem label="预估售价($)" prop="estimatedPrice">
            <ElInputNumber
              v-model="formData.estimatedPrice"
              :controls="false"
              :min="0"
              :precision="2"
              :value-on-clear="null"
              class="min-w-48"
            />
          </ElFormItem>
          <ElFormItem label="产品设计师" prop="designerId">
            <CascadeSelector
              v-model="formData.designerId"
              :props="{ emitPath: false }"
              :show-all-levels="!disabled"
              api-key="allUsers"
              cache
              placeholder="请选择产品设计师"
            />
          </ElFormItem>
          <ElFormItem v-if="isView" label="母Style">
            <RouterLink
              v-if="formData.vocOriginalStyleNumber"
              :to="{
                name: 'ViewProduct',
                query: {
                  id: formData.vocStyleNumberId
                }
              }"
              class="text-blue-500"
            >
              {{ formData.vocOriginalStyleNumber }}
            </RouterLink>
          </ElFormItem>
          <ElFormItem label="款式定位" prop="stylePositioning">
            <SelectPlus
              v-model="formData.stylePositioning"
              api-key="PRODUCT_STYLE_POSITIONING"
              cache
              clearable
              filterable
              placeholder="请选择款式定位"
            />
          </ElFormItem>
          <ElFormItem v-if="isView" label="母Style设计图" prop="vocOriginalStyleNumber">
            <OssUpload v-model="vocStyleThumbnail" disabled list-type="picture-card" />
          </ElFormItem>
          <ElFormItem label="儿童人群" prop="childrenCrowd">
            <SelectPlus
              v-model="formData.childrenCrowd"
              api-key="PRODUCT_CHILDREN"
              cache
              clearable
              filterable
              placeholder="请选择儿童人群"
            />
          </ElFormItem>
          <ElFormItem label="儿童年龄段" prop="childrenAvgGroup">
            <SelectPlus
              v-model="formData.childrenAvgGroup"
              api-key="PRODUCT_CHILDREN_AGE"
              cache
              clearable
              filterable
              multiple
              placeholder="请选择儿童年龄段"
            />
          </ElFormItem>
          <ElFormItem label="开发类型" prop="developmentType">
            <SelectPlus
              v-model="formData.developmentType"
              api-key="PRODUCT_DEV_TYPE"
              clearable
              filterable
              placeholder="请选择开发类型"
            />
          </ElFormItem>
          <ElFormItem label="开发渠道" prop="developmentChannel">
            <SelectPlus
              v-model="formData.developmentChannel"
              api-key="PRODUCT_DEV_CHANNELS"
              clearable
              filterable
              placeholder="请选择开发渠道"
            />
          </ElFormItem>
          <ElFormItem label="开发模式" prop="developmentModel">
            <SelectPlus
              v-model="formData.developmentModel"
              api-key="PRODUCT_DEV_MODEL"
              clearable
              filterable
              placeholder="请选择开发模式"
            />
          </ElFormItem>
          <ElFormItem label="关联型体" prop="modelId">
            <ElInput v-model="formData.modelIdItemName" class="w-48" placeholder="请选择" readonly>
              <template #prefix>
                <Icon
                  :size="26"
                  class="cursor-pointer"
                  color="#409EFF"
                  icon="mdi:search"
                  @click="handleOpenModelInfoDialog"
                />
              </template>
            </ElInput>
          </ElFormItem>
          <ElFormItem v-if="!isCreate" label="开发策略" prop="developmentStrategy">
            <SelectPlus
              v-model="formData.developmentStrategy"
              api-key="PRODUCT_DEV_STRATEGY"
              clearable
              collapse-tags
              filterable
              multiple
              placeholder="请选择开发策略"
            />
          </ElFormItem>
          <ElFormItem v-if="!isCreate" label="开口类型" prop="openingType">
            <SelectPlus
              v-model="formData.openingType"
              api-key="PRODUCT_OPEN_TYPE"
              clearable
              filterable
              placeholder="请选择开口类型"
            />
          </ElFormItem>
          <ElFormItem label="尺码段" prop="sizeRangeId">
            <ElSelect
              v-model="formData.sizeRangeId"
              clearable
              filterable
              multiple
              placeholder="请选择尺码段"
              @change="handleSizeRangeIdChange"
            >
              <ElOption
                v-for="item in filteredSizeList"
                :key="item.id"
                :disabled="item.disabled"
                :label="item.name"
                :value="item.id!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="标准码">
            <ElSelect v-model="formData.standardSizeId" disabled filterable multiple>
              <ElOption
                v-for="item in sizeValueList"
                :key="item.value"
                :disabled="item.disabled"
                :label="item.label"
                :value="item.value!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="穿脱方式" prop="closureMethod">
            <SelectPlus
              v-model="formData.closureMethod"
              api-key="PRODUCT_CLOSE_TYPE"
              clearable
              filterable
              placeholder="请选择穿脱方式"
            />
          </ElFormItem>
          <ElFormItem label="尺码值">
            <ElInput
              :autosize="{
                minRows: 2,
                maxRows: 4
              }"
              :model-value="sizeLabel"
              disabled
              resize="none"
              type="textarea"
            />
          </ElFormItem>
          <ElFormItem label="是否含金属鞋头" prop="safetyFeatures">
            <SelectPlus
              v-model="formData.safetyFeatures"
              api-key="COMMON_YES_NO"
              clearable
              filterable
              placeholder="请选择是否含金属鞋头"
            />
          </ElFormItem>
          <ElFormItem v-if="!isCreate" label="是否有功能吊牌挂牌" prop="hasFeatureTag">
            <SelectPlus v-model="formData.hasFeatureTag" api-key="PRODUCT_FUNCTION_TAG" />
          </ElFormItem>
          <ElFormItem label="是否过踝/过膝" prop="ankleCoverage">
            <SelectPlus v-model="formData.ankleCoverage" api-key="COVER KNEE OR ANKLE" />
          </ElFormItem>
          <ElFormItem v-if="!isCreate" label="基本计量单位" prop="basicUnitOfMeasure">
            <SelectPlus
              v-model="formData.basicUnitOfMeasure"
              api-key="COMMON_MEASURE_UNIT"
              clearable
              filterable
            />
          </ElFormItem>
          <ElFormItem v-if="!isCreate" label="特殊包装方式" prop="specialPackagingMethod">
            <ElInput
              v-model="formData.specialPackagingMethod"
              :autosize="{ minRows: 4, maxRows: 4 }"
              :resize="disabled ? 'none' : undefined"
              class="w-48"
              maxlength="500"
              show-word-limit
              type="textarea"
            />
          </ElFormItem>
          <ElFormItem label="Style Partner Code" prop="stylePartnerCode">
            <ElInput
              v-model="formData.stylePartnerCode"
              :disabled="stylePartnerCodeDisabled"
              class="w-48"
              clearable
              maxlength="100"
              show-word-limit
            />
          </ElFormItem>
          <ElFormItem v-if="isView" label="Style编码" prop="styleNumber">
            <ElInput v-model="formData.styleNumber" class="w-48" disabled />
          </ElFormItem>
          <ElFormItem v-if="isView" label="产品衍生类型" prop="derivedType">
            <ElInput v-model="formData.derivedType" class="w-48" disabled />
          </ElFormItem>
          <ElFormItem label="备注" prop="remarks">
            <ElInput
              v-model="formData.remarks"
              :autosize="{ minRows: 4, maxRows: 4 }"
              :resize="disabled ? 'none' : undefined"
              class="w-48"
              maxlength="500"
              show-word-limit
              type="textarea"
            />
          </ElFormItem>
        </div>
      </ElCollapseItem>
      <ElCollapseItem v-if="isCopy" name="2" title="产品运营信息">
        <template #title>
          <div class="font-bold text-base">产品运营信息</div>
        </template>
        <div class="grid grid-cols-2 items-start gap-x-4">
          <ElFormItem label="吊牌价($)" prop="retailPrice">
            <ElInputNumber
              v-model="formData.retailPrice"
              :controls="false"
              :min="0"
              :precision="2"
              :value-on-clear="null"
              class="min-w-48"
            />
          </ElFormItem>
          <ElFormItem label="产品目标定级" prop="productPositioning">
            <SelectPlus
              v-model="formData.productPositioning"
              api-key="PRODUCT_POSITION"
              cache
              clearable
              filterable
              placeholder="请选择产品目标定级"
            />
          </ElFormItem>
          <ElFormItem label="产品系列" prop="developmentDirection">
            <SelectPlus
              v-model="formData.developmentDirection"
              api-key="PRODUCT_SERIES"
              cache
              clearable
              filterable
              placeholder="请选择产品系列"
              @change="
                () => {
                  formData.developmentDirectionSubCode = ''
                }
              "
            />
          </ElFormItem>
          <ElFormItem label="产品子系列" prop="developmentDirectionSubCode">
            <ApiSelect
              v-model="formData.developmentDirectionSubCode"
              :childComponent="ElSelect"
              :api-config="{
                api: getDictLeafOpen,
                config: {
                  label: 'nameCn',
                  value: 'value'
                }
              }"
              :key="formData.developmentDirection"
              :params="{ typeCode: 'PRODUCT_SERIES', itemValue: formData.developmentDirection }"
              cache
              clearable
              filterable
              placeholder="请选择产品子系列"
            />
          </ElFormItem>
          <ElFormItem label="强制性警示声明" prop="mandatoryWarningStatement">
            <SelectPlus
              v-model="formData.mandatoryWarningStatement"
              api-key="COMMON_YES_NO"
              cache
              radio
            />
          </ElFormItem>
          <ElFormItem label="PRODUCT NAME" prop="shoeName">
            <ElInput
              v-model="formData.shoeName"
              class="w-48"
              clearable
              maxlength="100"
              placeholder="请输入"
              show-word-limit
            />
          </ElFormItem>
          <ElFormItem label="使用电池/附带电池" prop="usesBattery">
            <SelectPlus v-model="formData.usesBattery" api-key="COMMON_YES_NO" cache radio />
          </ElFormItem>
          <ElFormItem label="是否上市" prop="isProductLaunched">
            <SelectPlus v-model="formData.isProductLaunched" api-key="COMMON_YES_NO" cache radio />
          </ElFormItem>
        </div>
      </ElCollapseItem>
    </ElForm>
  </ElCollapse>
  <Teleport v-if="mounted" to=".product-info-footer">
    <span v-show="currentPane === 'BillingInfo' && (isView || currentPane !== initPane)">
      <ElButton
        v-if="
          formData.dataStatus?.toLowerCase() === ProductDataStatusEnum.EFFECT ||
          formData.dataStatus?.toLowerCase() === ProductDataStatusEnum.DRAFT
        "
        v-hasPermi="['editProduct:editProduct', 'viewProduct:editProduct']"
        @click="handleEditProduct"
        type="primary"
      >
        修改产品信息
      </ElButton>
    </span>
  </Teleport>
</template>

<style lang="less" scoped>
:deep(.const-range-max-form-item .el-form-item__label::before) {
  content: none !important;
}
</style>
