<script lang="ts" setup>
import { Emit, Props } from '@/views/basic-library-manage/product-library/api/productInfo'
import DistributeInfoDialog from './DistributeInfoDialog.vue'
import noop from 'xe-utils/noop'
import FactoryQuotationDialog from './FactoryQuotationDialog.vue'
import {
  descriptionsItem,
  getProductFactory,
  ProductFactoryAPI
} from '@/views/basic-library-manage/product-library/api/distributeInfoDialog'
import { useProductInfo } from './helper'

defineOptions({
  name: 'DistributeInfo'
})

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const { formData, formRef } = useProductInfo(props, emit)

const activeNames = ref(['1'])

const distributeInfoDialogVisible = ref(false)
const handleOpenDistributeInfoDialog = () => {
  distributeInfoDialogVisible.value = true
}

const factoryQuotationDialogVisible = ref(false)
const handleOpenFactoryQuotationDialog = () => {
  factoryQuotationDialogVisible.value = true
}

const handleRefresh = () => {
  fetchFactoryInfoList()
}

const descriptionData = ref<ProductFactoryAPI.ProductFactoryResp>()
const queryLoading = ref(false)
const factoryInfoList = ref<ProductFactoryAPI.List>([])
const fetchFactoryInfoList = async () => {
  if (!formData.value.id) return
  queryLoading.value = true
  const [error, result] = await getProductFactory(+formData.value.id)
  queryLoading.value = false
  if (!error && result?.datas) {
    descriptionData.value = result.datas
    factoryInfoList.value = result.datas.factoryConfigList || []
  }
}
onMounted(fetchFactoryInfoList)

defineExpose({
  submit: noop
})
</script>

<template>
  <FactoryQuotationDialog
    v-model="factoryQuotationDialogVisible"
    :selected-rows="[formData]"
    @refresh="handleRefresh"
  />
  <DistributeInfoDialog
    v-model="distributeInfoDialogVisible"
    :selected-rows="[formData]"
    @refresh="handleRefresh"
  />
  <ElCollapse v-model="activeNames">
    <ElForm ref="formRef" :model="formData" disabled label-width="auto">
      <ElCollapseItem name="1" disabled>
        <template #title>
          <div class="font-bold text-base text-[var(--el-collapse-header-text-color)]">
            工厂信息
          </div>
        </template>
        <Descriptions
          :data="descriptionData"
          :schema="descriptionsItem"
          class="descriptions-details-layout__2"
        />
        <VxeTable
          :cell-config="{ height: 80 }"
          :data="factoryInfoList"
          :loading="queryLoading"
          :show-header-overflow="false"
          :show-overflow="false"
        >
          <VxeColumn title="序号" type="seq" width="60" />
          <VxeColumn field="productNumber" min-width="100" title="产品编码" />
          <VxeColumn field="assignedFactoryItemName" min-width="120" title="分配工厂" />
          <VxeColumn field="regionalSupplyPersonIdItemName" min-width="120" title="区域供管" />
          <VxeColumn
            field="developmentContactPersonIdItemName"
            min-width="120"
            title="跟进开发人员"
          />
          <VxeColumn
            field="technicalContactPersonIdItemName"
            min-width="120"
            title="跟进技术人员"
          />
          <VxeColumn field="regionItemName" min-width="100" title="区域" />
          <VxeColumn field="remark" title="备注" width="300" />
          <VxeColumn field="addInformationImg" title="补充说明" width="140">
            <template #default="{ row }: { row: ProductFactoryAPI.Row }">
              <OssUpload
                v-model="row.addInformationImg"
                :limit="2"
                :size-limit="1024 * 1024 * 100"
                disabled
                drag
                list-type="text"
                multiple
              />
            </template>
          </VxeColumn>
          <VxeColumn field="factoryQuotation" title="工厂报价(￥)含税" width="130" />
          <VxeColumn field="singleColorMoq" title="单色MOQ" width="100" />
          <VxeColumn field="singleStyleMoq" title="单款MOQ" width="100" />
          <VxeColumn field="modifyByIdItemName" title="操作人" width="120" />
          <VxeColumn field="modifyTime" title="操作时间" width="140" />
        </VxeTable>
      </ElCollapseItem>
    </ElForm>
  </ElCollapse>
  <Teleport v-if="mounted" to=".product-info-footer">
    <span v-show="currentPane === 'DistributeInfo' && (isView || initPane !== currentPane)">
      <ElButton
        v-hasPermi="['viewProduct:distributeInfo', 'editProduct:distributeInfo']"
        @click="handleOpenDistributeInfoDialog"
      >
        修改打样分配信息
      </ElButton>
      <ElButton
        type="primary"
        v-hasPermi="['viewProduct:factoryInfo', 'editProduct:factoryInfo']"
        @click="handleOpenFactoryQuotationDialog"
      >
        修改工厂报价信息
      </ElButton>
    </span>
  </Teleport>
</template>

<style lang="less" scoped></style>
