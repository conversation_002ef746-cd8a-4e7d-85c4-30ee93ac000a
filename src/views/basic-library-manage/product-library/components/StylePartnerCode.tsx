import { getStylePartnerCode, StylePartnerCodeAPI } from '../api/stylePartnerCode'
import { ElInput, ElMessage, ElMessageBox, ElTable, ElTableColumn } from 'element-plus'
import { ProductSelectResultEnum, ProductTaskNodeEnum } from '../const'
import { BrandEnum, YesNoEnum } from '../../const'
import { createProductInfo, FormModel, SaveTypeEnum } from '../api/productInfo'
import { getMaterialCode } from '@/views/basic-library-manage/product-library/components/helper'

export const stylePartnerCode = async (id: number, formData: FormModel): Promise<boolean> => {
  const data = ref<(StylePartnerCodeAPI.BaseProductSku & { disabled?: boolean })[]>([])
  const [error, result] = await getStylePartnerCode({
    productId: id,
    skcId: (formData.skcInfoDetailResp || [])
      .filter((e) => e.selectable === ProductSelectResultEnum.SELECTED)
      .map((e) => e.id!),
    sizeId: formData.selectedSize
  })

  const { sendWms } = formData

  if (error === null && result?.datas) {
    data.value = result.datas.map((e) => ({
      ...e,
      disabled: !!(e.skuPartnerCode && sendWms === (YesNoEnum.Y as string))
    }))
  } else {
    return Promise.resolve(false)
  }

  return new Promise((resolve) => {
    if (error === null && result?.datas) {
      ElMessageBox({
        title: '选品会信息确认，填写完成SKU外部编号再下发WMS',
        showClose: true,
        draggable: true,
        showCancelButton: true,
        customClass: 'style-partner-code-dialog min-w-[90vw] align-baseline',
        message: () => {
          return (
            <ElTable data={data.value} border max-height={500} show-overflow-tooltip>
              <ElTableColumn align="center" label="序号" type="index" width="60" />
              <ElTableColumn align="center" property="productNumber" label="产品编号" />
              <ElTableColumn
                align="center"
                width="200"
                property="stylePartnerCode"
                label="Style Partner Code"
              />
              <ElTableColumn align="center" property="skcCode" label="SKC编号" />
              <ElTableColumn align="center" property="productColor" label="产品配色" />
              <ElTableColumn align="center" property="skuCode" label="SKU编号" />
              <ElTableColumn align="center" property="sizeIdItemName" label="尺码值" />
              <ElTableColumn
                width="200"
                align="center"
                property="skuPartnerCode"
                label="SKU Partner Code"
              >
                {{
                  default: ({
                    row
                  }: {
                    row: StylePartnerCodeAPI.BaseProductSku & { disabled?: boolean }
                  }) => {
                    return (
                      <ElInput
                        vModel={row.skuPartnerCode}
                        disabled={row.disabled}
                        showWordLimit
                        maxlength="200"
                        clearable
                      />
                    )
                  }
                }}
              </ElTableColumn>
              <ElTableColumn align="center" property="createByIdItemName" label="创建人" />
              <ElTableColumn align="center" property="createTime" label="创建时间" />
            </ElTable>
          )
        },
        beforeClose: async (action, _, done) => {
          if (action === 'confirm') {
            const req = [BrandEnum.FABKIDS, BrandEnum.JUSTFAB, BrandEnum.SHOEDAZZLE]
            if (req.includes(formData.brand!) && !data.value.every((e) => e.skuPartnerCode)) {
              ElMessage.warning('请填写SKU Partner Code')
              return
            }
            formData.skuList = data.value
            const { assignedFactory } = formData
            const [err, res] = await createProductInfo({
              ...formData,
              // 大货供应商
              assignedFactory: Array.isArray(assignedFactory) ? assignedFactory.join() : '',
              skcInfoDetailResp: formData.skcInfoDetailResp?.map((e) => ({
                ...e,
                coriumMaterialCode: getMaterialCode(e.coriumMaterialCode),
                otherMaterialCode: getMaterialCode(e.otherMaterialCode)
              })),
              operateBottom: SaveTypeEnum.SUBMIT,
              taskNode: ProductTaskNodeEnum.PRODUCT_CONCLUSION,
              sendWms: YesNoEnum.Y as string,
              autoSendWms: true
            })
            if (!err && res) {
              done()
              resolve(true)
              ElMessage.success(res.msg || '操作成功')
              useClosePage('ProductLibrary')
              return true
            } else {
              formData.skuList = []
              resolve(false)
            }
          } else {
            done()
            resolve(false)
          }
        }
      })
    } else {
      console.error(error)
      resolve(false)
    }
  })
}
