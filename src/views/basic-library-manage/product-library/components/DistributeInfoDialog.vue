<script lang="ts" setup>
import { ProductListPageAPI } from '../api/product-list'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  descriptionsItem,
  FactoryListAPI,
  getFactoryList,
  getProductFactory,
  getQueryTeamByParams,
  ProductFactoryAPI,
  saveProductFactory
} from '../api/distributeInfoDialog'
import { storeToRefs } from 'pinia'
import { useUserInfoStore } from '@/store/modules/userInfo'
import { cloneDeep } from 'lodash-es'
import { VxeTableInstance } from 'vxe-table'
import { VXE_TABLE_ROW_KEY } from '@/constants'
import { YesNoEnum } from '@/views/basic-library-manage/const'

defineOptions({
  name: 'DistributeInfoDialog'
})

const props = defineProps<{
  modelValue: boolean
  selectedRows: ProductListPageAPI.List
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

let hasFactory = false
const tableData = ref<ProductFactoryAPI.List>([])
const tableRef = ref<VxeTableInstance>()
const queryLoading = ref(false)

const handleCopy = (row: ProductFactoryAPI.Row, rowIndex: number) => {
  tableData.value.splice(rowIndex + 1, 0, {
    ...cloneDeep(row),
    id: undefined,
    assignedFactory: undefined,
    addFactoryInitialId: [],
    regionalSupplyPersonId: undefined,
    developmentContactPersonId: undefined,
    technicalContactPersonId: undefined,
    region: undefined,
    factoryQuotation: undefined,
    singleStyleMoq: undefined,
    singleColorMoq: undefined,
    [VXE_TABLE_ROW_KEY]: undefined
  })
  tableRef.value?.loadData(tableData.value)
}

const handleClose = () => {
  visible.value = false
  hasFactory = false
  tableData.value = []
}

const { userInfo } = storeToRefs(useUserInfoStore())

const formData = ref<ProductFactoryAPI.ProductFactoryResp>()

watch(
  () => visible.value,
  async (val) => {
    if (val) {
      queryLoading.value = true
      const productId = props.selectedRows.map((e) => e.id!)[0]
      const [error, result] = await getProductFactory(productId)
      if (!error && result?.datas) {
        formData.value = result.datas
        tableData.value =
          (result.datas || {}).factoryConfigList?.map((e) => ({
            ...e,
            regionalSupplyPersonId: e.regionalSupplyPersonId?.length
              ? e.regionalSupplyPersonId
              : [userInfo.value.id!]
          })) || []
        if (tableData.value.some((e) => e.assignedFactory)) {
          hasFactory = true
        }
        queryLoading.value = false
      }
    }
  }
)

const factoryList = ref<FactoryListAPI.Data[]>([])
const fetchFactoryList = async () => {
  const [error, result] = await getFactoryList()
  if (!error && result) {
    factoryList.value = result.datas || []
  }
}
const handleFactoryChange = async (val: number, row: ProductFactoryAPI.Row) => {
  const factoryObj = factoryList.value.find((e) => e.vendorId === val)
  const { brand, developmentModel } = formData.value || {}
  if (factoryObj) {
    row.region = factoryObj.areaCode?.toString()
    // 查询对应的人员
    const [error, result] = await getQueryTeamByParams({
      factoryId: val,
      band: brand,
      developmentModel
    })

    if (!error && result) {
      const { developmentContactPersonId, technicalContactPersonId } = result.datas
      row.developmentContactPersonId = developmentContactPersonId
      row.technicalContactPersonId = technicalContactPersonId
    }
  }
}

fetchFactoryList()

const submitLoading = ref(false)
const commonSubmit = async () => {
  submitLoading.value = true
  const [error, result] = await saveProductFactory({ factoryConfigList: tableData.value })
  submitLoading.value = false
  if (!error && result) {
    ElMessage.success(result.msg || '操作成功')
    emit('refresh')
    handleClose()
  }
}

const handleSubmit = () => {
  const valid = tableData.value.every(
    (e) =>
      e.assignedFactory &&
      e.developmentContactPersonId?.length &&
      e.technicalContactPersonId?.length &&
      e.regionalSupplyPersonId?.length
  )
  if (!valid) {
    ElMessage.warning('请选择分配工厂、跟进开发人员、跟进技术人员、区域供管')
    return
  }
  if (hasFactory) {
    ElMessageBox.alert('存在已分配工厂，是否确认修改', '提示', {
      showCancelButton: true,
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = instance.cancelButtonLoading = true
          await commonSubmit()
          instance.confirmButtonLoading = instance.cancelButtonLoading = false
          done()
        } else {
          done()
        }
      }
    })
  } else {
    commonSubmit()
  }
}
</script>

<template>
  <Dialog v-model="visible" :before-close="handleClose" title="打样分配" top="5vh">
    <Descriptions
      :data="formData"
      :schema="descriptionsItem"
      class="descriptions-details-layout__2"
    />
    <VxeTable
      ref="tableRef"
      :cell-config="{ height: 80 }"
      :data="tableData"
      :loading="queryLoading"
      :max-height="500"
      :show-header-overflow="false"
      :show-overflow="false"
    >
      <VxeColumn title="序号" type="seq" width="60" />
      <VxeColumn field="assignedFactoryItemName" min-width="180" title="分配工厂">
        <template #default="{ row }: { row: ProductFactoryAPI.Row }">
          <ElSelect
            v-model="row.assignedFactory"
            clearable
            filterable
            placeholder="请选择"
            @change="(val) => handleFactoryChange(val, row)"
          >
            <ElOption
              v-for="item in factoryList"
              :key="item.vendorCode"
              :disabled="item.useStatus !== +YesNoEnum.Y"
              :label="item.vendorName"
              :value="item.vendorId!"
            />
          </ElSelect>
        </template>
      </VxeColumn>
      <VxeColumn field="regionalSupplyPersonId" min-width="180" title="区域供管">
        <template #default="{ row }: { row: ProductFactoryAPI.Row }">
          <CascadeSelector
            v-model="row.regionalSupplyPersonId"
            :props="{ multiple: true, emitPath: false }"
            :show-all-levels="false"
            api-key="allUsers"
            cache
            clearable
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择"
          />
        </template>
      </VxeColumn>
      <VxeColumn field="developmentContactPersonId" min-width="180" title="跟进开发人员">
        <template #default="{ row }: { row: ProductFactoryAPI.Row }">
          <CascadeSelector
            v-model="row.developmentContactPersonId"
            :props="{ multiple: true, emitPath: false }"
            :show-all-levels="false"
            api-key="allUsers"
            cache
            clearable
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择"
          />
        </template>
      </VxeColumn>
      <VxeColumn field="technicalContactPersonId" min-width="180" title="跟进技术人员">
        <template #default="{ row }: { row: ProductFactoryAPI.Row }">
          <CascadeSelector
            v-model="row.technicalContactPersonId"
            :props="{ multiple: true, emitPath: false }"
            :show-all-levels="false"
            api-key="allUsers"
            cache
            clearable
            collapse-tags
            collapse-tags-tooltip
            placeholder="请选择"
          />
        </template>
      </VxeColumn>
      <VxeColumn field="region" min-width="140" title="区域">
        <template #default="{ row }: { row: ProductFactoryAPI.Row }">
          <ElSelect v-model="row.region" disabled>
            <ElOption
              v-for="item in factoryList"
              :key="item.vendorCode"
              :label="item.areaName"
              :value="item.areaCode!"
            />
          </ElSelect>
        </template>
      </VxeColumn>
      <VxeColumn field="remark" title="备注" width="300">
        <template #default="{ row }: { row: ProductFactoryAPI.Row }">
          <ElInput
            v-model="row.remark"
            :autosize="{ minRows: 3 }"
            maxlength="200"
            placeholder="请输入"
            show-word-limit
            type="textarea"
          />
        </template>
      </VxeColumn>
      <VxeColumn field="addInformationImg" title="补充说明" width="140">
        <template #default="{ row }: { row: ProductFactoryAPI.Row }">
          <OssUpload
            v-model="row.addInformationImg"
            :limit="2"
            :size-limit="1024 * 1024 * 100"
            drag
            list-type="text"
            multiple
          />
        </template>
      </VxeColumn>
      <VxeColumn :show-overflow="false" title="操作" width="120">
        <template #default="{ row, rowIndex }: { row: ProductFactoryAPI.Row, rowIndex: number }">
          <ElButton link type="primary" @click="handleCopy(row, rowIndex)">复制</ElButton>
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
