<script lang="ts" setup>
import { ProductListPageAPI } from '../api/product-list'
import { isNil } from 'lodash-es'
import { ElMessage } from 'element-plus'
import {
  batchGetProductFactory,
  ProductFactoryAPI,
  saveProductFactory
} from '../api/distributeInfoDialog'

defineOptions({
  name: 'FactoryQuotationDialog'
})

const props = defineProps<{
  modelValue: boolean
  selectedRows: ProductListPageAPI.List
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const tableData = ref<ProductFactoryAPI.List>([])
const queryLoading = ref(false)
watch(
  () => visible.value,
  async (val) => {
    if (val) {
      queryLoading.value = true
      const idList = props.selectedRows.map((e) => e.id!)
      const [error, result] = await batchGetProductFactory({ idList })
      queryLoading.value = false
      if (!error && result?.datas) {
        tableData.value = result.datas || []
      }
    }
  }
)

const handleClose = () => {
  visible.value = false
  tableData.value = []
}

const submitLoading = ref(false)
const handleSubmit = async () => {
  const valid = tableData.value.every((e) => !isNil(e.factoryQuotation))
  if (!valid) {
    ElMessage.error('请填写工厂报价')
    return
  }
  submitLoading.value = true
  const [error, result] = await saveProductFactory({ factoryConfigList: tableData.value })
  submitLoading.value = false
  if (!error && result) {
    emit('refresh')
    ElMessage.success(result.msg || '提交成功')
    handleClose()
  }
}
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :parent-scroll="false"
    title="工厂报价"
    top="5vh"
  >
    <VxeTable
      ref="table"
      :cell-config="{ height: 80 }"
      :data="tableData"
      :max-height="500"
      :show-header-overflow="false"
    >
      <VxeColumn title="序号" type="seq" width="60" />
      <VxeColumn field="productNumber" title="产品编码" />
      <VxeColumn field="assignedFactoryItemName" min-width="175" title="供应商" />
      <VxeColumn min-width="175" title="工厂报价(￥)含税">
        <template #header>
          <span class="text-red-500">* </span>
          <span>工厂报价(￥)含税</span>
        </template>
        <template #default="{ row }: { row: ProductFactoryAPI.Row }">
          <ElInputNumber
            v-model="row.factoryQuotation"
            :controls="false"
            :min="0"
            :precision="2"
            :value-on-clear="null"
          />
        </template>
      </VxeColumn>
      <VxeColumn field="singleColorMoq" min-width="175" title="单色MOQ">
        <template #default="{ row }: { row: ProductFactoryAPI.Row }">
          <ElInputNumber
            v-model="row.singleColorMoq"
            :controls="false"
            :min="0"
            :step="1"
            :value-on-clear="null"
            step-strictly
          />
        </template>
      </VxeColumn>
      <VxeColumn field="singleStyleMoq" min-width="175" title="单款MOQ">
        <template #default="{ row }: { row: ProductFactoryAPI.Row }">
          <ElInputNumber
            v-model="row.singleStyleMoq"
            :controls="false"
            :min="0"
            :step="1"
            :value-on-clear="null"
            step-strictly
          />
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
