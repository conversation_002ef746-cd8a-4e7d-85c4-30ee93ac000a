<script lang="tsx" setup>
import { ref } from 'vue'
import { CreateProductInfoAPI, FormModel } from '../api/productInfo'
import { VxeTableInstance, VxeTablePropTypes } from 'vxe-table'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ColorListPageAPI } from '../../color-library/api/color-list'
import ColorInfoDialog from '../../components/ColorInfoDialog.vue'
import { ProductDataStatusEnum } from '../../const'
import { productSkcInfoUpdateStatus } from '@/api/productSkcInfo'
import { MaterialTypeEnum, ProductSelectResultEnum } from '../const'
import { Icon } from '@/components/Icon'
import { CommonYesNoEnums, ProductDataStatusEnums } from '@/enums'
import { cloneDeep, pick } from 'lodash-es'
import { VXE_TABLE_ROW_KEY } from '@/constants'
import {
  FabricTypeEnum,
  MaterialConfig,
  useMaterial
} from '@/views/basic-library-manage/sample-manage/components/hooks'
import { getValuesByKey } from '@/utils'

interface Props {
  formData: FormModel
  type?: MaterialTypeEnum
  isEdit?: boolean
}

const props = defineProps<Props>()

const isSelected = computed(() => props.type === MaterialTypeEnum.SELECTED)

const {
  materialInfo,
  materialCascaderProps,
  materialCategoryList,
  liningMaterialCategoryList,
  outsoleMaterialCategoryList,
  insoleMaterialCategoryList
} = useMaterial()

const allMaterialInfo = computed(() => {
  const fabricTypeList: string[] = [
    FabricTypeEnum.FABRIC1,
    FabricTypeEnum.FABRIC2,
    FabricTypeEnum.FABRIC3,
    FabricTypeEnum.FABRIC4,
    FabricTypeEnum.LINING_MATERIAL,
    FabricTypeEnum.LINING_PLUSH,
    FabricTypeEnum.INSOLE_MATERIAL,
    FabricTypeEnum.OUTSOLE_MATERIAL
  ]
  return materialInfo.value.filter((e) => fabricTypeList.includes(e.indexName!))
})

const { formData } = toRefs(props)
const tableRef = ref<VxeTableInstance>()

const tableData = computed({
  get() {
    const skcInfoDetailResp = formData.value.skcInfoDetailResp || []
    if (!isSelected.value) {
      return skcInfoDetailResp
    }
    return skcInfoDetailResp.filter((e) => {
      if (!props.isEdit) {
        // view 展示 生效 草稿 作废
        return true
      }
      // edit 展示 生效  草稿
      return (
        ProductDataStatusEnums.Effective === e.dataStatus ||
        ProductDataStatusEnums.Draft === e.dataStatus
      )
    })
  },
  set(skcInfoDetailResp) {
    skcInfoDetailResp.forEach((item) => {
      const currentItem = formData.value.skcInfoDetailResp?.find((e) => e.id === item.id)
      if (currentItem) {
        Object.assign(currentItem, item)
      }
    })
  }
})
const tableRules = ref<VxeTablePropTypes.EditRules>({
  colorCode: [
    {
      required: true,
      content: '请选择产品配色',
      trigger: 'blur'
    }
  ],
  mainFabric: [
    {
      required: true,
      content: '请选择主面料',
      trigger: 'blur'
    }
  ],
  colorType: [
    {
      required: true,
      content: '请选择配色类型',
      trigger: 'blur'
    }
  ],
  velvetApplied: [
    {
      required: true,
      content: '请选择是否植绒',
      trigger: 'blur'
    }
  ],
  sampleUrl: [
    {
      required: true,
      content: '请上传样品图',
      trigger: 'change'
    }
  ],
  'materialInfoList.0.material': [
    {
      required: true,
      content: '请选择面料1',
      trigger: 'blur'
    }
  ],
  liningMaterialCode: [
    {
      required: true,
      content: '请选择里材料',
      trigger: 'blur'
    }
  ],
  paddingMaterialSurfaceCode: [
    {
      required: true,
      content: '请选择垫材料',
      trigger: 'blur'
    }
  ],
  soleMaterialOutsoleCode: [
    {
      required: true,
      content: '请选择底材料',
      trigger: 'blur'
    }
  ]
})
const handleAddSKC = () => {
  const materialInfoList = new Array(4).fill(null).map((_, i) => ({ indexName: `面料${i + 1}` }))
  if (!formData.value.skcInfoDetailResp) {
    formData.value.skcInfoDetailResp = [
      {
        materialInfoList
      }
    ]
    tableRef.value?.loadData(tableData.value)
    return
  }
  formData.value.skcInfoDetailResp.push({ materialInfoList })
  tableRef.value?.loadData(tableData.value)
}

const handleDelSKC = (row: CreateProductInfoAPI.SKCInfo, index: number) => {
  if (row.id) {
    ElMessage.warning('已经生成SKC数据，不可删除')
    return
  }
  formData.value.skcInfoDetailResp?.splice(index, 1)
  tableRef.value?.loadData(tableData.value)
}

const handleCopySKC = (row: CreateProductInfoAPI.SKCInfo, index: number) => {
  const copyRow = cloneDeep(row)
  formData.value.skcInfoDetailResp?.splice(index + 1, 0, {
    ...copyRow,
    id: undefined,
    [VXE_TABLE_ROW_KEY]: undefined,
    colorCode: undefined,
    colorId: undefined,
    colorCodeItemName: undefined,
    thumbnail: undefined,
    sampleUrl: [],
    velvetApplied: undefined,
    velvetRequirements: undefined,
    dataStatusItemName: undefined
  })
  tableRef.value?.loadData(tableData.value)
}

const handleInvalidateSKC = (row: CreateProductInfoAPI.SKCInfo) => {
  if (row.orderPlaced === CommonYesNoEnums.Yes) {
    ElMessage.warning(`${row.skcCode}已经下单，不能作废`)
    return
  }
  const isLastEffective =
    (formData.value.skcInfoDetailResp?.filter(
      (e) => e.skcStatus === ProductDataStatusEnum.EFFECT && e.id !== row.id
    ).length || 0) <= 0
  if (isLastEffective) {
    ElMessage.warning(`必须存在一个处于有效状态的SKC`)
    return
  }
  ElMessageBox({
    title: '作废',
    type: 'warning',
    draggable: true,
    closeOnClickModal: false,
    showCancelButton: true,
    message: () => {
      return (
        <div>
          <div>Warning：请确定是否对SKC进行作废，SKC作废后将同步WMS状态</div>
          <div>
            SKC编号：<span class="text-red-500">{row.skcCode}</span>
          </div>
          <div>
            产品配色：<span class="text-red-500">{row.colorCodeItemName}</span>
          </div>
        </div>
      )
    },
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = instance.cancelButtonLoading = true
        const { msg } = await productSkcInfoUpdateStatus({
          reqs: [
            {
              id: row.id!,
              dataStatus: ProductDataStatusEnum.INVALID
            }
          ]
        }).finally(() => {
          instance.confirmButtonLoading = instance.cancelButtonLoading = false
        })
        row.selectable = ProductSelectResultEnum.UNSELECTED
        row.dataStatus = ProductDataStatusEnum.INVALID
        row.dataStatusItemName = '作废'
        ElMessage.success(msg)
        done()
      } else {
        done()
      }
    }
  })
}

const handlePickColor = (val: ColorListPageAPI.Row) => {
  if (!currentRow.value) {
    return
  }
  currentRow.value.colorCodeItemName = val.englishName || ''
  currentRow.value.colorCode = val.code
  currentRow.value.colorId = val.id
  currentRow.value.thumbnail = val.thumbnail
}

const currentRow = ref<CreateProductInfoAPI.SKCInfo | null>(null)
const colorInfoDialogVisible = ref(false)
const handleOpenColorInfoDialog = (row: CreateProductInfoAPI.SKCInfo) => {
  if (row.id) {
    return
  }
  currentRow.value = row
  colorInfoDialogVisible.value = true
}

const materialConfig = ref<MaterialConfig>({
  multiple: false,
  materialOption: []
})
const material = ref<number | number[]>()
const commonFabricTypeList = [
  FabricTypeEnum.FABRIC1,
  FabricTypeEnum.FABRIC2,
  FabricTypeEnum.FABRIC3,
  FabricTypeEnum.FABRIC4
]
const fabricTypeMap = {
  [FabricTypeEnum.LINING_MATERIAL]: 'liningMaterialCode',
  [FabricTypeEnum.INSOLE_MATERIAL]: 'paddingMaterialSurfaceCode',
  [FabricTypeEnum.OUTSOLE_MATERIAL]: 'soleMaterialOutsoleCode',
  [FabricTypeEnum.LINING_PLUSH]: 'liningSituation'
}
const handleSetMaterial = () => {
  const selected = tableRef.value?.getCheckboxRecords() || []
  if (!selected.length) {
    ElMessage.warning('请先勾选数据')
    return
  }
  if (!material.value) {
    ElMessage.warning('请先选择材质')
    return
  }
  const editField = materialConfig.value.indexName!

  const index = commonFabricTypeList.findIndex((e) => e === editField)
  if (index !== -1) {
    selected.forEach((e) => {
      e.materialInfoList[index].material = material.value
    })
    tableRef.value?.loadData(tableData.value)
    return
  }

  selected.forEach((e) => {
    e[fabricTypeMap[materialConfig.value.indexName!]] = material.value
  })
  tableRef.value?.loadData(tableData.value)
}

const handleResetMaterial = () => {
  const selected = tableRef.value?.getCheckboxRecords() || []
  if (!selected.length) {
    ElMessage.warning('请先勾选数据')
    return
  }
  const editField = materialConfig.value.indexName!
  const index = commonFabricTypeList.findIndex((e) => e === editField)
  if (index !== -1) {
    selected.forEach((e) => {
      e.materialInfoList[index].material = undefined
    })
    tableRef.value?.loadData(tableData.value)
    return
  }
  selected.forEach((e) => {
    e[fabricTypeMap[materialConfig.value.indexName!]] = []
  })
  tableRef.value?.loadData(tableData.value)
}

const handleSyncFirst = () => {
  const firstRow = tableData.value?.[0]
  if (!firstRow) return
  const row = pick(firstRow, [
    'materialInfoList',
    'liningMaterialCode',
    'liningSituation',
    'paddingMaterialSurfaceCode',
    'soleMaterialOutsoleCode'
  ])

  tableData.value =
    tableData.value?.map((item) => {
      return {
        ...item,
        ...cloneDeep(row)
      }
    }) || []
}

const validate = computed(() => tableRef.value?.validate)
const scrollToRow = computed(() => tableRef.value?.scrollToRow)
const setSelectCell = computed(() => tableRef.value?.setSelectCell)

defineExpose({
  validate,
  scrollToRow,
  setSelectCell
})
</script>

<template>
  <ColorInfoDialog v-model="colorInfoDialogVisible" @submit="handlePickColor" />
  <div class="mb-2 w-full flex">
    <ElButton v-if="!isSelected" text type="primary" @click="handleAddSKC">添加</ElButton>
    <h3>批量修改属性</h3>
    <ElSelect
      v-model="materialConfig"
      :validate-event="false"
      class="ml-2 !w-48"
      filterable
      placeholder="请选择"
      value-key="indexName"
    >
      <ElOption
        v-for="item in allMaterialInfo"
        :key="item.indexName"
        :label="item.indexName"
        :value="item"
      />
    </ElSelect>
    <ElCascader
      v-model="material"
      :options="materialConfig.materialOption"
      :props="{ ...materialCascaderProps, multiple: materialConfig.multiple }"
      :validate-event="false"
      class="ml-2"
      clearable
      filterable
      placeholder="请选择材质"
    />
    <ElButton class="ml-2" type="primary" @click="handleSetMaterial">一键设置</ElButton>
    <ElButton v-if="isSelected" type="primary" @click="handleSyncFirst">一键设置同第一行</ElButton>
    <ElButton @click="handleResetMaterial">重置</ElButton>
  </div>
  <VxeTable
    ref="tableRef"
    :data="tableData"
    :edit-config="{ showIcon: false, mode: 'cell', trigger: 'click' }"
    :edit-rules="tableRules"
    :max-height="500"
    :scroll-x="{ enabled: true }"
    :scroll-y="{ enabled: true, gt: 0, mode: 'wheel' }"
    :show-overflow="false"
    :valid-config="{ showMessage: false, autoPos: true }"
    class="w-full"
  >
    <VxeColumn fixed="left" type="checkbox" width="40" />
    <VxeColumn fixed="left" title="序号" type="seq" width="60" />
    <VxeColumn :visible="isSelected" field="skcCode" fixed="left" title="SKC编号" min-width="180" />
    <VxeColumn field="dataStatusItemName" fixed="left" title="状态" width="100" />
    <VxeColumn
      field="colorCode"
      fixed="left"
      header-class-name="header-required"
      min-width="120"
      title="产品配色"
    >
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <ElInput
          v-model="row.colorCodeItemName"
          :disabled="!!row.id"
          :validate-event="false"
          placeholder="请选择"
          readonly
        >
          <template #prefix>
            <Icon
              :size="26"
              class="cursor-pointer"
              color="#409EFF"
              icon="mdi:search"
              @click="handleOpenColorInfoDialog(row)"
            />
          </template>
        </ElInput>
      </template>
    </VxeColumn>
    <VxeColumn
      :cell-render="{ name: 'Image' }"
      field="thumbnail"
      fixed="left"
      title="颜色缩略图"
      width="120"
    />
    <VxeColumn
      :show-overflow="false"
      :visible="!isSelected"
      header-class-name="header-required"
      field="sampleUrl"
      fixed="left"
      title="样品图"
      width="170"
    >
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <div class="m-2">
          <OssUpload
            v-model="row.sampleUrl"
            :disabled="isSelected"
            :limit="20"
            :show-file-list="false"
            :size-limit="1024 * 1024 * 100"
            :validate-event="false"
            accept="image/*"
            drag
            multiple
          />
          <div v-if="row.sampleUrl?.[0]" class="flex flex-col items-center relative m-[5px]">
            <Icon
              v-if="!isSelected"
              class="mb-1 absolute cursor-pointer top-[5px] right-0"
              icon="ep:close"
              @click="
                () => {
                  if (row.id) {
                    row.sampleUrl?.splice(0, 1)
                  }
                }
              "
            />
            <ElImage
              :preview-src-list="row.sampleUrl?.map((e) => e.signatureUrl || '') || []"
              :src="row.sampleUrl?.[0].signatureUrl"
              fit="contain"
              previewTeleported
              hide-on-click-modal
              class="w-[100px] h-[100px]"
            />
          </div>
        </div>
      </template>
    </VxeColumn>
    <VxeColumn
      field="mainFabric"
      fixed="left"
      header-class-name="header-required"
      min-width="120"
      title="主要面料"
    >
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <span v-if="isSelected">{{ row.mainFabricItemName }}</span>
        <ElCascader
          v-else
          v-model="row.mainFabric"
          :disabled="!!row.id"
          :options="materialCategoryList"
          :props="materialCascaderProps"
          :validate-event="false"
          clearable
          filterable
        />
      </template>
    </VxeColumn>
    <VxeColumn
      :visible="!isSelected"
      field="colorType"
      header-class-name="header-required"
      title="配色类型"
      width="120"
    >
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <SelectPlus
          v-model="row.colorType"
          :validate-event="false"
          api-key="PRODUCT_COLOR_TYPE"
          cache
        />
      </template>
    </VxeColumn>
    <template v-for="(item, index) in allMaterialInfo.slice(0, 4)" :key="item.indexName">
      <VxeColumn
        :edit-render="{}"
        :field="`materialInfoList.${index}.material`"
        :show-overflow="false"
        :title="item.indexName"
        width="120"
      >
        <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
          <span>
            {{
              getValuesByKey(item.materialOption, {
                targetValues: row.materialInfoList?.[index].material
              })
            }}
          </span>
        </template>
        <template #edit="{ row }">
          <span>
            {{
              getValuesByKey(item.materialOption, {
                targetValues: row.materialInfoList?.[index].material
              })
            }}
          </span>
          <ElCascader
            :clearable="false"
            :model-value="row.materialInfoList?.[index].material"
            :options="item.materialOption"
            :persistent="false"
            :props="materialCascaderProps"
            :validate-event="false"
            class="vxe-table--ignore-clear"
            collapse-tags
            collapse-tags-tooltip
            popper-class="vxe-table--ignore-clear"
            @change="
              (val: number) => {
                if (row.materialInfoList) {
                  row.materialInfoList[index].material = val
                }
              }
            "
          />
        </template>
      </VxeColumn>
    </template>
    <VxeColumn :edit-render="{}" field="liningMaterialCode" min-width="180" title="里材料">
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <span>
          {{ getValuesByKey(liningMaterialCategoryList, { targetValues: row.liningMaterialCode }) }}
        </span>
      </template>
      <template #edit="{ row }">
        <ElCascader
          v-model="row.liningMaterialCode"
          :clearable="false"
          :options="liningMaterialCategoryList"
          :persistent="false"
          :props="{ ...materialCascaderProps, multiple: true }"
          :validate-event="false"
          class="vxe-table--ignore-clear"
          filterable
          popper-class="vxe-table--ignore-clear"
        />
      </template>
    </VxeColumn>
    <VxeColumn
      :edit-render="{}"
      :show-overflow="false"
      field="liningSituation"
      min-width="180"
      title="里绒材料"
    >
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <span>
          {{ getValuesByKey(materialCategoryList, { targetValues: row.liningSituation }) }}
        </span>
      </template>
      <template #edit="{ row }">
        <ElCascader
          v-model="row.liningSituation"
          :clearable="false"
          :options="materialCategoryList"
          :persistent="false"
          :props="{ ...materialCascaderProps, multiple: true }"
          :validate-event="false"
          class="vxe-table--ignore-clear"
          filterable
          popper-class="vxe-table--ignore-clear"
        />
      </template>
    </VxeColumn>
    <VxeColumn :edit-render="{}" field="paddingMaterialSurfaceCode" min-width="180" title="垫材料">
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <span>
          {{
            getValuesByKey(insoleMaterialCategoryList, {
              targetValues: row.paddingMaterialSurfaceCode
            })
          }}
        </span>
      </template>
      <template #edit="{ row }">
        <ElCascader
          v-model="row.paddingMaterialSurfaceCode"
          :clearable="false"
          :options="insoleMaterialCategoryList"
          :persistent="false"
          :props="{ ...materialCascaderProps, multiple: true }"
          :validate-event="false"
          class="vxe-table--ignore-clear"
          filterable
          popper-class="vxe-table--ignore-clear"
        />
      </template>
    </VxeColumn>
    <VxeColumn :edit-render="{}" field="soleMaterialOutsoleCode" min-width="180" title="底材料">
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <span>
          {{
            getValuesByKey(outsoleMaterialCategoryList, {
              targetValues: row.soleMaterialOutsoleCode
            })
          }}
        </span>
      </template>
      <template #edit="{ row }">
        <ElCascader
          v-model="row.soleMaterialOutsoleCode"
          :clearable="false"
          :options="outsoleMaterialCategoryList"
          :persistent="false"
          :props="{ ...materialCascaderProps, multiple: true }"
          :validate-event="false"
          filterable
          popper-class="vxe-table--ignore-clear"
        />
      </template>
    </VxeColumn>
    <VxeColumn
      :visible="!isSelected"
      field="velvetApplied"
      header-class-name="header-required"
      title="是否植绒"
      width="100"
    >
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <SelectPlus
          v-model="row.velvetApplied"
          :validate-event="false"
          api-key="FLOCKING_YES_NO"
          cache
        />
      </template>
    </VxeColumn>
    <VxeColumn :visible="!isSelected" field="velvetRequirements" min-width="150" title="植绒要求">
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <ElInput
          v-model="row.velvetRequirements"
          :validate-event="false"
          clearable
          maxlength="100"
          placeholder="请输入"
          show-word-limit
        />
      </template>
    </VxeColumn>
    <VxeColumn :show-overflow="false" :visible="!isSelected" title="操作" width="160">
      <template
        #default="{ row, rowIndex }: { row: CreateProductInfoAPI.SKCInfo, rowIndex: number }"
      >
        <ElRow justify="center">
          <ElButton v-if="!row.id" text type="primary" @click="handleDelSKC(row, rowIndex)">
            删除
          </ElButton>
          <ElButton text type="primary" @click="handleCopySKC(row, rowIndex)">复制</ElButton>
        </ElRow>
        <ElRow v-if="row.id && row.dataStatus === ProductDataStatusEnum.EFFECT" justify="center">
          <ElButton text type="primary" @click="handleInvalidateSKC(row)">作废</ElButton>
        </ElRow>
      </template>
    </VxeColumn>
  </VxeTable>
</template>

<style lang="less" scoped>
:deep(.col--valid-error .vxe-cell) {
  .el-select__wrapper,
  .el-input__wrapper {
    box-shadow: 0 0 0 1px red inset !important;
  }
}
</style>
