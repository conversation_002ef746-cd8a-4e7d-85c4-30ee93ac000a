<script lang="ts" setup>
import { VxeTableInstance } from 'vxe-table'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import noop from 'xe-utils/noop'
import {
  CreateProductInfoAPI,
  Emit,
  Props
} from '@/views/basic-library-manage/product-library/api/productInfo'

defineOptions({
  name: 'SkcInfo'
})

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const formData = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const tableRef = ref<VxeTableInstance>()
const maxHeight = useTableHeight({
  tableRef
})

defineExpose({
  submit: noop
})
</script>

<template>
  <VxeTable
    ref="tableRef"
    :cell-config="{ height: 80 }"
    :data="formData.skcInfoDetailResp"
    :max-height="maxHeight"
    :show-header-overflow="false"
    :show-overflow="false"
  >
    <VxeColumn title="序号" type="seq" width="60" />
    <VxeColumn field="skcCode" title="SKC编号" min-width="180" />
    <VxeColumn field="skuName" title="选中颜色+编号" width="120">
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        {{ row.colorCodeItemName }} - {{ row.colorCode }}
      </template>
    </VxeColumn>
    <VxeColumn :cell-render="{ name: 'Image' }" field="thumbnail" title="颜色缩略图" width="120" />
    <VxeColumn field="wmsColorName" title="WMS色号名称" width="120" />
    <VxeColumn field="mainFabricItemName" title="主面料" width="80" />
    <VxeColumn field="colorTypeItemName" title="配色类型" width="80" />
    <VxeColumn field="selectionMeetingResultItemName" title="选品会结论" width="90" />
    <VxeColumn
      :cell-render="{ name: 'Image' }"
      field="initialProofResultImg"
      title="初样样品图"
      width="120"
    />
    <VxeColumn
      :cell-render="{ name: 'Image' }"
      field="colorProofResultImg"
      title="齐色样样品图"
      width="120"
    />
    <VxeColumn
      :cell-render="{ name: 'Image' }"
      field="confirmProofResultImg"
      title="确认样样品图"
      width="120"
    />
    <VxeColumn :cell-render="{ name: 'Image' }" field="erpSkcImgUrl" title="白底图" width="120" />
    <VxeColumn field="velvetAppliedItemName" title="是否植绒" width="80" />
    <VxeColumn field="velvetRequirements" title="植绒要求" width="80" />
    <VxeColumn field="velvetTariff" title="植绒/植皮关税率" width="140" />
    <VxeColumn field="unVelvetTariff" title="不植关税率" width="150" />
    <VxeColumn field="sendWmsItemName" title="最后一次下发WMS状态" min-width="175" />
    <VxeColumn field="downOrderFlag" title="SKC下单状态" width="100" />
    <VxeColumn field="orderCount" title="SKC下单数量" width="100" />
    <VxeColumn field="dataStatusItemName" title="状态" width="100" />
  </VxeTable>
</template>

<style lang="less" scoped></style>
