<script lang="ts" setup>
import { Emit, Props } from '@/views/basic-library-manage/product-library/api/productInfo'
import noop from 'xe-utils/noop'
import { ProductTaskNodeEnum } from '../const'
import BoxGaugeDialog from './BoxGaugeDialog.vue'
import { ProductListPageAPI } from '../api/product-list'

defineOptions({
  name: 'PackingInfo'
})

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const formData = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const boxGaugeDialogVisible = ref(false)
const currentRow = computed<ProductListPageAPI.Row>(() => ({
  id: formData.value.id
}))
const handleOpenBoxGaugeDialog = () => {
  boxGaugeDialogVisible.value = true
}

defineExpose({
  submit: noop
})
</script>

<template>
  <BoxGaugeDialog v-model="boxGaugeDialogVisible" :current-row="currentRow" is-view />
  <ElForm ref="formRef" :model="formData" disabled label-width="auto">
    <ElRow :gutter="20">
      <ElCol :span="12">
        <ElFormItem label="包装代码" prop="packagingCode">
          <SelectPlus v-model="formData.packagingCode" api-key="packageCode" cache />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="包装类型" prop="packagingType">
          <SelectPlus v-model="formData.packagingType" api-key="PRODUCT_PACKAGE_TYPE" cache />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="内里标签" prop="liningLabel">
          <ElInput v-model="formData.liningLabel" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem label="装箱数(箱规)" prop="cartonQuantity">
          <VxeButton status="primary" type="text" @click="handleOpenBoxGaugeDialog">
            查看箱规信息
          </VxeButton>
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem label="操作人" prop="createById">
          <ElInput
            :model-value="
              formData.taskTimeRecord?.[ProductTaskNodeEnum.PACKAGING_MAINTENANCE]
                ?.createByIdItemName
            "
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem label="操作时间" prop="createTime">
          <ElDatePicker
            :model-value="
              formData.taskTimeRecord?.[ProductTaskNodeEnum.PACKAGING_MAINTENANCE]?.createTime
            "
            value-format="YYYY-MM-DD"
          />
        </ElFormItem>
      </ElCol>
    </ElRow>
  </ElForm>
</template>

<style lang="less" scoped></style>
