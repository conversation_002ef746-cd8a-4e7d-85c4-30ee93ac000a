<script lang="ts" setup>
import {
  ElCascader,
  ElLoading,
  ElMessage,
  FormInstance,
  FormItemRule,
  FormRules
} from 'element-plus'
import { storeToRefs } from 'pinia'
import { useUserInfoStore } from '@/store/modules/userInfo'
import {
  DictValueAPI,
  getModelInfoByProductNumber,
  getProductCategoryList,
  ProductCategoryListAPI
} from '@/views/basic-library-manage/api/common'
import { useBasicLibraryDictStore } from '@/views/basic-library-manage/store/dict'
import { sizeList as getSizeList } from '@/components/Business/SelectPlus/src/api/index'
import { SizeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import {
  createProductInfo,
  CreateProductInfoAPI,
  FormModel,
  getProductInfo,
  getProductStore,
  getStyleImage,
  resetProductStore,
  saveProductStore,
  SaveTypeEnum
} from '@/views/basic-library-manage/product-library/api/productInfo'
import { Icon } from '@/components/Icon'
import {
  FactoryListAPI,
  getFactoryList
} from '@/views/basic-library-manage/product-library/api/distributeInfoDialog'
import {
  DevelopModeEnum,
  PFLevelEnum,
  ProductSelectResultEnum,
  ProductTaskNodeEnum
} from '@/views/basic-library-manage/product-library/const'
import {
  BrandEnum,
  HeadCategoryEnum,
  HeadStandardEnum,
  ProductCategoryEnum,
  ProductDataStatusEnum,
  shoesLastStandardList,
  shoesToeCategoryList,
  StatusEnum,
  YesNoEnum
} from '@/views/basic-library-manage/const'
import { CommonYesNoEnums, InfringementRiskLevelEnums, SizeCodeTypeEnums } from '@/enums'
import { sendToWMS } from '@/views/basic-library-manage/product-library/components/SendToWMS'
import ModelInfoDialog from '@/views/basic-library-manage/components/ModelInfoDialog.vue'
import { ModelListPageAPI } from '@/views/basic-library-manage/model-library/api/model-list'
import { stylePartnerCode } from '@/views/basic-library-manage/product-library/components/StylePartnerCode'
import AddMaterial from '@/views/basic-library-manage/product-library/components/AddMaterial.vue'
import { VxeTableDefines } from 'vxe-table'
import { getMaterialCode } from '@/views/basic-library-manage/product-library/components/helper'
import { groupBy } from 'lodash-es'
import { useSizeOptions } from '@/utils/useSizeOptions'
import { getDictLeafOpen } from '@/api/common'

defineOptions({
  name: 'QuickCreate'
})

const route = useRoute()
const router = useRouter()

const isCreate = computed(() => {
  return route.name === 'QuickCreateProduct'
})

const actives = ref(['1', '2', '3', '4'])

const formRef = ref<FormInstance>()
const formData = ref<FormModel>({
  sizeRangeId: []
})

const skcInfoValidator: FormItemRule['asyncValidator'] = async (_rule, _value, callback) => {
  if (!formData.value.skcInfoDetailResp?.length) {
    callback(new Error('请至少添加一条SKC数据'))
    return
  }
  const errorMsg: VxeTableDefines.ValidatorErrorMapParams | undefined | boolean =
    await tableRef.value?.validate?.(true).catch(() => false)
  if (errorMsg) {
    let message = ''
    Object.values(errorMsg as VxeTableDefines.ValidatorErrorMapParams).forEach((e) => {
      e.forEach(({ rowIndex, column }) => {
        const title = `第${rowIndex + 1}行的${column.title}信息`
        message += `请完善${title}\n`
      })
    })
    callback(new Error(message))
    return
  }
  callback()
}
const formRules = computed<
  FormRules<Omit<FormModel, 'skcInfoDetailResp'>> & { skcInfoDetailResp: any }
>(() => ({
  brand: [{ required: true, message: '请选择品牌', trigger: 'change' }],
  lastBottomType: [{ required: true, message: '请选择楦底类型', trigger: 'change' }],
  specialTestingRequirements: [
    { required: true, message: '请输入特殊检测需求/说明', trigger: 'blur' }
  ],
  productCategory: [{ required: true, message: '请选择产品类目', trigger: 'change' }],
  launchSeason: [{ required: true, message: '请选择开发季节', trigger: 'change' }],
  productStyle: [{ required: true, message: '请选择产品风格', trigger: 'change' }],
  productStyleSubCode: [{ required: true, message: '请选择产品子风格', trigger: 'change' }],
  targetAudience: [{ required: true, message: '请选择适用人群', trigger: 'change' }],
  styleStructure: [{ required: true, message: '请选择款式结构', trigger: 'change' }],
  applicableSeason: [{ required: true, message: '请选择适用季节', trigger: 'change' }],
  expectedLaunchDate: [{ required: true, message: '请选择期望上架日期', trigger: 'change' }],
  lastsStandard: [{ required: true, message: '请选择楦型标准', trigger: 'change' }],
  dtc: [{ required: true, message: '请选择是否S2C', trigger: 'change' }],
  toeStandard: [{ required: true, message: '请选择楦头类别', trigger: 'change' }],
  designPersonId: [{ required: true, message: '请选择产品企划', trigger: 'change' }],
  designerId: [{ required: true, message: '请选择产品设计师', trigger: 'change' }],
  sizeRangeId: [{ required: true, message: '请选择尺码段', trigger: 'change' }],
  designUrl: [{ required: true, message: '请上传设计图', trigger: 'blur' }],
  closureMethod: [{ required: true, message: '请选择穿脱方式', trigger: 'change' }],
  safetyFeatures: [{ required: true, message: '请选择是否含金属鞋头', trigger: 'change' }],
  developmentDirection: [{ required: true, message: '请选择产品系列', trigger: 'change' }],
  developmentDirectionSubCode: [{ required: true, message: '请选择产品子系列', trigger: 'change' }],
  ankleCoverage: [{ required: true, message: '请选择是否过踝/过膝', trigger: 'change' }],
  retailPrice: [{ required: true, message: '请输入吊牌价', trigger: 'blur' }],
  shoeSoleTechnique: [{ required: true, message: '请选择鞋底工艺', trigger: 'change' }],
  productPositioning: [{ required: true, message: '请输入产品目标定级', trigger: 'blur' }],
  developmentChannel: [{ required: true, message: '请选择开发渠道', trigger: 'change' }],
  developmentModel: [{ required: true, message: '请选择开发模式', trigger: 'change' }],
  waterproofLevel: [{ required: true, message: '请选择防水级别', trigger: 'change' }],
  combatTeam: [{ required: true, message: '请选择CT归属', trigger: 'change' }],
  costRangeMin: [{ required: true, message: '请输入成本区间', trigger: 'change' }],
  costRangeMax: [{ required: true, message: '请输入成本区间', trigger: 'change' }],
  // estimatedPrice: [{ required: true, message: '请输入预估售价', trigger: 'change' }],
  assignedFactory: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择大货供应商',
      trigger: 'change'
    }
  ],
  meetingResult: [
    {
      required: true,
      message: '请选择选品会结果',
      trigger: 'change'
    }
  ],
  selectionMeetingReviewOpinion: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请输入选品会评审意见',
      trigger: 'blur'
    }
  ],
  foldable: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择是否折叠',
      trigger: 'change'
    }
  ],
  chooseChannel: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择选品渠道',
      trigger: 'change'
    }
  ],
  mainChannelMark: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择主渠道标识',
      trigger: 'change'
    }
  ],
  selectedSize: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择选中尺码',
      trigger: 'change'
    }
  ],
  copyright: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择是否需要侵权排查',
      trigger: 'change'
    }
  ],
  noCpyrightReason: [
    {
      required:
        formData.value.meetingResult === ProductSelectResultEnum.SELECTED &&
        formData.value.copyright === YesNoEnum.N,
      message: '请输入无需排查原因',
      trigger: 'blur'
    }
  ],
  skcInfoDetailResp: [
    {
      asyncValidator: skcInfoValidator
    }
  ],
  productLaunchSeason: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择商品上市季节',
      trigger: 'change'
    }
  ],
  stylePartnerCode: [
    {
      required: [
        BrandEnum.FABKIDS,
        BrandEnum.JUSTFAB,
        BrandEnum.SHOEDAZZLE,
        BrandEnum.JUSTFAB_3RD_PARTY
      ].includes(formData.value.brand!),
      message: '请输入Style Partner Code',
      trigger: 'change'
    }
  ]
}))

const useConst = () => {
  const store = useBasicLibraryDictStore()
  const toeStandardList = computed(() => store.lastTypeList)
  const productLastStandardList = computed(() => store.productLastStandardList)
  const filteredToeStandardList = ref<DictValueAPI.Data[]>(toeStandardList.value)
  const filteredLastStandardList = ref<DictValueAPI.Data[]>(productLastStandardList.value)

  const styleStructureList = computed(() => store.commonStyleStructureList)
  const { userInfo } = storeToRefs(useUserInfoStore())
  const productCategoryList = ref<ProductCategoryListAPI.Data[]>([])
  const fetchProductCategoryList = async () => {
    const [error, result] = await getProductCategoryList()
    if (error === null && result?.datas) {
      productCategoryList.value = result?.datas
    }
  }
  fetchProductCategoryList()

  return {
    userInfo,
    styleStructureList,
    productCategoryList,
    toeStandardList,
    productLastStandardList,
    filteredToeStandardList,
    filteredLastStandardList
  }
}

const {
  userInfo,
  styleStructureList,
  productCategoryList,
  toeStandardList,
  productLastStandardList,
  filteredToeStandardList,
  filteredLastStandardList
} = useConst()

const colorList = computed<string[]>(() => {
  return formData.value.colorUrlList?.map((e) => e.signatureUrl!) || []
})

const styleStructureOptions = ref<DictValueAPI.Data[] | null>(null)
const productCategoryRef = ref<InstanceType<typeof ElCascader>>()

const getStyleStructureOptions = () => {
  const selected = productCategoryRef.value?.getCheckedNodes(true)
  if (selected?.length) {
    const selectorKeyList = (selected[0].data?.selectorAdditionList as string[]) || []
    styleStructureOptions.value = (styleStructureList.value || [])?.filter((e) =>
      selectorKeyList?.includes(e.dictValue!.toString())
    )
  } else {
    styleStructureOptions.value = null
  }
}

const handleProductCategoryChange = () => {
  formData.value.lastsStandard = ''
  formData.value.toeStandard = ''
  formData.value.styleStructure = ''
  getStyleStructureOptions()
}

watch(() => formData.value.productCategory, getStyleStructureOptions)

watchEffect(() => {
  if (!formData.value.designPersonId) {
    formData.value.designPersonId = userInfo.value.id
  }
})

const sizeList = ref<(SizeListAPI.Data & { disabled?: boolean })[]>([])

const filteredSizeList = computed(() => {
  const { lastsStandard, toeStandard } = unref(formData)

  return sizeList.value
    .map((e) => ({
      ...e,
      disabled:
        e.disabled ||
        (lastsStandard === HeadStandardEnum.U && e.lastsStandard !== HeadStandardEnum.U) ||
        (lastsStandard === HeadStandardEnum.E && e.lastsStandard !== HeadStandardEnum.E) ||
        (toeStandard === HeadCategoryEnum.W && e.toeStandard !== HeadCategoryEnum.W)
    }))
    .sort((a, b) => +a.disabled! - +b.disabled!)
})

const sizeLabel = computed(() => {
  const sizeRangeId: number[] | undefined = formData.value.sizeRangeId
  const sizeGroup = groupBy(
    filteredSizeList.value
      .filter((e) => sizeRangeId?.includes(e.id!))
      .map((e) => ({
        name: e.name,
        sizeValue: e.sizeValueList?.map((size) => size.sizeValue).join(', ')
      })),
    'name'
  )
  return Object.keys(sizeGroup)
    .map((key) => {
      const sizeValueList = sizeGroup[key].map((item) => item.sizeValue).join(', ')
      return `${sizeValueList}【${key}】`
    })
    .join('、')
})

const getAllSizeList = async () => {
  const { datas } = await getSizeList()
  if (datas) {
    sizeList.value = datas
      .filter((e) => e.sizeType === SizeCodeTypeEnums.PRODUCT)
      .map((e) => ({
        ...e,
        disabled: e.status !== StatusEnum.START
      }))
  }
}

const { sizeOptions, fetchSizeList } = useSizeOptions(SizeCodeTypeEnums.PRODUCT)
fetchSizeList()

const sizeValueList = computed(() => {
  if (!formData.value.sizeRangeId?.length) {
    return []
  }
  const sizeObj = sizeOptions.value.filter((e) => formData.value.sizeRangeId?.includes(e.id!))

  return sizeObj
    .filter((e) => e.children?.length)
    .map((e) =>
      e.children!.map((size) => ({
        label: `${size.label}【${e.label}】`,
        value: size.id,
        disabled: size.disabled
      }))
    )
    .flat()
})
const handleSizeRangeIdChange = async () => {
  const sizeRangeId: number[] | undefined = formData.value.sizeRangeId

  if (!Array.isArray(sizeRangeId) || sizeRangeId.length < 1) {
    formData.value.standardSizeId = []
    formData.value.selectedSize = []
    return
  }
  // 根据rangeId.standardValue对应的sizeId
  formData.value.standardSizeId = sizeRangeId
    .map((rangeId) => {
      const findRange = sizeList.value.find((item) => item.id === rangeId)
      if (!findRange) {
        return
      }
      const fineSize = findRange.sizeValueList?.find(
        (item) => item.sizeValue === findRange.standardValue
      )
      if (!fineSize) {
        return
      }
      return fineSize.id
    })
    .filter(Boolean) as number[]
  formData.value.selectedSize = sizeValueList.value.filter((e) => !e.disabled).map((e) => e.value!)
}

watchOnce(
  () => formData.value.sizeRangeId,
  async () => {
    const selectedSize = formData.value.selectedSize
    if (!sizeList.value.length) {
      await getAllSizeList()
      await handleSizeRangeIdChange()
      formData.value.selectedSize = selectedSize
    } else {
      await handleSizeRangeIdChange()
      formData.value.selectedSize = selectedSize
    }
  }
)

const tableRef = ref<InstanceType<typeof AddMaterial>>()

const factoryList = ref<FactoryListAPI.Data[]>([])
const fetchFactoryList = async () => {
  const [error, result] = await getFactoryList()
  if (!error && result) {
    factoryList.value = result.datas || []
  }
}
// const handleFactoryChange = (val: number) => {
//   const factoryObj = factoryList.value.find((e) => e.vendorId === val)
//   if (factoryObj) {
//     formData.value.region = factoryObj.areaCode
//   }
// }

fetchFactoryList()

const submitLoading = ref(false)
const couldSubmit = computed(() => {
  const developModeList: string[] = [
    DevelopModeEnum.FULL_SEASON,
    DevelopModeEnum.QUICK_REACTION,
    DevelopModeEnum.SCROLL
  ]
  return !developModeList.includes(formData.value.developmentModel!)
})

// 产品编码的生成需要加一个逻辑判断， 开款阶段已经完成后， 涉及到编码生成的字段，不能让用户修改
const codeDisabled = computed(() => {
  return !!formData.value.taskTimeRecord?.[ProductTaskNodeEnum.OPENING_STAGE]
})

const submitParams = computed(() => {
  const { assignedFactory, ...rest } = formData.value
  // 大货供应商
  const _assignedFactory = Array.isArray(assignedFactory)
    ? (assignedFactory as unknown as string[]).join()
    : ''
  return {
    ...rest,
    assignedFactory: _assignedFactory,
    skcInfoDetailResp: rest.skcInfoDetailResp?.map((e) => ({
      ...e,
      coriumMaterialCode: getMaterialCode(e.coriumMaterialCode),
      otherMaterialCode: getMaterialCode(e.otherMaterialCode)
    }))
  }
})

const handleSubmit = async () => {
  if (!couldSubmit.value) {
    ElMessage.warning('开发模式为正季、快反、滚动，必须按正常开发流程创建档案')
    return
  }
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) return
  submitLoading.value = true
  const [error, result] = await createProductInfo({
    ...submitParams.value,
    quicklyAble: 1,
    taskNode: ProductTaskNodeEnum.PRODUCT_CONCLUSION,
    operateBottom: SaveTypeEnum.SUBMIT,
    autoSendWms: false
  })
  if (!error && result) {
    const [err, res] = await getProductInfo(result.datas)
    if (!err && res) {
      formData.value = formatParams(res.datas)
    }
  }
  if (formData.value.meetingResult === ProductSelectResultEnum.SELECTED) {
    const res = await sendToWMS(formData.value, sizeValueList.value)
    if (!res) {
      submitLoading.value = false
      return false
    }
    formData.value.wmsCategoryId = res[0].wmsCategoryId
    if (formData.value.stylePartnerCode) {
      const data = await stylePartnerCode(formData.value.id!, formData.value)
      if (!data) {
        submitLoading.value = false
        return false
      }
      return true
    }
  }
  const [err, res] = await createProductInfo({
    ...submitParams.value,
    quicklyAble: 1,
    taskNode: ProductTaskNodeEnum.PRODUCT_CONCLUSION,
    operateBottom: SaveTypeEnum.SUBMIT,
    autoSendWms: true
  })
  submitLoading.value = false
  if (!err && res) {
    ElMessage.success(res.msg || '操作成功')
    useClosePage('ProductLibrary')
  } else {
    formData.value.skuList = []
  }
}

const handleStore = async () => {
  if (!couldSubmit.value) {
    ElMessage.warning('开发模式为正季、快反、滚动，必须按正常开发流程创建档案')
    return
  }
  submitLoading.value = true
  const [error, result] = await saveProductStore({
    ...submitParams.value,
    quicklyAble: 1,
    taskNode: ProductTaskNodeEnum.PRODUCT_CONCLUSION,
    operateBottom: SaveTypeEnum.STAGING
  })
  submitLoading.value = false
  if (!error && result) {
    ElMessage.success(result.msg || '操作成功')
    useClosePage('ProductLibrary')
  } else {
    formData.value.skuList = []
  }
}

const handleResetStore = async () => {
  submitLoading.value = true
  const [error, result] = await resetProductStore()
  submitLoading.value = false
  if (!error && result) {
    formData.value = {
      sizeRangeId: []
    }
    ElMessage.success(result.msg || '操作成功')
  }
}

const handleSendToWMS = async () => {
  const isEffect = formData.value.dataStatus === ProductDataStatusEnum.EFFECT
  const isOrderPlaced = formData.value.orderPlaced === CommonYesNoEnums.Yes
  if (isOrderPlaced) {
    ElMessage.warning('产品已下单，需走线上审批流程才能更新数据到WMS系统')
    return
  }
  if (!isEffect) {
    ElMessage.warning('产品的状态还未生效或已作废')
    return
  }
  const result = await sendToWMS(formData.value, sizeValueList.value)
  if (result) {
    formData.value.wmsCategoryId = result[0].wmsCategoryId
    submitLoading.value = true
    if (formData.value.stylePartnerCode) {
      const data = await stylePartnerCode(formData.value.id!, formData.value)
      if (!data) {
        submitLoading.value = false
        return false
      }
      return true
    }
    await createProductInfo({
      ...submitParams.value,
      sendWms: YesNoEnum.Y as string,
      quicklyAble: 1,
      taskNode: ProductTaskNodeEnum.PRODUCT_CONCLUSION,
      operateBottom: SaveTypeEnum.SUBMIT
    })
    submitLoading.value = false
    useClosePage('ProductLibrary')
  }
}

let loading: ReturnType<typeof ElLoading.service> | null = null

const id = computed(() => {
  return route.query.id
})

const formatParams = (datas: CreateProductInfoAPI.Params) => {
  const { assignedFactory, ...rest } = datas
  const _assignedFactory = assignedFactory
    ? (assignedFactory as unknown as string).split(',').filter(Boolean)
    : []
  return {
    assignedFactory: _assignedFactory,
    ...rest
  }
}

const fetchProductInfo = async () => {
  loading = ElLoading.service({
    fullscreen: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  const [error, result] = isCreate.value
    ? await getProductStore()
    : await getProductInfo(+id.value!)
  loading?.close()
  loading = null
  if (error === null && result?.datas) {
    formData.value = formatParams({
      ...formData.value,
      ...result.datas,
      standardSizeId: [],
      skcInfoDetailResp: result.datas.skcInfoDetailResp?.map((e) => {
        const materialInfoList = new Array(4).fill(null).map(() => ({}))
        return {
          ...e,
          coriumMaterialCode: (e.coriumMaterialCode as number[])?.length
            ? e.coriumMaterialCode?.[0]
            : undefined,
          otherMaterialCode: (e.otherMaterialCode as number[])?.length
            ? e.otherMaterialCode?.[0]
            : undefined,
          materialInfoList: e.materialInfoList || materialInfoList
        }
      })
    })
    setTimeout(() => {
      formData.value.standardSizeId = result.datas.standardSizeId
    })
  }
}

const meetingResultDisabled = ref(false)
const stylePartnerCodeDisabled = ref(false)
const assignedFactoryDisabled = ref(false)
watchOnce(
  () => formData.value,
  () => {
    meetingResultDisabled.value = formData.value.meetingResult === ProductSelectResultEnum.SELECTED
    stylePartnerCodeDisabled.value = !!(
      formData.value.sendWms === (YesNoEnum.Y as string) && formData.value.stylePartnerCode
    )
    assignedFactoryDisabled.value = formData.value.sendFactory === +YesNoEnum.Y
  }
)

/**
 * 1、如果产品类目为:鞋类-->运动鞋，默认为不防水，根据需要可修改
 * 2、鞋类-->非运动鞋，则防水级，默认为不防水，根据需要可修改
 * 3、鞋类-->户外鞋，默认为必填写，用户自己填写数据
 * 4、鞋类-->工作&安全鞋，默认为必填写，用户自己填写数据
 */
let waterproofFirst = true
watch(
  () => formData.value.productCategory,
  () => {
    if (!isCreate.value && waterproofFirst) {
      waterproofFirst = false
      return
    }
    const selected = productCategoryRef.value?.getCheckedNodes(true)
    if (selected?.length) {
      const list: number[] = [ProductCategoryEnum.SPORTS, ProductCategoryEnum.NON_SPORTS]
      const label = selected[0].pathValues[1] as number
      if (list.includes(label)) {
        formData.value.waterproofLevel = PFLevelEnum.NONE
      }
      if (label !== ProductCategoryEnum.SPORTS) {
        formData.value.specialTestingRequirements = '无'
      } else {
        formData.value.specialTestingRequirements = ''
      }
      const firstLevel = selected[0].pathValues[0] as number
      if (firstLevel === ProductCategoryEnum.SHOES) {
        filteredToeStandardList.value = toeStandardList.value.filter((e) =>
          shoesToeCategoryList.includes(e.dictValue as string)
        )
        filteredLastStandardList.value = productLastStandardList.value.filter((e) =>
          shoesLastStandardList.includes(e.dictValue as string)
        )
      } else {
        filteredToeStandardList.value = toeStandardList.value.filter(
          (e) => e.dictValue === HeadCategoryEnum.A
        )
        filteredLastStandardList.value = productLastStandardList.value.filter(
          (e) => e.dictValue === HeadStandardEnum.A
        )
      }
    } else {
      filteredToeStandardList.value = toeStandardList.value
      filteredLastStandardList.value = productLastStandardList.value
    }
  }
)

onActivated(() => {
  if (!loading) {
    fetchProductInfo()
  }
})

const modelInfoDialogVisible = ref(false)
const handleOpenModelInfoDialog = () => {
  modelInfoDialogVisible.value = true
}
const handlePickModel = (val: ModelListPageAPI.Row) => {
  formData.value.modelId = val.id
  formData.value.modelIdItemName = val.code
  formData.value.toeShape = val.head
  formData.value.heelType = val.pattern
  formData.value.heelHeight = val.high
  formData.value.heelHeightMm = val.heelHigh
}

const vocStyleThumbnail = ref<BaseFileDTO[]>([])

const handleVocOriginalStyleNumberChange = async () => {
  const productNumber = formData.value.vocOriginalStyleNumber
  if (productNumber) {
    const [error, result] = await getModelInfoByProductNumber(productNumber)
    if (error === null && result?.datas) {
      formData.value.modelId = result.datas.id
      formData.value.modelIdItemName = result.datas.code
      formData.value.toeShape = result.datas.head
      formData.value.heelType = result.datas.pattern
      formData.value.heelHeight = result.datas.high
      formData.value.heelHeightMm = result.datas.heelHigh
    }
  }
}
const clearSizeRange = (name: string) => {
  const sizeRangeName: number[] | undefined = formData.value?.[name]
  if (!Array.isArray(sizeRangeName) || sizeRangeName.length < 1) {
    formData.value.sizeRangeId = []
    formData.value.standardSizeId = []
  }
}
let isFirst = true
watch(
  () => formData.value.vocOriginalStyleNumber,
  async (val) => {
    if (val) {
      const [error, result] = await getStyleImage(val)
      if (error === null && result?.datas) {
        vocStyleThumbnail.value = [result.datas]
      }
    }
    if (isFirst && !isCreate.value) {
      isFirst = false
      return
    }
    await handleVocOriginalStyleNumberChange()
  }
)
const handleClose = () => {
  useClosePage('ProductLibrary')
}
const handleRenderOption = (options) => {
  if (options && options.length == 1) formData.value.productStyleSubCode = options[0].value
}
const handleRenderOptionDirection = (options) => {
  if (options && options.length == 1) formData.value.developmentDirectionSubCode = options[0].value
}
Promise.all([fetchProductInfo(), getAllSizeList()])
</script>

<template>
  <ModelInfoDialog v-model="modelInfoDialogVisible" @submit="handlePickModel" />
  <ContentWrap>
    <ElCard v-if="!isCreate" body-class="!py-2" class="sticky top-0 mb-2">
      <ElScrollbar>
        <div class="flex flex-nowrap items-center w-full min-h-36 h-36 whitespace-nowrap">
          <ElImage
            :preview-src-list="
              formData.thumbnail ? [formData.thumbnail]?.map((e) => e.signatureUrl!) : []
            "
            :src="formData.thumbnail?.signatureUrl"
            class="w-32 h-32 min-w-32 min-h-32 shadow-md shadow-gray-500/50"
            fit="contain"
            loading="lazy"
            hide-on-click-modal
            preview-teleported
          />
          <div class="h-1/2 ml-4 mr-2 flex flex-col justify-between">
            <div class="text-center">{{ formData.productNumber }}</div>
            <div>
              <ElTag class="mr-2" effect="dark" round size="large">
                {{ formData.launchSeasonItemName }}
              </ElTag>
              <ElTag v-if="formData.designerIdItemName" effect="plain" round size="large">
                {{ formData.designerIdItemName }}
              </ElTag>
            </div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
            direction="vertical"
          />
          <div
            class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-xs leading-[24px] text-gray-500"
          >
            <div>当前阶段</div>
            <div>{{ formData.developStageItemName }} {{ formData.stageCompDesc }}</div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
            direction="vertical"
          />
          <div class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500">
            <div>供应商</div>
            <div>
              {{ formData.assignedFactoryItemName }}
              {{ `${formData.regionItemName ? '，' + formData.regionItemName : ''}` }}
            </div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
            direction="vertical"
          />
          <div
            class="flex-1 flex-grow-[2] flex-shrink-[2] h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500"
          >
            <div>产品颜色</div>
            <div class="flex flex-nowrap">
              <div v-for="item in colorList" :key="item" class="mr-2 w-6 h-6">
                <ElImage
                  :preview-src-list="colorList"
                  :src="item"
                  class="w-6 h-6"
                  fit="contain"
                  loading="lazy"
                  hide-on-click-modal
                  preview-teleported
                />
              </div>
            </div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
            direction="vertical"
          />
          <div class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500">
            <div>风险判定</div>
            <div
              :class="[
                formData.infringementRisk === InfringementRiskLevelEnums.High ||
                formData.infringementRisk === InfringementRiskLevelEnums.Medium
                  ? 'text-red-500'
                  : null
              ]"
              class="text-xs leading-[24px] text-gray-500"
            >
              <span> {{ formData.infringementRiskItemName }}</span>
              <span> {{ formData.riskCountry }}</span>
            </div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
            direction="vertical"
          />
          <div class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500">
            <div>当前状态</div>
            <div>{{ formData.dataStatusItemName }}</div>
          </div>
          <div class="flex flex-col ml-2">
            <ElButton size="large" @click="router.push({ name: 'ProductLibrary' })">
              返回产品清单
            </ElButton>
            <div class="h-1"></div>
            <ElButton
              v-if="formData.sendWms !== YesNoEnum.Y"
              class="!ml-0"
              size="large"
              @click="handleSendToWMS"
            >
              下发数据到WMS
            </ElButton>
          </div>
        </div>
      </ElScrollbar>
    </ElCard>
    <ElCollapse v-model="actives">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :scroll-into-view-options="{ block: 'start', behavior: 'smooth' }"
        label-width="auto"
        scroll-to-error
      >
        <ElCollapseItem name="1" title="产品档案信息">
          <div class="grid grid-cols-2 items-start gap-x-4">
            <ElFormItem label="设计图" prop="designUrl">
              <OssUpload
                v-model="formData.designUrl"
                :limit="20"
                :size-limit="100 * 1024 * 1024"
                accept="image/*"
                drag
                multiple
              />
            </ElFormItem>
            <ElFormItem label="参考款式图" prop="referenceUrl">
              <OssUpload
                v-model="formData.referenceUrl"
                :limit="20"
                :size-limit="1024 * 1024 * 100"
                accept="image/*"
                drag
                multiple
              />
            </ElFormItem>

            <div></div>
            <ElFormItem label="Style（WMS）" prop="styleWms">
              <ElInput
                v-model="formData.styleWms"
                class="w-48"
                clearable
                disabled
                maxlength="100"
                show-word-limit
              />
            </ElFormItem>
            <ElFormItem label="品牌" prop="brand">
              <SelectPlus
                v-model="formData.brand"
                :disabled="codeDisabled"
                api-key="baseBrand"
                cache
                clearable
                filterable
                placeholder="请选择品牌"
              />
            </ElFormItem>
            <ElFormItem label="产品类目" prop="productCategory">
              <ElCascader
                ref="productCategoryRef"
                v-model="formData.productCategory"
                :disabled="codeDisabled"
                :options="productCategoryList"
                :props="{
                  emitPath: false,
                  value: 'selectorKey',
                  label: 'selectorEnValue',
                  children: 'childList'
                }"
                clearable
                filterable
                placeholder="请选择产品类目"
                @change="handleProductCategoryChange"
              />
            </ElFormItem>
            <ElFormItem label="开发季节" prop="launchSeason">
              <SelectPlus
                v-model="formData.launchSeason"
                :disabled="codeDisabled"
                api-key="COMMON_MARKET_SEASON"
                cache
                clearable
                filterable
                placeholder="请选择开发季节"
              />
            </ElFormItem>
            <ElFormItem label="产品风格" prop="productStyle">
              <SelectPlus
                v-model="formData.productStyle"
                api-key="PRODUCT_STYLE"
                cache
                clearable
                filterable
                placeholder="请选择产品风格"
                @change="
                  () => {
                    formData.productStyleSubCode = ''
                  }
                "
              />
            </ElFormItem>
            <ElFormItem label="适用人群" prop="targetAudience">
              <SelectPlus
                v-model="formData.targetAudience"
                :disabled="codeDisabled"
                api-key="PRODUCT_PEOPLE"
                cache
                clearable
                filterable
                placeholder="请选择适用人群"
              />
            </ElFormItem>
            <ElFormItem label="产品子风格" prop="productStyleSubCode">
              <ApiSelect
                v-model="formData.productStyleSubCode"
                :childComponent="ElSelect"
                :api-config="{
                  api: getDictLeafOpen,
                  config: {
                    label: 'nameCn',
                    value: 'leafCode'
                  }
                }"
                :key="formData.productStyle"
                :params="{ typeCode: 'PRODUCT_STYLE', itemValue: formData.productStyle }"
                cache
                clearable
                filterable
                placeholder="请选择产品子风格"
                @render-option="handleRenderOption"
              />
            </ElFormItem>
            <ElFormItem label="款式结构" prop="styleStructure">
              <ElSelect
                v-model="formData.styleStructure"
                clearable
                filterable
                placeholder="请选择款式结构"
              >
                <ElOption
                  v-for="item in styleStructureOptions || styleStructureList"
                  :key="item.dictValue"
                  :label="item.dictCnName"
                  :value="item.dictValue!"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="适用季节" prop="applicableSeason">
              <SelectPlus
                v-model="formData.applicableSeason"
                api-key="PRODUCT_SUITABLE_SEASON"
                cache
                clearable
                filterable
                placeholder="请选择适用季节"
              />
            </ElFormItem>
            <ElFormItem label="期望上架日期" prop="expectedLaunchDate">
              <ElDatePicker
                v-model="formData.expectedLaunchDate"
                class="min-w-48"
                clearable
                placeholder="请选择期望上架日期"
                value-format="YYYY-MM-DD"
              />
            </ElFormItem>
            <ElFormItem label="楦型标准" prop="lastsStandard">
              <ElSelect
                v-model="formData.lastsStandard"
                :disabled="codeDisabled"
                clearable
                filterable
                placeholder="请选择楦型标准"
                @change="clearSizeRange('lastsStandard')"
              >
                <ElOption
                  v-for="item in filteredLastStandardList"
                  :key="item.dictValue"
                  :label="item.dictCnName"
                  :value="item.dictValue!"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="是否S2C" prop="dtc">
              <SelectPlus v-model="formData.dtc" api-key="COMMON_YES_NO" cache radio />
            </ElFormItem>
            <ElFormItem label="楦头类别" prop="toeStandard">
              <ElSelect
                v-model="formData.toeStandard"
                :disabled="codeDisabled"
                clearable
                filterable
                placeholder="请选择楦头类别"
                @change="clearSizeRange('toeStandard')"
              >
                <ElOption
                  v-for="item in filteredToeStandardList"
                  :key="item.dictValue"
                  :label="item.dictCnName"
                  :value="item.dictValue!"
                />
              </ElSelect>
            </ElFormItem>
            <div class="!flex flex-nowrap">
              <ElFormItem label="成本区间($)" prop="costRangeMin">
                <ElInputNumber
                  v-model="formData.costRangeMin"
                  :controls="false"
                  :max="formData.costRangeMax || Infinity"
                  :min="0"
                  :precision="2"
                  :value-on-clear="null"
                />
              </ElFormItem>
              <ElFormItem
                class="const-range-max-form-item"
                label="~"
                label-width="32"
                prop="costRangeMax"
              >
                <ElInputNumber
                  v-model="formData.costRangeMax"
                  :controls="false"
                  :min="formData.costRangeMin || 0"
                  :precision="2"
                  :value-on-clear="null"
                />
              </ElFormItem>
            </div>
            <ElFormItem label="产品企划" prop="designPersonId">
              <CascadeSelector
                v-model="formData.designPersonId"
                :props="{ emitPath: false }"
                api-key="allUsers"
                cache
                placeholder="请选择产品企划"
              />
            </ElFormItem>
            <ElFormItem label="预估售价($)" prop="estimatedPrice">
              <ElInputNumber
                v-model="formData.estimatedPrice"
                :controls="false"
                :min="0"
                :precision="2"
                :value-on-clear="null"
              />
            </ElFormItem>
            <ElFormItem label="产品设计师" prop="designerId">
              <CascadeSelector
                v-model="formData.designerId"
                :props="{ emitPath: false }"
                api-key="allUsers"
                cache
                placeholder="请选择产品设计师"
              />
            </ElFormItem>
            <ElFormItem label="开发渠道" prop="developmentChannel">
              <SelectPlus
                v-model="formData.developmentChannel"
                api-key="PRODUCT_DEV_CHANNELS"
                cache
                clearable
                filterable
                placeholder="请选择开发渠道"
              />
            </ElFormItem>
            <ElFormItem label="关联型体" prop="modelId">
              <ElInput
                v-model="formData.modelIdItemName"
                class="w-48"
                placeholder="请选择"
                readonly
              >
                <template #prefix>
                  <Icon
                    :size="26"
                    class="cursor-pointer"
                    color="#409EFF"
                    icon="mdi:search"
                    @click="handleOpenModelInfoDialog"
                  />
                </template>
              </ElInput>
            </ElFormItem>
            <ElFormItem label="款式定位" prop="stylePositioning">
              <SelectPlus
                v-model="formData.stylePositioning"
                api-key="PRODUCT_STYLE_POSITIONING"
                cache
                clearable
                filterable
                placeholder="请选择款式定位"
              />
            </ElFormItem>
            <ElFormItem label="开发模式" prop="developmentModel">
              <SelectPlus
                v-model="formData.developmentModel"
                api-key="PRODUCT_DEV_MODEL"
                cache
                clearable
                filterable
                placeholder="请选择开发模式"
              />
            </ElFormItem>
            <ElFormItem label="尺码段" prop="sizeRangeId">
              <ElSelect
                v-model="formData.sizeRangeId"
                clearable
                filterable
                multiple
                placeholder="请选择尺码段"
                @change="handleSizeRangeIdChange"
              >
                <ElOption
                  v-for="item in filteredSizeList"
                  :key="item.id"
                  :disabled="item.disabled"
                  :label="item.name!"
                  :value="item.id!"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="儿童人群" prop="childrenCrowd">
              <SelectPlus
                v-model="formData.childrenCrowd"
                api-key="PRODUCT_CHILDREN"
                cache
                clearable
                filterable
                placeholder="请选择儿童人群"
              />
            </ElFormItem>
            <ElFormItem label="标准码">
              <ElSelect
                v-model="formData.standardSizeId"
                disabled
                filterable
                multiple
                placeholder="请选择标准码"
              >
                <ElOption
                  v-for="item in sizeValueList"
                  :key="item.value"
                  :disabled="item.disabled"
                  :label="item.label"
                  :value="item.value!"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="儿童年龄段" prop="childrenAvgGroup">
              <SelectPlus
                v-model="formData.childrenAvgGroup"
                api-key="PRODUCT_CHILDREN_AGE"
                cache
                clearable
                filterable
                multiple
                placeholder="请选择儿童年龄段"
              />
            </ElFormItem>
            <ElFormItem label="尺码值">
              <ElInput
                :autosize="{
                  minRows: 2,
                  maxRows: 4
                }"
                :model-value="sizeLabel"
                disabled
                resize="none"
                type="textarea"
              />
            </ElFormItem>
            <ElFormItem label="基本计量单位" prop="basicUnitOfMeasure">
              <SelectPlus
                v-model="formData.basicUnitOfMeasure"
                api-key="COMMON_MEASURE_UNIT"
                cache
                clearable
                filterable
                placeholder="请选择基本计量单位"
              />
            </ElFormItem>
            <ElFormItem label="穿脱方式" prop="closureMethod">
              <SelectPlus
                v-model="formData.closureMethod"
                api-key="PRODUCT_CLOSE_TYPE"
                cache
                clearable
                filterable
                placeholder="请选择穿脱方式"
              />
            </ElFormItem>
            <ElFormItem label="防水级别" prop="waterproofLevel">
              <SelectPlus
                v-model="formData.waterproofLevel"
                api-key="PRODUCT_WATERPROOF_LEVEL"
                cache
                clearable
                filterable
                placeholder="请选择防水级别"
              />
            </ElFormItem>
            <ElFormItem label="Style Partner Code" prop="stylePartnerCode">
              <ElInput
                v-model="formData.stylePartnerCode"
                :disabled="stylePartnerCodeDisabled"
                class="w-48"
                clearable
                maxlength="100"
                show-word-limit
              />
            </ElFormItem>
            <ElFormItem label="是否过踝/过膝" prop="ankleCoverage">
              <SelectPlus v-model="formData.ankleCoverage" api-key="COVER KNEE OR ANKLE" cache />
            </ElFormItem>
            <ElFormItem label="备注" prop="remarks">
              <ElInput
                v-model="formData.remarks"
                :autosize="{ minRows: 4, maxRows: 4 }"
                class="w-48"
                maxlength="500"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
            <ElFormItem label="是否含金属鞋头" prop="safetyFeatures">
              <SelectPlus
                v-model="formData.safetyFeatures"
                api-key="COMMON_YES_NO"
                cache
                clearable
                filterable
                placeholder="请选择是否含金属鞋头"
              />
            </ElFormItem>
          </div>
        </ElCollapseItem>
        <ElCollapseItem name="2" title="款式设计信息">
          <div class="grid grid-cols-2 items-start gap-x-4">
            <ElFormItem label="头型" prop="toeShape">
              <SelectPlus
                v-model="formData.toeShape"
                api-key="HEEL_HEAD"
                cache
                clearable
                filterable
                placeholder="请选择头型"
              />
            </ElFormItem>
            <ElFormItem label="场景" prop="scene">
              <SelectPlus
                v-model="formData.scene"
                api-key="PRODUCT_SCENES"
                cache
                clearable
                filterable
                placeholder="请选择场景"
              />
            </ElFormItem>
            <ElFormItem label="跟型" prop="heelType">
              <SelectPlus
                v-model="formData.heelType"
                api-key="HEEL_PATTERN"
                cache
                clearable
                filterable
                placeholder="请选择跟型"
              />
            </ElFormItem>
            <ElFormItem label="流行元素" prop="trendyElements">
              <SelectPlus
                v-model="formData.trendyElements"
                api-key="PRODUCT_POPULAT_ELEMENTS"
                cache
                clearable
                filterable
                placeholder="请选择流行元素"
              />
            </ElFormItem>
            <ElFormItem label="跟高" prop="heelHeight">
              <SelectPlus
                v-model="formData.heelHeight"
                api-key="HEEL_HIGH"
                cache
                clearable
                filterable
                placeholder="请选择跟高"
              />
            </ElFormItem>
            <ElFormItem label="楦底类型" prop="lastBottomType">
              <SelectPlus
                v-model="formData.lastBottomType"
                api-key="PRODUCT_LAST_HEEL_TYPE"
                cache
                clearable
                filterable
                placeholder="请选择楦底类型"
              />
            </ElFormItem>
            <ElFormItem label="特殊检测需求/说明" prop="specialTestingRequirements">
              <ElInput
                v-model="formData.specialTestingRequirements"
                :autosize="{ minRows: 4, maxRows: 4 }"
                maxlength="500"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
            <ElFormItem
              class="material-form-item col-span-2"
              label-width="0"
              prop="skcInfoDetailResp"
            >
              <AddMaterial ref="tableRef" :form-data="formData" />
            </ElFormItem>
          </div>
        </ElCollapseItem>
        <ElCollapseItem name="3" title="选品会信息">
          <div class="grid grid-cols-2 items-start gap-x-4">
            <ElFormItem label="大货供应商" prop="assignedFactory">
              <ElSelect
                v-model="formData.assignedFactory"
                :disabled="assignedFactoryDisabled"
                clearable
                collapse-tags
                collapse-tags-tooltip
                filterable
                multiple
                placeholder="请选择大货供应商"
              >
                <ElOption
                  v-for="item in factoryList"
                  :key="item.vendorCode"
                  :disabled="item.useStatus !== +YesNoEnum.Y"
                  :label="item.vendorName"
                  :value="item.vendorId!"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="商品上市季节" prop="productLaunchSeason">
              <SelectPlus
                v-model="formData.productLaunchSeason"
                api-key="COMMON_MARKET_SEASON"
                cache
                clearable
                filterable
                placeholder="请选择商品上市季"
              />
            </ElFormItem>
            <ElFormItem label="选品会评审意见" prop="selectionMeetingReviewOpinion">
              <ElInput
                v-model="formData.selectionMeetingReviewOpinion"
                :autosize="{ minRows: 4, maxRows: 4 }"
                class="w-48"
                maxlength="500"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
            <ElFormItem label="选品会结果" prop="meetingResult">
              <SelectPlus
                v-model="formData.meetingResult"
                :disabled="meetingResultDisabled"
                api-key="COMMON_PASS_FAIL"
                clearable
                filterable
                placeholder="请选择选品会结果"
              />
            </ElFormItem>
            <ElFormItem label="尺码段" prop="sizeRangeId">
              <ElSelect
                v-model="formData.sizeRangeId"
                clearable
                disabled
                filterable
                multiple
                placeholder="请选择尺码段"
                @change="handleSizeRangeIdChange"
              >
                <ElOption
                  v-for="item in filteredSizeList"
                  :key="item.id"
                  :disabled="item.disabled"
                  :label="item.name!"
                  :value="item.id!"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="是否折叠" prop="foldable">
              <SelectPlus v-model="formData.foldable" api-key="COMMON_YES_NO" cache radio />
            </ElFormItem>
            <ElFormItem label="选中尺码" prop="selectedSize">
              <ElSelect
                v-model="formData.selectedSize"
                clearable
                collapse-tags
                collapse-tags-tooltip
                filterable
                multiple
                placeholder="请选择选中尺码"
              >
                <ElOption
                  v-for="item in sizeValueList"
                  :key="item.value"
                  :disabled="item.disabled"
                  :label="item.label"
                  :value="item.value!"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="鞋底工艺" prop="shoeSoleTechnique">
              <SelectPlus
                v-model="formData.shoeSoleTechnique"
                api-key="PRODUCT_SOLE_CRAFT"
                cache
                filterable
                placeholder="请选择鞋底工艺"
              />
            </ElFormItem>
            <ElFormItem label="选品渠道" prop="chooseChannel">
              <SelectPlus
                v-model="formData.chooseChannel"
                api-key="PRODUCT_DEV_CHANNELS"
                cache
                clearable
                filterable
                multiple
                placeholder="请选择选品渠道"
              />
            </ElFormItem>
            <ElFormItem label="是否需要侵权排查" prop="copyright">
              <SelectPlus v-model="formData.copyright" api-key="COMMON_YES_NO" cache radio />
            </ElFormItem>
            <ElFormItem label="主渠道标识" prop="mainChannelMark">
              <SelectPlus
                v-model="formData.mainChannelMark"
                api-key="PRODUCT_CHANNEL_IDENTIFICATION"
                cache
                clearable
                filterable
                placeholder="请选择主渠道标识"
              />
            </ElFormItem>
            <ElFormItem label="无需排查原因" prop="noCpyrightReason">
              <ElInput
                v-model="formData.noCpyrightReason"
                :autosize="{ minRows: 4, maxRows: 4 }"
                class="w-48"
                maxlength="500"
                placeholder="请输入无需排查原因"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
            <ElFormItem label="预估上架日期" prop="predictLaunchDate">
              <ElDatePicker
                v-model="formData.predictLaunchDate"
                class="min-w-48"
                clearable
                placeholder="请选择预估上架日期"
                value-format="YYYY-MM-DD"
              />
            </ElFormItem>
            <ElFormItem label="CT归属" prop="combatTeam">
              <SelectPlus
                v-model="formData.combatTeam"
                api-key="COMBAT_TEAM"
                clearable
                filterable
                placeholder="请选择CT归属"
              />
            </ElFormItem>
          </div>
        </ElCollapseItem>
        <ElCollapseItem name="4" title="产品运营信息">
          <div class="grid grid-cols-2 items-start gap-x-4">
            <ElFormItem label="吊牌价($)" prop="retailPrice">
              <ElInputNumber
                v-model="formData.retailPrice"
                :controls="false"
                :min="0"
                :precision="2"
                :value-on-clear="null"
              />
            </ElFormItem>
            <ElFormItem label="产品目标定级" prop="productPositioning">
              <SelectPlus
                v-model="formData.productPositioning"
                api-key="PRODUCT_POSITION"
                cache
                clearable
                filterable
                placeholder="请选择产品目标定级"
              />
            </ElFormItem>
            <ElFormItem label="PRODUCT NAME" prop="shoeName">
              <ElInput
                v-model="formData.shoeName"
                class="w-48"
                clearable
                maxlength="100"
                placeholder="请输入"
                show-word-limit
              />
            </ElFormItem>
            <ElFormItem label="产品系列" prop="developmentDirection">
              <SelectPlus
                v-model="formData.developmentDirection"
                api-key="PRODUCT_SERIES"
                cache
                clearable
                filterable
                placeholder="请选择产品系列"
                @change="
                  () => {
                    formData.developmentDirectionSubCode = ''
                  }
                "
              />
            </ElFormItem>
            <ElFormItem label="产品子系列" prop="developmentDirectionSubCode">
              <ApiSelect
                v-model="formData.developmentDirectionSubCode"
                :childComponent="ElSelect"
                :api-config="{
                  api: getDictLeafOpen,
                  config: {
                    label: 'nameCn',
                    value: 'leafCode'
                  }
                }"
                :key="formData.developmentDirection"
                :params="{ typeCode: 'PRODUCT_SERIES', itemValue: formData.developmentDirection }"
                cache
                clearable
                filterable
                placeholder="请选择产品子系列"
                @render-option="handleRenderOptionDirection"
              />
            </ElFormItem>
          </div>
        </ElCollapseItem>
      </ElForm>
    </ElCollapse>
    <div class="w-full text-center mt-4">
      <ElButton @click="handleClose">返回</ElButton>
      <ElButton v-if="isCreate" :loading="submitLoading" @click="handleStore"> 暂存 </ElButton>
      <ElButton v-if="isCreate" :loading="submitLoading" @click="handleResetStore"> 重置 </ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit"> 确定</ElButton>
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
:deep(.const-range-max-form-item .el-form-item__label::before) {
  content: none !important;
}

:deep(.material-form-item.is-error) {
  .el-input__wrapper,
  .el-select__wrapper {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
  }
}
</style>
