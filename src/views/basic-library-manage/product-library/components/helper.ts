import {
  Emit,
  FormModel,
  getRelatedProduct,
  getRelatedSell,
  Props,
  RelatedProductApi
} from '../api/productInfo'
import { FormInstance } from 'element-plus'
import { DefineProps } from 'vue'
import { RelatedObjectMapping } from '@/views/basic-library-manage/product-library/const'
import { RelatedInfoApi } from '@/views/basic-library-manage/product-library/api/productInfo'
import router from '@/router'

export function getMaterialCode(materialCode?: number[] | number) {
  if (Array.isArray(materialCode)) {
    return materialCode
  }
  if (materialCode) {
    return [materialCode]
  }
  return []
}

export const useProductInfo = (
  props: DefineProps<
    Props,
    | 'mounted'
    | 'isCreate'
    | 'isEdit'
    | 'isCopy'
    | 'isView'
    | 'selectionDisabled'
    | 'confirmDisabled'
    | 'isOldData'
  >,
  emit: Emit
) => {
  const formData = computed<FormModel>({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  })

  const formRef = ref<FormInstance>()

  const getCommonParams = () => {
    const { assignedFactory } = formData.value
    return {
      ...formData.value,
      // 大货供应商
      assignedFactory: Array.isArray(assignedFactory) ? assignedFactory.join() : '',
      skcInfoDetailResp: formData.value.skcInfoDetailResp?.map((e) => ({
        ...e,
        coriumMaterialCode: getMaterialCode(e?.coriumMaterialCode),
        otherMaterialCode: getMaterialCode(e?.otherMaterialCode)
      }))
    }
  }

  return {
    props,
    emit,
    formData,
    formRef,
    getCommonParams
  }
}
// 自定义单元格渲染
export const RelateConfig = {
  [RelatedObjectMapping.relatedProduct]: {
    title: '相关衍生产品',
    columns: [
      {
        type: 'seq',
        title: '序号',
        width: 60
      },
      {
        field: 'thumbnail',
        title: '缩略图',
        width: 150,
        cellRender: {
          name: 'Image'
        }
      },
      {
        field: 'productNumber',
        title: '产品编码',
        width: 150,
        cellRender: {
          name: 'Link',
          props: {
            clickFn: ({ row }: { row: RelatedProductApi.Row }) => {
              console.log(router, row)
              router.push({
                name: 'ViewProduct',
                query: {
                  id: row.id
                }
              })
            }
          }
        }
      },
      {
        field: 'styleWms',
        title: 'Style（WMS）',
        width: 150
      },
      {
        field: 'styleNumber',
        title: 'Style编号',
        width: 150,
        cellRender: {
          name: 'Link',
          props: {
            clickFn: ({ row }: { row: RelatedProductApi.Row }) => {
              router.push({
                name: 'ViewProduct',
                query: {
                  id: row.id
                }
              })
            }
          }
        }
      },
      {
        field: 'derivedType',
        title: '产品衍生类型',
        width: 100
      },
      {
        field: 'vocOriginalStyleNumber',
        title: '母Style',
        minWidth: 100,
        cellRender: {
          name: 'Link',
          props: {
            clickFn: ({ row }: { row: RelatedProductApi.Row }) => {
              router.push({
                name: 'ViewProduct',
                query: {
                  id: row.vocStyleNumberId
                }
              })
            }
          }
        }
      },
      {
        field: 'brandItemName',
        title: '品牌',
        width: 150
      },
      {
        field: 'productCategoryItemName',
        title: '产品类目',
        showOverflow: false,
        width: 100
      },
      {
        field: 'productLaunchSeason',
        title: '商品上市季节',
        width: 100
      },
      {
        field: 'productPositioning',
        title: '产品目标定级',
        width: 100
      },
      {
        field: 'productActualPosition',
        title: '产品实际定级',
        width: 100
      },
      {
        field: 'productType',
        title: '产品类型',
        width: 80
      },
      {
        field: 'dataStatus',
        title: '状态',
        width: 80
      }
    ],
    needPage: true,
    API: getRelatedProduct
  },
  [RelatedObjectMapping.relatedMolds]: {
    title: '相关模具',
    columns: [
      {
        field: 'code',
        title: '模具编码',
        width: 150,
        cellRender: {
          name: 'Link',
          props: {
            clickFn: ({ row }: { row: RelatedProductApi.Row }) => {
              router.push({
                name: 'ViewMold',
                query: {
                  id: row.id
                }
              })
            }
          }
        }
      },
      {
        field: 'thumbnail',
        title: '缩略图',
        width: 80,
        cellRender: {
          name: 'Image'
        }
      },
      {
        field: 'typeItemName',
        title: '模具类型',
        width: 80
      },
      {
        field: 'brandItemName',
        title: '品牌',
        width: 80
      },
      {
        field: 'headItemName',
        title: '楦头类型',
        width: 80
      },
      {
        field: 'targetAudienceItemName',
        title: '适用人群',
        width: 80
      },
      {
        field: 'designer',
        title: '设计师',
        width: 80
      },
      {
        field: 'regionItemName',
        title: '区域',
        width: 80
      },
      {
        field: 'productNumber',
        title: '关联产品编号',
        width: 120
      },
      {
        field: 'size',
        title: '模具尺码段',
        width: 120
      },
      {
        field: 'sizeValue',
        title: '样品码',
        width: 100
      },
      {
        field: 'supplier',
        title: '模具供应商',
        width: 100
      },
      {
        field: 'moldOpeningTime',
        title: '开模时间',
        width: 80
      },
      {
        field: 'moldFinishTime',
        title: '模具完成时间',
        width: 100
      },
      {
        field: 'quantity',
        title: '模具数量（套）',
        width: 80
      },
      {
        field: 'moldApplyCode',
        title: '关联的申请单编号',
        minWidth: 180,
        cellRender: {
          name: 'moreLink',
          props: {
            clickFn: (item: string, row: RelatedProductApi.Row) => {
              router.push({
                name: 'ViewMoldProcess',
                query: {
                  id: row['linkMoldApplyIdMap']?.[item]
                }
              })
            }
          }
        }
      },
      {
        field: 'status',
        title: '状态',
        width: 80
      }
    ]
  },
  [RelatedObjectMapping.relatedSellingPoints]: {
    title: '相关卖点',
    columns: [
      {
        field: 'sellPointNumber',
        title: '卖点编号',
        width: 150,
        cellRender: {
          name: 'Link',
          props: {
            clickFn: ({ row }: { row: RelatedInfoApi.RelatedModels }) => {
              router.push({
                name: 'ViewFeature',
                query: {
                  id: row.id,
                  productNumber: row.productNumber
                }
              })
            }
          }
        }
      },
      {
        field: 'validSkcCount',
        title: '款色数',
        width: 100
      },
      {
        field: 'productStyleItemName',
        title: '产品风格',
        minWidth: 150
      },
      {
        field: 'sellPointPriority',
        title: '卖点优先级',
        minWidth: 150
      },
      {
        field: 'sceneDressingItemName',
        title: '场景/穿搭',
        minWidth: 150
      },
      {
        field: 'functionCharacteristicsItemName',
        title: '功能/特性',
        minWidth: 150
      },
      {
        field: 'trendElementItemName',
        title: '趋势/元素',
        minWidth: 150
      },
      {
        field: 'comfortLevelItemName',
        title: '舒适度',
        minWidth: 150
      },
      {
        field: 'sellPointOtherItemName',
        title: '其他卖点',
        minWidth: 150
      },
      {
        field: 'sellPointStatusItemName',
        title: '状态',
        width: 80
      }
    ],
    needPage: true,
    API: getRelatedSell
  },
  [RelatedObjectMapping.relatedLastTypes]: {
    title: '相关楦型',
    columns: [
      {
        field: 'code',
        title: '楦型编码',
        width: 150,
        cellRender: {
          name: 'Link',
          props: {
            clickFn: ({ row }: { row: RelatedInfoApi.RelatedModels }) => {
              router.push({
                name: 'ViewLast',
                query: {
                  id: row.id
                }
              })
            }
          }
        }
      },
      {
        field: 'thumbnail',
        title: '缩略图',
        width: 80,
        cellRender: {
          name: 'Image'
        }
      },
      {
        field: 'sexItemName',
        title: '性别',
        width: 80
      },
      {
        field: 'typeItemName',
        title: '楦头类别',
        minWidth: 150
      },
      {
        field: 'marketItemName',
        title: '楦头市场',
        minWidth: 150
      },
      {
        field: 'designer',
        title: '设计师',
        minWidth: 120
      },
      {
        field: 'regionItemName',
        title: '区域',
        minWidth: 120
      },
      {
        field: 'productNumber',
        title: '关联的Style',
        minWidth: 150
      },
      {
        field: 'supplierLastNumber',
        title: '供应商楦编码',
        minWidth: 150
      },
      {
        field: 'shoeFactory',
        title: '鞋厂名称',
        minWidth: 120
      },
      {
        field: 'status',
        title: '状态',
        width: 80
      }
    ]
  },
  [RelatedObjectMapping.relatedProductStyles]: {
    title: '相关型体',
    columns: [
      {
        field: 'code',
        title: '型体编码',
        width: 150,
        cellRender: {
          name: 'Link',
          props: {
            clickFn: ({ row }: { row: RelatedInfoApi.RelatedModels }) => {
              router.push({
                name: 'ViewModel',
                query: {
                  id: row.id
                }
              })
            }
          }
        }
      },
      {
        field: 'thumbnail',
        title: '缩略图',
        width: 80,
        cellRender: {
          name: 'Image'
        }
      },
      {
        field: 'brandItemName',
        title: '品牌',
        minWidth: 120
      },
      {
        field: 'developmentYearItemName',
        title: '开发年份',
        minWidth: 100
      },
      {
        field: 'regionItemName',
        title: '区域',
        width: 80
      },
      {
        field: 'targetAudience',
        title: '适用人群',
        minWidth: 100
      },
      {
        field: 'productNumber',
        title: '关联的产品',
        minWidth: 160
      },
      {
        field: 'status',
        title: '状态',
        width: 80
      }
    ]
  }
}
