<script lang="ts" setup>
import { ProductSelectResultEnum, ProductTaskNodeEnum } from '../const'
import { ElMessage, FormRules } from 'element-plus'
import { ProductDataStatusEnum, YesNoEnum } from '../../const'
import {
  createProductInfo,
  CreateProductInfoAPI,
  Emit,
  FormModel,
  Props,
  SaveTypeEnum
} from '@/views/basic-library-manage/product-library/api/productInfo'
import { getMaterialCode, useProductInfo } from './helper'
import { getDictLeafOpen } from '@/api/common'

defineOptions({
  name: 'OperationInfo'
})

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const { formData, formRef, getCommonParams } = useProductInfo(props, emit)

const formRules: FormRules<Omit<FormModel, 'skcInfoDetailResp'>> = {
  retailPrice: [{ required: true, message: '请输入吊牌价', trigger: 'blur' }],
  productPositioning: [{ required: true, message: '请选择产品目标定级', trigger: 'change' }],
  developmentDirection: [{ required: true, message: '请选择产品系列', trigger: 'change' }],
  developmentDirectionSubCode: [{ required: true, message: '请选择产品子系列', trigger: 'change' }]
}

const disabled = computed(() => {
  // 历史数据isOldData 无法修改运营信息form
  if (props.isOldData) {
    return true
  }
  return props.isView || props.initPane !== props.currentPane
})

const router = useRouter()

const handleEditProduct = () => {
  router.push({
    name: 'EditProduct',
    query: {
      id: formData.value.id,
      pane: props.currentPane
    }
  })
  // location.href = router.resolve({
  //   name: 'EditProduct',
  //   query: {
  //     id: formData.value.id,
  //     pane: props.currentPane
  //   }
  // }).href
}

const selectedColor = computed(() => {
  const skcArr = formData.value.skcInfoDetailResp
  if (!Array.isArray(skcArr)) {
    return []
  }

  return skcArr
    .filter((e) => e.selectable === ProductSelectResultEnum.SELECTED)
    .map((row) => {
      //  产品运营除了oldData之外,均可修改植绒信息,与选品会的植绒信息禁用逻辑不一致
      let disabled = false
      if (props.isOldData) {
        // 如果产品是老数据, 每条skc的newAddColor为1, 可以修改,0 禁用
        disabled = row.newAddColor === +YesNoEnum.N
      }
      return {
        ...row,
        $disable: disabled
      }
    })
})

// todo orderPlaced为1的情况下,产品运营可以修改植绒信息(按条),选品会不可以

const submit = async (type: SaveTypeEnum) => {
  const valid = await formRef.value?.validate()
  if (!valid) {
    return false
  }

  const originalSkcList = formData.value.skcInfoDetailResp || []
  const editSkcList = selectedColor.value
  const [error, result] = await createProductInfo({
    ...getCommonParams(),
    // getCommonParams返回的是skcInfoDetailResp的初始值
    skcInfoDetailResp: originalSkcList.map((e) => {
      const findRow = editSkcList.find((item) => item.id === e.id)
      // 保留ProductSelectResultEnum.UNSELECTED
      if (!findRow) {
        return {
          ...e,
          coriumMaterialCode: getMaterialCode(e.coriumMaterialCode),
          otherMaterialCode: getMaterialCode(e.otherMaterialCode)
        }
      }
      return {
        ...e,
        coriumMaterialCode: getMaterialCode(findRow.coriumMaterialCode),
        otherMaterialCode: getMaterialCode(findRow.otherMaterialCode),
        velvetApplied: findRow.velvetApplied,
        velvetRequirements: findRow.velvetRequirements,
        velvetRemark: findRow.velvetRemark
      }
    }),
    operateBottom: type,
    taskNode: ProductTaskNodeEnum.PRODUCT_OPERATION_IMPROVEMENT
  })
  if (error === null && result) {
    ElMessage.success(result.msg)
    useClosePage('ProductLibrary', {
      state: {
        productNumber: formData.value.productNumber
      }
    })
    return true
  }
  return false
}

/**
 * orderPlaced '1' 展示植绒信息
 * pdm发起的确认样评审qms通过 且 scm采购系统已下单
 */
const displayVelvetInfo = computed(() => {
  return formData.value.confirmResult || formData.value.orderPlaced === YesNoEnum.Y
})

/**
 * 老数据 oldData 1 运营信息 禁用修改
 */
const operationInfoDisabled = computed(() => {
  if (props.isView) return true
  return formData.value.oldData === +YesNoEnum.Y
})
const handleRenderOptionDirection = (options) => {
  if (options && options.length == 1) formData.value.developmentDirectionSubCode = options[0].value
}
defineExpose({
  submit
})
</script>

<template>
  <ElForm ref="formRef" :disabled="isView" :model="formData" :rules="formRules" label-width="130">
    <ElRow :gutter="20">
      <ElCol :span="12">
        <ElFormItem label="吊牌价($)" prop="retailPrice">
          <ElInputNumber
            v-model="formData.retailPrice"
            :controls="false"
            :disabled="operationInfoDisabled"
            :min="0"
            :precision="2"
            :value-on-clear="null"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="产品目标定级" prop="productPositioning">
          <SelectPlus
            v-model="formData.productPositioning"
            :disabled="operationInfoDisabled"
            api-key="PRODUCT_POSITION"
            cache
            clearable
            filterable
            placeholder="请选择产品目标定级"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="强制性警示声明" prop="mandatoryWarningStatement">
          <SelectPlus
            v-model="formData.mandatoryWarningStatement"
            :disabled="operationInfoDisabled"
            api-key="COMMON_YES_NO"
            cache
            radio
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="产品系列" prop="developmentDirection">
          <SelectPlus
            v-model="formData.developmentDirection"
            :disabled="operationInfoDisabled"
            api-key="PRODUCT_SERIES"
            cache
            clearable
            filterable
            placeholder="请选择产品系列"
            @change="
              () => {
                formData.developmentDirectionSubCode = ''
              }
            "
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="产品子系列" prop="developmentDirectionSubCode">
          <ApiSelect
            v-model="formData.developmentDirectionSubCode"
            :childComponent="ElSelect"
            :disabled="operationInfoDisabled"
            :api-config="{
              api: getDictLeafOpen,
              config: {
                label: 'nameCn',
                value: 'value'
              }
            }"
            :key="formData.developmentDirection"
            :params="{ typeCode: 'PRODUCT_SERIES', itemValue: formData.developmentDirection }"
            cache
            clearable
            filterable
            placeholder="请选择产品子系列"
            @render-option="handleRenderOptionDirection"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="使用电池/附带电池" prop="usesBattery">
          <SelectPlus
            v-model="formData.usesBattery"
            :disabled="operationInfoDisabled"
            api-key="COMMON_YES_NO"
            cache
            radio
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="产品实际定级" prop="productActualPosition">
          <SelectPlus
            v-model="formData.productActualPosition"
            api-key="PRODUCT_POSITION"
            cache
            clearable
            disabled
            filterable
            placeholder="请选择产品实际定级"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="PRODUCT NAME" prop="shoeName">
          <ElInput
            v-model="formData.shoeName"
            :disabled="operationInfoDisabled"
            clearable
            maxlength="100"
            placeholder="请输入"
            show-word-limit
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="产品类型" prop="productType">
          <ElInput v-model="formData.productType" disabled placeholder="请输入" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="是否上市" prop="isProductLaunched">
          <SelectPlus
            v-model="formData.isProductLaunched"
            :disabled="operationInfoDisabled"
            api-key="COMMON_YES_NO"
            cache
            radio
          />
        </ElFormItem>
      </ElCol>
      <ElCol v-if="disabled" :span="12">
        <ElFormItem label="操作人">
          <ElInput
            :model-value="
              formData.taskTimeRecord?.[ProductTaskNodeEnum.PRODUCT_OPERATION_IMPROVEMENT]
                ?.createByIdItemName
            "
            disabled
          />
        </ElFormItem>
      </ElCol>
      <ElCol v-if="disabled" :span="12">
        <ElFormItem label="操作时间">
          <ElDatePicker
            :model-value="
              formData.taskTimeRecord?.[ProductTaskNodeEnum.PRODUCT_OPERATION_IMPROVEMENT]
                ?.createTime
            "
            disabled
            value-format="YYYY-MM-DD"
          />
        </ElFormItem>
      </ElCol>
    </ElRow>
    <ElCollapse :model-value="['1']">
      <ElCollapseItem v-if="displayVelvetInfo" name="1">
        <template #title>
          <div class="font-bold text-base">植绒信息判定</div>
        </template>
        <VxeTable ref="tableRef" :data="selectedColor" :max-height="500" class="w-full">
          <VxeColumn title="序号" type="seq" width="60" />
          <VxeColumn field="skcCode" title="SKC编号" width="180" />
          <VxeColumn field="colorCodeItemName" title="选中颜色" />
          <VxeColumn field="mainFabricItemName" title="主要面料" />
          <VxeColumn field="velvetApplied" title="是否植绒">
            <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
              <SelectPlus
                v-model="row.velvetApplied"
                :disabled="row.$disable"
                api-key="FLOCKING_YES_NO"
              />
            </template>
          </VxeColumn>
          <VxeColumn field="velvetRequirements" title="植绒要求">
            <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
              <ElInput
                v-model="row.velvetRequirements"
                :disabled="row.$disable"
                clearable
                maxlength="100"
                placeholder="请输入"
                show-word-limit
              />
            </template>
          </VxeColumn>
          <VxeColumn field="velvetTariff" title="植绒/植皮关税率" />
          <VxeColumn field="unVelvetTariff" title="不植关税率" />
          <VxeColumn
            :class-name="
              ({ row }) => {
                return row.customsVelvetSuggestion !== row.velvetAppliedItemName
                  ? 'text-red-500 font-bold'
                  : ''
              }
            "
            field="customsVelvetSuggestion"
            title="关务植绒/植皮建议"
          />
          <VxeColumn field="customsVelvetRemark" title="关务植绒/植皮备注" />
          <VxeColumn field="dataStatusItemName" title="状态" />
          <VxeColumn field="velvetRemark" title="备注">
            <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
              <ElInput
                v-model="row.velvetRemark"
                :disabled="row.$disable"
                clearable
                maxlength="500"
                placeholder="请输入"
                show-word-limit
              />
            </template>
          </VxeColumn>
        </VxeTable>
      </ElCollapseItem>
    </ElCollapse>
  </ElForm>
  <Teleport v-if="mounted" to=".product-info-footer">
    <span v-show="currentPane === 'OperationInfo' && (isView || currentPane !== initPane)">
      <ElButton
        v-if="
          formData.dataStatus?.toLowerCase() === ProductDataStatusEnum.EFFECT ||
          formData.dataStatus?.toLowerCase() === ProductDataStatusEnum.DRAFT
        "
        v-hasPermi="[
          'editProduct:editProductOperationInfo',
          'viewProduct:editProductOperationInfo'
        ]"
        type="primary"
        @click="handleEditProduct"
      >
        修改产品运营信息
      </ElButton>
    </span>
  </Teleport>
</template>

<style lang="less" scoped></style>
