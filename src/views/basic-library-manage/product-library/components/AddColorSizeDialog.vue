<script lang="ts" setup>
import { ref } from 'vue'
import {
  CreateProductInfoAPI,
  FormModel
} from '@/views/basic-library-manage/product-library/api/productInfo'
import { VxeTable, VxeTableDefines, VxeTableInstance, VxeTablePropTypes } from 'vxe-table'
import { ElMessage, FormInstance, FormItemRule, FormRules } from 'element-plus'
import { MaterialCategoryEnum, StatusEnum } from '@/views/basic-library-manage/const'
import {
  MaterialTypeEnum,
  ProductSelectResultEnum
} from '@/views/basic-library-manage/product-library/const'
import ColorInfoDialog from '@/views/basic-library-manage/components/ColorInfoDialog.vue'
import { ColorListPageAPI } from '@/views/basic-library-manage/color-library/api/color-list'
import { Icon } from '@/components/Icon'
import { cloneDeep, groupBy, isNil } from 'lodash-es'
import { SizeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import { sizeList as getSizeList } from '@/components/Business/SelectPlus/src/api'
import { SizeValueAPI } from '@/views/basic-library-manage/size-library/api/sizeInfo'
import { addColorSize, getSkcDetailInfoByProductId } from '../api/addColorSizeDialog'
import { VXE_TABLE_ROW_KEY } from '@/constants'
import { useMaterial } from '@/views/basic-library-manage/sample-manage/components/hooks'
import { getMaterialCode } from '@/views/basic-library-manage/product-library/components/helper'
import { SizeCodeTypeEnums } from '@/enums'

const props = defineProps<{
  modelValue: boolean
  formData?: FormModel
  type?: MaterialTypeEnum
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const handleClose = () => {
  visible.value = false
  editField.value = 1
  tempSizeTableData.value = []
  tempSizeRangeId.value = []
}

const isSelected = computed(() => props.type === MaterialTypeEnum.SELECTED)

const skcInfoValidator: FormItemRule['asyncValidator'] = async (_rule, _value, callback) => {
  if (!formData?.value?.skcInfoDetailResp?.length) {
    callback(new Error('请至少添加一条SKC数据'))
    return
  }
  const errorMsg = await tableRef.value?.validate?.(true).catch(() => false)
  if (errorMsg) {
    const { row, column, rule } = Object.values(errorMsg)[0][0]
    tableRef.value?.scrollToRow(row, column)
    tableRef.value?.setSelectCell(row, column)
    callback(new Error(rule.message))
    return
  }
  callback()
}

const { formData } = toRefs(props)
const queryLoading = ref(false)
const formRef = ref<FormInstance>()
const formRules = ref<FormRules<{ skcInfoDetailResp: any }>>({
  skcInfoDetailResp: [
    {
      asyncValidator: skcInfoValidator
    }
  ]
})
const tableRef = ref<VxeTableInstance>()
const tableData = computed(() => {
  if (isSelected.value) {
    return (
      formData?.value?.skcInfoDetailResp?.filter(
        (e) => e.selectable === ProductSelectResultEnum.SELECTED
      ) || []
    )
  }
  return formData?.value?.skcInfoDetailResp || []
})

watch(
  () => visible.value,
  async (val) => {
    if (val) {
      if (!formData?.value) {
        return
      }
      queryLoading.value = true
      const [error, result] = await getSkcDetailInfoByProductId(formData!.value.id!)
      queryLoading.value = false
      if (!error && result?.datas) {
        formData!.value.skcInfoDetailResp = result.datas
          .filter((e) => e.selectable === ProductSelectResultEnum.SELECTED)
          .map((e) => {
            const materialInfoList = new Array(4).fill(null).map(() => ({}))
            return {
              ...e,
              coriumMaterialCode: (e.coriumMaterialCode as number[])?.length
                ? e.coriumMaterialCode?.[0]
                : undefined,
              otherMaterialCode: (e.otherMaterialCode as number[])?.length
                ? e.otherMaterialCode?.[0]
                : undefined,
              materialInfoList: e.materialInfoList || materialInfoList
            }
          })
        tableRef.value?.loadData(formData?.value?.skcInfoDetailResp)
      }
    }
  }
)

const sumValidator: VxeTableDefines.ValidatorRule['validator'] = ({
  row
}: {
  row: CreateProductInfoAPI.SKCInfo
}) => {
  const total =
    +(row.percentCorium || 0) +
    +(row.percentPu || 0) +
    +(row.percentOther || 0) +
    +(row.percentTextile || 0)
  return new Promise((resolve, reject) => {
    if (row.id) {
      resolve()
      return
    }
    if (total !== 100) {
      reject(new Error('橡胶/塑料、真皮、纺织物、其他占比应为100%'))
      return
    }
    resolve()
  })
}

const getValidatorByField = (
  field: string,
  title: string
): VxeTableDefines.ValidatorRule['validator'] => {
  return ({ row }: { row: CreateProductInfoAPI.SKCInfo }) => {
    return new Promise((resolve, reject) => {
      if (row.id) {
        resolve()
        return
      }
      if (isNil(row[field]) || (Array.isArray(row[field]) && !row[field].length)) {
        reject(new Error(`请选择${title}`))
        return
      }
      resolve()
    })
  }
}

const tableRules = ref<VxeTablePropTypes.EditRules>({
  colorCode: [
    {
      validator: getValidatorByField('colorCode', '产品配色')
    }
  ],
  mainFabric: [
    {
      validator: getValidatorByField('mainFabric', '主面料')
    }
  ],
  colorType: [
    {
      validator: getValidatorByField('colorType', '配色类型')
    }
  ],
  sampleUrl: [
    {
      validator: getValidatorByField('sampleUrl', '样品图')
    }
  ],
  velvetApplied: [
    {
      validator: getValidatorByField('velvetApplied', '是否植绒')
    }
  ],
  'materialInfoList.0.material': [
    {
      validator: ({ row }: { row: CreateProductInfoAPI.SKCInfo }) => {
        return new Promise((resolve, reject) => {
          if (row.id) {
            resolve()
            return
          }
          if (!row.materialInfoList?.[0]?.material) {
            reject(new Error(`请选择面料1`))
            return
          }
          resolve()
        })
      }
    }
  ],
  liningMaterialCode: [
    {
      validator: getValidatorByField('liningMaterialCode', '里材料')
    }
  ],
  paddingMaterialSurfaceCode: [
    {
      validator: getValidatorByField('paddingMaterialSurfaceCode', '垫材料')
    }
  ],
  soleMaterialOutsoleCode: [
    {
      validator: getValidatorByField('soleMaterialOutsoleCode', '底材料')
    }
  ],
  percentPu: [
    {
      validator: getValidatorByField('percentPu', '橡胶/塑料占比')
    },
    {
      validator: sumValidator
    }
  ],
  percentCorium: [
    {
      validator: getValidatorByField('percentCorium', '真皮占比')
    },
    {
      validator: sumValidator
    },
    {
      validator({ row }: { row: CreateProductInfoAPI.SKCInfo }) {
        return new Promise((resolve, reject) => {
          if (row.id) {
            resolve()
            return
          }
          if (row.percentCorium !== 0) {
            if (Array.isArray(row.coriumMaterialCode) && !row.coriumMaterialCode.length) {
              reject(new Error('请选择真皮材料'))
              return
            }
            if (!row.coriumMaterialCode) {
              reject(new Error('请选择真皮材料'))
            }
          }
          resolve()
        })
      }
    }
  ],
  percentTextile: [
    {
      validator: getValidatorByField('percentTextile', '纺织物占比')
    },
    {
      validator: sumValidator
    }
  ],
  percentOther: [
    {
      validator: getValidatorByField('percentOther', '其他材料占比')
    },
    {
      validator: sumValidator
    },
    {
      validator({ row }: { row: CreateProductInfoAPI.SKCInfo }) {
        return new Promise((resolve, reject) => {
          if (row.id) {
            resolve()
            return
          }
          if (row.percentOther !== 0) {
            if (Array.isArray(row.otherMaterialCode) && !row.otherMaterialCode.length) {
              reject(new Error('请选择其他材料'))
              return
            }
            if (!row.otherMaterialCode) {
              reject(new Error('请选择其他材料'))
            }
          }
          resolve()
        })
      }
    }
  ]
})

const editField = ref<string | number>(1)
const material = ref<string | string[]>('')
const rate = ref<number>()

watch(
  () => editField.value,
  (val) => {
    rate.value = undefined
    if (typeof val === 'number' || val === 'coriumMaterialCode' || val === 'otherMaterialCode') {
      material.value = ''
    } else {
      material.value = []
    }
  }
)

const {
  materialCascaderProps,
  materialCategoryList,
  commonMaterialCategoryList,
  liningMaterialCategoryList,
  outsoleMaterialCategoryList,
  insoleMaterialCategoryList
} = useMaterial()

// 皮料
const leatherMaterialCategoryList = computed(() => {
  const commonList: string[] = [MaterialCategoryEnum.LEATHER]
  return materialCategoryList.value.filter((e) => commonList.includes(e.selectorEnValue!))
})

// 其他材料
const otherMaterialCategoryList = computed(() => {
  const commonList: string[] = [MaterialCategoryEnum.OTHERS, MaterialCategoryEnum.ACC]
  return materialCategoryList.value.filter((e) => commonList.includes(e.selectorEnValue!))
})

const setMaterialOptions = computed(() => {
  if (typeof editField.value === 'number') {
    return commonMaterialCategoryList.value
  }
  if (editField.value === 'paddingMaterialSurfaceCode') {
    return insoleMaterialCategoryList.value
  }
  if (editField.value === 'liningMaterialCode') {
    return liningMaterialCategoryList.value
  }
  if (editField.value === 'soleMaterialOutsoleCode') {
    return outsoleMaterialCategoryList.value
  }
  if (editField.value === 'coriumMaterialCode') {
    return leatherMaterialCategoryList.value
  }
  if (editField.value === 'otherMaterialCode') {
    return otherMaterialCategoryList.value
  }
  return materialCategoryList.value
})

const setMaterialCascaderProps = computed(() => {
  if (
    typeof editField.value === 'number' ||
    editField.value === 'coriumMaterialCode' ||
    editField.value === 'otherMaterialCode'
  ) {
    return materialCascaderProps
  }
  return {
    ...materialCascaderProps,
    multiple: true
  }
})

const handleSetMaterial = () => {
  const selected = tableRef.value?.getCheckboxRecords() || []
  if (!selected.length) {
    ElMessage.warning('请先勾选数据')
    return
  }
  if (isNil(material.value) && isNil(rate.value)) {
    ElMessage.warning('请先选择材质或输入占比')
    return
  }
  if (typeof editField.value === 'number') {
    // 面料1、2、3、4
    selected.forEach((e) => {
      e.materialInfoList[(editField.value as number) - 1].material = material.value
    })
    tableRef.value?.loadData(tableData.value)
    tableRef.value?.setMergeCells(mergeCells.value)
    return
  }
  if (editField.value === 'coriumMaterialCode' || editField.value === 'otherMaterialCode') {
    // 真皮、其他材料
    selected.forEach((e) => {
      if (editField.value === 'coriumMaterialCode') {
        e.percentCorium = rate.value
      }
      if (editField.value === 'otherMaterialCode') {
        e.percentOther = rate.value
      }
      e[editField.value] = material.value
    })
    tableRef.value?.loadData(formData?.value?.skcInfoDetailResp || [])
    tableRef.value?.setMergeCells(mergeCells.value)
    return
  }
  if (editField.value === 'percentPu' || editField.value === 'percentTextile') {
    // 纺织物、橡胶/塑料
    selected.forEach((e) => {
      e[editField.value] = rate.value
    })
    tableRef.value?.loadData(tableData.value)
    tableRef.value?.setMergeCells(mergeCells.value)
    return
  }
  selected.forEach((e) => {
    e[editField.value] = material.value
  })
  tableRef.value?.loadData(tableData.value)
  tableRef.value?.setMergeCells(mergeCells.value)
}

const handleResetMaterial = () => {
  const selected = tableRef.value?.getCheckboxRecords() || []
  if (!selected.length) {
    ElMessage.warning('请先勾选数据')
    return
  }
  if (typeof editField.value === 'number') {
    // 面料1、2、3、4
    selected.forEach((e) => {
      e.materialInfoList[(editField.value as number) - 1].material = ''
    })
    return
  }
  if (editField.value === 'coriumMaterialCode' || editField.value === 'otherMaterialCode') {
    // 真皮、其他材料
    selected.forEach((e) => {
      if (editField.value === 'coriumMaterialCode') {
        e.percentCorium = null
      }
      if (editField.value === 'otherMaterialCode') {
        e.percentOther = null
      }
      e[editField.value] = ''
    })
    return
  }
  selected.forEach((e) => {
    e[editField.value] = []
  })
}

const currentRow = ref<CreateProductInfoAPI.SKCInfo | null>(null)
const colorInfoDialogVisible = ref(false)
const handleOpenColorInfoDialog = (row: CreateProductInfoAPI.SKCInfo) => {
  if (row.id) {
    return
  }
  currentRow.value = row
  colorInfoDialogVisible.value = true
}

const handlePickColor = (val: ColorListPageAPI.Row) => {
  if (!currentRow.value) {
    return
  }
  currentRow.value.colorCodeItemName = val.englishName || ''
  currentRow.value.colorCode = val.code
  currentRow.value.colorId = val.id
  currentRow.value.thumbnail = val.thumbnail
}

const addSizeDialogVisible = ref(false)
const handleOpenSizeDialog = ({ column }: VxeTableDefines.CellClickEventParams) => {
  if (column.field === 'newSize') {
    addSizeDialogVisible.value = true
  }
}

const sizeList = ref<(SizeListAPI.Data & { sizeValueList: SizeValueAPI.List })[]>([])
const sizeRangeId = ref<number[]>([])
const tempSizeRangeId = ref<number[]>([])

const sizeTableData = ref<
  {
    id: number
    name?: string
    sizeValueList?: SizeValueAPI.List
    sizeValue?: number[]
  }[]
>([])
const tempSizeTableData = ref<typeof sizeTableData.value>([])
const sizeValueMap = computed<Record<string, { name: string; sizeValueList: SizeValueAPI.List }>>(
  () => {
    return sizeList.value.reduce((pre, e) => {
      pre[e.id!] = {
        name: e.name,
        sizeValueList: e.sizeValueList
      }
      return pre
    }, {})
  }
)
const selectedSize = computed(() => {
  return [
    ...new Set(
      tempSizeTableData.value
        .map((e) => e.sizeValue)
        .join(',')
        .split(',')
        .filter((e) => e)
    )
  ]
})

const selectedSizeList = computed(() => {
  return sizeList.value.filter((e) => formData?.value?.sizeRangeId?.includes(e.id!))
})

const selectSizeItemName = computed(() => {
  const selectedSizeListValue = selectedSizeList.value
  const sizeGroup = groupBy(
    formData?.value?.selectedSize?.map((id) => {
      const sizeObj = selectedSizeListValue.find((item) =>
        item.sizeValueList?.find((size) => size.id === id)
      )
      return {
        name: sizeObj?.name,
        sizeValue: sizeObj?.sizeValueList?.find((size) => size.id === id)?.sizeValue
      }
    }),
    'name'
  )
  return Object.keys(sizeGroup).map((key) => {
    const sizeValueList = sizeGroup[key].map((item) => item.sizeValue).join(', ')
    return `${sizeValueList}【${key}】`
  })
})

const fetchSizeList = async () => {
  const { datas } = await getSizeList()
  if (datas) {
    sizeList.value = datas
      .filter((e) => e.sizeType === SizeCodeTypeEnums.PRODUCT)
      .map((e, i) => {
        return {
          ...datas[i],
          sizeValueList:
            e.sizeValueList?.map((item) => ({
              ...item,
              disabled: item.status !== StatusEnum.START
            })) || []
        }
      })
  }
}

Promise.all([fetchSizeList()])

watch(
  () => addSizeDialogVisible.value,
  (val) => {
    if (val) {
      if (tempSizeRangeId.value.length) {
        sizeRangeId.value = tempSizeRangeId.value
      } else {
        sizeRangeId.value = formData?.value?.sizeRangeId || []
      }
      sizeTableData.value = tempSizeTableData.value
    }
  }
)

watch(
  () => sizeRangeId.value,
  (val) => {
    sizeTableData.value = val.map((e, i) => {
      const rangeSize = sizeValueMap.value[e]
      const rangeSizeInTable = sizeTableData.value[i]

      const selectedRangeSizeId = rangeSize.sizeValueList
        .filter((item) => formData.value?.selectedSize?.includes(item.id!))
        .filter(Boolean)
        .map((item) => item.id!)
      const obj: {
        id: number
        name?: string
        sizeValueList?: SizeValueAPI.List
        sizeValue?: number[]
      } = {
        id: e,
        name: rangeSize?.name,
        sizeValue: rangeSizeInTable?.sizeValue?.length
          ? rangeSizeInTable.sizeValue
          : selectedRangeSizeId,
        sizeValueList: rangeSize?.sizeValueList || []
      }
      return obj
    })
  }
)

const handleAddSizeDialogClose = () => {
  addSizeDialogVisible.value = false
  sizeTableData.value = []
  sizeRangeId.value = []
}
const handleAddSizeSubmit = () => {
  if (!sizeRangeId.value.length) {
    ElMessage.warning('请选择尺码段')
    return
  }
  tempSizeTableData.value = cloneDeep(sizeTableData.value)
  tempSizeRangeId.value = sizeRangeId.value.slice()
  handleAddSizeDialogClose()
}

const handleAddSKC = () => {
  const materialInfoList = new Array(4).fill(null).map((_, i) => ({
    indexName: `面料${i + 1}`,
    addOld: false,
    selectable: ProductSelectResultEnum.SELECTED
  }))
  if (!formData?.value?.skcInfoDetailResp) {
    formData!.value!.skcInfoDetailResp = [
      {
        materialInfoList,
        addOld: true,
        selectable: ProductSelectResultEnum.SELECTED,
        percentCorium: 0,
        percentOther: 0,
        percentPu: 0,
        percentTextile: 0
      }
    ]
    tableRef.value?.loadData(tableData.value)
    return
  }
  formData?.value?.skcInfoDetailResp.push({
    materialInfoList,
    addOld: true,
    selectable: ProductSelectResultEnum.SELECTED,
    percentCorium: 0,
    percentOther: 0,
    percentPu: 0,
    percentTextile: 0
  })
  tableRef.value?.loadData(tableData.value)
}

const mergeCells = computed(() => [
  { row: 0, col: 11, rowspan: tableData.value.length || 1, colspan: 1 },
  { row: 0, col: 12, rowspan: tableData.value?.length || 1, colspan: 1 }
])

const handleDelSKC = (row: CreateProductInfoAPI.SKCInfo, index: number) => {
  if (row.id) {
    ElMessage.warning('已经生成SKC数据，不可删除')
    return
  }
  formData?.value?.skcInfoDetailResp?.splice(index, 1)
  tableRef.value?.loadData(tableData.value)
}

const handleCopySKC = (row: CreateProductInfoAPI.SKCInfo, index: number) => {
  const copyRow = cloneDeep(row)
  formData?.value?.skcInfoDetailResp?.splice(index + 1, 0, {
    ...copyRow,
    id: undefined,
    skcCode: undefined,
    [VXE_TABLE_ROW_KEY]: undefined,
    colorCode: undefined,
    colorId: undefined,
    colorCodeItemName: undefined,
    thumbnail: undefined,
    sampleUrl: [],
    velvetApplied: undefined,
    velvetRequirements: undefined,
    dataStatusItemName: '草稿',
    addOld: true,
    selectable: ProductSelectResultEnum.SELECTED
  })
  tableRef.value?.loadData(tableData.value)
}

const submitLoading = ref(false)
const submitParams = computed(() => {
  return {
    id: formData?.value?.id,
    sizeRangeId: tempSizeRangeId.value.length
      ? tempSizeRangeId.value
      : formData?.value?.sizeRangeId,
    selectedSize: selectedSize.value.length ? selectedSize.value : formData?.value?.selectedSize,
    oldData: formData?.value?.oldData,
    productNumber: formData?.value?.productNumber,
    skcInfoDetailResp: formData?.value?.skcInfoDetailResp?.map((e) => ({
      ...e,
      coriumMaterialCode: getMaterialCode(e.coriumMaterialCode),
      otherMaterialCode: getMaterialCode(e.otherMaterialCode)
    }))
  }
})
const handleSubmit = async () => {
  const formValid = await formRef.value?.validate().catch(() => false)
  const addSizeOrColorValid = tableData.value.some((e) => !e.id) || selectedSize.value.length
  if (!formValid) return
  if (!addSizeOrColorValid) {
    ElMessage.warning('请加色或者加码')
    return
  }
  submitLoading.value = true
  const [error, result] = await addColorSize(submitParams.value)
  submitLoading.value = false
  if (!error && result) {
    ElMessage.success(result.msg || '提交成功')
    emit('refresh')
    handleClose()
  }
}
</script>

<template>
  <Dialog
    v-model="addSizeDialogVisible"
    :before-close="handleAddSizeDialogClose"
    :parent-scroll="false"
    title="新增尺码"
    top="5vh"
    width="700px"
  >
    <ElForm label-width="auto">
      <ElFormItem label="尺码段" prop="sizeRangeId">
        <ElSelect
          v-model="sizeRangeId"
          clearable
          collapse-tags
          collapse-tags-tooltip
          filterable
          multiple
          placeholder="请选择尺码段"
        >
          <ElOption
            v-for="item in sizeList"
            :key="item.id"
            :disabled="formData?.sizeRangeId?.includes(item.id!)"
            :label="item.name"
            :value="item.id!"
          />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label-width="0">
        <div class="w-full">
          <VxeTable
            :data="sizeTableData"
            :highlight-hover-row="false"
            :max-height="500"
            :scroll-x="{ enabled: true }"
            :scroll-y="{ enabled: true }"
            :show-overflow="false"
          >
            <VxeColumn field="name" title="尺码段" />
            <VxeColumn align="left" header-align="center" title="尺码值">
              <template #default="{ row }">
                <ElCheckboxGroup v-model="row.sizeValue" class="grid grid-cols-4 items-start">
                  <ElCheckbox
                    v-for="item in row.sizeValueList"
                    :key="item.id"
                    :disabled="formData?.selectedSize?.includes(item.id) || item.disabled"
                    :label="item.sizeValue"
                    :value="item.id"
                  />
                </ElCheckboxGroup>
              </template>
            </VxeColumn>
          </VxeTable>
        </div>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="handleAddSizeDialogClose()">取消</ElButton>
      <ElButton type="primary" @click="handleAddSizeSubmit"> 确定 </ElButton>
    </template>
  </Dialog>
  <ColorInfoDialog v-model="colorInfoDialogVisible" @submit="handlePickColor" />
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    append-to-body
    title="产品配色信息"
    top="5vh"
  >
    <ElForm
      ref="formRef"
      v-loading="queryLoading"
      :model="formData"
      :rules="formRules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      class="mt-2"
      label-width="auto"
      scroll-to-error
    >
      <ElFormItem class="material-form-item" label-width="0" prop="skcInfoDetailResp">
        <div class="mb-2 w-full flex">
          <ElButton type="primary" @click="handleAddSKC"> 添加</ElButton>
          <h3 class="ml-2 whitespace-nowrap">批量修改属性</h3>
          <ElSelect
            v-model="editField"
            :validate-event="false"
            class="ml-2 !w-48"
            filterable
            placeholder="请选择"
          >
            <ElOption v-for="item in 4" :key="item" :label="'面料' + item" :value="item" />
            <ElOption label="里材料" value="liningMaterialCode" />
            <ElOption label="里绒材料" value="liningSituation" />
            <ElOption label="垫材料" value="paddingMaterialSurfaceCode" />
            <ElOption label="底材料" value="soleMaterialOutsoleCode" />
            <ElOption label="橡胶/塑料%" value="percentPu" />
            <ElOption label="真皮%" value="coriumMaterialCode" />
            <ElOption label="纺织物%" value="percentTextile" />
            <ElOption label="其他%" value="otherMaterialCode" />
          </ElSelect>
          <ElCascader
            v-model="material"
            :options="setMaterialOptions"
            :props="setMaterialCascaderProps"
            :validate-event="false"
            class="ml-2"
            clearable
            filterable
            placeholder="请选择材质"
          />
          <ElInputNumber
            v-if="
              ['coriumMaterialCode', 'otherMaterialCode', 'percentTextile', 'percentPu'].includes(
                editField.toString()
              )
            "
            v-model="rate"
            :controls="false"
            :min="0"
            :precision="0"
            :validate-event="false"
            :value-on-clear="null"
            class="!w-24 ml-2"
            placeholder="占比"
          />
          <ElButton class="ml-2" type="primary" @click="handleSetMaterial">一键设置</ElButton>
          <ElButton @click="handleResetMaterial">重置</ElButton>
        </div>
        <div>
          <VxeTable
            ref="tableRef"
            :checkbox-config="{ checkMethod: ({ row }) => !row.id }"
            :data="tableData"
            :edit-config="{ showIcon: false }"
            :edit-rules="tableRules"
            :highlight-hover-row="false"
            :max-height="500"
            :merge-cells="mergeCells"
            :scroll-x="{ enabled: false }"
            :scroll-y="{ enabled: false }"
            :show-overflow="false"
            :valid-config="{ showMessage: false, autoPos: true }"
            class="w-full color-size-table"
            @cell-click="handleOpenSizeDialog"
          >
            <VxeColumn fixed="left" type="checkbox" width="40" />
            <VxeColumn fixed="left" title="序号" type="seq" width="60" />
            <VxeColumn field="skcCode" fixed="left" title="SKC编号" width="120" />
            <VxeColumn field="dataStatusItemName" fixed="left" title="状态" width="100" />
            <VxeColumn
              field="colorCode"
              fixed="left"
              header-class-name="header-required"
              min-width="120"
              title="产品配色"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <ElInput
                  v-model="row.colorCodeItemName"
                  :disabled="!!row.id"
                  :validate-event="false"
                  placeholder="请选择"
                  readonly
                >
                  <template #prefix>
                    <Icon
                      :size="26"
                      class="cursor-pointer"
                      color="#409EFF"
                      icon="mdi:search"
                      @click="handleOpenColorInfoDialog(row)"
                    />
                  </template>
                </ElInput>
              </template>
            </VxeColumn>
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="thumbnail"
              fixed="left"
              title="颜色缩略图"
              width="120"
            />
            <VxeColumn
              :show-overflow="false"
              field="sampleUrl"
              fixed="left"
              header-class-name="header-required"
              title="样品图"
              width="170"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <OssUpload
                  v-model="row.sampleUrl"
                  :disabled="!!row.id"
                  :limit="20"
                  :show-file-list="false"
                  :size-limit="1024 * 1024 * 100"
                  :validate-event="false"
                  accept="image/*"
                  drag
                  multiple
                />
                <div
                  v-if="row.sampleUrl?.[0]"
                  class="flex flex-col items-center relative"
                  style="margin: 5px"
                >
                  <Icon
                    v-if="!row.id"
                    class="mb-1"
                    icon="ep:close"
                    style="position: absolute; top: 5px; right: 0; cursor: pointer"
                    @click="!row.id && row.sampleUrl?.splice(0, 1)"
                  />
                  <ElImage
                    :preview-src-list="row.sampleUrl?.map((e) => e.signatureUrl || '') || []"
                    :src="row.sampleUrl?.[0].signatureUrl"
                    fit="contain"
                    previewTeleported
                    hide-on-click-modal
                    style="width: 100px; height: 100px"
                  />
                </div>
              </template>
            </VxeColumn>
            <VxeColumn
              field="velvetApplied"
              header-class-name="header-required"
              min-width="100"
              title="是否植绒"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <SelectPlus
                  v-model="row.velvetApplied"
                  :disabled="!!row.id"
                  :validate-event="false"
                  api-key="FLOCKING_YES_NO"
                  cache
                />
              </template>
            </VxeColumn>
            <VxeColumn field="velvetRequirements" min-width="160" title="植绒要求">
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <ElInput
                  v-model="row.velvetRequirements"
                  :disabled="!!row.id"
                  :validate-event="false"
                  clearable
                  maxlength="100"
                  placeholder="请输入"
                  show-word-limit
                />
              </template>
            </VxeColumn>
            <VxeColumn
              field="mainFabric"
              fixed="left"
              header-class-name="header-required"
              min-width="120"
              title="主要面料"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <span v-if="row.id">
                  {{ row.mainFabricItemName }}
                </span>
                <ElCascader
                  v-else
                  v-model="row.mainFabric"
                  :disabled="!!row.id"
                  :options="materialCategoryList"
                  :props="materialCascaderProps"
                  :validate-event="false"
                  clearable
                  filterable
                />
              </template>
            </VxeColumn>
            <VxeColumn
              field="colorType"
              header-class-name="header-required"
              title="配色类型"
              width="120"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <SelectPlus
                  v-model="row.colorType"
                  :disabled="!!row.id"
                  :validate-event="false"
                  api-key="PRODUCT_COLOR_TYPE"
                  cache
                />
              </template>
            </VxeColumn>
            <VxeColumn field="currentSize" title="当前尺码" width="150">
              <template #default>
                <div v-for="item in selectSizeItemName" :key="item">
                  {{ item }}
                </div>
              </template>
            </VxeColumn>
            <VxeColumn field="newSize" title="新增尺码" width="120">
              <template #default>
                <Icon :size="26" class="cursor-pointer" color="#409EFF" icon="mdi:search" />
                <div v-for="item in tempSizeTableData" :key="item.id">
                  <span v-if="item.sizeValue?.length">{{
                    item.sizeValue
                      ?.map((sizeId) => item.sizeValueList?.find((e) => e.id === sizeId)?.sizeValue)
                      .join(', ')
                  }}</span>
                  【{{ item.name }}】
                </div>
              </template>
            </VxeColumn>
            <VxeColumn
              v-for="item in 4"
              :key="item"
              :field="`materialInfoList.${item - 1}.material`"
              :header-class-name="item === 1 ? 'header-required' : ''"
              :title="'面料' + item"
              min-width="180"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <span v-if="row.id">
                  {{ row.materialInfoList?.[item - 1].materialItemName }}
                </span>
                <ElCascader
                  v-else
                  v-model="row.materialInfoList![item - 1].material"
                  :disabled="!!row.id"
                  :options="commonMaterialCategoryList"
                  :props="materialCascaderProps"
                  :validate-event="false"
                  clearable
                  filterable
                />
              </template>
            </VxeColumn>
            <VxeColumn
              field="liningMaterialCode"
              header-class-name="header-required"
              min-width="180"
              title="里材料"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <span v-if="row.id">
                  {{ row.liningMaterialCodeItemName?.join(',') }}
                </span>
                <ElCascader
                  v-else
                  v-model="row.liningMaterialCode"
                  :disabled="!!row.id"
                  :options="liningMaterialCategoryList"
                  :props="{ ...materialCascaderProps, multiple: true }"
                  :validate-event="false"
                  clearable
                  filterable
                />
              </template>
            </VxeColumn>
            <VxeColumn field="liningSituation" min-width="180" title="里绒材料">
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <span v-if="row.id">
                  {{ row.liningSituationItemName?.join(',') }}
                </span>
                <ElCascader
                  v-else
                  v-model="row.liningSituation"
                  :disabled="!!row.id"
                  :options="materialCategoryList"
                  :props="{ ...materialCascaderProps, multiple: true }"
                  :validate-event="false"
                  clearable
                  filterable
                />
              </template>
            </VxeColumn>
            <VxeColumn
              field="paddingMaterialSurfaceCode"
              header-class-name="header-required"
              min-width="180"
              title="垫材料"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <span v-if="row.id">
                  {{ row.paddingMaterialSurfaceCodeItemName?.join(',') }}
                </span>
                <ElCascader
                  v-else
                  v-model="row.paddingMaterialSurfaceCode"
                  :disabled="!!row.id"
                  :options="insoleMaterialCategoryList"
                  :props="{ ...materialCascaderProps, multiple: true }"
                  :validate-event="false"
                  clearable
                  filterable
                />
              </template>
            </VxeColumn>
            <VxeColumn
              field="soleMaterialOutsoleCode"
              header-class-name="header-required"
              min-width="180"
              title="底材料"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <span v-if="row.id">
                  {{ row.soleMaterialOutsoleCodeItemName?.join(',') }}
                </span>
                <ElCascader
                  v-else
                  v-model="row.soleMaterialOutsoleCode"
                  :disabled="!!row.id"
                  :options="outsoleMaterialCategoryList"
                  :props="{ ...materialCascaderProps, multiple: true }"
                  :validate-event="false"
                  clearable
                  filterable
                />
              </template>
            </VxeColumn>
            <VxeColumn
              :show-overflow="false"
              field="percentPu"
              header-class-name="header-required"
              min-width="180"
              title="橡胶/塑料%"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <ElInputNumber
                  v-model="row.percentPu"
                  :controls="false"
                  :disabled="!!row.id"
                  :max="100"
                  :min="0"
                  :precision="0"
                  :validate-event="false"
                  :value-on-clear="0"
                  class="!w-24"
                />
              </template>
            </VxeColumn>
            <VxeColumn
              :show-overflow="false"
              field="percentCorium"
              header-class-name="header-required"
              min-width="260"
              title="真皮%"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <ElCascader
                  v-model="row.coriumMaterialCode"
                  :disabled="!!row.id"
                  :options="leatherMaterialCategoryList"
                  :props="materialCascaderProps"
                  class="!w-32"
                  clearable
                  filterable
                />
                <ElInputNumber
                  v-model="row.percentCorium"
                  :controls="false"
                  :disabled="!!row.id"
                  :max="100"
                  :min="0"
                  :precision="0"
                  :value-on-clear="0"
                  class="!w-24 ml-2"
                />
              </template>
            </VxeColumn>
            <VxeColumn
              :show-overflow="false"
              field="percentTextile"
              header-class-name="header-required"
              min-width="180"
              title="纺织物%"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <ElInputNumber
                  v-model="row.percentTextile"
                  :controls="false"
                  :disabled="!!row.id"
                  :max="100"
                  :min="0"
                  :precision="0"
                  :validate-event="false"
                  :value-on-clear="0"
                  class="!w-24"
                />
              </template>
            </VxeColumn>
            <VxeColumn
              :show-overflow="false"
              field="percentOther"
              header-class-name="header-required"
              min-width="260"
              title="其他%"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <ElCascader
                  v-model="row.otherMaterialCode"
                  :disabled="!!row.id"
                  :options="otherMaterialCategoryList"
                  :props="materialCascaderProps"
                  :validate-event="false"
                  class="!w-32"
                  clearable
                  filterable
                />
                <ElInputNumber
                  v-model="row.percentOther"
                  :controls="false"
                  :disabled="!!row.id"
                  :max="100"
                  :min="0"
                  :precision="0"
                  :validate-event="false"
                  :value-on-clear="0"
                  class="!w-24 ml-2"
                />
              </template>
            </VxeColumn>
            <VxeColumn
              v-if="!isSelected"
              :show-overflow="false"
              fixed="right"
              title="操作"
              width="80"
            >
              <template
                #default="{
                  row,
                  rowIndex
                }: {
                  row: CreateProductInfoAPI.SKCInfo,
                  rowIndex: number
                }"
              >
                <ElRow justify="center">
                  <ElButton v-if="!row.id" text type="primary" @click="handleDelSKC(row, rowIndex)">
                    删除
                  </ElButton>
                </ElRow>
                <ElRow justify="center">
                  <ElButton text type="primary" @click="handleCopySKC(row, rowIndex)">
                    复制
                  </ElButton>
                </ElRow>
              </template>
            </VxeColumn>
          </VxeTable>
        </div>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit"> 确定 </ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.material-form-item.is-error) {
  .el-input__wrapper,
  .el-select__wrapper {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
  }
}

:deep(.color-size-table .col--valid-error .vxe-cell) {
  .el-select__wrapper,
  .el-input__wrapper {
    box-shadow: 0 0 0 1px red inset !important;
  }
}
</style>
