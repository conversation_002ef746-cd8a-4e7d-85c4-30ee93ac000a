<script lang="ts" setup>
import { ProductCategoryAPI } from '@/views/basic-library-manage/product-library/api/product-category'
import { AddCategoryTypeEnum } from '@/views/basic-library-manage/product-library/const'
import { ElCascader, ElMessage, FormInstance, FormRules } from 'element-plus'
import {
  addCategory,
  AddCategoryAPI,
  updateCategory
} from '@/views/basic-library-manage/product-library/api/addCategoryDialog'
import { statusConst, StatusEnum } from '@/views/basic-library-manage/const'
import { getWMSCategoryList, WMSCategoryListAPI } from '@/views/basic-library-manage/api/common'

defineOptions({
  name: 'AddCategoryDialog'
})

const useConst = () => {
  const statusList = statusConst.statusList

  return {
    statusList: statusList.filter((e) => e.value !== StatusEnum.APPROVING)
  }
}

const { statusList } = useConst()

const props = defineProps<{
  modelValue: boolean
  currentRow: ProductCategoryAPI.Data | null
  type: AddCategoryTypeEnum
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'refresh'): void
}>()

const isAdd = computed(() => {
  return props.type === AddCategoryTypeEnum.ADD
})

const isUpdate = computed(() => {
  return props.type === AddCategoryTypeEnum.UPDATE
})

const title = computed(() => {
  return isUpdate.value ? '修改分类' : '新增分类'
})

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const formData = ref<AddCategoryAPI.Request>({
  structure: [],
  categoryCode: '',
  categoryCnName: '',
  levelCode: '',
  categoryEnName: '',
  parentId: 0,
  status: StatusEnum.DRAFT,
  wmsCategoryIdList: []
})
const formRules = reactive<FormRules<AddCategoryAPI.Request>>({
  categoryCode: [
    {
      required: true,
      message: '请输入品类编码',
      trigger: 'blur'
    }
  ],
  categoryCnName: [
    {
      required: true,
      message: '请输入品类名称',
      trigger: 'blur'
    }
  ],
  categoryEnName: [
    {
      required: true,
      message: '请输入品类英文名称',
      trigger: 'blur'
    }
  ],
  status: [
    {
      required: true,
      message: '请选择状态',
      trigger: 'change'
    }
  ],
  wmsCategoryIdList: [
    {
      required: true,
      message: '请选择商品三级分类名称',
      trigger: 'change'
    }
  ],
  structure: [
    {
      required: true,
      message: '请选择款式结构',
      trigger: 'change'
    }
  ]
})
const formRef = ref<FormInstance>()

watch(
  () => visible.value,
  (val) => {
    if (val) {
      formData.value.parentId = props.currentRow?.id || 0
      if (isUpdate.value) {
        formData.value = {
          ...props.currentRow,
          parentId: props.currentRow?.id || 0
        }
      }
    }
  }
)

const handleClose = () => {
  visible.value = false
  formData.value = {
    structure: [],
    categoryCode: '',
    categoryCnName: '',
    categoryEnName: '',
    parentId: 0,
    status: StatusEnum.DRAFT,
    wmsCategoryIdList: []
  }
}

const submitLoading = ref(false)

const submitFn = computed(() => {
  return isAdd.value ? addCategory : updateCategory
})

const submitParams = computed<AddCategoryAPI.Request>(() => {
  return {
    ...formData.value
  }
})

const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) {
    return
  }
  submitLoading.value = true
  const [error, result] = await submitFn.value(submitParams.value)
  submitLoading.value = false
  if (error === null && result) {
    ElMessage.success(result.msg || '操作成功')
    emit('refresh')
    handleClose()
  }
}

const wmsCategoryList = ref<WMSCategoryListAPI.Data[]>([])
const fetchCategoryList = async () => {
  const [error, result] = await getWMSCategoryList()
  if (error === null && result?.datas) {
    wmsCategoryList.value = result.datas
  }
}
Promise.all([fetchCategoryList()])
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :title="title"
    width="700"
  >
    <ElForm class="mt-2" ref="formRef" :model="formData" :rules="formRules" label-width="auto">
      <template v-if="isAdd && !currentRow">
        <ElFormItem label="品类名称" prop="categoryCnName">
          <ElInput
            v-model="formData.categoryCnName"
            clearable
            maxlength="100"
            placeholder="请输入品类名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="品类英文名称" prop="categoryEnName">
          <ElInput
            v-model="formData.categoryEnName"
            clearable
            maxlength="100"
            placeholder="请输入品类英文名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="品类编码" prop="categoryCode">
          <ElInput
            v-model="formData.categoryCode"
            clearable
            maxlength="20"
            placeholder="请输入品类编码"
            show-word-limit
          />
        </ElFormItem>
      </template>
      <template v-if="isAdd && currentRow?.index === 1">
        <ElFormItem label="品类名称">
          <ElInput
            v-model="currentRow!.categoryCnName"
            clearable
            disabled
            maxlength="100"
            placeholder="请输入品类名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品大类名称" prop="categoryCnName">
          <ElInput
            v-model="formData!.categoryCnName"
            clearable
            maxlength="100"
            placeholder="请输入产品大类名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品大类英文名称" prop="categoryEnName">
          <ElInput
            v-model="formData!.categoryEnName"
            clearable
            maxlength="100"
            placeholder="请输入产品大类英文名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品大类编码">
          <ElInput
            v-model="formData!.categoryCode"
            clearable
            disabled
            maxlength="100"
            placeholder="自动生成，P+2位流水码"
            show-word-limit
          />
        </ElFormItem>
      </template>
      <template v-if="isAdd && currentRow?.index === 2">
        <ElFormItem label="产品层级">
          <ElInput
            :model-value="currentRow!.categoryParentCnName + '/' + currentRow!.categoryCnName"
            clearable
            disabled
            maxlength="100"
            placeholder="请输入产品层级"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品中类名称" prop="categoryCnName">
          <ElInput
            v-model="formData!.categoryCnName"
            clearable
            maxlength="100"
            placeholder="请输入产品中类名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品中类英文名称" prop="categoryEnName">
          <ElInput
            v-model="formData!.categoryEnName"
            clearable
            maxlength="100"
            placeholder="请输入产品中类英文名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品中类编码">
          <ElInput
            v-model="formData!.categoryCode"
            clearable
            disabled
            maxlength="100"
            placeholder="自动生成，大类编码+2位流水码"
            show-word-limit
          />
        </ElFormItem>
      </template>
      <template v-if="isAdd && currentRow?.index! >= 3">
        <ElFormItem label="产品层级">
          <ElInput
            :model-value="currentRow!.categoryParentCnName + '/' + currentRow!.categoryCnName"
            clearable
            disabled
            maxlength="100"
            placeholder="请输入产品层级"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品小类名称" prop="categoryCnName">
          <ElInput
            v-model="formData!.categoryCnName"
            clearable
            maxlength="100"
            placeholder="请输入产品小类名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品小类英文名称" prop="categoryEnName">
          <ElInput
            v-model="formData!.categoryEnName"
            clearable
            maxlength="100"
            placeholder="请输入产品小类英文名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品小类编码" prop="categoryCode">
          <ElInput
            v-model="formData!.categoryCode"
            clearable
            maxlength="2"
            placeholder="请输入产品小类编码"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="款式结构" prop="structure">
          <SelectPlus
            :parent-scroll="false"
            class="!w-full"
            v-model="formData!.structure"
            api-key="COMMON_STYLE_STRUCTURE"
            cache
            collapse-tags
            collapse-tags-tooltip
            multiple
          />
        </ElFormItem>
        <ElFormItem label="商品三级分类名称" prop="wmsCategoryIdList">
          <ElCascader
            class="!w-full"
            v-model="formData!.wmsCategoryIdList"
            :options="wmsCategoryList"
            :props="{
              children: 'sonCategory',
              label: 'categoryName',
              value: 'id',
              emitPath: false,
              multiple: true
            }"
            collapse-tags
            collapse-tags-tooltip
            clearable
            filterable
            placeholder="请选择商品三级分类名称"
          />
        </ElFormItem>
      </template>
      <template v-if="isUpdate && currentRow?.index === 1">
        <ElFormItem label="品类名称" prop="categoryCnName">
          <ElInput
            v-model="formData.categoryCnName"
            clearable
            maxlength="100"
            placeholder="请输入品类名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="品类英文名称" prop="categoryEnName">
          <ElInput
            v-model="formData.categoryEnName"
            clearable
            maxlength="100"
            placeholder="请输入品类英文名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="品类编码" prop="categoryCode">
          <ElInput
            v-model="formData.categoryCode"
            clearable
            maxlength="20"
            placeholder="请输入品类编码"
            show-word-limit
          />
        </ElFormItem>
      </template>
      <template v-if="isUpdate && currentRow?.index === 2">
        <ElFormItem label="品类名称">
          <ElInput
            v-model="currentRow!.categoryCnName"
            clearable
            disabled
            maxlength="100"
            placeholder="请输入品类名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品大类名称" prop="categoryCnName">
          <ElInput
            v-model="formData!.categoryCnName"
            clearable
            maxlength="100"
            placeholder="请输入产品大类名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品大类英文名称" prop="categoryEnName">
          <ElInput
            v-model="formData!.categoryEnName"
            clearable
            maxlength="100"
            placeholder="请输入产品大类英文名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品大类编码">
          <ElInput
            v-model="formData!.categoryCode"
            clearable
            disabled
            maxlength="100"
            placeholder="自动生成，P+2位流水码"
            show-word-limit
          />
        </ElFormItem>
      </template>
      <template v-if="isUpdate && currentRow?.index === 3">
        <ElFormItem label="产品层级">
          <ElInput
            :model-value="currentRow!.categoryParentCnName + '/' + currentRow!.categoryCnName"
            clearable
            disabled
            maxlength="100"
            placeholder="请输入产品层级"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品中类名称" prop="categoryCnName">
          <ElInput
            v-model="formData!.categoryCnName"
            clearable
            maxlength="100"
            placeholder="请输入产品中类名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品中类英文名称" prop="categoryEnName">
          <ElInput
            v-model="formData!.categoryEnName"
            clearable
            maxlength="100"
            placeholder="请输入产品中类英文名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品中类编码" prop="categoryCode">
          <ElInput
            v-model="formData!.categoryCode"
            clearable
            disabled
            maxlength="100"
            placeholder="自动生成，大类编码+2位流水码"
            show-word-limit
          />
        </ElFormItem>
      </template>
      <template v-if="isUpdate && currentRow?.index! >= 4">
        <ElFormItem label="产品层级">
          <ElInput
            :model-value="currentRow!.categoryParentCnName + '/' + currentRow!.categoryCnName"
            clearable
            disabled
            maxlength="100"
            placeholder="请输入产品层级"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品小类名称" prop="categoryCnName">
          <ElInput
            v-model="formData!.categoryCnName"
            clearable
            maxlength="100"
            placeholder="请输入产品小类名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品小类英文名称" prop="categoryEnName">
          <ElInput
            v-model="formData!.categoryEnName"
            clearable
            maxlength="100"
            placeholder="请输入产品小类英文名称"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="产品小类编码" prop="categoryCode">
          <ElInput
            v-model="formData!.categoryCode"
            clearable
            maxlength="2"
            placeholder="请输入产品小类编码"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="款式结构" prop="structure">
          <SelectPlus
            v-model="formData!.structure"
            api-key="COMMON_STYLE_STRUCTURE"
            cache
            collapse-tags
            collapse-tags-tooltip
            multiple
          />
        </ElFormItem>
        <ElFormItem label="商品三级分类名称" prop="wmsCategoryIdList">
          <ElCascader
            v-model="formData!.wmsCategoryIdList"
            :options="wmsCategoryList"
            :props="{
              children: 'sonCategory',
              label: 'categoryName',
              value: 'id',
              emitPath: false,
              multiple: true
            }"
            clearable
            filterable
            placeholder="请选择商品三级分类名称"
          />
        </ElFormItem>
      </template>
      <ElFormItem label="层级编码" v-if="isUpdate">
        <ElInput
          v-model="formData.levelCode"
          clearable
          disabled
          maxlength="100"
          placeholder="请输入层级编码"
          show-word-limit
        />
      </ElFormItem>
      <ElFormItem label="状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio
            v-for="item in statusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确认</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
