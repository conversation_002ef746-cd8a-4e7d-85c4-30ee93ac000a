<script lang="ts" setup>
import { ProductListPageAPI } from '../api/product-list'
import { cloneDeep } from 'lodash-es'
import { ProductDataStatusEnum } from '../../const'
import { ElMessage } from 'element-plus'
import { CommonYesNoEnums } from '@/enums'
import { useBasicLibraryDictStore } from '../../store/dict'
import { updateProductStatus } from '../api/statusDialog'
import { ProductSelectResultEnum } from '../const'

defineOptions({
  name: 'StatusDialog'
})

const useConst = () => {
  const productDataStatusList = computed(() => useBasicLibraryDictStore().productDataStatusList)

  const getProductDataStatusListByRow = (row: ProductListPageAPI.Row) => {
    if (row.dataStatus === ProductDataStatusEnum.INVALID) {
      // 如果当前状态是作废，要判断一下meetingResult字段，如果这个字段是1，下拉框就只有“生效”状态，如果这个字段为0或者为空，下拉框就只有“草稿”状态
      if (row.meetingResult === ProductSelectResultEnum.SELECTED) {
        return productDataStatusList.value.filter(
          (e) => e.dictValue === ProductDataStatusEnum.EFFECT || e.dictValue === row.dataStatus
        )
      }
      return productDataStatusList.value.filter(
        (e) => e.dictValue === ProductDataStatusEnum.DRAFT || e.dictValue === row.dataStatus
      )
    }
    if (row.dataStatus === ProductDataStatusEnum.DRAFT) {
      // 如果当前状态是草稿，下拉框就只有“作废”状态
      return productDataStatusList.value.filter(
        (e) => e.dictValue === ProductDataStatusEnum.INVALID || e.dictValue === row.dataStatus
      )
    }
    if (row.dataStatus === ProductDataStatusEnum.EFFECT) {
      // 如果当前状态是生效，要判断一下meetingResult字段，如果这个字段是1，下拉框就只有“作废”状态，如果这个字段为0或者为空，下拉框就只有“草稿”状态
      if (row.meetingResult === ProductSelectResultEnum.SELECTED) {
        return productDataStatusList.value.filter(
          (e) => e.dictValue === ProductDataStatusEnum.INVALID || e.dictValue === row.dataStatus
        )
      }
      return productDataStatusList.value.filter(
        (e) => e.dictValue === ProductDataStatusEnum.DRAFT || e.dictValue === row.dataStatus
      )
    }
    return []
  }

  return {
    productDataStatusList,
    getProductDataStatusListByRow
  }
}

const { getProductDataStatusListByRow } = useConst()

const props = defineProps<{
  modelValue: boolean
  selectedRows: ProductListPageAPI.List
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const tableData = ref<ProductListPageAPI.List>([])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

watch(
  () => visible.value,
  (val) => {
    if (val) {
      tableData.value = cloneDeep(props.selectedRows)
    }
  }
)

const handleClose = () => {
  visible.value = false
  tableData.value = []
}

const submitLoading = ref(false)
const handleSubmit = async () => {
  const valid = tableData.value.every((e) => e.invalidReason)
  if (!valid) {
    ElMessage.error('请填写变更原因')
    submitLoading.value = false
    return
  }
  submitLoading.value = true
  const [error, result] = await updateProductStatus({
    detailList: tableData.value
  })
  submitLoading.value = false
  if (!error && result) {
    ElMessage.success(result.msg || '操作成功')
    emit('refresh')
    handleClose()
  }
}
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    title="操作确认"
    top="5vh"
    :parent-scroll="false"
  >
    <VxeTable ref="table" :data="tableData" :max-height="500" :show-header-overflow="false">
      <VxeColumn title="序号" type="seq" width="60" />
      <VxeColumn field="productNumber" title="产品编码" />
      <VxeColumn field="brandItemName" title="品牌" />
      <VxeColumn field="launchSeasonItemName" title="开发季节" />
      <VxeColumn field="designerIdItemName" title="设计师" />
      <VxeColumn field="createTime" title="创建时间" />
      <VxeColumn field="developStageItemName" title="产品阶段" />
      <VxeColumn field="sendWmsItemName" title="最后一次下发WMS状态" width="175" />
      <VxeColumn title="已下单" width="80">
        <template #default="{ row }: { row: ProductListPageAPI.Row }">
          <span>{{ row.orderPlacedItemName }}</span>
        </template>
      </VxeColumn>
      <VxeColumn field="dataStatusItemName" title="当前状态" />
      <VxeColumn title="修改状态" width="175">
        <template #default="{ row }: { row: ProductListPageAPI.Row }">
          <span
            v-if="
              row.sendWms === CommonYesNoEnums.Yes &&
              row.orderPlaced === CommonYesNoEnums.Yes &&
              row.dataStatus?.toLowerCase() === ProductDataStatusEnum.EFFECT
            "
          >
            已下单，需走线上数据修改流程，审批通过后修改数据；
          </span>
          <ElSelect v-else v-model="row.dataStatus" clearable filterable>
            <ElOption
              v-for="item in getProductDataStatusListByRow(row)"
              :key="item.dictValue"
              :label="item.dictCnName"
              :value="item.dictValue!"
            />
          </ElSelect>
        </template>
      </VxeColumn>
      <VxeColumn title="变更原因" width="300">
        <template #header> <span class="text-red-500">*</span>变更原因 </template>
        <template #default="{ row }: { row: ProductListPageAPI.Row }">
          <ElInput v-model="row.invalidReason" clearable maxlength="200" />
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
