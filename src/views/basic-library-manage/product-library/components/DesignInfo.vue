<script lang="ts" setup>
import { ElMessage, FormRules } from 'element-plus'
import LastInfoDialog from '../../components/LastInfoDialog.vue'
import HeelInfoDialog from '../../components/HeelInfoDialog.vue'
import { Icon } from '@/components/Icon'
import { HeelListPageAPI } from '../../heel-library/api/heel-list'
import { LastListPageAPI } from '../../last-library/api/last-list'
import { HeelBottomTypeEnum, YesNoEnum } from '../../const'
import { LastBottomTypeEnum, ProductTaskNodeEnum } from '../const'
import {
  createProductInfo,
  Emit,
  getProductImageList,
  getProductLastHeelInfo,
  Props,
  SaveTypeEnum
} from '@/views/basic-library-manage/product-library/api/productInfo'
import { ProductListPageAPI } from '../api/product-list'
import { storeToRefs } from 'pinia'
import { useUserInfoStore } from '@/store/modules/userInfo'
import { viewModel } from '../../model-library/api/modelInfo'
import { RouteNameEnums } from '@/views/InfringementInvestigation/helper'
import { getMaterialCode, useProductInfo } from './helper'

defineOptions({
  name: 'DesignInfo'
})

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const { formData, formRef, getCommonParams } = useProductInfo(props, emit)

const disabled = computed(() => {
  return (
    props.isView ||
    props.initPane !== props.currentPane ||
    props.selectionDisabled ||
    props.confirmDisabled ||
    props.isOldData
  )
})

const router = useRouter()

const activeNames = ref(['1', '2'])

const isNewLast = computed(() => {
  return formData.value.lastBottomType === LastBottomTypeEnum.NEW_BOTTOM_NEW_LAST
})

const formRules = computed<FormRules<Omit<ProductListPageAPI.Row, 'skcInfoDetailResp'>>>(() => ({
  designUrl: [{ required: true, message: '请上传设计图', trigger: 'modelValue' }],
  designerId: [{ required: true, message: '请选择设计师', trigger: 'change' }],
  waterproofLevel: [{ required: true, message: '请选择防水级别', trigger: 'change' }],
  initialSampleColorId: [{ required: true, message: '请选择初样颜色', trigger: 'change' }],
  lastBottomType: [{ required: true, message: '请选择楦底类型', trigger: 'change' }],
  toeShape: [{ required: isNewLast.value, message: '请选择头形', trigger: 'change' }],
  heelType: [{ required: isNewLast.value, message: '请选择跟型', trigger: 'change' }],
  heelHeight: [{ required: isNewLast.value, message: '请选择跟高', trigger: 'change' }],
  specialTestingRequirements: [
    { required: true, message: '请输入特殊检测要求/说明', trigger: 'blur' }
  ],
  samplePrintUrl: [{ required: true, message: '请上传初样打样单', trigger: 'modelValue' }],
  initialSampleIssueDate: [{ required: true, message: '请选择下发打样日期', trigger: 'blur' }]
}))

const lastInfoDialogVisible = ref(false)
const heelInfoDialogVisible = ref(false)
const currentHeelBottomType = ref<HeelBottomTypeEnum[]>([])
const handleOpenLastInfoDialog = () => {
  if (disabled.value) return
  lastInfoDialogVisible.value = true
}

const handleOpenHeelInfoDialog = (type: HeelBottomTypeEnum[]) => {
  if (disabled.value) return
  currentHeelBottomType.value = type
  heelInfoDialogVisible.value = true
}

const { userInfo } = storeToRefs(useUserInfoStore())
watchEffect(async () => {
  if (!userInfo.value) return
  if (disabled.value) {
    return
  }
  await nextTick()
  if (!formData.value.designerId) {
    formData.value.designerId = userInfo.value.id
  }
})

const handlePickHeel = (val?: HeelListPageAPI.Row) => {
  if (currentHeelBottomType.value.includes(HeelBottomTypeEnum.HL)) {
    formData.value.associatedHeelInfoItemName = val?.code
    formData.value.associatedHeelInfo = val?.id
  }
  if (
    currentHeelBottomType.value.includes(HeelBottomTypeEnum.FB) ||
    currentHeelBottomType.value.includes(HeelBottomTypeEnum.OS)
  ) {
    formData.value.associatedSoleInfoItemName = val?.code
    formData.value.associatedSoleInfo = val?.id
  }
  if (currentHeelBottomType.value.includes(HeelBottomTypeEnum.SL)) {
    formData.value.associatedInsoleInfoItemName = val?.code
    formData.value.associatedInsoleInfo = val?.id
  }
  // 跟高
  formData.value.heelHeight = val?.highCode
  // 头型
  formData.value.toeShape = val?.headCode
  // 跟型
  formData.value.heelType = val?.patternCode
  // 跟高(mm)
  formData.value.heelHeightMm = val?.heelHigh
}

const handlePickLast = (val?: LastListPageAPI.Row) => {
  formData.value.associatedLastTypeItemName = val?.code
  formData.value.associatedLastType = val?.id
  // 跟高(mm)
  formData.value.heelHeightMm = val?.heelHeight
  // 头型
  formData.value.toeShape = val?.headCode
}

const handleReferenceProductCodeChange = async (val?: string) => {
  if (!val) return
  const [error, result] = await getProductLastHeelInfo(val)
  if (!error && result?.datas) {
    const { associatedHeelInfo, associatedSoleInfo, associatedInsoleInfo, associatedLastType } =
      result.datas
    currentHeelBottomType.value = [HeelBottomTypeEnum.HL]
    handlePickHeel(associatedHeelInfo)
    currentHeelBottomType.value = [HeelBottomTypeEnum.FB, HeelBottomTypeEnum.OS]
    handlePickHeel(associatedSoleInfo)
    currentHeelBottomType.value = [HeelBottomTypeEnum.SL]
    handlePickHeel(associatedInsoleInfo)
    handlePickLast(associatedLastType)
  }
}

const productImageList = ref<BaseFileDTO[]>([])

const fetchProductImageList = async () => {
  if (!disabled) {
    return
  }
  const [error, result] = await getProductImageList(formData.value.id!)
  if (!error && result?.datas) {
    productImageList.value = result.datas.list
  }
}

fetchProductImageList()

const taskNode = ref<ProductTaskNodeEnum>(ProductTaskNodeEnum.STYLE_DESIGN)

const submit = async (type: SaveTypeEnum) => {
  const valid = await formRef.value?.validate().catch(() => {})
  if (!valid) {
    return false
  }
  const [error, result] = await createProductInfo({
    ...getCommonParams(),
    operateBottom: type,
    taskNode: taskNode.value,
    autoSendWms: true
  })
  if (!error && result) {
    ElMessage.success(result.msg || '操作成功')
    useClosePage('ProductLibrary', {
      state: {
        productNumber: formData.value.productNumber
      }
    })
    return true
  }
  return false
}

const handleEditDesignInfo = () => {
  if (props.selectionDisabled || props.confirmDisabled) {
    ElMessage.warning('该操作已被锁定')
    return
  }
  if (
    !(
      (formData.value.sendWms === (YesNoEnum.N as string) ||
        formData.value.sendWms === (YesNoEnum.Y as string)) &&
      formData.value.orderPlaced === (YesNoEnum.N as string)
    )
  ) {
    ElMessage.warning('产品信息已下单，不能进行修改')
    return
  }
  if (
    formData.value.sendWms === (YesNoEnum.Y as string) &&
    formData.value.orderPlaced === (YesNoEnum.Y as string)
  ) {
    ElMessage.warning('产品信息已下单，不能进行修改')
    return
  }
  router.push({
    name: 'EditProduct',
    query: {
      id: formData.value.id,
      pane: props.currentPane
    }
  })
  // location.href = router.resolve({
  //   name: 'EditProduct',
  //   query: {
  //     id: formData.value.id,
  //     pane: props.currentPane
  //   }
  // }).href
}

const submitLoading = ref(false)
const handleConfigColor = async () => {
  if (props.selectionDisabled || props.confirmDisabled || props.isOldData) {
    ElMessage.warning('该操作已被锁定')
    return
  }
  const valid = await formRef.value?.validate().catch(() => {})
  if (!valid) {
    return
  }
  submitLoading.value = true
  const { assignedFactory } = formData.value
  const [error, result] = await createProductInfo({
    ...formData.value,
    // 大货供应商
    assignedFactory: Array.isArray(assignedFactory) ? assignedFactory.join() : '',
    skcInfoDetailResp: formData.value.skcInfoDetailResp?.map((e) => ({
      ...e,
      coriumMaterialCode: getMaterialCode(e.coriumMaterialCode),
      otherMaterialCode: getMaterialCode(e.otherMaterialCode)
    })),
    taskNode: taskNode.value
  })
  submitLoading.value = false
  if (!error && result) {
    ElMessage.success(result.msg || '操作成功')
    location.href = router.resolve({
      name: 'EditProduct',
      query: {
        id: formData.value.id,
        pane: 'ColorSampleInfo',
        type: 'config'
      }
    }).href
  }
}

const handleInitialScreen = () => {
  const {
    productNumber,
    designUrl,
    designerId,
    brand,
    launchSeason,
    referenceBrand,
    referenceUrl,
    competitiveBrandLink
  } = formData.value
  useClosePage(RouteNameEnums.IS, {
    query: {
      productInfo: JSON.stringify({
        productNumber,
        designUrl,
        designerId,
        brand,
        launchSeason,
        referenceBrand,
        referenceUrl,
        competitiveBrandLink
      })
    }
  })
}

const onMountedFn = async () => {
  if (formData.value.modelId) {
    const [error, result] = await viewModel(formData.value.modelId)
    if (!error && result?.datas) {
      formData.value.toeShape = formData.value.toeShape || result.datas.head
      formData.value.heelType = formData.value.heelType || result.datas.pattern
      formData.value.heelHeight = formData.value.heelHeight || result.datas.high
      formData.value.heelHeightMm = formData.value.heelHeightMm || result.datas.heelHigh
    }
  }
}

watchOnce(() => formData.value.modelId, onMountedFn)

onMounted(onMountedFn)

const handleSample = () => {
  location.href = router.resolve({
    name: 'SampleInfo',
    query: {
      id: formData.value.id
    }
  }).href
}
defineExpose({
  submit
})
</script>

<template>
  <LastInfoDialog v-model="lastInfoDialogVisible" @submit="handlePickLast" />
  <HeelInfoDialog
    v-model="heelInfoDialogVisible"
    :type="currentHeelBottomType"
    @submit="handlePickHeel"
  />
  <ElCarousel
    v-if="productImageList.length && disabled"
    :autoplay="false"
    arrow="always"
    height="300px"
  >
    <ElCarouselItem v-for="item in productImageList" :key="item.signatureUrl">
      <h2 class="text-center font-semibold">{{ item.fileType }}</h2>
      <ElImage
        :preview-src-list="[item.signatureUrl!]"
        :src="item.signatureUrl"
        class="card-img w-full h-full"
        fit="contain"
        loading="lazy"
        hide-on-click-modal
        preview-teleported
      />
    </ElCarouselItem>
  </ElCarousel>
  <ElCollapse v-model="activeNames">
    <ElForm
      ref="formRef"
      :disabled="disabled"
      :model="formData"
      :rules="formRules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      label-width="auto"
      scroll-to-error
    >
      <ElCollapseItem disabled name="1">
        <template #title>
          <div class="font-bold text-base text-[var(--el-collapse-header-text-color)]">
            产品设计信息
          </div>
        </template>
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="设计图" prop="designUrl">
              <OssUpload
                v-model="formData.designUrl"
                :limit="20"
                :size-limit="1024 * 1024 * 100"
                accept="image/*"
                drag
                multiple
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="参考款式图" prop="referenceUrl">
              <OssUpload
                v-model="formData.referenceUrl"
                :limit="20"
                :size-limit="1024 * 1024 * 100"
                accept="image/*"
                drag
                multiple
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品设计师" prop="designerId">
              <CascadeSelector
                v-model="formData.designerId"
                :props="{ emitPath: false }"
                :show-all-levels="!disabled"
                api-key="allUsers"
                cache
                placeholder="请选择产品设计师"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="防水级别" prop="waterproofLevel">
              <SelectPlus
                v-model="formData.waterproofLevel"
                api-key="PRODUCT_WATERPROOF_LEVEL"
                cache
                clearable
                filterable
                placeholder="请选择防水级别"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="头型" prop="toeShape">
              <SelectPlus
                v-model="formData.toeShape"
                api-key="HEEL_HEAD"
                cache
                clearable
                filterable
                placeholder="请选择头型"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="跟型" prop="heelType">
              <SelectPlus
                v-model="formData.heelType"
                api-key="HEEL_PATTERN"
                cache
                clearable
                filterable
                placeholder="请选择跟型"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="场景" prop="scene">
              <SelectPlus
                v-model="formData.scene"
                api-key="PRODUCT_SCENES"
                cache
                clearable
                filterable
                placeholder="请选择场景"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="跟高" prop="heelHeight">
              <SelectPlus
                v-model="formData.heelHeight"
                api-key="HEEL_HIGH"
                cache
                clearable
                filterable
                placeholder="请选择跟高"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="流行元素" prop="trendyElements">
              <SelectPlus
                v-model="formData.trendyElements"
                api-key="PRODUCT_POPULAT_ELEMENTS"
                cache
                clearable
                filterable
                placeholder="请选择流行元素"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="跟高(mm)" prop="heelHeightMm">
              <ElInputNumber
                v-model="formData.heelHeightMm"
                :controls="false"
                :min="0"
                :precision="2"
                :value-on-clear="null"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="参考产品编号" prop="referenceProductCode">
              <SelectPlus
                v-model="formData.referenceProductCode"
                api-key="getProductNumberList"
                clearable
                filterable
                placeholder="请选择参考产品编号"
                virtualized
                @update:model-value="handleReferenceProductCodeChange"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="靴筒高(mm)" prop="bootLegHeightMm">
              <ElInputNumber
                v-model="formData.bootLegHeightMm"
                :controls="false"
                :min="0"
                :precision="2"
                :value-on-clear="null"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="楦底类型" prop="lastBottomType">
              <SelectPlus
                v-model="formData.lastBottomType"
                api-key="PRODUCT_LAST_HEEL_TYPE"
                cache
                clearable
                filterable
                placeholder="请选择楦底类型"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品楦型" prop="associatedLastType">
              <ElInput v-model="formData.associatedLastTypeItemName" placeholder="请选择" readonly>
                <template #prefix>
                  <Icon
                    :size="26"
                    class="cursor-pointer"
                    color="#409EFF"
                    icon="mdi:search"
                    @click="handleOpenLastInfoDialog"
                  />
                </template>
              </ElInput>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="参考品牌" prop="referenceBrand">
              <ElInput
                v-model="formData.referenceBrand"
                clearable
                maxlength="100"
                placeholder="请输入参考品牌"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品大底" prop="associatedSoleInfo">
              <ElInput v-model="formData.associatedSoleInfoItemName" placeholder="请选择" readonly>
                <template #prefix>
                  <Icon
                    :size="26"
                    class="cursor-pointer"
                    color="#409EFF"
                    icon="mdi:search"
                    @click="
                      handleOpenHeelInfoDialog([HeelBottomTypeEnum.OS, HeelBottomTypeEnum.FB])
                    "
                  />
                </template>
              </ElInput>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="竞品链接" prop="competitiveBrandLink">
              <ElInput
                v-model="formData.competitiveBrandLink"
                clearable
                maxlength="200"
                placeholder="请输入竞品链接"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品鞋垫" prop="associatedInsoleInfo">
              <ElInput
                v-model="formData.associatedInsoleInfoItemName"
                placeholder="请选择"
                readonly
              >
                <template #prefix>
                  <Icon
                    :size="26"
                    class="cursor-pointer"
                    color="#409EFF"
                    icon="mdi:search"
                    @click="handleOpenHeelInfoDialog([HeelBottomTypeEnum.SL])"
                  />
                </template>
              </ElInput>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品鞋跟" prop="associatedHeelInfo">
              <ElInput v-model="formData.associatedHeelInfoItemName" placeholder="请选择" readonly>
                <template #prefix>
                  <Icon
                    :size="26"
                    class="cursor-pointer"
                    color="#409EFF"
                    icon="mdi:search"
                    @click="handleOpenHeelInfoDialog([HeelBottomTypeEnum.HL])"
                  />
                </template>
              </ElInput>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="特殊检测需求/说明" prop="specialTestingRequirements">
              <ElInput
                v-model="formData.specialTestingRequirements"
                :autosize="{ minRows: 4, maxRows: 4 }"
                maxlength="500"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElCollapseItem>
    </ElForm>
  </ElCollapse>
  <Teleport v-if="mounted" to=".product-info-footer">
    <span v-show="currentPane === 'DesignInfo' && isView" class="mr-3">
      <ElButton
        v-hasPermi="['viewProduct:initialScreen', 'editProduct:initialScreen']"
        @click="handleInitialScreen"
      >
        侵权初筛
      </ElButton>
    </span>
    <span v-show="currentPane === 'DesignInfo' && disabled">
      <ElButton
        v-hasPermi="['viewProduct:reviewColor', 'editProduct:reviewColor']"
        @click="handleSample"
      >
        打样管理
      </ElButton>
      <ElButton
        v-hasPermi="['viewProduct:designProduct', 'editProduct:designProduct']"
        @click="handleEditDesignInfo"
        type="primary"
      >
        修改款式设计信息
      </ElButton>
    </span>
    <span v-show="currentPane === 'DesignInfo' && !disabled && isEdit">
      <ElButton
        v-hasPermi="['viewProduct:configColor', 'editProduct:configColor']"
        :loading="submitLoading"
        type="primary"
        @click="handleConfigColor"
      >
        提交并配置齐色样
      </ElButton>
    </span>
  </Teleport>
</template>

<style lang="less" scoped></style>
