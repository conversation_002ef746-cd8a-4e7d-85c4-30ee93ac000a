<script lang="ts" setup>
import {
  getRelatedObject,
  Props,
  RelatedInfoApi
} from '@/views/basic-library-manage/product-library/api/productInfo'
import { ElPagination } from 'element-plus'
import { ref } from 'vue'
import { RelateConfig } from '@/views/basic-library-manage/product-library/components/helper'
import { useRoute } from 'vue-router'
import { RelatedObjectMapping } from '@/views/basic-library-manage/product-library/const'
import noop from 'xe-utils/noop'

defineOptions({
  name: 'RelatedProductInfo'
})
const props = defineProps<Props>()
const pagers = reactive({
  [RelatedObjectMapping.relatedProduct]: {
    current: 1,
    size: 10,
    total: 0,
    tableData: [],
    queryLoading: false,
    tableRef: null,
    controller: null,
    searchParam: {
      styleNumber: props.modelValue.styleNumber
    }
  },
  [RelatedObjectMapping.relatedSellingPoints]: {
    current: 1,
    size: 10,
    total: 0,
    tableData: [],
    queryLoading: false,
    tableRef: null,
    controller: null,
    searchParam: {
      styleNumber: props.modelValue.styleNumber,
      productIdList: [props.modelValue.id]
    }
  }
})
const notPageData = ref<RelatedInfoApi.Row>()
const handleQuery = async (name: string) => {
  const queryParams = computed(() => {
    return {
      current: pagers[name].current,
      size: pagers[name].size,
      ...pagers[name].searchParam
    }
  })
  pagers[name].queryLoading = true
  pagers[name].controller = new AbortController()
  const [error, result] = await RelateConfig[name].API(
    queryParams.value,
    pagers[name].controller.signal
  )
  pagers[name].queryLoading = false
  if (error === null && result?.datas) {
    const { records } = result.datas
    pagers[name].tableData = records
    pagers[name].total = result.datas.pager.total
  }
}
const relateCollapse = ref<string[]>([
  RelatedObjectMapping.relatedProduct,
  RelatedObjectMapping.relatedMolds,
  RelatedObjectMapping.relatedSellingPoints,
  RelatedObjectMapping.relatedLastTypes,
  RelatedObjectMapping.relatedProductStyles
])
const getRelatedData = async () => {
  let id: number | string = useRoute().query.id || ''
  if (!id) return false
  const [error, result] = await getRelatedObject(id)
  if (!error && result?.datas) {
    notPageData.value = result.datas
  }
}
watch(
  () => props.currentPane,
  (newVal) => {
    if (newVal === 'RelatedProduct') {
      getRelatedData()
      handleQuery(RelatedObjectMapping.relatedProduct)
      handleQuery(RelatedObjectMapping.relatedSellingPoints)
    }
  },
  {
    immediate: true
  }
)
const dataComputed = computed(() => (name: string) => {
  return RelateConfig[name].needPage
    ? pagers[name]?.tableData || []
    : notPageData.value?.[name] || []
})

defineExpose({
  submit: noop
})
</script>

<template>
  <div>
    <el-collapse v-model="relateCollapse">
      <template v-for="(item, index) in Object.keys(RelateConfig)" :key="index">
        <el-collapse-item :name="item">
          <template #title>
            {{ RelateConfig[item].title }}
          </template>
          <div>
            <VxeGrid
              :key="index + '-table'"
              v-bind="{
                columns: RelateConfig[item].columns,
                maxHeight: 400,
                data: dataComputed(item),
                minHeight: 100,
                showOverflow: 'tooltip',
                scrollX: {
                  enabled: true,
                  gt: 20
                },
                scrollY: {
                  enabled: true,
                  gt: 20
                }
              }"
            />
            <ElPagination
              ref="pagerRef"
              :key="item + '-pager'"
              v-if="RelateConfig[item].needPage"
              v-model:current-page="pagers[item].current"
              v-model:page-size="pagers[item].size"
              :total="pagers[item].total"
              class="mt-4"
              layout="total, sizes, prev, pager, next,jumper"
              @size-change="handleQuery(item)"
              @current-change="handleQuery(item)"
            />
          </div>
        </el-collapse-item>
      </template>
    </el-collapse>
  </div>
</template>

<style lang="less" scoped>
.title {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}
</style>
