<script lang="ts" setup>
import { ElMessage, FormItemRule, FormRules } from 'element-plus'
import { ColorListPageAPI, getColorListByPage } from '../../color-library/api/color-list'
import {
  copyProductSkc,
  createProductInfo,
  CreateProductInfoAPI,
  Emit,
  FormModel,
  Props,
  SaveTypeEnum
} from '@/views/basic-library-manage/product-library/api/productInfo'
import { Icon } from '@/components/Icon'
import ColorInfoDialog from '../../components/ColorInfoDialog.vue'
import { ProductTaskNodeEnum } from '../const'
import { VxeTableInstance } from 'vxe-table'
import { ProductDataStatusEnum, YesNoEnum } from '../../const'
import { VXE_TABLE_ROW_KEY } from '@/constants'
import { useMaterial } from '@/views/basic-library-manage/sample-manage/components/hooks'
import { useProductInfo } from './helper'
import StyleCopyDialog from './StyleCopyDialog.vue'

defineOptions({
  name: 'ColorSampleInfo'
})

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const { formData, formRef, getCommonParams } = useProductInfo(props, emit)

const disabled = computed(() => {
  return props.initPane !== props.currentPane || props.isView
})

const formRules = ref<FormRules<Omit<FormModel, 'skcInfoDetailResp'>>>({
  matchingSampleUrl: [{ required: true, message: '请上传齐色样打样单', trigger: 'input' }],
  developmentContactPersonId: [
    { required: true, message: '请选择跟进开发人员', trigger: 'change' }
  ],
  technicalContactPersonId: [{ required: true, message: '请选择跟进技术人员', trigger: 'change' }],
  sameColorSampleDate: [{ required: true, message: '请选择齐色样打样日期', trigger: 'change' }]
})

const onMountedFn = async () => {
  if (formData.value.initialSampleColorId) {
    if (!formData.value.skcInfoDetailResp?.length) {
      const [error, result] = await getColorListByPage({
        idList: [formData.value.initialSampleColorId.toString()]
      })
      if (!error && result?.datas) {
        const colorInfo = result.datas.records[0]
        formData.value.skcInfoDetailResp = [
          {
            colorCodeItemName: colorInfo?.englishName,
            colorCode: colorInfo?.code,
            colorId: colorInfo?.id,
            thumbnail: colorInfo?.thumbnail,
            skcStatus: ProductDataStatusEnum.DRAFT,
            selectable: YesNoEnum.N
          }
        ]
      }
    }
  }
}

watchOnce(() => formData.value, onMountedFn)

onMounted(onMountedFn)

const tableRef = ref<VxeTableInstance | null>(null)

const handleAddSKC = () => {
  if (!formData.value.skcInfoDetailResp) {
    formData.value.skcInfoDetailResp = [
      {
        skcStatus: ProductDataStatusEnum.DRAFT,
        selectable: YesNoEnum.N
      }
    ]
    tableRef.value?.loadData(formData.value.skcInfoDetailResp || [])
    return
  }
  formData.value.skcInfoDetailResp.push({
    skcStatus: ProductDataStatusEnum.DRAFT,
    selectable: YesNoEnum.N
  })
  tableRef.value?.loadData(formData.value.skcInfoDetailResp)
}

const handleDelSKC = (row: CreateProductInfoAPI.SKCInfo, index: number) => {
  if (row.skcCode) {
    ElMessage.warning('已经生成SKC数据，不可删除')
    return
  }
  formData.value.skcInfoDetailResp?.splice(index, 1)
  tableRef.value?.loadData(formData.value.skcInfoDetailResp || [])
}

const handleCopySKC = (row: CreateProductInfoAPI.SKCInfo, index: number) => {
  formData.value.skcInfoDetailResp?.splice(index + 1, 0, {
    id: undefined,
    skcCode: undefined,
    [VXE_TABLE_ROW_KEY]: undefined,
    colorCode: row.colorCode,
    colorId: row.colorId,
    colorCodeItemName: row.colorCodeItemName,
    thumbnail: row.thumbnail,
    mainFabric: row.mainFabric,
    colorType: row.colorType,
    colorSchemeDraftUrl: [],
    skcStatus: ProductDataStatusEnum.DRAFT
  })
  tableRef.value?.loadData(formData.value.skcInfoDetailResp || [])
}

const handlePickColor = (val: ColorListPageAPI.Row) => {
  if (!currentRow.value) {
    return
  }
  currentRow.value.colorCodeItemName = val.englishName || ''
  currentRow.value.colorCode = val.code
  currentRow.value.colorId = val.id
  currentRow.value.thumbnail = val.thumbnail
}

const currentRow = ref<CreateProductInfoAPI.SKCInfo | null>(null)
const colorInfoDialogVisible = ref(false)
const handleOpenColorInfoDialog = (row: CreateProductInfoAPI.SKCInfo) => {
  if (props.isView) return
  if (row.skcCode) return
  currentRow.value = row
  colorInfoDialogVisible.value = true
}

const styleCopyDialogVisible = ref(false)

async function handleCopyStyle({
  copyProductNumber,
  type
}: {
  copyProductNumber: string
  type: number[]
}) {
  const originalProductNumber = formData.value.productNumber
  const [error, result] = await copyProductSkc({
    productNumber: originalProductNumber,
    copyProductNumber: copyProductNumber,
    type: type
  })
  if (error) {
    return
  }
  if (!Array.isArray(result.datas)) {
    ElMessage.error('复制的skc为空!')
    return
  }

  formData.value.skcInfoDetailResp = result.datas
}

const taskNode = computed<ProductTaskNodeEnum>(() => {
  return ProductTaskNodeEnum.COLOR_CONFIGURATION
})

const router = useRouter()

const submit = async (type: SaveTypeEnum, isSample = false) => {
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) {
    return false
  }
  const [error, result] = await createProductInfo({
    ...getCommonParams(),
    operateBottom: type,
    taskNode: taskNode.value,
    autoSendWms: true
  })
  if (!error && result) {
    ElMessage.success(result.msg || '操作成功')
    if (isSample) {
      await router.push({
        name: 'SampleInfo',
        query: {
          id: formData.value.id
        }
      })
    } else {
      useClosePage('ProductLibrary', {
        state: {
          productNumber: formData.value.productNumber
        }
      })
    }
    return true
  }
  return false
}

const handleEditColorSampleInfo = () => {
  if (!formData.value.canColorSample) {
    ElMessage.error('请先完成款式设计在操作')
    return false
  }
  router.push({
    name: 'EditProduct',
    query: {
      id: formData.value.id,
      pane: 'ColorSampleInfo',
      type: 'config'
    }
  })
  // location.href = router.resolve({
  //   name: 'EditProduct',
  //   query: {
  //     id: formData.value.id,
  //     pane: 'ColorSampleInfo',
  //     type: 'config'
  //   }
  // }).href
}

const handleSample = () => {
  router.push({
    name: 'SampleInfo',
    query: {
      id: formData.value.id
    }
  })
  // location.href = router.resolve({
  //   name: 'SampleInfo',
  //   query: {
  //     id: formData.value.id
  //   }
  // }).href
}

const skcInfoValidator: FormItemRule['validator'] = (_rule, _value, callback) => {
  const notRequired = formData.value.orderPlaced === YesNoEnum.Y || props.isOldData
  const hasColor = formData.value.skcInfoDetailResp?.every((item) => {
    if (item.skcCode && notRequired && item.skcStatus !== ProductDataStatusEnum.EFFECT) {
      return true
    }
    return !!item.colorCode && !!item.colorCodeItemName
  })
  if (!hasColor) {
    callback(new Error('请添加配色信息'))
    return
  }
  const hasMaterial = formData.value.skcInfoDetailResp?.every((item) => {
    if (item.skcCode && notRequired && item.skcStatus !== ProductDataStatusEnum.EFFECT) {
      return true
    }
    return !!item.mainFabric
  })
  if (!hasMaterial) {
    callback(new Error('请添加面料信息'))
    return
  }
  const hasColorType = formData.value.skcInfoDetailResp?.every((item) => {
    if (item.skcCode && notRequired && item.skcStatus !== ProductDataStatusEnum.EFFECT) {
      return true
    }
    return !!item.colorType
  })
  if (!hasColorType) {
    callback(new Error('请添加配色类型信息'))
    return
  }
  callback()
  return
}

const handleSubmitSample = async () => {
  const result = await submit(SaveTypeEnum.SUBMIT, true)
  if (!result) {
    return
  }
  handleSample()
}

const { materialCascaderProps, materialCategoryList } = useMaterial()

/**
 * 同款复制返回的skcList与productDetail返回的skcList顺序不一致
 * 前端统一排序为id自增排序, 新增数据没有id,排在最末尾
 */
const sortedSkcList = computed(() => {
  return formData.value.skcInfoDetailResp?.toSorted((a, b) => {
    if (!!a.id && !!b.id) {
      return a.id - b.id
    }
    if (!!a.id) {
      return -1
    }
    if (!!b.id) {
      return 1
    }
    return 0
  })
})

defineExpose({
  submit
})
</script>

<template>
  <ColorInfoDialog v-model="colorInfoDialogVisible" @submit="handlePickColor" />
  <StyleCopyDialog v-model="styleCopyDialogVisible" @submit="handleCopyStyle" />
  <ElForm
    ref="formRef"
    :disabled="disabled"
    :model="formData"
    :rules="formRules"
    :scroll-into-view-options="{ behavior: 'smooth' }"
    label-width="auto"
    scroll-to-error
  >
    <ElCollapse :model-value="'1'">
      <ElCollapseItem :name="'1'" disabled>
        <template #title>
          <div class="font-bold text-base text-[var(--el-collapse-header-text-color)]">
            {{ '产品配色信息' }}
          </div>
        </template>
        <ElButton v-if="!disabled" class="mb-2" text type="primary" @click="handleAddSKC">
          添加
        </ElButton>
        <ElButton
          v-if="!disabled"
          class="mb-2"
          text
          type="primary"
          @click="styleCopyDialogVisible = true"
        >
          同款复制
        </ElButton>
        <ElFormItem
          :rules="[
            { required: true, message: '请添加产品配色信息', trigger: 'input' },
            {
              validator: skcInfoValidator,
              trigger: 'change'
            }
          ]"
          class="w-full"
          label=""
          label-width="0"
          prop="skcInfoDetailResp"
        >
          <VxeTable ref="tableRef" :data="sortedSkcList" class="!w-full" show-overflow>
            <VxeColumn title="序号" type="seq" width="60" />
            <VxeColumn field="skcCode" min-width="100" title="SKC编号" />
            <VxeColumn field="colorCode" title="产品配色" width="160">
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <ElInput
                  v-model="row.colorCodeItemName"
                  :disabled="!!row.skcCode"
                  placeholder="请选择"
                  readonly
                >
                  <template #prefix>
                    <Icon
                      :size="26"
                      :class="{ 'cursor-pointer': !props.isView }"
                      color="#409EFF"
                      icon="mdi:search"
                      @click="handleOpenColorInfoDialog(row)"
                    />
                  </template>
                </ElInput>
              </template>
            </VxeColumn>
            <VxeColumn field="wmsColorName" title="WMS色号名称" width="120" />
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="thumbnail"
              title="颜色缩略图"
              width="120"
            />
            <VxeColumn field="mainFabric" title="主要面料" width="200">
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <ElCascader
                  v-model="row.mainFabric"
                  :disabled="!!row.skcCode"
                  :options="materialCategoryList"
                  :props="materialCascaderProps"
                  :validate-event="false"
                  clearable
                  filterable
                />
              </template>
            </VxeColumn>
            <VxeColumn field="colorType" title="配色类型" width="140">
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <SelectPlus
                  v-model="row.colorType"
                  :disabled="!!row.skcCode"
                  api-key="PRODUCT_COLOR_TYPE"
                  cache
                />
              </template>
            </VxeColumn>
            <VxeColumn
              :show-overflow="false"
              field="colorSchemeDraftUrl"
              title="配色图稿"
              width="160"
            >
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <OssUpload
                  v-if="!props.isView"
                  v-model="row.colorSchemeDraftUrl"
                  :disabled="!!row.skcCode"
                  :limit="20"
                  :multiple="true"
                  :show-file-list="false"
                  :size-limit="1024 * 1024 * 100"
                  accept="image/*"
                  drag
                  listType="text"
                >
                  <template #trigger>
                    <Icon :size="22" color="var(--el-color-primary)" icon="mdi:upload" />
                  </template>
                </OssUpload>
                <div
                  v-if="row.colorSchemeDraftUrl?.[0]"
                  class="flex flex-col items-center relative m-[5px"
                >
                  <Icon
                    v-if="!props.isView"
                    class="absolute top-[5px] right-0 cursor-pointer mb-1"
                    icon="ep:close"
                    @click="row.colorSchemeDraftUrl?.splice(0, 1)"
                  />
                  <ElImage
                    :preview-src-list="
                      row.colorSchemeDraftUrl?.map((e) => e.signatureUrl || '') || []
                    "
                    :src="row.colorSchemeDraftUrl?.[0].signatureUrl"
                    fit="contain"
                    class="w-[100px] h-[100px] m-1"
                    hide-on-click-modal
                  />
                </div>
              </template>
            </VxeColumn>
            <VxeColumn field="skcStatus" title="状态" width="100">
              <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
                <SelectPlus v-model="row.skcStatus" api-key="PRODUCT_DATA_STATUS" disabled />
              </template>
            </VxeColumn>
            <VxeColumn field="initialProof" show-overflow title="初样状态" width="100" />
            <VxeColumn field="colorProof" show-overflow title="齐色样状态" width="100" />
            <VxeColumn field="confirmProof" show-overflow title="确认样状态" width="100" />
            <VxeColumn :show-overflow="false" :visible="!disabled" title="操作" width="150">
              <template #default="{ row, rowIndex }">
                <div class="flex">
                  <ElButton text type="primary" @click="handleCopySKC(row, rowIndex)">
                    复制
                  </ElButton>
                  <ElButton
                    v-if="!row.initialProof && !row.colorProof && !row.confirmProof && !row.skcCode"
                    text
                    type="primary"
                    @click="handleDelSKC(row, rowIndex)"
                  >
                    删除
                  </ElButton>
                </div>
              </template>
            </VxeColumn>
          </VxeTable>
        </ElFormItem>
      </ElCollapseItem>
    </ElCollapse>
  </ElForm>
  <Teleport v-if="props.mounted" to=".product-info-footer">
    <span v-show="props.currentPane === 'ColorSampleInfo' && disabled">
      <ElButton
        v-hasPermi="['viewProduct:configColor', 'editProduct:configColor']"
        @click="handleEditColorSampleInfo"
      >
        修改齐色样配置
      </ElButton>
      <ElButton
        v-hasPermi="['viewProduct:reviewColor', 'editProduct:reviewColor']"
        @click="handleSample"
        type="primary"
      >
        打样管理
      </ElButton>
    </span>
    <span v-show="props.currentPane === 'ColorSampleInfo' && !disabled">
      <ElButton
        v-hasPermi="['viewProduct:sample', 'editProduct:sample']"
        @click="handleSubmitSample"
      >
        提交并打样配置
      </ElButton>
    </span>
  </Teleport>
</template>

<style lang="less" scoped>
.title {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}
</style>
