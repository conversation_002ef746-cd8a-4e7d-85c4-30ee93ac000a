import { ElCascader, ElMessage, ElMessageBox, ElTable, ElTableColumn } from 'element-plus'
import { getWMSCategoryList, WMSCategoryListAPI } from '../../api/common'
import { cloneDeep } from 'lodash-es'
import { ProductListPageAPI } from '../api/product-list'
import { ProductSelectResultEnum } from '../const'

const wmsCategoryList = ref<WMSCategoryListAPI.Data[]>([])
const fetchCategoryList = async () => {
  if (wmsCategoryList.value.length > 0) {
    return
  }
  const [error, result] = await getWMSCategoryList()
  if (error === null && result?.datas) {
    wmsCategoryList.value = result.datas
  }
}
export const sendToWMS = (
  productAttr: ProductListPageAPI.Row,
  sizeOptions: {
    label: string
    value: number | undefined
    disabled: boolean | undefined
  }[]
): Promise<false | ProductListPageAPI.Row[]> => {
  const colorList = ref(
    cloneDeep([productAttr]).map((e) => ({
      ...e,
      disabled: !!e.wmsCategoryId
    }))
  )
  fetchCategoryList()
  return new Promise((resolve) => {
    ElMessageBox({
      title: '下发数据到WMS',
      showClose: true,
      draggable: true,
      showCancelButton: true,
      customClass: 'send-to-wms-dialog min-w-[90vw] align-baseline',
      message: () => {
        return (
          <ElTable data={colorList.value} border max-height={500} show-overflow-tooltip>
            <ElTableColumn align="center" label="序号" type="index" width="60" />
            <ElTableColumn
              align="center"
              property="productNumber"
              label="产品编号"
              min-width="100"
            />
            <ElTableColumn
              align="center"
              property="allProductColorIdListItemName"
              label="选中的颜色"
              width={160}
            >
              {{
                default: () => {
                  const colorAttr = productAttr.skcInfoDetailResp
                    ?.filter((e) => e.selectable === ProductSelectResultEnum.SELECTED)
                    ?.map(
                      (e) =>
                        `${e.colorCodeItemName}${
                          e.mainFabricItemName ? `/${e.mainFabricItemName}` : ''
                        }`
                    )
                    ?.join(',')
                  return <span>{colorAttr || productAttr.allProductColorIdListItemName}</span>
                }
              }}
            </ElTableColumn>
            <ElTableColumn
              align="center"
              property="selectedSizeItemName"
              label="选中的尺码"
              min-width="100"
            >
              {{
                default: () => {
                  const sizeAttr =
                    productAttr.selectedSize?.length && sizeOptions.length
                      ? productAttr.selectedSize
                          .map((e) => {
                            const sizeLabel = sizeOptions.find((item) => item.value === e)
                            return sizeLabel?.label
                          })
                          .join(',')
                      : productAttr.selectedSizeItemName?.join(',')
                  return <span>{sizeAttr}</span>
                }
              }}
            </ElTableColumn>
            <ElTableColumn align="center" property="brandItemName" label="品牌" />
            <ElTableColumn
              align="center"
              property="launchSeasonItemName"
              label="开发季节"
              min-width="100"
            />
            <ElTableColumn
              align="center"
              property="targetAudienceItemName"
              label="适用人群"
              min-width="100"
            />
            <ElTableColumn
              align="center"
              property="assignedFactoryItemName"
              label="大货供应商"
              min-width="100"
            />
            <ElTableColumn
              align="center"
              property="productCategoryItemName"
              label="产品类目"
              min-width="100"
            />
            <ElTableColumn align="center" label="商品类目" width={200}>
              {{
                default: ({ row }: { row: ProductListPageAPI.Row }) => {
                  return (
                    <ElCascader
                      vModel={row.wmsCategoryId}
                      disabled={row.disabled}
                      placeholder="请选择商品三级分类名称"
                      options={wmsCategoryList.value}
                      filterable
                      filterMethod={(node, keyword) => {
                        return node.text.toLowerCase().indexOf(keyword.toLowerCase()) !== -1
                      }}
                      props={{
                        children: 'sonCategory',
                        label: 'categoryName',
                        value: 'id',
                        emitPath: false
                      }}
                      clearable
                      collapseTags
                      collapseTagsTooltip
                    />
                  )
                },
                header: () => (
                  <>
                    <span class="text-red-500">* </span>
                    <span>商品类目</span>
                  </>
                )
              }}
            </ElTableColumn>
            <ElTableColumn align="center" property="createByIdItemName" label="创建人" />
            <ElTableColumn align="center" property="createTime" label="创建时间" />
          </ElTable>
        )
      },
      beforeClose: (action, _, done) => {
        if (action === 'confirm') {
          if (!colorList.value.every((e) => e.wmsCategoryId)) {
            ElMessage.warning('请选择商品类目')
            return
          }
          done()
        } else {
          done()
        }
      }
    })
      .then(() => {
        resolve(colorList.value)
      })
      .catch(() => {
        resolve(false)
      })
  })
}
