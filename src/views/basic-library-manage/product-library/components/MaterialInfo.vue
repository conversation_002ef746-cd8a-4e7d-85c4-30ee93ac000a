<script lang="ts" setup>
import { ElMessage, FormItemRule, FormRules } from 'element-plus'
import {
  createProductInfo,
  Emit,
  Props,
  SaveTypeEnum
} from '@/views/basic-library-manage/product-library/api/productInfo'
import { MaterialTypeEnum, ProductTaskNodeEnum } from '../const'
import AddMaterial from './AddMaterial.vue'
import AddMaterialRatio from './AddMaterialRatio.vue'
import { VxeTableDefines } from 'vxe-table'
import { useProductInfo } from './helper'
import { YesNoEnum } from '@/views/basic-library-manage/const'

defineOptions({
  name: 'MaterialInfo'
})

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const { formData, formRef, getCommonParams } = useProductInfo(props, emit)

const skcInfoValidator: FormItemRule['asyncValidator'] = async (_rule, _value, callback) => {
  if (!formData.value.skcInfoDetailResp?.length) {
    callback(new Error('请至少添加一条SKC数据'))
    return
  }
  const errorMsg: VxeTableDefines.ValidatorErrorMapParams | undefined | boolean =
    await tableRef.value?.validate?.(true).catch(() => false)
  if (errorMsg) {
    const { row, column, rule } = Object.values(
      errorMsg as VxeTableDefines.ValidatorErrorMapParams
    )[0][0]
    tableRef.value?.scrollToRow?.(row, column)
    tableRef.value?.setSelectCell?.(row, column)
    callback(new Error(rule.message))
    return
  }
  callback()
}
const formRules = ref<FormRules<{ skcInfoDetailResp: any }>>({
  skcInfoDetailResp: [
    {
      asyncValidator: skcInfoValidator
    }
  ]
})

const router = useRouter()
const route = useRoute()

const disabled = computed(() => {
  return props.isView || props.initPane !== props.currentPane
})

const isEditRatio = computed(() => {
  return route.query.type === 'ratio'
})

const taskNode = computed<ProductTaskNodeEnum>(() =>
  isEditRatio.value
    ? ProductTaskNodeEnum.MATERIAL_PERCENT_INFORMATION
    : ProductTaskNodeEnum.MATERIAL_INFORMATION
)

const handleEditMaterial = () => {
  if (formData.value.oldData === +YesNoEnum.Y) {
    ElMessage.error('历史数据不允许修改')
    return
  }
  router.push({
    name: 'EditProduct',
    query: {
      id: formData.value.id,
      pane: 'MaterialInfo'
    }
  })
  // location.href = router.resolve({
  //   name: 'EditProduct',
  //   query: {
  //     id: formData.value.id,
  //     pane: 'MaterialInfo'
  //   }
  // }).href
}

const handleEditMaterialRatio = () => {
  if (formData.value.oldData === +YesNoEnum.Y) {
    ElMessage.error('历史数据不允许修改')
    return
  }
  router.push({
    name: 'EditProduct',
    query: {
      id: formData.value.id,
      pane: 'MaterialInfo',
      type: 'ratio'
    }
  })
  // location.href = router.resolve({
  //   name: 'EditProduct',
  //   query: {
  //     id: formData.value.id,
  //     pane: 'MaterialInfo',
  //     type: 'ratio'
  //   }
  // }).href
}

const submit = async (type: SaveTypeEnum) => {
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) {
    return false
  }
  const [error, result] = await createProductInfo({
    ...getCommonParams(),
    operateBottom: type,
    taskNode: taskNode.value,
    autoSendWms: true
  })
  if (!error && result) {
    ElMessage.success(result.msg || '操作成功')
    useClosePage('ProductLibrary', {
      state: {
        productNumber: formData.value.productNumber
      }
    })
    return true
  }
  return false
}

const tableRef = ref<InstanceType<typeof AddMaterial>>()

defineExpose({
  submit
})
</script>

<template>
  <ElForm
    ref="formRef"
    :disabled="disabled"
    :model="formData"
    :rules="formRules"
    :scroll-into-view-options="{ behavior: 'smooth' }"
    label-width="auto"
    scroll-to-error
  >
    <ElFormItem class="material-form-item" label-width="0" prop="skcInfoDetailResp">
      <AddMaterial
        v-if="!isEditRatio && !disabled"
        ref="tableRef"
        :form-data="formData"
        :type="MaterialTypeEnum.SELECTED"
        :isEdit="props.isEdit"
      />
      <AddMaterialRatio
        v-else
        ref="tableRef"
        :disabled="disabled"
        :form-data="formData"
        :type="MaterialTypeEnum.SELECTED"
        :isEdit="props.isEdit"
      />
    </ElFormItem>
  </ElForm>
  <Teleport v-if="mounted" to=".product-info-footer">
    <span v-show="currentPane === 'MaterialInfo' && disabled">
      <ElButton
        v-hasPermi="['viewProduct:configMaterial', 'editProduct:configMaterial']"
        @click="handleEditMaterial"
      >
        修改材质信息
      </ElButton>
      <ElButton
        v-hasPermi="['viewProduct:configMaterialRatio', 'editProduct:configMaterialRatio']"
        @click="handleEditMaterialRatio"
        type="primary"
      >
        修改材质占比
      </ElButton>
    </span>
  </Teleport>
</template>

<style lang="less" scoped>
:deep(.material-form-item.is-error) {
  .el-input__wrapper,
  .el-select__wrapper {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
  }
}
</style>
