<script lang="ts" setup>
import { ElMessage, FormRules } from 'element-plus'
import {
  createProductInfo,
  CreateProductInfoAPI,
  Emit,
  FormModel,
  getProductInfo,
  Props,
  SaveTypeEnum
} from '@/views/basic-library-manage/product-library/api/productInfo'
import { ProductSelectResultEnum, ProductTaskNodeEnum } from '../const'
import { sendToWMS } from './SendToWMS'
import { StatusEnum, YesNoEnum } from '../../const'
import { getSizeValueById } from '../../size-library/api/sizeInfo'
import { isEqual } from 'lodash-es'
import { Icon } from '@/components/Icon'
import { ModelListPageAPI } from '@/views/basic-library-manage/model-library/api/model-list'
import ModelInfoDialog from '@/views/basic-library-manage/components/ModelInfoDialog.vue'
import { FactoryListAPI, getFactoryList } from '../api/distributeInfoDialog'
import { useProductInfo } from '@/views/basic-library-manage/product-library/components/helper'
import SkuPartnerCodeDialog from '@/views/basic-library-manage/product-library/components/SkuPartnerCodeDialog.vue'
import { useSizeOptions } from '@/utils/useSizeOptions'
import { SizeCodeTypeEnums } from '@/enums'

defineOptions({
  name: 'SelectionInfo'
})

const props = defineProps<Props>()

const emit = defineEmits<Emit>()
const activeNames = ref(['1', '2'])

const { formData, formRef, getCommonParams } = useProductInfo(props, emit)

const factoryList = ref<FactoryListAPI.Data[]>([])
const formRules = computed<FormRules<Omit<FormModel, 'skcInfoDetailResp'>>>(() => ({
  combatTeam: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择CT归属',
      trigger: 'change'
    }
  ],
  meetingResult: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择选品会结果',
      trigger: 'change'
    }
  ],
  selectionMeetingReviewOpinion: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请输入选品会评审意见',
      trigger: 'blur'
    }
  ],
  foldable: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择是否折叠',
      trigger: 'change'
    }
  ],
  chooseChannel: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择选品渠道',
      trigger: 'change'
    }
  ],
  mainChannelMark: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择主渠道标识',
      trigger: 'change'
    }
  ],
  selectedColor: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择SKC',
      trigger: 'change'
    }
  ],
  selectedSize: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择选中尺码',
      trigger: 'change'
    }
  ],
  copyright: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择是否需要侵权排查',
      trigger: 'change'
    }
  ],
  noCpyrightReason: [
    {
      required:
        formData.value.meetingResult === ProductSelectResultEnum.SELECTED &&
        formData.value.copyright === YesNoEnum.N,
      message: '请输入无需排查原因',
      trigger: 'blur'
    }
  ],
  assignedFactory: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择大货供应商',
      trigger: 'change'
    }
  ],
  productLaunchSeason: [
    {
      required: formData.value.meetingResult === ProductSelectResultEnum.SELECTED,
      message: '请选择商品上市季节',
      trigger: 'change'
    }
  ]
}))

// 是否已下单 或者 历史数据
const partialDisabled = computed(() => {
  return props.isOldData || formData.value.orderPlaced === YesNoEnum.Y
})
const disabled = computed(() => {
  return props.isView || props.initPane !== props.currentPane
})

const selectedColor = computed(
  () =>
    formData.value.skcInfoDetailResp?.filter(
      (e) => e.selectable === ProductSelectResultEnum.SELECTED
    ) || []
)

const handleInputChannelSalesNum = (
  row: CreateProductInfoAPI.SKCInfo,
  channel: string,
  val?: number
) => {
  if (!row.channelSales) {
    row.channelSales = []
  }
  const channelSales = row.channelSales.find((e) => e.salesChannel === channel)
  if (channelSales) {
    channelSales.salesNum = val
  } else {
    row.channelSales.push({ salesChannel: channel, salesNum: val })
  }
}

const taskNode = ref<ProductTaskNodeEnum>(ProductTaskNodeEnum.PRODUCT_CONCLUSION)

const router = useRouter()

const formatParams = (datas: CreateProductInfoAPI.Params) => {
  const { assignedFactory, ...rest } = datas
  // 大货供应商
  const _assignedFactory =
    typeof assignedFactory === 'string'
      ? (assignedFactory as unknown as string).split(',').filter(Boolean)
      : []
  return {
    ...rest,
    assignedFactory: _assignedFactory
  }
}

const handleAssignedFactoryChange = (value: string[]) => {
  const factoryIds = value || []
  formData.value.assignedFactoryItemName = factoryList.value
    .filter((item) => factoryIds.includes(item.vendorId as unknown as string))
    .map((item) => item.vendorName)
    .join()
}

const submit = async (type: SaveTypeEnum) => {
  const valid = await formRef.value?.validate().catch(() => false)
  if (valid) {
    const validVelvet =
      selectedColor.value.length && selectedColor.value.every((e) => e.velvetApplied)
    if (!validVelvet && formData.value.meetingResult === ProductSelectResultEnum.SELECTED) {
      ElMessage.warning('请填写植绒信息')
      return false
    }
    if (formData.value.meetingResult === ProductSelectResultEnum.SELECTED) {
      const res = await sendToWMS(formData.value, sizeValueList.value)
      if (!res) {
        return false
      }
      formData.value.wmsCategoryId = res[0].wmsCategoryId
      const [error, result] = await createProductInfo({
        ...getCommonParams(),
        operateBottom: type,
        taskNode: taskNode.value,
        sendWms: YesNoEnum.Y as string,
        autoSendWms: false
      })
      if (!error && result) {
        const [err, res] = await getProductInfo(result.datas)
        if (!err && res) {
          formData.value = formatParams(res.datas)
          if (formData.value.stylePartnerCode) {
            skuPartnerCodeDialogVisible.value = true
            return
          }
        }
      }
    }
    const [err, res] = await createProductInfo({
      ...getCommonParams(),
      operateBottom: type,
      taskNode: taskNode.value,
      sendWms: YesNoEnum.Y as string,
      autoSendWms: true
    })
    if (!err && res) {
      ElMessage.success(res.msg || '操作成功')
      useClosePage('ProductLibrary', {
        state: {
          productNumber: formData.value.productNumber
        }
      })
      return true
    } else {
      formData.value.skuList = []
    }
  }
  return false
}

const handleEditSelectionInfo = () => {
  router.push({
    name: 'EditProduct',
    query: {
      id: formData.value.id,
      pane: props.currentPane
    }
  })
  // location.href = router.resolve({
  //   name: 'EditProduct',
  //   query: {
  //     id: formData.value.id,
  //     pane: props.currentPane
  //   }
  // }).href
}

// 获取供应商
const fetchFactoryList = async () => {
  const [error, result] = await getFactoryList()
  if (!error && result) {
    factoryList.value = result.datas || []
  }
}
const { sizeOptions, fetchSizeList } = useSizeOptions(SizeCodeTypeEnums.PRODUCT)
fetchSizeList()

const sizeValueList = computed(() => {
  if (!formData.value.sizeRangeId?.length) {
    return []
  }
  const sizeObj = sizeOptions.value.filter((e) => formData.value.sizeRangeId?.includes(e.id!))

  return sizeObj
    .filter((e) => e.children?.length)
    .map((e) =>
      e.children!.map((size) => ({
        label: `${size.label}【${e.label}】`,
        value: size.id,
        disabled: size.disabled
      }))
    )
    .flat()
})

const handleSizeRangeIdChange = async () => {
  if (!formData.value.sizeRangeId?.length) {
    formData.value.selectedSize = []
    return
  }
  const [error, result] = await getSizeValueById({ sizeId: formData.value.sizeRangeId })
  if (!error && result?.datas) {
    formData.value.selectedSize = result.datas
      .filter((e) => e.status === StatusEnum.START)
      .map((e) => e.id!)
  } else {
    formData.value.selectedSize = []
  }
}

const onMountedFn = async () => {
  const selectedSize = formData.value.selectedSize
  formData.value.productLaunchSeason =
    formData.value.productLaunchSeason || formData.value.launchSeason
  await handleSizeRangeIdChange()
  formData.value.selectedSize = selectedSize
}

watchOnce(() => formData.value.sizeRangeId, onMountedFn)

onMounted(onMountedFn)

const meetingResultDisabled = ref(true)
const assignedFactoryDisabled = ref(true)

const handleSelectedColorChange = (val: CreateProductInfoAPI.SKCInfo[]) => {
  formData.value.selectedColor = val.map((e) => e.colorId!)
  formData.value.skcInfoDetailResp = formData.value.skcInfoDetailResp?.map((e) => ({
    ...e,
    selectable: val.some((c) => c.id === e.id)
      ? ProductSelectResultEnum.SELECTED
      : ProductSelectResultEnum.UNSELECTED
  }))
}
const bindSelectedColor = ref<CreateProductInfoAPI.SKCInfo[]>([])
watchEffect(() => {
  if (disabled.value) {
    bindSelectedColor.value = (formData.value.skcInfoDetailResp || []).filter(
      (e) => e.selectable === ProductSelectResultEnum.SELECTED
    )
  }
})

watchOnce(
  () => formData.value,
  () => {
    meetingResultDisabled.value = formData.value.meetingResult === ProductSelectResultEnum.SELECTED
    // 1 禁用 0 不禁用
    if (partialDisabled.value) {
      formData.value.skcInfoDetailResp = formData.value.skcInfoDetailResp?.map((e) => ({
        ...e,
        disabled: e.newAddColor === +YesNoEnum.N
      }))
    }
    assignedFactoryDisabled.value = formData.value.sendFactory === +YesNoEnum.Y
  }
)

watch(
  () => formData.value.skcInfoDetailResp,
  (oldVal, newVal) => {
    if (isEqual(oldVal, newVal)) {
      return
    }
    bindSelectedColor.value = (formData.value.skcInfoDetailResp || []).filter(
      (e) => e.selectable === ProductSelectResultEnum.SELECTED
    )
    handleSelectedColorChange(bindSelectedColor.value)
  }
)

onMounted(() => {
  fetchFactoryList()
})

const modelInfoDialogVisible = ref(false)
const handleOpenModelInfoDialog = () => {
  if (disabled.value) return
  modelInfoDialogVisible.value = true
}
const handlePickModel = (val: ModelListPageAPI.Row) => {
  formData.value.modelId = val.id
  formData.value.modelIdItemName = val.code
}

const skuPartnerCodeDialogVisible = ref(false)

defineExpose({
  submit
})
</script>

<template>
  <ModelInfoDialog v-model="modelInfoDialogVisible" @submit="handlePickModel" />
  <ElForm
    ref="formRef"
    :disabled="disabled"
    :model="formData"
    :rules="formRules"
    :scroll-into-view-options="{ behavior: 'smooth' }"
    label-width="auto"
    scroll-to-error
  >
    <ElRow :gutter="20">
      <ElCol :span="12">
        <ElFormItem label="选品会结果" prop="meetingResult">
          <SelectPlus
            v-model="formData.meetingResult"
            :disabled="meetingResultDisabled"
            api-key="COMMON_PASS_FAIL"
            cache
            clearable
            filterable
            placeholder="请选择选品会结果"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="选品会评审意见" prop="selectionMeetingReviewOpinion">
          <ElInput
            v-model="formData.selectionMeetingReviewOpinion"
            :autosize="{ minRows: 4, maxRows: 4 }"
            :disabled="partialDisabled"
            maxlength="500"
            show-word-limit
            type="textarea"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="是否折叠" prop="foldable">
          <SelectPlus
            v-model="formData.foldable"
            :disabled="partialDisabled"
            api-key="COMMON_YES_NO"
            cache
            radio
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="选品渠道" prop="chooseChannel">
          <SelectPlus
            v-model="formData.chooseChannel"
            :disabled="partialDisabled"
            api-key="PRODUCT_DEV_CHANNELS"
            cache
            clearable
            filterable
            multiple
            placeholder="请选择选品渠道"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="尺码段" prop="sizeRangeId">
          <SelectPlus
            v-model="formData.sizeRangeId"
            :data-method="
              (val) => val.map((e) => ({ ...e, disabled: e.status !== StatusEnum.START }))
            "
            api-key="sizeList"
            clearable
            disabled
            filterable
            multiple
            placeholder="请选择尺码段"
            @update:modle-value="handleSizeRangeIdChange"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="预估上架日期" prop="predictLaunchDate">
          <ElDatePicker
            v-model="formData.predictLaunchDate"
            :disabled="partialDisabled"
            clearable
            placeholder="请选择预估上架日期"
            value-format="YYYY-MM-DD"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="主渠道标识" prop="mainChannelMark">
          <SelectPlus
            v-model="formData.mainChannelMark"
            :disabled="partialDisabled"
            api-key="PRODUCT_CHANNEL_IDENTIFICATION"
            cache
            clearable
            filterable
            placeholder="请选择主渠道标识"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="选择SKC" prop="selectedColor">
          <ElSelect
            v-model="bindSelectedColor"
            clearable
            filterable
            multiple
            placeholder="请选择SKC"
            value-key="id"
            @change="handleSelectedColorChange"
          >
            <ElOption
              v-for="item in formData.skcInfoDetailResp"
              :key="item.id"
              :disabled="item.disabled"
              :label="`${item.colorCodeItemName}${
                item.mainFabricItemName ? `/${item.mainFabricItemName}` : ''
              }`"
              :value="item"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="CT归属" prop="combatTeam">
          <SelectPlus
            v-model="formData.combatTeam"
            :disabled="partialDisabled"
            api-key="COMBAT_TEAM"
            clearable
            filterable
            placeholder="请选择CT归属"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="选中尺码" prop="selectedSize">
          <ElSelect
            v-model="formData.selectedSize"
            :disabled="partialDisabled"
            clearable
            filterable
            multiple
            placeholder="请选择选中尺码"
          >
            <ElOption
              v-for="item in sizeValueList"
              :key="item.value"
              :disabled="item.disabled"
              :label="item.label"
              :value="item.value!"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="技术内审记录" prop="techReviewRecordUrl">
          <OssUpload
            v-model="formData.techReviewRecordUrl"
            :disabled="partialDisabled"
            :limit="20"
            :size-limit="1024 * 1024 * 100"
            list-type="text"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="是否需要侵权排查" prop="copyright">
          <SelectPlus
            v-model="formData.copyright"
            :disabled="partialDisabled"
            api-key="COMMON_YES_NO"
            cache
            radio
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="商品上市季节" prop="productLaunchSeason">
          <SelectPlus
            v-model="formData.productLaunchSeason"
            :disabled="partialDisabled"
            api-key="COMMON_MARKET_SEASON"
            cache
            clearable
            filterable
            placeholder="请选择商品上市季"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="关联型体" prop="modelId">
          <ElInput
            v-model="formData.modelIdItemName"
            :disabled="partialDisabled"
            placeholder="请选择"
            readonly
          >
            <template #prefix>
              <Icon
                :size="26"
                class="cursor-pointer"
                color="#409EFF"
                icon="mdi:search"
                @click="handleOpenModelInfoDialog"
              />
            </template>
          </ElInput>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="大货供应商" prop="assignedFactory">
          <ElSelect
            v-model="formData.assignedFactory"
            :disabled="assignedFactoryDisabled || partialDisabled"
            clearable
            collapse-tags
            collapse-tags-tooltip
            filterable
            multiple
            placeholder="请选择大货供应商"
            @change="handleAssignedFactoryChange"
          >
            <ElOption
              v-for="item in factoryList"
              :key="item.vendorCode"
              :disabled="item.useStatus !== +YesNoEnum.Y"
              :label="item.vendorName"
              :value="item.vendorId!"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol
        v-if="
          formData.meetingResult === ProductSelectResultEnum.SELECTED &&
          formData.copyright === YesNoEnum.N
        "
        :span="12"
      >
        <ElFormItem label="无需排查原因" prop="noCpyrightReason">
          <ElInput
            v-model="formData.noCpyrightReason"
            :disabled="partialDisabled"
            maxlength="500"
            placeholder="请输入无需排查原因"
            show-word-limit
          />
        </ElFormItem>
      </ElCol>
    </ElRow>
    <ElCollapse v-model="activeNames">
      <ElCollapseItem name="1">
        <template #title>
          <div class="font-bold text-base">植绒信息判定</div>
        </template>
        <VxeTable ref="tableRef" :data="selectedColor" :max-height="500" class="w-full">
          <VxeColumn title="序号" type="seq" width="60" />
          <VxeColumn field="skcCode" title="SKC编号" width="auto" />
          <VxeColumn
            :cell-render="{ name: 'Image' }"
            field="erpSkcImgUrl"
            title="白底图"
            width="120"
          />
          <VxeColumn field="colorCodeItemName" title="选中颜色" />
          <VxeColumn field="wmsColorName" title="WMS色号名称" />
          <VxeColumn field="mainFabricItemName" title="主要面料" />
          <VxeColumn field="velvetApplied" title="是否植绒">
            <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
              <SelectPlus
                v-model="row.velvetApplied"
                :disabled="partialDisabled && row.newAddColor === +YesNoEnum.N"
                api-key="FLOCKING_YES_NO"
                cache
              />
            </template>
          </VxeColumn>
          <VxeColumn field="velvetRequirements" title="植绒要求">
            <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
              <ElInput
                v-model="row.velvetRequirements"
                :disabled="partialDisabled && row.newAddColor === +YesNoEnum.N"
                clearable
                maxlength="100"
                placeholder="请输入"
                show-word-limit
              />
            </template>
          </VxeColumn>
          <VxeColumn field="dataStatusItemName" title="状态" width="80" />
          <VxeColumn field="velvetRemark" title="备注">
            <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
              <ElInput
                v-model="row.velvetRemark"
                :disabled="partialDisabled && row.newAddColor === +YesNoEnum.N"
                clearable
                maxlength="500"
                placeholder="请输入"
                show-word-limit
              />
            </template>
          </VxeColumn>
        </VxeTable>
      </ElCollapseItem>
      <ElCollapseItem name="2" title="预估订单量汇总">
        <template #title>
          <div class="font-bold text-base">预估订单量汇总</div>
        </template>
        <VxeTable
          ref="tableRef"
          :column-config="{ resizable: true }"
          :data="selectedColor"
          :max-height="500"
          :show-header-overflow="false"
          :show-overflow="false"
          class="w-full"
        >
          <VxeColumn title="序号" type="seq" width="60" />
          <VxeColumn field="skcCode" min-width="100" title="SKC编号" />
          <VxeColumn field="colorCodeItemName" min-width="100" title="选中颜色/渠道" />
          <VxeColumn field="mainFabricItemName" min-width="100" title="主要面料" />
          <VxeColumn
            v-for="item in formData.chooseChannel"
            :key="item"
            :title="item"
            min-width="165"
          >
            <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
              <ElInputNumber
                :controls="false"
                :disabled="partialDisabled && row.newAddColor === +YesNoEnum.N"
                :min="0"
                :model-value="row.channelSales?.find((e) => e.salesChannel === item)?.salesNum || 0"
                :step="1"
                :value-on-clear="null"
                step-strictly
                @change="(val) => handleInputChannelSalesNum(row, item, val)"
              />
            </template>
          </VxeColumn>
          <VxeColumn field="totalSalesNum" min-width="100" title="订单量汇总">
            <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
              <span>
                {{ row.channelSales?.reduce((sum, e) => sum + (e.salesNum || 0), 0) || 0 }}
              </span>
            </template>
          </VxeColumn>
        </VxeTable>
      </ElCollapseItem>
    </ElCollapse>
  </ElForm>
  <SkuPartnerCodeDialog v-model="skuPartnerCodeDialogVisible" :form-data="formData" />
  <Teleport v-if="mounted" to=".product-info-footer">
    <span v-show="currentPane === 'SelectionInfo' && disabled">
      <ElButton
        v-hasPermi="['viewProduct:selectionProduct', 'editProduct:selectionProduct']"
        @click="handleEditSelectionInfo"
        type="primary"
      >
        修改选品会信息
      </ElButton>
    </span>
  </Teleport>
</template>

<style lang="less" scoped></style>
