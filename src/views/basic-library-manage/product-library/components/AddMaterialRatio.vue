<script lang="ts" setup>
import { ref } from 'vue'
import { CreateProductInfoAPI, FormModel } from '../api/productInfo'
import { VxeTableDefines, VxeTableInstance, VxeTablePropTypes } from 'vxe-table'
import { ElMessage } from 'element-plus'
import { MaterialCategoryEnum } from '../../const'
import { MaterialTypeEnum } from '../const'
import { cloneDeep, isNil, pick } from 'lodash-es'
import { useMaterial } from '@/views/basic-library-manage/sample-manage/components/hooks'
import { getValuesByKey } from '@/utils'
import { ProductDataStatusEnums } from '@/enums'

interface Props {
  formData: FormModel
  type?: MaterialTypeEnum
  disabled?: boolean
  isEdit?: boolean
}

const props = defineProps<Props>()

const isSelected = computed(() => props.type === MaterialTypeEnum.SELECTED)

const { formData } = toRefs(props)
const tableRef = ref<VxeTableInstance>()
const tableData = computed({
  get() {
    const skcInfoDetailResp = formData.value.skcInfoDetailResp || []
    if (!isSelected.value) {
      return skcInfoDetailResp
    }
    return skcInfoDetailResp.filter((e) => {
      if (!props.isEdit) {
        // view 展示 生效 草稿 作废
        return true
      }
      // edit 展示 生效  草稿
      return (
        ProductDataStatusEnums.Effective === e.dataStatus ||
        ProductDataStatusEnums.Draft === e.dataStatus
      )
    })
  },
  set(skcInfoDetailResp) {
    skcInfoDetailResp.forEach((item) => {
      const currentItem = formData.value.skcInfoDetailResp?.find((e) => e.id === item.id)
      if (currentItem) {
        Object.assign(currentItem, item)
      }
    })
  }
})

const sumValidator: VxeTableDefines.ValidatorRule['validator'] = ({
  row
}: {
  row: CreateProductInfoAPI.SKCInfo
}) => {
  const total =
    +(row.percentCorium || 0) +
    +(row.percentPu || 0) +
    +(row.percentOther || 0) +
    +(row.percentTextile || 0)
  return new Promise((resolve, reject) => {
    if (total !== 100) {
      reject(new Error('橡胶/塑料、真皮、纺织物、其他占比应为100%'))
      return
    }
    resolve()
  })
}

const tableRules = ref<VxeTablePropTypes.EditRules>({
  paddingMaterialSurfaceCode: [
    {
      required: true,
      content: '请选择垫材料',
      trigger: 'blur'
    }
  ],
  soleMaterialOutsoleCode: [
    {
      required: true,
      content: '请选择底材料',
      trigger: 'blur'
    }
  ],
  percentPu: [
    {
      validator: sumValidator
    }
  ],
  percentCorium: [
    {
      validator: sumValidator
    },
    {
      validator({ row }: { row: CreateProductInfoAPI.SKCInfo }) {
        return new Promise((resolve, reject) => {
          if (!row.coriumMaterialCode && +(row.percentCorium || 0) > 0) {
            reject(new Error('请选择真皮材料'))
          }
          resolve()
        })
      }
    }
  ],
  percentTextile: [
    {
      validator: sumValidator
    }
  ],
  percentOther: [
    {
      validator: sumValidator
    },
    {
      validator({ row }: { row: CreateProductInfoAPI.SKCInfo }) {
        return new Promise((resolve, reject) => {
          if (!row.otherMaterialCode && +(row.percentOther || 0) > 0) {
            reject(new Error('请选择其他材料'))
          }
          resolve()
        })
      }
    }
  ]
})

const editField = ref('percentPu')
const isEditCorium = computed(() => editField.value === 'percentCorium')
const ratio = ref()
const material = ref()

watch(
  () => editField.value,
  () => {
    ratio.value = null
    material.value = undefined
  }
)

const {
  materialCascaderProps,
  materialCategoryList,
  commonMaterialCategoryList,
  liningMaterialCategoryList,
  outsoleMaterialCategoryList,
  insoleMaterialCategoryList
} = useMaterial()

// 皮料
const leatherMaterialCategoryList = computed(() => {
  const commonList: string[] = [MaterialCategoryEnum.LEATHER]
  return materialCategoryList.value.filter((e) => commonList.includes(e.selectorEnValue!))
})

// 其他材料
const otherMaterialCategoryList = computed(() => {
  const commonList: string[] = [MaterialCategoryEnum.OTHERS, MaterialCategoryEnum.ACC]
  return materialCategoryList.value.filter((e) => commonList.includes(e.selectorEnValue!))
})

const handleSetMaterial = () => {
  const selected = tableRef.value?.getCheckboxRecords() || []
  if (!selected.length) {
    ElMessage.warning('请先勾选数据')
    return
  }
  if (isNil(ratio.value) && !material.value) {
    ElMessage.warning('请先输入材质占比或选择材料')
    return
  }
  selected.forEach((e) => {
    e[editField.value] = ratio.value
    if (
      (editField.value === 'percentCorium' || editField.value === 'percentOther') &&
      material.value
    ) {
      e[isEditCorium.value ? 'coriumMaterialCode' : 'otherMaterialCode'] = material.value
    }
  })
  tableRef.value?.loadData(tableData.value)
}

const handleSyncFirst = () => {
  const firstRow = tableData.value?.[0]
  if (!firstRow) return
  const row = pick(firstRow, [
    'percentPu',
    'coriumMaterialCode',
    'percentCorium',
    'percentTextile',
    'otherMaterialCode',
    'percentOther'
  ])

  tableData.value =
    tableData.value?.map((item) => {
      return {
        ...item,
        ...cloneDeep(row)
      }
    }) || []
}

const handleResetMaterial = () => {
  const selected = tableRef.value?.getCheckboxRecords() || []
  if (!selected.length) {
    ElMessage.warning('请先勾选数据')
    return
  }
  selected.forEach((e) => {
    e[editField.value] = null
  })
}

const validate = computed(() => tableRef.value?.validate)
const scrollToRow = computed(() => tableRef.value?.scrollToRow)
const setSelectCell = computed(() => tableRef.value?.setSelectCell)

defineExpose({
  validate,
  scrollToRow,
  setSelectCell
})
</script>

<template>
  <div v-if="!disabled" class="mb-2 w-full flex">
    <h3 class="whitespace-nowrap">批量修改属性</h3>
    <ElSelect
      v-model="editField"
      :validate-event="false"
      class="mx-2 !w-48"
      filterable
      placeholder="请选择"
    >
      <ElOption label="橡胶/塑料%" value="percentPu" />
      <ElOption label="真皮%" value="percentCorium" />
      <ElOption label="纺织物%" value="percentTextile" />
      <ElOption label="其他%" value="percentOther" />
    </ElSelect>
    <ElCascader
      v-if="editField === 'percentCorium' || editField === 'percentOther'"
      v-model="material"
      :options="isEditCorium ? leatherMaterialCategoryList : otherMaterialCategoryList"
      :props="materialCascaderProps"
      :validate-event="false"
      clearable
      filterable
    />
    <ElInputNumber
      v-model="ratio"
      :controls="false"
      :min="0"
      :precision="0"
      :value-on-clear="null"
      class="!w-24 mx-2"
    />
    <ElButton type="primary" @click="handleSetMaterial">一键设置</ElButton>
    <ElButton v-if="isSelected" type="primary" @click="handleSyncFirst">一键设置同第一行</ElButton>
    <ElButton @click="handleResetMaterial">重置</ElButton>
  </div>
  <VxeTable
    ref="tableRef"
    :cell-config="{ height: 80 }"
    :data="tableData"
    :edit-config="{ showIcon: false, mode: 'cell', trigger: 'click' }"
    :edit-rules="tableRules"
    :max-height="500"
    :scroll-x="{ enabled: true }"
    :scroll-y="{ enabled: true, gt: 0, mode: 'wheel' }"
    :show-overflow="false"
    :valid-config="{ showMessage: false, autoPos: true }"
    class="w-full"
  >
    <VxeColumn fixed="left" type="checkbox" width="40" />
    <VxeColumn fixed="left" title="序号" type="seq" width="60" />
    <VxeColumn field="skcCode" fixed="left" min-width="180" title="SKC编号" />
    <VxeColumn field="dataStatusItemName" fixed="left" title="状态" width="100" />
    <VxeColumn field="colorCodeItemName" fixed="left" min-width="180" title="选中颜色" />
    <VxeColumn
      :cell-render="{ name: 'Image' }"
      field="thumbnail"
      fixed="left"
      title="颜色缩略图"
      width="120"
    />
    <VxeColumn field="mainFabric" fixed="left" min-width="120" title="主要面料">
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <span v-if="isSelected">{{ row.mainFabricItemName }}</span>
        <ElCascader
          v-else
          v-model="row.mainFabric"
          :disabled="!!row.id"
          :options="materialCategoryList"
          :props="materialCascaderProps"
          :validate-event="false"
          clearable
          filterable
        />
      </template>
    </VxeColumn>
    <template v-if="disabled">
      <VxeColumn
        v-for="item in 4"
        :key="item"
        :edit-render="{}"
        :field="`materialInfoList.${item - 1}.material`"
        :title="'面料' + item"
        min-width="180"
      >
        <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
          <span>
            {{
              getValuesByKey(commonMaterialCategoryList, {
                targetValues: row.materialInfoList?.[item - 1].material
              })
            }}
          </span>
        </template>
        <template #edit="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
          <span>
            {{
              getValuesByKey(commonMaterialCategoryList, {
                targetValues: row.materialInfoList?.[item - 1].material
              })
            }}
          </span>
          <ElCascader
            :model-value="row.materialInfoList?.[item - 1].material"
            :options="commonMaterialCategoryList"
            :persistent="false"
            :props="materialCascaderProps"
            :validate-event="false"
            class="vxe-table--ignore-clear"
            clearable
            filterable
            popper-class="vxe-table--ignore-clear"
            @change="
              (val: number) => {
                if (row.materialInfoList) {
                  row.materialInfoList[item - 1].material = val
                }
              }
            "
          />
        </template>
      </VxeColumn>
      <VxeColumn :edit-render="{}" field="liningMaterialCode" min-width="180" title="里材料">
        <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
          <span>
            {{
              getValuesByKey(liningMaterialCategoryList, { targetValues: row.liningMaterialCode })
            }}
          </span>
        </template>
        <template #edit="{ row }">
          <ElCascader
            v-model="row.liningMaterialCode"
            :clearable="false"
            :options="liningMaterialCategoryList"
            :persistent="false"
            :props="{ ...materialCascaderProps, multiple: true }"
            :validate-event="false"
            class="vxe-table--ignore-clear"
            filterable
            popper-class="vxe-table--ignore-clear"
          />
        </template>
      </VxeColumn>
      <VxeColumn :edit-render="{}" field="liningSituation" min-width="180" title="里绒材料">
        <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
          <span>
            {{ getValuesByKey(materialCategoryList, { targetValues: row.liningSituation }) }}
          </span>
        </template>
        <template #edit="{ row }">
          <ElCascader
            v-model="row.liningSituation"
            :clearable="false"
            :options="materialCategoryList"
            :persistent="false"
            :props="{ ...materialCascaderProps, multiple: true }"
            :validate-event="false"
            class="vxe-table--ignore-clear"
            filterable
            popper-class="vxe-table--ignore-clear"
          />
        </template>
      </VxeColumn>
      <VxeColumn
        :edit-render="{}"
        field="paddingMaterialSurfaceCode"
        min-width="180"
        title="垫材料"
      >
        <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
          <span>
            {{
              getValuesByKey(insoleMaterialCategoryList, {
                targetValues: row.paddingMaterialSurfaceCode
              })
            }}
          </span>
        </template>
        <template #edit="{ row }">
          <ElCascader
            v-model="row.paddingMaterialSurfaceCode"
            :clearable="false"
            :options="insoleMaterialCategoryList"
            :persistent="false"
            :props="{ ...materialCascaderProps, multiple: true }"
            :validate-event="false"
            class="vxe-table--ignore-clear"
            filterable
            popper-class="vxe-table--ignore-clear"
          />
        </template>
      </VxeColumn>
      <VxeColumn :edit-render="{}" field="soleMaterialOutsoleCode" min-width="180" title="底材料">
        <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
          <span>
            {{
              getValuesByKey(outsoleMaterialCategoryList, {
                targetValues: row.soleMaterialOutsoleCode
              })
            }}
          </span>
        </template>
        <template #edit="{ row }">
          <ElCascader
            v-model="row.soleMaterialOutsoleCode"
            :clearable="false"
            :options="outsoleMaterialCategoryList"
            :persistent="false"
            :props="{ ...materialCascaderProps, multiple: true }"
            :validate-event="false"
            filterable
            popper-class="vxe-table--ignore-clear"
          />
        </template>
      </VxeColumn>
    </template>
    <VxeColumn
      field="percentPu"
      header-class-name="header-required"
      min-width="180"
      title="橡胶/塑料%"
    >
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <ElInputNumber
          v-model="row.percentPu"
          :controls="false"
          :max="100"
          :min="0"
          :precision="0"
          :validate-event="false"
          :value-on-clear="0"
          class="!w-24"
        />
      </template>
    </VxeColumn>
    <VxeColumn
      field="percentCorium"
      header-class-name="header-required"
      min-width="260"
      title="真皮%"
    >
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <div class="flex items-center">
          <ElCascader
            v-model="row.coriumMaterialCode"
            :options="leatherMaterialCategoryList"
            :props="materialCascaderProps"
            :validate-event="false"
            class="!w-32"
            clearable
            filterable
          />
          <ElInputNumber
            v-model="row.percentCorium"
            :controls="false"
            :max="100"
            :min="0"
            :precision="0"
            :validate-event="false"
            :value-on-clear="0"
            class="!w-24 ml-2"
          />
        </div>
      </template>
    </VxeColumn>
    <VxeColumn
      field="percentTextile"
      header-class-name="header-required"
      min-width="180"
      title="纺织物%"
    >
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <ElInputNumber
          v-model="row.percentTextile"
          :controls="false"
          :max="100"
          :min="0"
          :precision="0"
          :validate-event="false"
          :value-on-clear="0"
          class="!w-24"
        />
      </template>
    </VxeColumn>
    <VxeColumn
      field="percentOther"
      header-class-name="header-required"
      min-width="260"
      title="其他%"
    >
      <template #default="{ row }: { row: CreateProductInfoAPI.SKCInfo }">
        <div class="flex items-center">
          <ElCascader
            v-model="row.otherMaterialCode"
            :options="otherMaterialCategoryList"
            :props="materialCascaderProps"
            :validate-event="false"
            class="!w-32"
            clearable
            filterable
          />
          <ElInputNumber
            v-model="row.percentOther"
            :controls="false"
            :max="100"
            :min="0"
            :precision="0"
            :validate-event="false"
            :value-on-clear="0"
            class="!w-24 ml-2"
          />
        </div>
      </template>
    </VxeColumn>
  </VxeTable>
</template>

<style lang="less" scoped>
:deep(.col--valid-error .vxe-cell) {
  .el-select__wrapper,
  .el-input__wrapper {
    box-shadow: 0 0 0 1px red inset !important;
  }
}
</style>
