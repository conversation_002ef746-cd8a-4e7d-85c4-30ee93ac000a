<script lang="tsx" setup>
import { Icon } from '@/components/Icon'
import Sortable from 'sortablejs'
import type { VxeTableInstance } from 'vxe-table'
import { ElForm, ElFormItem, ElInput, ElMessage, ElMessageBox } from 'element-plus'
import {
  ColumnListAPI,
  createView,
  EditableEnum,
  getAllColumns,
  getSkcAllColumns,
  VisibleEnum
} from '../api/customColumns'
import type { ViewListAPI } from '../api/product-list'
import { getAllFieldsByType } from '@/api/common'

defineOptions({
  name: 'CustomColumnsDialog'
})

const props = defineProps<{
  modelValue: boolean
  viewList: ViewListAPI.List
  columnType: 'product' | 'skc' | 'sellPoint'
}>()

const viewListComputed = computed(() => {
  // 去除前1条，图形视图
  return props.viewList.slice(1)
})

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh', val: number): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const columnsConfig = ref<ColumnListAPI.Data[]>([])
const tableRef = ref<VxeTableInstance>()

// 拖拽排序后,刷新tableData,保留scroll信息
async function refreshTableConfigData() {
  const tableRefVal = tableRef.value
  if (!tableRefVal) {
    return
  }
  const scrollInfo = tableRefVal.getScroll()
  // tableData设置为空数组,不然不触发重新渲染
  await tableRefVal.loadData([])
  await tableRefVal.loadData(columnsConfig.value)
  await nextTick()
  await tableRefVal.scrollTo(scrollInfo.scrollLeft, scrollInfo.scrollTop)
}

function bindRowDrop() {
  let sortable: Sortable | null = null
  watch(
    () => visible.value,
    async (val) => {
      if (!val) {
        sortable?.destroy()
        return
      }
      await nextTick()
      const { refTableBody } = tableRef.value?.getRefMaps() || {}
      const el = refTableBody?.value?.$el
      const tableBody = el?.querySelector('.vxe-table--body tbody')
      sortable = Sortable.create(tableBody, {
        handle: '.row-drag',
        filter: '.cursor-not-allowed',
        onEnd: async ({ newIndex, oldIndex }) => {
          const dragToNewRow =
            newIndex === oldIndex ||
            newIndex === null ||
            oldIndex === null ||
            !columnsConfig.value ||
            oldIndex === undefined ||
            newIndex === undefined
          if (dragToNewRow) {
            return
          }
          const currRow = columnsConfig.value[oldIndex]
          const nextRow = columnsConfig.value[newIndex]
          if (
            currRow.editable === EditableEnum.NOT_EDITABLE ||
            nextRow.editable === EditableEnum.NOT_EDITABLE
          ) {
            await refreshTableConfigData()
            return
          }
          columnsConfig.value.splice(oldIndex, 1)
          columnsConfig.value.splice(newIndex, 0, currRow!)
          await nextTick()
          await refreshTableConfigData()
        }
      })
    }
  )
}

const config = computed(() => {
  const map = {
    product: {
      api: getAllColumns,
      type: 0
    },
    skc: {
      api: getSkcAllColumns,
      type: 1
    },
    sellPoint: {
      api: getAllFieldsByType,
      type: 3
    }
  }
  return map[props.columnType]
})

const allColumns = ref<ColumnListAPI.Data[]>([])
const fetchAllColumns = async () => {
  const queryApi = config.value.api
  // TODO
  const [error, result] = await queryApi(3)
  if (error === null && result) {
    allColumns.value = result.datas || []
    columnsConfig.value = result.datas || []
  }
}

const formData = ref<ViewListAPI.Row>({
  name: '',
  type: 0,
  fieldAttr: []
})

const selectedColumnView = ref<ViewListAPI.Row>()

watch(
  () => selectedColumnView.value,
  (newVal) => {
    if (!newVal) {
      return
    }
    const savedSortedColumns = Array.isArray(newVal.fieldAttr) ? newVal.fieldAttr : []
    columnsConfig.value = columnsConfig.value
      .map((e) => ({
        ...e,
        visible: e.attrField
          ? savedSortedColumns.includes(e.attrField)
            ? VisibleEnum.VISIBLE
            : VisibleEnum.HIDDEN
          : VisibleEnum.HIDDEN
      }))
      /**
       * selectedColumnView只保留了勾选的数据
       * 因此,排序columnConfig中把勾选的数据提前,再按attrField在view中的index排序
       */
      .toSorted((a, b) => {
        if (a.visible !== b.visible) {
          return a.visible ? 1 : -1
        }
        if (a.attrField && b.attrField) {
          const indexA = savedSortedColumns.indexOf(a.attrField)
          const indexB = savedSortedColumns.indexOf(b.attrField)
          return indexA - indexB
        }

        return 0
      })
  }
)

const handleClose = () => {
  visible.value = false
  selectedColumnView.value = undefined
  columnsConfig.value = allColumns.value
}

const handleSubmit = (type: 'add' | 'edit') => {
  if (type === 'edit') {
    if (!selectedColumnView.value?.id) {
      ElMessage.warning('请选择要修改的视图')
      return
    }
    formData.value.name = selectedColumnView.value?.name
  } else {
    formData.value.name = ''
  }
  const length = columnsConfig.value.filter((e) => e.visible === VisibleEnum.VISIBLE).length
  if (length < 5) {
    ElMessage.warning('至少需要5个字段')
    return
  }
  ElMessageBox({
    title: type === 'add' ? '新增视图' : '修改视图',
    message: (
      <>
        <ElForm>
          <ElFormItem label="视图名称">
            <ElInput v-model={formData.value.name} placeholder="请输入视图名称" />
          </ElFormItem>
        </ElForm>
        <span>本次选择了{length}个字段</span>
      </>
    ),
    draggable: true,
    closeOnClickModal: false,
    beforeClose: async (action, instance, done) => {
      if (action !== 'confirm') {
        done()
        return
      }
      if (!formData.value.name?.trim()) {
        ElMessage.warning('请输入视图名称')
        return
      }
      instance.confirmButtonLoading = instance.cancelButtonLoading = true

      const viewType = config.value.type

      const [error, result] = await createView({
        id: selectedColumnView.value?.id,
        type: viewType,
        fieldAttr: columnsConfig.value
          .filter((e) => e.visible === VisibleEnum.VISIBLE)
          .map((e) => e.attrField!),
        name: formData.value.name
      })
      instance.confirmButtonLoading = instance.cancelButtonLoading = false
      if (error === null && result.datas) {
        ElMessage.success(result.msg)
        emit('refresh', result.datas)
        done()
        handleClose()
      }
    }
  }).catch(() => {})
}

bindRowDrop()
Promise.all([fetchAllColumns()])

defineExpose({
  allColumns
})
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    title="自定义配置视图"
    top="5vh"
    width="700"
  >
    <template #header>
      <div class="text-lg">自定义配置视图</div>
      <span class="text-sm text-gray-500/50">
        列项显示不得少于5项，灰色选中列不支持隐藏和排序
      </span>
    </template>
    <VxeTable ref="tableRef" :data="columnsConfig" :max-height="500" row-id="attrField">
      <VxeColumn title="显示" width="60">
        <template #default="{ row }">
          <VxeCheckbox
            v-model="row.visible"
            :checked-value="VisibleEnum.VISIBLE"
            :disabled="row.editable === EditableEnum.NOT_EDITABLE"
            :unchecked-value="VisibleEnum.HIDDEN"
          />
        </template>
      </VxeColumn>
      <VxeColumn field="attrName" title="列名" />
      <VxeColumn
        class-name="drag-cell"
        field="key"
        header-class-name="drag-header-cell"
        title="拖动调整顺序"
      >
        <template #default="{ row }">
          <span
            :class="
              row.editable === EditableEnum.NOT_EDITABLE ? 'cursor-not-allowed' : 'cursor-move'
            "
            class="row-drag inline-flex"
          >
            <Icon icon="ep:rank" />
          </span>
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElSelect v-model="selectedColumnView" value-key="id">
        <ElOption
          v-for="item in viewListComputed"
          :key="item.id"
          :label="item.name"
          :value="item"
        />
      </ElSelect>
      <ElButton class="ml-4" @click="handleSubmit('edit')"> 修改视图</ElButton>
      <ElButton type="primary" @click="handleSubmit('add')"> 新增视图</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.el-dialog__body) {
  padding-top: 5px;
}
</style>
