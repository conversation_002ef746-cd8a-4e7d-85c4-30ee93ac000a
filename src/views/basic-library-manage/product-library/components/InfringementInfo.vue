<script lang="ts" setup>
import {
  Emit,
  InfringementQueryResp,
  Props,
  saveProductInfringe
} from '@/views/basic-library-manage/product-library/api/productInfo'
import { RouteNameEnums } from '@/views/InfringementInvestigation/helper'
import InfringementInvestigationDialog from '@/views/basic-library-manage/components/InfringementInvestigationDialog.vue'
import { InfringementPageAPI } from '@/api/infringementInvestigation/types'
import { uniqBy } from 'lodash-es'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'InfringementInfo'
})

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const formData = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const tableData = ref<InfringementQueryResp[]>([])

const copyrightInspectionVisible = ref(false)
const handleOpenInfringementDialog = () => {
  copyrightInspectionVisible.value = true
}
const handlePickCopyright = (val: InfringementPageAPI.List[]) => {
  tableData.value = uniqBy(tableData.value.concat(val), 'id')
}

const disabled = computed(() => {
  return (
    props.isView || props.initPane !== props.currentPane || props.confirmDisabled || props.isOldData
  )
})

const router = useRouter()

const handleEditInfringement = () => {
  if (props.confirmDisabled || props.isOldData) {
    ElMessage.warning('该操作已被锁定')
    return
  }
  router.push({
    name: 'EditProduct',
    query: {
      id: formData.value.id,
      pane: props.currentPane
    }
  })
  // location.href = router.resolve({
  //   name: 'EditProduct',
  //   query: {
  //     id: formData.value.id,
  //     pane: props.currentPane
  //   }
  // }).href
}

onMounted(() => {
  tableData.value = formData.value.infringementList || []
})

watchOnce(
  () => formData.value,
  () => {
    tableData.value = formData.value.infringementList || []
  }
)

const submit = async () => {
  const [error, result] = await saveProductInfringe({
    productNumber: formData.value.productNumber!,
    infringementList: tableData.value
  })

  if (!error && result) {
    ElMessage.success(result.msg || '操作成功')
    useClosePage('ProductLibrary', {
      state: {
        productNumber: formData.value.productNumber
      }
    })
    return true
  }
  return false
}

const handleInitialScreen = () => {
  const {
    productNumber,
    designUrl,
    designerId,
    brand,
    launchSeason,
    referenceBrand,
    referenceUrl,
    competitiveBrandLink
  } = formData.value
  useClosePage(RouteNameEnums.IS, {
    query: {
      productInfo: JSON.stringify({
        productNumber,
        designUrl,
        designerId,
        brand,
        launchSeason,
        referenceBrand,
        referenceUrl,
        competitiveBrandLink
      })
    }
  })
}

defineExpose({
  submit
})
</script>

<template>
  <InfringementInvestigationDialog
    v-model="copyrightInspectionVisible"
    embed-type="productInfo"
    @submit="handlePickCopyright"
  />
  <ElButton v-if="!disabled" type="primary" @click="handleOpenInfringementDialog">添加</ElButton>
  <ElButton v-if="!disabled" type="primary" @click="handleInitialScreen"> 侵权初筛 </ElButton>
  <VxeTable :data="tableData" :show-overflow="false" class="mt-2">
    <VxeColumn fixed="left" title="序号" type="seq" width="60" />
    <VxeColumn field="infringementCode" fixed="left" min-width="150px" title="侵权记录编号">
      <template #default="{ row }: { row: InfringementQueryResp }">
        <RouterLink
          :to="{ name: RouteNameEnums.ID, params: { routerId: row.id } }"
          class="text-blue-500"
        >
          {{ row.infringementCode }}
        </RouterLink>
      </template>
    </VxeColumn>
    <VxeColumn field="createByIdItemName" fixed="left" min-width="80px" title="创建人" />
    <VxeColumn field="createTime" fixed="left" min-width="160px" title="创建时间" />
    <VxeColumn field="status" fixed="left" min-width="160px" title="状态" />
    <VxeColgroup title="侵权排查-初筛">
      <VxeColumn field="safeguardRightsStrengthPre" min-width="190px" title="品牌维权力度" />
      <VxeColumn field="innerControlUserNamePre" min-width="130px" title="内控复核人" />
      <VxeColumn field="searchDatePre" min-width="160px" title="检索日期" />
      <VxeColumn field="sendErpTimePre" min-width="160px" title="ERP下发时间" />
    </VxeColgroup>
    <VxeColgroup title="侵权排查-复筛">
      <VxeColumn field="safeguardRightsStrengthRe" min-width="190px" title="品牌维权力度" />
      <VxeColumn field="innerControlUserNameRe" min-width="130px" title="内控复核人" />
      <VxeColumn field="searchDateRe" min-width="160px" title="检索日期" />
      <VxeColumn field="sendErpTimeRe" min-width="160px" title="ERP下发时间" />
    </VxeColgroup>
  </VxeTable>
  <Teleport v-if="mounted" to=".product-info-footer">
    <span v-show="currentPane === 'InfringementInfo' && disabled">
      <ElButton
        v-hasPermi="['viewProduct:editInfringement', 'editProduct:editInfringement']"
        @click="handleEditInfringement"
        type="primary"
      >
        修改侵权排查
      </ElButton>
    </span>
  </Teleport>
</template>

<style lang="less" scoped></style>
