<script setup lang="ts">
import {
  getStylePartnerCode,
  StylePartnerCodeAPI
} from '@/views/basic-library-manage/product-library/api/stylePartnerCode'
import {
  ProductSelectResultEnum,
  ProductTaskNodeEnum
} from '@/views/basic-library-manage/product-library/const'
import { BrandEnum, YesNoEnum } from '@/views/basic-library-manage/const'
import {
  createProductInfo,
  FormModel,
  SaveTypeEnum
} from '@/views/basic-library-manage/product-library/api/productInfo'
import * as XLSX from 'xlsx'
import { isEqual } from 'lodash-es'
import { ElMessage } from 'element-plus'
import { getMaterialCode } from '@/views/basic-library-manage/product-library/components/helper'

const props = defineProps<{
  modelValue: boolean
  formData: FormModel
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

type ITableDataItem = StylePartnerCodeAPI.BaseProductSku & { $disabled: boolean }

const tableData = ref<ITableDataItem[]>([])

async function queryTableData() {
  const id = props.formData.id
  const [error, result] = await getStylePartnerCode({
    productId: id,
    skcId: (props.formData.skcInfoDetailResp || [])
      .filter((e) => e.selectable === ProductSelectResultEnum.SELECTED)
      .map((e) => e.id!),
    sizeId: props.formData.selectedSize
  })

  if (error === null && result?.datas) {
    tableData.value = result.datas.map((e) => {
      let disabled: boolean
      if (sentToWms.value) {
        disabled = !!e.skuPartnerCode
      } else {
        disabled = false
      }
      return {
        ...e,
        $disabled: disabled
      }
    })
    return
  }
}

onMounted(() => {
  watch(visible, async (flag) => {
    if (flag) {
      submitLoading.value = false
      tableData.value = []
      await queryTableData()
    }
  })
})
const handleClose = () => {
  visible.value = false
  tableData.value = []
}

const TEMPLATE_HEADERS = [
  '序号',
  '产品编号',
  'Style Partner Code',
  'SKC编号',
  '产品配色',
  'SKU编号',
  '尺码值',
  'SKU Partner Code',
  '创建人',
  '创建时间'
]

const TEMPLATE_PROPERTIES = [
  'productNumber',
  'stylePartnerCode',
  'skcCode',
  'productColor',
  'skuCode',
  'sizeIdItemName',
  'skuPartnerCode',
  'createByIdItemName',
  'createTime'
]

function handleExport() {
  // 将数组转换为工作表
  // 将数据转换为二维数组（包含列名）
  const dataWithHeaders = [
    TEMPLATE_HEADERS,
    ...tableData.value.map((item, index) => {
      return [
        index, //序号
        item.productNumber,
        item.stylePartnerCode,
        item.skcCode,
        item.productColor,
        item.skuCode,
        item.sizeIdItemName,
        item.skuPartnerCode,
        // @ts-ignore
        item.createByIdItemName,
        item.createTime
      ]
    })
  ]
  const worksheet = XLSX.utils.aoa_to_sheet(dataWithHeaders)

  // 创建工作簿并添加工作表
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

  // 定义导出的文件名
  const filename = 'exported_data.xlsx'

  // 导出为 Excel 文件
  XLSX.writeFile(workbook, filename)
}

const fileImportRef = ref()
function handleImport() {
  fileImportRef.value.click()
}

const sentToWms = computed(() => {
  return props.formData.sendWms === (YesNoEnum.Y as string)
})

function handleFileUpload(event: InputEvent) {
  const target = event.target as HTMLInputElement
  if (!target || !target.files) return
  const file = target.files[0]
  if (!file) return

  // 创建 FileReader 实例
  const reader = new FileReader()
  reader.onload = (e) => {
    const data = e.target?.result
    const workbook = XLSX.read(data, { type: 'buffer' })
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // 将工作表转换为 JSON 格式
    const excelData: string[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
    if (!excelData) {
      return
    }
    const headers = excelData[0] as unknown as string[]
    if (!isEqual(TEMPLATE_HEADERS, headers)) {
      ElMessage({
        type: 'error',
        message: '不匹配的Excel模板!'
      })
      return
    }
    const parsedData = excelData.slice(1).reduce((total, excelRow) => {
      // 跳过表头，处理每一行数据
      const rowData = TEMPLATE_PROPERTIES.reduce((obj, key, propertyIndex) => {
        return {
          ...obj,
          [key]: excelRow[propertyIndex + 1]
        }
      }, {})
      const newRow: ITableDataItem = {
        ...rowData,
        $disabled: false
      }
      total.push(newRow)
      return total
    }, [] as ITableDataItem[])
    tableData.value = tableData.value.map((i) => {
      const findItem = parsedData.find((j) => i.skuCode === j.skuCode)
      if (!findItem || i.$disabled) {
        return i
      }
      // 导入excel时,只修改tableData中skuCode能对应的skuPartnerCode
      return {
        ...i,
        skuPartnerCode: findItem.skuPartnerCode
      }
    })

    fileImportRef.value.value = null
  }
  reader.readAsArrayBuffer(file)
}

const submitLoading = ref(false)

async function handleSubmit() {
  const req = [BrandEnum.FABKIDS, BrandEnum.JUSTFAB, BrandEnum.SHOEDAZZLE]
  const formData = props.formData
  if (req.includes(formData.brand!) && !tableData.value.every((e) => e.skuPartnerCode)) {
    ElMessage.warning('请填写SKU Partner Code')
    return
  }
  const { assignedFactory } = formData
  submitLoading.value = true
  const [err, res] = await createProductInfo({
    ...formData,
    skuList: tableData.value,
    // 大货供应商
    assignedFactory: Array.isArray(assignedFactory) ? assignedFactory.join() : '',
    skcInfoDetailResp: formData.skcInfoDetailResp?.map((e) => ({
      ...e,
      coriumMaterialCode: getMaterialCode(e.coriumMaterialCode),
      otherMaterialCode: getMaterialCode(e.otherMaterialCode)
    })),
    operateBottom: SaveTypeEnum.SUBMIT,
    taskNode: ProductTaskNodeEnum.PRODUCT_CONCLUSION,
    sendWms: YesNoEnum.Y as string,
    autoSendWms: true
  })
  submitLoading.value = false
  if (!err && res) {
    ElMessage.success(res.msg || '操作成功')
    useClosePage('ProductLibrary')
    handleClose()
  }
}
</script>

<template>
  <Dialog
    v-model="visible"
    title="选品会信息确认，填写完成SKU外部编号再下发WMS"
    :before-close="handleClose"
  >
    <ElTable fit :data="tableData" border max-height="500" show-overflow-tooltip>
      <ElTableColumn align="center" label="序号" type="index" width="60" />
      <ElTableColumn align="center" property="productNumber" label="产品编号" />
      <ElTableColumn
        align="center"
        width="200"
        property="stylePartnerCode"
        label="Style Partner Code"
      />
      <ElTableColumn align="center" property="skcCode" label="SKC编号" />
      <ElTableColumn align="center" property="productColor" label="产品配色" />
      <ElTableColumn align="center" property="skuCode" label="SKU编号" />
      <ElTableColumn align="center" property="sizeIdItemName" label="尺码值" />
      <ElTableColumn width="200" align="center" property="skuPartnerCode" label="SKU Partner Code">
        <template #default="{ row }: { row: ITableDataItem }">
          <ElInput
            v-model="row.skuPartnerCode"
            :disabled="row.$disabled"
            showWordLimit
            max-length="200"
            clearable
          />
        </template>
      </ElTableColumn>
      <ElTableColumn align="center" property="createByIdItemName" label="创建人" />
      <ElTableColumn align="center" property="createTime" label="创建时间" />
    </ElTable>
    <template #footer>
      <input
        type="file"
        ref="fileImportRef"
        class="hidden"
        @change="handleFileUpload"
        accept=".xlsx"
      />
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton @click="handleExport">导出</ElButton>
      <ElButton @click="handleImport">导入</ElButton>
      <ElButton type="primary" :loading="submitLoading" @click="handleSubmit">提交</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>
