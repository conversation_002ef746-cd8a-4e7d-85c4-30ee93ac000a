<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import {
  getImportOrderVolumeList,
  getImportPriceList,
  getImportProductList,
  getImportQuickCreateProductList,
  getImportSKCList,
  getUpdateAllProductList,
  ImportAllProductListAPI,
  importOrderVolume,
  ImportOrderVolumeListAPI,
  importPrice,
  importProduct,
  ImportProductListAPI,
  importQuickCreateProduct,
  ImportQuickCreateProductListAPI,
  importSKC,
  ImportSKCListAPI,
  SuccessEnum,
  updateAllProduct
} from '../api/importDialog'
import { utils, writeFile } from 'xlsx'
import { Icon } from '@/components/Icon'
import { ImportTypeEnum } from '../const'

defineOptions({
  name: 'ImportDialog'
})
const props = defineProps<{
  modelValue: boolean
  type: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const isImportProduct = computed(() => {
  return props.type === ImportTypeEnum.PRODUCT
})

const isImportOrderVolume = computed(() => {
  return props.type === ImportTypeEnum.ORDER_VOLUME
})

const isImportProductPrice = computed(() => {
  return props.type === ImportTypeEnum.PRODUCT_PRICE
})

const isUpdateAllProduct = computed(() => {
  return props.type === ImportTypeEnum.UPDATE_ALL_PRODUCT
})

const isImportQuickCreate = computed(() => {
  return props.type === ImportTypeEnum.QUICK_CREATE
})

const isImportSKC = computed(() => {
  return props.type === ImportTypeEnum.UPDATE_SKC
})

const isUat = import.meta.env.MODE === 'uat'

// 批量导入产品价格模板
const importPriceTemplateUrl = computed(() => {
  return isUat
    ? 'https://aliyun-oa-query-results-1509030252432744-oss-cn-hongkong.oss-cn-hongkong.aliyuncs.com/template/pdm/uat/%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E4%BA%A7%E5%93%81%E4%BB%B7%E6%A0%BC%E6%A8%A1%E6%9D%BF.xlsx'
    : 'https://aliyun-oa-query-results-1509030252432744-oss-cn-hongkong.oss-cn-hongkong.aliyuncs.com/template/%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E4%BA%A7%E5%93%81%E4%BB%B7%E6%A0%BC%E6%A8%A1%E6%9D%BF.xlsx'
})

// 导入修改产品SKC模板
const importSKCTemplateUrl = computed(() => {
  return isUat
    ? 'https://aliyun-oa-query-results-1509030252432744-oss-cn-hongkong.oss-cn-hongkong.aliyuncs.com/template/pdm/uat/%E6%89%B9%E9%87%8F%E4%BF%AE%E6%94%B9%E4%BA%A7%E5%93%81SKC%E6%95%B0%E6%8D%AE%E6%A8%A1%E6%9D%BF-SKC.xlsx'
    : 'https://aliyun-oa-query-results-1509030252432744-oss-cn-hongkong.oss-cn-hongkong.aliyuncs.com/template/%E6%89%B9%E9%87%8F%E4%BF%AE%E6%94%B9%E4%BA%A7%E5%93%81SKC%E6%95%B0%E6%8D%AE%E6%A8%A1%E6%9D%BF-SKC.xlsx'
})

// 批量导入快速建档
const importQuickCreateTemplateUrl = computed(() => {
  return isUat
    ? 'https://aliyun-oa-query-results-1509030252432744-oss-cn-hongkong.oss-cn-hongkong.aliyuncs.com/template/pdm/uat/%E5%BF%AB%E9%80%9F%E5%BB%BA%E6%A1%A3%E4%BA%A7%E5%93%81%E6%A1%A3%E6%A1%88%E5%85%A8%E9%87%8F%E6%95%B0%E6%8D%AE%E6%A8%A1%E6%9D%BF-PDM.xlsx'
    : 'https://aliyun-oa-query-results-1509030252432744-oss-cn-hongkong.oss-cn-hongkong.aliyuncs.com/template/%E5%BF%AB%E9%80%9F%E5%BB%BA%E6%A1%A3%E4%BA%A7%E5%93%81%E6%A1%A3%E6%A1%88%E5%85%A8%E9%87%8F%E6%95%B0%E6%8D%AE%E6%A8%A1%E6%9D%BF-PDM.xlsx'
})

// 导入修改产品档案
const importAllProductTemplateUrl = computed(() => {
  return isUat
    ? 'https://aliyun-oa-query-results-1509030252432744-oss-cn-hongkong.oss-cn-hongkong.aliyuncs.com/template/pdm/uat/%E6%89%B9%E9%87%8F%E4%BF%AE%E6%94%B9%E4%BA%A7%E5%93%81%E6%A1%A3%E6%A1%88%E5%85%A8%E9%87%8F%E6%95%B0%E6%8D%AE%E6%A8%A1%E6%9D%BF-Style.xlsx'
    : 'https://aliyun-oa-query-results-1509030252432744-oss-cn-hongkong.oss-cn-hongkong.aliyuncs.com/template/%E6%89%B9%E9%87%8F%E4%BF%AE%E6%94%B9%E4%BA%A7%E5%93%81%E6%A1%A3%E6%A1%88%E5%85%A8%E9%87%8F%E6%95%B0%E6%8D%AE%E6%A8%A1%E6%9D%BF-Style.xlsx'
})

const configMap = {
  [ImportTypeEnum.PRODUCT]: {
    title: '批量导入产品',
    importApi: importProduct,
    listApi: getImportProductList,
    templateUrl:
      'https://aliyun-oa-query-results-1509030252432744-oss-cn-hongkong.oss-cn-hongkong.aliyuncs.com/template/%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E4%BA%A7%E5%93%81%E6%A8%A1%E6%9D%BF.xlsx',
    header: {
      errorMsg: '错误信息',
      brand: '品牌',
      launchSeason: '开发季节',
      productCategoryParent: '品类',
      productCategoryMax: '大类',
      productCategoryMidden: '中类',
      productCategory: '小类',
      productStyle: '产品风格',
      productStyleSub: '产品子风格',
      styleStructure: '款式结构',
      targetAudience: '适用人群',
      applicableSeason: '适用季节',
      lastsStandard: '楦型标准',
      toeStandard: '楦头类别',
      cost: '成本区间($)',
      estimatedPriceStr: '预估售价($)',
      designPersonIdStr: '产品企划',
      designerIdStr: '产品设计师',
      expectedLaunchDateStr: '期望上架日期',
      dtc: '是否S2C',
      remake: '备注'
    }
  },
  [ImportTypeEnum.ORDER_VOLUME]: {
    title: '批量导入预估单',
    importApi: importOrderVolume,
    listApi: getImportOrderVolumeList,
    templateUrl:
      'https://aliyun-oa-query-results-1509030252432744-oss-cn-hongkong.oss-cn-hongkong.aliyuncs.com/template/%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E4%BA%A7%E5%93%81%E9%A2%84%E4%BC%B0%E5%8D%95%E9%87%8F%E6%A8%A1%E6%9D%BF.xlsx',
    header: {
      errorMsg: '错误信息',
      brand: '品牌',
      launchSeason: '开发季节',
      productNumber: '产品编号',
      productCategoryMax: '一级分类',
      productCategoryMidden: '二级分类',
      productCategory: '三级分类',
      skcCode: 'SKC编号',
      colorIdItemName: '产品配色',
      chooseChannel: '选品渠道',
      estimatedOrderVolume: '预估订单量'
    }
  },
  [ImportTypeEnum.PRODUCT_PRICE]: {
    title: '批量导入产品价格',
    importApi: importPrice,
    listApi: getImportPriceList,
    templateUrl: importPriceTemplateUrl.value,
    header: {
      errorMsg: '错误信息',
      brand: '品牌',
      launchSeason: '开发季节',
      productNumber: '产品编号',
      productPositioning: '产品目标定级',
      shoeName: 'PRODUCT NAME',
      retailPrice: '吊牌价($)'
    }
  },
  [ImportTypeEnum.QUICK_CREATE]: {
    title: '批量导入快速建档',
    templateUrl: importQuickCreateTemplateUrl.value,
    listApi: getImportQuickCreateProductList,
    importApi: importQuickCreateProduct,
    header: {
      errorMsg: '错误信息',
      sameSkc: '导入序号',
      brand: '品牌',
      launchSeason: '开发季节',
      productCategory: '小类',
      productCategoryMax: '大类',
      productCategoryMidden: '中类',
      productCategoryParent: '品类',
      productStyle: '产品风格',
      productStyleSub: '产品子风格',
      styleStructure: '款式结构',
      targetAudience: '适用人群',
      applicableSeason: '适用季节',
      lastsStandard: '楦型标准',
      toeStandard: '楦头类别',
      cost: '成本区间($)',
      estimatedPriceStr: '预估售价($)',
      designPersonIdStr: '产品企划',
      designerIdStr: '产品设计师',
      expectedLaunchDateStr: '期望上架日期',
      dtc: '是否S2C',
      developmentChannel: '开发渠道',
      developmentModel: '开发模式',
      childrenAvgGroup: '儿童年龄段',
      childrenCrowd: '儿童人群',
      sizeRangeId: '尺码段',
      closureMethod: '穿脱方式',
      safetyFeatures: '是否含金属鞋头',
      ankleCoverage: '是否过踝/过膝',
      basicUnitOfMeasure: '基本计量单位',
      waterproofLevel: '防水级别',
      scene: '场景',
      trendyElements: '流行元素',
      toeShape: '头型',
      heelType: '跟型',
      heelHeight: '跟高',
      lastBottomType: '楦底类型',
      specialTestingRequirements: '特殊检测需求/说明',
      assignedFactory: '供应商',
      region: '区域',
      colorId: '产品配色',
      mainFabric: '主面料',
      colorType: '配色类型',
      sampleUrl: '样品图',
      velvetApplied: '是否植绒',
      velvetRequirements: '植绒要求',
      meetingResult: '选品会结果',
      selectionMeetingReviewOpinion: '选品会评审意见',
      foldable: '是否折叠',
      chooseChannel: '选品渠道',
      mainChannelMark: '主渠道标识',
      selectedSize: '选中尺码',
      combatTeam: 'CT归属',
      copyright: '是否需要侵权排查',
      noCpyrightReason: '无需排查原因',
      shoeSoleTechnique: '鞋底工艺',
      retailPrice: '吊牌价($)',
      productPositioning: '产品目标定级',
      developmentDirection: '产品系列',
      developmentDirectionSub: '产品子系列',
      shoeName: 'PRODUCT NAME',
      remake: '备注',
      materialIndex1: '面料1',
      materialIndex2: '面料2',
      materialIndex3: '面料3',
      materialIndex4: '面料4',
      liningMaterialCode: '里材料',
      liningSituation: '里绒情况',
      paddingMaterialSurfaceCode: '垫材料',
      soleMaterialOutsoleCode: '底材料',
      percentPu: '橡胶/塑料%',
      coriumMaterialCode: '真皮-分类',
      percentCorium: '真皮%',
      percentTextile: '纺织物%',
      otherMaterialCode: '其他-分类',
      percentOther: '其他%'
    }
  },
  [ImportTypeEnum.UPDATE_ALL_PRODUCT]: {
    title: '导入修改产品档案',
    templateUrl: importAllProductTemplateUrl.value,
    listApi: getUpdateAllProductList,
    importApi: updateAllProduct,
    header: {
      errorMsg: '错误信息',
      ankleCoverage: '是否过踝/过膝',
      applicableSeason: '适用季节',
      assignedFactory: '供应商',
      brand: '品牌',
      childrenAvgGroup: '儿童年龄段',
      childrenCrowd: '儿童人群',
      chooseChannel: '选品渠道',
      closureMethod: '穿脱方式',
      colorId: '颜色',
      colorType: '配色类型',
      confirmationSampleOrderUrl: '确认样打样单',
      copyright: '是否需要侵权排查',
      cost: '成本区间($)',
      designerIdStr: '产品设计师',
      designPersonIdStr: '产品企划',
      designUrl: '设计图',
      developmentChannel: '开发渠道',
      developmentContactPersonId: '跟进开发人员',
      developmentStrategy: '开发策略',
      modelCode: '关联型体',
      developmentType: '开发类型',
      dtc: '是否S2C',
      estimatedPriceStr: '预估售价($)',
      expectedLaunchDateStr: '期望上架日期',
      foldable: '是否折叠',
      heelHeight: '跟高',
      heelType: '跟型',
      initialSampleColorId: '初样颜色',
      initialSampleIssueDate: '下发打样日期',
      lastBottomType: '楦底类型',
      lastsStandard: '楦型标准',
      launchSeason: '开发季节',
      liningMaterialCode: '里材料',
      liningSituation: '里绒情况',
      mainChannelMark: '主渠道标识',
      mainFabric: '主面料',
      matchingSampleUrl: '齐色样打样单',
      materialIndex1: '面料1',
      materialIndex2: '面料2',
      materialIndex3: '面料3',
      materialIndex4: '面料4',
      materialInspectionInstructions: '材料送检说明',
      materialRatio1: '面料1占比%',
      materialRatio2: '面料2占比%',
      materialRatio3: '面料3占比%',
      materialRatio4: '面料4占比%',
      openingType: '开口类型',
      outsourcingIssueDate: '外发打样日期',
      paddingMaterialFillingCode: '垫材料（填充）',
      paddingMaterialSurfaceCode: '垫材料（面）',
      productCategory: '小类',
      productCategoryMax: '大类',
      productCategoryMidden: '中类',
      productCategoryParent: '品类',
      productPositioning: '产品目标定级',
      developmentDirection: '产品系列',
      developmentDirectionSub: '产品子系列',
      productStyle: '产品风格',
      productStyleSub: '产品子风格',
      region: '区域',
      remake: '备注',
      retailPrice: '吊牌价',
      safetyFeatures: '是否含金属鞋头',
      sameColorSampleDate: '齐色打样日期',
      sameSku: '导入序号',
      samplePrintUrl: '初样打样单',
      scene: '场景',
      selectedSize: '选中尺码',
      combatTeam: 'CT归属',
      selectionMeetingReviewOpinion: '选品会评审意见',
      shoeName: 'SHOES NAME',
      shoeSoleTechnique: '鞋底工艺',
      sizeRangeId: '尺码段',
      soleMaterialOutsoleCode: '底材料（大底）',
      specialPackagingMethod: '特殊包装方式',
      specialTestingRequirements: '特殊检测需求/说明',
      stylePositioning: '款式定位',
      styleStructure: '款式结构',
      targetAudience: '适用人群',
      technicalContactPersonId: '跟进技术人员',
      toeShape: '头型',
      toeStandard: '楦头类别',
      trendyElements: '流行元素',
      velvetApplied: '是否植绒',
      velvetRequirements: '植绒要求',
      waterproofLevel: '防水级别'
    }
  },
  [ImportTypeEnum.UPDATE_SKC]: {
    title: '导入修改产品SKC',
    templateUrl: importSKCTemplateUrl.value,
    listApi: getImportSKCList,
    importApi: importSKC,
    header: {
      errorMsg: '错误信息',
      skcCode: 'SKC编号',
      productNumber: '产品编号',
      designUrl: '设计图',
      colorId: '产品配色',
      mainFabric: '主面料',
      colorType: '配色类型',
      sampleUrl: '样品图',
      velvetApplied: '是否植绒',
      velvetRequirements: '植绒要求',
      materialIndex1: '面料1',
      materialIndex2: '面料2',
      materialIndex3: '面料3',
      materialIndex4: '面料4',
      liningMaterialCode: '里材料',
      liningSituation: '里绒情况',
      paddingMaterialSurfaceCode: '垫材料',
      soleMaterialOutsoleCode: '底材料',
      percentPu: '橡胶/塑料%',
      coriumMaterialCode: '真皮-分类',
      percentCorium: '真皮%',
      percentTextile: '纺织物%',
      otherMaterialCode: '其他-分类',
      percentOther: '其他%',
      remark: '备注'
    }
  }
}

const active = ref(1)
const currentTypeConfig = computed(() => {
  return configMap[props.type]
})
const importTemplateUrl = computed(() => {
  return currentTypeConfig.value.templateUrl
})
const fileList = ref<BaseFileDTO[]>([])
const queryLoading = ref(false)
const submitLoading = ref(false)
const tableData = ref<
  | ImportProductListAPI.List
  | ImportOrderVolumeListAPI.List
  | ImportQuickCreateProductListAPI.List
  | ImportAllProductListAPI.List
  | ImportSKCListAPI.List
>([])
const percentage = ref(0)
const isSuccess = ref(false)
const colors = [
  { color: '#f56c6c', percentage: 40 },
  { color: '#e6a23c', percentage: 80 },
  { color: '#5cb87a', percentage: 100 }
]

const successNum = ref(0)
const errorNum = ref(0)
const handleUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请上传文件')
    return
  }
  queryLoading.value = true
  const [error, result] = await currentTypeConfig.value.listApi(fileList.value[0])
  queryLoading.value = false
  if (!error && result?.datas) {
    tableData.value = result.datas
    successNum.value = (tableData.value as { success: SuccessEnum }[]).filter(
      (e) => e.success === SuccessEnum.SUCCESS
    ).length
    errorNum.value = (tableData.value as { success: SuccessEnum }[]).filter(
      (e) => e.success === SuccessEnum.ERROR
    ).length
    active.value = 2
  }
}

const handleDownloadError = () => {
  const header: {
    [key in keyof (ImportProductListAPI.Row &
      ImportOrderVolumeListAPI.Row &
      ImportQuickCreateProductListAPI.Row &
      ImportAllProductListAPI.Row &
      ImportSKCListAPI.Row)]: string | undefined
  } = currentTypeConfig.value.header
  const data = tableData.value.map((e) => {
    const obj = {}
    for (const key in e) {
      if (header[key]) {
        obj[header[key]] = e[key]
      }
    }
    return obj
  })
  const workBook = utils.book_new()
  const workSheet = utils.json_to_sheet(data)
  utils.book_append_sheet(workBook, workSheet)
  writeFile(workBook, `${currentTypeConfig.value.title}错误信息.xlsx`, {
    bookType: 'xlsx'
  })
}

const handleSubmit = async () => {
  if (isImportProduct.value || isImportQuickCreate.value) {
    const hasImg = (tableData.value as ImportProductListAPI.List).every((e) => e.designUrl?.length)
    if (!hasImg) {
      ElMessage.error('请上传设计图')
      return
    }
  }
  if (isImportQuickCreate.value) {
    const hasImg = (tableData.value as ImportQuickCreateProductListAPI.List).every(
      (e) => e.sampleUrl?.length
    )
    if (!hasImg) {
      ElMessage.error('请上传样品图')
      return
    }
  }
  if (isImportSKC.value) {
    const tableList = tableData.value as ImportQuickCreateProductListAPI.List
    const { rowLine, hasValid } = tableList.reduce(
      (acc, item: ImportSKCListAPI.Row, index: number) => {
        const isValid = item.add && (item.sampleUrl?.length ?? 0) <= 0
        if (isValid) {
          acc.rowLine.push(index + 1)
          acc.hasValid = true
        }
        return acc
      },
      { rowLine: [] as number[], hasValid: false }
    )
    if (hasValid) {
      ElMessage.error(`请对第${rowLine.join(',')}行的skc上传样品图`)
      return false
    }
  }
  active.value = 3
  percentage.value = 0
  submitLoading.value = true
  const timer = setInterval(() => {
    percentage.value = (percentage.value % 100) + 10
    if (percentage.value >= 100) {
      percentage.value = 99
    }
  }, 500)
  const [error, result] = await currentTypeConfig.value.importApi({ reqList: tableData.value })
  clearInterval(timer)
  percentage.value = 100
  submitLoading.value = false
  if (!error) {
    isSuccess.value = true
    ElMessage.success(result?.msg || '导入成功')
    emit('refresh')
  } else {
    isSuccess.value = false
  }
}

const handleClose = () => {
  active.value = 1
  fileList.value = []
  tableData.value = []
  percentage.value = successNum.value = errorNum.value = 0
  isSuccess.value = visible.value = false
}
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :title="currentTypeConfig.title"
    top="5vh"
    width="1000px"
  >
    <ElSteps :active="active" align-center class="mb-2">
      <ElStep title="上传文件" />
      <ElStep title="数据预览" />
      <ElStep title="导入数据" />
    </ElSteps>
    <template v-if="active === 1">
      <div class="flex h-28 border">
        <div class="w-28 bg-gray-200/50 flex items-center justify-center">
          <Icon :size="48" icon="ep:edit" />
        </div>
        <div class="flex flex-1 flex-col justify-center p-2">
          <div class="font-bold py-1 text-base"> 填写导入数据信息 </div>
          <div class="text-gray-500/70 text-sm py-1">
            请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除
          </div>
          <div v-if="importTemplateUrl" class="py-1">
            <ElLink :href="importTemplateUrl" :underline="false" target="_blank" type="primary">
              下载模板
            </ElLink>
          </div>
        </div>
      </div>
      <div class="flex h-28 border mt-4">
        <div class="w-28 bg-gray-200/50 flex items-center justify-center">
          <Icon :size="48" icon="ep:upload" />
        </div>
        <div class="flex flex-1 flex-col justify-center p-2">
          <div class="font-bold py-1 text-base"> 上传填好的信息表 </div>
          <div class="text-gray-500/70 text-sm py-1">
            文件后缀名必须为xls或xlsx(即Excel格式),文件大小不不得大于10M,最多支持导入3000条数据
          </div>
          <div class="py-1">
            <OssUpload
              v-model="fileList"
              :limit="1"
              :size-limit="1024 * 1024 * 10"
              accept=".xls,.xlsx"
              drag
              list-type="text"
              @error="submitLoading = false"
              @progress="submitLoading = true"
              @success="submitLoading = false"
            >
              <template #trigger>
                <ElLink :underline="false" type="primary"> 上传文件 </ElLink>
              </template>
            </OssUpload>
          </div>
        </div>
      </div>
      <div
        class="flex h-28 mt-4 bg-$el-color-warning-light-9 border-1 border-$el-color-warning-light-5 rounded-md"
      >
        <div class="w-28 flex items-center justify-center">
          <Icon :size="48" color="var(--el-color-warning)" icon="ep:warning" />
        </div>
        <div class="flex flex-1 flex-col justify-center p-2">
          <div class="font-bold py-1 text-base"> 特别提示 </div>
          <div class="text-gray-500/70 text-sm py-1">
            导入过程中如发现个别数据校验不通过，则全量回滚修正后再重新操作导入
          </div>
        </div>
      </div>
    </template>
    <template v-if="active === 2">
      <div class="flex h-28 border">
        <div class="w-28 bg-gray-200/50 flex items-center justify-center">
          <Icon :size="48" icon="ep:circle-close" />
        </div>
        <div class="flex flex-1 flex-col justify-center p-2">
          <div class="font-bold py-1">
            正常数量条数:
            <span class="text-$el-color-success">
              {{ successNum }}
            </span>
          </div>
          <div class="font-bold py-1">
            异常数量条数:
            <span class="text-$el-color-danger">
              {{ errorNum }}
            </span>
            <span
              v-if="errorNum > 0"
              class="ml-4 font-normal text-blue-500 cursor-pointer"
              @click="handleDownloadError"
            >
              下载异常数据详情提示
            </span>
          </div>
        </div>
      </div>
      <VxeTable
        v-if="isImportProduct"
        :data="tableData"
        :loading="queryLoading"
        :max-height="500"
        :scroll-x="{
          enabled: false
        }"
        class="mt-4"
      >
        <VxeColumn class-name="text-red-500" field="errorMsg" min-width="100" title="错误信息" />
        <VxeColumn field="brand" title="品牌" width="100" />
        <VxeColumn field="launchSeason" title="开发季节" width="100" />
        <VxeColumn field="productCategoryParent" title="品类" width="100" />
        <VxeColumn field="productCategoryMax" title="大类" width="100" />
        <VxeColumn field="productCategoryMidden" title="中类" width="100" />
        <VxeColumn field="productCategory" title="小类" width="100" />
        <VxeColumn :show-overflow="false" width="120">
          <template #header>
            <span class="text-red-500">*</span>
            <span>设计图</span>
          </template>
          <template #default="{ row }: { row: ImportProductListAPI.Row }">
            <OssUpload
              v-model="row.designUrl"
              :limit="5"
              :size-limit="1024 * 1024 * 100"
              accept="image/*"
              drag
              listType="text"
              multiple
            >
              <template #trigger>
                <Icon :size="22" color="var(--el-color-primary)" icon="mdi:upload" />
              </template>
            </OssUpload>
          </template>
        </VxeColumn>
        <VxeColumn field="productStyle" title="产品风格" width="100" />
        <VxeColumn field="productStyleSub" title="产品子风格" width="100" />
        <VxeColumn field="styleStructure" title="款式结构" width="100" />
        <VxeColumn field="targetAudience" title="适用人群" width="100" />
        <VxeColumn field="applicableSeason" title="适用季节" width="100" />
        <VxeColumn field="lastsStandard" title="楦型标准" width="100" />
        <VxeColumn field="toeStandard" title="楦头类别" width="100" />
        <VxeColumn field="cost" title="成本区间($)" width="100" />
        <VxeColumn field="estimatedPriceStr" title="预估售价($)" width="100" />
        <VxeColumn field="designPersonIdStr" title="产品企划" width="100" />
        <VxeColumn field="designerIdStr" title="产品设计师" width="120" />
        <VxeColumn field="expectedLaunchDateStr" title="期望上架日期" width="160" />
        <VxeColumn field="dtc" title="是否S2C" width="100" />
        <VxeColumn field="remake" title="备注" width="100" />
      </VxeTable>
      <VxeTable
        v-else-if="isImportOrderVolume"
        :data="tableData"
        :loading="queryLoading"
        :max-height="500"
        :scroll-x="{
          enabled: false
        }"
        class="mt-4"
      >
        <VxeColumn class-name="text-red-500" field="errorMsg" min-width="100" title="错误信息" />
        <VxeColumn field="brand" title="品牌" width="100" />
        <VxeColumn field="launchSeason" title="开发季节" width="100" />
        <VxeColumn field="productNumber" title="产品编号" width="100" />
        <VxeColumn field="productCategoryMax" title="一级分类" width="100" />
        <VxeColumn field="productCategoryMidden" title="二级分类" width="100" />
        <VxeColumn field="productCategory" title="三级分类" width="100" />
        <VxeColumn field="skcCode" title="SKC编号" width="100" />
        <VxeColumn field="colorIdItemName" title="产品配色" width="100" />
        <VxeColumn field="chooseChannel" title="选品渠道" width="100" />
        <VxeColumn field="estimatedOrderVolume" title="预估订单量" width="100" />
      </VxeTable>
      <VxeTable
        v-else-if="isImportProductPrice"
        :data="tableData"
        :loading="queryLoading"
        :max-height="500"
        :scroll-x="{
          enabled: false
        }"
        class="mt-4"
      >
        <VxeColumn class-name="text-red-500" field="errorMsg" min-width="100" title="错误信息" />
        <VxeColumn field="brand" title="品牌" width="100" />
        <VxeColumn field="launchSeason" title="开发季节" width="100" />
        <VxeColumn field="productNumber" title="产品编号" width="100" />
        <VxeColumn field="productPositioning" title="产品目标定级" width="100" />
        <VxeColumn field="shoeName" title="PRODUCT NAME" width="100" />
        <VxeColumn field="retailPrice" title="吊牌价($)" width="100" />
      </VxeTable>
      <VxeTable
        v-else-if="isImportQuickCreate"
        :data="tableData"
        :loading="queryLoading"
        :max-height="500"
        :scroll-x="{
          enabled: false
        }"
        class="mt-4"
      >
        <VxeColumn class-name="text-red-500" field="errorMsg" min-width="100" title="错误信息" />
        <VxeColumn field="sameSkc" title="导入序号" width="100" />
        <VxeColumn field="stylePartnerCode" title="Style Partner Code" width="100" />
        <VxeColumn field="brand" title="品牌" width="100" />
        <VxeColumn field="launchSeason" title="开发季节" width="100" />
        <VxeColumn field="productLaunchSeason" title="商品上市季节" width="100" />
        <VxeColumn field="productCategory" title="小类" width="100" />
        <VxeColumn field="productCategoryMax" title="大类" width="100" />
        <VxeColumn field="productCategoryMidden" title="中类" width="100" />
        <VxeColumn field="productCategoryParent" title="品类" width="100" />
        <VxeColumn :show-overflow="false" field="designUrl" title="设计图" width="120">
          <template #header>
            <span class="text-red-500">*</span>
            <span>设计图</span>
          </template>
          <template #default="{ row }: { row: ImportQuickCreateProductListAPI.Row }">
            <OssUpload
              v-model="row.designUrl"
              :limit="5"
              :size-limit="1024 * 1024 * 100"
              accept="image/*"
              drag
              listType="text"
              multiple
            >
              <template #trigger>
                <Icon :size="22" color="var(--el-color-primary)" icon="mdi:upload" />
              </template>
            </OssUpload>
          </template>
        </VxeColumn>
        <VxeColumn field="productStyle" title="产品风格" width="100" />
        <VxeColumn field="productStyleSub" title="产品子风格" width="100" />
        <VxeColumn field="styleStructure" title="款式结构" width="100" />
        <VxeColumn field="targetAudience" title="适用人群" width="100" />
        <VxeColumn field="applicableSeason" title="适用季节" width="100" />
        <VxeColumn field="lastsStandard" title="楦型标准" width="100" />
        <VxeColumn field="toeStandard" title="楦头类别" width="100" />
        <VxeColumn field="cost" title="成本区间($)" width="100" />
        <VxeColumn field="estimatedPriceStr" title="预估售价($)" width="100" />
        <VxeColumn field="designPersonIdStr" title="产品企划" width="100" />
        <VxeColumn field="designerIdStr" title="产品设计师" width="100" />
        <VxeColumn field="expectedLaunchDateStr" title="期望上架日期" width="100" />
        <VxeColumn field="dtc" title="是否S2C" width="100" />
        <VxeColumn field="developmentChannel" title="开发渠道" width="100" />
        <VxeColumn field="developmentModel" title="开发模式" width="100" />
        <VxeColumn field="childrenAvgGroup" title="儿童年龄段" width="100" />
        <VxeColumn field="childrenCrow500d" title="儿童人群" width="100" />
        <VxeColumn field="sizeRangeId" title="尺码段" width="100" />
        <VxeColumn field="closureMethod" title="穿脱方式" width="100" />
        <VxeColumn field="safetyFeatures" title="是否含金属鞋头" width="100" />
        <VxeColumn field="ankleCoverage" title="是否过踝/过膝" width="100" />
        <VxeColumn field="basicUnitOfMeasure" title="基本计量单位" width="100" />
        <VxeColumn field="waterproofLevel" title="防水级别" width="100" />
        <VxeColumn field="scene" title="场景" width="100" />
        <VxeColumn field="trendyElements" title="流行元素" width="100" />
        <VxeColumn field="toeShape" title="头型" width="100" />
        <VxeColumn field="heelType" title="跟型" width="100" />
        <VxeColumn field="heelHeight" title="跟高" width="100" />
        <VxeColumn field="lastBottomType" title="楦底类型" width="100" />
        <VxeColumn field="specialTestingRequirements" title="特殊检测需求/说明" width="100" />
        <VxeColumn field="assignedFactory" title="供应商" width="100" />
        <VxeColumn field="region" title="区域" width="100" />
        <VxeColumn field="colorId" title="产品配色" width="100" />
        <VxeColumn field="mainFabric" title="主面料" width="100" />
        <VxeColumn field="colorType" title="配色类型" width="100" />
        <VxeColumn :show-overflow="false" title="样品图" width="120">
          <template #header>
            <span class="text-red-500">*</span>
            <span>样品图</span>
          </template>
          <template #default="{ row }: { row: ImportQuickCreateProductListAPI.Row }">
            <OssUpload
              v-model="row.sampleUrl"
              :limit="5"
              :size-limit="1024 * 1024 * 100"
              accept="image/*"
              drag
              listType="text"
              multiple
            >
              <template #trigger>
                <Icon :size="22" color="var(--el-color-primary)" icon="mdi:upload" />
              </template>
            </OssUpload>
          </template>
        </VxeColumn>
        <VxeColumn field="velvetApplied" title="是否植绒" width="100" />
        <VxeColumn field="velvetRequirements" title="植绒要求" width="100" />
        <VxeColumn field="meetingResult" title="选品会结果" width="100" />
        <VxeColumn field="selectionMeetingReviewOpinion" title="选品会评审意见" width="100" />
        <VxeColumn field="foldable" title="是否折叠" width="100" />
        <VxeColumn field="chooseChannel" title="选品渠道" width="100" />
        <VxeColumn field="mainChannelMark" title="主渠道标识" width="100" />
        <VxeColumn field="selectedSize" title="选中尺码" width="100" />
        <VxeColumn field="combatTeam" title="CT归属" width="100" />
        <VxeColumn field="copyright" title="是否需要侵权排查" width="100" />
        <VxeColumn field="noCpyrightReason" title="无需排查原因" width="100" />
        <VxeColumn field="shoeSoleTechnique" title="鞋底工艺" width="100" />
        <VxeColumn field="retailPrice" title="吊牌价($)" width="100" />
        <VxeColumn field="productPositioning" title="产品目标定级" width="100" />
        <VxeColumn field="developmentDirection" title="产品系列" width="100" />
        <VxeColumn field="developmentDirectionSub" title="产品子系列" width="100" />
        <VxeColumn field="shoeName" title="PRODUCT NAME" width="100" />
        <VxeColumn field="remake" title="备注" width="100" />
        <VxeColumn field="materialIndex1" title="面料1" width="100" />
        <VxeColumn field="materialIndex2" title="面料2" width="100" />
        <VxeColumn field="materialIndex3" title="面料3" width="100" />
        <VxeColumn field="materialIndex4" title="面料4" width="100" />
        <VxeColumn field="liningMaterialCode" title="里材料" width="100" />
        <VxeColumn field="liningSituation" title="里绒情况" width="100" />
        <VxeColumn field="paddingMaterialSurfaceCode" title="垫材料" width="100" />
        <VxeColumn field="soleMaterialOutsoleCode" title="底材料" width="100" />
        <VxeColumn field="percentPu" title="橡胶/塑料%" width="100" />
        <VxeColumn field="coriumMaterialCode" title="真皮-分类" width="100" />
        <VxeColumn field="percentCorium" title="真皮%" width="100" />
        <VxeColumn field="percentTextile" title="纺织物%" width="100" />
        <VxeColumn field="otherMaterialCode" title="其他-分类" width="100" />
        <VxeColumn field="percentOther" title="其他%" width="100" />
      </VxeTable>
      <VxeTable
        v-else-if="isUpdateAllProduct"
        :data="tableData"
        :loading="queryLoading"
        :max-height="500"
        :scroll-x="{
          enabled: false
        }"
        class="mt-4"
      >
        <VxeColumn class-name="text-red-500" field="errorMsg" min-width="100" title="错误信息" />
        <VxeColumn field="productNumber" title="产品编号" width="100" />
        <VxeColumn :show-overflow="false" title="设计图" width="120">
          <template #default="{ row }: { row: ImportAllProductListAPI.Row }">
            <OssUpload
              v-model="row.designUrl"
              :limit="5"
              :size-limit="1024 * 1024 * 100"
              accept="image/*"
              drag
              listType="text"
              multiple
            >
              <template #trigger>
                <Icon :size="22" color="var(--el-color-primary)" icon="mdi:upload" />
              </template>
            </OssUpload>
          </template>
        </VxeColumn>
        <VxeColumn field="productStyle" title="产品风格" width="100" />
        <VxeColumn field="productStyleSub" title="产品子风格" width="100" />
        <VxeColumn field="styleStructure" title="款式结构" width="100" />
        <VxeColumn field="applicableSeason" title="适用季节" width="100" />
        <VxeColumn field="productLaunchSeason" title="商品上市季节" width="100" />
        <VxeColumn field="cost" title="成本区间($)" width="100" />
        <VxeColumn field="estimatedPriceStr" title="预估售价($)" width="100" />
        <VxeColumn field="designPersonIdStr" title="产品企划" width="100" />
        <VxeColumn field="designerIdStr" title="产品设计师" width="100" />
        <VxeColumn field="expectedLaunchDateStr" title="期望上架日期" width="100" />
        <VxeColumn field="dtc" title="是否S2C" width="100" />
        <VxeColumn field="stylePositioning" title="款式定位" width="100" />
        <VxeColumn field="developmentChannel" title="开发渠道" width="100" />
        <VxeColumn field="developmentModel" title="开发模式" width="100" />
        <VxeColumn field="developmentType" title="开发类型" width="100" />
        <VxeColumn field="childrenAvgGroup" title="儿童年龄段" width="100" />
        <VxeColumn field="childrenCrowd" title="儿童人群" width="100" />
        <VxeColumn field="openingType" title="开口类型" width="100" />
        <VxeColumn field="developmentStrategy" title="开发策略" width="100" />
        <VxeColumn field="modelCode" title="关联型体" width="100" />
        <VxeColumn field="sizeRangeId" title="尺码段" width="100" />
        <VxeColumn field="closureMethod" title="穿脱方式" width="100" />
        <VxeColumn field="safetyFeatures" title="是否含金属鞋头" width="100" />
        <VxeColumn field="ankleCoverage" title="是否过踝/过膝" width="100" />
        <VxeColumn field="waterproofLevel" title="防水级别" width="100" />
        <VxeColumn field="specialPackagingMethod" title="特殊包装方式" width="100" />
        <VxeColumn field="scene" title="场景" width="100" />
        <VxeColumn field="trendyElements" title="流行元素" width="100" />
        <VxeColumn field="toeShape" title="头型" width="100" />
        <VxeColumn field="heelType" title="跟型" width="100" />
        <VxeColumn field="heelHeight" title="跟高" width="100" />
        <VxeColumn field="specialTestingRequirements" title="特殊检测需求/说明" width="100" />
        <VxeColumn field="lastBottomType" title="楦底类型" width="100" />
        <VxeColumn field="initialSampleColorId" title="初样颜色" width="100" />
        <VxeColumn field="initialSampleIssueDate" title="下发打样日期" width="100" />
        <VxeColumn field="assignedFactory" title="供应商" width="100" />
        <VxeColumn field="region" title="区域" width="100" />
        <VxeColumn field="regionalSupplyPersonId" title="区域供管" width="100" />
        <VxeColumn field="developmentContactPersonId" title="跟进开发人员Id" width="100" />
        <VxeColumn field="technicalContactPersonId" title="跟进技术人员" width="100" />
        <VxeColumn field="selectionMeetingReviewOpinion" title="选品会评审意见" width="100" />
        <VxeColumn field="foldable" title="是否折叠" width="100" />
        <VxeColumn field="chooseChannel" title="选品渠道" width="100" />
        <VxeColumn field="mainChannelMark" title="主渠道标识" width="100" />
        <VxeColumn field="selectedSize" title="选中尺码" width="100" />
        <VxeColumn field="combatTeam" title="CT归属" width="100" />
        <VxeColumn field="copyright" title="是否需要侵权排查" width="100" />
        <VxeColumn field="noCpyrightReason" title="无需排查原因" width="100" />
        <VxeColumn field="shoeSoleTechnique" title="鞋底工艺" width="100" />
        <VxeColumn field="retailPrice" title="吊牌价($)" width="100" />
        <VxeColumn field="productPositioning" title="产品目标定级" width="100" />
        <VxeColumn field="developmentDirection" title="产品系列" width="100" />
        <VxeColumn field="developmentDirectionSub" title="产品子系列" width="100" />
        <VxeColumn field="shoeName" title="PRODUCT NAME" width="100" />
        <VxeColumn field="remake" title="备注" width="100" />
      </VxeTable>
      <VxeTable
        v-else-if="isImportSKC"
        :data="tableData"
        :loading="queryLoading"
        :max-height="500"
        :scroll-x="{
          enabled: false
        }"
        class="mt-4"
      >
        <VxeColumn min-width="60px" title="序号" type="seq" />
        <VxeColumn class-name="text-red-500" field="errorMsg" min-width="100" title="错误信息" />
        <VxeColumn field="skcCode" title="SKC编号" width="100" />
        <VxeColumn field="productNumber" title="产品编号" width="100" />
        <VxeColumn field="colorId" title="产品配色" width="100" />
        <VxeColumn field="mainFabric" title="主面料" width="100" />
        <VxeColumn field="colorType" title="配色类型" width="100" />
        <VxeColumn :show-overflow="false" title="样品图" width="120">
          <template #default="{ row }: { row: ImportSKCListAPI.Row }">
            <OssUpload
              v-model="row.sampleUrl"
              :limit="5"
              :size-limit="1024 * 1024 * 100"
              accept="image/*"
              drag
              listType="text"
              multiple
            >
              <template #trigger>
                <Icon :size="22" color="var(--el-color-primary)" icon="mdi:upload" />
              </template>
            </OssUpload>
          </template>
        </VxeColumn>
        <VxeColumn field="velvetApplied" title="是否植绒" width="100" />
        <VxeColumn field="velvetRequirements" title="植绒要求" width="100" />
        <VxeColumn field="materialIndex1" title="面料1" width="100" />
        <VxeColumn field="materialIndex2" title="面料2" width="100" />
        <VxeColumn field="materialIndex3" title="面料3" width="100" />
        <VxeColumn field="materialIndex4" title="面料4" width="100" />
        <VxeColumn field="liningMaterialCode" title="里材料" width="100" />
        <VxeColumn field="liningSituation" title="里绒情况" width="100" />
        <VxeColumn field="paddingMaterialSurfaceCode" title="垫材料" width="100" />
        <VxeColumn field="soleMaterialOutsoleCode" title="底材料" width="100" />
        <VxeColumn field="percentPu" title="橡胶/塑料%" width="100" />
        <VxeColumn field="coriumMaterialCode" title="真皮-分类" width="100" />
        <VxeColumn field="percentCorium" title="真皮%" width="100" />
        <VxeColumn field="percentTextile" title="纺织物%" width="100" />
        <VxeColumn field="otherMaterialCode" title="其他-分类" width="100" />
        <VxeColumn field="percentOther" title="其他%" width="100" />
        <VxeColumn field="remark" title="备注" width="100" />
      </VxeTable>
    </template>
    <template v-if="active === 3">
      <div v-if="submitLoading" class="h-96 flex flex-col items-center justify-center">
        <el-progress :color="colors" :percentage="percentage" :stroke-width="16" class="w-1/2" />
        <span class="mt-8">正在导入数据</span>
      </div>
      <el-result
        v-else-if="isSuccess"
        :sub-title="`您已成功导入${tableData.length}条数据`"
        icon="success"
        title="批量导入完成"
      />
      <el-result v-else icon="error" sub-title="请检查数据后重新导入" title="批量导入失败" />
    </template>
    <template #footer>
      <ElButton v-if="active === 1" @click="handleClose">取消</ElButton>
      <ElButton
        v-if="active === 1"
        :loading="submitLoading || queryLoading"
        type="primary"
        @click="handleUpload"
      >
        下一步
      </ElButton>
      <ElButton v-if="active === 2" :loading="submitLoading" @click="active = 1">上一步</ElButton>
      <ElButton v-if="active === 2" :loading="submitLoading" type="primary" @click="handleSubmit">
        提交
      </ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
