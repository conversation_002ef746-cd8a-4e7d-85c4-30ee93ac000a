<script lang="ts" setup>
import { ComponentPublicInstance, DefineSetupFnComponent } from 'vue'
import { ElMessage } from 'element-plus'
import {
  BillingInfo,
  DesignInfo,
  DistributeInfo,
  ColorSampleInfo,
  SelectionInfo,
  MaterialInfo,
  PackingInfo,
  InfringementInfo,
  SkcInfo,
  OperationInfo,
  RelatedProductInfo
} from './components'
import {
  createProductInfo,
  CreateProductInfoAPI,
  FormModel,
  getCopyProductInfo,
  getNextProduct,
  getProductInfo,
  getProductInfoByVersionId,
  Props,
  SaveTypeEnum
} from './api/productInfo'
import { ProductDataStatusEnum, StatusEnum, YesNoEnum } from '../const'
import { sendToWMS } from './components/SendToWMS'
import { VocTypeEnum } from './api/VocDialog'
import { useHelpStore } from '../store'
import { getSizeValueById } from '../size-library/api/sizeInfo'
import { stylePartnerCode } from './components/StylePartnerCode'
import { ProductSelectResultEnum } from './const'
import { getMaterialCode } from '@/views/basic-library-manage/product-library/components/helper'
import { useSizeOptions } from '@/utils/useSizeOptions'
import { SizeCodeTypeEnums } from '@/enums'

defineOptions({
  name: 'ProductInfo'
})

type ComponentIns = DefineSetupFnComponent<
  Props,
  { 'update:modelValue': (val: FormModel) => void; refresh: () => void }
> & {
  submit?: (type?: SaveTypeEnum) => Promise<void> | void
}

interface InfoPanel {
  title: string
  name: string
  component: ComponentIns
  ref: ComponentIns | null
}

const infoPanelList: InfoPanel[] = [
  {
    title: '开款信息',
    name: 'BillingInfo',
    component: BillingInfo,
    ref: null
  },
  {
    title: '款式设计',
    name: 'DesignInfo',
    component: DesignInfo,
    ref: null
  },
  {
    title: '打样分配',
    name: 'DistributeInfo',
    component: DistributeInfo,
    ref: null
  },
  {
    title: '齐色样信息',
    name: 'ColorSampleInfo',
    component: ColorSampleInfo,
    ref: null
  },
  {
    title: '选品会信息',
    name: 'SelectionInfo',
    component: SelectionInfo,
    ref: null
  },
  {
    title: '包装信息',
    name: 'PackingInfo',
    component: PackingInfo,
    ref: null
  },
  {
    title: '材质信息',
    name: 'MaterialInfo',
    component: MaterialInfo,
    ref: null
  },
  {
    title: 'SKC信息',
    name: 'SkcInfo',
    component: SkcInfo,
    ref: null
  },
  {
    title: '相关对象',
    name: 'RelatedProduct',
    component: RelatedProductInfo,
    ref: null
  },
  {
    title: '侵权排查',
    name: 'InfringementInfo',
    component: InfringementInfo,
    ref: null
  },
  {
    title: '产品运营信息',
    name: 'OperationInfo',
    component: OperationInfo,
    ref: null
  }
]

const route = useRoute()
const router = useRouter()

const id = computed(() => {
  return route.query.id
})

const versionId = computed(() => {
  return route.query.versionId
})

const initPane = computed(() => {
  return (route.query.pane as string) || 'BillingInfo'
})

const currentPane = ref(initPane.value)

const useType = () => {
  const isCreate = computed(() => {
    return route.name === 'CreateProduct'
  })

  const isEdit = computed(() => {
    return route.name === 'EditProduct' || route.name == 'CopyItemProduct'
  })

  const isView = computed(() => {
    return route.name === 'ViewProduct'
  })

  const isCopy = computed(() => {
    return route.name === 'CopyProduct'
  })

  const isVoc = computed(() => {
    return route.query.type === 'voc'
  })

  const isListing = computed(() => {
    return ['Listing', 'listing'].includes(route.query.type as string)
  })

  return {
    isCreate,
    isEdit,
    isView,
    isCopy,
    isVoc,
    isListing
  }
}

const { isCreate, isEdit, isView, isCopy, isVoc, isListing } = useType()

const loading = ref(false)
const useQueryInfo = () => {
  const formData = ref<FormModel>({
    sizeRangeId: []
  })

  const formatParams = (data: CreateProductInfoAPI.Params) => {
    const { assignedFactory, ...rest } = data
    const _assignedFactory =
      typeof assignedFactory === 'string'
        ? (assignedFactory as unknown as string)?.split(',').filter(Boolean)
        : []
    return {
      assignedFactory: _assignedFactory,
      ...rest
    }
  }

  const fetchProductInfo = async () => {
    if (isCreate.value) {
      return
    }
    loading.value = true
    const [error, result] = id.value
      ? isVoc.value
        ? await getCopyProductInfo(+id.value!)
        : await getProductInfo(+id.value!)
      : await getProductInfoByVersionId(+versionId.value!)
    if (error === null && result?.datas) {
      formData.value = formatParams({
        ...formData.value,
        ...result.datas,
        standardSizeValue: [],
        skcInfoDetailResp: result.datas.skcInfoDetailResp?.map((e) => {
          const materialInfoList = new Array(4).fill(null).map(() => ({}))
          return {
            ...e,
            coriumMaterialCode: (e.coriumMaterialCode as number[])?.length
              ? e.coriumMaterialCode?.[0]
              : undefined,
            otherMaterialCode: (e.otherMaterialCode as number[])?.length
              ? e.otherMaterialCode?.[0]
              : undefined,
            materialInfoList: e.materialInfoList || materialInfoList
          }
        })
      })
      setTimeout(() => {
        formData.value.standardSizeValue = result.datas.standardSizeValue
      })
      if (isVoc.value) {
        formData.value.stylePartnerCode = ''
        formData.value.type = VocTypeEnum.VOC
      }
      if (isListing.value) {
        formData.value.type = VocTypeEnum.LISTING
      }
      if (isVoc.value || isListing.value) {
        formData.value.vocOriginalStyleNumber = route.query.productNumber as string
        ElMessage.info('请手工下发产品数据到WMS系统')
      }
    }
    setTimeout(() => {
      loading.value = false
    })
  }

  return {
    formData,
    fetchProductInfo
  }
}

const { formData, fetchProductInfo } = useQueryInfo()

const handleTabsChange = () => {
  return !isCreate.value
}

// 选品会之前的信息禁用判断
const selectionDisabled = computed(() => {
  return formData.value.meetingResult === ProductSelectResultEnum.SELECTED && isOrderPlaced.value
})

// 禁用所有的
const confirmDisabled = computed(() => {
  return formData.value.confirmResult && isOrderPlaced.value
})

// 是否是历史数据
const isOldData = computed(() => {
  return +formData.value.oldData! === 1
})

// 是否已下单
const isOrderPlaced = computed(() => {
  return formData.value.orderPlaced === YesNoEnum.Y
})

const submitLoading = ref(false)
const handleSubmit = async (type: SaveTypeEnum) => {
  const currentPaneInstance = infoPanelList.find((e) => e.name === currentPane.value)?.ref
  if (!currentPaneInstance) {
    return
  }
  submitLoading.value = true
  await currentPaneInstance.submit?.(type)?.finally(() => {
    submitLoading.value = false
  })
  submitLoading.value = false
}

let mounted = ref<boolean>(false)
onMounted(() => {
  mounted.value = true
})
onBeforeRouteLeave(() => {
  mounted.value = false
})
onActivated(() => {
  mounted.value = true
})

const colorList = computed<string[]>(() => {
  return formData.value.colorUrlList?.map((e) => e.signatureUrl!) || []
})

function setRef<T extends ComponentPublicInstance>(el: T | null, info: InfoPanel) {
  info.ref = el as unknown as ComponentIns
}

const { sizeOptions, fetchSizeList } = useSizeOptions(SizeCodeTypeEnums.PRODUCT)
fetchSizeList()
const sizeValueList = computed(() => {
  if (!formData.value.sizeRangeId?.length) {
    return []
  }
  const sizeObj = sizeOptions.value.filter((e) => formData.value.sizeRangeId?.includes(e.id!))

  return sizeObj
    .filter((e) => e.children?.length)
    .map((e) =>
      e.children!.map((size) => ({
        label: `${size.label}【${e.label}】`,
        value: size.id,
        disabled: size.disabled
      }))
    )
    .flat()
})
watchEffect(async () => {
  if (!formData.value.sizeRangeId) {
    return
  }
  const [error, result] = await getSizeValueById({ sizeId: formData.value.sizeRangeId })
  if (!error && result?.datas) {
    if (!formData.value.selectedSize?.length) {
      formData.value.selectedSize = result.datas
        .filter((e) => e.status === StatusEnum.START)
        .map((e) => e.id!)
    }
  }
})

const handleSendToWMS = async () => {
  const isEffect = formData.value.dataStatus === ProductDataStatusEnum.EFFECT
  const isOrderPlaced = formData.value.orderPlaced === YesNoEnum.Y
  if (isOrderPlaced) {
    ElMessage.warning('产品已下单，需走线上审批流程才能更新数据到WMS系统')
    return
  }
  if (!isEffect) {
    ElMessage.warning('产品的状态还未生效或已作废')
    return
  }
  const result = await sendToWMS(formData.value, sizeValueList.value)
  if (result) {
    formData.value.wmsCategoryId = result[0].wmsCategoryId
    submitLoading.value = true
    if (formData.value.stylePartnerCode) {
      const data = await stylePartnerCode(formData.value.id!, formData.value)
      if (!data) {
        submitLoading.value = false
        return false
      }
      return true
    }
    const { assignedFactory } = formData.value
    await createProductInfo({
      ...formData.value,
      // 大货供应商
      assignedFactory: Array.isArray(assignedFactory) ? assignedFactory.join() : '',
      skcInfoDetailResp: formData.value.skcInfoDetailResp?.map((e) => ({
        ...e,
        coriumMaterialCode: getMaterialCode(e.coriumMaterialCode),
        otherMaterialCode: getMaterialCode(e.otherMaterialCode)
      })),
      sendWms: YesNoEnum.Y as string
    })
    submitLoading.value = false
    useClosePage('ProductLibrary')
  }
}

const handleNext = async (isNext: boolean) => {
  const { productFormData } = useHelpStore()
  if (!productFormData) {
    ElMessage.warning('请回到产品筛选清单重新进入')
    return
  }
  submitLoading.value = true
  const [error, result] = await getNextProduct(formData.value.id!, productFormData)
  submitLoading.value = false
  if (error !== null || !result?.datas) {
    return
  }
  let href: string | undefined
  if (isNext) {
    if (result.datas.after) {
      href = router.resolve({
        name: 'ViewProduct',
        query: {
          id: result.datas.after
        }
      }).href
    } else {
      ElMessage.warning('已经是最后一个产品了')
    }
  } else {
    if (result.datas.before) {
      href = router.resolve({
        name: 'ViewProduct',
        query: {
          id: result.datas.before
        }
      }).href
    } else {
      ElMessage.warning('已经是第一个产品了')
    }
  }
  if (href) {
    location.href = href
  }
}

onActivated(() => {
  if (isView.value && !loading.value) {
    fetchProductInfo()
  }
})
const handleClose = () => {
  useClosePage('ProductLibrary', {
    state: {
      productNumber: formData.value.productNumber
    }
  })
}
Promise.all([fetchProductInfo()])
</script>

<template>
  <div v-loading="loading" class="reset-height">
    <ElCard v-if="!isCreate" body-class="!py-2" class="sticky top-0 mb-2">
      <ElScrollbar>
        <div class="flex flex-nowrap items-center w-full min-h-28 h-28 whitespace-nowrap">
          <ElImage
            hide-on-click-modal
            :preview-src-list="
              formData.thumbnail ? [formData.thumbnail]?.map((e) => e.signatureUrl!) : []
            "
            :src="formData.thumbnail?.signatureUrl"
            class="w-24 h-24 min-w-24 min-h-24 shadow-md shadow-gray-500/50"
            fit="contain"
            preview-teleported
          />
          <div class="h-1/2 ml-4 mr-2 flex flex-col justify-between">
            <div class="text-center">{{ formData.productNumber }}</div>
            <div>
              <ElTag class="mr-2" effect="dark" round size="large">
                {{ formData.launchSeasonItemName }}
              </ElTag>
              <ElTag v-if="formData.designerIdItemName" effect="plain" round size="large">
                {{ formData.designerIdItemName }}
              </ElTag>
            </div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
            direction="vertical"
          />
          <div class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-xs leading-[24px]">
            <div>当前阶段</div>
            <div>{{ formData.developStageItemName }} {{ formData.stageCompDesc }}</div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-divider"
            direction="vertical"
          />
          <div class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500">
            <div>打样供应商</div>
            <div>
              {{ formData.factoryItemName }}
            </div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-md shadow-divider"
            direction="vertical"
          />
          <div
            class="flex-1 flex-grow-[2] flex-shrink-[2] h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500"
          >
            <div>产品颜色</div>
            <div class="flex flex-nowrap">
              <div v-for="item in colorList" :key="item" class="mr-2 w-6 h-6">
                <ElImage
                  :preview-src-list="colorList"
                  :src="item"
                  class="w-6 h-6"
                  fit="contain"
                  loading="lazy"
                  hide-on-click-modal
                  preview-teleported
                />
              </div>
            </div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-md shadow-divider"
            direction="vertical"
          />
          <div class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500">
            <div>风险判定</div>
            <div class="text-xs leading-[24px] text-gray-500">
              {{ formData.infringementRisk }}
            </div>
          </div>
          <ElDivider
            class="!h-1/2 !border-l-4 border-[rgb(228,228,228)] rounded-full shadow-md shadow-divider"
            direction="vertical"
          />
          <div class="flex-1 h-1/2 ml-4 mr-2 flex flex-col justify-between text-gray-500">
            <div>当前状态</div>
            <div>{{ formData.dataStatusItemName }}</div>
          </div>
          <div class="flex flex-col ml-2">
            <ElButton size="large" @click="router.push({ name: 'ProductLibrary' })">
              返回产品清单
            </ElButton>
            <div class="h-1"></div>
            <ElButton
              v-if="formData.sendWms !== YesNoEnum.Y"
              class="!ml-0"
              size="large"
              @click="handleSendToWMS"
            >
              下发数据到WMS
            </ElButton>
          </div>
        </div>
      </ElScrollbar>
    </ElCard>
    <ElCard
      :style="{ height: `calc(100% - ${isCreate ? '0px' : '130px'} - 1rem)` }"
      body-class="h-full !py-0 !pb-2 !pr-0"
    >
      <ElScrollbar>
        <ElTabs v-model="currentPane" :before-leave="handleTabsChange" class="pr-5">
          <ElTabPane
            v-for="item in infoPanelList"
            :key="item.name"
            :label="item.title"
            :lazy="!['SelectionInfo', 'BillingInfo'].includes(item.name)"
            :name="item.name"
          >
            <component
              :is="item.component"
              :id="formData.copyrightInspection"
              :ref="(el: ComponentPublicInstance) => setRef(el, item)"
              v-model="formData"
              :confirm-disabled="confirmDisabled"
              :current-pane="currentPane"
              :init-pane="initPane"
              :is-copy="isCopy"
              :is-create="isCreate"
              :is-edit="isEdit"
              :is-old-data="isOldData"
              :is-view="isView"
              :mounted="mounted"
              :selection-disabled="selectionDisabled"
              @refresh="fetchProductInfo"
            />
          </ElTabPane>
        </ElTabs>
      </ElScrollbar>
    </ElCard>
    <ElCard body-class="!py-2 text-center" class="sticky bottom-[var(--app-footer-height)] mt-2">
      <ElButton @click="handleClose" class="mr-[12px]">返回</ElButton>
      <div class="product-info-footer mr-[12px]" style="display: inline"> </div>
      <template v-if="(isCreate || isEdit) && initPane === currentPane">
        <ElButton
          :loading="submitLoading"
          class="mr-[12px]"
          type="primary"
          @click="handleSubmit(SaveTypeEnum.SUBMIT)"
        >
          确定
        </ElButton>
      </template>
      <template v-if="isView">
        <ElButton :disabled="loading" :loading="submitLoading" @click="handleNext(false)">
          上一个
        </ElButton>
        <ElButton
          :disabled="loading"
          :loading="submitLoading"
          class="mr-[12px]"
          @click="handleNext(true)"
        >
          下一个
        </ElButton>
      </template>
    </ElCard>
  </div>
</template>

<style lang="less" scoped>
:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}

.reset-height {
  height: calc(
    100vh - var(--app-footer-height) - var(--app-content-padding) * 2 - var(--top-tool-height) -
      var(--tags-view-height) - var(--app-footer-height)
  );
}
</style>
