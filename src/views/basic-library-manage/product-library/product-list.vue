<script lang="tsx" setup>
import { ContentWrap } from '@/components/ContentWrap'
import dayjs from 'dayjs'
import { isEqual } from 'lodash-es'
import { Icon } from '@/components/Icon'
import { ref } from 'vue'
import type { VxeGridInstance, VxeGridProps } from 'vxe-table'
import type { FormInstance, FormRules } from 'element-plus'
import {
  ElButton,
  ElCollapseTransition,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElMessage,
  ElPagination,
  ElRow
} from 'element-plus'
import CustomColumnsDialog from './components/CustomColumnsDialog.vue'
import DistributeInfoDialog from './components/DistributeInfoDialog.vue'
import { storeToRefs } from 'pinia'
import type { Pager, ProductCategoryListAPI } from '../api/common'
import { getProductCategoryList } from '../api/common'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import type { ProductListPageAPI, ViewListAPI } from './api/product-list'
import { getAllViews, getProductListByPage } from './api/product-list'
import { Column, ImportTypeEnum, MemberTypeEnums } from './const'
import FactoryQuotationDialog from './components/FactoryQuotationDialog.vue'
import { ProductDataStatusEnum, YesNoEnum } from '../const'
import CardView from './components/CardView.vue'
import StatusDialog from './components/StatusDialog.vue'
import ImportDialog from './components/ImportDialog.vue'
import VersionDialog from './components/VersionDialog.vue'
import { CommonYesNoEnums } from '@/enums'
import { SelectPlus } from '@/components/Business/SelectPlus'
import { useUserInfoStore } from '@/store/modules/userInfo'
import { useHelpStore } from '../store'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import AddColorSizeDialog from './components/AddColorSizeDialog.vue'
import { hasValue } from '@/utils'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import DeriveDialog from './components/DeriveDialog.vue'
import { hasPermission } from '@/directives/permission/hasPermi'
import { watchDebounced } from '@vueuse/core'

defineOptions({
  name: 'ProductLibrary'
})

const props = defineProps<{
  isEmbed?: boolean
}>()

const router = useRouter()
const route = useRoute()

const useConst = () => {
  const { userInfo } = storeToRefs(useUserInfoStore())

  const productCategoryList = ref<ProductCategoryListAPI.Data[]>([])
  const fetchProductCategoryList = async () => {
    const [error, result] = await getProductCategoryList()
    if (error === null && result?.datas) {
      productCategoryList.value = result?.datas
    }
  }
  fetchProductCategoryList()

  const productNumberRef = ref<InstanceType<typeof SelectPlus>>()
  const vocProductNumberRef = ref<InstanceType<typeof SelectPlus>>()
  const styleNumberRef = ref<InstanceType<typeof SelectPlus>>()
  onActivated(() => {
    styleNumberRef.value?.queryOptions()
    productNumberRef.value?.queryOptions()
    vocProductNumberRef.value?.queryOptions()
  })

  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '135px'
    },
    {
      formLabelLength: '180px'
    }
  ])

  return {
    userInfo,
    styleNumberRef,
    productNumberRef,
    vocProductNumberRef,
    productCategoryList,
    formLabelLength
  }
}

const {
  userInfo,
  styleNumberRef,
  productNumberRef,
  vocProductNumberRef,
  productCategoryList,
  formLabelLength
} = useConst()

const { setProductFormData } = useHelpStore()

const useQuery = () => {
  type FormModel = ProductListPageAPI.Params
  const formRef = ref<FormInstance>()
  const tableRef = ref<VxeGridInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const formRules = ref<FormRules<FormModel>>({})
  const defaultFormData: FormModel & {
    date: [string, string]
  } = {
    emptySupplier: undefined,
    brand: [],
    productNumber: [],
    launchSeason: [],
    targetAudience: [],
    designPersonId: [],
    date: ['', ''],
    designerId: [],
    region: [],
    productCategory: [],
    technicalContactPersonId: [],
    developmentContactPersonId: [],
    toeStandard: [],
    toeShape: [],
    vocOriginalStyleNumber: '',
    dataStatus: props.isEmbed ? [ProductDataStatusEnum.EFFECT, ProductDataStatusEnum.DRAFT] : [],
    styleNumber: '',
    productName: '',
    developmentType: [],
    developmentDirection: [],
    chooseChannel: [],
    mainChannelMark: [],
    developmentChannel: []
  }
  let lastFormData = ref({
    ...defaultFormData
  })
  const formData = ref({
    ...defaultFormData
  })

  const handleDetail = (row: ProductListPageAPI.Row, name: string = 'id') => {
    setProductFormData(lastFormData.value)
    router.push({ name: 'ViewProduct', query: { id: row[name] } })
  }

  const columnSettingRef = ref<InstanceType<typeof CustomColumnsDialog>>()
  const defaultColumns: Column[] = [
    {
      type: 'checkbox',
      fixed: 'left',
      width: 40
    },
    {
      type: 'seq',
      title: '序号',
      width: 60,
      fixed: 'left'
    },
    {
      title: '产品编号',
      field: 'productNumber',
      width: 150,
      fixed: 'left',
      cellRender: {
        name: 'Link',
        props: {
          clickFn: ({ row }: { row: ProductListPageAPI.Row }) => handleDetail(row)
        }
      }
    },
    {
      title: 'Style（WMS）',
      field: 'styleWms',
      fixed: 'left',
      width: 120
    },
    {
      title: '缩略图',
      width: 80,
      fixed: 'left',
      field: 'thumbnail',
      cellRender: {
        name: 'Image'
      }
    },
    {
      field: 'vocOriginalStyleNumber',
      title: '母Style',
      width: 150,
      cellRender: {
        name: 'Link',
        props: {
          clickFn: ({ row }: { row: ProductListPageAPI.Row }) =>
            handleDetail(row, 'vocStyleNumberId')
        }
      }
    },
    {
      field: 'styleNumber',
      title: 'Style编码',
      width: 150,
      cellRender: {
        name: 'Link',
        props: {
          clickFn: ({ row }: { row: ProductListPageAPI.Row }) => handleDetail(row)
        }
      }
    },
    {
      title: '任务节点和状态',
      field: 'stageCompDesc',
      width: '125',
      className: 'ellipsis-cell',
      cellRender: {
        name: 'Ellipsis',
        props: {
          maxRow: 3,
          separator: '\r\n'
        }
      }
    },
    { title: '状态', fixed: 'right', field: 'dataStatusItemName', width: '80' },
    {
      title: '操作',
      field: 'operation',
      fixed: 'right',
      width: '140',
      visible: !props.isEmbed,
      showOverflow: false,
      slots: {
        default: ({ row }: { row: ProductListPageAPI.Row }) => (
          <>
            <ElDropdown>
              {{
                default: () => (
                  <span class="el-dropdown-link">
                    操作
                    <Icon icon="ep:arrow-down" />
                  </span>
                ),
                dropdown: () => (
                  <ElDropdownMenu>
                    {row.quicklyAble === 1 && hasPermission('product:editQuickCreate') ? (
                      <ElDropdownItem onClick={() => handleEditQuickCreate(row)}>
                        修改产品
                      </ElDropdownItem>
                    ) : null}
                    {hasPermission('product:copyProduct') ? (
                      <ElDropdownItem onClick={() => handleCopyProduct(row)}>
                        复制产品
                      </ElDropdownItem>
                    ) : null}
                    {row.derivedType &&
                    hasPermission('product:derived') &&
                    row.dataStatus?.toLowerCase() !== ProductDataStatusEnum.INVALID ? (
                      <ElDropdownItem onClick={() => handleOpenDeriveDialog(row)}>
                        产品衍生
                      </ElDropdownItem>
                    ) : null}
                    <ElDropdownItem onClick={() => handleOpenVersionDialog(row)}>
                      版本记录
                    </ElDropdownItem>
                  </ElDropdownMenu>
                )
              }}
            </ElDropdown>
          </>
        )
      }
    }
  ]
  const columns = ref([...defaultColumns])
  const tableData = ref<ProductListPageAPI.List>([])
  const pager = ref<Pager>({
    current: 1,
    size: 50,
    total: 0
  })
  const tableOptions = computed<VxeGridProps<ProductListPageAPI.Row>>(() => ({
    minHeight: 100,
    columns: columns.value,
    data: tableData.value,
    maxHeight: maxHeight.value - 80,
    showOverflow: 'tooltip',
    rowId: 'id',
    scrollX: {
      enabled: true,
      gt: 20
    },
    scrollY: {
      enabled: true,
      gt: 20
    },
    loading: queryLoading.value,
    cellConfig: {
      height: 80
    }
  }))
  const queryLoading = ref(false)
  const queryParams = computed<ProductListPageAPI.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      productCategory: formData.value.productCategory?.map((item) => item.toString()),
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1],
      date: undefined
    }
  })
  const defaultTime: [Date, Date] = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ]

  let controller: AbortController | null = null
  const handleQuery = async () => {
    const valid = await formRef.value?.validate()
    if (!valid) {
      return
    }
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData.value, formData.value)) {
      pager.value.current = 1
    }
    const result = await getProductListByPage(queryParams.value, controller.signal)
    selectedRows.value = []
    tableRef.value?.clearCheckboxRow()
    if (result?.datas) {
      lastFormData.value = { ...formData.value }
      const { records } = result.datas
      tableData.value = records || []
      await nextTick()
      // tableRef.value?.loadData([]).then(() => {
      //   tableRef.value?.loadData(tableData.value)
      // })
      pager.value.total = result.datas?.pager?.total || 0
    }
    queryLoading.value = false
  }

  const queryByRoute = () => {
    const query = route.query
    if (hasValue(query)) {
      const { brandId, launchSeason, taskNode, memberType, idList } = query
      const productIdList = idList ? (idList as string).split(',').map((item) => item) : []
      formData.value.brand = brandId ? [+brandId] : []
      formData.value.launchSeason = launchSeason ? [launchSeason as string] : []
      formData.value.dataStatus = [ProductDataStatusEnum.EFFECT, ProductDataStatusEnum.DRAFT]
      formData.value.taskNode = taskNode ? [taskNode as string] : []
      if (memberType === MemberTypeEnums.DESIGNER) {
        formData.value.designerId = [userInfo.value.id!]
      }
      if (memberType === MemberTypeEnums.DEVELOPER) {
        formData.value.developmentContactPersonId = [userInfo.value.id!]
      }
      if (memberType === MemberTypeEnums.PLANNER) {
        formData.value.designPersonId = [userInfo.value.id!]
      }
      // idList 来自home
      if (productIdList.length > 0) {
        formData.value.idList = productIdList
      }
      handleQuery()
    }
  }

  onMounted(queryByRoute)

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
  }

  enum ExportTypeEnum {
    PRODUCT = 'product-export',
    FACTORY = 'productFactory-export'
  }

  const exportList = [
    {
      label: '导出产品档案数据',
      value: ExportTypeEnum.PRODUCT
    },
    {
      label: '导出产品工厂数据',
      value: ExportTypeEnum.FACTORY
    }
  ]

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = (exportType: ExportTypeEnum) => {
    formRef.value
      ?.validate((valid) => {
        if (!valid) {
          return
        }
        let reqParam: string
        if (selectedRows && selectedRows.value?.length > 0) {
          const idList = selectedRows.value.map((item) => item.id)
          reqParam = JSON.stringify({ idList, exportConfigViewId: currentView.value.id })
        } else {
          reqParam = JSON.stringify({
            ...queryParams.value,
            exportConfigViewId: currentView.value.id
          })
        }
        exportFn({
          exportType: exportType.toString(),
          reqParam
        })
      })
      .catch(() => {})
  }

  const visible = ref(false)
  const setVisible = async () => {
    visible.value = !visible.value
    await nextTick()
    tableRef.value?.recalculate()
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef,
    offsetBottom: props.isEmbed ? 50 + 20 + 32 + 10 + 30 : 0
  })

  return {
    formRef,
    tableRef,
    pagerRef,
    formRules,
    formData,
    lastFormData,
    tableData,
    tableOptions,
    pager,
    queryLoading,
    defaultTime,
    queryParams,
    handleQuery,
    queryByRoute,
    handleReset,
    exportList,
    handleExport,
    exportLoading,
    visible,
    setVisible,
    maxHeight,
    columns,
    handleDetail,
    defaultColumns,
    columnSettingRef
  }
}

const {
  formRef,
  tableRef,
  pagerRef,
  formRules,
  formData,
  lastFormData,
  tableData,
  tableOptions,
  pager,
  queryLoading,
  defaultTime,
  handleQuery,
  queryByRoute,
  handleReset,
  exportList,
  handleExport,
  exportLoading,
  visible,
  setVisible,
  maxHeight,
  columns,
  handleDetail,
  defaultColumns,
  columnSettingRef
} = useQuery()

const useOperation = () => {
  const selectedRows = ref<ProductListPageAPI.List>([])
  const currentRow = ref<ProductListPageAPI.Row>()
  const defaultViewList: ViewListAPI.List = [
    {
      name: '图形视图',
      fieldAttr: []
    }
  ]
  const viewList = ref<ViewListAPI.List>([...defaultViewList])
  const cardRef = ref<InstanceType<typeof CardView> | null>(null)
  const currentView = ref<ViewListAPI.Row>(viewList.value[0])
  const isShowList = computed(() => {
    return currentView.value.name !== '图形视图'
  })
  const customColumnsDialogVisible = ref(false)
  const distributeDialogVisible = ref(false)
  const factoryDialogVisible = ref(false)
  const addColorSizeDialogVisible = ref(false)

  const fetchAllViews = async (viewId?: number) => {
    const [error, result] = await getAllViews()
    if (error === null && result?.datas) {
      viewList.value = defaultViewList.concat(result.datas || [])
      if (typeof viewId === 'number') {
        const updatedView = viewList.value.find((item) => item.id === viewId)
        if (updatedView) {
          currentView.value = updatedView
          return
        }
      }
      currentView.value = viewList.value[1] || viewList.value[0]
    }
  }

  onMounted(() => {
    fetchAllViews()
  })

  watch(
    () => currentView.value,
    (val) => {
      selectedRows.value = []
      const allColumns = columnSettingRef.value?.allColumns
      const currentViewColumns = Array.isArray(val.fieldAttr)
        ? val.fieldAttr.map((field, index) => {
            const column = defaultColumns.find((item) => item.field === field)
            if (column) {
              return column
            }

            const columnImgFlag = allColumns?.find((item) => {
              return item.attrField === field
            })?.imgFlag
            const isImgColumn = columnImgFlag === parseInt(YesNoEnum.Y, 10)

            const defaultColumnConfig = defaultColumns.find((item) => item.field === field)
            const fieldTitle = val.fieldTitle?.[index] || field
            const minWidth = defaultColumnConfig?.minWidth || 100
            return {
              ...defaultColumnConfig,
              field: field,
              title: fieldTitle,
              minWidth,
              ...(isImgColumn && {
                cellRender: { name: 'Image' }
              })
            }
          })
        : []
      currentViewColumns.unshift(defaultColumns[0])
      columns.value = currentViewColumns
    }
  )

  const isOnlyOne = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择产品')
      return false
    }
    if (selectedRows.value.length > 1) {
      ElMessage.warning('只能选择一个产品')
      return false
    }
    return true
  }

  const validateStatus = () => {
    if (
      selectedRows.value.some((e) => e.dataStatus?.toLowerCase() === ProductDataStatusEnum.INVALID)
    ) {
      ElMessage.warning('已作废的产品不能进行操作')
      return false
    }
    if (selectedRows.value.some((e) => e.orderPlaced === CommonYesNoEnums.Yes)) {
      ElMessage.warning('已下单的产品不能进行操作')
      return false
    }
    return true
  }

  // 新增产品
  const handleCreateProduct = () => {
    setProductFormData(lastFormData.value)
    router.push({
      name: 'CreateProduct'
    })
  }

  // 修改产品
  const handleEditProduct = (row: ProductListPageAPI.Row) => {
    setProductFormData(lastFormData.value)
    router.push({
      name: 'EditProduct',
      query: {
        id: row.id,
        pane: 'BillingInfo'
      }
    })
  }

  // 款式设计
  const handleDesignProduct = () => {
    if (!isOnlyOne()) {
      return
    }
    if (!validateStatus()) {
      return
    }
    if (
      selectedRows.value.some(
        (e) =>
          e.selectionDisabled ||
          (e.confirmResult && e.orderPlaced === YesNoEnum.Y) ||
          +e.oldData! === 1
      )
    ) {
      ElMessage.warning('该操作已被锁定')
      return
    }
    setProductFormData(lastFormData.value)
    router.push({
      name: 'EditProduct',
      query: {
        id: selectedRows.value[0].id,
        pane: 'DesignInfo'
      }
    })
  }

  // 打样分配
  const handleOpenDistributeInfoDialog = () => {
    if (!isOnlyOne()) {
      return
    }
    distributeDialogVisible.value = true
  }

  // 初样评审
  const handleReviewSample = () => {
    if (!isOnlyOne()) {
      return
    }
    if (!validateStatus()) {
      return
    }
    if (
      selectedRows.value.some(
        (e) =>
          e.selectionDisabled ||
          (e.confirmResult && e.orderPlaced === YesNoEnum.Y) ||
          +e.oldData! === 1
      )
    ) {
      ElMessage.warning('该操作已被锁定')
      return
    }
    setProductFormData(lastFormData.value)
    router.push({
      name: 'EditProduct',
      query: {
        id: selectedRows.value[0].id,
        pane: 'SampleInfo',
        type: 'review'
      }
    })
  }

  // 齐色样配置
  const handleConfigColor = () => {
    if (!isOnlyOne()) {
      return
    }
    const { canColorSample } = selectedRows.value[0]
    if (!canColorSample) {
      ElMessage.error('请先完成款式设计在操作')
      return false
    }
    setProductFormData(lastFormData.value)
    router.push({
      name: 'EditProduct',
      query: {
        id: selectedRows.value[0].id,
        pane: 'ColorSampleInfo',
        type: 'config'
      }
    })
  }

  // 齐色样评审
  const handleReviewColor = () => {
    if (!isOnlyOne()) {
      return
    }
    if (!validateStatus()) {
      return
    }
    if (
      selectedRows.value.some(
        (e) =>
          e.selectionDisabled ||
          (e.confirmResult && e.orderPlaced === YesNoEnum.Y) ||
          +e.oldData! === 1
      )
    ) {
      ElMessage.warning('该操作已被锁定')
      return
    }
    setProductFormData(lastFormData.value)
    router.push({
      name: 'EditProduct',
      query: {
        id: selectedRows.value[0].id,
        pane: 'ColorSampleInfo',
        type: 'review'
      }
    })
  }

  // 工厂报价
  const handleOpenFactoryInfoDialog = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择产品')
      return
    }
    factoryDialogVisible.value = true
  }

  // 快速建档
  const handleQuickCreate = () => {
    setProductFormData(lastFormData.value)
    router.push({
      name: 'QuickCreateProduct'
    })
  }

  // 修改快速建档
  const handleEditQuickCreate = (row: ProductListPageAPI.Row) => {
    if (row.dataStatus?.toLowerCase() === ProductDataStatusEnum.INVALID) {
      ElMessage.warning('已作废的产品不能进行操作')
      return
    }
    if (row.sendWms === CommonYesNoEnums.Yes && row.orderPlaced === CommonYesNoEnums.Yes) {
      ElMessage.warning('已下发WMS并且已下单的产品不能进行操作')
      return
    }
    setProductFormData(lastFormData.value)
    router.push({
      name: 'QuickEditProduct',
      query: {
        id: row.id
      }
    })
  }

  // 选品会
  const handleSelectionProduct = () => {
    if (!isOnlyOne()) {
      return
    }
    setProductFormData(lastFormData.value)
    router.push({
      name: 'EditProduct',
      query: {
        id: selectedRows.value[0].id,
        pane: 'SelectionInfo'
      }
    })
  }

  // 材质信息维护
  const handleConfigMaterialInfo = () => {
    if (!isOnlyOne()) {
      return
    }
    if (selectedRows.value.some((e) => e.oldData === +YesNoEnum.Y)) {
      ElMessage.error('历史数据不允许修改')
      return
    }
    setProductFormData(lastFormData.value)
    router.push({
      name: 'EditProduct',
      query: {
        id: selectedRows.value[0].id,
        pane: 'MaterialInfo'
      }
    })
  }

  // 材质占比维护
  const handleConfigMaterialRatio = () => {
    if (!isOnlyOne()) {
      return
    }
    if (selectedRows.value.some((e) => e.oldData === +YesNoEnum.Y)) {
      ElMessage.error('历史数据不允许修改')
      return
    }
    setProductFormData(lastFormData.value)
    router.push({
      name: 'EditProduct',
      query: {
        id: selectedRows.value[0].id,
        pane: 'MaterialInfo',
        type: 'ratio'
      }
    })
  }

  // 确认样配置
  const handleConfigConfirmSample = () => {
    if (!isOnlyOne()) {
      return
    }
    if (
      selectedRows.value.some(
        (e) => (e.confirmResult && e.orderPlaced === YesNoEnum.Y) || +e.oldData! === 1
      )
    ) {
      ElMessage.warning('该操作已被锁定')
      return
    }
    if (
      selectedRows.value.some((e) => e.dataStatus?.toLowerCase() === ProductDataStatusEnum.INVALID)
    ) {
      ElMessage.warning('已作废的产品不能进行操作')
      return
    }
    setProductFormData(lastFormData.value)
    router.push({
      name: 'EditProduct',
      query: {
        id: selectedRows.value[0].id,
        pane: 'ConfirmSampleInfo',
        type: 'config'
      }
    })
  }

  // 确认样评审
  const handleReviewConfirmSample = () => {
    if (!isOnlyOne()) {
      return
    }
    if (
      selectedRows.value.some(
        (e) =>
          e.selectionDisabled ||
          (e.confirmResult && e.orderPlaced === YesNoEnum.Y) ||
          +e.oldData! === 1
      )
    ) {
      ElMessage.warning('该操作已被锁定')
      return
    }
    if (
      selectedRows.value.some((e) => e.dataStatus?.toLowerCase() === ProductDataStatusEnum.INVALID)
    ) {
      ElMessage.warning('已作废的产品不能进行操作')
      return
    }
    setProductFormData(lastFormData.value)
    router.push({
      name: 'EditProduct',
      query: {
        id: selectedRows.value[0].id,
        pane: 'ConfirmSampleInfo',
        type: 'review'
      }
    })
  }

  const handleAddColorSize = () => {
    if (!isOnlyOne()) {
      return
    }
    if (
      selectedRows.value.some(
        (e) =>
          e.dataStatus?.toLowerCase() !== ProductDataStatusEnum.EFFECT ||
          e.orderPlaced !== CommonYesNoEnums.Yes
      )
    ) {
      ElMessage.warning('仅已生效并且已下单的产品能进行操作')
      return
    }
    currentRow.value = selectedRows.value[0]
    addColorSizeDialogVisible.value = true
  }

  const handleCheckedChange = () => {
    const checked: ProductListPageAPI.List | undefined =
      tableRef.value?.getCheckboxRecords() || cardRef.value?.getCheckboxRecords()
    selectedRows.value = checked || []
  }

  // 生效/作废
  const statusDialogVisible = ref(false)
  const handleChangeStatus = (row?: ProductListPageAPI.Row) => {
    if (row) {
      selectedRows.value = [row]
    } else {
      handleCheckedChange()
    }
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择产品')
      return
    }
    statusDialogVisible.value = true
  }

  // 配置视图
  const handleConfigView = () => {
    customColumnsDialogVisible.value = true
  }

  // 复制产品
  const handleCopyProduct = (row: ProductListPageAPI.Row) => {
    if (row.dataStatus?.toLowerCase() === ProductDataStatusEnum.INVALID) {
      ElMessage.warning('数据状态为作废的产品不可以操作')
      return
    }
    setProductFormData(lastFormData.value)
    router.push({
      name: 'CopyItemProduct',
      query: {
        id: row?.id,
        pane: 'BillingInfo',
        type: 'voc',
        productNumber: row?.productNumber
      }
    })
  }

  // 导入
  const importType = ref(ImportTypeEnum.PRODUCT)
  const importDialogVisible = ref(false)
  const handleImport = (importTypeArg: ImportTypeEnum) => {
    importType.value = importTypeArg
    importDialogVisible.value = true
  }

  // 版本
  const versionDialogVisible = ref(false)
  const handleOpenVersionDialog = (row: ProductListPageAPI.Row) => {
    currentRow.value = row
    versionDialogVisible.value = true
  }

  // 产品衍生
  const deriveDialogVisible = ref(false)
  const handleOpenDeriveDialog = (row: ProductListPageAPI.Row) => {
    currentRow.value = row
    deriveDialogVisible.value = true
  }

  return {
    viewList,
    isShowList,
    cardRef,
    importType,
    currentView,
    selectedRows,
    fetchAllViews,
    handleConfigView,
    statusDialogVisible,
    handleChangeStatus,
    customColumnsDialogVisible,
    distributeDialogVisible,
    factoryDialogVisible,
    addColorSizeDialogVisible,
    handleAddColorSize,
    handleConfigConfirmSample,
    handleReviewConfirmSample,
    handleSelectionProduct,
    handleConfigMaterialInfo,
    handleConfigMaterialRatio,
    handleReviewSample,
    handleReviewColor,
    handleConfigColor,
    handleDesignProduct,
    handleCreateProduct,
    handleEditProduct,
    handleOpenDistributeInfoDialog,
    handleOpenFactoryInfoDialog,
    handleQuickCreate,
    handleEditQuickCreate,
    handleCheckedChange,
    importDialogVisible,
    handleImport,
    currentRow,
    handleCopyProduct,
    versionDialogVisible,
    handleOpenVersionDialog,
    deriveDialogVisible,
    handleOpenDeriveDialog
  }
}

const {
  isShowList,
  cardRef,
  viewList,
  importType,
  currentView,
  selectedRows,
  fetchAllViews,
  handleConfigView,
  statusDialogVisible,
  addColorSizeDialogVisible,
  handleAddColorSize,
  handleChangeStatus,
  handleConfigConfirmSample,
  handleReviewConfirmSample,
  handleSelectionProduct,
  handleReviewSample,
  handleReviewColor,
  customColumnsDialogVisible,
  distributeDialogVisible,
  handleConfigMaterialInfo,
  handleConfigMaterialRatio,
  handleCreateProduct,
  handleEditProduct,
  handleConfigColor,
  handleOpenDistributeInfoDialog,
  factoryDialogVisible,
  handleDesignProduct,
  handleOpenFactoryInfoDialog,
  handleQuickCreate,
  handleEditQuickCreate,
  handleCheckedChange,
  importDialogVisible,
  handleImport,
  handleCopyProduct,
  currentRow,
  versionDialogVisible,
  handleOpenVersionDialog,
  deriveDialogVisible,
  handleOpenDeriveDialog
} = useOperation()

onActivated(() => {
  const query = route.query
  const params = history.state
  if (params?.productNumber) {
    formData.value.productNumber = [params.productNumber]
  }
  if (hasValue(query)) {
    queryByRoute()
    return
  }

  const { productFormData } = useHelpStore()
  if (productFormData && hasValue(productFormData)) {
    formData.value = {
      ...formData.value,
      ...productFormData
    }
  }
  handleQuery()
})

// dialog确定后选中那条数据

const reloadSearch = () => {
  formData.value.productNumber = (selectedRows.value?.map((e) => e.productNumber) as string[]) || []
  handleQuery()
}

onMounted(() => {
  handleQuery()
  watchDebounced(
    formData,
    async () => {
      await handleQuery()
    },
    { deep: true, debounce: 400 }
  )
})

defineExpose({
  tableRef
})
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_200px)] overflow-x-hidden overflow-y-auto">
        <ElForm
          ref="formRef"
          :label-width="formLabelLength"
          :model="formData"
          :rules="formRules"
          @submit="
            (e) => {
              e.preventDefault()
            }
          "
        >
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="产品编号" prop="productNumber">
                <SelectPlus
                  ref="productNumberRef"
                  v-model="formData.productNumber"
                  api-key="getProductNumberList"
                  filterable
                  multiple
                  virtualized
                  @change="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="开发季节" prop="launchSeason">
                <SelectPlus
                  v-model="formData.launchSeason"
                  api-key="COMMON_MARKET_SEASON"
                  cache
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  multiple
                  placeholder="请选择"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElScrollbar class="w-full" style="height: 32px">
                <ElFormItem class="form-item-no-wrap" label="" label-width="0">
                  <ElButton text @click="setVisible">
                    {{ visible ? '收起' : '展开' }}
                    <Icon
                      :class="visible ? 'rotate-90' : ''"
                      class="transform transition duration-400"
                      icon="ant-design:down-outlined"
                    />
                  </ElButton>
                  <ElButton
                    :loading="queryLoading"
                    class="w-16"
                    native-type="submit"
                    type="primary"
                    @click="handleQuery"
                  >
                    <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
                    查询
                  </ElButton>
                  <ElButton
                    :loading="queryLoading"
                    class="w-16"
                    native-type="reset"
                    @click="handleReset"
                  >
                    <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
                    重置
                  </ElButton>
                  <ElDropdown v-if="!isEmbed" class="ml-3" @command="handleExport">
                    <ElButton :loading="exportLoading" class="w-16" type="primary">
                      <Icon class="mr-1" icon="ep:upload-filled" />
                      导出
                    </ElButton>
                    <template #dropdown>
                      <ElDropdownMenu>
                        <ElDropdownItem
                          v-for="item in exportList"
                          :key="item.value"
                          :command="item.value"
                        >
                          {{ item.label }}
                        </ElDropdownItem>
                      </ElDropdownMenu>
                    </template>
                  </ElDropdown>
                </ElFormItem>
              </ElScrollbar>
            </ElCol>
            <ElCol :span="16">
              <ElFormItem label="品牌" prop="brand">
                <SelectPlus
                  v-model="formData.brand"
                  api-key="baseBrand"
                  cache
                  checkbox
                  checkbox-button
                  placeholder="请选择品牌"
                />
              </ElFormItem>
            </ElCol>
            <ElCollapseTransition>
              <ElCol :span="16">
                <div v-show="visible" class="grid grid-cols-2 items-start w-full">
                  <ElFormItem label="Style编号" prop="styleNumber">
                    <SelectPlus
                      ref="styleNumberRef"
                      v-model="formData.styleNumber"
                      api-key="getStyleNumberList"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      placeholder="请选择"
                      virtualized
                    />
                  </ElFormItem>
                  <ElFormItem label="PRODUCT NAME" prop="productName">
                    <ElInput
                      v-model="formData.productName"
                      class="w-48"
                      clearable
                      maxlength="100"
                      placeholder="请输入"
                      show-word-limit
                    />
                  </ElFormItem>
                  <ElFormItem label="楦头类别" prop="brand">
                    <SelectPlus
                      v-model="formData.toeStandard"
                      api-key="LAST_TYPE"
                      cache
                      checkbox
                      checkbox-button
                      class="flex flex-nowrap"
                      @change="handleQuery"
                    />
                  </ElFormItem>
                  <ElFormItem label="适用人群" prop="targetAudience">
                    <SelectPlus
                      v-model="formData.targetAudience"
                      api-key="PRODUCT_PEOPLE"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="产品风格" prop="productStyle">
                    <SelectPlus
                      v-model="formData.productStyle"
                      api-key="PRODUCT_STYLE"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="产品企划" prop="designPersonId">
                    <CascadeSelector
                      v-model="formData.designPersonId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="创建时间" prop="date">
                    <ElDatePicker
                      v-model="formData.date"
                      :default-time="defaultTime"
                      class="max-w-48"
                      clearable
                      end-placeholder="结束时间"
                      range-separator="~"
                      start-placeholder="开始时间"
                      type="daterange"
                      unlink-panels
                      value-format="YYYY-MM-DD"
                    />
                  </ElFormItem>
                  <ElFormItem label="设计师" prop="designerId">
                    <CascadeSelector
                      v-model="formData.designerId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="供应商" prop="assignedFactory">
                    <SelectPlus
                      v-model="formData.assignedFactory"
                      api-key="getSupplierList"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="区域" prop="region">
                    <SelectPlus
                      v-model="formData.region"
                      api-key="COMMON_REGION"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="产品类目" prop="productCategory">
                    <ElCascader
                      v-model="formData.productCategory"
                      :options="productCategoryList"
                      :props="{
                        emitPath: false,
                        checkStrictly: true,
                        value: 'selectorKey',
                        label: 'selectorEnValue',
                        children: 'childList',
                        multiple: true
                      }"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="Style（WMS）" prop="styleWms">
                    <SelectPlus
                      v-model="formData.styleWms"
                      api-key="getStyleWmsList"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                      virtualized
                    />
                  </ElFormItem>
                  <ElFormItem label="产品阶段" prop="developStage">
                    <SelectPlus
                      v-model="formData.developStage"
                      api-key="PRODUCT_STAGE"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="任务节点" prop="taskNode">
                    <SelectPlus
                      v-model="formData.taskNode"
                      api-key="PRODUCT_TASK_NOTE"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="开发类型" prop="developmentType">
                    <SelectPlus
                      v-model="formData.developmentType"
                      api-key="PRODUCT_DEV_TYPE"
                      clearable
                      filterable
                      multiple
                      placeholder="请选择开发类型"
                    />
                  </ElFormItem>
                  <ElFormItem label="数据状态" prop="dataStatus">
                    <SelectPlus
                      v-model="formData.dataStatus"
                      :disabled="isEmbed"
                      api-key="PRODUCT_DATA_STATUS"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="开发跟进人员" prop="developmentContactPersonId">
                    <CascadeSelector
                      v-model="formData.developmentContactPersonId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="技术跟进人员" prop="technicalContactPersonId">
                    <CascadeSelector
                      v-model="formData.technicalContactPersonId"
                      :props="{ multiple: true, emitPath: false }"
                      api-key="allUsers"
                      cache
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="头型" prop="toeShape">
                    <SelectPlus
                      v-model="formData.toeShape"
                      api-key="HEEL_HEAD"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="产品配色" prop="colorId">
                    <SelectPlus
                      v-model="formData.colorId"
                      api-key="colorDrop"
                      filterable
                      multiple
                      virtualized
                    />
                  </ElFormItem>
                  <ElFormItem label="产品楦型" prop="associatedLastType">
                    <SelectPlus
                      v-model="formData.associatedLastType"
                      api-key="lastDrop"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                    />
                  </ElFormItem>
                  <ElFormItem label="母Style" prop="vocOriginalStyleNumber">
                    <SelectPlus
                      ref="vocProductNumberRef"
                      v-model="formData.vocOriginalStyleNumber"
                      api-key="getProductNumberList"
                      filterable
                      virtualized
                      @change="handleQuery"
                    />
                  </ElFormItem>
                  <ElFormItem label="CT归属" prop="combatTeam">
                    <SelectPlus v-model="formData.combatTeam" api-key="COMBAT_TEAM" cache />
                  </ElFormItem>
                  <ElFormItem label="供应商为空" prop="associatedLastType">
                    <ElCheckbox v-model="formData.emptySupplier" />
                  </ElFormItem>
                  <ElFormItem label="选品渠道" prop="chooseChannel">
                    <SelectPlus
                      v-model="formData.chooseChannel"
                      api-key="PRODUCT_DEV_CHANNELS"
                      cache
                      clearable
                      filterable
                      multiple
                      placeholder="请选择选品渠道"
                    />
                  </ElFormItem>
                  <ElFormItem label="开发渠道" prop="developmentChannel">
                    <SelectPlus
                      v-model="formData.developmentChannel"
                      api-key="PRODUCT_DEV_CHANNELS"
                      clearable
                      filterable
                      multiple
                      placeholder="请选择开发渠道"
                    />
                  </ElFormItem>
                  <ElFormItem label="产品系列" prop="developmentDirection">
                    <SelectPlus
                      v-model="formData.developmentDirection"
                      api-key="PRODUCT_SERIES"
                      cache
                      clearable
                      filterable
                      multiple
                      placeholder="请选择产品系列"
                    />
                  </ElFormItem>
                  <ElFormItem label="主渠道标识" prop="mainChannelMark">
                    <SelectPlus
                      v-model="formData.mainChannelMark"
                      api-key="PRODUCT_CHANNEL_IDENTIFICATION"
                      cache
                      clearable
                      filterable
                      multiple
                      placeholder="请选择主渠道标识"
                    />
                  </ElFormItem>
                  <ElFormItem label="无打样单" prop="associatedLastType">
                    <ElCheckbox v-model="formData.emptyProof" />
                  </ElFormItem>
                </div>
              </ElCol>
            </ElCollapseTransition>
          </ElRow>
        </ElForm>
        <div v-show="!isEmbed" class="mb-[10px] min-h-8 flex justify-between items-center">
          <div>
            <ElButton
              v-hasPermi="'product:createProduct'"
              class="!m-1"
              type="primary"
              @click="handleCreateProduct"
            >
              <Icon icon="ep:plus" />
              <span class="text-[14px]">新增产品</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:designProduct'"
              class="!m-1"
              type="primary"
              @click="handleDesignProduct"
            >
              <Icon icon="ep:edit" />
              <span class="text-[14px]">款式设计</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:distributeInfo'"
              class="!m-1"
              type="primary"
              @click="handleOpenDistributeInfoDialog"
            >
              <Icon icon="game-icons:sewing-machine" />
              <span class="text-[14px]">打样分配</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:followSample'"
              class="!m-1"
              type="primary"
              @click="handleReviewSample"
            >
              <Icon icon="fluent-emoji-high-contrast:running-shoe" />
              <span class="text-[14px]">初样评审</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:configColor'"
              class="!m-1"
              type="primary"
              @click="handleConfigColor"
            >
              <Icon icon="ic:outline-color-lens" />
              <span class="text-[14px]">齐色样配置</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:followColor'"
              class="!m-1"
              type="primary"
              @click="handleReviewColor"
            >
              <Icon icon="fluent-emoji-high-contrast:running-shoe" />
              <span class="text-[14px]">齐色样评审</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:factoryInfo'"
              class="!m-1"
              type="primary"
              @click="handleOpenFactoryInfoDialog"
            >
              <Icon icon="mingcute:currency-cny-2-line" />
              <span class="text-[14px]">工厂报价</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:selectionProduct'"
              class="!m-1"
              type="primary"
              @click="handleSelectionProduct"
            >
              <Icon icon="bi:card-checklist" />
              <span class="text-[14px]">选品会</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:configMaterialInfo'"
              class="!m-1"
              type="primary"
              @click="handleConfigMaterialInfo"
            >
              <Icon icon="mdi:fabric-outline" />
              <span class="text-[14px]">材质信息维护</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:configMaterialRadio'"
              class="!m-1"
              type="primary"
              @click="handleConfigMaterialRatio"
            >
              <Icon icon="mdi:fabric-outline" />
              <span class="text-[14px]">材质占比维护</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:configConfirmSample'"
              class="!m-1"
              type="primary"
              @click="handleConfigConfirmSample"
            >
              <Icon icon="mingcute:shoe-2-line" />
              <span class="text-[14px]">确认样配置</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:followConfirmSample'"
              class="!m-1"
              type="primary"
              @click="handleReviewConfirmSample"
            >
              <Icon icon="mingcute:shoe-2-line" />
              <span class="text-[14px]">确认样评审</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:changeStatus'"
              class="!m-1"
              type="primary"
              @click="handleChangeStatus()"
            >
              <Icon icon="ion:switch" />
              <span class="text-[14px]">生效/作废</span>
            </ElButton>
            <ElDropdown class="m-1" @command="handleImport">
              <ElButton type="primary">
                <Icon icon="ant-design:import-outlined" />
                <span class="text-[14px]">导入</span>
              </ElButton>
              <template #dropdown>
                <ElDropdownMenu>
                  <div v-hasPermi="'product:importProduct'">
                    <ElDropdownItem :command="ImportTypeEnum.PRODUCT">
                      <span>批量导入产品</span>
                    </ElDropdownItem>
                  </div>
                  <div v-hasPermi="'product:importOrderVolume'">
                    <ElDropdownItem :command="ImportTypeEnum.ORDER_VOLUME">
                      <span>批量导入预估单量</span>
                    </ElDropdownItem>
                  </div>
                  <div v-hasPermi="'product:importProductPrice'">
                    <ElDropdownItem :command="ImportTypeEnum.PRODUCT_PRICE">
                      <span>批量导入产品价格</span>
                    </ElDropdownItem>
                  </div>
                  <div v-hasPermi="'product:importQuickCreate'">
                    <ElDropdownItem :command="ImportTypeEnum.QUICK_CREATE">
                      <span>批量导入快速建档</span>
                    </ElDropdownItem>
                  </div>
                </ElDropdownMenu>
              </template>
            </ElDropdown>
            <ElDropdown class="m-1" @command="handleImport">
              <ElButton type="primary">
                <Icon icon="ant-design:import-outlined" />
                <span class="text-[14px]">批量修改</span>
              </ElButton>
              <template #dropdown>
                <ElDropdownMenu>
                  <div v-hasPermi="'product:importAllProduct'">
                    <ElDropdownItem :command="ImportTypeEnum.UPDATE_ALL_PRODUCT">
                      <span>导入修改产品档案</span>
                    </ElDropdownItem>
                  </div>
                  <div v-hasPermi="'product:importSKC'">
                    <ElDropdownItem :command="ImportTypeEnum.UPDATE_SKC">
                      <span>导入修改产品SKC</span>
                    </ElDropdownItem>
                  </div>
                </ElDropdownMenu>
              </template>
            </ElDropdown>
            <ElButton
              v-hasPermi="'product:quickCreate'"
              class="!m-1"
              type="primary"
              @click="handleQuickCreate"
            >
              <Icon icon="ep:plus" />
              <span class="text-[14px]">快速建档</span>
            </ElButton>
            <ElButton
              v-hasPermi="'product:addColorSize'"
              class="!m-1"
              type="primary"
              @click="handleAddColorSize"
            >
              <Icon icon="ep:plus" />
              <span class="text-[14px]">加色/加码</span>
            </ElButton>
          </div>
          <div class="flex flex-nowrap">
            <ElSelect v-model="currentView" class="min-w-36 mr-1" filterable value-key="name">
              <ElOption
                v-for="item in viewList"
                :key="item.name"
                :label="item.name"
                :value="item"
              />
            </ElSelect>

            <ElButton type="primary" @click="handleConfigView">
              <Icon icon="uil:setting" />
              <span class="text-[14px]">配置视图</span>
            </ElButton>
          </div>
        </div>
        <CardView
          v-if="!isShowList"
          ref="cardRef"
          :loading="queryLoading"
          :max-height="maxHeight"
          :table-data="tableData"
          @edit-product="handleEditProduct"
          @copy-product="handleCopyProduct"
          @change-status="handleChangeStatus"
          @view-product="handleDetail"
          @checkbox-change="handleCheckedChange"
        />
        <div class="vxe-table" v-else>
          <VxeGrid
            ref="tableRef"
            v-bind="tableOptions"
            @checkbox-change="handleCheckedChange"
            @checkbox-all="handleCheckedChange"
          />
        </div>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
      <CustomColumnsDialog
        ref="columnSettingRef"
        v-model="customColumnsDialogVisible"
        :view-list="viewList"
        column-type="product"
        @refresh="(viewId) => fetchAllViews(viewId)"
      />
      <DistributeInfoDialog
        v-model="distributeDialogVisible"
        :selected-rows="selectedRows"
        @refresh="reloadSearch"
      />
      <FactoryQuotationDialog
        v-model="factoryDialogVisible"
        :selected-rows="selectedRows"
        @refresh="reloadSearch"
      />
      <StatusDialog
        v-model="statusDialogVisible"
        :selected-rows="selectedRows"
        @refresh="reloadSearch"
      />
      <AddColorSizeDialog
        v-model="addColorSizeDialogVisible"
        :form-data="currentRow"
        @refresh="
          () => {
            if (currentRow?.productNumber) formData.productNumber = [currentRow.productNumber]
            handleQuery()
          }
        "
      />
      <ImportDialog v-model="importDialogVisible" :type="importType" @refresh="handleQuery" />
      <VersionDialog v-model="versionDialogVisible" :current-row="currentRow" />
      <DeriveDialog
        v-model="deriveDialogVisible"
        :current-row="currentRow"
        @refresh="
          () => {
            handleQuery()
            productNumberRef?.queryOptions()
          }
        "
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
.ellipsis-cell {
  height: 80px;
}
</style>
