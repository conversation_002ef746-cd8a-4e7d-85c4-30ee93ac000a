import { CreateProductInfoAPI } from './productInfo'
import { service } from '@/config/axios/service'
import to from 'await-to-js'

export namespace AddColorSizeAPI {
  export interface Request {
    id?: number | null
    /**
     * 是否为老数据 0否 1是
     */
    oldData?: number
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 选中尺码
     */
    selectedSize?: number[] | string[]
    /**
     * 尺码段id表
     */
    sizeRangeId?: number[]
    /**
     * 加色之后的skc数据
     */
    skcInfoDetailResp?: CreateProductInfoAPI.SKCInfo[]
  }
  export type Response = BasicResponseData
}

export function addColorSize(data: AddColorSizeAPI.Request) {
  return to<AddColorSizeAPI.Response>(service.post('/pdm-base/pdmProductInfo/addColorSize', data))
}

export namespace GetSkcDetailInfoByProductIdAPI {
  export type Response = ResponseData<CreateProductInfoAPI.SKCInfo[]>
}
export function getSkcDetailInfoByProductId(id: number) {
  return to<GetSkcDetailInfoByProductIdAPI.Response>(
    service.get(`/pdm-base/pdmProductInfo/skcDetail/${id}`)
  )
}
