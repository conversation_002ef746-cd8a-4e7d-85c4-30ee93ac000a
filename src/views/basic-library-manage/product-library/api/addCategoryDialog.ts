import { service } from '@/config/axios/service'
import to from 'await-to-js'

export namespace AddCategoryAPI {
  export interface Params {
    /**
     * 款式结构
     */
    structure?: string[]
    /**
     * 分类中文名称
     */
    categoryCnName?: string
    levelCode?: string
    /**
     * 分类编码
     */
    categoryCode?: string
    /**
     * 分类英文名称
     */
    categoryEnName?: string
    errorList?: string[]
    /**
     * 数据ID
     */
    id?: number
    /**
     * 父类ID 最顶级固定为0
     */
    parentId?: number
    /**
     * 状态
     */
    status?: string
    /**
     * 商品类目ID
     */
    wmsCategoryIdList?: number[]
  }
  export type Request = Params
  export type Response = BasicResponseData
}
export function addCategory(data: AddCategoryAPI.Params) {
  return to<AddCategoryAPI.Response>(service.post('/pdm-base/product/category/add', data))
}

export function updateCategory(data: AddCategoryAPI.Params) {
  return to<AddCategoryAPI.Response>(service.post('/pdm-base/product/category/update', data))
}
