import to from 'await-to-js'
import { service } from '@/config/axios/service'

export enum LastDataStatusEnum {
  Approving = 'APPROVING',
  Ban = 'BAN',
  Draft = 'DRAFT',
  Start = 'START'
}

export namespace ProductCategoryAPI {
  export interface Params {
    categoryName?: string
    parentId?: number
  }
  export interface Data {
    categoryCnName?: string
    categoryCode?: string
    categoryEnName?: string
    categoryParentCnName?: string
    categoryParentEnName?: string
    childList?: Data[]
    createById?: number
    createByName?: string
    /**
     * 操作时间
     */
    createTime?: string
    delFlag?: number
    id?: number
    index?: number
    lastDataStatusEnum?: LastDataStatusEnum
    modifyById?: number
    modifyByName?: string
    modifyTime?: string
    parentId?: number
    status?: string
    wmsCategoryCode?: string
    wmsCategoryId?: number
    wmsCategoryName?: string
  }
  export type Request = Params
  export type Response = ResponseData<Data[]>
}
export function getProductCategoryList(data: ProductCategoryAPI.Request) {
  return to<ProductCategoryAPI.Response>(service.post('/pdm-base/product/category/lists', data))
}

export namespace ProductCategoryStatusAPI {
  export interface Params {
    /**
     * 当前状态
     */
    beforeStatus?: string
    /**
     * 数据ID
     */
    id?: number
    /**
     * 数据状态 start:启用  ban:禁用
     */
    status?: string
  }
  export type Request = Params
  export type Response = BasicResponseData
}
export function updateProductCategoryStatus(data: ProductCategoryStatusAPI.Request) {
  return to<ProductCategoryStatusAPI.Response>(
    service.post('/pdm-base/product/category/status', data)
  )
}
