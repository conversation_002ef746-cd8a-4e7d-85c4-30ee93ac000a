import to from 'await-to-js'
import request from '@/config/fetch'

export enum SuccessEnum {
  SUCCESS = 0,
  ERROR = 1
}

export namespace ImportProductListAPI {
  export interface Row {
    /**
     * 适用季节
     */
    applicableSeason?: string
    /**
     * 品牌
     */
    brand?: string
    /**
     * 成本区间($)
     */
    cost?: string
    /**
     * 产品企划
     */
    designPersonIdStr?: string
    /**
     * 是否STC
     */
    dtc?: string
    /**
     * 错误原因
     */
    errorMsg?: string
    /**
     * 预估售价($)
     */
    estimatedPriceStr?: string
    /**
     * 期望上架日期
     */
    expectedLaunchDateStr?: string
    /**
     * 楦型标准
     */
    lastsStandard?: string
    /**
     * 上市季节
     */
    launchSeason?: string
    /**
     * 小类
     */
    productCategory?: string
    /**
     * 大类
     */
    productCategoryMax?: string
    /**
     * 中类
     */
    productCategoryMidden?: string
    /**
     * 品类
     */
    productCategoryParent?: string
    /**
     * 产品风格
     */
    productStyle?: string
    /**
     * 备注
     */
    remake?: string
    /**
     * 款式结构
     */
    styleStructure?: string
    /**
     * 是否成功 0 成功 1失败
     */
    success?: number
    /**
     * 适用人群
     */
    targetAudience?: string
    /**
     * 楦头类别
     */
    toeStandard?: string
    /**
     * 设计图
     */
    designUrl?: BaseFileDTO[]
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}

export function getImportProductList(data: BaseFileDTO) {
  return to<ImportProductListAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductInfo/importProductExcel',
      data
    })
  )
}

export namespace ImportProductAPI {
  export type Request = { reqList: ImportProductListAPI.List }
  export type Response = BasicResponseData
}
export function importProduct(data: ImportProductAPI.Request) {
  return to<ImportProductAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductInfo/importProduct',
      data
    })
  )
}

export namespace ImportOrderVolumeListAPI {
  export interface Row {
    /**
     * 品牌
     */
    brand?: string
    /**
     * 选品渠道
     */
    chooseChannel?: string
    /**
     * 产品配色
     */
    colorIdItemName?: string
    /**
     * 错误原因
     */
    errorMsg?: string
    /**
     * 预估订单量
     */
    estimatedOrderVolume?: number
    /**
     * 上市季节
     */
    launchSeason?: string
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * SKC编号
     */
    skcCode?: string
    /**
     * 是否成功 0 成功 1失败
     */
    success?: number
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}
export function getImportOrderVolumeList(data: BaseFileDTO) {
  return to<ImportOrderVolumeListAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductInfo/importProductOrderExcel',
      data
    })
  )
}

export namespace ImportOrderVolumeAPI {
  export type Request = { reqList: ImportOrderVolumeListAPI.List }
  export type Response = BasicResponseData
}
export function importOrderVolume(data: ImportOrderVolumeAPI.Request) {
  return to<ImportOrderVolumeAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductInfo/importProductOrder',
      data
    })
  )
}

export namespace ImportPriceListAPI {
  export interface Row {
    /**
     * 错误原因
     */
    errorMsg?: string
    /**
     * 是否成功 0 成功 1失败
     */
    success?: number
    brand?: string
    launchSeason?: string
    productNumber?: string
    productPositioning?: string
    shoeName?: string
    retailPrice?: string
    minPrice?: string
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}
export function getImportPriceList(data: BaseFileDTO) {
  return to<ImportPriceListAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductInfo/importProductPriceExcel',
      data
    })
  )
}

export namespace ImportPriceAPI {
  export type Request = { reqList: ImportPriceListAPI.List }
  export type Response = BasicResponseData
}
export function importPrice(data: ImportPriceAPI.Request) {
  return to<ImportPriceAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductInfo/importProductPrice',
      data
    })
  )
}

export namespace ImportAllProductListAPI {
  export interface Row {
    /**
     * 齐色样图片
     */
    sampleUrl?: BaseFileDTO[]
    /**
     * 是否过踝/过膝
     */
    ankleCoverage?: null | string
    /**
     * 适用季节
     */
    applicableSeason?: null | string
    /**
     * 供应商
     */
    assignedFactory?: null | string
    /**
     * 品牌
     */
    brand?: null | string
    /**
     * 儿童年龄段
     */
    childrenAvgGroup?: null | string[]
    /**
     * 儿童人群
     */
    childrenCrowd?: null | string
    /**
     * 选品渠道
     */
    chooseChannel?: string[] | null
    /**
     * 穿脱方式
     */
    closureMethod?: null | string
    /**
     * 颜色
     */
    colorId?: null | string
    /**
     * 配色类型
     */
    colorType?: null | string
    /**
     * 确认样打样单
     */
    confirmationSampleOrderUrl?: BaseFileDTO[]
    /**
     * 是否需要侵权排查
     */
    copyright?: null | string
    /**
     * 成本区间($)
     */
    cost?: null | string
    /**
     * 产品设计师
     */
    designerIdStr?: null | string
    /**
     * 产品企划
     */
    designPersonIdStr?: null | string
    /**
     * 设计图
     */
    designUrl?: BaseFileDTO[]
    /**
     * 开发渠道
     */
    developmentChannel?: null | string
    /**
     * 跟进开发人员Id
     */
    developmentContactPersonId?: null | string
    /**
     * 开发策略
     */
    developmentStrategy?: null | string
    /**
     * 开发类型
     */
    developmentType?: null | string
    /**
     * 是否S2C
     */
    dtc?: null | string
    /**
     * 错误原因
     */
    errorMsg?: null | string
    /**
     * 预估售价($)
     */
    estimatedPriceStr?: null | string
    /**
     * 期望上架日期
     */
    expectedLaunchDateStr?: null | string
    /**
     * 是否折叠
     */
    foldable?: null | string
    /**
     * 跟高
     */
    heelHeight?: null | string
    /**
     * 跟型
     */
    heelType?: null | string
    /**
     * 初样颜色
     */
    initialSampleColorId?: null | string
    /**
     * 下发打样日期
     */
    initialSampleIssueDate?: null | string
    /**
     * 楦底类型
     */
    lastBottomType?: null | string
    /**
     * 楦型标准
     */
    lastsStandard?: null | string
    /**
     * 上市季节
     */
    launchSeason?: null | string
    /**
     * 里材料
     */
    liningMaterialCode?: number[] | null
    /**
     * 里绒情况
     */
    liningSituation?: number[] | null
    /**
     * 主渠道标识
     */
    mainChannelMark?: null | string
    /**
     * 主面料
     */
    mainFabric?: null | string
    /**
     * 齐色样打样单
     */
    matchingSampleUrl?: BaseFileDTO[]
    /**
     * 面料1
     */
    materialIndex1?: number | null
    /**
     * 面料2
     */
    materialIndex2?: number | null
    /**
     * 面料3
     */
    materialIndex3?: number | null
    /**
     * 面料4
     */
    materialIndex4?: number | null
    /**
     * 材料送检说明
     */
    materialInspectionInstructions?: null | string
    /**
     * 面料1占比%
     */
    materialRatio1?: number | null
    /**
     * 面料2占比%
     */
    materialRatio2?: number | null
    /**
     * 面料3占比%
     */
    materialRatio3?: number | null
    /**
     * 面料4占比%
     */
    materialRatio4?: number | null
    /**
     * 选品会结果
     */
    meetingResult?: null | string
    /**
     * 品牌底线价格
     */
    minPrice?: null | string
    /**
     * 开口类型
     */
    openingType?: null | string
    /**
     * 外发打样日期
     */
    outsourcingIssueDate?: null | string
    /**
     * 垫材料（填充）
     */
    paddingMaterialFillingCode?: number[] | null
    /**
     * 垫材料（面）
     */
    paddingMaterialSurfaceCode?: number[] | null
    /**
     * 小类
     */
    productCategory?: null | string
    /**
     * 大类
     */
    productCategoryMax?: null | string
    /**
     * 中类
     */
    productCategoryMidden?: null | string
    /**
     * 品类
     */
    productCategoryParent?: null | string
    /**
     * 产品目标定级
     */
    productPositioning?: null | string
    /**
     * 产品风格
     */
    productStyle?: null | string
    /**
     * 区域
     */
    region?: null | string
    /**
     * 备注
     */
    remake?: null | string
    /**
     * 吊牌价
     */
    retailPrice?: null | string
    /**
     * *是否含金属鞋头
     */
    safetyFeatures?: null | string
    /**
     * 齐色打样日期
     */
    sameColorSampleDate?: null | string
    /**
     * 导入序号
     */
    sameSku?: null | string
    /**
     * 初样打样单
     */
    samplePrintUrl?: BaseFileDTO[]
    /**
     * 场景
     */
    scene?: null | string
    /**
     * 选中尺码
     */
    selectedSize?: number[] | null
    /**
     * 选品会评审意见
     */
    selectionMeetingReviewOpinion?: null | string
    /**
     * SHOES NAME
     */
    shoeName?: null | string
    /**
     * 鞋底工艺
     */
    shoeSoleTechnique?: null | string
    /**
     * 尺码段
     */
    sizeRangeId?: null | string
    /**
     * 底材料（大底）
     */
    soleMaterialOutsoleCode?: number[] | null
    /**
     * 特殊包装方式
     */
    specialPackagingMethod?: null | string
    /**
     * 特殊检测需求/说明
     */
    specialTestingRequirements?: null | string
    /**
     * 标准码
     */
    standardSizeValue?: null | string
    /**
     * 款式定位
     */
    stylePositioning?: null | string
    /**
     * 款式结构
     */
    styleStructure?: null | string
    /**
     * Style(WMS)
     */
    styleWms?: null | string
    /**
     * 是否成功 0 成功 1失败
     */
    success?: number | null
    /**
     * 适用人群
     */
    targetAudience?: null | string
    /**
     * 跟进技术人员Id
     */
    technicalContactPersonId?: null | string
    /**
     * 头型
     */
    toeShape?: null | string
    /**
     * 楦头类别
     */
    toeStandard?: null | string
    /**
     * 流行元素
     */
    trendyElements?: null | string
    uuid?: null | string
    /**
     * 是否植绒
     */
    velvetApplied?: null | string
    /**
     * 植绒要求
     */
    velvetRequirements?: null | string
    /**
     * *防水级别
     */
    waterproofLevel?: null | string
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}

export namespace ImportAllProductAPI {
  export type Request = { reqList: ImportAllProductListAPI.List }
  export type Response = BasicResponseData
}

export namespace UpdateAllProductListAPI {
  export interface Row extends ImportAllProductListAPI.Row {
    productNumber?: string
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}

export function getUpdateAllProductList(data: BaseFileDTO) {
  return to<ImportAllProductListAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductInfo/importAllProductUpdateExcel',
      data
    })
  )
}

export namespace UpdateAllProductAPI {
  export type Request = { reqList: UpdateAllProductListAPI.List }
  export type Response = BasicResponseData
}
export function updateAllProduct(data: UpdateAllProductAPI.Request) {
  return to<ImportAllProductAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductInfo/importAllProductUpdateInfoImportTemplateExcel',
      data
    })
  )
}

export namespace ImportQuickCreateProductListAPI {
  export interface Row {
    /**
     * 是否过踝/过膝
     */
    ankleCoverage?: null | string
    /**
     * 适用季节
     */
    applicableSeason?: null | string
    /**
     * 供应商
     */
    assignedFactory?: null | string
    /**
     * 基本计量单位
     */
    basicUnitOfMeasure?: null | string
    /**
     * 品牌
     */
    brand?: null | string
    /**
     * 儿童年龄段
     */
    childrenAvgGroup?: null | string[]
    /**
     * 儿童人群
     */
    childrenCrowd?: null | string
    /**
     * 选品渠道
     */
    chooseChannel?: null | string
    /**
     * 穿脱方式
     */
    closureMethod?: null | string
    /**
     * 产品配色
     */
    colorId?: null | string
    /**
     * 配色类型
     */
    colorType?: null | string
    /**
     * 是否需要侵权排查
     */
    copyright?: null | string
    /**
     * 成本区间($)
     */
    cost?: null | string
    /**
     * 产品设计师
     */
    designerIdStr?: null | string
    /**
     * 产品企划
     */
    designPersonIdStr?: null | string
    /**
     * 设计图
     */
    designUrl?: BaseFileDTO[]
    /**
     * 开发渠道
     */
    developmentChannel?: null | string
    /**
     * todo
     * 开发模式
     */
    developmentModel?: null | string
    /**
     * 是否S2C
     */
    dtc?: null | string
    /**
     * 错误原因
     */
    errorMsg?: null | string
    /**
     * 预估售价($)
     */
    estimatedPriceStr?: null | string
    /**
     * 期望上架日期
     */
    expectedLaunchDateStr?: null | string
    /**
     * 是否折叠
     */
    foldable?: null | string
    /**
     * 跟高
     */
    heelHeight?: null | string
    /**
     * 跟型
     */
    heelType?: null | string
    /**
     * 楦型标准
     */
    lastsStandard?: null | string
    /**
     * 上市季节
     */
    launchSeason?: null | string
    /**
     * 主渠道标识
     */
    mainChannelMark?: null | string
    /**
     * 主面料
     */
    mainFabric?: null | string
    /**
     * 面料1
     */
    materialIndex1?: null | string
    /**
     * 面料2
     */
    materialIndex2?: null | string
    /**
     * 面料3
     */
    materialIndex3?: null | string
    /**
     * 面料4
     */
    materialIndex4?: null | string
    /**
     * 面料1占比%
     */
    materialRatio1?: null | string
    /**
     * 面料2占比%
     */
    materialRatio2?: null | string
    /**
     * 面料3占比%
     */
    materialRatio3?: null | string
    /**
     * 面料4占比%
     */
    materialRatio4?: null | string
    /**
     * 选品会结果
     */
    meetingResult?: null | string
    /**
     * 无需排查原因
     */
    noCpyrightReason?: null | string
    /**
     * 小类
     */
    productCategory?: null | string
    /**
     * 大类
     */
    productCategoryMax?: null | string
    /**
     * 中类
     */
    productCategoryMidden?: null | string
    /**
     * 产品目标定级
     */
    productPositioning?: null | string
    /**
     * 产品风格
     */
    productStyle?: null | string
    /**
     * 区域
     */
    region?: null | string
    /**
     * 吊牌价
     */
    retailPrice?: null | string
    /**
     * 是否含金属鞋头
     */
    safetyFeatures?: null | string
    /**
     * 导入序号
     */
    sameSkc?: null | string
    /**
     * 齐色样图片
     */
    sampleUrl?: BaseFileDTO[]
    /**
     * 场景
     */
    scene?: null | string
    /**
     * 选中尺码
     */
    selectedSize?: null | string
    /**
     * 选品会评审意见
     */
    selectionMeetingReviewOpinion?: null | string
    /**
     * SHOES NAME
     */
    shoeName?: null | string
    /**
     * 鞋底工艺
     */
    shoeSoleTechnique?: null | string
    /**
     * 尺码段
     */
    sizeRangeId?: null | string
    /**
     * 标准码
     */
    standardSizeValue?: null | string
    /**
     * 款式定位
     */
    stylePositioning?: null | string
    /**
     * 款式结构
     */
    styleStructure?: null | string
    /**
     * Style(WMS)
     */
    styleWms?: null | string
    /**
     * 是否成功 0 成功 1失败
     */
    success?: number | null
    /**
     * 适用人群
     */
    targetAudience?: null | string
    /**
     * 头型
     */
    toeShape?: null | string
    /**
     * 楦头类别
     */
    toeStandard?: null | string
    /**
     * 流行元素
     */
    trendyElements?: null | string
    /**
     * 是否植绒
     */
    velvetApplied?: null | string
    /**
     * 植绒要求
     */
    velvetRequirements?: null | string
    /**
     * 防水级别
     */
    waterproofLevel?: null | string
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}

export function getImportQuickCreateProductList(data: BaseFileDTO) {
  return to<ImportQuickCreateProductListAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductInfo/importQuickProductExcel',
      data
    })
  )
}

export namespace ImportQuickCreateProductAPI {
  export type Request = { reqList: ImportQuickCreateProductListAPI.List }
  export type Response = BasicResponseData
}

export function importQuickCreateProduct(data: ImportQuickCreateProductAPI.Request) {
  return to<ImportQuickCreateProductAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductInfo/importQuickProduct',
      data
    })
  )
}

export namespace ImportSKCListAPI {
  export interface Row {
    /**
     * 是否新增
     */
    add?: boolean
    /**
     * 产品配色
     */
    colorId?: null | string
    /**
     * 配色类型
     */
    colorType?: null | string
    /**
     * 错误原因
     */
    errorMsg?: null | string
    /**
     * 里材料
     */
    liningMaterialCode?: null | string
    /**
     * 里绒情况
     */
    liningSituation?: null | string
    /**
     * 主面料
     */
    mainFabric?: null | string
    /**
     * 面料1
     */
    materialIndex1?: null | string
    /**
     * 面料2
     */
    materialIndex2?: null | string
    /**
     * 面料3
     */
    materialIndex3?: null | string
    /**
     * 面料4
     */
    materialIndex4?: null | string
    /**
     * 面料1占比%
     */
    materialRatio1?: null | string
    /**
     * 面料2占比%
     */
    materialRatio2?: null | string
    /**
     * 面料3占比%
     */
    materialRatio3?: null | string
    /**
     * 面料4占比%
     */
    materialRatio4?: null | string
    /**
     * 垫材料（填充）
     */
    paddingMaterialFillingCode?: null | string
    /**
     * 垫材料（面）
     */
    paddingMaterialSurfaceCode?: null | string
    /**
     * *产品编号
     */
    productNumber?: null | string
    /**
     * 备注
     */
    remake?: null | string
    /**
     * 齐色样图片
     */
    sampleUrl?: BaseFileDTO[]
    /**
     * SKC编号
     */
    skcCode?: null | string
    /**
     * 底材料（大底）
     */
    soleMaterialOutsoleCode?: null | string
    /**
     * Style(WMS)
     */
    styleWms?: null | string
    /**
     * 是否植绒
     */
    velvetApplied?: null | string
    /**
     * 植绒要求
     */
    velvetRequirements?: null | string
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}

export function getImportSKCList(data: BaseFileDTO) {
  return to<ImportSKCListAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductInfo/importAllSkcUpdateExcel',
      data
    })
  )
}

export namespace ImportSKCAPI {
  export type Request = { reqList: ImportSKCListAPI.List }
  export type Response = BasicResponseData
}

export function importSKC(data: ImportSKCAPI.Request) {
  return to<ImportSKCAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductInfo/importAllSkcUpdate',
      data
    })
  )
}
