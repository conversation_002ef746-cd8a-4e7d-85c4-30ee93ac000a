import to from 'await-to-js'
import { service } from '@/config/axios/service'
import { CreateProductInfoAPI } from './productInfo'
import { ProductTaskNodeEnum } from '../const'
import { postJSON } from '@/config/fetch'

export namespace ProductListPageAPI {
  export type Params = {
    combatTeam?: string
    vocOriginalStyleNumber?: string
    emptySupplier?: boolean
    designPersonId?: number[]
    styleWms?: string[]
    emptyProof?: boolean
    /**
     * 是否关联过侵权排查
     */
    copyrightInspection?: string
    /**
     * 产品配色
     */
    colorId?: number[]
    /**
     * 供应商
     */
    assignedFactory?: string[]
    /**
     * 产品楦型
     */
    associatedLastType?: string[]
    /**
     * 品牌
     */
    brand?: number[]
    /**
     * 品类
     */
    categoryId?: number[]
    /**
     * 数据状态
     */
    dataStatus?: string[]
    /**
     * 产品设计师的id
     */
    designerId?: number[]
    /**
     * 跟进开发人员Id
     */
    developmentContactPersonId?: number[]
    /**
     * 开发类型
     */
    developmentType?: string[]
    /**
     * 产品阶段
     */
    developStage?: string[]
    /**
     * 创建时间结束时间
     */
    endDate?: string
    /**
     * 上市季节
     */
    launchSeason?: string[]
    /**
     * 产品类目,最低等级id
     */
    productCategory?: string[]
    /**
     * 产品编号（自动生成）
     */
    productNumber?: string[]
    /**
     * 类目风格
     */
    productStyle?: string[]
    /**
     * 区域
     */
    region?: string[]
    /**
     * 创建时间开始时间
     */
    startDate?: string
    /**
     * 适用人群
     */
    targetAudience?: string[]
    /**
     * 任务节点
     */
    taskNode?: string[]
    /**
     * 跟进技术人员Id
     */
    technicalContactPersonId?: number[]
    /**
     * 头型
     */
    toeShape?: string[]
    /**
     * 楦头标准
     */
    toeStandard?: string[]
    idList?: string[]
    /**
     * style编号
     */
    styleNumber?: string
    productName?: string
    developmentDirection?: string[]
    chooseChannel?: string[]
    mainChannelMark?: string[]
    developmentChannel?: string[]
  }
  export type Row = CreateProductInfoAPI.Request
  export type List = Row[]
  export type Request = Params & PageParams
  export type Response = PagedResponseData<Row>
}

export function getProductListByPage(data: ProductListPageAPI.Request, signal?: AbortSignal) {
  return postJSON<ProductListPageAPI.Response>({
    url: '/pdm-base/pdmProductInfo/page',
    data,
    signal
  })
}

export namespace ViewListAPI {
  export interface Row {
    /**
     * 字段属性(string)
     * 字段属性(string)集合
     */
    fieldAttr?: string[]
    fieldTitle?: string
    /**
     * 名称
     */
    name?: string
    /**
     * 类型(0 就是产品库)
     */
    type?: number
    id?: number
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}
export function getAllViews() {
  return to<ViewListAPI.Response>(service.get(`/pdm-base/viewConfiguration/allViewByLogin`))
}

export type ProductDetail = ProductListPageAPI.Row

export namespace BatchUpdateProductAPI {
  export interface Params {
    detailList: ProductDetail[]
    taskNode?: ProductTaskNodeEnum
  }
  export type Request = Params
  export type Response = BasicResponseData
}

export function batchUpdateProduct(data: BatchUpdateProductAPI.Request) {
  return to<BatchUpdateProductAPI.Response>(
    service.post('/pdm-base/pdmProductInfo/updateStatus', data)
  )
}
