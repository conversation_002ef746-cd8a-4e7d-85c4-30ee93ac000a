import to from 'await-to-js'
import { service } from '@/config/fetch/service'

export namespace StylePartnerCodeAPI {
  export interface BaseProductSku {
    /**
     * 颜色id
     * sku颜色
     * colorIdItemName
     */
    colorId?: number
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    /**
     * 状态
     */
    dataStatus?: string
    delFlag?: number
    /**
     * 数据下发失败说明
     */
    errorDataMsg?: string
    id?: number
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 是否已下单
     */
    orderPlaced?: string
    /**
     * 产品配色
     */
    productColor?: string
    /**
     * 产品主表的id
     */
    productId?: number
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 数据下发时间
     */
    sendTime?: string
    /**
     * 是否已下发WMS
     */
    sendWms?: string
    /**
     * 尺码id
     * sku尺码
     * sizeIdItemName
     */
    sizeId?: number
    sizeIdItemName?: string
    /**
     * SKC编号
     */
    skcCode?: string
    /**
     * sku编码
     */
    skuCode?: string
    /**
     * SKU Partner Code
     */
    skuPartnerCode?: string
    /**
     * 状态
     */
    skuStatus?: string
    /**
     * Style Partner Code
     */
    stylePartnerCode?: string
  }
  export interface Request {
    productId?: number
    sizeId?: number[]
    skcId?: number[]
  }
  export type Response = ResponseData<BaseProductSku[]>
}

export function getStylePartnerCode(params: StylePartnerCodeAPI.Request) {
  return to<StylePartnerCodeAPI.Response>(
    service.get(`/pdm-base/pdmProductInfo/skuDetail`, { params })
  )
}
