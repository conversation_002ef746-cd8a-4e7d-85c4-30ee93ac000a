import fetch from '@/config/fetch'
import to from 'await-to-js'
import { CodeListAPI } from '@/components/Business/SelectPlus/src/api/types'

export namespace FactoryListAPI {
  export type Data = {
    useStatus?: number
    /**
     * 地区名称
     */
    areaCode?: number
    /**
     * 地区名称
     */
    areaName?: string
    vendorId?: number
    /**
     * 工厂名称
     */
    vendorCode?: string
    /**
     * 工厂名称
     */
    vendorName?: string
    /**
     * 跟进技术人员id
     */
    tecUserId?: number
    /**
     * 跟进开发人员id
     */
    devUserId?: number
  }
  export type Response = ResponseData<Data[]>
}
export function getFactoryList() {
  return to<FactoryListAPI.Response>(fetch.get({ url: '/pdm-base/base/getFactory' }))
}

type DescriptionsItem = {
  label: string
  field: keyof ProductFactoryAPI.ProductFactoryResp
  span?: number
}

export const descriptionsItem: DescriptionsItem[] = [
  { field: 'productNumber', label: '产品编号' },
  { field: 'brandItemName', label: '品牌' },
  { field: 'launchSeason', label: '开发季节' },
  { field: 'productCategoryItemName', label: '产品类目' },
  { field: 'designerIdItemName', label: '设计师' },
  { field: 'createTime', label: '创建时间' },
  { field: 'cost', label: '成本区间($)' },
  { field: 'estimatedPrice', label: '预估售价($)' },
  { field: 'sizeRangeIdItemName', label: '尺码段' },
  { field: 'standardSizeValueItemName', label: '尺码值' }
]
export namespace ProductFactoryAPI {
  export interface ProductFactoryResp {
    /**
     * 品牌
     */
    brand?: number
    brandItemName?: string
    /**
     * 成本区间
     * 成本区间（$）
     */
    cost?: string
    /**
     * 创建时间
     */
    createTime?: string
    /**
     * 设计师
     */
    designerId?: number
    designerIdItemName?: string
    /**
     * 产品阶段
     */
    developStage?: string
    /**
     * 预估售价（$）
     */
    estimatedPrice?: number
    /**
     * 工厂配置
     */
    factoryConfigList?: FactoryConfig[]
    /**
     * 上市季节
     */
    launchSeason?: string
    /**
     * 产品类目
     */
    productCategory?: number
    productCategoryItemName?: number
    /**
     * 产品id
     */
    productId?: number
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 缩略图
     */
    productThumbnail?: BaseFileDTO[]
    /**
     * 尺码段
     */
    sizeRangeId?: number[]
    sizeRangeIdItemName?: string
    /**
     * 标准码
     */
    standardSizeValue?: string[]
    standardSizeValueItemName?: string
    /**
     * 开发模式
     */
    developmentModel?: string
    developmentModelItemName?: string
  }
  export interface FactoryConfig {
    /**
     * 供应商新增初样单id
     */
    addFactoryInitialId?: number[]
    /**
     * 补充说明
     */
    addInformationImg?: BaseFileDTO[]
    /**
     * 供应商
     */
    assignedFactory?: string
    assignedFactoryItemName?: string
    /**
     * 跟进开发人员Id
     */
    developmentContactPersonId?: number[]
    /**
     * 工厂报价（¥）含税
     */
    factoryQuotation?: number
    /**
     * 主键编号
     */
    id?: number
    /**
     * 产品id
     */
    productId?: number
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 区域
     */
    region?: string
    /**
     * 区域供管人员id
     */
    regionalSupplyPersonId?: number[]
    regionItemName?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 单色MOQ
     */
    singleColorMoq?: number
    /**
     * 单款MOQ
     */
    singleStyleMoq?: number
    /**
     * 跟进技术人员Id
     */
    technicalContactPersonId?: number[]
    [property: string]: any
  }
  export type Row = FactoryConfig
  export type List = Row[]
  export type Response = ResponseData<ProductFactoryResp>
}
export function getProductFactory(id: number) {
  return to<ProductFactoryAPI.Response>(
    fetch.get({
      url: `/pdm-base/pdmProductInfo/factoryList/${id}`
    })
  )
}

export function batchGetProductFactory(params: { idList: number[] }) {
  return to<ResponseData<ProductFactoryAPI.List>>(
    fetch.get({
      url: `/pdm-base/pdmProductInfo/factoryPriceList`,
      params
    })
  )
}

export namespace SaveProductFactoryAPI {
  export type Request = { factoryConfigList: ProductFactoryAPI.FactoryConfig[] }
  export type Response = BasicResponseData
}
export function saveProductFactory(data: SaveProductFactoryAPI.Request) {
  return to<SaveProductFactoryAPI.Response>(
    fetch.post({ url: '/pdm-base/pdmProductInfo/saveOrUpdateFactory', data })
  )
}

export namespace SampleListAPI {
  export interface AssignProofResp {
    productId?: number
    selectorList?: CodeListAPI.Data[]
    [property: string]: any
  }
  export type Response = ResponseData<AssignProofResp>
}
export function getSampleListByProductIds(id: number) {
  return to<SampleListAPI.Response>(fetch.get({ url: `/pdm-base/proof/assignProof/${id}` }))
}

export namespace TeamPeopleQueryTeamAPI {
  export interface Params {
    /**
     * 品牌
     */
    band?: number
    /**
     * 开发模式
     */
    developmentModel?: string
    /**
     * 工厂id
     */
    factoryId?: number
  }

  export interface Data {
    /**
     * 开发人员id
     */
    developmentContactPersonId?: number[]
    /**
     * 技术人员id
     */
    technicalContactPersonId?: number[]
  }
  export type Request = Params
  export type Response = ResponseData<Data>
}
// 查询对应的人员
export function getQueryTeamByParams(params: TeamPeopleQueryTeamAPI.Request) {
  return to<TeamPeopleQueryTeamAPI.Response>(
    fetch.get({ url: `/pdm-base/teamPeople/queryTeam`, params })
  )
}
