import { service } from '@/config/axios/service'
import to from 'await-to-js'
import { ProductTaskNodeEnum } from '@/views/basic-library-manage/product-library/const'
import { ProductListPageAPI } from '@/views/basic-library-manage/product-library/api/product-list'
import { HeelListPageAPI } from '@/views/basic-library-manage/heel-library/api/heel-list'
import { LastListPageAPI } from '@/views/basic-library-manage/last-library/api/last-list'
import { StylePartnerCodeAPI } from '@/views/basic-library-manage/product-library/api/stylePartnerCode'
import SKCInfo = CreateProductInfoAPI.SKCInfo

/**
 * 暂时废弃了，默认全为SUBMIT
 */
export enum SaveTypeEnum {
  SUBMIT = 1,
  STAGING = 0
}
export interface InfringementQueryResp {
  brandId?: number | null
  brandItemName?: null | string
  /**
   * 品牌
   */
  brandName?: null | string
  /**
   * 创建人
   */
  createById?: number | null
  /**
   * 创建时间
   */
  createTime?: null | string
  delFlag?: number | null
  delReason?: null | string
  /**
   * 产品设计师
   */
  designerName?: null | string
  /**
   * 数据ID
   */
  id?: number | null
  /**
   * 侵权记录编号
   */
  infringementCode?: null | string
  /**
   * 内控复核人名称-初判
   */
  innerControlUserNamePre?: null | string
  /**
   * 内控复核人名称-复判
   */
  innerControlUserNameRe?: null | string
  /**
   * 操作人
   */
  modifyById?: number | null
  /**
   * 操作时间
   */
  modifyTime?: null | string
  /**
   * 设计师是否需要协助 1是 0否
   */
  needHelpFlag?: null | string
  needHelpFlagItemName?: null | string
  /**
   * 操作时间
   */
  operateTime?: null | string
  /**
   * 操作人
   */
  operator?: null | string
  /**
   * 其他风险国家
   */
  otherRiskCountry?: null | string
  /**
   * 关联产品编号
   */
  productCode?: null | string
  /**
   * 参考品牌
   */
  referenceBrand?: null | string
  /**
   * 参考品牌链接
   */
  referenceBrandUrl?: null | string
  /**
   * 品牌维权力度-初判
   */
  safeguardRightsStrengthPre?: null | string
  /**
   * 品牌维权力度-复判
   */
  safeguardRightsStrengthRe?: null | string
  /**
   * 上市季节
   */
  saleSeason?: null | string
  /**
   * 同款鞋对应品牌
   */
  sameShoesBrand?: null | string
  /**
   * 检索分类code
   * searchCategoryItemName实际字段
   */
  searchCategory?: null | string
  /**
   * 检索日期-初判
   */
  searchDatePre?: null | string
  /**
   * 检索日期-复判
   */
  searchDateRe?: null | string
  /**
   * 检索需求
   */
  searchNeed?: null | string
  /**
   * erp下发时间-初判
   */
  sendErpTimePre?: null | string
  /**
   * erp下发时间-复判
   */
  sendErpTimeRe?: null | string
  /**
   * 状态
   */
  status?: null | string
  /**
   * 规避建议/风险情况说明-初判
   */
  suggestionInstructionPre?: null | string
  /**
   * 规避建议/风险情况说明-复判
   */
  suggestionInstructionRe?: null | string
  /**
   * 暂存标识 1暂存 0正式
   */
  tempStorageFlag?: null | string
  /**
   * 缩略图
   */
  thumbnail?: null | string
  [property: string]: any
}
/**
 * 新增/更新产品基础信息
 */
export namespace CreateProductInfoAPI {
  export interface SKUMaterialInfo {
    addOld?: boolean
    /**
     * 面料的名称  如  1 ，2 ，3， 4
     */
    indexName?: string
    /**
     * 面料
     */
    material?: number
    materialItemName?: string
    /**
     * 面料占比%
     */
    materialRatio?: number
    /**
     * 纹路
     */
    texture?: string
    /**
     * 厚度(mm)
     */
    thickness?: number
    /**
     * 宽幅(cm)：
     */
    widthInCm?: number
    /**
     * 克重
     */
    wight?: number
  }

  export interface SKCChannelSales {
    /**
     * 渠道名称
     */
    salesChannel?: string
    /**
     * 销量量
     */
    salesNum?: number
  }
  export type SKCInfo = {
    newAddColor?: number
    paddingMaterialSurfaceCodeItemName?: string[]
    liningMaterialCodeItemName?: string[]
    /**
     * pu占比
     */
    percentPu?: number

    /**
     * 真皮占比
     */
    percentCorium?: number

    /**
     * 纺织物占比
     */
    percentTextile?: number

    /**
     * 其他占比
     */
    percentOther?: number

    /**
     * 真皮编码
     */
    coriumMaterialCode?: number[] | number

    /**
     * 其他材料编码
     */
    otherMaterialCode?: number[] | number
    skcInformationUrl?: BaseFileDTO[]
    skcConfirmInformationUrl?: BaseFileDTO[]
    addInformationImg?: BaseFileDTO[]
    originalIndex?: number
    totalSampleUrl?: BaseFileDTO[]
    /**
     * WMS分类
     */
    wmsCategoryId?: number
    /**
     * 确认样评审结果
     */
    confirmSampleReviewResults?: string
    /**
     * 确认样评审意见
     */
    confirmSampleReviewOpinions?: string
    /**
     * 确认样完成日期
     */
    confirmSampleReviewDate?: string
    /**
     * 确认样打样次数
     */
    confirmSampleReviewNum?: number
    /**
     * 齐色样处理结果
     */
    uniformColorSampleProcessingResult?: string
    /**
     * 齐色样外观意见
     */
    appearanceOpinion?: string
    /**
     * 确认样外观意见
     */
    confirmAppearanceOpinion?: string
    /**
     * 品牌
     */
    brand?: string
    /**
     * 渠道销量汇总
     */
    channelSales?: SKCChannelSales[]
    /**
     * 渠道销售标识
     */
    channelSalesMark?: string
    /**
     * 颜色编码
     */
    colorCode?: string
    colorId?: number
    /**
     * 产品配色
     */
    colorCodeItemName?: string
    /**
     * 配色图稿
     */
    colorSchemeDraftUrl?: BaseFileDTO[]
    /**
     * 配色类型
     */
    colorType?: string
    colorTypeItemName?: string
    /**
     * 创建人
     */
    createById?: number
    /**
     * 操作时间
     */
    createTime?: string
    /**
     * 状态
     */
    dataStatus?: string
    dataStatusItemName?: string
    delFlag?: number
    /**
     * 跟进开发人员
     */
    developmentPersonnel?: string
    /**
     * 产品阶段
     */
    developStage?: string
    developStageItemName?: string
    /**
     * 齐色样完成日期
     */
    dyeSampleCompletionDate?: string
    /**
     * 齐色样打样次数
     */
    dyeSampleMakingCount?: number
    /**
     * 齐色样评审意见
     */
    dyeSampleReviewOpinion?: string
    /**
     * 数据下发失败说明
     */
    errorDataMsg?: string
    /**
     * 预估订单量
     */
    estimatedOrderVolume?: number
    /**
     * 预估上架时间
     */
    estimatedShelfTime?: string
    /**
     * 齐色样试穿意见
     */
    fittingOpinion?: string
    /**
     * 确认样试穿意见
     */
    confirmFittingOpinion?: string
    id?: number
    /**
     * 上市季节
     */
    launchSeason?: string
    /**
     * 里材料编码
     */
    liningMaterialCode?: number | number[]
    /**
     * 里绒情况
     */
    liningSituation?: number[]
    /**
     * 主面料
     */
    mainFabric?: string
    mainFabricItemName?: string
    /**
     * skc材料信息
     */
    materialInfoList?: SKUMaterialInfo[]
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 是否已下单
     */
    orderPlaced?: string
    /**
     * 垫材料(填充)编码
     */
    paddingMaterialFillingCode?: string[]
    /**
     * 垫材料(面)编码
     */
    paddingMaterialSurfaceCode?: number[]
    /**
     * 初样结论(通过 不通过)
     */
    preliminaryConclusion?: string
    /**
     * 产品类目
     */
    productCategory?: number
    productCategoryItemName?: string
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 产品信息的详情（在订单详情接口中使用）
     */
    productSkcInfoDetailResp?: Request[]
    /**
     * 齐色样打样信息记录
     */
    sampleMakingInfo?: string
    /**
     * 确认样打样信息记录
     */
    confirmSampleMakingInfo?: string
    /**
     * 齐色样样品图片
     */
    sampleUrl?: BaseFileDTO[]
    /**
     * 确认样样品图片
     */
    confirmSampleUrl?: BaseFileDTO[]
    /**
     * 1是  0否 是否选中
     */
    selectable?: string
    /**
     * 选品渠道
     */
    selectionChannel?: string[]
    /**
     * 选品会结果
     */
    selectionMeetingResult?: string
    /**
     * 选品会评审意见
     */
    selectionMeetingReviewOpinion?: string
    /**
     * 数据下发时间
     */
    sendTime?: string
    /**
     * 是否已下发WMS
     */
    sendWms?: string
    /**
     * SKC编码
     */
    skcCode?: string
    /**
     * 状态
     */
    skcStatus?: string
    /**
     * 底材料（中底）编码
     */
    soleMaterialMidsoleCode?: string
    /**
     * 底材料（大底）编码
     */
    soleMaterialOutsoleCode?: number[]
    /**
     * 阶段完成说明
     */
    stageCompDesc?: string
    /**
     * 款式编码
     */
    styleCode?: string
    /**
     * 齐色样技术评估意见
     */
    technicalAssessment?: string
    /**
     * 确认样技术评估意见
     */
    confirmTechnicalAssessment?: string
    /**
     * 跟进技术人员
     */
    technicalPersonnel?: string
    /**
     * 颜色缩略图
     */
    thumbnail?: BaseFileDTO
    /**
     * 齐色样结论(通过 不通过
     */
    uniformColorConclusion?: string
    /**
     * 不植关税率(%)
     */
    unVelvetTariff?: number
    /**
     * 是否植绒
     */
    velvetApplied?: string
    /**
     * 植绒备注
     */
    velvetRemark?: string
    /**
     * 植绒要求
     */
    velvetAppliedItemName?: string
    customsVelvetSuggestion?: string
    velvetRequirements?: string
    /**
     * 植绒/植皮关税率(%)
     */
    velvetTariff?: number
    disabled?: boolean
    /**
     * 开发评估意见
     */
    developmentAssessment?: string
    [property: string]: any
  }
  export interface Params {
    canColorSample?: boolean
    /**
     * 母Style id
     */
    vocStyleNumberId?: string
    /**
     *  产品类型编号
     */
    productCategoryItemCode?: string
    /**
     * 产品类型
     */
    productType?: string
    /**
     * 产品实际定级
     */
    productActualPosition?: string
    /**
     * 产品系列
     */
    /**
     * 同款多配类型
     */
    multipleAccessoriesType?: string
    sendFactory?: number
    /**
     * 衍生类型
     */
    derivedType?: string
    /**
     * 商品上市季节
     */
    productLaunchSeason?: string
    /**
     * 产品关联侵权列表
     */
    infringementList?: InfringementQueryResp[]
    /**
     * CT
     */
    combatTeam?: string
    styleNumber?: string
    /**
     * 变更原因
     */
    invalidReason?: string
    /**
     * 侵权风险判定
     */
    infringementRisk?: string
    infringementRiskItemName?: string
    /**
     * 风险国家
     */
    riskCountry?: string
    selectionDisabled?: boolean
    sizeRangeIdItemName?: string[]
    /**
     * 确认样是否通过
     */
    confirmResult?: boolean
    modelId?: number
    modelIdItemName?: string
    skuList?: StylePartnerCodeAPI.BaseProductSku[]
    stylePartnerCode?: string
    autoSendWms?: boolean
    quicklyAble?: number
    /**
     * 打样数量
     */
    initSampleNumber?: number
    /**
     * 确认样外发打样日期
     */
    confirmOutsourcingIssueDate?: string
    /**
     * 初样打样跟进结果
     */
    initSampleResultDict?: string
    /**
     * 初样打样完成日期
     */
    initSampleCompletionDate?: string
    /**
     * 初样样品寄送日期
     */
    initSampleSendDate?: string
    /**
     * 初样外发打样日期
     */
    initOutsourcingIssueDate?: string
    /**
     * 初样工厂接收日期
     */
    initOutsourcingAcceptDate?: string
    supplyDate?: string
    /**
     * 材料色卡图片
     */
    materialColorImg?: BaseFileDTO[]
    /**
     * 材料色卡提供日期
     */
    materialColorSupplyDate?: string
    /**
     * 打样数量 （确认样）
     */
    confirmNumber?: number
    /**
     * 工厂接收日期 (确认样)
     */
    confirmOutsourcingAcceptDate?: string
    /**
     * 打样完成日期 （确认样）
     */
    confirmCompletionDate?: string
    /**
     * 打样跟进结果（确认样）
     */
    confirmResultDict?: string
    /**
     * 样品寄送日期 （确认样）
     */
    confirmSendDate?: string
    /**
     * 打样要求说明 （确认样）
     */
    confirmRequiredMark?: string
    /**
     * 打样需求数量 （ 确认样）
     */
    confirmRequiredNumber?: number
    /**
     * 需求完成日期 （确认样）
     */
    confirmRequiredFinishDate?: string
    /**
     * 材料色卡确认日期
     */
    materialColorConfirmationDate?: string
    /**
     * 需求完成日期 （齐色样）
     */
    sampleRequiredFinishDate?: string
    /**
     * 完成日期 （打样分配和 初样）
     */
    initRequiredFinishDate?: string
    /**
     * 要求说明 （打样分配和 初样）
     */
    initRequiredMark?: string
    /**
     * 打样需求数量 （打样分配和 初样）
     */
    initRequiredNumber?: number
    /**
     * 开发评审意见
     */
    developmentEvaluationOpinions?: string
    /**
     * 样品寄送日期
     */
    sampleSendDate?: string
    /**
     * 补充说明
     */
    addInformationImg?: BaseFileDTO[]
    /**
     * 打样数量
     */
    sampleNumber?: number
    /**
     * 原版鞋接收日期
     */
    originalShoesAcceptDate?: string
    /**
     * 原版鞋图
     */
    originalShoesImg?: BaseFileDTO[]
    /**
     * 原版鞋数量
     */
    originalShoesNumber?: number
    /**
     * 工厂接收日期
     */
    outsourcingAcceptDate?: string
    /**
     * 打样环节
     */
    sampleProcessDict?: string
    /**
     * 打样要求说明
     */
    sampleRequiredMark?: string
    /**
     * 打样跟进结果
     */
    sampleResultDict?: string
    /**
     * 打样完成日期
     */
    sampleCompletionDate?: string
    /**
     * 需求完成日期
     */
    requiredFinishDate?: string
    /**
     * 是否提供原版鞋
     */
    requiredOriginalShoes?: string
    /**
     * 打样需求数量
     */
    sampleRequiredNumber?: number
    /**
     * 样品接收日期
     */
    sampleAcceptDate?: string
    /**
     * 开发模式
     */
    developmentModel?: string
    /**
     * 标准码id
     */
    standardSizeId?: number[]
    /**
     * 标准码
     */
    standardSizeValue?: string[]
    /**
     * 供管
     */
    regionalSupplyPersonId?: number[]
    noCpyrightReason?: string
    allProductColorIdListItemName?: string
    /**
     * 操作时间map
     */
    taskTimeRecord?: {
      [key in ProductTaskNodeEnum]?: {
        taskNode?: ProductTaskNodeEnum
        createTime?: string
        createById?: number
        createByIdItemName?: string
      }
    }
    /**
     * 0 voc 1 新Listing
     */
    type?: number
    /**
     * 产品颜色图片
     */
    colorUrlList?: BaseFileDTO[]
    /**
     * 任务节点描述
     */
    stageCompDesc?: string
    // 暂存/提交
    operateBottom?: SaveTypeEnum
    sendWms?: string
    orderPlaced?: string
    orderPlacedItemName?: string
    operateType?: string
    /**
     * 1是  0否  是否过踝/过膝
     */
    ankleCoverage?: string
    /**
     * 齐色样外观意见
     */
    appearanceOpinions?: string
    /**
     * 确认样外观意见
     */
    confirmAppearanceOpinions?: string
    /**
     * 适用季节
     */
    applicableSeason?: string
    /**
     * arch type
     */
    archType?: string
    /**
     * 大货供应商(数据类型需前端转换)
     */
    assignedFactory?: string | string[]
    assignedFactoryItemName?: string
    /**
     * 产品鞋跟
     */
    associatedHeelInfo?: number
    associatedHeelInfoItemName?: string
    /**
     * 关联跟信息
     */
    associatedHeelInfo2?: string
    /**
     * 产品鞋垫
     */
    associatedInsoleInfo?: number
    associatedInsoleInfoItemName?: string
    /**
     * 关联垫信息
     */
    associatedInsoleInfo2?: string
    /**
     * 产品楦型
     */
    associatedLastType?: number
    associatedLastTypeItemName?: string
    /**
     * 关联楦型
     */
    associatedLastType2?: string
    /**
     * 产品大底
     */
    associatedSoleInfo?: number
    associatedSoleInfoItemName?: string
    /**
     * 关联底信息
     */
    associatedSoleInfo2?: string
    /**
     * 基本计量单位
     */
    basicUnitOfMeasure?: string
    /**
     * 批量工艺
     */
    batchProcess?: string
    /**
     * 批量工艺图链接
     */
    batchProcessImageLink?: string
    /**
     * 靴筒高(mm）
     */
    bootLegHeightMm?: number
    /**
     * 品牌
     */
    brand?: number
    brandItemName?: string
    /**
     * 品牌维权力度
     */
    brandEnforcementEffort?: string
    /**
     * 品牌维权力度
     */
    brandEnforcementEffort2?: string
    /**
     * 装箱数（箱规）
     */
    cartonQuantity?: number
    /**
     * 儿童年龄段
     */
    childrenAvgGroup?: string[]
    /**
     * 儿童人群
     */
    childrenCrowd?: string
    /**
     * 选品渠道
     */
    chooseChannel?: string[]
    /**
     * 穿脱方式
     */
    closureMethod?: string
    /**
     * 配色类型
     */
    colorType?: string
    /**
     * 竞品链接
     */
    competitiveBrandLink?: string
    /**
     * 技转全套报告
     */
    completeTechnologyTransferReport?: BaseFileDTO
    /**
     * 确认样操作人Id
     */
    confirmationSampleCreateBy?: number
    /**
     * 确认样操作日期
     */
    confirmationSampleCreateTime?: string
    /**
     * 确认样打样日期
     */
    confirmationSampleDate?: string
    /**
     * 确认样打样单
     */
    confirmationSampleOrderUrl?: BaseFileDTO[]
    /**
     * 确认样外发日期
     */
    confirmDispatchDate?: string
    /**
     * 1是  0否 是否需要侵权排查
     */
    copyright?: string
    /**
     * 关联侵权排查
     */
    copyrightInspection?: number
    copyrightInspectionItemName?: string
    /**
     * 同款鞋对应品牌
     */
    correspondingBrand?: string
    /**
     * 对应品牌鞋图
     */
    correspondingBrandImage?: string
    /**
     * 成本区间最小值（$）
     */
    costRangeMax?: number
    /**
     * 成本区间最小值（$）
     */
    costRangeMin?: number
    /**
     * 创建人
     */
    createById?: number
    createByIdItemName?: string
    /**
     * 操作时间
     */
    createTime?: string
    /**
     * 数据状态
     */
    dataStatus?: string
    dataStatusItemName?: string
    delFlag?: number
    /**
     * 设计师是否需协助
     */
    designerAssistanceRequired?: string
    /**
     * 产品设计师的id
     */
    designerId?: number
    designerIdItemName?: string
    /**
     * 企划创建人
     */
    designPersonId?: number
    /**
     * 设计图
     */
    designUrl?: BaseFileDTO[]
    /**
     * 开发渠道
     */
    developmentChannel?: string
    /**
     * 跟进开发人员Id
     */
    developmentContactPersonId?: number[]
    developmentContactPersonIdItemName?: string
    /**
     * 产品系列
     */
    developmentDirection?: string

    /**
     *
     * 产品子系列
     */
    developmentDirectionSubCode?: string
    /**
     * 开发策略
     */
    developmentStrategy?: string[]
    /**
     * 开发类型
     */
    developmentType?: string
    developmentTypeItemName?: string
    /**
     * 产品阶段
     */
    developStage?: string
    developStageItemName?: string
    /**
     * 1是  0否 是否S2C
     */
    dtc?: string
    /**
     * 预估售价（$）
     */
    estimatedPrice?: number
    /**
     * 汇率
     */
    exchangeRate?: number
    /**
     * 期望上架日期
     */
    expectedLaunchDate?: string
    /**
     * 工厂报价（¥）含税
     */
    factoryQuotation?: number
    /**
     * 1是  0否 是否折叠
     */
    foldable?: string
    /**
     * 垫脚工艺
     */
    footbedTechnique?: string
    /**
     * 齐色样打样单
     */
    fullColorSampleOrder?: string
    /**
     * 功能系列(A+参考）
     */
    functionSeriesAPlusReference?: string
    /**
     * 功能系列(视频参考）
     */
    functionSeriesVideoReference?: string
    /**
     * 1是  0否有功能吊牌挂牌
     */
    hasFeatureTag?: string
    /**
     * 跟高
     */
    heelHeight?: string
    /**
     * 跟高（mm）
     */
    heelHeightMm?: number
    /**
     * 跟型
     */
    heelType?: string
    id?: number
    /**
     * 图片详情
     */
    imageDetails?: string
    /**
     * 侵权风险初判
     */
    infringementRiskAssessment?: string
    /**
     * 侵权风险初判
     */
    infringementRiskAssessment2?: string
    /**
     * 初样颜色的id
     */
    initialSampleColorId?: number
    colorName?: string
    colorEnName?: string
    /**
     * 初样完成日期
     */
    initialSampleCompletionDate?: string
    /**
     * 下发打样日期
     */
    initialSampleIssueDate?: string
    /**
     * 初样评审意见
     */
    initialSampleReviewOpinions?: string
    /**
     * 初样评审结果
     */
    preliminaryConclusion?: string
    /**
     * 初样打样次数
     */
    initialSampleTimes?: number
    /**
     * 内控复核人
     */
    internalControlReviewer?: string
    /**
     * 内控复核人
     */
    internalControlReviewer2?: string
    /**
     * 内控知识产权复核检索记录
     */
    ipReviewRecord?: string
    /**
     * 内控知识产权复核检索记录
     */
    ipReviewRecord2?: string
    /**
     * 是否新开模
     */
    isNewMold?: string
    /**
     * 产品是否上市
     */
    isProductLaunched?: string
    /**
     * 楦底类型
     */
    lastBottomType?: string
    /**
     * 楦型标准
     */
    lastsStandard?: string
    lastStandardItemName?: string
    /**
     * 上市季
     */
    launchSeason?: string
    launchSeasonItemName?: string
    /**
     * 内里标签
     */
    liningLabel?: string
    /**
     * 主渠道标识
     */
    mainChannelMark?: string
    /**
     * 主面料
     */
    mainMaterial?: string
    /**
     * 1是 0否 强制性警示声明
     */
    mandatoryWarningStatement?: string
    /**
     * 齐色样打样单
     */
    matchingSampleUrl?: BaseFileDTO[]
    /**
     * 材料送检说明
     */
    materialInspectionInstructions?: string
    /**
     * 选品会结果
     */
    meetingResult?: string
    /**
     * 操作人
     */
    modifyById?: number
    modifyByIdItemName?: string
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 颜色（新增）
     */
    newColor?: string
    /**
     * 配色类型（新增）
     */
    newColorType?: string
    /**
     * 主面料（新增）
     */
    newMainMaterial?: string
    /**
     * 开口类型（单鞋必填）
     */
    openingType?: string
    /**
     * 外发打样日期(设计师)
     */
    outsourcingIssueDate?: string
    /**
     * 包装代码
     */
    packagingCode?: number
    /**
     * 包装类型
     */
    packagingType?: string
    /**
     * 预估上架日期
     */
    predictLaunchDate?: string
    /**
     * 产品类目,最低等级id
     */
    productCategory?: number
    productCategoryItemName?: string
    /**
     * 产品描述
     */
    productDescription?: string
    /**
     * 产品简介【50字内】
     */
    productIntroShort?: string
    /**
     * 制作工艺
     */
    productionTechnique?: string
    /**
     * 1是 0否 是否上市
     */
    productLaunched2?: string
    /**
     * 产品编号（自动生成）
     */
    productNumber?: string
    /**
     * 产品定位
     */
    productPositioning?: string
    /**
     * 产品风格
     */
    productStyle?: string

    /**
     * 产品子风格
     */
    productStyleSubCode?: string
    /**
     * 参考品牌
     */
    referenceBrand?: string
    /**
     * 参考品牌
     */
    referenceBrand2?: string
    /**
     * 参考品牌链接
     */
    referenceBrandLink?: string
    /**
     * 对标页面呈现参考
     */
    referencePageDisplay?: string
    /**
     * 参考产品编码
     */
    referenceProductCode?: string
    /**
     * 参考款式图
     */
    referenceUrl?: BaseFileDTO[]
    /**
     * 区域
     */
    region?: number
    regionItemName?: string
    /**
     * 相关外观图片
     */
    relatedAppearanceImages?: string
    /**
     * 备注
     */
    remarks?: string
    /**
     * 吊牌价
     */
    retailPrice?: number
    /**
     * 品牌底线价格
     */
    minPrice?: number
    /**
     * 复核日期
     */
    reviewDate?: string
    /**
     * 评审日期
     */
    reviewDate2?: string
    /**
     * 复核日期
     */
    reviewDate3?: string
    /**
     * 评审意见
     */
    reviewOpinions?: string
    /**
     * 规避建议/风险情况说明
     */
    riskAvoidanceSuggestions?: string
    /**
     * 规避建议/风险情况说明
     */
    riskAvoidanceSuggestions2?: string
    /**
     * 风险国家
     */
    riskCountries?: string
    /**
     * 风险国家
     */
    riskCountries2?: string
    /**
     * 风险初筛
     */
    riskScreening?: string
    /**
     * 是否含金属鞋头
     */
    safetyFeatures?: string
    /**
     * 齐色样
     */
    sameColorSampleDate?: string
    /**
     * 样品图片
     */
    sampleImages?: BaseFileDTO[]
    /**
     * 打样信息记录
     */
    sampleInformationRecord?: string
    /**
     * 初样打样单
     */
    samplePrintUrl?: BaseFileDTO[]
    /**
     * 场景
     */
    scene?: string
    /**
     * 场景图1-主场景(场景+服装)
     */
    sceneImage1?: string
    /**
     * 场景图2-辅场景
     */
    sceneImage2?: string
    /**
     * 场景图3-辅场景
     */
    sceneImage3?: string
    /**
     * 颜色
     */
    selectedColor?: number[]
    /**
     * 选中尺码
     */
    selectedSize?: number[]
    selectedSizeItemName?: string[]
    /**
     * 选品会评审意见
     */
    selectionMeetingReviewOpinion?: string
    /**
     * 卖点1【25字内】
     */
    sellingPoint1?: string
    /**
     * 卖点2【25字内】
     */
    sellingPoint2?: string
    /**
     * 卖点3【25字内】
     */
    sellingPoint3?: string
    /**
     * 系列/重点款
     */
    seriesKeyStyles?: string
    /**
     * PRODUCT NAME
     */
    shoeName?: string
    /**
     * 鞋底工艺
     */
    shoeSoleTechnique?: string
    /**
     * 单色MOQ
     */
    singleColorMoq?: number
    /**
     * 单款MOQ
     */
    singleStyleMoq?: number
    /**
     * 尺码段id表
     */
    sizeRangeId?: number[]
    /**
     * sku信息
     */
    skcInfoDetailResp?: SKCInfo[]
    /**
     * 特殊包装方式
     */
    specialPackagingMethod?: string
    /**
     * 特殊检测需求及说明
     */
    specialTestingRequirements?: string
    /**
     * 立体图
     */
    stereogramImage?: string
    /**
     * 款式定位
     */
    stylePositioning?: string
    /**
     * 款式结构
     */
    styleStructure?: string
    /**
     * Style（WMS）
     */
    styleWms?: string
    /**
     * 建议主推色
     */
    suggestedMainColor?: string
    /**
     * 适用人群
     */
    targetAudience?: string
    targetAudienceItemName?: string
    /**
     * 任务节点
     */
    taskNode?: ProductTaskNodeEnum
    /**
     * 跟进技术人员Id
     */
    technicalContactPersonId?: number[]
    technicalContactPersonIdItemName?: string
    /**
     * 图稿技术评估
     */
    technicalEvaluationOpinions?: string
    /**
     * 技术评估意见
     */
    technicalEvaluationOpinions2?: string
    /**
     * 产前技术评估报告
     */
    technologyAssessmentReport?: BaseFileDTO[]
    /**
     * 技术内审记录
     */
    techReviewRecordUrl?: BaseFileDTO[]
    /**
     * 缩略图
     */
    thumbnail?: BaseFileDTO
    /**
     * 头型
     */
    toeShape?: string
    /**
     * 楦头标准
     */
    toeStandard?: string
    toeStandardItemName?: string
    /**
     * 流行元素
     */
    trendyElements?: string
    /**
     * 试穿意见
     */
    trialWearOpinions?: string
    /**
     * 试穿报告
     */
    tryOnReportUrl?: BaseFileDTO[]
    /**
     * 不含税美金报价
     */
    usdQuotationExcludingTax?: number
    /**
     * 使用电池/附带电池
     */
    usesBattery?: string
    /**
     * VOC原款号
     */
    vocOriginalStyleNumber?: string
    /**
     * 防水级别
     */
    waterproofLevel?: string
    wmsCategoryId?: number
    disabled?: boolean
    /**
     * 是否为老数据 0否 1是
     */
    oldData?: number
    // 供应商
    factoryItemName?: string
    derivedChildType?: string
  }

  export enum SendFactoryEnum {
    YES = 1, //  禁用
    NO = 0 //  不禁用
  }

  export type Request = Params
  export type Response = ResponseData<number>
}
/**
 * 创建/更新产品信息
 * 有id是更新，无id是创建
 * @param data
 */
export function createProductInfo(data: CreateProductInfoAPI.Params) {
  return to<CreateProductInfoAPI.Response>(service.post('/pdm-base/pdmProductInfo/save', data))
}

export namespace ProductInfoAPI {
  export type Response = ResponseData<CreateProductInfoAPI.Request>
}
export function getProductInfo(id: number) {
  return to<ProductInfoAPI.Response>(service.get(`/pdm-base/pdmProductInfo/detail/${id}`))
}

export function getCopyProductInfo(id: number) {
  return to<ProductInfoAPI.Response>(service.get(`/pdm-base/pdmProductInfo/copy/${id}`))
}

export function getProductInfoByVersionId(versionId: number) {
  return to<ProductInfoAPI.Response>(
    service.get(`/pdm-base/pdmProductInfo/versionDetail/${versionId}`)
  )
}

export namespace ProductImageListAPI {
  export interface Data {
    list: BaseFileDTO[]
  }
  export type Response = ResponseData<Data>
}
export function getProductImageList(productId: number) {
  return to<ProductImageListAPI.Response>(
    service.get(`/pdm-base/pdmProductInfo/allImages`, { params: { productId } })
  )
}

/**
 * 获取上/下一个产品
 */
export namespace GetNextProductAPI {
  export interface Data {
    after?: number
    before?: number
  }
  export type Response = ResponseData<Data>
}
export function getNextProduct(id: number, data: ProductListPageAPI.Params) {
  return to<GetNextProductAPI.Response>(
    service.post(`/pdm-base/pdmProductInfo/productStepResp/${id}`, data)
  )
}

export namespace ProductLastHeelInfoAPI {
  export interface LastBaseProductResp {
    /**
     * 产品鞋跟
     */
    associatedHeelInfo?: HeelListPageAPI.Row
    /**
     * 产品鞋垫
     */
    associatedInsoleInfo?: HeelListPageAPI.Row
    /**
     * 产品楦型
     */
    associatedLastType?: LastListPageAPI.Row
    /**
     * 产品大底
     */
    associatedSoleInfo?: HeelListPageAPI.Row
  }
  export type Response = ResponseData<LastBaseProductResp>
}
export function getProductLastHeelInfo(code: string) {
  return to<ProductLastHeelInfoAPI.Response>(
    service.get(`/pdm-base/pdmProductInfo/getAllModel/${code}`)
  )
}

export function getStyleImage(style: string) {
  return to<ResponseData<BaseFileDTO>>(
    service.get(`/pdm-base/pdmProductInfo/relatedStylePicture`, { params: { style } })
  )
}

export namespace ProductInfringeAPI {
  export type Request = {
    productNumber: string
    infringementList: InfringementQueryResp[]
  }
}
export function saveProductInfringe(data: ProductInfringeAPI.Request) {
  return to<BasicResponseData>(
    service.post(`/pdm-base/pdmProductInfo/updateProductInfringement`, data)
  )
}

export namespace ProductStoreAPI {
  export type Request = CreateProductInfoAPI.Request
  export type Response = BasicResponseData
}
export function saveProductStore(data: ProductStoreAPI.Request) {
  return to<ProductStoreAPI.Response>(service.post(`/pdm-base/pdmProductInfo/quickStorage`, data))
}
export function resetProductStore() {
  return to<BasicResponseData>(service.get(`/pdm-base/pdmProductInfo/resetQuickProduct`))
}

export function getProductStore() {
  return to<ProductInfoAPI.Response>(service.get(`/pdm-base/pdmProductInfo/getQuickProduct`))
}

export type FormModel = CreateProductInfoAPI.Request

export interface Props {
  modelValue: FormModel
  currentPane: string
  initPane: string
  mounted: boolean
  isCreate: boolean
  isEdit: boolean
  isCopy: boolean
  isView: boolean
  id?: number | string
  selectionDisabled?: boolean
  confirmDisabled?: boolean
  isOldData?: boolean
}

export interface Emit {
  (e: 'update:modelValue', val: FormModel): void
  (e: 'refresh'): void
}

export namespace CopySkcApi {
  export type Request = {
    /**
     * 复制的产品编号
     */
    copyProductNumber?: string
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 复制类型
     * 复制类型 0:产品配色 1:材质信息 2:材质占比 3:植绒信息
     */
    type?: number[]
  }
  export type Response = ResponseData<SKCInfo[]>
}

/**
 * 产品 skc同款复制
 */
export function copyProductSkc(req: CopySkcApi.Request) {
  return to<CopySkcApi.Response>(service.post(`/pdm-base/pdmProductInfo/copySameSkc`, req))
}

export namespace RelatedProductApi {
  export type Request = PageParams & { styleNumber: string }
  export type Row = {
    vocOriginalStyleNumber: string
    vocStyleNumberId: string
    brand: number
    dataStatus: string
    derivedType: string
    id: number
    productActualPosition: string
    productCategory: number
    productLaunchSeason: string
    productNumber: string
    setBrandItemName: string
    styleNumber: string
    thumbnail: {}
  }
  export type Response = PagedResponseData<Row>
}

export namespace RelatedInfoApi {
  export type Response = ResponseData<Row>
  export type RelatedModels = {
    id?: string
    code?: string
    thumbnail?: BaseFileDTO[]
    brand: number
    developmentYear?: string
    region?: string
    targetAudience?: string
    status?: string
    productNumber?: string
  }
  export type RelatedLasts = {
    id?: string
    code?: string
    thumbnail?: BaseFileDTO[]
    sex?: string
    type?: string
    market?: string
    designer?: string
    region?: string
    status?: string
    productNumber?: string
    shoeFactory?: string
    supplierLastNumber?: string
  }
  export type Row = {
    relatedModels: RelatedModels
    relatedLasts: RelatedLasts
  }
}
export namespace RelatedSellInfoApi {
  export type Request = PageParams & {
    styleNumber: string
    productIdList: string[]
  }
  export type Response = PagedResponseData<Row>
  export type Row = {
    id?: string
    sellPointNumber?: string
    productId?: string
    productNumber?: string
    thumbnail?: BaseFileDTO[]
    styleWms?: string
    StyleWMS?: string
    brandName?: string
    productLaunchSeason?: string
    productCategoryItemName?: string
    shoeName?: string
  }
}
/**
 * 分页查询关联产品信息
 */
export function getRelatedProduct(req: RelatedProductApi.Request, signal?: AbortSignal) {
  return to<RelatedProductApi.Response>(
    service.post(`/pdm-base/pdmProductRelateInfo/relateProductList`, req, { signal })
  )
}

/**
 * 相关对象查询
 */
export function getRelatedObject(id: string | number) {
  return to<RelatedInfoApi.Response>(service.get(`/pdm-base/pdmProductInfo/relatedObjects/${id}`))
}

/**
 * 相关卖点
 */
export function getRelatedSell(req: RelatedSellInfoApi.Request) {
  return to<RelatedSellInfoApi.Response>(
    service.post(`/pdm-base/pdmProductRelateInfo/relateProductSellPointList`, req)
  )
}
