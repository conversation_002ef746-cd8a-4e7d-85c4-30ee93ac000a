import { ProductTaskNodeEnum } from '../const'
import to from 'await-to-js'
import { service } from '@/config/fetch/service'
import { ProductDetail } from '../api/product-list'

export namespace UpdateProductStatusAPI {
  export interface Params {
    detailList: ProductDetail[]
    taskNode?: ProductTaskNodeEnum
  }
  export type Request = Params
  export type Response = BasicResponseData
}

export function updateProductStatus(data: UpdateProductStatusAPI.Request) {
  return to<UpdateProductStatusAPI.Response>(
    service.post('/pdm-base/pdmProductInfo/updateDataStatus', data)
  )
}
