import { service } from '@/config/axios/service'
import to from 'await-to-js'

export enum EditableEnum {
  EDITABLE = 0,
  NOT_EDITABLE = 1
}

export enum VisibleEnum {
  VISIBLE = 0,
  HIDDEN = 1
}

export namespace ColumnListAPI {
  export interface Data {
    /**
     * 属性值
     */
    attrField?: null | string
    /**
     * 属性名称
     */
    attrName?: null | string
    editable?: number
    visible?: number
    /**
     * 图片列标识
     */
    imgFlag?: number
  }
  export type Response = ResponseData<Data[]>
}

export function getAllColumns() {
  return to<ColumnListAPI.Response>(service.get(`/pdm-base/viewConfiguration/all`))
}

/**
 * 获取skc所有可自定义column
 */
export function getSkcAllColumns() {
  return to<ColumnListAPI.Response>(service.get(`/pdm-base/viewConfiguration/skcAll`))
}

export namespace CreateViewAPI {
  export interface Params {
    id?: number
    fieldAttr?: string[]
    /**
     * 名称
     */
    name?: string
    /**
     * 类型(0 就是产品库)
     */
    type?: number
  }
  export type Request = Params
  export type Response = ResponseData<number>
}

export function createView(data: CreateViewAPI.Request) {
  return to<CreateViewAPI.Response>(service.post('/pdm-base/viewConfiguration/add', data))
}
