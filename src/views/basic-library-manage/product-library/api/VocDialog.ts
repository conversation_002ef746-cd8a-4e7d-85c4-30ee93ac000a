import { service } from '@/config/fetch/service'
import to from 'await-to-js'

export enum VocTypeEnum {
  VOC = 0,
  LISTING = 1
}

export namespace AddVocAPI {
  export interface Params {
    id?: number | null
    /**
     * 0 voc 1 新Listing
     */
    type?: number | null
  }
  export type Request = Params
  export type Response = ResponseData<number>
}

export function addVoc(params: AddVocAPI.Request) {
  return to<AddVocAPI.Response>(service.post('/pdm-base/pdmProductInfo/saveVoc', params))
}
