import to from 'await-to-js'
import { service } from '@/config/fetch/service'

/**
 * 根据产品id获取sku箱规信息
 */
export namespace GetBoxGaugeByProductIdAPI {
  export interface Row {
    brand?: number
    /**
     * 品牌
     */
    brandItemName?: string
    /**
     * 箱规
     */
    cartonQuantity?: number
    /**
     * 颜色
     */
    colorName?: string
    /**
     * 主键id
     */
    id?: number
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 尺码
     */
    sizeValue?: string
    /**
     * skc code
     */
    skuCode?: string
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}

export function getBoxGaugeByProductId(productId: number) {
  return to<GetBoxGaugeByProductIdAPI.Response>(
    service.get(`/pdm-base/pdmProductInfo/getSkuCartonResp/${productId}`)
  )
}

export namespace UpdateBoxGaugeAPI {
  export interface Request {
    list: GetBoxGaugeByProductIdAPI.List
  }
  export type Response = BasicResponseData
}

export function updateBoxGauge(data: UpdateBoxGaugeAPI.Request) {
  return to<UpdateBoxGaugeAPI.Response>(
    service.post('/pdm-base/pdmProductInfo/updateSkuCartonResp', data)
  )
}
