<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { ref } from 'vue'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import type { VxeTableInstance } from 'vxe-table'
import {
  getProductCategoryList,
  LastDataStatusEnum,
  ProductCategoryAPI,
  updateProductCategoryStatus
} from './api/product-category'
import AddCategoryDialog from './components/AddCategoryDialog.vue'
import { AddCategoryTypeEnum } from './const'
import { statusConst } from '../const'

defineOptions({
  name: 'ProductCategory'
})

const useConst = () => {
  const statusMap = statusConst.statusMap

  return {
    statusMap
  }
}

const { statusMap } = useConst()

const useQuery = () => {
  const formRef = ref<FormInstance>()
  const tableRef = ref<VxeTableInstance>()
  const formData = ref<ProductCategoryAPI.Request>({
    categoryName: ''
  })
  const tableData = ref<ProductCategoryAPI.Data[]>([])
  const queryLoading = ref(false)

  function addIndex(array: ProductCategoryAPI.Data[], parentIndex = 1) {
    array.forEach((item) => {
      item.index = parentIndex
      if (item.childList && item.childList.length > 0) {
        addIndex(item.childList, parentIndex + 1)
      }
    })
  }

  const handleQuery = async () => {
    queryLoading.value = true
    const [error, result] = await getProductCategoryList(formData.value)
    queryLoading.value = false
    if (error === null && result?.datas) {
      tableData.value = result.datas || []
      addIndex(tableData.value)
    }
  }

  onActivated(handleQuery)

  return {
    formRef,
    tableRef,
    formData,
    tableData,
    queryLoading,
    handleQuery
  }
}

const { formRef, tableRef, formData, tableData, queryLoading, handleQuery } = useQuery()

Promise.all([handleQuery()])

const useOperation = () => {
  const currentRow = ref<ProductCategoryAPI.Data | null>(null)
  const addDialogType = ref(AddCategoryTypeEnum.ADD)
  const addDialogVisible = ref(false)
  const handleOpenAddDialog = (row: ProductCategoryAPI.Data | null, type: AddCategoryTypeEnum) => {
    currentRow.value = row
    addDialogType.value = type
    addDialogVisible.value = true
  }

  const handleStatusChange = async (status: string, row: ProductCategoryAPI.Data) => {
    ElMessageBox.alert('当前分类及对应的子层级分类都会同时更新状态', '提示', {
      showCancelButton: true,
      showClose: true,
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = instance.cancelButtonLoading = true
          const [error, result] = await updateProductCategoryStatus({
            id: row.id,
            status,
            beforeStatus: row.status
          })
          instance.confirmButtonLoading = instance.cancelButtonLoading = false
          if (error === null && result) {
            ElMessage.success(result.msg || '操作成功')
            done()
            await handleQuery()
          }
        } else {
          done()
        }
      }
    }).catch(() => {})
  }

  return {
    currentRow,
    addDialogType,
    addDialogVisible,
    handleStatusChange,
    handleOpenAddDialog
  }
}

const { currentRow, addDialogType, addDialogVisible, handleStatusChange, handleOpenAddDialog } =
  useOperation()
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_200px)] overflow-x-hidden overflow-y-auto">
        <ElForm
          ref="formRef"
          :model="formData"
          inline
          label-width="auto"
          @submit="
            (e) => {
              e.preventDefault()
            }
          "
        >
          <ElFormItem label="分类名称" prop="date">
            <ElInput
              v-model="formData.categoryName"
              clearable
              placeholder="请输入分类名称"
              @change="handleQuery"
            />
          </ElFormItem>
          <ElFormItem label-width="0">
            <ElButton
              :loading="queryLoading"
              type="primary"
              @click="handleQuery"
              native-type="submit"
            >
              查询
            </ElButton>
            <ElButton @click="handleOpenAddDialog(null, AddCategoryTypeEnum.ADD)"
              >新增分类</ElButton
            >
          </ElFormItem>
        </ElForm>
        <VxeTable
          ref="tableRef"
          :data="tableData"
          :row-config="{ keyField: 'id' }"
          :tree-config="{ childrenField: 'childList', reserve: true }"
        >
          <VxeColumn field="categoryCnName" title="分类名称" tree-node />
          <VxeColumn field="categoryEnName" title="分类英文名称" />
          <VxeColumn field="categoryCode" title="分类编码" />
          <VxeColumn field="structureItemName" title="款式结构" />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: statusMap } }"
            field="status"
            title="状态"
          />
          <VxeColumn field="wmsCategoryName" title="商品分类" />
          <VxeColumn field="wmsCategoryCode" title="商品分类编码" />
          <VxeColumn :show-overflow="false" title="操作">
            <template #default="{ row }: { row: ProductCategoryAPI.Data }">
              <div class="flex justify-around items-center">
                <ElSwitch
                  v-if="
                    row.status?.toLowerCase() === LastDataStatusEnum.Start.toLowerCase() ||
                    row.status?.toLowerCase() === LastDataStatusEnum.Ban.toLowerCase()
                  "
                  :model-value="row.status"
                  active-value="start"
                  inactive-value="ban"
                  @change="(val:string) => handleStatusChange(val, row)"
                />
                <ElButton
                  v-if="row.status?.toLowerCase() === LastDataStatusEnum.Draft.toLowerCase()"
                  text
                  type="primary"
                  @click="handleOpenAddDialog(row, AddCategoryTypeEnum.UPDATE)"
                >
                  修改
                </ElButton>
                <ElButton
                  text
                  type="primary"
                  @click="handleOpenAddDialog(row, AddCategoryTypeEnum.ADD)"
                >
                  新增分类
                </ElButton>
              </div>
            </template>
          </VxeColumn>
        </VxeTable>
      </div>
      <AddCategoryDialog
        v-model="addDialogVisible"
        :current-row="currentRow"
        :type="addDialogType"
        @refresh="handleQuery"
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped></style>
