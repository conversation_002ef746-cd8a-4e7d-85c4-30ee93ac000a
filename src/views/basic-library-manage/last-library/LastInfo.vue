<script lang="ts">
import { DiffFieldEnum } from '@/views/basic-library-manage/last-library/const'

export type DiffData = {
  [key in DiffFieldEnum]?: string[]
}
</script>

<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { CascaderInstance, ElLoading, ElMessage, FormInstance, FormRules } from 'element-plus'
import {
  createLastInfo,
  LastInfoAPI,
  updateLastInfo,
  viewLastInfo,
  viewLastInfoByVersionId
} from '@/views/basic-library-manage/last-library/api/lastInfo'
import BaseUpload from '@/components/Upload/BaseUpload.vue'
import { UPLOAD_URL } from '@/views/basic-library-manage/api/upload'
import { useBasicLibraryDictStore } from '@/views/basic-library-manage/store/dict'
import { Icon } from '@/components/Icon'
import { cloneDeep, groupBy, pick } from 'lodash-es'
import * as jsondiffpatch from 'jsondiffpatch'
import DiffDialog, {
  FormMode as DiffFormMode
} from '@/views/basic-library-manage/last-library/components/DiffDialog.vue'
import { statusConst, StatusEnum } from '@/views/basic-library-manage/const'
import {
  getHeelReferencedListById,
  HeelReferencedListAPI
} from '@/views/basic-library-manage/api/common'
import { ProductListPageAPI } from '@/views/basic-library-manage/product-library/api/product-list'
import { VxeTableInstance } from 'vxe-table'
import ResourceTable from '@/views/basic-library-manage/last-library/components/ResourceTable.vue'
import ElCascader from '@/components/Cascader/src/Cascader'
import { useSizeOptions } from '@/utils/useSizeOptions'
import { SizeCodeTypeEnums } from '@/enums'

defineOptions({
  name: 'LastInfo'
})

const activeNames = ref(['1', '2', '3', '4'])

const route = useRoute()

const useType = () => {
  const isCreate = computed(() => {
    return route.name === 'CreateLast'
  })

  const isEdit = computed(() => {
    return route.name === 'EditLast'
  })

  const isView = computed(() => {
    return route.name === 'ViewLast'
  })

  const isCopy = computed(() => {
    return route.name === 'CopyLast'
  })

  return {
    isCreate,
    isEdit,
    isView,
    isCopy
  }
}

const { isCreate, isEdit, isView, isCopy } = useType()

const id = computed(() => {
  return route.query.id
})

const versionId = computed(() => {
  return route.query.versionId
})

const useConst = () => {
  const dictStore = useBasicLibraryDictStore()
  const store = computed(() => ({
    lastTypeList: dictStore.lastTypeList,
    lastHeightList: dictStore.lastHeightList,
    lastMarketList: dictStore.lastMarketList,
    lastSexList: dictStore.lastSexList,
    lastHeadList: dictStore.heelHeadList,
    commonRegionList: dictStore.commonRegionList,
    commonSeasonList: dictStore.commonDevSeasonList
  }))

  const statusList = statusConst.statusList.filter((e) => e.value !== StatusEnum.APPROVING)

  return {
    statusList,
    store
  }
}

const { statusList, store } = useConst()

type FormMode = LastInfoAPI.Data

const useBasicInfo = () => {
  const formRef = ref<FormInstance>()
  const formData = ref<FormMode>({
    id: undefined,
    thumbnail: undefined,
    reference: [],
    developmentSeason: '',
    head: '',
    sex: '',
    height: '',
    type: '',
    designer: [],
    market: '',
    shoeFactory: '',
    region: '',
    brand: [],
    supplierLastNumber: '',
    remark: '',
    sizeId: [],
    standardSizeValue: [],
    standardSizeId: [],
    lastBottomLength: undefined,
    palmWidth: undefined,
    waistCircumference: undefined,
    palmCircumference: undefined,
    toeCurl: undefined,
    heelCircumference: undefined,
    lastRearLength: undefined,
    heelHeight: undefined,
    last3dScanFile: []
  })
  const formRules = ref<FormRules<Omit<FormMode, 'productList'>>>({
    thumbnail: [
      {
        required: true,
        message: '请上传缩略图'
      }
    ],
    developmentSeason: [
      {
        required: true,
        message: '请选择开发季节',
        trigger: 'change'
      }
    ],
    head: [
      {
        required: true,
        message: '请选择头型',
        trigger: 'change'
      }
    ],
    sex: [
      {
        required: true,
        message: '请选择性别',
        trigger: 'change'
      }
    ],
    height: [
      {
        required: true,
        message: '请选择楦头帮高',
        trigger: 'change'
      }
    ],
    type: [
      {
        required: true,
        message: '请选择楦头类别',
        trigger: 'change'
      }
    ],
    designer: [
      {
        required: true,
        message: '请选择设计师',
        trigger: 'change'
      }
    ],
    market: [
      {
        required: true,
        message: '请选择楦头市场',
        trigger: 'change'
      }
    ],
    structure: [
      {
        required: true,
        message: '请选择结构',
        trigger: 'change'
      }
    ],
    region: [
      {
        required: true,
        message: '请选择区域',
        trigger: 'change'
      }
    ],
    standardSizeId: [
      {
        required: true,
        message: '请选择尺码段',
        trigger: 'change'
      }
    ]
  })
  const formDataHistory = ref<FormMode>({})
  const referencedList = ref<HeelReferencedListAPI.Data[]>([])

  const sizeIdRef = ref<CascaderInstance>()
  const sizeValue = ref('')
  const { sizeOptions, fetchSizeList } = useSizeOptions(SizeCodeTypeEnums.PRODUCT)
  const handleSizeIdChange = (value?: number[]) => {
    if (!value?.length) {
      sizeValue.value = ''
      return
    }
    const nodes = sizeIdRef.value?.getCheckedNodes(true)
    const sizeValues = groupBy(
      nodes?.map((node) => {
        const [name, value] = node.pathLabels
        return {
          name,
          value
        }
      }),
      'name'
    )
    sizeValue.value = Object.keys(sizeValues)
      .map((key) => {
        const values = sizeValues[key]
        return `${values.map((e) => e.value).join(', ')} 【${key}】`
      })
      .join('、')
  }

  /**
   * 1、【草稿】状态的数据可以修改；
   * 2、【启用】状态的楦型数据没有被产品库、模具库等数据引用，用户可以修改内容；
   * 3、启用状态下已经被引用的楦型数据【性别】、【楦头类别】、【楦头市场】、【楦头帮高】、【开发季节】信息置灰不能修改， 其他内容可以调整；
   */
  const formDisabled = computed(() => {
    return !(
      isCreate.value ||
      isCopy.value ||
      formData.value.statusCode === StatusEnum.DRAFT ||
      (formData.value.statusCode === StatusEnum.START && !formData.value.referenced)
    )
  })

  const fetchLastInfo = async () => {
    if (isCreate.value) {
      return
    }
    const loading = ElLoading.service({
      fullscreen: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const [[error, result], [referenceError, referenceResult]] = await Promise.all([
      id.value ? viewLastInfo(+id.value!) : viewLastInfoByVersionId(+versionId.value!),
      id.value ? getHeelReferencedListById(+id.value!) : []
    ])
    loading.close()
    if (error === null && result?.datas) {
      formDataHistory.value = cloneDeep(result.datas)
      formData.value = result.datas
      if (isCopy.value) {
        // 复制时，清空图片信息
        formData.value.thumbnail = undefined
        formData.value.reference = []
        formData.value.productList = []
        formData.value.productId = []
      }
    }
    if (referenceError === null && referenceResult?.datas) {
      referencedList.value = referenceResult.datas || []
    }
  }

  return {
    formRef,
    sizeIdRef,
    sizeOptions,
    formData,
    formRules,
    sizeValue,
    fetchSizeList,
    handleSizeIdChange,
    formDisabled,
    fetchLastInfo,
    referencedList,
    formDataHistory
  }
}

const {
  formRef,
  sizeIdRef,
  sizeValue,
  sizeOptions,
  fetchSizeList,
  formData,
  formRules,
  formDisabled,
  fetchLastInfo,
  referencedList,
  formDataHistory,
  handleSizeIdChange
} = useBasicInfo()

onActivated(() => {
  if (isView.value) {
    fetchLastInfo()
  }
})

Promise.all([fetchLastInfo(), fetchSizeList()]).then(() => {
  handleSizeIdChange(formData.value.standardSizeId)
})

const useDiffDialog = () => {
  const diffDialogVisible = ref(false)
  const handleOpen = () => {
    diffDialogVisible.value = true
  }
  return {
    diffDialogVisible,
    handleOpenDiffDialog: handleOpen
  }
}

const { diffDialogVisible, handleOpenDiffDialog } = useDiffDialog()

const router = useRouter()

const useOperation = () => {
  const diffData = ref<DiffData>({})
  const getDifference = () => {
    const diffKeys = [
      DiffFieldEnum.SIZE_ID,
      DiffFieldEnum.STANDARD_SIZE_VALUE,
      DiffFieldEnum.LAST_BOTTOM_LENGTH,
      DiffFieldEnum.PALM_WIDTH,
      DiffFieldEnum.WAIST_CIRCUMFERENCE,
      DiffFieldEnum.PALM_CIRCUMFERENCE,
      DiffFieldEnum.TOE_CURL,
      DiffFieldEnum.HEEL_CIRCUMFERENCE,
      DiffFieldEnum.LAST_REAR_LENGTH,
      DiffFieldEnum.HEEL_HEIGHT
    ]
    const left = pick(formData.value, diffKeys)
    const right = pick(formDataHistory.value, diffKeys)
    return jsondiffpatch.diff(left, right)
  }
  const handleClose = () => {
    useClosePage('LastLibrary')
  }

  const submitLoading = ref(false)
  const submitFn = computed(() => {
    if (isCreate.value || isCopy.value) {
      return createLastInfo
    }
    if (isEdit.value) {
      return updateLastInfo
    }
    return createLastInfo
  })

  async function handleConfirm() {
    if (isView.value) {
      router.push({
        name: 'EditLast',
        query: {
          id: id.value
        }
      })
      return
    }
    await handleSubmit()
  }

  const handleSubmit = async () => {
    if (isEdit.value) {
      formData.value.id = Number(id.value)
    }
    const valid = await formRef.value?.validate()
    if (valid) {
      if (isEdit.value && formData.value.statusCode === StatusEnum.START) {
        const diff = getDifference()
        if (diff) {
          diffData.value = diff
          handleOpenDiffDialog()
          return
        }
      }
      submitLoading.value = true
      const [error, result] = await submitFn.value(formData.value)
      submitLoading.value = false
      if (error === null && result) {
        handleClose()
        ElMessage.success(result.msg || '保存成功')
      } else {
        ElMessage.error(error?.message || '保存失败')
      }
    }
  }

  const handleDiffSubmit = async (formMode: DiffFormMode) => {
    if (formMode.isEdit) {
      const [error, result] = await updateLastInfo(formData.value)
      if (error === null && result) {
        handleClose()
        ElMessage.success(result.msg || '保存成功')
      } else {
        ElMessage.error(error?.message || '保存失败')
      }
      return
    }
    if (!formMode.isEdit) {
      const [error, result] = await createLastInfo({
        ...formData.value,
        productId: formMode.styleList
      })
      if (error === null && result) {
        handleClose()
        ElMessage.success(result.msg || '保存成功')
      } else {
        ElMessage.error(error?.message || '保存失败')
      }
    }
  }

  const tableRef = ref<VxeTableInstance | null>(null)

  const currentResourceRow = ref<ProductListPageAPI.Row>({})
  const resourceDialogVisible = ref(false)
  const handleViewResource = (row: ProductListPageAPI.Row) => {
    currentResourceRow.value = row
    resourceDialogVisible.value = true
  }
  return {
    diffData,
    submitLoading,
    handleConfirm,
    handleClose,
    handleDiffSubmit,
    tableRef,
    currentResourceRow,
    resourceDialogVisible,
    handleViewResource
  }
}
const handleEditLast = () => {
  const status = formData.value.statusCode
  if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
    ElMessage.warning('审批中/禁用数据不允许修改')
    return
  }
  router.push({
    name: 'EditLast',
    query: {
      id: formData.value.id
    }
  })
}
const {
  diffData,
  submitLoading,
  handleClose,
  handleConfirm,
  handleDiffSubmit,
  tableRef,
  currentResourceRow,
  resourceDialogVisible
} = useOperation()
</script>

<template>
  <ResourceTable
    v-model="resourceDialogVisible"
    :current-row="currentResourceRow"
    :supplierLastNumber="formData.supplierLastNumber"
  />
  <ContentWrap class="info-wrapper">
    <ElCollapse v-model="activeNames">
      <ElForm
        ref="formRef"
        :disabled="isView"
        :model="formData"
        :rules="formRules"
        :scroll-into-view-options="{ behavior: 'smooth' }"
        labelWidth="auto"
        scroll-to-error
      >
        <ElCollapseItem name="1" title="基础信息">
          <template #title>
            <div class="font-bold text-base">基础信息</div>
          </template>
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="楦型缩略图" prop="thumbnail">
                <BaseUpload
                  :action="UPLOAD_URL"
                  :limit="1"
                  :modelValue="formData.thumbnail ? [formData.thumbnail] : []"
                  :size-limit="1024 * 1024 * 10"
                  accept="image/*"
                  drag
                  multiple
                  name="files"
                  @update:model-value="(val) => (formData.thumbnail = val[0])"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="参考图片" prop="referenceAddress">
                <BaseUpload
                  v-model="formData.reference"
                  :action="UPLOAD_URL"
                  :limit="20"
                  :size-limit="1024 * 1024 * 100"
                  accept="image/*"
                  drag
                  multiple
                  name="files"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="开发季节" prop="developmentSeason">
                <ElSelect
                  v-model="formData.developmentSeason"
                  :disabled="formDisabled"
                  clearable
                  filterable
                  placeholder="请选择开发季节"
                >
                  <ElOption
                    v-for="item in store.commonSeasonList"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue!"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isView" :span="12">
              <ElFormItem label="楦型编码" prop="code">
                <ElInput v-model="formData.code" class="w-48" disabled />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="头型" prop="head">
                <ElSelect v-model="formData.head" clearable filterable placeholder="请选择头型">
                  <ElOption
                    v-for="item in store.lastHeadList"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue!"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="性别" prop="sex">
                <ElSelect
                  v-model="formData.sex"
                  :disabled="formDisabled"
                  clearable
                  filterable
                  placeholder="请选择性别"
                >
                  <ElOption
                    v-for="item in store.lastSexList"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue!"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="楦头帮高" prop="height">
                <ElSelect
                  v-model="formData.height"
                  :disabled="formDisabled"
                  clearable
                  filterable
                  placeholder="请选择楦头帮高"
                >
                  <ElOption
                    v-for="item in store.lastHeightList"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue!"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="楦头类别" prop="type">
                <ElSelect
                  v-model="formData.type"
                  :disabled="formDisabled"
                  clearable
                  filterable
                  placeholder="请选择楦头类别"
                >
                  <ElOption
                    v-for="item in store.lastTypeList"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue!"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="设计师" prop="designer">
                <CascadeSelector
                  v-model="formData.designer"
                  :props="{ emitPath: false, multiple: true }"
                  :show-all-levels="!isView"
                  api-key="allUsers"
                  cache
                  clearable
                  filterable
                  placeholder="请选择"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="楦头市场" prop="market">
                <ElSelect
                  v-model="formData.market"
                  :disabled="formDisabled"
                  clearable
                  filterable
                  placeholder="请选择楦头市场"
                >
                  <ElOption
                    v-for="item in store.lastMarketList"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue!"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="结构" prop="structure">
                <SelectPlus
                  v-model="formData.structure"
                  api-key="COMMON_STYLE_STRUCTURE"
                  cache
                  clearable
                  filterable
                  placeholder="请选择款式结构"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="区域" prop="region">
                <ElSelect v-model="formData.region" clearable filterable placeholder="请选择区域">
                  <ElOption
                    v-for="item in store.commonRegionList"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue!"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="供应商" prop="assignedFactory">
                <SelectPlus
                  v-model="formData.assignedFactory"
                  api-key="getSupplierList"
                  clearable
                  filterable
                  placeholder="请选择"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="鞋厂名称" prop="shoeFactory">
                <ElInput
                  v-model="formData.shoeFactory"
                  class="w-48"
                  clearable
                  maxlength="100"
                  placeholder="请输入鞋厂名称"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="品牌" prop="brand">
                <SelectPlus
                  v-model="formData.brand"
                  api-key="baseBrand"
                  cache
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  multiple
                  placeholder="请选择品牌"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="供应商楦头编号" prop="supplierLastNumber">
                <ElInput
                  v-model="formData.supplierLastNumber"
                  class="w-48"
                  clearable
                  maxlength="100"
                  placeholder="请输入供应商楦头编号"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="备注" prop="remark">
                <ElInput
                  v-model="formData.remark"
                  :autosize="{ minRows: 4, maxRows: 4 }"
                  :resize="isView ? 'none' : undefined"
                  class="w-48"
                  maxlength="500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isView" :span="12">
              <ElFormItem label="状态" prop="statusCode">
                <ElRadioGroup v-model="formData.statusCode">
                  <ElRadio
                    v-for="e in statusList"
                    :key="e.value"
                    :label="e.label"
                    :value="e.value"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCollapseItem>
        <ElCollapseItem name="2" title="楦型详细信息">
          <template #title>
            <div class="font-bold text-base">楦型详细信息</div>
          </template>
          <El-Row :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="尺码段" prop="standardSizeId">
                <ElCascader
                  ref="sizeIdRef"
                  v-model="formData.standardSizeId"
                  :options="sizeOptions"
                  :props="{
                    value: 'id',
                    emitPath: false,
                    multiple: true
                  }"
                  clearable
                  filterable
                  placeholder="请选择尺码段"
                  @change="handleSizeIdChange"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="样品码" prop="standardSizeValue">
                <ElInput
                  v-model="sizeValue"
                  :autosize="{
                    minRows: 2,
                    maxRows: 4
                  }"
                  disabled
                  placeholder="请选择尺码值"
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="楦底长(mm)" prop="lastBottomLength">
                <ElInputNumber
                  v-model="formData.lastBottomLength"
                  :controls="false"
                  :min="0"
                  :precision="2"
                  :step="1"
                  :value-on-clear="null"
                  class="min-w-48"
                  clearable
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="掌宽(mm)" prop="palmWidth">
                <ElInputNumber
                  v-model="formData.palmWidth"
                  :controls="false"
                  :min="0"
                  :precision="2"
                  :step="1"
                  :value-on-clear="null"
                  class="min-w-48"
                  clearable
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="腰围(mm)" prop="waistCircumference">
                <ElInputNumber
                  v-model="formData.waistCircumference"
                  :controls="false"
                  :min="0"
                  :precision="2"
                  :step="1"
                  :value-on-clear="null"
                  class="min-w-48"
                  clearable
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="掌围(mm)" prop="palmCircumference">
                <ElInputNumber
                  v-model="formData.palmCircumference"
                  :controls="false"
                  :min="0"
                  :precision="2"
                  :step="1"
                  :value-on-clear="null"
                  class="min-w-48"
                  clearable
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="头翘(mm)" prop="toeCurl">
                <ElInputNumber
                  v-model="formData.toeCurl"
                  :controls="false"
                  :min="0"
                  :precision="2"
                  :step="1"
                  :value-on-clear="null"
                  class="min-w-48"
                  clearable
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="背围(mm)" prop="heelCircumference">
                <ElInputNumber
                  v-model="formData.heelCircumference"
                  :controls="false"
                  :min="0"
                  :precision="2"
                  :step="1"
                  class="min-w-48"
                  clearable
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="楦后身长(mm)" prop="lastRearLength">
                <ElInputNumber
                  v-model="formData.lastRearLength"
                  :controls="false"
                  :min="0"
                  :precision="2"
                  :step="1"
                  :value-on-clear="null"
                  class="min-w-48"
                  clearable
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="跟高(mm)" prop="heelHeight">
                <ElInputNumber
                  v-model="formData.heelHeight"
                  :controls="false"
                  :min="0"
                  :precision="2"
                  :step="1"
                  :value-on-clear="null"
                  class="min-w-48"
                  clearable
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="楦型3D扫描文件" prop="last3dScanFile">
                <OssUpload
                  v-model="formData.last3dScanFile"
                  :limit="20"
                  :size-limit="1024 * 1024 * 100"
                  drag
                  list-type="text"
                  muliple
                >
                  <template #trigger>
                    <el-button>
                      <Icon icon="material-symbols:upload" />
                      <div class="leading-4">上传文件</div>
                    </el-button>
                  </template>
                </OssUpload>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="技转报告文件" prop="completeTechnologyTransferReport">
                <OssUpload
                  v-model="formData.transferReportFile"
                  :limit="20"
                  :size-limit="1024 * 1024 * 100"
                  drag
                  list-type="text"
                  muliple
                >
                  <template #trigger>
                    <el-button>
                      <Icon icon="material-symbols:upload" />
                      <div class="leading-4">上传文件</div>
                    </el-button>
                  </template>
                </OssUpload>
              </ElFormItem>
            </ElCol>
          </El-Row>
        </ElCollapseItem>
      </ElForm>
      <ElCollapseItem name="3" title="关联产品信息">
        <template #title>
          <div class="font-bold text-base">关联产品信息</div>
        </template>
        <VxeTable
          ref="tableRef"
          :cell-config="{ height: 80 }"
          :data="formData.productList"
          :max-height="500"
        >
          <VxeColumn title="序号" type="seq" />
          <VxeColumn field="productNumber" title="产品编码" />
          <VxeColumn :cell-render="{ name: 'Image' }" field="thumbnail" title="产品缩略图" />
          <VxeColumn field="targetAudienceItemName" title="适用人群" />
          <VxeColumn field="productCategoryItemName" title="产品分类" />
          <VxeColumn field="brandItemName" title="品牌" />
          <VxeColumn field="launchSeasonItemName" title="开发季节" />
          <VxeColumn field="meetingResultItemName" title="选品会结果" />
          <!--          <VxeColumn field="styleResourceList" title="关联的楦头级放数据">-->
          <!--            <template #default="{ row }: { row: ProductListPageAPI.Row }">-->
          <!--              <ElButton text type="primary" @click="handleViewResource(row)">查看级放数据</ElButton>-->
          <!--            </template>-->
          <!--          </VxeColumn>-->
        </VxeTable>
      </ElCollapseItem>
      <ElCollapseItem v-if="isView" name="4" title="关联的跟底信息">
        <template #title>
          <div class="font-bold text-base">关联的跟底信息</div>
        </template>
        <VxeTable :cell-config="{ height: 80 }" :data="referencedList" :max-height="500">
          <VxeColumn title="序号" type="seq" />
          <VxeColumn field="code" title="跟底编码" />
          <VxeColumn
            :cell-render="{ name: 'Image' }"
            field="thumbnailDownloadUrl"
            title="缩略图"
            width="100"
          />
          <VxeColumn field="typeCodeItemName" title="类型" />
          <VxeColumn field="brandItemName" title="品牌" />
          <VxeColumn field="regionItemName" title="区域" />
          <VxeColumn field="supplierHeelNumber" title="供应商跟底编码" />
          <VxeColumn field="developmentSeasonCodeItemName" title="开发季节" />
          <VxeColumn field="status" title="状态" />
        </VxeTable>
      </ElCollapseItem>
    </ElCollapse>
  </ContentWrap>
  <ContentWrap class="mt-2">
    <div class="text-center">
      <ElButton @click="handleClose">返回</ElButton>
      <ElButton v-if="!isView" :loading="submitLoading" type="primary" @click="handleConfirm">
        确定
      </ElButton>
      <ElButton
        v-if="
          formData.statusCode !== StatusEnum.APPROVING &&
          formData.statusCode !== StatusEnum.BAN &&
          isView
        "
        v-hasPermi="['editLast']"
        type="primary"
        @click="handleEditLast"
      >
        <Icon size="20" icon="ep:edit" />
        修改楦型
      </ElButton>
    </div>
  </ContentWrap>
  <DiffDialog
    v-model="diffDialogVisible"
    :diff-data="diffData"
    :left="formData"
    :right="formDataHistory"
    @submit="handleDiffSubmit"
  />
</template>

<style lang="less" scoped>
.info-wrapper {
  max-height: calc(100vh - 250px);
  overflow: auto;
}

:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}
</style>
