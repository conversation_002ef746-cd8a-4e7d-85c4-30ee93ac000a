import to from 'await-to-js'
import { service } from '@/config/axios/service'

/**
 * 根据条件获取楦型列表数据
 */
export namespace LastListPageAPI {
  export interface Params {
    /**
     * 品牌
     */
    brand?: string[]
    /**
     * 楦型编码
     */
    code?: string
    /**
     * 页码
     */
    current?: number
    /**
     * 开发季节
     */
    devSeason?: string[]
    /**
     * 结束时间
     */
    endTime?: string
    /**
     * 头型
     */
    head?: string[]
    idList?: string[]
    /**
     * 楦型市场
     */
    market?: string[]
    /**
     * 区域
     */
    region?: string[]
    /**
     * 性别
     */
    sex?: string[]
    /**
     * 页面尺寸
     */
    size?: number
    /**
     * 开始时间
     */
    startTime?: string
    /**
     * 状态
     */
    status?: string[]
    /**
     * 结构
     */
    structure?: string[]
    /**
     * style
     */
    style?: string[]
    /**
     * 供应商楦头编号
     */
    supplierLastNumber?: string
  }
  export interface Row {
    /**
     * 品牌
     */
    brand?: string[]
    /**
     * 楦型编码
     */
    code?: string
    /**
     * 设计师
     */
    designer?: string[]
    designerItemName?: string
    /**
     * 开发季节code
     */
    developmentSeasonCode?: string
    /**
     * 头型code
     */
    headCode?: string
    /**
     * 背围(mm)
     */
    heelCircumference?: number
    /**
     * 跟高(mm)
     */
    heelHeight?: number
    /**
     * 楦头帮高code
     */
    heightCode?: string
    id?: number
    /**
     * 楦底长(mm)
     */
    lastBottomLength?: number
    /**
     * 楦后身长(mm)
     */
    lastRearLength?: number
    /**
     * 市场code
     */
    marketCode?: string
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 操作人
     */
    operator?: string
    /**
     * 掌围(mm)
     */
    palmCircumference?: number
    /**
     * 掌宽(mm)
     */
    palmWidth?: number
    /**
     * 区域code
     */
    regionCode?: string
    /**
     * 性别code
     */
    sexCode?: string
    /**
     * 鞋厂
     */
    shoeFactory?: string
    /**
     * 尺码段
     */
    sizeName?: string
    /**
     * 标准码
     */
    standardSizeValue?: string[]
    /**
     * 状态
     */
    status?: string
    /**
     * 状态code
     */
    statusCode?: string
    /**
     * 结构code
     */
    structureCode?: string
    /**
     * 关联的style
     */
    styleName?: string[]
    /**
     * 供应商楦头编号
     */
    supplierLastNumber?: string
    thumbnail?: BaseFileDTO
    /**
     * 头翘(mm)
     */
    toeCurl?: number
    /**
     * 楦头类别code
     */
    typeCode?: string
    /**
     * 腰围(mm)
     */
    waistCircumference?: number
  }
  export type List = Row[]
  export type Response = PagedResponseData<Row>
  export type Request = Params & PageParams
}
export function getLastListByPage(params: LastListPageAPI.Request, signal?: AbortSignal) {
  return to<LastListPageAPI.Response>(service.get('/pdm-base/last/page', { params, signal }))
}
