import to from 'await-to-js'
import { service } from '@/config/fetch/service'

export namespace AmplificationAPI {
  export interface Request {
    productNumber?: string
    supplierLastNumber?: string
  }
  export interface Data {
    /**
     * 靴筒脚踝宽（整圈或半面）
     */
    bootAnkleWidth?: string[]
    /**
     * 靴筒腿肚宽（整圈或半面）
     */
    bootCalfWidth?: string[]
    /**
     * 靴筒高度直量
     */
    bootCuffHeightStraight?: string[]
    /**
     * 靴筒筒口宽（整圈或半面)
     */
    bootCuffOpeningWidth?: string[]
    /**
     * 领口内高度
     */
    collarInnerHeight?: string[]
    /**
     * 领口外高度
     */
    collarOuterHeight?: string[]
    /**
     * 后跟高（空楦/外量）
     */
    heelHeight?: string[]
    /**
     * 跟分段
     */
    heelSegmentation?: string[]
    /**
     * 后提带外露长度
     */
    heelStrapExposedLength?: string[]
    /**
     * 鞋眼最高点
     */
    highestEyeletPoint?: string[]
    id?: number
    /**
     * 内腰（空檀/外量）
     */
    innerWaist?: string[]
    /**
     * 后跟内量高度
     */
    insideHeelHeight?: string[]
    /**
     * 鞋带长度（cm）
     */
    laceLength?: string[]
    /**
     * 楦背围
     */
    lastBackGirth?: string[]
    /**
     * 檀底长
     */
    lastBottomLength?: string[]
    /**
     * 楦头后跟底版宽
     */
    lastHeelBaseWidth?: string[]
    /**
     * 楦头跟高
     */
    lastHeelHeight?: string[]
    /**
     * 檀掌围
     */
    lastPalmGirth?: string[]
    /**
     * 楦掌宽
     */
    lastPalmWidth?: string[]
    /**
     * 楦头后身宽
     */
    lastRearBodyWidth?: string[]
    /**
     * 楦头号码
     */
    lastSize?: string[]
    /**
     * 楦头头翘
     */
    lastToeHeadSpring?: string[]
    /**
     * 楦头翘度
     */
    lastToeSpring?: string[]
    /**
     * 楦腰围
     */
    lastWaistGirth?: string[]
    /**
     * 鱼嘴大小
     */
    mouthOpeningSize?: string[]
    /**
     * 订单号码
     */
    orderSize?: string[]
    /**
     * 其他
     */
    other?: string[]
    /**
     * 外腰（空楦/外量）
     */
    outerWaist?: string[]
    /**
     * 后跟外量高度
     */
    outsideHeelHeight?: string[]
    /**
     * 大底号码
     */
    soleSize?: string[]
    /**
     * 条带1
     */
    strapOne?: string[]
    /**
     * 条带3
     */
    strapThree?: string[]
    /**
     * 条带2
     */
    strapTwo?: string[]
    /**
     * 鞋头外露长度
     */
    toeExposedLength?: string[]
    /**
     * 鞋头长（外量）
     */
    toeLengthOuter?: string[]
    /**
     * 鞋舌外露长度
     */
    tongueExposedLength?: string[]
    /**
     * 产前试做明细表id
     */
    trialDetailId?: number
    /**
     * 防水台
     */
    waterproofPlatform?: string[]
  }
  export type Response = ResponseData<Data>
}

export function getAmplificationByProductNumber(params: AmplificationAPI.Request) {
  return to<AmplificationAPI.Response>(service.get('/pdm-base/last/amplification', { params }))
}
