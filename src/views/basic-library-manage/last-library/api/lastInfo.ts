import { service } from '@/config/axios/service'
import to from 'await-to-js'
import { ProductListPageAPI } from '@/views/basic-library-manage/product-library/api/product-list'

export namespace CreateLastAPI {
  export type Params = Omit<
    LastInfoAPI.Data,
    'id' | 'referenced' | 'status' | 'statusCode' | 'referenceDownloadUrl' | 'thumbnailDownloadUrl'
  >
  export type Response = BasicResponseData
  export type Request = Params
}
export function createLastInfo(data: CreateLastAPI.Request) {
  return to<CreateLastAPI.Response>(service.post('/pdm-base/last/save', data))
}

export namespace UpdateLastAPI {
  export type Params = Omit<
    LastInfoAPI.Data,
    'referenced' | 'status' | 'statusCode' | 'referenceDownloadUrl' | 'thumbnailDownloadUrl'
  >
  export type Response = BasicResponseData
  export type Request = Params
}
export function updateLastInfo(data: UpdateLastAPI.Params) {
  return to<UpdateLastAPI.Response>(service.post('/pdm-base/last/update', data))
}

export namespace LastInfoAPI {
  export interface Data {
    /**
     * 技转报告文件
     */
    transferReportFile?: BaseFileDTO[]
    /**
     * 供应商
     */
    assignedFactory?: string
    /**
     * 编码
     */
    code?: string
    /**
     * 品牌
     */
    brand?: string[]
    /**
     * 设计师
     */
    designer?: string[]
    /**
     * 开发季节code
     */
    developmentSeason?: string
    /**
     * 头型code
     */
    head?: string
    /**
     * 背围(mm)
     */
    heelCircumference?: number
    /**
     * 跟高(mm)
     */
    heelHeight?: number
    /**
     * 楦头帮高code
     */
    height?: string
    id?: number
    /**
     * 楦型3D扫描文件
     */
    last3dScanFile?: BaseFileDTO[]
    /**
     * 楦底长(mm)
     */
    lastBottomLength?: number
    /**
     * 楦后身长(mm)
     */
    lastRearLength?: number
    /**
     * 楦头市场code
     */
    market?: string
    /**
     * 掌围(mm)
     */
    palmCircumference?: number
    /**
     * 掌宽(mm)
     */
    palmWidth?: number
    /**
     * 产品id
     */
    productId?: number[]
    /**
     * 关联的产品详情
     */
    productList?: Array<ProductListPageAPI.Row & { styleResourceList?: StyleResourceListAPI.List }>
    /**
     * 参考图oss地址
     */
    reference?: BaseFileDTO[]
    /**
     * 是否被产品库，模具库引用
     */
    referenced?: boolean
    /**
     * 区域code
     */
    region?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 性别code
     */
    sex?: string
    /**
     * 鞋厂
     */
    shoeFactory?: string
    /**
     * 尺码段id
     */
    sizeId?: number[]
    standardSizeId?: number[]
    /**
     * 标准码
     */
    standardSizeValue?: string[]
    /**
     * 状态
     */
    status?: string
    /**
     * 状态code
     */
    statusCode?: string
    /**
     * 结构code
     */
    structure?: string
    /**
     * 供应商楦头编号
     */
    supplierLastNumber?: string
    thumbnail?: BaseFileDTO
    /**
     * 头翘(mm)
     */
    toeCurl?: number
    /**
     * 楦头类别code
     */
    type?: string
    /**
     * 腰围(mm)
     */
    waistCircumference?: number
  }
  export type Response = ResponseData<Data>
}
export function viewLastInfo(id: number) {
  return to<LastInfoAPI.Response>(service.get(`/pdm-base/last/detail/${id}`))
}
export function viewLastInfoByVersionId(id: number) {
  return to<LastInfoAPI.Response>(service.get(`/pdm-base/last/versionDetail/${id}`))
}

export namespace StyleResourceListAPI {
  export interface PtyStdChoppingExcelChangeRpcDTO {
    /**
     * 后包高度（外量）
     */
    bagHeight?: null | string
    /**
     * 大底号码
     */
    bigBottomSize?: null | string
    /**
     * id
     */
    choppingId?: number | null
    /**
     * 口深（空楦/外量）
     */
    deep?: null | string
    /**
     * 码数
     */
    euSize?: null | string
    /**
     * 跟分段
     */
    heelLevel?: null | string
    /**
     * 后高（空楦/外量）
     */
    height?: null | string
    /**
     * 内腰（空楦/外量）
     */
    innerSize?: null | string
    /**
     * 量产号码
     */
    number?: null | string
    /**
     * size信息
     */
    numberSize?: null | string
    /**
     * 外腰（空楦/外量）
     */
    outSize?: null | string
    /**
     * 楦背围
     */
    shoeBack?: null | string
    /**
     * 楦底长
     */
    shoeBottomLength?: null | string
    /**
     * 楦掌宽
     */
    shoeBottomWidth?: null | string
    /**
     * 楦头头翘
     */
    shoeHeadWarp?: null | string
    /**
     * 楦头后跟宽
     */
    shoeHeelWidth?: null | string
    /**
     * 楦头跟高
     */
    shoeHeight?: null | string
    /**
     * 楦头后跟肥
     */
    shoeHellSize?: null | string
    /**
     * 楦掌围
     */
    shoePlam?: null | string
    /**
     * 靴筒脚踝宽度
     */
    shoesAnkleWidth?: null | string
    /**
     * 靴筒后高
     */
    shoesHeight?: null | string
    /**
     * 楦头号码
     */
    shoesSize?: null | string
    /**
     * 靴筒腿肚宽度
     */
    shoesTripeWidth?: null | string
    /**
     * 靴筒筒口宽
     */
    shoesWidth?: null | string
    /**
     * 楦腰围
     */
    shoeWaist?: null | string
    /**
     * 楦头总翘度
     */
    shoeWarp?: null | string
    /**
     * sizeid
     */
    sizeId?: number | null
  }
  export interface PtyStdChoppingExcelHandRpcDTO {
    /**
     * 背围
     */
    back?: string
    /**
     * 大底号码
     */
    bigBottomSize?: string
    /**
     * 楦长
     */
    bottomLength?: string
    /**
     * 掌宽
     */
    bottomWidth?: string
    /**
     * id
     */
    choppingId?: number
    /**
     * 码数
     */
    euSize?: string
    /**
     * 跟分段
     */
    heelLevel?: string
    /**
     * 量产号码
     */
    number?: string
    /**
     * size信息
     */
    numberSize?: string
    /**
     * 掌围
     */
    plam?: string
    /**
     * 后跟高（内量空楦）
     */
    shoeHeelHeight?: string
    /**
     * 内腰高（外量）
     */
    shoeInnerHeight?: string
    /**
     * 鞋头长（外量长度）
     */
    shoeLength?: string
    /**
     * 外腰高（外量）
     */
    shoeOutSize?: string
    /**
     * 靴脚腕围宽
     */
    shoesAnkleWidth?: string
    /**
     * 靴筒高
     */
    shoesHeight?: string
    /**
     * 楦头号码
     */
    shoesSize?: string
    /**
     * 靴筒宽（腿肚围）
     */
    shoesTripeWidth?: string
    /**
     * 靴筒口宽
     */
    shoesWidth?: string
    /**
     * 楦头总翘度
     */
    shoeWarp?: string
    /**
     * sizeid
     */
    sizeId?: number
    /**
     * 腰围
     */
    waist?: string
    /**
     * 防水台
     */
    waterProof?: string
  }
  export interface Row {
    /**
     * 附件OSS名称
     */
    attachmentName?: string
    /**
     * 附件保存后对应的名称
     */
    attachmentResourceName?: string
    /**
     * 附件URL
     */
    attachmentUrl?: string
    /**
     * 全套VOCExcel级放内容
     */
    changeList?: PtyStdChoppingExcelChangeRpcDTO[]
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建人
     */
    createByName?: string
    /**
     * 创建时间
     */
    createTime?: string
    /**
     * 数据状态（0：正常，1：删除）
     */
    delFlag?: number
    /**
     * 手剪试做excel级放内容
     */
    handList?: PtyStdChoppingExcelHandRpcDTO[]
    /**
     * 主键
     */
    id?: number
    /**
     * 更新人
     */
    modifyById?: number
    /**
     * 创建人
     */
    modifyByName?: string
    /**
     * 更新时间
     */
    modifyTime?: string
    /**
     * 报告类型
     */
    reportType?: string
    reportTypeI18?: string
    /**
     * 资料包名称字典值（STYLE_RESOURCE_ZIP）
     */
    resourceDict?: string
    /**
     * 资料包名称字典值（STYLE_RESOURCE_ZIP）
     */
    resourceDictI18?: string
    /**
     * 型体标准id
     */
    styleStdMgmtId?: number
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}
export function getStyleResourceListByProductNumber(productNumber: string) {
  return to<StyleResourceListAPI.Response>(
    service.get('/pdm-base/base/styleResource', { params: { styleCode: productNumber } })
  )
}
