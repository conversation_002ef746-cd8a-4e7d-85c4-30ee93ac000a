export const infoVOCTableSchema = [
  {
    field: 'number',
    label: '量产号码',
    width: 90
  },
  {
    field: 'numberSize',
    label: '关联系统尺码',
    width: 120
  },
  {
    field: 'euSize',
    label: '欧码',
    width: 80
  },
  {
    field: 'shoesSize',
    label: '楦头号码',
    width: 100
  },
  {
    field: 'bigBottomSize',
    label: '大底号码',
    width: 100
  },
  {
    field: 'shoeBottomLength',
    label: '楦底长',
    width: 80
  },
  {
    field: 'shoeBottomWidth',
    label: '楦掌宽',
    width: 80
  },
  {
    field: 'shoePlam',
    label: '楦掌围',
    width: 80
  },
  {
    field: 'shoeWaist',
    label: '楦腰围',
    width: 80
  },
  {
    field: 'shoeBack',
    label: '楦背围',
    width: 80
  },
  {
    field: 'shoeWarp',
    label: '楦头总翘度',
    width: 100
  },
  {
    field: 'shoeHeight',
    label: '楦头跟高',
    width: 150
  },
  {
    field: 'shoeHeadWarp',
    label: '楦头头翘',
    width: 150
  },
  {
    field: 'shoeHeelWidth',
    label: '楦头后跟宽',
    width: 150
  },
  {
    field: 'shoeHellSize',
    label: '楦头后跟肥',
    width: 100
  },
  {
    field: 'deep',
    label: '口深（空楦/外量)',
    width: 180
  },
  {
    field: 'height',
    label: '后高（空楦/外量）',
    width: 180
  },
  {
    field: 'outSize',
    label: '外腰（空楦/外量)',
    width: 180
  },
  {
    field: 'innerSize',
    label: '内腰（空楦/外量）',
    width: 180
  },
  {
    field: 'shoesHeight',
    label: '靴筒后高',
    width: 100
  },
  {
    field: 'shoesWidth',
    label: '靴筒筒口宽',
    width: 100
  },
  {
    field: 'shoesTripeWidth',
    label: '靴筒腿肚宽度',
    width: 150
  },
  {
    field: 'shoesAnkleWidth',
    label: '靴筒脚踝宽度',
    width: 150
  },
  {
    field: 'bagHeight',
    label: '后包高度（外量）',
    width: 150
  },
  {
    field: 'heelLevel',
    label: '跟分段',
    width: 100
  }
]

export const infoTableSchema = [
  {
    field: 'number',
    label: '量产号码',
    width: 90
  },
  {
    field: 'numberSize',
    label: '关联系统尺码',
    width: 120
  },
  {
    field: 'euSize',
    label: '码数',
    width: 80
  },
  {
    field: 'shoesSize',
    label: '楦头号码',
    width: 100
  },
  {
    field: 'bigBottomSize',
    label: '大底号码',
    width: 100
  },
  {
    field: 'bottomLength',
    label: '楦长',
    width: 80
  },
  {
    field: 'bottomWidth',
    label: '掌宽',
    width: 80
  },
  {
    field: 'plam',
    label: '掌围',
    width: 80
  },
  {
    field: 'waist',
    label: '腰围',
    width: 80
  },
  {
    field: 'back',
    label: '背围',
    width: 80
  },
  {
    field: 'shoeWarp',
    label: '楦头总翘度',
    width: 100
  },
  {
    field: 'shoeLength',
    label: '鞋头长（外量长度)',
    width: 150
  },
  {
    field: 'shoeHeelHeight',
    label: '后跟高（内量空楦）',
    width: 150
  },
  {
    field: 'shoeInnerHeight',
    label: '内腰高（外量）',
    width: 150
  },
  {
    field: 'shoeOutSize',
    label: '外腰高（外量）',
    width: 150
  },
  {
    field: 'shoesHeight',
    label: '靴筒高',
    width: 100
  },
  {
    field: 'shoesTripeWidth',
    label: '靴筒宽（腿肚围）',
    width: 150
  },
  {
    field: 'shoesWidth',
    label: '靴筒口宽',
    width: 100
  },
  {
    field: 'shoesAnkleWidth',
    label: '靴脚腕围宽',
    width: 100
  },
  {
    field: 'waterProof',
    label: '防水台',
    width: 100
  },
  {
    field: 'heelLevel',
    label: '跟分段',
    width: 100
  }
]
