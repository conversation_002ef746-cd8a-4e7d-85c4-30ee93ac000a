<script lang="ts" setup>
import { Icon } from '@/components/Icon'
import { LastListPageAPI } from '../api/last-list'
import { StatusEnum } from '../../const'
import { useBasicLibraryDictStore } from '../../store/dict'
import { ElTooltip, Measurable } from 'element-plus'
import { BaseClamp, MouseoverEvent } from '@/components/BaseClamp'

defineOptions({
  name: 'CardView'
})

const dictStore = useBasicLibraryDictStore()
const store = computed(() => ({
  brandMap: dictStore.brandMap,
  lastSexMap: dictStore.lastSexMap,
  lastTypeMap: dictStore.lastTypeMap,
  lastMarketMap: dictStore.lastMarketMap,
  commonRegionMap: dictStore.commonRegionMap,
  commonSeasonMap: dictStore.commonDevSeasonMap
}))

defineProps<{
  tableData: LastListPageAPI.List
  loading: boolean
  maxHeight?: number
}>()

const emit = defineEmits<{
  (e: 'change-status', val: LastListPageAPI.Row): void
  (e: 'edit-last', val: LastListPageAPI.Row): void
  (e: 'copy-last', val: LastListPageAPI.Row): void
  (e: 'view-last', val: LastListPageAPI.Row): void
}>()

const contentRef = ref<HTMLElement>()
const content = ref<string>('')
const tooltipRef = ref<InstanceType<typeof ElTooltip>>()

const handleMouseover = (e: MouseoverEvent) => {
  if (!e.clamped) {
    return
  }
  contentRef.value = e.currentTarget
  content.value = e.text
}
</script>

<template>
  <ElScrollbar>
    <div
      v-loading="loading"
      :style="{ maxHeight: maxHeight + 'px' }"
      class="grid justify-center min-h-[140px] grid-gap-[10px] grid-cols-[repeat(auto-fit,minmax(300px,1fr))]"
    >
      <div v-for="item in tableData" :key="item.id" class="p-2">
        <ElCard
          :body-style="{ padding: '0px', width: '100%', height: '100%' }"
          class="card-container"
          shadow="always"
          @click="emit('view-last', item)"
        >
          <div class="card-body">
            <div class="card-content">
              <div class="card-img-container">
                <ElImage
                  :src="item.thumbnail?.signatureUrl"
                  class="card-img"
                  fit="cover"
                  hide-on-click-modal
                  loading="lazy"
                />
                <ElTag class="card-status" effect="dark">
                  {{ item.status }}
                </ElTag>
              </div>
              <div class="card-button-container">
                <ElButton
                  v-hasPermi="['editLast']"
                  class="card-button"
                  size="small"
                  text
                  type="primary"
                  @click.stop="emit('edit-last', item)"
                >
                  <span class="card-button-content">
                    <Icon class="mb-1" icon="ep:edit" />
                    修改
                  </span>
                </ElButton>
                <ElButton
                  v-hasPermi="['copyLast']"
                  class="card-button"
                  size="small"
                  text
                  type="primary"
                  @click.stop="emit('copy-last', item)"
                >
                  <span class="card-button-content">
                    <Icon class="mb-1" icon="ep:copy-document" />
                    复制
                  </span>
                </ElButton>
                <ElButton
                  v-hasPermi="['last:changeStatus']"
                  class="card-button"
                  size="small"
                  text
                  type="primary"
                  @click.stop="emit('change-status', item)"
                >
                  <span class="card-button-content">
                    <Icon
                      :icon="item.statusCode === StatusEnum.START ? 'mdi:ban' : 'ep:video-play'"
                      class="mb-1"
                    />
                    {{ item.statusCode === StatusEnum.START ? '禁用' : '启用' }}
                  </span>
                </ElButton>
              </div>
            </div>
            <div class="card-content">
              <div class="card-info-container">
                <BaseClamp
                  :max-lines="1"
                  :text-content="item.code"
                  autoresize
                  @mouseover="handleMouseover"
                >
                  {{ item.code }}
                </BaseClamp>
              </div>
              <div class="card-info-container">
                <div class="w-full">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.brand?.map((e) => store.brandMap[e]).join(';')"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.brand?.map((e) => store.brandMap[e]).join(';') }}
                  </BaseClamp>
                </div>
              </div>
              <div class="card-info-container !flex-row">
                <div class="w-1/3">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.sexCode ? store.lastSexMap[item.sexCode] as string : ''"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.sexCode ? store.lastSexMap[item.sexCode] : '' }}
                  </BaseClamp>
                </div>
                <div class="w-1/3">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.typeCode ? store.lastTypeMap[item.typeCode] as string: ''"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.typeCode ? store.lastTypeMap[item.typeCode] : '' }}
                  </BaseClamp>
                </div>
                <div class="w-1/3">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.marketCode ? store.lastMarketMap[item.marketCode] as string: ''"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.marketCode ? store.lastMarketMap[item.marketCode] : '' }}
                  </BaseClamp>
                </div>
              </div>
              <div class="card-info-container !border-bottom-0 !flex-row">
                <div class="w-1/3">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="
                      item.developmentSeasonCode ? store.commonSeasonMap[item.developmentSeasonCode] as string: ''
                    "
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{
                      item.developmentSeasonCode
                        ? store.commonSeasonMap[item.developmentSeasonCode]
                        : ''
                    }}
                  </BaseClamp>
                </div>
                <div class="w-1/3">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.designerItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.designerItemName }}
                  </BaseClamp>
                </div>
                <div class="w-1/3">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.regionCode ? store.commonRegionMap[item.regionCode] as string: ''"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.regionCode ? store.commonRegionMap[item.regionCode] : '' }}
                  </BaseClamp>
                </div>
              </div>
              <div
                class="card-info-container !border-bottom-0 border-top-1 border-left-1 border-solid mt-[-2px] ml-[-1px]"
              >
                <div class="flex items-center h-full w-full">
                  <div class="w-[40%]">
                    <BaseClamp
                      :max-lines="1"
                      :text-content="item.operator"
                      autoresize
                      @mouseover="handleMouseover"
                    >
                      {{ item.operator }}
                    </BaseClamp>
                  </div>
                  <div class="w-[60%]">
                    <BaseClamp
                      :max-lines="1"
                      :text-content="item.modifyTime"
                      autoresize
                      @mouseover="handleMouseover"
                    >
                      {{ item.modifyTime }}
                    </BaseClamp>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ElCard>
      </div>
      <ElTooltip
        ref="tooltipRef"
        :content="content"
        :popper-options="{
          modifiers: [
            {
              name: 'computeStyles',
              options: {
                adaptive: false,
                enabled: false
              }
            }
          ]
        }"
        :virtual-ref="contentRef as Measurable"
        popper-class="singleton-tooltip"
        virtual-triggering
      />
    </div>
  </ElScrollbar>
</template>

<style lang="less" scoped>
.card-container {
  height: 200px;
  max-width: 330px;

  &:hover {
    cursor: pointer;
  }

  .card-body {
    display: flex;
    height: 100%;
    justify-content: space-between;

    > .card-content {
      width: 50%;
      font-size: 12px;

      > .card-img-container {
        position: relative;
        width: 100%;
        height: 80%;
        overflow: hidden;

        > .card-img {
          width: 100%;
          height: 100%;
        }

        > .card-status {
          position: absolute;
          top: 0;
          left: 0;
          border-radius: 0 var(--el-card-border-radius) var(--el-card-border-radius)
            var(--el-card-border-radius);
        }
      }

      > .card-button-container {
        height: 20%;

        > .card-button {
          width: 33.33%;
          height: auto;

          .card-button-content {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
        }

        > .card-button + .card-button {
          margin-left: 0;
        }
      }

      > .card-info-container {
        display: flex;
        width: 100%;
        height: 20%;
        padding: 0 8px;
        text-align: center;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
