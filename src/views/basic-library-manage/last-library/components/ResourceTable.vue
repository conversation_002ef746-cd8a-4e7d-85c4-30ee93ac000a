<script lang="ts" setup>
// 该页面是复制的QMS系统
// https://codeup.aliyun.com/61ee8bb874c3f3b55073ffd0/JS/QMS-admin-vue3/blob/master/src/views/ProofingManagement/TrialManagement/components/StageInformation.vue
import { onMounted, ref } from 'vue'
import { VxeGrid, VxeTableInstance } from 'vxe-table'
import { ProductListPageAPI } from '../../product-library/api/product-list'
import { AmplificationAPI, getAmplificationByProductNumber } from '../api/resourceTable'

const props = defineProps<{
  modelValue: boolean
  currentRow?: ProductListPageAPI.Row
  supplierLastNumber?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const handleClose = () => {
  visible.value = false
}

const xTable = ref<VxeTableInstance>()

const columns = ref<any>([])

const tableData = ref([
  { title: '订单号码', name: 'orderSize' },
  { title: '楦头号码', name: 'lastSize' },
  { title: '大底号码', name: 'soleSize' },
  { title: '级放信息分类', name: 'gradedInformation' },
  { title: '楦底长', name: 'lastBottomLength' },
  { title: '楦掌宽', name: 'lastPalmWidth' },
  { title: '楦掌围', name: 'lastPalmGirth' },
  { title: '楦腰围', name: 'lastWaistGirth' },
  { title: '楦背围', name: 'lastBackGirth' },
  { title: '楦头翘度', name: 'lastToeSpring' },
  { title: '后跟内量高度', name: 'insideHeelHeight' },
  { title: '后跟外量高度', name: 'outsideHeelHeight' },
  { title: '领口外高度', name: 'collarOuterHeight' },
  { title: '领口内高度', name: 'collarInnerHeight' },
  { title: '鞋眼最高点', name: 'highestEyeletPoint' },
  { title: '鞋带长度（cm）', name: 'laceLength' },
  { title: '鞋舌外露长度', name: 'tongueExposedLength' },
  { title: '鞋头外露长度', name: 'toeExposedLength' },
  { title: '后提带外露长度', name: 'heelStrapExposedLength' },
  { title: '楦头跟高', name: 'lastHeelHeight' },
  { title: '楦头头翘', name: 'lastToeHeadSpring' },
  { title: '楦头后身宽', name: 'lastRearBodyWidth' },
  { title: '楦头后跟底版宽', name: 'lastHeelBaseWidth' },
  { title: '后跟高（空楦/外量）', name: 'heelHeight' },
  { title: '外腰（空楦/外量）', name: 'outerWaist' },
  { title: '内腰（空楦/外量）', name: 'innerWaist' },
  { title: '鞋头长（外量）', name: 'toeLengthOuter' },
  { title: '鱼嘴大小', name: 'mouthOpeningSize' },
  { title: '条带1', name: 'strapOne' },
  { title: '条带2', name: 'strapTwo' },
  { title: '条带3', name: 'strapThree' },
  { title: '靴筒高度直量', name: 'bootCuffHeightStraight' },
  { title: '靴筒筒口宽（整圈或半面）', name: 'bootCuffOpeningWidth' },
  { title: '靴筒腿肚宽（整圈或半面）', name: 'bootCalfWidth' },
  { title: '靴筒脚踝宽（整圈或半面）', name: 'bootAnkleWidth' },
  { title: '防水台', name: 'waterproofPlatform' },
  { title: '跟分段', name: 'heelSegmentation' },
  { title: '其它', name: 'other' }
])

const mergeCells = ref<any>([])

const setColumns = () => {
  columns.value = ['尺码分类', ...new Array(23)].map((item, index) => {
    if (index == 0) {
      return {
        field: `title`, // todo
        title: item,
        width: 150,
        align: 'center'
      }
    }
    return {
      field: `col${index}`,
      title: index === 12 ? `尺码号` : '',
      slots: { default: `col${index}` },
      width: index == 1 ? 150 : 80,
      align: 'center'
    }
  })
}

const levelAmplification = ref<AmplificationAPI.Data>({})
const basicSizeData = ref<any>({})
const initDataResult = () => {
  const initObj = levelAmplification.value || {}
  const array = Array.from({ length: 22 }, (_, i) => i + 2)
  unref(tableData).forEach((v) => {
    if (v.name !== 'gradedInformation') {
      const obj = {}
      array.forEach((j, i) => {
        Object.assign(obj, {
          ['col' + j]: initObj?.[v.name]?.[i] || ''
        })
      })
      Object.assign(v, obj)
    }
  })
  xTable.value?.loadData(tableData.value)
}

watch(
  () => levelAmplification.value,
  (value) => {
    if (value && Object.keys(value).length) {
      initDataResult()
    }
  }
)

const queryLoading = ref(false)

defineExpose({
  tableData: tableData.value
})

const setMergeCells = () => {
  const row = tableData.value.length - 1
  mergeCells.value = [{ row, col: 2, rowspan: 0, colspan: 22 }]
}

watch(
  () => visible.value,
  async (val) => {
    if (val) {
      queryLoading.value = true
      const [error, result] = await getAmplificationByProductNumber({
        productNumber: props.currentRow?.productNumber,
        supplierLastNumber: props.supplierLastNumber
      })
      queryLoading.value = false
      if (error === null && result?.datas) {
        levelAmplification.value = result.datas
      }
      setMergeCells()
    }
  }
)

onMounted(() => {
  setColumns()
})
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    center
    title="查看级放信息"
    top="5vh"
    width="90%"
  >
    <VxeGrid
      v-if="visible"
      ref="xTable"
      :border="false"
      :column-config="{ resizable: false }"
      :columns="columns"
      :data="tableData"
      :loading="queryLoading"
      :mergeCells="mergeCells"
      :row-class-name="({ rowIndex }) => (rowIndex === 3 ? 'vxe-table-header-row' : '')"
      :show-header-overflow="false"
    >
      <template v-for="(item, index) in columns" :key="index" #[item.field]="{ rowIndex, row }">
        <!-- 第四行 -->
        <div v-if="rowIndex == 3">
          <div v-if="index == 1">基本码鞋型数据（{{ basicSizeData.baseSize }}）</div>
          <div v-if="index == 2">整码差</div>
          <div v-if="index == 3">半码差</div>
          <div v-if="index == 12">级放数据</div>
        </div>
        <div v-else>
          <!-- 第二列 -->
          <div v-if="index == 1 && rowIndex > 3 && rowIndex < 15">
            {{ basicSizeData[row.name] }}
          </div>
          <!-- 输入框 -->
          <span v-if="index != 1 && row.title != '其它'">
            {{ row[item.field] }}
          </span>
          <!-- 关于其它 v-model-->
          <span v-if="index != 1 && row.title == '其它'">
            {{ row[item.field] }}
          </span>
        </div>
      </template>
    </VxeGrid>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.vxe-table-header-row) {
  font-weight: bold;
  background-color: #f8f8f9;

  > .vxe-body--column {
    border: none;
  }
}

:deep(.vxe-body--column) {
  border: 0.5px solid #e8eaec;
  border-top: none;
  border-bottom: none;
}
</style>
