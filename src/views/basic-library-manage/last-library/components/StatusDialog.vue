<script lang="ts" setup>
import { LastListPageAPI } from '../api/last-list'
import { statusConst, StatusEnum } from '../../const'
import { changeStatus } from '../api/statusDialog'
import { ElMessage } from 'element-plus'
import { useBasicLibraryDictStore } from '@/views/basic-library-manage/store/dict'
import { UpdateStatusAPI } from '@/views/basic-library-manage/api/common'

defineOptions({
  name: 'StatusDialog'
})

const dictStore = useBasicLibraryDictStore()
const store = computed(() => ({
  lastSexMap: dictStore.lastSexMap,
  lastMarketMap: dictStore.lastMarketMap,
  lastHeadMap: dictStore.heelHeadMap,
  commonRegionMap: dictStore.commonRegionMap
}))

const props = defineProps<{
  modelValue: boolean
  selectedRows: LastListPageAPI.List
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

type TableRow = Partial<LastListPageAPI.Row & UpdateStatusAPI.StatusData & { updateStatus: string }>

const tableData = ref<TableRow[]>([])

watch(visible, (val) => {
  if (val) {
    tableData.value = props.selectedRows.slice()
  }
})

const handleClose = () => {
  visible.value = false
  tableData.value = []
}

const submitLoading = ref(false)
const handleSubmit = async () => {
  const hasError = tableData.value.some((item) => {
    return item.newStatus === undefined || item.newStatus === ''
  })
  if (hasError) {
    ElMessage.warning('请选择状态')
    return
  }
  submitLoading.value = true
  const [error, result] = await changeStatus({
    updateStatusReqList: tableData.value.map((e) => ({
      id: e.id!,
      oldStatus: e.statusCode!,
      newStatus: e.newStatus!
    }))
  })
  submitLoading.value = false
  if (error === null && result) {
    ElMessage.success(result.msg || '操作成功')
    handleClose()
    emit('refresh')
  }
}

// 状态枚举
const statusList = statusConst.statusList

const getStatusList = (row: LastListPageAPI.Row) => {
  if (row.statusCode === StatusEnum.DRAFT) {
    return statusList.filter((e) => e.value === StatusEnum.START || e.value === StatusEnum.BAN)
  }
  if (row.statusCode === StatusEnum.START) {
    return statusList.filter((e) => e.value === StatusEnum.BAN)
  }
  if (row.statusCode === StatusEnum.BAN) {
    return statusList.filter((e) => e.value === StatusEnum.START)
  }
}
</script>

<template>
  <Dialog v-model="visible" :before-close="handleClose" title="操作确认">
    <VxeTable :data="tableData" :max-height="500">
      <VxeColumn title="序号" type="seq" width="60" />
      <VxeColumn field="code" title="楦型编码" />
      <VxeColumn field="brand" title="品牌" />
      <VxeColumn
        :cell-render="{ name: 'Dict', props: { dictMap: store.lastSexMap } }"
        field="sexCode"
        title="性别"
      />
      <VxeColumn
        :cell-render="{ name: 'Dict', props: { dictMap: store.lastMarketMap } }"
        field="marketCode"
        title="楦头市场"
      />
      <VxeColumn
        :cell-render="{ name: 'Dict', props: { dictMap: store.lastHeadMap } }"
        field="headCode"
        title="楦头类别"
      />
      <VxeColumn
        :cell-render="{ name: 'Dict', props: { dictMap: store.commonRegionMap } }"
        field="regionCode"
        title="区域"
      />
      <VxeColumn field="styleName" title="关联的Style" />
      <VxeColumn field="status" title="当前状态" />
      <VxeColumn title="修改状态">
        <template #default="{ row }: { row: LastListPageAPI.Row & { newStatus: string } }">
          <ElSelect v-model="row.newStatus" size="small">
            <ElOption
              v-for="item in getStatusList(row)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
