<script lang="ts">
export interface FormMode {
  isEdit?: boolean
  styleList: number[]
}
</script>
<script setup lang="ts">
import { diffFieldMap } from '../const'
import { DiffData } from '../LastInfo.vue'
import { FormInstance, FormRules } from 'element-plus'
import { LastInfoAPI } from '@/views/basic-library-manage/last-library/api/lastInfo'

defineOptions({
  name: 'DiffDialog'
})

const props = defineProps<{
  modelValue: boolean
  diffData: DiffData
  left: LastInfoAPI.Data
  right: LastInfoAPI.Data
}>()

const diffDataKeys = computed(() => {
  return Object.keys(props.diffData)
})

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', val: FormMode): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const handleClose = () => {
  formData.value.isEdit = undefined
  formData.value.styleList = []
  visible.value = false
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const formRef = ref<FormInstance>()
const formData = ref<FormMode>({
  isEdit: undefined,
  styleList: []
})
const formRules = ref<FormRules<FormMode>>({
  isEdit: [{ required: true, message: '请选择', trigger: 'change' }],
  styleList: [{ required: true, message: '请选择', trigger: 'change' }]
})

watch(
  () => formData.value.isEdit,
  (val) => {
    if (val) {
      formData.value.styleList = []
    }
  }
)

const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) {
    return
  }
  emit('submit', formData.value)
  handleClose()
}
</script>

<template>
  <Dialog v-model="visible" :before-close="handleClose" width="800" title="操作确认">
    <div class="inline-flex flex-wrap" :class="diffDataKeys.length < 2 ? 'w-full' : 'w-1/2'">
      <div class="w-full flex flex-nowrap text-center font-medium text-sm bg-gray-100">
        <div class="border-1 border-solid border-gray-300 w-1/3 py-2">三维参数</div>
        <div class="border-1 border-l-0 border-solid border-gray-300 w-1/3 py-2">修改前</div>
        <div class="border-1 border-l-0 border-solid border-gray-300 w-1/3 py-2">修改后</div>
      </div>
      <div
        v-for="(item, index) in diffDataKeys"
        class="w-full flex flex-nowrap text-center font-medium text-sm"
        :class="index % 2 === 0 ? '' : 'hidden'"
        :key="item"
      >
        <div class="border-1 border-t-0 border-solid border-gray-300 w-1/3 py-2">
          {{ diffFieldMap[item] }}
        </div>
        <div class="border-1 border-t-0 border-l-0 border-solid border-gray-300 w-1/3 py-2">
          {{ right[item]?.join?.(',') || right[item] }}
        </div>
        <div
          class="border-1 border-t-0 border-l-0 border-solid border-gray-300 w-1/3 py-2 text-red-500"
        >
          {{ left[item]?.join?.(',') || left[item] }}
        </div>
      </div>
    </div>
    <div v-if="diffDataKeys.length > 1" class="inline-flex flex-wrap w-1/2">
      <div class="w-full flex flex-nowrap text-center font-medium text-sm bg-gray-100">
        <div class="border-1 border-l-0 border-solid border-gray-300 w-1/3 py-2">三维参数</div>
        <div class="border-1 border-l-0 border-solid border-gray-300 w-1/3 py-2">修改前</div>
        <div class="border-1 border-l-0 border-solid border-gray-300 w-1/3 py-2">修改后</div>
      </div>
      <div
        v-for="(item, index) in diffDataKeys"
        class="w-full flex flex-nowrap text-center font-medium text-sm"
        :class="index % 2 === 0 ? 'hidden' : ''"
        :key="item"
      >
        <div class="border-1 border-t-0 border-l-0 border-solid border-gray-300 w-1/3 py-2">
          {{ diffFieldMap[item] }}
        </div>
        <div class="border-1 border-t-0 border-l-0 border-solid border-gray-300 w-1/3 py-2">
          {{ right[item]?.join?.(',') || right[item] }}
        </div>
        <div
          class="border-1 border-t-0 border-l-0 border-solid border-gray-300 w-1/3 py-2 text-red-500"
        >
          {{ left[item]?.join?.(',') || left[item] }}
        </div>
      </div>
    </div>
    <ElForm ref="formRef" :model="formData" :rules="formRules" label-width="120">
      <ElFormItem label=" " prop="isEdit">
        <ElRadioGroup v-model="formData.isEdit">
          <ElRadio :label="true">修改当前楦型信息</ElRadio>
          <ElRadio :label="false">添加新楦型信息</ElRadio>
        </ElRadioGroup>
      </ElFormItem>
      <!--TODO: 可能放迭代里，暂时注释吧-->
      <!--<ElFormItem v-if="formData.isEdit === false" label="关联新产品" prop="styleList">-->
      <!--  <SelectPlus-->
      <!--    v-model="formData.styleList"-->
      <!--    api-key="getEffectProductIdList"-->
      <!--    :cache="false"-->
      <!--    placeholder="请选择"-->
      <!--    multiple-->
      <!--    collapse-tags-->
      <!--    collapse-tags-tooltip-->
      <!--    filterable-->
      <!--  />-->
      <!--</ElFormItem>-->
    </ElForm>
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确认</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>
