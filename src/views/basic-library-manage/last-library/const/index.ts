export enum DiffFieldEnum {
  /**
   * 尺码段
   */
  SIZE_ID = 'sizeId',
  /**
   * 样品码
   */
  STANDARD_SIZE_VALUE = 'standardSizeValue',
  /**
   * 楦底长(mm)
   */
  LAST_BOTTOM_LENGTH = 'lastBottomLength',
  /**
   * 掌宽(mm)
   */
  PALM_WIDTH = 'palmWidth',
  /**
   * 腰围(cm)
   */
  WAIST_CIRCUMFERENCE = 'waistCircumference',
  /**
   * 掌围(cm)
   */
  PALM_CIRCUMFERENCE = 'palmCircumference',
  /**
   * 头翘(mm)
   */
  TOE_CURL = 'toeCurl',
  /**
   * 背围(mm)
   */
  HEEL_CIRCUMFERENCE = 'heelCircumference',
  /**
   * 楦后身长(mm)
   */
  LAST_REAR_LENGTH = 'lastRearLength',
  /**
   * 跟高(mm)
   */
  HEEL_HEIGHT = 'heelHeight'
}

export const diffFieldMap = {
  [DiffFieldEnum.SIZE_ID]: '尺码段',
  [DiffFieldEnum.STANDARD_SIZE_VALUE]: '样品码',
  [DiffFieldEnum.LAST_BOTTOM_LENGTH]: '楦底长(mm)',
  [DiffFieldEnum.PALM_WIDTH]: '掌宽(mm)',
  [DiffFieldEnum.WAIST_CIRCUMFERENCE]: '腰围(cm)',
  [DiffFieldEnum.PALM_CIRCUMFERENCE]: '掌围(cm)',
  [DiffFieldEnum.TOE_CURL]: '头翘(mm)',
  [DiffFieldEnum.HEEL_CIRCUMFERENCE]: '背围(mm)',
  [DiffFieldEnum.LAST_REAR_LENGTH]: '楦后身长(mm)',
  [DiffFieldEnum.HEEL_HEIGHT]: '跟高(mm)'
}
