<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { ref } from 'vue'
import { ElCollapseTransition, ElMessage, ElPagination, FormInstance } from 'element-plus'
import { VxeColumn, VxeTableInstance } from 'vxe-table'
import { getLastListByPage, LastListPageAPI } from './api/last-list'
import { Pager } from '../api/common'
import dayjs from 'dayjs'
import { isEqual } from 'lodash-es'
import { useBasicLibraryDictStore } from '../store/dict'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { statusConst, StatusEnum } from '@/views/basic-library-manage/const'
import { Icon } from '@/components/Icon'
import CardView from './components/CardView.vue'
import StatusDialog from './components/StatusDialog.vue'
import VersionDialog from './components/VersionDialog.vue'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { SelectPlus } from '@/components/Business/SelectPlus'
import { formatRowSizeText } from '@/views/basic-library-manage/mold-library/mold-utils'
import { SizeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import { sizeList } from '@/components/Business/SelectPlus/src/api'

const props = defineProps<{
  isEmbed?: boolean
}>()

defineOptions({
  name: 'LastLibrary'
})

const router = useRouter()

const useConst = () => {
  const dictStore = useBasicLibraryDictStore()
  const store = computed(() => ({
    brandMap: dictStore.brandMap,
    lastSexMap: dictStore.lastSexMap,
    lastTypeMap: dictStore.lastTypeMap,
    lastMarketMap: dictStore.lastMarketMap,
    lastHeightMap: dictStore.lastHeightMap,
    lastHeadMap: dictStore.heelHeadMap,
    commonSeasonMap: dictStore.commonDevSeasonMap,
    lastSexList: dictStore.lastSexList,
    lastMarketList: dictStore.lastMarketList,
    commonRegionMap: dictStore.commonRegionMap,
    commonRegionList: dictStore.commonRegionList,
    commonSeasonList: dictStore.commonDevSeasonList
  }))

  // 状态枚举
  const statusMap = statusConst.statusMap
  const statusList = statusConst.statusList

  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '135'
    },
    {
      formLabelLength: '180'
    }
  ])

  return {
    store,
    statusMap,
    statusList,
    formLabelLength
  }
}

const { statusMap, statusList, store, formLabelLength } = useConst()

const sizeOptions = ref<SizeListAPI.Data[]>([])

async function querySizeList() {
  const { datas } = await sizeList()
  if (datas) {
    sizeOptions.value = datas
  }
}

const useQuery = () => {
  type FormMode = LastListPageAPI.Params & {
    date?: [string, string]
  }
  const formRef = ref<FormInstance>()
  const tableRef = ref<VxeTableInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const tableData = ref<LastListPageAPI.List>([])
  const queryLoading = ref(false)
  const defaultFormData: FormMode = {
    code: '',
    market: [],
    brand: [],
    status: props.isEmbed ? [StatusEnum.START] : [],
    region: [],
    sex: [],
    date: ['', ''],
    supplierLastNumber: '',
    devSeason: [],
    style: []
  }
  const formData = ref<FormMode>({
    ...defaultFormData
  })
  let lastFormData = {
    ...defaultFormData
  }
  const pager = ref<Pager>({
    current: 1,
    size: 10,
    total: 0
  })
  const queryParams = computed<LastListPageAPI.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1],
      date: undefined
    }
  })

  const defaultTime: [Date, Date] = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ]

  let controller: AbortController | null = null
  const handleQuery = async () => {
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await getLastListByPage(queryParams.value, controller.signal)
    queryLoading.value = false
    if (error === null && result?.datas) {
      lastFormData = { ...formData.value }
      const { records } = result.datas
      tableData.value = records.map((row) => {
        return {
          ...row,
          sizeValueIdText: formatRowSizeText(row, sizeOptions.value)
        }
      })
      pager.value.total = result.datas.pager.total
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
    handleQuery()
  }

  onActivated(handleQuery)

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = () => {
    const selected: LastListPageAPI.List | undefined = tableRef.value?.getCheckboxRecords()
    let reqParam: string
    if (selected && selected.length > 0) {
      reqParam = JSON.stringify({ idList: selected.map((item) => item.id) })
    } else {
      reqParam = JSON.stringify(queryParams.value)
    }
    exportFn({
      exportType: 'last-export',
      reqParam
    })
  }

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef,
    offsetBottom: props.isEmbed ? 50 + 20 + 32 + 10 + 30 : 0
  })

  return {
    formRef,
    tableRef,
    pagerRef,
    tableData,
    queryLoading,
    formData,
    pager,
    defaultTime,
    handleQuery,
    handleReset,
    handleExport,
    exportLoading,
    visible,
    setVisible,
    maxHeight
  }
}

const {
  formRef,
  tableRef,
  pagerRef,
  tableData,
  queryLoading,
  formData,
  pager,
  defaultTime,
  handleQuery,
  handleReset,
  handleExport,
  exportLoading,
  visible,
  setVisible,
  maxHeight
} = useQuery()

// 版本记录弹窗
const useVersionDialog = () => {
  const versionDialogVisible = ref(false)
  const setVersionDialogVisible = () => {
    versionDialogVisible.value = !unref(versionDialogVisible)
  }
  return {
    versionDialogVisible,
    setVersionDialogVisible
  }
}

// 启用/禁用弹窗
const useStatusDialog = () => {
  const statusDialogVisible = ref(false)
  const setStatusDialogVisible = () => {
    statusDialogVisible.value = !unref(statusDialogVisible)
  }
  return {
    statusDialogVisible,
    setStatusDialogVisible
  }
}

const useOperation = () => {
  const currentRow = ref<LastListPageAPI.Row>()
  const selectedRows = ref<LastListPageAPI.List>([])
  const isShowList = ref(props.isEmbed)

  // 切换图片/列表
  const handleToggleList = () => {
    isShowList.value = !unref(isShowList)
  }

  // 修改楦型
  const handleEditLast = (row?: LastListPageAPI.Row) => {
    let selectedRows: LastListPageAPI.List | undefined
    if (isShowList.value) {
      selectedRows = unref(tableRef)?.getCheckboxRecords()
    } else {
      selectedRows = row ? [row] : []
    }
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    const status = selectedRows[0].statusCode
    if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
      ElMessage.warning('审批中/禁用数据不允许修改')
      return
    }
    router.push({
      name: 'EditLast',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 复制楦型
  const handleCopyLast = (row?: LastListPageAPI.Row) => {
    let selectedRows: LastListPageAPI.List | undefined
    if (isShowList.value) {
      selectedRows = unref(tableRef)?.getCheckboxRecords()
    } else {
      selectedRows = row ? [row] : []
    }
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    router.push({
      name: 'CopyLast',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 启用/禁用
  const { statusDialogVisible, setStatusDialogVisible } = useStatusDialog()
  const handleChangeStatus = (row?: LastListPageAPI.Row) => {
    let selected: LastListPageAPI.List | undefined
    if (isShowList.value) {
      selected = unref(tableRef)?.getCheckboxRecords()
    } else {
      selected = row ? [row] : []
    }
    if (!selected?.length) {
      ElMessage.warning('请选择数据')
      return
    }
    const hasApproving = selected.some((item) => item.status === statusMap['approving'])
    if (hasApproving) {
      ElMessage.warning('存在审批中的数据')
      return
    }
    selectedRows.value = selected
    setStatusDialogVisible()
  }

  // 新增楦型
  const handleCreateLast = () => {
    router.push({
      name: 'CreateLast'
    })
  }

  // 版本记录
  const { versionDialogVisible, setVersionDialogVisible } = useVersionDialog()
  const handleOpenVersionDialog = (row: LastListPageAPI.Row) => {
    currentRow.value = row
    setVersionDialogVisible()
  }

  return {
    currentRow,
    selectedRows,
    isShowList,
    handleToggleList,
    handleCopyLast,
    handleEditLast,
    handleCreateLast,
    versionDialogVisible,
    handleOpenVersionDialog,
    handleChangeStatus,
    statusDialogVisible
  }
}

const {
  isShowList,
  handleToggleList,
  handleCopyLast,
  handleEditLast,
  handleCreateLast,
  versionDialogVisible,
  handleOpenVersionDialog,
  currentRow,
  selectedRows,
  handleChangeStatus,
  statusDialogVisible
} = useOperation()

Promise.all([querySizeList(), handleQuery()])

defineExpose({
  tableRef
})
</script>

<template>
  <ContentWrap>
    <div class="max-h-[calc(100vh_-_250px)] overflow-x-hidden overflow-y-auto">
      <ElForm ref="formRef" :label-width="formLabelLength" :model="formData">
        <ElRow :gutter="20">
          <ElCol :span="8">
            <ElFormItem label="楦型编码" prop="code">
              <ElInput
                v-model="formData.code"
                clearable
                placeholder="请输入楦型编码，支持模糊查询"
                @change="handleQuery"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="楦型市场" prop="market">
              <ElSelect
                v-model="formData.market"
                clearable
                collapse-tags
                collapse-tags-tooltip
                filterable
                multiple
                placeholder="请选择楦型市场"
              >
                <ElOption
                  v-for="item in store.lastMarketList"
                  :key="item.dictValue"
                  :label="item.dictCnName"
                  :value="item.dictValue!"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="" label-width="0">
              <ElButton text @click="setVisible">
                {{ visible ? '收起' : '展开' }}
                <Icon
                  :class="visible ? 'rotate-90' : ''"
                  class="transform transition duration-400"
                  icon="ant-design:down-outlined"
                />
              </ElButton>
              <ElButton :loading="queryLoading" type="primary" @click="handleQuery">
                <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
                查询
              </ElButton>
              <ElButton :loading="queryLoading" @click="handleReset">
                <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
                重置
              </ElButton>
              <ElButton
                v-if="!isEmbed"
                :loading="exportLoading"
                type="primary"
                @click="handleExport"
              >
                <Icon v-show="!exportLoading" class="mr-1" icon="ep:upload-filled" />
                导出
              </ElButton>
            </ElFormItem>
          </ElCol>
          <ElCol :span="16">
            <ElFormItem label="品牌" prop="brand">
              <SelectPlus
                v-model="formData.brand"
                api-key="baseBrand"
                cache
                checkbox
                checkbox-button
                placeholder="请选择品牌"
              />
            </ElFormItem>
          </ElCol>
          <ElCollapseTransition>
            <div v-show="visible" class="flex flex-wrap w-full">
              <ElCol :span="8">
                <ElFormItem label="状态" prop="status">
                  <ElScrollbar>
                    <ElCheckboxGroup
                      v-model="formData.status"
                      :disabled="isEmbed"
                      class="flex flex-nowrap"
                      @change="handleQuery"
                    >
                      <ElCheckboxButton
                        v-for="item in statusList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </ElCheckboxGroup>
                  </ElScrollbar>
                </ElFormItem>
              </ElCol>
              <ElCol :span="9">
                <ElFormItem label="区域" prop="region">
                  <ElScrollbar>
                    <ElCheckboxGroup
                      v-model="formData.region"
                      class="flex flex-nowrap"
                      @change="handleQuery"
                    >
                      <ElCheckboxButton
                        v-for="item in store.commonRegionList"
                        :key="item.dictValue"
                        :label="item.dictCnName"
                        :value="item.dictValue"
                      />
                    </ElCheckboxGroup>
                  </ElScrollbar>
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem label="性别" prop="sex">
                  <ElScrollbar>
                    <ElCheckboxGroup
                      v-model="formData.sex"
                      class="flex flex-nowrap"
                      @change="handleQuery"
                    >
                      <ElCheckboxButton
                        v-for="item in store.lastSexList"
                        :key="item.dictValue"
                        :label="item.dictCnName"
                        :value="item.dictValue"
                      />
                    </ElCheckboxGroup>
                  </ElScrollbar>
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem label="操作时间段" prop="date">
                  <ElDatePicker
                    v-model="formData.date"
                    :default-time="defaultTime"
                    class="max-w-96"
                    clearable
                    end-placeholder="结束时间"
                    range-separator="~"
                    start-placeholder="开始时间"
                    type="daterange"
                    unlink-panels
                    value-format="YYYY-MM-DD"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="供应商楦头编码" prop="supplierLastNumber">
                  <ElInput
                    v-model="formData.supplierLastNumber"
                    clearable
                    placeholder="请输入供应商楦头编码，支持模糊查询"
                    @change="handleQuery"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="9">
                <ElFormItem label="开发季节" prop="devSeason">
                  <ElSelect
                    v-model="formData.devSeason"
                    clearable
                    collapse-tags
                    collapse-tags-tooltip
                    filterable
                    multiple
                    placeholder="请选择开发季节"
                  >
                    <ElOption
                      v-for="item in store.commonSeasonList"
                      :key="item.dictValue"
                      :label="item.dictCnName"
                      :value="item.dictValue!"
                    />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="头型" prop="head">
                  <SelectPlus v-model="formData.head" api-key="HEEL_HEAD" cache clearable />
                </ElFormItem>
              </ElCol>
              <ElCol :span="16">
                <ElFormItem label="结构" prop="structure">
                  <SelectPlus
                    v-model="formData.structure"
                    api-key="COMMON_STYLE_STRUCTURE"
                    cache
                    clearable
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="关联的Style" prop="style">
                  <SelectPlus
                    v-model="formData.style"
                    api-key="getProductNumberList"
                    clearable
                    filterable
                    multiple
                    placeholder="请输入Style，支持模糊查询"
                    virtualized
                    @change="handleQuery"
                  />
                </ElFormItem>
              </ElCol>
            </div>
          </ElCollapseTransition>
        </ElRow>
      </ElForm>
      <div v-show="!isEmbed" class="mb-[10px] min-h-8">
        <ElButton v-hasPermi="['createLast']" type="primary" @click="handleCreateLast">
          <Icon icon="ep:plus" />
          <span class="text-[14px]">新增楦型</span>
        </ElButton>
        <ElButton
          v-show="isShowList"
          v-hasPermi="['editLast']"
          type="primary"
          @click="handleEditLast()"
        >
          <Icon icon="ep:edit" />
          <span class="text-[14px]">修改楦型</span>
        </ElButton>
        <ElButton
          v-show="isShowList"
          v-hasPermi="['last:changeStatus']"
          type="primary"
          @click="handleChangeStatus()"
        >
          <Icon icon="ep:share" />
          <span class="text-[14px]">启用/禁用</span>
        </ElButton>
        <ElButton
          v-show="isShowList"
          v-hasPermi="['copyLast']"
          type="primary"
          @click="handleCopyLast()"
        >
          <Icon icon="ep:copy-document" />
          <span class="text-[14px]">复制楦型</span>
        </ElButton>
        <ElButton class="float-right" text type="primary" @click="handleToggleList">
          <Icon :icon="isShowList ? 'ep:menu' : 'tabler:list'" />
          <span class="text-black text-xs">
            {{ isShowList ? '图片' : '列表' }}
          </span>
        </ElButton>
      </div>
      <CardView
        v-if="!isShowList"
        ref="cardRef"
        :loading="queryLoading"
        :max-height="maxHeight"
        :table-data="tableData"
        @change-status="handleChangeStatus"
        @copy-last="handleCopyLast"
        @edit-last="handleEditLast"
        @view-last="(row) => router.push({ name: 'ViewLast', query: { id: (row as LastListPageAPI.Row).id } })"
      />
      <div v-if="isShowList">
        <VxeTable
          ref="tableRef"
          :cell-config="{ height: 80 }"
          :data="tableData"
          :loading="queryLoading"
          :max-height="maxHeight - 125"
          :show-header-overflow="false"
        >
          <VxeColumn fixed="left" type="checkbox" width="40" />
          <VxeColumn title="序号" fixed="left" type="seq" width="60" />
          <VxeColumn field="code" fixed="left" show-overflow title="楦型编码" width="80">
            <template #default="{ row }: { row: LastListPageAPI.Row }">
              <router-link :to="{ name: 'ViewLast', query: { id: row.id } }">
                <span class="p-0 max-w-full cursor-pointer text-blue-500">
                  {{ row.code }}
                </span>
              </router-link>
            </template>
          </VxeColumn>
          <VxeColumn
            fixed="left"
            :cell-render="{ name: 'Image' }"
            field="thumbnail"
            title="缩略图"
            width="100"
          />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: store.brandMap } }"
            field="brand"
            min-width="100"
            title="品牌"
          />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: store.lastSexMap } }"
            field="sexCode"
            title="性别"
            width="80"
          />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: store.lastTypeMap } }"
            field="typeCode"
            title="楦头类别"
            width="100"
          />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: store.lastMarketMap } }"
            field="marketCode"
            min-width="100"
            title="楦头市场"
          />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: store.lastHeadMap } }"
            field="headCode"
            title="头型"
            width="80"
          />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: store.lastHeightMap } }"
            field="heightCode"
            min-width="100"
            title="楦头帮高"
          />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: store.commonSeasonMap } }"
            field="developmentSeasonCode"
            title="开发季节"
            width="100"
          />
          <VxeColumn field="designerItemName" title="设计师" width="100" />
          <VxeColumn field="status" fixed="right" title="状态" width="80" />
          <VxeColumn
            :cell-render="{ name: 'Dict', props: { dictMap: store.commonRegionMap } }"
            field="regionCode"
            title="区域"
            width="100"
          />
          <VxeColumn field="styleName" title="关联的Style" width="120" />
          <VxeColumn field="supplierLastNumber" title="供应商楦头编码" width="120" />
          <VxeColumn field="shoeFactory" title="鞋厂名称" width="90" />
          <VxeColumn field="sizeName" title="尺码段" width="90" />
          <VxeColumn
            :cell-render="{ name: 'Ellipsis', props: { maxRow: 2, separator: '、' } }"
            field="sizeValueIdText"
            title="样品码"
            width="90"
          />
          <VxeColumn field="lastBottomLength" title="楦底长(mm)" width="110" />
          <VxeColumn field="palmWidth" title="掌宽(mm)" width="90" />
          <VxeColumn field="waistCircumference" title="腰围(mm)" width="90" />
          <VxeColumn field="heelCircumference" title="背围(mm)" width="90" />
          <VxeColumn field="toeCurl" title="头翘(mm)" width="90" />
          <VxeColumn field="heelHeight" title="跟高(mm)" width="90" />
          <VxeColumn field="lastRearLength" title="楦后身长(mm)" width="110" />
          <VxeColumn field="operator" title="操作人" width="90" />
          <VxeColumn :show-overflow="false" field="modifyTime" title="操作时间" width="90" />
          <VxeColumn :show-overflow="false" fixed="right" title="操作" width="120">
            <template #default="{ row }">
              <ElButton
                v-if="!isEmbed"
                size="small"
                text
                type="primary"
                @click="handleOpenVersionDialog(row)"
              >
                版本记录
              </ElButton>
            </template>
          </VxeColumn>
        </VxeTable>
      </div>
    </div>
    <ElPagination
      ref="pagerRef"
      v-model:current-page="pager.current"
      v-model:page-size="pager.size"
      :total="pager.total"
      background
      class="mt-4"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleQuery"
      @current-change="handleQuery"
    />
    <VersionDialog v-model="versionDialogVisible" :current-row="currentRow" />
    <StatusDialog
      v-model="statusDialogVisible"
      :selected-rows="selectedRows"
      @refresh="handleQuery"
    />
  </ContentWrap>
</template>

<style lang="less" scoped>
:deep(.el-checkbox-button.is-checked .el-checkbox-button__inner) {
  color: var(--el-checkbox-button-checked-text-color);
  background-color: var(--el-checkbox-button-checked-bg-color);
  border-color: var(--el-checkbox-button-checked-border-color);
  box-shadow: -1px 0 0 0 var(--el-color-primary-light-7);
}
</style>
