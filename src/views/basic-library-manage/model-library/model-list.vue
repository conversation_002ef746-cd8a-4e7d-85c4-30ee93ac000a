<script lang="ts" setup>
import { ref } from 'vue'
import { ElCollapseTransition, ElMessage, ElPagination, FormInstance } from 'element-plus'
import { VxeTableInstance } from 'vxe-table'
import { Pager } from '../api/common'
import dayjs from 'dayjs'
import { isEqual } from 'lodash-es'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { Icon } from '@/components/Icon'
import { statusConst, StatusEnum } from '../const'
import { getModelListByPage, ModelListPageAPI } from './api/model-list'
import VersionDialog from './components/VersionDialog.vue'
import StatusDialog from './components/StatusDialog.vue'
import { SelectPlus } from '@/components/Business/SelectPlus'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { watchDebounced } from '@vueuse/core'

defineOptions({
  name: 'ModelLibrary'
})

const props = defineProps<{
  isEmbed?: boolean
}>()

const router = useRouter()

const useConst = () => {
  // 状态枚举
  const statusList = statusConst.statusList.filter((e) => e.value !== StatusEnum.APPROVING)
  const productNumberRef = ref<InstanceType<typeof SelectPlus>>()
  onActivated(() => {
    productNumberRef.value?.queryOptions()
  })

  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '135'
    },
    {
      formLabelLength: '180'
    }
  ])

  return {
    statusList,
    productNumberRef,
    formLabelLength
  }
}

const { statusList, productNumberRef, formLabelLength } = useConst()

const useQuery = () => {
  type FormModel = ModelListPageAPI.Params & {
    date?: [string, string]
  }
  const formRef = ref<FormInstance>()
  const tableRef = ref<VxeTableInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const tableData = ref<ModelListPageAPI.List>([])
  const queryLoading = ref(false)
  const defaultFormData: FormModel = {
    code: '',
    brand: [],
    status: props.isEmbed ? [StatusEnum.START] : [],
    region: [],
    developmentYear: [],
    productNumber: [],
    date: ['', ''],
    styleWms: []
  }
  const formData = ref<FormModel>({
    ...defaultFormData
  })
  let lastFormData = {
    ...defaultFormData
  }
  const pager = ref<Pager>({
    current: 1,
    size: 10,
    total: 0
  })
  const queryParams = computed<ModelListPageAPI.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1],
      date: undefined
    }
  })

  const defaultTime: [Date, Date] = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ]

  let controller: AbortController | null = null
  const handleQuery = async () => {
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await getModelListByPage(queryParams.value, controller.signal)
    queryLoading.value = false
    if (error === null && result?.datas) {
      lastFormData = { ...formData.value }
      const { records } = result.datas
      tableData.value = records
      pager.value.total = result.datas.pager.total
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
    handleQuery()
  }

  onActivated(handleQuery)

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = () => {
    const selected: ModelListPageAPI.List | undefined = tableRef.value?.getCheckboxRecords()
    let reqParam: string
    if (selected && selected.length > 0) {
      reqParam = JSON.stringify({ idList: selected.map((item) => item.id) })
    } else {
      reqParam = JSON.stringify(queryParams.value)
    }
    exportFn({
      exportType: 'model-export',
      reqParam
    })
  }

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef,
    offsetBottom: props.isEmbed ? 50 + 20 + 32 + 10 + 30 : 0
  })

  return {
    formRef,
    tableRef,
    pagerRef,
    tableData,
    queryLoading,
    formData,
    pager,
    defaultTime,
    handleQuery,
    handleReset,
    handleExport,
    exportLoading,
    visible,
    setVisible,
    maxHeight
  }
}

const useOperation = () => {
  const currentRow = ref<ModelListPageAPI.Row>()
  const selectedRows = ref<ModelListPageAPI.List>([])

  // 修改楦型
  const handleEditModel = () => {
    const selectedRows: ModelListPageAPI.List = tableRef.value?.getCheckboxRecords() || []
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    const status = selectedRows[0].statusCode
    if (status === StatusEnum.BAN) {
      ElMessage.warning('禁用数据不允许修改')
      return
    }
    router.push({
      name: 'EditModel',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 复制楦型
  const handleCopyModel = () => {
    const selectedRows: ModelListPageAPI.List = tableRef.value?.getCheckboxRecords() || []
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    router.push({
      name: 'CopyModel',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 启用/禁用
  const statusDialogVisible = ref(false)
  const setStatusDialogVisible = () => {
    statusDialogVisible.value = !unref(statusDialogVisible)
  }
  const handleChangeStatus = (row?: ModelListPageAPI.Row) => {
    let selected: ModelListPageAPI.List | undefined
    if (row) {
      selected = [row]
    } else {
      selected = unref(tableRef)?.getCheckboxRecords()
    }
    if (!selected?.length) {
      ElMessage.warning('请选择数据')
      return
    }
    selectedRows.value = selected
    setStatusDialogVisible()
  }

  // 新增楦型
  const handleCreateModel = () => {
    router.push({
      name: 'CreateModel'
    })
  }

  // 版本记录
  const versionDialogVisible = ref(false)
  const setVersionDialogVisible = () => {
    versionDialogVisible.value = !unref(versionDialogVisible)
  }
  const handleOpenVersionDialog = (row: ModelListPageAPI.Row) => {
    currentRow.value = row
    setVersionDialogVisible()
  }

  return {
    currentRow,
    selectedRows,
    handleCopyModel,
    handleEditModel,
    handleCreateModel,
    versionDialogVisible,
    handleOpenVersionDialog,
    handleChangeStatus,
    statusDialogVisible
  }
}

const {
  formRef,
  tableRef,
  pagerRef,
  tableData,
  queryLoading,
  formData,
  pager,
  defaultTime,
  handleQuery,
  handleReset,
  handleExport,
  exportLoading,
  visible,
  setVisible,
  maxHeight
} = useQuery()

const {
  handleCopyModel,
  handleEditModel,
  handleCreateModel,
  versionDialogVisible,
  handleOpenVersionDialog,
  currentRow,
  selectedRows,
  handleChangeStatus,
  statusDialogVisible
} = useOperation()

onActivated(handleQuery)

onMounted(() => {
  handleQuery()
  watchDebounced(
    formData,
    async () => {
      await handleQuery()
    },
    { deep: true, debounce: 400 }
  )
})

defineExpose({
  tableRef
})
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_225px)] overflow-x-hidden overflow-y-auto">
        <ElForm ref="formRef" :label-width="formLabelLength" :model="formData">
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="型体编码" prop="code">
                <ElInput
                  v-model="formData.code"
                  clearable
                  placeholder="请输入型体编码，支持模糊查询"
                  @change="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="品牌" prop="brand">
                <SelectPlus
                  v-model="formData.brand"
                  api-key="baseBrand"
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  multiple
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="" label-width="0">
                <ElButton text @click="setVisible">
                  {{ visible ? '收起' : '展开' }}
                  <Icon
                    :class="visible ? 'rotate-90' : ''"
                    class="transform transition duration-400"
                    icon="ant-design:down-outlined"
                  />
                </ElButton>
                <ElButton :loading="queryLoading" type="primary" @click="handleQuery">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
                  查询
                </ElButton>
                <ElButton :loading="queryLoading" @click="handleReset">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
                  重置
                </ElButton>
                <ElButton
                  v-if="!isEmbed"
                  v-hasPermi="['exportModel']"
                  :loading="exportLoading"
                  type="primary"
                  @click="handleExport"
                >
                  <Icon v-show="!exportLoading" class="mr-1" icon="ep:upload-filled" />
                  导出
                </ElButton>
              </ElFormItem>
            </ElCol>
            <ElCollapseTransition>
              <div v-show="visible" class="flex flex-wrap w-full">
                <ElCol :span="8">
                  <ElFormItem label="状态" prop="status">
                    <ElScrollbar>
                      <ElCheckboxGroup
                        v-model="formData.status"
                        :disabled="isEmbed"
                        class="flex flex-nowrap"
                        @change="handleQuery"
                      >
                        <ElCheckboxButton
                          v-for="item in statusList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </ElCheckboxGroup>
                    </ElScrollbar>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="9">
                  <ElFormItem label="区域" prop="region">
                    <ElScrollbar>
                      <SelectPlus
                        v-model="formData.region"
                        api-key="COMMON_REGION"
                        collapse-tags
                        collapse-tags-tooltip
                        filterable
                        multiple
                      />
                    </ElScrollbar>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="开发年份" prop="developmentYear">
                    <SelectPlus
                      v-model="formData.developmentYear"
                      api-key="COMMON_DEV_YEAR"
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="关联的产品" prop="productNumber">
                    <SelectPlus
                      ref="productNumberRef"
                      v-model="formData.productNumber"
                      api-key="getProductNumberList"
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      virtualized
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="操作时间段" prop="date">
                    <ElDatePicker
                      v-model="formData.date"
                      :default-time="defaultTime"
                      class="max-w-96"
                      clearable
                      end-placeholder="结束时间"
                      range-separator="~"
                      start-placeholder="开始时间"
                      type="daterange"
                      unlink-panels
                      value-format="YYYY-MM-DD"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="Style（WMS）" prop="styleWms">
                    <SelectPlus
                      v-model="formData.styleWms"
                      api-key="getStyleWmsList"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                      virtualized
                    />
                  </ElFormItem>
                </ElCol>
              </div>
            </ElCollapseTransition>
          </ElRow>
        </ElForm>
        <div v-if="!isEmbed" class="mb-[10px] min-h-8">
          <ElButton v-hasPermi="['createModel']" type="primary" @click="handleCreateModel">
            <Icon icon="ep:plus" />
            <span class="text-[14px]">新增型体</span>
          </ElButton>
          <ElButton v-hasPermi="['editModel']" type="primary" @click="handleEditModel()">
            <Icon icon="ep:edit" />
            <span class="text-[14px]">修改型体</span>
          </ElButton>
          <ElButton
            v-hasPermi="['model:changeStatus']"
            type="primary"
            @click="handleChangeStatus()"
          >
            <Icon icon="ep:share" />
            <span class="text-[14px]">启用/禁用</span>
          </ElButton>
          <ElButton v-hasPermi="['copyModel']" type="primary" @click="handleCopyModel()">
            <Icon icon="ep:copy-document" />
            <span class="text-[14px]">复制型体</span>
          </ElButton>
        </div>
        <VxeTable
          ref="tableRef"
          :cell-config="{ height: 80 }"
          :data="tableData"
          :loading="queryLoading"
          :max-height="maxHeight - 125"
          :show-header-overflow="false"
        >
          <VxeColumn fixed="left" type="checkbox" width="40" />
          <VxeColumn title="序号" fixed="left" type="seq" width="60" />
          <VxeColumn field="code" fixed="left" show-overflow title="型体编码" width="90">
            <template #default="{ row }: { row: ModelListPageAPI.Row }">
              <router-link :to="{ name: 'ViewModel', query: { id: row.id } }">
                <span class="p-0 max-w-full cursor-pointer text-blue-500">
                  {{ row.code }}
                </span>
              </router-link>
            </template>
          </VxeColumn>
          <VxeColumn
            fixed="left"
            :cell-render="{ name: 'Image' }"
            field="thumbnail"
            title="缩略图"
            width="80"
          />
          <VxeColumn field="brandItemName" min-width="100" title="品牌" />
          <VxeColumn field="developmentYearItemName" title="开发年份" width="80" />
          <VxeColumn field="regionItemName" min-width="100" title="区域" />
          <VxeColumn field="targetAudienceItemName" min-width="100" title="适用人群" />
          <VxeColumn field="productNumber" min-width="120" show-overflow title="关联的产品" />
          <VxeColumn field="status" fixed="right" title="状态" width="80" />
          <VxeColumn field="modifyByIdItemName" title="操作人" width="120" />
          <VxeColumn field="modifyTime" title="操作时间" width="80" />
          <VxeColumn :show-overflow="false" fixed="right" title="操作" width="120">
            <template #default="{ row }">
              <ElButton size="small" text type="primary" @click="handleOpenVersionDialog(row)">
                版本记录
              </ElButton>
            </template>
          </VxeColumn>
        </VxeTable>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
      <VersionDialog v-model="versionDialogVisible" :current-row="currentRow" />
      <StatusDialog
        v-model="statusDialogVisible"
        :selected-rows="selectedRows"
        @refresh="handleQuery"
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped></style>
