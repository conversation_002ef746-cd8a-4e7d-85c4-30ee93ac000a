<script lang="ts" setup>
import { ElLoading, ElMessage, FormInstance, FormRules } from 'element-plus'
import { HeelBottomTypeEnum, StatusEnum } from '../const'
import { Icon } from '@/components/Icon'
import HeelInfoDialog from '../components/HeelInfoDialog.vue'
import { HeelListPageAPI } from '../heel-library/api/heel-list'
import { VxeColumn, VxeTableInstance } from 'vxe-table'
import { getLastReferencedListByIdList, LastReferencedListAPI } from '../api/common'
import LastInfoDialog from '../components/LastInfoDialog.vue'
import {
  createModel,
  editModel,
  ModelInfoApi,
  viewModel,
  viewModelByVersion
} from './api/modelInfo'
import { useBasicLibraryDictStore } from '../store/dict'
import { ContentWrap } from '@/components/ContentWrap'

const route = useRoute()

const isView = computed(() => {
  return route.name === 'ViewModel'
})

const isCreate = computed(() => {
  return route.name === 'CreateModel'
})

const isEdit = computed(() => {
  return route.name === 'EditModel'
})

const isCopy = computed(() => {
  return route.name === 'CopyModel'
})

const id = computed(() => {
  return route.query.id
})

const versionId = computed(() => {
  return route.query.versionId
})

const dictStore = useBasicLibraryDictStore()
const store = computed(() => ({
  brandMap: dictStore.brandMap,
  lastSexMap: dictStore.lastSexMap,
  lastTypeMap: dictStore.lastTypeMap,
  lastMarketMap: dictStore.lastMarketMap,
  lastHeightMap: dictStore.lastHeightMap
}))

const activeNames = ref([1, 2, 3])

type FormModel = ModelInfoApi.ModelDetailResp & {
  heelIdItemName?: string
}

const formRef = ref<FormInstance>()
const formData = ref<FormModel>({})
const formRules = ref<FormRules<FormModel>>({
  thumbnail: [{ required: true, message: '请上传型体缩略图' }],
  developmentYear: [{ required: true, message: '请选择开发年份' }],
  targetAudience: [{ required: true, message: '请选择适用人群' }],
  brand: [{ required: true, message: '请选择品牌' }],
  region: [{ required: true, message: '请选择区域' }]
})

const submitParams = computed(() => {
  return {
    ...formData.value,
    thumbnail: formData.value.thumbnail?.[0]
  }
})

const heelInfoDialogVisible = ref(false)
const currentHeelBottomType = ref<HeelBottomTypeEnum[]>([])

const handleOpenHeelInfoDialog = (type: HeelBottomTypeEnum[]) => {
  if (isView.value) return
  currentHeelBottomType.value = type
  heelInfoDialogVisible.value = true
}

const handlePickHeel = (val?: HeelListPageAPI.Row) => {
  if (
    currentHeelBottomType.value.includes(HeelBottomTypeEnum.FB) ||
    currentHeelBottomType.value.includes(HeelBottomTypeEnum.OS)
  ) {
    formData.value.heelIdItemName = val?.code
    formData.value.heelId = val?.id
  }
  // 跟高
  formData.value.high = val?.highCode
  // 头型
  formData.value.head = val?.headCode
  // 跟型
  formData.value.pattern = val?.patternCode
  // 跟高(mm)
  formData.value.heelHigh = val?.heelHigh
}

const lastReferencedList = ref<LastReferencedListAPI.Data[]>([])
const handleQueryLastInfo = async (lastId) => {
  const [error, result] = await getLastReferencedListByIdList(lastId)
  if (!error && result) {
    lastReferencedList.value = result.datas || []
  }
}
const lastInfoDialogVisible = ref(false)
const handleOpenLastInfoDialog = () => {
  lastInfoDialogVisible.value = true
}

const handlePickLastInfo = (selectedRows: LastReferencedListAPI.Data[]) => {
  if (!formData.value.lastId) {
    formData.value.lastId = []
  }
  lastReferencedList.value = lastReferencedList.value.concat(
    selectedRows.filter((e) => !formData.value.lastId?.includes(e.id!))
  )
  formData.value.lastId = [
    ...new Set(formData.value.lastId.concat(selectedRows.map((e) => e.id!)) || [])
  ]
  lastTableRef.value?.loadData(lastReferencedList.value)
}
const lastTableRef = ref<VxeTableInstance | null>(null)
const handleDeleteLastInfo = (index: number) => {
  formData.value.lastId?.splice(index, 1)
  lastReferencedList.value.splice(index, 1)
  lastTableRef.value?.loadData(lastReferencedList.value)
}

const fetchModelInfo = async () => {
  if (isCreate.value) {
    return
  }
  const loading = ElLoading.service({
    fullscreen: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  const [error, result] = await (id.value
    ? viewModel(+id.value)
    : viewModelByVersion(+versionId.value!))
  loading.close()
  if (error === null && result?.datas) {
    formData.value = result.datas
    formData.value.thumbnail = result.datas.thumbnail
      ? [formData.value.thumbnail as BaseFileDTO]
      : []
    if (isCopy.value) {
      // 复制时，清空图片信息
      formData.value.thumbnail = undefined
      formData.value.reference = []
      formData.value.lastId = []
      lastReferencedList.value = []
      // 清空关联大底
      formData.value.heelIdItemName = ''
      formData.value.heelId = undefined
    }
    if (result.datas.lastId && !isCopy.value) {
      await handleQueryLastInfo(result.datas.lastId)
    }
  }
}

const submitLoading = ref(false)
const router = useRouter()
async function handleConfirm() {
  if (isView.value) {
    router.push({
      name: 'EditModel',
      query: {
        id: id.value
      }
    })
    return
  }
  await handleSubmit()
}

const handleSubmit = () => {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      const [error, result] = await (isEdit.value
        ? editModel(submitParams.value)
        : createModel(submitParams.value))
      submitLoading.value = false
      if (error === null && result) {
        ElMessage.success(result.msg || '提交成功')
        useClosePage('ModelLibrary')
      }
    }
  })
}

const handleClose = () => {
  useClosePage('ModelLibrary')
}
const handleEditModel = () => {
  const status = formData.value.status
  if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
    ElMessage.warning('审批中/禁用数据不允许修改')
    return
  }
  router.push({
    name: 'EditModel',
    query: {
      id: formData.value.id
    }
  })
}
fetchModelInfo()
onActivated(() => {
  if (isView.value) {
    fetchModelInfo()
  }
})
</script>

<template>
  <HeelInfoDialog
    v-model="heelInfoDialogVisible"
    :type="currentHeelBottomType"
    @submit="handlePickHeel"
  />
  <LastInfoDialog v-model="lastInfoDialogVisible" multiple @submit="handlePickLastInfo" />
  <ContentWrap class="info-wrapper">
    <ElCollapse v-model="activeNames">
      <ElForm
        ref="formRef"
        :disabled="isView"
        :model="formData"
        :rules="formRules"
        label-width="auto"
      >
        <ElCollapseItem :name="1">
          <template #title>
            <div class="font-bold text-base">基础信息</div>
          </template>
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="型体缩略图" prop="thumbnail">
                <OssUpload
                  :limit="1"
                  :model-value="(formData.thumbnail) as BaseFileDTO[]"
                  :size-limit="1024 * 1024 * 10"
                  accept="image/*"
                  drag
                  listType="picture-card"
                  @update:model-value="(val) => (formData.thumbnail = val)"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="参考图片">
                <OssUpload
                  v-model="formData.reference"
                  :limit="10"
                  :size-limit="1024 * 1024 * 100"
                  accept="image/*"
                  drag
                  listType="picture-card"
                  multiple
                />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isEdit || isView" :span="24">
              <ElFormItem label="型体编号" prop="code">
                <ElInput v-model="formData.code" :disabled="isEdit" class="w-48" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="开发年份" prop="developmentYear">
                <SelectPlus
                  v-model="formData.developmentYear"
                  :disabled="isEdit"
                  api-key="COMMON_DEV_YEAR"
                  clearable
                  filterable
                  placeholder="请选择开发年份"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="供应商" prop="assignedFactory">
                <SelectPlus
                  v-model="formData.assignedFactory"
                  api-key="getSupplierList"
                  clearable
                  filterable
                  placeholder="请选择供应商"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="适用人群" prop="targetAudience">
                <SelectPlus
                  v-model="formData.targetAudience"
                  :disabled="isEdit"
                  api-key="PRODUCT_PEOPLE"
                  clearable
                  filterable
                  placeholder="请选择适用人群"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="头型" prop="head">
                <SelectPlus
                  v-model="formData.head"
                  api-key="HEEL_HEAD"
                  clearable
                  filterable
                  placeholder="请选择头型"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="区域" prop="region">
                <SelectPlus
                  v-model="formData.region"
                  api-key="COMMON_REGION"
                  clearable
                  filterable
                  placeholder="请选择区域"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="跟型" prop="pattern">
                <SelectPlus
                  v-model="formData.pattern"
                  api-key="HEEL_PATTERN"
                  clearable
                  filterable
                  placeholder="请选择跟型"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="品牌" prop="brand">
                <SelectPlus
                  v-model="formData.brand"
                  :disabled="isEdit"
                  api-key="baseBrand"
                  clearable
                  filterable
                  placeholder="请选择品牌"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="跟高" prop="high">
                <SelectPlus
                  v-model="formData.high"
                  api-key="HEEL_HIGH"
                  clearable
                  filterable
                  placeholder="请选择跟高"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="关联大底" prop="heelId">
                <ElInput
                  v-model="formData.heelIdItemName"
                  class="w-48"
                  placeholder="请选择"
                  readonly
                >
                  <template #prefix>
                    <Icon
                      :size="26"
                      class="cursor-pointer"
                      color="#409EFF"
                      icon="mdi:search"
                      @click="
                        handleOpenHeelInfoDialog([HeelBottomTypeEnum.OS, HeelBottomTypeEnum.FB])
                      "
                    />
                  </template>
                </ElInput>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="跟高(mm)" prop="heelHeightMm">
                <ElInputNumber
                  v-model="formData.heelHigh"
                  :controls="false"
                  :min="0"
                  :precision="2"
                  :value-on-clear="null"
                  class="min-w-48 number-input"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="备注" prop="remark">
                <ElInput
                  v-model="formData.remark"
                  :autosize="{ minRows: 4, maxRows: 4 }"
                  :resize="isView ? 'none' : undefined"
                  class="w-48"
                  maxlength="500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCollapseItem>
        <ElCollapseItem :name="2">
          <template #title>
            <div class="font-bold text-base">关联楦型信息</div>
          </template>
          <ElButton
            v-if="!isView"
            class="mb-2"
            text
            type="primary"
            @click="handleOpenLastInfoDialog"
          >
            搜索楦型信息
          </ElButton>
          <VxeTable
            ref="lastTableRef"
            :cell-config="{ height: 80 }"
            :data="lastReferencedList"
            :max-height="500"
          >
            <VxeColumn title="序号" type="seq" />
            <VxeColumn field="code" title="楦型编码" />
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="thumbnail"
              title="缩略图"
              width="100"
            />
            <VxeColumn field="developmentSeasonCodeItemName" title="开发季节" />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.brandMap } }"
              field="brand"
              title="品牌"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.lastSexMap } }"
              field="sexCode"
              title="性别"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.lastTypeMap } }"
              field="typeCode"
              title="楦头类别"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.lastMarketMap } }"
              field="marketCode"
              title="楦头市场"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.lastHeightMap } }"
              field="heightCode"
              title="楦头帮高"
            />
            <VxeColumn field="productName" title="关联的产品信息" />
            <VxeColumn v-if="!isView" :show-overflow="false" title="操作">
              <template #default="{ rowIndex }">
                <ElButton text type="primary" @click="handleDeleteLastInfo(rowIndex)">
                  删除
                </ElButton>
              </template>
            </VxeColumn>
          </VxeTable>
        </ElCollapseItem>
        <ElCollapseItem v-if="isView" :name="3">
          <template #title>
            <div class="font-bold text-base">关联产品信息</div>
          </template>
          <VxeTable :cell-config="{ height: 80 }" :data="formData.productInfo" :max-height="500">
            <VxeColumn title="序号" type="seq" />
            <VxeColumn field="productNumber" title="产品编码" />
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="thumbnail"
              title="产品缩略图"
              width="100"
            />
            <VxeColumn field="targetAudience" title="适用人群" />
            <VxeColumn field="productCategory" title="产品分类" />
            <VxeColumn field="brandItemName" title="品牌" />
            <VxeColumn field="launchSeasonItemName" title="开发季节" />
            <VxeColumn field="meetingResultItemName" title="选品会结果" />
            <VxeColumn field="dataStatusItemName" title="状态" />
            <VxeColumn v-if="!isView" :show-overflow="false" title="操作">
              <template #default="{ rowIndex }">
                <ElButton text type="primary" @click="handleDeleteLastInfo(rowIndex)">
                  删除
                </ElButton>
              </template>
            </VxeColumn>
          </VxeTable>
        </ElCollapseItem>
      </ElForm>
    </ElCollapse>
  </ContentWrap>
  <ContentWrap class="mt-2">
    <div class="text-center">
      <ElButton @click="handleClose">返回</ElButton>
      <ElButton v-if="!isView" :loading="submitLoading" type="primary" @click="handleSubmit">
        确定
      </ElButton>
      <ElButton
        v-if="
          formData.status !== StatusEnum.APPROVING && formData.status !== StatusEnum.BAN && isView
        "
        v-hasPermi="['editModel']"
        type="primary"
        @click="handleEditModel"
      >
        <Icon size="20" icon="ep:edit" />
        修改型体
      </ElButton>
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
.info-wrapper {
  max-height: calc(100vh - 250px);
  overflow: auto;
}

.number-input {
  :deep(.el-input__wrapper) {
    padding-left: 12px;
  }

  :deep(.el-input__inner) {
    text-align: left;
  }
}

:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}
</style>
