import to from 'await-to-js'
import { service } from '@/config/fetch/service'

export namespace ModelInfoApi {
  export type ModelDetailResp = {
    /**
     * 供应商
     */
    assignedFactory?: string
    /**
     * 品牌
     */
    brand?: string
    /**
     * 编码
     */
    code?: string
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    /**
     * 开发年份
     */
    developmentYear?: string
    /**
     * 头型
     */
    head?: string
    /**
     * 跟高(mm)
     */
    heelHigh?: number
    /**
     * 根底id
     */
    heelId?: number
    /**
     * 跟高
     */
    high?: string
    id?: number
    /**
     * 楦型id数组
     */
    lastId?: number[]
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 跟型
     */
    pattern?: string
    /**
     * 关联的产品信息
     */
    productInfo?: ModelProductResp[]
    /**
     * 参考图片
     */
    reference?: BaseFileDTO[]
    /**
     * 区域
     */
    region?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 启用状态（草稿，禁用，启用）
     * 启用状态
     */
    status?: string
    /**
     * 适用人群
     */
    targetAudience?: string
    /**
     * 型体缩略图
     */
    thumbnail?: BaseFileDTO[] | BaseFileDTO
    [property: string]: any
  }

  export type ModelProductResp = {
    /**
     * 品牌
     */
    brand?: number
    /**
     * 数据状态
     */
    dataStatus?: string
    id?: number
    /**
     * 上市季
     */
    launchSeason?: string
    /**
     * 选品会结果
     */
    meetingResult?: string
    /**
     * 产品分类
     */
    productCategory?: string
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 适用人群
     */
    targetAudience?: string
    /**
     * 缩略图
     */
    thumbnail?: BaseFileDTO
    [property: string]: any
  }

  export type Response = ResponseData<ModelDetailResp>
}

export function viewModel(id: number) {
  return to<ModelInfoApi.Response>(service.get(`/pdm-base/model/detail/${id}`))
}

export function viewModelByVersion(versionId: number) {
  return to<ModelInfoApi.Response>(service.get(`/pdm-base/model/versionDetail/${versionId}`))
}

export namespace CreateModelAPI {
  export type Params = ModelInfoApi.ModelDetailResp

  export type Request = Params
  export type Response = BasicResponseData
}

export function createModel(data: CreateModelAPI.Request) {
  return to<CreateModelAPI.Response>(service.post('/pdm-base/model/save', data))
}

export namespace EditModelAPI {
  export type Params = ModelInfoApi.ModelDetailResp

  export type Request = Params
  export type Response = BasicResponseData
}

export function editModel(data: EditModelAPI.Request) {
  return to<EditModelAPI.Response>(service.post('/pdm-base/model/update', data))
}
