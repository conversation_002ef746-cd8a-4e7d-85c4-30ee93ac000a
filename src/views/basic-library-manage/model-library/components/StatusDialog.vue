<script lang="ts" setup>
import { ModelListPageAPI } from '../api/model-list'
import { statusConst, StatusEnum } from '../../const'
import { changeStatus } from '../api/statusDialog'
import { UpdateStatusAPI } from '../../api/common'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'StatusDialog'
})

const props = defineProps<{
  modelValue: boolean
  selectedRows: ModelListPageAPI.List
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

type TableRow = Partial<ModelListPageAPI.Row & UpdateStatusAPI.StatusData>

const tableData = ref<TableRow[]>([])
watch(visible, (val) => {
  if (val) {
    tableData.value = props.selectedRows.slice()
  }
})

const handleClose = () => {
  visible.value = false
  tableData.value = []
}

const submitLoading = ref(false)
const handleSubmit = async () => {
  const hasError = tableData.value.some((item) => {
    return item.newStatus === undefined || item.newStatus === ''
  })
  if (hasError) {
    ElMessage.warning('请选择状态')
    return
  }
  submitLoading.value = true
  const [error, result] = await changeStatus({
    updateStatusReqList: tableData.value.map((e) => ({
      id: e.id!,
      oldStatus: e.statusCode!,
      newStatus: e.newStatus!
    }))
  })
  submitLoading.value = false
  if (error === null && result) {
    ElMessage.success(result.msg || '操作成功')
    handleClose()
    emit('refresh')
  }
}

// 状态枚举
const statusList = statusConst.statusList

const getStatusList = (row: ModelListPageAPI.Row) => {
  if (row.statusCode === StatusEnum.DRAFT) {
    return statusList.filter((e) => e.value === StatusEnum.START || e.value === StatusEnum.BAN)
  }
  if (row.statusCode === StatusEnum.START) {
    return statusList.filter((e) => e.value === StatusEnum.BAN)
  }
  if (row.statusCode === StatusEnum.BAN) {
    return statusList.filter((e) => e.value === StatusEnum.START)
  }
}
</script>

<template>
  <Dialog v-model="visible" :before-close="handleClose" title="操作确认">
    <VxeTable :data="tableData" :max-height="500">
      <VxeColumn type="seq" title="序号" width="60" />
      <VxeColumn field="code" title="型体编码" />
      <VxeColumn field="brandItemName" title="品牌" />
      <VxeColumn field="developmentYearItemName" title="开发年份" />
      <VxeColumn field="targetAudienceItemName" title="适用人群" />
      <VxeColumn field="regionItemName" title="区域" />
      <VxeColumn field="status" title="当前状态" />
      <VxeColumn title="修改状态">
        <template #default="{ row }">
          <ElSelect v-model="(row as TableRow).newStatus" size="small">
            <ElOption
              v-for="item in getStatusList(row)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
