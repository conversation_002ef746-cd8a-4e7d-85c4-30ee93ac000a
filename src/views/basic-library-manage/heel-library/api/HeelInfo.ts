import to from 'await-to-js'
import { service } from '@/config/axios/service'

/**
 * 根据跟底id获取跟底详情
 */
export namespace HeelInfoAPI {
  export interface QueryHeelPartResp {
    /**
     * 品牌
     */
    brand?: string
    /**
     * 跟底编码
     */
    code?: string
    /**
     * 开发季节code
     */
    developmentSeasonCode?: string
    id?: number
    /**
     * 材质 材料id
     */
    materialId?: number[]
    /**
     * 区域code
     */
    regionCode?: string
    thumbnail?: BaseFileDTO
    /**
     * 缩略图下载地址
     */
    thumbnailDownloadUrl?: string
    /**
     * 跟底分类code
     */
    typeCode?: string
  }
  export interface Data {
    standardSizeId?: number[]
    /**
     * 底厂
     */
    bottomFactory?: string
    /**
     * 品牌
     */
    brand?: string
    /**
     * 设计师
     */
    designer?: string[]
    /**
     * 开发季节
     */
    developmentSeason?: string
    /**
     * 跟底特性说明
     */
    featureDescription?: string
    /**
     * 是否有足弓
     */
    footArch?: boolean
    /**
     * 头型
     */
    head?: string
    /**
     * 跟高(mm)
     */
    heelHigh?: number
    /**
     * 成品底的跟底部件id
     */
    heelPartId?: number[]
    /**
     * 跟底部件信息
     */
    heelPartList?: QueryHeelPartResp[]
    /**
     * 跟高
     */
    high?: string
    id?: number
    /**
     * 鞋垫材料
     */
    insoleMaterialId?: number[]
    /**
     * 楦型id数组
     */
    lastId?: number[]
    /**
     * 材质
     */
    materialId?: number[]
    /**
     * 跟型
     */
    pattern?: string
    /**
     * 详细图片oss地址
     */
    reference?: BaseFileDTO[]
    /**
     * 是否被产品库，模具库引用
     */
    referenced?: boolean
    /**
     * 区域
     */
    region?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 鞋厂
     */
    shoeFactory?: string
    /**
     * 尺码段id
     */
    sizeId?: number[]
    /**
     * 标准码
     */
    standardSizeValue?: string[]
    /**
     * 状态
     */
    status?: string
    /**
     * 状态code
     */
    statusCode?: string
    /**
     * 供应商跟底编号
     */
    supplierHeelNumber?: string
    /**
     * 厚度(mm)
     */
    thickness?: number
    thumbnail?: BaseFileDTO
    /**
     * 跟底分类
     */
    type?: string
  }
  export type Response = ResponseData<Data>
}
export function getHeelInfo(id: number) {
  return to<HeelInfoAPI.Response>(service.get(`/pdm-base/heel/detail/${id}`))
}
/**
 * 根据版本id获取跟底详情
 */
export function getHeelInfoByVersionId(id: number) {
  return to<HeelInfoAPI.Response>(service.get(`/pdm-base/heel/versionDetail/${id}`))
}

/**
 * 创建跟底
 */
export namespace CreateHeelAPI {
  export type Params = Omit<HeelInfoAPI.Data, 'id' | 'status' | 'statusCode'>
  export type Request = Params
  export type Response = BasicResponseData
}
export function createHeel(data: CreateHeelAPI.Request) {
  return to<CreateHeelAPI.Response>(service.post('/pdm-base/heel/save', data))
}

/**
 * 更新跟底
 */
export namespace UpdateHeelAPI {
  export type Params = Omit<HeelInfoAPI.Data, 'status' | 'statusCode'>
  export type Request = Params
  export type Response = BasicResponseData
}
export function updateHeel(data: UpdateHeelAPI.Request) {
  return to<UpdateHeelAPI.Response>(service.post('/pdm-base/heel/update', data))
}
