import to from 'await-to-js'
import { service } from '@/config/axios/service'

export namespace HeelListPageAPI {
  export interface Params {
    /**
     * 品牌
     */
    brand?: string[]
    /**
     * 跟底编码
     */
    code?: string
    /**
     * 页码
     */
    current?: number
    /**
     * 开发季节
     */
    devSeason?: string[]
    /**
     * 结束时间
     */
    endTime?: string
    idList?: number[]
    /**
     * 区域
     */
    region?: string[]
    /**
     * 页面尺寸
     */
    size?: number
    /**
     * 开始时间
     */
    startTime?: string
    /**
     * 状态
     */
    status?: string[]
    /**
     * 供应商跟底编号
     */
    supplierHeelNumber?: string
    /**
     * 跟底分类
     */
    type?: string[]
  }
  export interface Row {
    sizeId?: number[]
    standardSizeId?: number[]
    heelHigh?: number
    /**
     * 底厂
     */
    bottomFactory?: string
    /**
     * 品牌
     */
    brand?: string
    /**
     * 跟底编码
     */
    code?: string
    /**
     * 设计师
     */
    designer?: string[]
    /**
     * 开发季节code
     */
    developmentSeasonCode?: string
    /**
     * 头型code
     */
    headCode?: string
    /**
     * 跟高code
     */
    highCode?: string
    id?: number
    /**
     * 楦型id
     */
    lastId?: number[]
    /**
     * 关联的楦型信息
     */
    lastInfo?: string
    /**
     * 操作时间
     */
    modifyTime?: Date
    /**
     * 操作人
     */
    operator?: string
    /**
     * 跟型code
     */
    patternCode?: string
    /**
     * 区域code
     */
    regionCode?: string
    /**
     * 鞋厂
     */
    shoeFactory?: string
    /**
     * 尺码段
     */
    sizeName?: string
    /**
     * 标准码
     */
    standardSizeValue?: string[]
    /**
     * 状态
     */
    status?: string
    /**
     * 状态code
     */
    statusCode?: string
    /**
     * 供应商跟底编号
     */
    supplierHeelNumber?: string
    thumbnail?: BaseFileDTO
    /**
     * 缩略图下载地址
     */
    thumbnailDownloadUrl?: string
    /**
     * 跟底分类code
     */
    typeCode?: string
  }
  export type List = Row[]
  export type Request = Params & Partial<PageParams>
  export type Response = PagedResponseData<Row>
}

export function getHeelListByPage(params: HeelListPageAPI.Request, signal?: AbortSignal) {
  return to<HeelListPageAPI.Response>(service.get('/pdm-base/heel/page', { params, signal }))
}
