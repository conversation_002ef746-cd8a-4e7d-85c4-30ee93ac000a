<script lang="ts" setup>
import { getHeelListByPage, HeelListPageAPI } from './api/heel-list'
import { ElCollapseTransition, ElMessage, ElPagination, FormInstance } from 'element-plus'
import { useBasicLibraryDictStore } from '../store/dict'
import { HeelBottomTypeEnum, statusConst, StatusEnum } from '../const'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { Pager } from '../api/common'
import { ref } from 'vue'
import { VxeColumn, VxeTableInstance } from 'vxe-table'
import dayjs from 'dayjs'
import { isEqual } from 'lodash-es'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import StatusDialog from './components/StatusDialog.vue'
import VersionDialog from './components/VersionDialog.vue'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { SelectPlus } from '@/components/Business/SelectPlus'
import { watchDebounced } from '@vueuse/core'
import { SizeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import { sizeList } from '@/components/Business/SelectPlus/src/api'
import { formatRowSizeText } from '@/views/basic-library-manage/mold-library/mold-utils'

defineOptions({
  name: 'HeelLibrary'
})

const props = defineProps<{
  isEmbed?: boolean
  initType?: HeelBottomTypeEnum[]
}>()

const router = useRouter()

const useConst = () => {
  const dictStore = useBasicLibraryDictStore()
  const store = computed(() => ({
    brandMap: dictStore.brandMap,
    commonRegionList: dictStore.commonRegionList,
    commonRegionMap: dictStore.commonRegionMap,
    commonSeasonMap: dictStore.commonDevSeasonMap,
    commonSeasonList: dictStore.commonDevSeasonList,
    heelBottomTypeList: dictStore.heelBottomTypeList,
    heelBottomTypeMap: dictStore.heelBottomTypeMap
  }))

  // 状态枚举
  const statusMap = statusConst.statusMap
  const statusList = statusConst.statusList

  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '130'
    },
    {
      formLabelLength: '180'
    }
  ])

  return {
    statusList,
    store,
    statusMap,
    formLabelLength
  }
}

const { store, statusList, statusMap, formLabelLength } = useConst()

const sizeOptions = ref<SizeListAPI.Data[]>([])

async function querySizeList() {
  const { datas } = await sizeList()
  if (datas) {
    sizeOptions.value = datas
  }
}

const useQuery = () => {
  type FormModel = Omit<HeelListPageAPI.Request, keyof PageParams> & {
    date?: [string, string]
  }

  const defaultFormData: FormModel = {
    brand: [],
    code: '',
    devSeason: [],
    date: ['', ''],
    region: [],
    status: props.isEmbed ? [StatusEnum.START] : [],
    supplierHeelNumber: '',
    type: props.isEmbed ? props.initType : []
  }
  let lastFormData = {
    ...defaultFormData
  }
  const formData = ref<FormModel>({
    ...defaultFormData
  })
  const formRef = ref<FormInstance>()
  const tableRef = ref<VxeTableInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const tableData = ref<HeelListPageAPI.List>([])
  const queryLoading = ref(false)
  const pager = ref<Pager>({
    current: 1,
    size: 10,
    total: 0
  })
  const queryParams = computed<HeelListPageAPI.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1],
      date: undefined
    }
  })
  const defaultTime: [Date, Date] = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ]

  let controller: AbortController | null = null
  const handleQuery = async () => {
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await getHeelListByPage(queryParams.value, controller.signal)
    queryLoading.value = false
    if (error === null && result?.datas) {
      lastFormData = { ...formData.value }
      const { records } = result.datas
      tableData.value = records.map((row) => {
        return {
          ...row,
          sizeValueIdText: formatRowSizeText(row, sizeOptions.value)
        }
      })
      pager.value.total = result.datas.pager.total
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
    handleQuery()
  }

  onActivated(handleQuery)

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = () => {
    const selected: HeelListPageAPI.List | undefined = tableRef.value?.getCheckboxRecords()
    let reqParam: string
    if (selected && selected?.length > 0) {
      const idList = selected.map((item) => item.id)
      reqParam = JSON.stringify({ idList })
    } else {
      reqParam = JSON.stringify(queryParams.value)
    }
    exportFn({
      exportType: 'heel-export',
      reqParam
    })
  }

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef,
    offsetBottom: props.isEmbed ? 50 + 20 + 32 + 10 + 30 : 0
  })

  return {
    formRef,
    tableRef,
    pagerRef,
    formData,
    tableData,
    queryLoading,
    pager,
    defaultTime,
    handleQuery,
    handleReset,
    handleExport,
    exportLoading,
    visible,
    setVisible,
    maxHeight
  }
}

const {
  formRef,
  tableRef,
  pagerRef,
  formData,
  tableData,
  queryLoading,
  pager,
  defaultTime,
  handleQuery,
  handleReset,
  handleExport,
  exportLoading,
  visible,
  setVisible,
  maxHeight
} = useQuery()

// 版本记录弹窗
const useVersionDialog = () => {
  const versionDialogVisible = ref(false)
  const setVersionDialogVisible = () => {
    versionDialogVisible.value = !unref(versionDialogVisible)
  }
  return {
    versionDialogVisible,
    setVersionDialogVisible
  }
}

// 启用/禁用弹窗
const useStatusDialog = () => {
  const statusDialogVisible = ref(false)
  const setStatusDialogVisible = () => {
    statusDialogVisible.value = !unref(statusDialogVisible)
  }
  return {
    statusDialogVisible,
    setStatusDialogVisible
  }
}

const useOperation = () => {
  const currentRow = ref<HeelListPageAPI.Row>()
  const selectedRows = ref<HeelListPageAPI.List>([])

  // 修改跟底
  const handleEditHeel = () => {
    const selectedRows: HeelListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    const status = selectedRows[0].statusCode
    if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
      ElMessage.warning('审批中/禁用数据不允许修改')
      return
    }
    router.push({
      name: 'EditHeel',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 复制跟底
  const handleCopyHeel = () => {
    const selectedRows: HeelListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    router.push({
      name: 'CopyHeel',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 启用/禁用
  const { statusDialogVisible, setStatusDialogVisible } = useStatusDialog()
  const handleChangeStatus = () => {
    const checkedRecords: HeelListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!checkedRecords?.length) {
      ElMessage.warning('请选择数据')
      return
    }
    const hasApproving = checkedRecords.some((item) => item.status === statusMap['approving'])
    if (hasApproving) {
      ElMessage.warning('存在审批中的数据')
      return
    }
    selectedRows.value = checkedRecords
    setStatusDialogVisible()
  }

  // 新增跟底
  const handleCreateHeel = () => {
    router.push({
      name: 'CreateHeel'
    })
  }

  // 版本记录
  const { versionDialogVisible, setVersionDialogVisible } = useVersionDialog()
  const handleOpenVersionDialog = (row: HeelListPageAPI.Row) => {
    currentRow.value = row
    setVersionDialogVisible()
  }

  return {
    currentRow,
    selectedRows,
    handleCopyHeel,
    handleEditHeel,
    handleCreateHeel,
    versionDialogVisible,
    handleOpenVersionDialog,
    handleChangeStatus,
    statusDialogVisible
  }
}

const {
  handleCopyHeel,
  handleEditHeel,
  handleCreateHeel,
  versionDialogVisible,
  handleOpenVersionDialog,
  currentRow,
  selectedRows,
  handleChangeStatus,
  statusDialogVisible
} = useOperation()

defineExpose({
  tableRef
})

onMounted(() => {
  Promise.all([querySizeList(), handleQuery()])
  watchDebounced(
    formData,
    async () => {
      await handleQuery()
    },
    { deep: true, debounce: 400 }
  )
})
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_200px)] overflow-x-hidden overflow-y-auto">
        <ElForm ref="formRef" :label-width="formLabelLength" :model="formData">
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="跟底编码" prop="code">
                <ElInput
                  v-model="formData.code"
                  clearable
                  placeholder="请输入跟底编码，支持模糊查询"
                  @change="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="跟底分类" prop="type">
                <ElSelect
                  v-model="formData.type"
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  multiple
                  placeholder="请选择跟底分类"
                >
                  <ElOption
                    v-for="item in store.heelBottomTypeList"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue!"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="" label-width="0">
                <ElButton text @click="setVisible">
                  {{ visible ? '收起' : '展开' }}
                  <Icon
                    :class="visible ? 'rotate-90' : ''"
                    class="transform transition duration-400"
                    icon="ant-design:down-outlined"
                  />
                </ElButton>
                <ElButton :loading="queryLoading" type="primary" @click="handleQuery">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
                  查询
                </ElButton>
                <ElButton :loading="queryLoading" @click="handleReset">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
                  重置
                </ElButton>
                <ElButton
                  v-if="!isEmbed"
                  :loading="exportLoading"
                  type="primary"
                  @click="handleExport"
                >
                  <Icon v-show="!exportLoading" class="mr-1" icon="ep:upload-filled" />
                  导出
                </ElButton>
              </ElFormItem>
            </ElCol>
            <ElCol :span="16">
              <ElFormItem label="品牌" prop="brand">
                <SelectPlus
                  v-model="formData.brand"
                  api-key="baseBrand"
                  cache
                  checkbox
                  checkbox-button
                  placeholder="请选择品牌"
                />
              </ElFormItem>
            </ElCol>
            <ElCollapseTransition>
              <div v-show="visible" class="flex flex-wrap w-full">
                <ElCol :span="8">
                  <ElFormItem label="状态" prop="status">
                    <ElScrollbar>
                      <ElCheckboxGroup
                        v-model="formData.status"
                        :disabled="isEmbed"
                        class="flex flex-nowrap"
                        @change="handleQuery"
                      >
                        <ElCheckboxButton
                          v-for="item in statusList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </ElCheckboxGroup>
                    </ElScrollbar>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="区域" prop="region">
                    <ElCheckboxGroup
                      v-model="formData.region"
                      class="flex flex-nowrap"
                      @change="handleQuery"
                    >
                      <ElCheckboxButton
                        v-for="item in store.commonRegionList"
                        :key="item.dictValue"
                        :label="item.dictCnName"
                        :value="item.dictValue"
                      />
                    </ElCheckboxGroup>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="开发季节" prop="devSeason">
                    <ElSelect
                      v-model="formData.devSeason"
                      class="flex flex-nowrap"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      multiple
                      placeholder="请选择开发季节"
                    >
                      <ElOption
                        v-for="item in store.commonSeasonList"
                        :key="item.dictValue"
                        :label="item.dictValue"
                        :value="item.dictValue!"
                      />
                    </ElSelect>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem label="操作时间段" prop="date">
                    <ElDatePicker
                      v-model="formData.date"
                      :default-time="defaultTime"
                      class="max-w-96"
                      clearable
                      end-placeholder="结束时间"
                      range-separator="~"
                      start-placeholder="开始时间"
                      type="daterange"
                      unlink-panels
                      value-format="YYYY-MM-DD"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="供应商跟底编号" prop="supplierHeelNumber">
                    <ElInput
                      v-model="formData.supplierHeelNumber"
                      clearable
                      placeholder="请输入供应商跟底编号, 支持模糊查询"
                      @change="handleQuery"
                    />
                  </ElFormItem>
                </ElCol>
              </div>
            </ElCollapseTransition>
          </ElRow>
        </ElForm>
        <div v-show="!isEmbed" class="mb-[10px] min-h-8">
          <ElButton v-hasPermi="['createHeel']" type="primary" @click="handleCreateHeel">
            <Icon icon="ep:plus" />
            <span class="text-[14px]">新增跟底</span>
          </ElButton>
          <ElButton v-hasPermi="['editHeel']" type="primary" @click="handleEditHeel">
            <Icon icon="ep:edit" />
            <span class="text-[14px]">修改跟底</span>
          </ElButton>
          <ElButton v-hasPermi="['heel:changeStatus']" type="primary" @click="handleChangeStatus">
            <Icon icon="ep:share" />
            <span class="text-[14px]">启用/禁用</span>
          </ElButton>
          <ElButton v-hasPermi="['copyHeel']" type="primary" @click="handleCopyHeel">
            <Icon icon="ep:copy-document" />
            <span class="text-[14px]">复制跟底</span>
          </ElButton>
        </div>
        <div>
          <VxeTable
            ref="tableRef"
            :data="tableData"
            :loading="queryLoading"
            :max-height="maxHeight - 125"
            :show-header-overflow="false"
          >
            <VxeColumn fixed="left" type="checkbox" width="40" />
            <VxeColumn title="序号" fixed="left" type="seq" width="60" />
            <VxeColumn field="code" fixed="left" show-overflow title="跟底编码" width="90">
              <template #default="{ row }: { row: HeelListPageAPI.Row }">
                <router-link :to="{ name: 'ViewHeel', query: { id: row.id } }">
                  <span class="p-0 max-w-full cursor-pointer text-blue-500">
                    {{ row.code }}
                  </span>
                </router-link>
              </template>
            </VxeColumn>
            <VxeColumn
              fixed="left"
              :cell-render="{ name: 'Image' }"
              field="thumbnailDownloadUrl"
              title="缩略图"
              width="80"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.heelBottomTypeMap } }"
              field="typeCode"
              min-width="100"
              title="跟底分类"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.brandMap } }"
              field="brand"
              min-width="100"
              title="品牌"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.commonSeasonMap } }"
              field="developmentSeasonCode"
              title="开发季节"
              width="80"
            />
            <VxeColumn field="designerItemName" min-width="100" title="设计师" />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.commonRegionMap } }"
              field="regionCode"
              min-width="100"
              title="区域"
            />
            <VxeColumn field="lastInfo" min-width="120" title="关联的楦型信息" />
            <VxeColumn field="supplierHeelNumber" min-width="120" title="供应商跟底编码" />
            <VxeColumn field="shoeFactory" min-width="100" title="鞋厂" />
            <VxeColumn field="bottomFactory" min-width="120" title="底厂" />
            <VxeColumn
              :cell-render="{ name: 'Ellipsis', props: { maxRow: 2, separator: ',' } }"
              field="sizeName"
              min-width="100"
              title="尺码段"
            />
            <VxeColumn
              :cell-render="{ name: 'Ellipsis', props: { maxRow: 2, separator: '、' } }"
              field="sizeValueIdText"
              title="样品码"
              width="80"
            />
            <VxeColumn field="status" fixed="right" title="状态" width="80" />
            <VxeColumn field="operator" title="操作人" width="120" />
            <VxeColumn field="modifyTime" title="操作时间" width="80" />
            <VxeColumn :show-overflow="false" fixed="right" title="操作" width="120">
              <template #default="{ row }">
                <ElButton
                  v-if="!isEmbed"
                  size="small"
                  text
                  type="primary"
                  @click="handleOpenVersionDialog(row)"
                >
                  版本记录
                </ElButton>
              </template>
            </VxeColumn>
          </VxeTable>
        </div>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
      <VersionDialog v-model="versionDialogVisible" :current-row="currentRow" />
      <StatusDialog
        v-model="statusDialogVisible"
        :selected-rows="selectedRows"
        @refresh="handleQuery"
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
:deep(.el-checkbox-button.is-checked .el-checkbox-button__inner) {
  color: var(--el-checkbox-button-checked-text-color);
  background-color: var(--el-checkbox-button-checked-bg-color);
  border-color: var(--el-checkbox-button-checked-border-color);
  box-shadow: -1px 0 0 0 var(--el-color-primary-light-7);
}
</style>
