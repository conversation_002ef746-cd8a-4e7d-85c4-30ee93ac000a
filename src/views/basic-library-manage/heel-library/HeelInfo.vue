<script lang="ts" setup>
import { useBasicLibraryDictStore } from '../store/dict'
import { HeelBottomTypeEnum, statusConst, StatusEnum } from '../const'
import {
  CascaderInstance,
  type CascaderProps,
  ElLoading,
  ElMessage,
  FormInstance,
  FormRules
} from 'element-plus'
import {
  createHeel,
  getHeelInfo,
  getHeelInfoByVersionId,
  HeelInfoAPI,
  updateHeel
} from './api/HeelInfo'
import { UPLOAD_URL } from '../api/upload'
import BaseUpload from '@/components/Upload/BaseUpload.vue'
import { ContentWrap } from '@/components/ContentWrap'
import { getLastReferencedListByIdList, LastReferencedListAPI } from '../api/common'
import LastInfoDialog from '../components/LastInfoDialog.vue'
import { getHeelListByPage, HeelListPageAPI } from './api/heel-list'
import { VxeColumn, VxeTableInstance } from 'vxe-table'
import HeelInfoDialog from '../components/HeelInfoDialog.vue'
import { useMaterial } from '@/views/basic-library-manage/sample-manage/components/hooks'
import ElCascader from '@/components/Cascader/src/Cascader'
import { useSizeOptions } from '@/utils/useSizeOptions'
import { SizeCodeTypeEnums } from '@/enums'
import { groupBy } from 'lodash-es'
import { Icon } from '@/components/Icon'

defineOptions({
  name: 'HeelInfo'
})

const activeNames = ref(['1', '2', '3'])

const route = useRoute()

const id = computed(() => {
  return route.query.id
})

const versionId = computed(() => {
  return route.query.versionId
})

const useType = () => {
  const isCreate = computed(() => {
    return route.name === 'CreateHeel'
  })

  const isEdit = computed(() => {
    return route.name === 'EditHeel'
  })

  const isView = computed(() => {
    return route.name === 'ViewHeel'
  })

  const isCopy = computed(() => {
    return route.name === 'CopyHeel'
  })

  return {
    isCreate,
    isEdit,
    isView,
    isCopy
  }
}

const { isCreate, isEdit, isView, isCopy } = useType()

const useConst = () => {
  const dictStore = useBasicLibraryDictStore()
  const store = computed(() => ({
    brandMap: dictStore.brandMap,
    lastSexMap: dictStore.lastSexMap,
    lastTypeMap: dictStore.lastTypeMap,
    lastMarketMap: dictStore.lastMarketMap,
    lastHeightMap: dictStore.lastHeightMap,
    lastHeadList: dictStore.heelHeadList,
    heelBottomTypeList: dictStore.heelBottomTypeList,
    commonSeasonList: dictStore.commonDevSeasonList,
    commonRegionList: dictStore.commonRegionList,
    commonRegionMap: dictStore.commonRegionMap
  }))

  const statusList = statusConst.statusList.filter((e) => e.value !== StatusEnum.APPROVING)

  // 材料分类级联
  const { materialCategoryList } = useMaterial()

  const cascaderProps: CascaderProps = {
    label: 'selectorValue',
    value: 'selectorKey',
    children: 'childList',
    expandTrigger: 'hover' as const,
    emitPath: false,
    multiple: true
  }

  return {
    statusList,
    materialCategoryList,
    cascaderProps,
    store
  }
}

const { statusList, materialCategoryList, cascaderProps, store } = useConst()

const useQueryInfo = () => {
  const formRef = ref<FormInstance>()
  const formData = ref<HeelInfoAPI.Data>({
    thumbnail: undefined,
    reference: [],
    brand: '',
    type: '',
    developmentSeason: '',
    region: '',
    designer: [],
    remark: '',
    sizeId: undefined,
    standardSizeValue: [],
    thickness: undefined,
    heelHigh: undefined,
    bottomFactory: '',
    materialId: [],
    insoleMaterialId: [],
    shoeFactory: '',
    footArch: undefined,
    featureDescription: ''
  })
  const formRules = ref<FormRules<HeelInfoAPI.Data>>({
    thumbnail: [
      {
        required: true,
        message: '请上传跟底缩略图'
      }
    ],
    brand: [
      {
        required: true,
        message: '请选择品牌',
        trigger: 'change'
      }
    ],
    type: [
      {
        required: true,
        message: '请选择跟底分类',
        trigger: 'change'
      }
    ],
    developmentSeason: [
      {
        required: true,
        message: '请选择开发季节',
        trigger: 'change'
      }
    ],
    region: [
      {
        required: true,
        message: '请选择区域',
        trigger: 'change'
      }
    ],
    designer: [
      {
        required: true,
        message: '请选择设计师',
        trigger: 'change'
      }
    ],
    standardSizeId: [
      {
        required: true,
        message: '请选择尺码段',
        trigger: 'change'
      }
    ]
  })

  const sizeIdRef = ref<CascaderInstance>()
  const sizeValue = ref('')
  const { sizeOptions, fetchSizeList } = useSizeOptions(SizeCodeTypeEnums.PRODUCT)
  const handleSizeIdChange = (value?: number[]) => {
    if (!value?.length) {
      sizeValue.value = ''
      return
    }
    const nodes = sizeIdRef.value?.getCheckedNodes(true)
    const sizeValues = groupBy(
      nodes?.map((node) => {
        const [name, value] = node.pathLabels
        return {
          name,
          value
        }
      }),
      'name'
    )
    sizeValue.value = Object.keys(sizeValues)
      .map((key) => {
        const values = sizeValues[key]
        return `${values.map((e) => e.value).join(', ')} 【${key}】`
      })
      .join('、')
  }

  const lastReferencedList = ref<LastReferencedListAPI.Data[]>([])
  const heelPartList = ref<HeelListPageAPI.List>([])

  const fetchHeelInfo = async () => {
    if (isCreate.value) {
      return
    }
    const loading = ElLoading.service({
      fullscreen: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    const [error, result] = await (id.value
      ? getHeelInfo(+id.value)
      : getHeelInfoByVersionId(+versionId.value!))
    loading.close()
    if (error === null && result?.datas) {
      formData.value = result.datas
      if (isCopy.value) {
        // 复制时，清空图片信息
        formData.value.thumbnail = undefined
        formData.value.reference = []
      }
      if (result.datas.lastId && !isCopy.value) {
        const [referenceError, referenceResult] = await getLastReferencedListByIdList(
          result.datas.lastId
        )
        if (referenceError === null && referenceResult?.datas) {
          lastReferencedList.value = referenceResult.datas
        }
      }
      if (result.datas.heelPartId) {
        const [heelPartError, heelPartResult] = await getHeelListByPage({
          idList: result.datas.heelPartId
        })
        if (heelPartError === null && heelPartResult?.datas) {
          heelPartList.value = heelPartResult.datas.records || []
        }
      }
    }
  }

  const formCouldEdit = computed(() => {
    return (
      isCreate.value ||
      isCopy.value ||
      formData.value.statusCode === StatusEnum.DRAFT ||
      (formData.value.statusCode === StatusEnum.START && !formData.value.referenced)
    )
  })

  const isFinishedBottom = computed(() => {
    return formData.value.type === HeelBottomTypeEnum.FB
  })

  return {
    formRef,
    formData,
    formRules,
    sizeIdRef,
    sizeValue,
    sizeOptions,
    fetchSizeList,
    handleSizeIdChange,
    fetchHeelInfo,
    formCouldEdit,
    heelPartList,
    lastReferencedList,
    isFinishedBottom
  }
}

const {
  formRef,
  formData,
  formRules,
  sizeValue,
  sizeIdRef,
  sizeOptions,
  fetchSizeList,
  handleSizeIdChange,
  fetchHeelInfo,
  formCouldEdit,
  heelPartList,
  lastReferencedList,
  isFinishedBottom
} = useQueryInfo()

onActivated(() => {
  if (isView.value) {
    fetchHeelInfo()
  }
})

Promise.all([fetchHeelInfo(), fetchSizeList()]).then(() => {
  handleSizeIdChange(formData.value.standardSizeId)
})

const router = useRouter()
const useOperation = () => {
  const lastInfoDialogVisible = ref(false)
  const handleOpenLastInfoDialog = () => {
    lastInfoDialogVisible.value = true
  }
  const handlePickLastInfo = (selectedRows: LastReferencedListAPI.Data[]) => {
    if (!formData.value.lastId) {
      formData.value.lastId = []
    }
    lastReferencedList.value = lastReferencedList.value.concat(
      selectedRows.filter((e) => !formData.value.lastId?.includes(e.id!))
    )
    formData.value.lastId = [
      ...new Set(formData.value.lastId.concat(selectedRows.map((e) => e.id!)) || [])
    ]
  }
  const lastTableRef = ref<VxeTableInstance | null>(null)
  const handleDeleteLastInfo = (index: number) => {
    formData.value.lastId?.splice(index, 1)
    lastReferencedList.value.splice(index, 1)
    lastTableRef.value?.loadData(lastReferencedList.value)
  }

  const heelTableRef = ref<VxeTableInstance | null>(null)
  const heelInfoDialogVisible = ref(false)
  const handleOpenHeelInfoDialog = () => {
    heelInfoDialogVisible.value = true
  }
  const handlePickHeelInfo = (selectedRows: HeelListPageAPI.List) => {
    if (!formData.value.heelPartId) {
      formData.value.heelPartId = []
    }
    heelPartList.value = heelPartList.value.concat(
      selectedRows.filter((e) => !formData.value.heelPartId?.includes(e.id!))
    )
    formData.value.heelPartId = [
      ...new Set(formData.value.heelPartId.concat(selectedRows.map((e) => e.id!)))
    ]
    heelTableRef.value?.loadData(heelPartList.value)
    heelInfoDialogVisible.value = false
  }
  const handleDeleteHeelInfo = (index: number) => {
    if (formData.value.referenced) {
      ElMessage.warning('此数据已被引用，无法删除')
      return
    }
    formData.value.heelPartId?.splice(index, 1)
    heelPartList.value.splice(index, 1)
    heelTableRef.value?.loadData(heelPartList.value)
  }

  const submitLoading = ref(false)
  const submitFn = computed(() => {
    if (isCreate.value || isCopy.value) {
      return createHeel
    }
    if (isEdit.value) {
      return updateHeel
    }
    return createHeel
  })

  async function handleConfirm() {
    if (isView.value) {
      router.push({
        name: 'EditHeel',
        query: {
          id: id.value
        }
      })
      return
    }
    await handleSubmit()
  }

  const handleSubmit = async () => {
    if (isEdit.value) {
      formData.value.id = Number(id.value)
    }
    const valid = await formRef.value?.validate()
    if (valid) {
      submitLoading.value = true
      const [error, result] = await submitFn.value(formData.value)
      submitLoading.value = false
      if (error === null && result) {
        useClosePage('HeelLibrary')
        ElMessage.success(result.msg || '保存成功')
      } else {
        ElMessage.error(error?.message || '保存失败')
      }
    }
  }
  const handleClose = () => {
    useClosePage('HeelLibrary')
  }
  return {
    submitLoading,
    handleClose,
    handleConfirm,
    heelTableRef,
    handlePickHeelInfo,
    handleDeleteHeelInfo,
    heelInfoDialogVisible,
    handleOpenHeelInfoDialog,
    lastTableRef,
    handlePickLastInfo,
    handleDeleteLastInfo,
    lastInfoDialogVisible,
    handleOpenLastInfoDialog
  }
}
const handleEditHeel = () => {
  const status = formData.value.statusCode
  if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
    ElMessage.warning('审批中/禁用数据不允许修改')
    return
  }
  router.push({
    name: 'EditHeel',
    query: {
      id: formData.value.id
    }
  })
}
const {
  submitLoading,
  handleClose,
  handleConfirm,
  heelTableRef,
  handlePickHeelInfo,
  handleDeleteHeelInfo,
  heelInfoDialogVisible,
  handleOpenHeelInfoDialog,
  lastTableRef,
  handlePickLastInfo,
  handleDeleteLastInfo,
  lastInfoDialogVisible,
  handleOpenLastInfoDialog
} = useOperation()
</script>

<template>
  <HeelInfoDialog v-model="heelInfoDialogVisible" multiple @submit="handlePickHeelInfo" />
  <LastInfoDialog v-model="lastInfoDialogVisible" multiple @submit="handlePickLastInfo" />
  <ContentWrap class="info-wrapper">
    <ElCollapse v-model="activeNames">
      <ElForm
        ref="formRef"
        :disabled="isView"
        :model="formData"
        :rules="formRules"
        :scroll-into-view-options="{ behavior: 'smooth' }"
        labelWidth="auto"
        scroll-to-error
      >
        <ElCollapseItem name="1" title="基础信息">
          <template #title>
            <div class="font-bold text-base">基础信息</div>
          </template>
          <div class="grid items-start grid-cols-2">
            <ElFormItem label="缩略图" prop="thumbnail">
              <BaseUpload
                :action="UPLOAD_URL"
                :limit="1"
                :modelValue="formData.thumbnail ? [formData.thumbnail] : []"
                :size-limit="1024 * 1024 * 10"
                accept="image/*"
                drag
                multiple
                name="files"
                @update:model-value="(val) => (formData.thumbnail = val[0])"
              />
            </ElFormItem>
            <ElFormItem label="详细图片" prop="reference">
              <BaseUpload
                v-model="formData.reference"
                :action="UPLOAD_URL"
                :limit="20"
                :size-limit="1024 * 1024 * 100"
                accept="image/*"
                drag
                multiple
                name="files"
              />
            </ElFormItem>
            <ElFormItem label="品牌" prop="brand">
              <SelectPlus
                v-model="formData.brand"
                :disabled="!formCouldEdit"
                api-key="baseBrand"
                cache
                clearable
                filterable
                placeholder="请选择品牌"
              />
            </ElFormItem>
            <ElFormItem label="跟底分类" prop="type">
              <ElSelect
                v-model="formData.type"
                :disabled="!formCouldEdit"
                clearable
                filterable
                placeholder="请选择跟底分类"
              >
                <ElOption
                  v-for="item in store.heelBottomTypeList"
                  :key="item.dictValue"
                  :label="item.dictCnName"
                  :value="item.dictValue!"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="开发季节" prop="developmentSeason">
              <ElSelect
                v-model="formData.developmentSeason"
                :disabled="!formCouldEdit"
                clearable
                filterable
                placeholder="请选择开发季节"
              >
                <ElOption
                  v-for="item in store.commonSeasonList"
                  :key="item.dictValue"
                  :label="item.dictCnName"
                  :value="item.dictValue!"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="区域" prop="region">
              <ElSelect v-model="formData.region" clearable filterable placeholder="请选择区域">
                <ElOption
                  v-for="item in store.commonRegionList"
                  :key="item.dictValue"
                  :label="item.dictCnName"
                  :value="item.dictValue!"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="备注" prop="remark">
              <ElInput
                v-model="formData.remark"
                :autosize="{ minRows: 4, maxRows: 4 }"
                class="w-48"
                maxlength="500"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
            <ElFormItem label="设计师" prop="designer">
              <CascadeSelector
                v-model="formData.designer"
                :props="{ multiple: true, emitPath: false }"
                api-key="allUsers"
                cache
                clearable
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择"
              />
            </ElFormItem>
            <ElFormItem v-if="isView" label="状态" prop="statusCode">
              <ElRadioGroup v-model="formData.statusCode">
                <ElRadio v-for="e in statusList" :key="e.value" :label="e.label" :value="e.value" />
              </ElRadioGroup>
            </ElFormItem>
            <ElFormItem
              v-if="isFinishedBottom"
              class="col-span-2"
              label-width="0"
              prop="heelPartId"
            >
              <ElButton
                v-if="!isView"
                class="mb-2"
                text
                type="primary"
                @click="handleOpenHeelInfoDialog"
              >
                选择跟底部件信息
              </ElButton>
              <VxeTable
                ref="heelTableRef"
                :cell-config="{ height: 80 }"
                :data="heelPartList"
                :max-height="500"
                class="w-full"
              >
                <VxeColumn title="序号" type="seq" width="60" />
                <VxeColumn field="code" title="跟底编码" />
                <VxeColumn field="typeCodeItemName" min-width="100" title="部件分类" />
                <VxeColumn
                  :cell-render="{ name: 'Image' }"
                  field="thumbnailDownloadUrl"
                  title="缩略图"
                  width="80"
                />
                <VxeColumn
                  :cell-render="{ name: 'Dict', props: { dictMap: store.brandMap } }"
                  field="brand"
                  min-width="100"
                  title="品牌"
                />
                <VxeColumn field="developmentSeasonCodeItemName" title="开发季节" />
                <VxeColumn field="regionCodeItemName" min-width="100" title="区域" />
                <VxeColumn :show-overflow="false" title="操作">
                  <template #default="{ rowIndex }">
                    <ElButton text type="primary" @click="handleDeleteHeelInfo(rowIndex)">
                      删除
                    </ElButton>
                  </template>
                </VxeColumn>
              </VxeTable>
            </ElFormItem>
          </div>
        </ElCollapseItem>
        <ElCollapseItem name="2" title="跟底详细信息">
          <template #title>
            <div class="font-bold text-base">跟底详细信息</div>
          </template>
          <div class="grid items-start grid-cols-2">
            <ElFormItem label="尺码段" prop="standardSizeId">
              <ElCascader
                ref="sizeIdRef"
                v-model="formData.standardSizeId"
                :options="sizeOptions"
                :props="{
                  value: 'id',
                  emitPath: false,
                  multiple: true
                }"
                clearable
                filterable
                placeholder="请选择尺码段"
                @change="handleSizeIdChange"
              />
            </ElFormItem>

            <ElFormItem label="样品码" prop="standardSizeValue">
              <ElInput
                v-model="sizeValue"
                :autosize="{
                  minRows: 2,
                  maxRows: 4
                }"
                disabled
                placeholder="请选择尺码值"
                type="textarea"
              />
            </ElFormItem>

            <ElFormItem label="头型" prop="head">
              <ElSelect v-model="formData.head" clearable filterable placeholder="请选择头型">
                <ElOption
                  v-for="item in store.lastHeadList"
                  :key="item.dictValue"
                  :label="item.dictCnName"
                  :value="item.dictValue!"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="厚度(mm)" prop="thickness">
              <ElInputNumber
                v-model="formData.thickness"
                :controls="false"
                :min="0"
                :precision="2"
                :step="1"
                :value-on-clear="null"
                class="!w-48"
                clearable
                placeholder="请输入厚度(mm)"
              />
            </ElFormItem>
            <ElFormItem label="跟型" prop="pattern">
              <SelectPlus
                v-model="formData.pattern"
                api-key="HEEL_PATTERN"
                cache
                clearable
                filterable
                placeholder="请选择跟型"
              />
            </ElFormItem>
            <ElFormItem label="跟高(mm)" prop="thickness">
              <ElInputNumber
                v-model="formData.heelHigh"
                :controls="false"
                :min="0"
                :precision="2"
                :step="1"
                :value-on-clear="null"
                class="!w-48"
                clearable
                placeholder="请输入跟高(mm)"
              />
            </ElFormItem>
            <ElFormItem label="跟高" prop="high">
              <SelectPlus
                v-model="formData.high"
                api-key="HEEL_HIGH"
                cache
                clearable
                placeholder="请选择跟高"
              />
            </ElFormItem>
            <ElFormItem label="底厂" prop="bottomFactory">
              <ElInput
                v-model="formData.bottomFactory"
                class="w-48"
                clearable
                maxlength="100"
                placeholder="请输入底厂"
                show-word-limit
              />
            </ElFormItem>
            <ElFormItem label="材质" prop="materialId">
              <ElCascader
                v-model="formData.materialId"
                :options="materialCategoryList"
                :props="cascaderProps"
                clearable
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择材质"
              />
            </ElFormItem>
            <ElFormItem label="鞋垫材料" prop="insoleMaterialId">
              <ElCascader
                v-model="formData.insoleMaterialId"
                :options="materialCategoryList"
                :props="cascaderProps"
                clearable
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择鞋垫材料"
              />
            </ElFormItem>
            <ElFormItem label="鞋厂" prop="shoeFactory">
              <ElInput
                v-model="formData.shoeFactory"
                class="w-48"
                clearable
                maxlength="100"
                placeholder="请输入鞋厂"
                show-word-limit
              />
            </ElFormItem>
            <ElFormItem class="row-span-3" label="跟底特性说明" prop="featureDescription">
              <ElInput
                v-model="formData.featureDescription"
                :autosize="{ minRows: 4, maxRows: 4 }"
                :resize="isView ? 'none' : undefined"
                class="w-48"
                maxlength="500"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
            <ElFormItem label="供应商跟底编码" prop="supplierHeelNumber">
              <ElInput
                v-model="formData.supplierHeelNumber"
                class="w-48"
                clearable
                maxlength="100"
                placeholder="请输入供应商跟底编码"
                show-word-limit
              />
            </ElFormItem>
            <ElFormItem label="是否有足弓" prop="footArch">
              <ElRadioGroup v-model="formData.footArch">
                <ElRadio :value="true" label="是" />
                <ElRadio :value="false" label="否" />
              </ElRadioGroup>
            </ElFormItem>
          </div>
        </ElCollapseItem>
        <ElCollapseItem name="3" title="关联的楦型信息">
          <template #title>
            <div class="font-bold text-base">关联的楦型信息</div>
          </template>
          <ElButton
            v-if="!isView"
            class="mb-2"
            text
            type="primary"
            @click="handleOpenLastInfoDialog"
          >
            搜索楦型信息
          </ElButton>
          <VxeTable
            ref="lastTableRef"
            :cell-config="{ height: 80 }"
            :data="lastReferencedList"
            :max-height="500"
          >
            <VxeColumn title="序号" type="seq" />
            <VxeColumn field="code" title="楦型编码" />
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="thumbnail"
              title="缩略图"
              width="100"
            />
            <VxeColumn field="developmentSeasonCodeItemName" title="开发季节" />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.brandMap } }"
              field="brand"
              title="品牌"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.lastSexMap } }"
              field="sexCode"
              title="性别"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.lastTypeMap } }"
              field="typeCode"
              title="楦头类别"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.lastMarketMap } }"
              field="marketCode"
              title="楦头市场"
            />
            <VxeColumn
              :cell-render="{ name: 'Dict', props: { dictMap: store.lastHeightMap } }"
              field="heightCode"
              title="楦头帮高"
            />
            <VxeColumn field="productName" title="关联的产品信息" />
            <VxeColumn v-if="!isView" :show-overflow="false" title="操作">
              <template #default="{ rowIndex }">
                <ElButton text type="primary" @click="handleDeleteLastInfo(rowIndex)">
                  删除
                </ElButton>
              </template>
            </VxeColumn>
          </VxeTable>
        </ElCollapseItem>
      </ElForm>
    </ElCollapse>
  </ContentWrap>
  <ContentWrap class="mt-2">
    <div class="text-center">
      <ElButton @click="handleClose">返回</ElButton>
      <ElButton v-if="!isView" :loading="submitLoading" type="primary" @click="handleConfirm">
        确定
      </ElButton>
      <ElButton
        v-if="
          formData.statusCode !== StatusEnum.APPROVING &&
          formData.statusCode !== StatusEnum.BAN &&
          isView
        "
        v-hasPermi="['editHeel']"
        type="primary"
        @click="handleEditHeel"
      >
        <Icon size="20" icon="ep:edit" />
        修改跟底
      </ElButton>
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
.info-wrapper {
  max-height: calc(100vh - 250px);
  overflow: auto;
}

.number-input {
  :deep(.el-input__wrapper) {
    padding-left: 12px;
  }

  :deep(.el-input__inner) {
    text-align: left;
  }
}

:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}
</style>
