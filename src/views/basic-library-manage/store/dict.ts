import { defineStore } from 'pinia'
import {
  CommonOptionsAPI,
  DictValueAPI,
  getBrandList,
  getDictValueList,
  getMultiMatchedOptions
} from '@/views/basic-library-manage/api/common'
import { Result, useConstants } from '@/hooks/web/useConstants'
import { ToCamelCase, toCamelCase } from '@/utils'

enum ListKeyEnum {
  /**
   * 尺码库性别
   */
  SIZE_SEX = 'SIZE_SEX',
  /**
   * 尺码库分类
   */
  SIZE_TYPE = 'SIZE_TYPE',
  /**
   * 楦型头型
   */
  LAST_HEAD = 'HEEL_HEAD',
  /**
   * 楦型帮高
   */
  LAST_HEIGHT = 'LAST_HEIGHT',
  /**
   * 楦型市场
   */
  LAST_MARKET = 'LAST_MARKET',
  /**
   * 楦型性别
   */
  LAST_SEX = 'LAST_SEX',
  /**
   * 楦型类别
   */
  LAST_TYPE = 'LAST_TYPE',
  /**
   * 基础库区域
   */
  COMMON_REGION = 'COMMON_REGION',
  /**
   * 基础库季节
   */
  COMMON_DEV_SEASON = 'COMMON_DEV_SEASON',
  /**
   * 跟底分类
   */
  HEEL_BOTTOM_TYPE = 'HEEL_BOTTOM_TYPE',
  /**
   * 材料材质
   */
  MATERIAL_TEXTURE = 'MATERIAL_TEXTURE',
  /**
   * 材料单位
   */
  MATERIAL_UNIT = 'MATERIAL_UNIT',
  /**
   * 模具类型
   */
  MOLD_TYPE = 'MOLD_TYPE',
  /**
   * 开模类型
   */
  MOLD_OPEN_TYPE = 'MOLD_OPEN_TYPE',
  /**
   * 模具性质
   */
  MOLD_NATURE = 'MOLD_NATURE',
  /**
   * 品牌
   */
  BRAND = 'BRAND',
  /**
   * 供应商
   */
  SUPPLIER = 'SUPPLIER',
  /**
   * 产品风格
   */
  PRODUCT_STYLE = 'PRODUCT_STYLE',
  /**
   * 是否
   */
  COMMON_BOOL = 'COMMON_YES_NO',
  /**
   * 产品数据状态
   */
  PRODUCT_DATA_STATUS = 'PRODUCT_DATA_STATUS',
  /**
   * 款式结构
   */
  STYLE_STRUCTURE = 'COMMON_STYLE_STRUCTURE',
  /**
   * 任务节点
   */
  PRODUCT_TASK_NOTE = 'PRODUCT_TASK_NOTE',
  /**
   * 产品阶段
   */
  PRODUCT_STAGE = 'PRODUCT_STAGE',
  DERIVED_TYPE = 'DERIVED_TYPE',
  PRODUCT_PEOPLE = 'PRODUCT_PEOPLE',
  PRODUCT_LAST_STANDARD = 'PRODUCT_LAST_STANDARD',
  SIZE_CODE_TYPE = 'SIZE_CODE_TYPE'
}

enum OptionsKeyEnum {
  MULTI_MATCHED = 'MULTI_MATCHED'
}

type Mutable<T> = {
  -readonly [P in keyof T]: T[P]
}

type AppendSuffix<S extends string, Suffix extends 'List' | 'Map'> = `${ToCamelCase<S>}${Suffix}`

// 将 ListKeyEnum 的值映射为小驼峰形式
type Getters<Suffix extends 'List' | 'Map'> = {
  [K in keyof typeof ListKeyEnum as AppendSuffix<(typeof ListKeyEnum)[K], Suffix>]: (
    state: DictState
  ) => Suffix extends 'List' ? DictValueAPI.Data[] : Result['map']
}

type MapKeyEnum = `${ListKeyEnum}_MAP`

export type DictState = {
  // 字典列表
  [key in ListKeyEnum]: DictValueAPI.Data[]
} & {
  // 字典映射
  [key in MapKeyEnum]: Result['map']
} & {
  [key in OptionsKeyEnum]: CommonOptionsAPI.Value[]
}

const signal: { [key in ListKeyEnum]?: boolean } = {}

export const useBasicLibraryDictStore = defineStore('basicLibraryDict', {
  state: (): DictState => {
    const initialState: DictState = {} as DictState

    // 初始化 state
    Object.values(ListKeyEnum).forEach((key) => {
      initialState[key] = []
      initialState[`${key}_MAP` as MapKeyEnum] = {}
    })

    return initialState
  },
  getters: {
    // 动态生成 getters
    ...Object.values(ListKeyEnum).reduce((getters, key) => {
      const camelCaseKey = toCamelCase(key)
      getters[`${camelCaseKey}List`] = handleListGetter(key)
      return getters
    }, {} as Mutable<Getters<'List'>>),
    ...Object.values(ListKeyEnum).reduce((getters, key) => {
      const camelCaseKey = toCamelCase(key)
      getters[`${camelCaseKey}Map`] = handleMapGetter(`${key}_MAP` as MapKeyEnum)
      return getters
    }, {} as Mutable<Getters<'Map'>>),

    multiMatchedList: (state) => {
      if (!state[OptionsKeyEnum.MULTI_MATCHED] || !state[OptionsKeyEnum.MULTI_MATCHED].length) {
        getMultiMatchedOptions().then((result) => {
          if (result?.datas) {
            state[OptionsKeyEnum.MULTI_MATCHED] = result.datas.values
          }
        })
        return []
      }
      return state[OptionsKeyEnum.MULTI_MATCHED]
    },
    // 单独处理品牌列表和映射
    brandList: (state): DictValueAPI.Data[] => {
      if (!state[ListKeyEnum.BRAND] || !state[ListKeyEnum.BRAND].length) {
        getBrandList().then(([error, result]) => {
          if (!error && result?.datas) {
            state[ListKeyEnum.BRAND] = result.datas.map((item) => ({
              dictCnName: item.selectorKey,
              dictValue: item.selectorValue,
              dictEnName: item.selectorKey
            }))
          }
        })
        return []
      }
      return state[ListKeyEnum.BRAND]
    },
    brandMap(state): Result['map'] {
      const brandMap = `${ListKeyEnum.BRAND}_MAP` as MapKeyEnum
      if (!state[brandMap] || !Object.keys(state[brandMap]).length) {
        return useConstants(this.brandList, { label: 'dictCnName', value: 'dictValue' }).map
      }
      return state[brandMap]
    }
  },
  actions: {
    setDictListByType(type: ListKeyEnum, value: DictValueAPI.Data[]) {
      this[type] = value
    },
    setDictMapByType(type: MapKeyEnum, value: Result['map']) {
      this[type] = value
    },
    async initDictByType(type: ListKeyEnum) {
      if (signal[type]) return
      signal[type] = true

      const [error, result] = await getDictValueList(type)
      signal[type] = false

      if (!error && result?.datas) {
        this.setDictListByType(type, result.datas)
        const map = useConstants(result.datas, { label: 'dictCnName', value: 'dictValue' }).map
        this.setDictMapByType(`${type}_MAP` as MapKeyEnum, map)
      }
    }
  }
})

function handleListGetter(key: ListKeyEnum) {
  return (state: DictState) => {
    if (!state[key] || !state[key].length) {
      useBasicLibraryDictStore().initDictByType(key)
      return [] as DictValueAPI.Data[]
    }
    return state[key] as DictValueAPI.Data[]
  }
}

function handleMapGetter(key: MapKeyEnum) {
  return (state: DictState) => {
    if (!state[key] || !Object.keys(state[key]).length) {
      const listKey = key.slice(0, -4) as ListKeyEnum
      useBasicLibraryDictStore().initDictByType(listKey)
      return {} as Result['map']
    }
    return state[key] as Result['map']
  }
}

export class useAllDictStore {}
