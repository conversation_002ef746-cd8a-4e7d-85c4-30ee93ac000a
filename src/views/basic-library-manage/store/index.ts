import { ProductListPageAPI } from '../product-library/api/product-list'
import { defineStore } from 'pinia'
import { ProductSkcInfoPageAPI } from '@/api/productSkcInfo/types'

export interface HelpState {
  productFormData?: ProductListPageAPI.Params
  skcFormData?: ProductSkcInfoPageAPI.Params
}

const productListPageFormData = useSessionStorage('productListPageFormData', {})
const skcListPageFormData = useSessionStorage('skcListPageFormData', {})

export const useHelpStore = defineStore('basicLibraryHelp', {
  state: (): HelpState => {
    return {
      productFormData: productListPageFormData.value,
      skcFormData: skcListPageFormData.value
    }
  },
  actions: {
    setProductFormData(data: ProductListPageAPI.Params) {
      productListPageFormData.value = data
      this.productFormData = data
    },
    setSkcFormData(data: ProductSkcInfoPageAPI.Params) {
      skcListPageFormData.value = data
      this.skcFormData = data
    }
  }
})
