<script lang="ts" setup>
import { ElForm, ElMessage, FormRules } from 'element-plus'
import {
  getTeamPeopleDetailById,
  getVersionDetailById,
  OperationTypeEnum,
  teamPeopleSave,
  TeamPeopleSaveAPI,
  teamPeopleUpdate
} from '../api'
import { FactoryListAPI } from '@/components/Business/SelectPlus/src/api/types'

type FormData = Partial<TeamPeopleSaveAPI.Params>

defineOptions({
  name: 'EditFactoryTeamDialog'
})

const props = defineProps<{
  modelValue: boolean
  currentRow: any | undefined
  type: OperationTypeEnum
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'update:currentRow', val: any): void
  (e: 'refresh'): void
}>()

const defaultFormData: FormData = {
  factorId: '',
  name: '',
  region: '',
  brand: undefined,
  developmentModel: '',
  regionalSupplyPersonId: [],
  developmentContactPersonId: [],
  technicalContactPersonId: [],
  remark: ''
}

const formRules: FormRules<TeamPeopleSaveAPI.Params> = {
  factorId: [{ required: true, message: '请输入工厂英文名称', trigger: 'change' }],
  region: [{ required: true, message: '请输入区域', trigger: 'change' }],
  brand: [{ required: true, message: '请输入品牌', trigger: 'change' }]
}

const formRef = ref<ComponentRef<typeof ElForm>>()
const formData = ref<FormData>({ ...defaultFormData })
const supplierList = ref<FactoryListAPI.Data[]>([])
const submitLoading = ref(false)

const isEdit = computed(() => props.type === OperationTypeEnum.EDIT)
const isView = computed(() => props.type === OperationTypeEnum.VIEW)
const isAdd = computed(() => props.type === OperationTypeEnum.ADD)

const getTitle = computed(() =>
  isEdit.value ? '编辑工厂团队' : isView.value ? '查看工厂团队' : '新增工厂团队'
)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
  formData.value = { ...defaultFormData }
  emit('update:currentRow', null)
}

const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (valid) {
    const api = isEdit.value ? teamPeopleUpdate : teamPeopleSave
    const { factorId, ...rest } = formData.value
    const { vendorName, vendorFullName } =
      supplierList.value.find((item) => item.vendorId === factorId) || {}
    const params = {
      ...rest,
      factorId,
      name: vendorFullName,
      englishName: vendorName
    }
    submitLoading.value = true
    const [error, result] = await api(params)
    submitLoading.value = false
    if (!error && result) {
      ElMessage.success(result.msg || '操作成功')
      handleClose()
      emit('refresh')
    } else {
      ElMessage.error(error?.message || '操作失败')
    }
  }
}

const getDetail = async () => {
  if (isAdd.value) return
  const { id } = props.currentRow
  submitLoading.value = true
  const [error, result] = isView.value
    ? await getVersionDetailById(id)
    : await getTeamPeopleDetailById(id)
  submitLoading.value = false
  if (!error && result) {
    const {
      factorId,
      region,
      brand,
      developmentModel,
      regionalSupplyPersonId,
      developmentContactPersonId,
      technicalContactPersonId,
      remark,
      id,
      name
    } = result.datas
    Object.assign(formData.value, {
      id,
      factorId: factorId + '',
      region,
      brand,
      developmentModel,
      regionalSupplyPersonId,
      developmentContactPersonId,
      technicalContactPersonId,
      remark,
      name
    })
  }
}

const handleSupplierResponseData = (value: FactoryListAPI.Data[]) => {
  supplierList.value = value
}

const handleFactorChange = (value: string) => {
  const vendorFullName =
    supplierList.value.find((item) => (item.vendorId as unknown as string) === value)
      ?.vendorFullName || ''
  formData.value.name = vendorFullName
}

watch(visible, () => {
  if (visible.value) {
    getDetail()
  }
})
</script>

<template>
  <Dialog v-model="visible" :before-close="handleClose" :title="getTitle" width="60%">
    <ElForm
      ref="formRef"
      :disabled="isView"
      :model="formData"
      :rules="formRules"
      class="my-10"
      labelWidth="130px"
    >
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="工厂英文名称" prop="factorId">
            <SelectPlus
              v-model="formData.factorId"
              :disabled="isEdit"
              api-key="getSupplierList"
              class="!w-full"
              clearable
              filterable
              placeholder="请选择"
              @change="handleFactorChange"
              @response-data="handleSupplierResponseData"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="工厂名称" prop="name">
            <ElInput
              v-model="formData.name"
              class="!w-full"
              clearable
              disabled
              placeholder="请输入"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="区域" prop="region">
            <SelectPlus
              v-model="formData.region"
              api-key="COMMON_REGION"
              cache
              class="!w-full"
              clearable
              collapse-tags
              collapse-tags-tooltip
              filterable
              placeholder="请选择"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="品牌" prop="brand">
            <SelectPlus
              v-model="formData.brand"
              :disabled="isEdit"
              api-key="baseBrand"
              class="!w-full"
              clearable
              filterable
              placeholder="请选择品牌"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="开发模式" prop="developmentModel">
            <SelectPlus
              v-model="formData.developmentModel"
              api-key="PRODUCT_DEV_MODEL"
              class="!w-full"
              clearable
              filterable
              placeholder="请选择开发模式"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="供管" prop="regionalSupplyPersonId">
            <CascadeSelector
              v-model="formData.regionalSupplyPersonId"
              :props="{ multiple: true, emitPath: false }"
              api-key="allUsers"
              cache
              class="!w-full"
              clearable
              collapse-tags
              collapse-tags-tooltip
              placeholder="请选择"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="技术人员" prop="technicalContactPersonId">
            <CascadeSelector
              v-model="formData.technicalContactPersonId"
              :props="{ multiple: true, emitPath: false }"
              api-key="allUsers"
              cache
              class="!w-full"
              clearable
              collapse-tags
              collapse-tags-tooltip
              placeholder="请选择"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="开发人员" prop="developmentContactPersonId">
            <CascadeSelector
              v-model="formData.developmentContactPersonId"
              :props="{ multiple: true, emitPath: false }"
              api-key="allUsers"
              cache
              class="!w-full"
              clearable
              collapse-tags
              collapse-tags-tooltip
              placeholder="请选择"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem label="备注" prop="remark">
            <ElInput
              v-model="formData.remark"
              clearable
              maxlength="500"
              placeholder="请输入备注"
              type="textarea"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton v-if="!isView" :loading="submitLoading" type="primary" @click="handleSubmit"
        >确定
      </ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.el-scrollbar__wrap) {
  width: 100%;
}
</style>
