<script lang="ts" setup>
import { ElButton, ElMessage, ElPagination, FormInstance } from 'element-plus'
import { Pager } from '../api/common'
import type { VxeTableInstance } from 'vxe-table'
import dayjs from 'dayjs'
import { statusConst, StatusEnum } from '../const'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { isEqual } from 'lodash-es'
import { getModelVendorByPage, OperationTypeEnum, TeamPeoplePageAPI } from './api'
import EditFactoryTeamDialog from './components/EditFactoryTeamDialog.vue'
import EditStatusDialog from './components/EditStatusDialog.vue'
import ProductTeamVersionDialog from './components/ProductTeamVersionDialog.vue'
import { useTableHeight } from '@/hooks/web/useTableHeight'

interface FormModel
  extends Omit<TeamPeoplePageAPI.Params, 'startTime' | 'endTime' | 'current' | 'size'> {
  date?: [string, string]
}

// 产品团队
defineOptions({
  name: 'ProductTeam'
})

const { handleExport: exportFn, loading: exportLoading } = useOmsExport()

let controller: AbortController | null = null
const formRef = ref<FormInstance>()
const statusList = statusConst.statusList.filter((item) =>
  [StatusEnum.BAN, StatusEnum.DRAFT, StatusEnum.START].includes(item.value as StatusEnum)
)

const vxeTableRef = ref<VxeTableInstance>()
const pagerRef = ref<InstanceType<typeof ElPagination>>()
const maxHeight = useTableHeight({
  tableRef: vxeTableRef,
  pagerRef
})
const selectedRows = ref<TeamPeoplePageAPI.Data[]>([])
const currentRow = ref<TeamPeoplePageAPI.Data | null>(null)
const queryLoading = ref(false)
// 关于弹窗
const editFactoryTeamVisible = ref(false)
const editStatusVisible = ref(false)
const productTeamVersionVisible = ref(false)

const operationType = ref<OperationTypeEnum>(OperationTypeEnum.ADD)
const visible = ref(false)
const tableData = ref<TeamPeoplePageAPI.Data[]>([])
const pager = ref<Pager>({
  current: 1,
  size: 10,
  total: 0
})

const defaultTime: [Date, Date] = [
  dayjs('00:00:00', 'HH:mm:ss').toDate(),
  dayjs('23:59:59', 'HH:mm:ss').toDate()
]

const defaultFormData: FormModel = {
  englishName: '',
  name: '',
  region: [],
  status: [StatusEnum.DRAFT, StatusEnum.START],
  modifyById: undefined
}

let lastFormData = {
  ...defaultFormData
}

const formData = ref<FormModel>({
  ...defaultFormData
})

const queryParams = computed<TeamPeoplePageAPI.Request>(() => {
  const { date, ...rest } = formData.value
  const { current, size } = pager.value
  return {
    ...rest,
    current,
    size,
    startTime: date?.[0],
    endTime: date?.[1]
  }
})

const setVisible = () => {
  visible.value = !unref(visible)
}

const handleReset = () => {
  formData.value = {
    ...defaultFormData
  }
  handleQuery()
}

const handleQuery = async () => {
  if (controller) {
    controller.abort()
    controller = null
    setTimeout(() => {
      handleQuery()
    })
    return
  }
  queryLoading.value = true
  controller = new AbortController()
  if (!isEqual(lastFormData, formData.value)) {
    pager.value.current = 1
  }
  const [error, result] = await getModelVendorByPage(queryParams.value, controller.signal)
  queryLoading.value = false
  selectedRows.value = []
  if (error === null && result?.datas) {
    lastFormData = { ...formData.value }
    const { records } = result.datas
    tableData.value = records
    pager.value.total = result.datas.pager.total
  }
}

const handleEditFactoryTeam = (type: OperationTypeEnum) => {
  if (type === OperationTypeEnum.EDIT) {
    const rows = vxeTableRef.value?.getCheckboxRecords() as TeamPeoplePageAPI.Data[]
    const hasBan = rows.some((row) => row.status === StatusEnum.BAN)
    if (rows.length === 0) {
      ElMessage.error('请勾选一条记录，再进行内容修改')
      return
    }

    if (rows.length > 1) {
      ElMessage.error('只能选择一行进行修改')
      return
    }

    if (hasBan) {
      ElMessage.error('禁用状态下的数据不允许操作')
      return
    }
    currentRow.value = rows[0]
  }
  operationType.value = type
  editFactoryTeamVisible.value = true
}

const handleStatusSupplier = () => {
  const rows = vxeTableRef.value?.getCheckboxRecords() as TeamPeoplePageAPI.Data[]
  if (rows.length === 0) {
    ElMessage.error('启用或禁用材料信息，需勾选一条或多条信息')
    return
  }
  editStatusVisible.value = true
  selectedRows.value = rows as TeamPeoplePageAPI.Data[]
}

const handleVersionRecord = (row: TeamPeoplePageAPI.Data) => {
  currentRow.value = row
  productTeamVersionVisible.value = true
}

const handleVersionDialogClose = (row: TeamPeoplePageAPI.Data) => {
  currentRow.value = row
  handleEditFactoryTeam(OperationTypeEnum.VIEW)
}

const handleExport = async () => {
  const valid = await formRef.value?.validate()
  if (valid) {
    let reqParam: string
    const rows = vxeTableRef.value?.getCheckboxRecords() as TeamPeoplePageAPI.Data[]
    if (rows && rows?.length > 0) {
      const idList = rows.map((item) => item.id)
      reqParam = JSON.stringify({ idList })
    } else {
      reqParam = JSON.stringify(queryParams.value)
    }
    exportFn({
      exportType: 'team-export',
      reqParam
    })
  }
}

onMounted(handleQuery)
</script>
<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_225px)] overflow-x-hidden overflow-y-auto">
        <ElForm
          ref="formRef"
          :model="formData"
          labelWidth="100px"
          @submit="
        (e:Event) => {
          e.preventDefault()
        }
      "
        >
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="工厂英文名称" prop="englishName">
                <ElInput
                  v-model="formData.englishName"
                  class="w-full"
                  clearable
                  placeholder="输入工厂英文名称，支持模糊搜索"
                  @blur="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="工厂中文名称" prop="name">
                <ElInput
                  v-model="formData.name"
                  class="w-full"
                  clearable
                  placeholder="输入工厂中文名称，支持模糊搜索"
                  @blur="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="" label-width="0">
                <ElButton text @click="setVisible">
                  {{ visible ? '收起' : '展开' }}
                  <Icon
                    :class="visible ? 'rotate-90' : ''"
                    class="transform transition duration-400"
                    icon="ant-design:down-outlined"
                  />
                </ElButton>
                <ElButton
                  :loading="queryLoading"
                  type="primary"
                  @click="handleQuery"
                  class="w-16"
                  native-type="submit"
                >
                  <Icon class="mr-1" icon="ep:search" />
                  查询
                </ElButton>
                <ElButton type="primary" @click="handleReset" native-type="reset">重置</ElButton>
                <ElButton
                  v-hasPermi="['productTeam:export']"
                  :loading="exportLoading"
                  class="w-16"
                  type="primary"
                  @click="handleExport"
                >
                  <Icon class="mr-1" icon="ep:upload-filled" />
                  导出
                </ElButton>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="区域" prop="region">
                <SelectPlus
                  v-model="formData.region"
                  api-key="COMMON_REGION"
                  cache
                  checkbox
                  checkbox-button
                  @change="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCollapseTransition>
              <div v-show="visible" class="flex flex-wrap w-full">
                <ElCol :span="8">
                  <ElFormItem label="状态" prop="status">
                    <ElCheckboxGroup v-model="formData.status" @change="handleQuery">
                      <ElCheckboxButton
                        v-for="item in statusList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </ElCheckboxGroup>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="操作时间段" prop="date">
                    <ElDatePicker
                      v-model="formData.date"
                      :default-time="defaultTime"
                      class="w-full"
                      clearable
                      end-placeholder="结束时间"
                      range-separator="~"
                      start-placeholder="开始时间"
                      type="daterange"
                      unlink-panels
                      value-format="YYYY-MM-DD"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="操作人" prop="modifyById">
                    <CascadeSelector
                      v-model="formData.modifyById"
                      :props="{ emitPath: false }"
                      api-key="allUsers"
                      cache
                      class="w-full"
                    />
                  </ElFormItem>
                </ElCol>
              </div>
            </ElCollapseTransition>
          </ElRow>
        </ElForm>
        <div class="mb-[10px] min-h-8">
          <ElButton
            v-hasPermi="['productTeam:add']"
            type="primary"
            @click="handleEditFactoryTeam(OperationTypeEnum.ADD)"
          >
            <Icon icon="ep:plus" />
            <span class="text-[14px]">新增工厂团队</span>
          </ElButton>
          <ElButton
            v-hasPermi="['productTeam:edit']"
            type="primary"
            @click="handleEditFactoryTeam(OperationTypeEnum.EDIT)"
          >
            <Icon icon="ep:edit" />
            <span class="text-[14px]">修改工厂团队</span>
          </ElButton>
          <ElButton
            v-hasPermi="['productTeam:status']"
            type="primary"
            @click="handleStatusSupplier"
          >
            <Icon icon="ep:share" />
            <span class="text-[14px]">启用/禁用</span>
          </ElButton>
        </div>
        <div>
          <VxeTable
            ref="vxeTableRef"
            :data="tableData"
            :loading="queryLoading"
            :max-height="maxHeight - 125"
          >
            <VxeColumn type="checkbox" width="50" />
            <VxeColumn show-overflow title="序号" type="seq" width="80" />
            <VxeColumn field="englishName" show-overflow title="工厂英文名称" />
            <VxeColumn field="name" show-overflow title="工厂名称" />
            <VxeColumn field="regionItemName" show-overflow title="区域" />
            <VxeColumn field="brandItemName" show-overflow title="品牌" />
            <VxeColumn field="developmentModelItemName" show-overflow title="开发模式" />
            <VxeColumn field="regionalSupplyPersonIdItemName" show-overflow title="供管" />
            <VxeColumn field="technicalContactPersonIdItemName" show-overflow title="技术人员" />
            <VxeColumn field="developmentContactPersonIdItemName" show-overflow title="开发人员" />
            <VxeColumn field="remark" show-overflow title="备注" />
            <VxeColumn field="statusItemName" show-overflow title="状态" />
            <VxeColumn field="modifyByIdItemName" show-overflow title="操作人" />
            <VxeColumn field="modifyTime" show-overflow title="操作时间" />
            <VxeColumn :show-overflow="false" title="操作">
              <template #default="{ row }: { row: TeamPeoplePageAPI.Data }">
                <div class="flex justify-around items-center">
                  <ElButton size="small" text type="primary" @click="handleVersionRecord(row)">
                    版本记录
                  </ElButton>
                </div>
              </template>
            </VxeColumn>
          </VxeTable>
        </div>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
      <!-- 新建/编辑/详情 -->
      <EditFactoryTeamDialog
        v-model="editFactoryTeamVisible"
        v-model:current-row="currentRow"
        :type="operationType"
        @refresh="handleQuery"
      />
      <!-- 修改状态 -->
      <EditStatusDialog
        v-model="editStatusVisible"
        v-model:currentRows="selectedRows"
        @refresh="handleQuery"
      />
      <!-- 版本 -->
      <ProductTeamVersionDialog
        v-model="productTeamVersionVisible"
        :currentRow="currentRow"
        @close="handleVersionDialogClose"
      />
    </div>
  </ContentWrap>
</template>
