import { service } from '@/config/axios/service'
import to from 'await-to-js'

export enum OperationTypeEnum {
  EDIT = 'edit',
  VIEW = 'view',
  ADD = 'add'
}

// 新增产品团队管理
export namespace TeamPeopleSaveAPI {
  export interface Params {
    /**
     * 品牌
     */
    brand?: number
    /**
     * 开发人员id
     */
    developmentContactPersonId?: number[]
    /**
     * 开发模式
     */
    developmentModel?: string
    /**
     * 工厂英文名称
     */
    englishName?: string
    /**
     * scm工厂id
     */
    factorId?: string
    /**
     * 工厂名称
     */
    name?: string
    /**
     * 区域
     */
    region?: string
    /**
     * 供管人员id
     */
    regionalSupplyPersonId?: number[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 技术人员id
     */
    technicalContactPersonId?: number[]
  }

  export type Request = Params
  export type Response = BasicResponseData
}

// 产品团队管理详情
export namespace TeamPeopleAPI {
  export interface Data {
    /**
     * 品牌
     */
    brand?: number
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    /**
     * 开发人员id
     */
    developmentContactPersonId?: number[]
    /**
     * 开发模式
     */
    developmentModel?: string
    /**
     * 工厂英文名称
     */
    englishName?: string
    /**
     * scm工厂id
     */
    factorId?: number
    id?: number
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 工厂名称
     */
    name?: string
    /**
     * 区域
     */
    region?: string
    /**
     * 供管人员id
     */
    regionalSupplyPersonId?: number[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 状态
     * 状态code
     */
    status?: string
    /**
     * 技术人员id
     */
    technicalContactPersonId?: number[]
  }
  export type Response = ResponseData<Data>
}

// 启用/禁用
export namespace TeamPeopleStartOrBanApi {
  export interface Params {
    updateStatusReqList?: BaseUpdateStatusReq[]
  }

  export interface BaseUpdateStatusReq {
    /**
     * id
     */
    id?: number
    /**
     * 修改后的状态
     */
    newStatus?: string
    /**
     * 修改前的状态
     */
    oldStatus?: string
    status?: string
  }

  /**
   * 修改后的状态
   *
   * 修改前的状态
   */
  export enum Status {
    Approving = 'APPROVING',
    Ban = 'BAN',
    Draft = 'DRAFT',
    Start = 'START'
  }

  export type Request = Params
  export type Response = BasicResponseData
}

// 分页
export namespace TeamPeoplePageAPI {
  export interface Params {
    /**
     * 页码
     */
    current?: number
    /**
     * 结束时间
     */
    endTime?: string
    /**
     * 工厂英文名称
     */
    englishName?: string
    idList?: string[]
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 工厂名称
     */
    name?: string
    /**
     * 区域
     */
    region?: string[]
    /**
     * 页面尺寸
     */
    size?: number
    /**
     * 开始时间
     */
    startTime?: string
    /**
     * 状态
     * 状态code
     */
    status?: string[]
  }

  export interface Data {
    /**
     * 品牌
     */
    brand?: number
    /**
     * 开发人员id
     */
    developmentContactPersonId?: number[]
    /**
     * 开发模式
     */
    developmentModel?: string
    /**
     * 工厂英文名称
     */
    englishName?: string
    /**
     * scm工厂id
     */
    factorId?: number
    id?: number
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 工厂名称
     */
    name?: string
    /**
     * 区域
     */
    region?: string
    /**
     * 供管人员id
     */
    regionalSupplyPersonId?: number[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 状态
     * 状态code
     */
    status?: string
    /**
     * 技术人员id
     */
    technicalContactPersonId?: number[]
  }
  export type Request = Params & PageParams
  export type Response = PagedResponseData<Data>
}

// 查询对应的人员
export namespace TeamPeopleQueryApi {
  export interface Params {
    /**
     * 品牌
     */
    band?: number
    /**
     * 开发模式
     */
    developmentModel?: string
    /**
     * 工厂id
     */
    factoryId?: number
  }

  export interface Data {
    /**
     * 开发人员id
     */
    developmentContactPersonId?: number[]
    /**
     * 技术人员id
     */
    technicalContactPersonId?: number[]
  }
  export type Request = Params
  export type Response = ResponseData<Data>
}

// 版本记录
export namespace ModelVendorVersionApi {
  export interface Data {
    /**
     * 数据变更时间
     */
    dataTime?: string
    id?: number
    /**
     * 操作人
     */
    operator?: string
    /**
     * 操作人id
     */
    operatorId?: number
    /**
     * 状态
     */
    status?: string
    /**
     * 版本号
     */
    versionCode?: string
    /**
     * 版本说明
     */
    versionRemark?: string
    /**
     * 版本生效时间
     */
    versionTime?: string
    /**
     * 版本变更类型
     */
    versionType?: string
  }
  export type Response = ResponseData<Data[]>
}
// 版本记录详情
export namespace ModelVendorVersionDetailApi {
  export interface Params {
    versionId: string
  }
  export interface Data {
    /**
     * 品牌
     */
    brand?: number
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    /**
     * 开发人员id
     */
    developmentContactPersonId?: number[]
    /**
     * 开发模式
     */
    developmentModel?: string
    /**
     * 工厂英文名称
     */
    englishName?: string
    /**
     * scm工厂id
     */
    factorId?: number
    id?: number
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 工厂名称
     */
    name?: string
    /**
     * 区域
     */
    region?: string
    /**
     * 供管人员id
     */
    regionalSupplyPersonId?: number[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 状态
     * 状态code
     */
    status?: string
    /**
     * 技术人员id
     */
    technicalContactPersonId?: number[]
  }

  export type Request = Params
  export type Response = ResponseData<Data>
}

// 新增产品团队管理
export function teamPeopleSave(data: TeamPeopleSaveAPI.Request) {
  return to<TeamPeopleSaveAPI.Response>(service.post('/pdm-base/teamPeople/save', data))
}

// 修改产品团队管理
export function teamPeopleUpdate(data: TeamPeopleSaveAPI.Request) {
  return to<TeamPeopleSaveAPI.Response>(service.post('/pdm-base/teamPeople/update', data))
}

// 产品团队管理详情
export function getTeamPeopleDetailById(id: string) {
  return to<TeamPeopleAPI.Response>(service.get(`/pdm-base/teamPeople/detail/${id}`))
}

// 启用或禁用
export function updateTeamPeopleStatus(data: TeamPeopleStartOrBanApi.Request) {
  return to<TeamPeopleStartOrBanApi.Response>(service.post('/pdm-base/teamPeople/startOrBan', data))
}

// 分页
export function getModelVendorByPage(params, signal?: AbortSignal) {
  return to<TeamPeoplePageAPI.Response>(
    service.get('/pdm-base/teamPeople/page', { params, signal })
  )
}

// 版本详情列表
export function getVersionListById(id: number) {
  return to<ModelVendorVersionApi.Response>(service.get(`/pdm-base/teamPeople/version/${id}`))
}

// 版本详情
export function getVersionDetailById(versionId: string) {
  return to<ModelVendorVersionDetailApi.Response>(
    service.get(`/pdm-base/teamPeople/versionDetail/${versionId}`)
  )
}
