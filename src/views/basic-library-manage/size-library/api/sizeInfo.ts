import { service } from '@/config/axios/service'
import to from 'await-to-js'

export namespace CreateSizeAPI {
  export type Params = Omit<
    SizeInfoAPI.Data,
    'id' | 'wmsName' | 'wmsCode' | 'status' | 'statusCode'
  > & {
    needApprove?: boolean
  }
  export type Request = Params
  export type Response = BasicResponseData
}
export function createSize(data: CreateSizeAPI.Request) {
  return to<CreateSizeAPI.Response>(service.post('/pdm-base/size/save', data))
}

export namespace SizeInfoAPI {
  export interface BaseSizeValueResp {
    /**
     * 尺码值编码
     */
    code?: string
    /**
     * 尺码值id
     */
    id?: number
    /**
     * 尺码值
     */
    sizeValue?: string
    /**
     * 是否标准码
     */
    standard?: boolean
    /**
     * wms尺码值编码
     */
    wmsCode?: string
    /**
     * wms尺码值
     */
    wmsSizeValue?: string
    status?: string
    disabled?: boolean
  }
  export interface Data {
    sizeType?: string
    /**
     * 尺码集编码
     */
    code?: string
    id?: number
    /**
     * 名称
     */
    name?: string
    /**
     * 是否被产品库，材料库引用
     */
    referenced?: boolean
    /**
     * 备注
     */
    remark?: string
    /**
     * 尺码段
     */
    segment?: string
    /**
     * 性别code
     */
    sexCode?: string
    /**
     * 尺码值信息
     */
    sizeValueList?: BaseSizeValueResp[]
    /**
     * 状态
     */
    status?: string
    /**
     * 状态code
     */
    statusCode?: string
    /**
     * 尺码分类编码
     */
    typeCode?: string
  }
  export type Response = ResponseData<Data>
}
export function viewSize(id: number) {
  return to<SizeInfoAPI.Response>(service.get(`/pdm-base/size/detail/${id}`))
}

export function viewSizeByVersionId(id: number) {
  return to<SizeInfoAPI.Response>(service.get(`/pdm-base/size/versionDetail/${id}`))
}

export namespace UpdateSizeAPI {
  export interface Params extends CreateSizeAPI.Params {
    id?: number
    status?: string
  }
  export type Request = Params
  export type Response = BasicResponseData
}
export function editSize(data: UpdateSizeAPI.Params) {
  return to<UpdateSizeAPI.Response>(service.post('/pdm-base/size/update', data))
}

export namespace SizeValueAPI {
  export interface Params {
    sizeId?: number[]
  }
  export interface Row {
    /**
     * 尺码值编码
     */
    code?: string
    /**
     * 尺码值id
     */
    id?: number
    /**
     * 尺码值
     */
    sizeValue?: string
    /**
     * 是否标准码
     */
    standard?: boolean
    /**
     * wms尺码值编码
     */
    wmsCode?: string
    /**
     * wms尺码值
     */
    wmsSizeValue?: string
    /**
     * 昨天
     */
    status?: string
  }
  export type List = Row[]
  export type Request = Params
  export type Response = ResponseData<List>
}
export function getSizeValueById(data: SizeValueAPI.Request) {
  return to<SizeValueAPI.Response>(service.post(`/pdm-base/size/getSizeValueById/1`, data))
}
