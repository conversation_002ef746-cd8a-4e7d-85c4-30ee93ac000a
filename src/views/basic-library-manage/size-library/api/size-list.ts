import { service } from '@/config/axios/service'
import to from 'await-to-js'

export namespace SizeListPageAPI {
  export interface Params {
    /**
     * 结束时间
     */
    endTime?: string
    idList?: string[]
    /**
     * 尺码集名称
     */
    name?: string
    /**
     * 尺码段
     */
    segment?: string
    /**
     * 性别编码
     */
    sexCode?: string[]
    /**
     * 尺码分类编码
     */
    sizeTypeCode?: string[]
    /**
     * 开始时间
     */
    startTime?: string
    /**
     * 状态编码
     */
    statusCode?: string[]
    /**
     * 尺码集分类
     */
    sizeType?: string[]
  }
  export interface Row {
    /**
     * 尺码集编码
     */
    code?: string
    /**
     * 审批状态
     */
    dataStatus?: string
    /**
     * 审批状态code
     */
    dataStatusCode?: string
    id?: number
    /**
     * 操作时间
     */
    modifyTime?: Date
    /**
     * 名称
     */
    name?: string
    /**
     * 操作人
     */
    operator?: string
    /**
     * 尺码段
     */
    segment?: string
    /**
     * 性别
     */
    sex?: string
    /**
     * 性别code
     */
    sexCode?: string
    /**
     * 尺码值，逗号分隔
     */
    sizeValue?: string
    /**
     * 标准码
     */
    standardValue?: string
    /**
     * 状态
     */
    status?: string
    /**
     * 状态code
     */
    statusCode?: string
    /**
     * 尺码集分类
     */
    sizeType?: string
  }
  export type List = Row[]
  export type Request = Params
  export type Response = PagedResponseData<Row>
}

export function getSizeListByPage(params: SizeListPageAPI.Request, signal?: AbortSignal) {
  return to<SizeListPageAPI.Response>(service.get('/pdm-base/size/page', { params, signal }))
}
