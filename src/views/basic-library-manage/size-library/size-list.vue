<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { ref } from 'vue'
import { Icon } from '@/components/Icon'
import {
  getSizeListByPage,
  SizeListPageAPI
} from '@/views/basic-library-manage/size-library/api/size-list'
import { Pager } from '@/views/basic-library-manage/api/common'
import type { ElPagination, FormInstance } from 'element-plus'
import { ElCollapseTransition, ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { VxeTableInstance } from 'vxe-table'
import {
  approvalStatusConst,
  ApprovalStatusEnum,
  statusConst,
  StatusEnum
} from '@/views/basic-library-manage/const'
import type { VxeTablePropTypes } from 'vxe-table/types'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import VersionDialog from '@/views/basic-library-manage/size-library/components/VersionDialog.vue'
import StatusDialog from '@/views/basic-library-manage/size-library/components/StatusDialog.vue'
import { isEqual } from 'lodash-es'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { useAllDictStore } from '@/store/modules/dictAll'

defineOptions({
  name: 'SizeLibrary'
})

const router = useRouter()

const useConst = () => {
  const allDictStore = useAllDictStore()

  function getSizeSex(sexValue?: string) {
    return allDictStore.valueToLabel('SIZE_SEX', sexValue)
  }

  function getSizeCodeType(sizeCodeType?: string) {
    return allDictStore.valueToLabel('SIZE_CODE_TYPE', sizeCodeType)
  }
  // 状态枚举
  const statusMap = statusConst.statusMap
  const statusList = statusConst.statusList

  // 审批状态枚举
  const approvalStatusMap = approvalStatusConst.approvalStatusMap

  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '100'
    },
    {
      formLabelLength: '180'
    }
  ])
  return {
    getSizeSex,
    getSizeCodeType,
    statusMap,
    statusList,
    approvalStatusMap,
    formLabelLength
  }
}

const { getSizeSex, getSizeCodeType, statusMap, statusList, approvalStatusMap, formLabelLength } =
  useConst()

const useQuery = () => {
  interface FormModel
    extends Omit<SizeListPageAPI.Request, 'startTime' | 'endTime' | 'current' | 'size'> {
    date?: [string, string]
  }

  const defaultFormData: FormModel = {
    sexCode: [],
    sizeTypeCode: [],
    name: '',
    statusCode: [],
    segment: '',
    date: ['', ''],
    sizeType: []
  }
  const formData = ref<FormModel>({
    ...defaultFormData
  })
  let lastFormData = {
    ...defaultFormData
  }
  const formRef = ref<FormInstance>()
  const tableRef = ref<VxeTableInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const tableData = ref<SizeListPageAPI.List>([])
  const queryLoading = ref(false)
  const pager = ref<Pager>({
    current: 1,
    size: 10,
    total: 0
  })
  const queryParams = computed<SizeListPageAPI.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1],
      date: undefined
    }
  })
  const defaultTime: [Date, Date] = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ]

  let controller: AbortController | null = null
  const handleQuery = async () => {
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await getSizeListByPage(queryParams.value, controller.signal)
    queryLoading.value = false
    if (error === null && result?.datas) {
      lastFormData = { ...formData.value }
      const { records } = result.datas
      tableData.value = records
      pager.value.total = result.datas.pager.total
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
    handleQuery()
  }

  onActivated(handleQuery)

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef
  })

  const setRejectedCellClassName: VxeTablePropTypes.CellClassName = ({ row, column }) => {
    if (
      row.dataStatus === approvalStatusMap[ApprovalStatusEnum.REJECTION] &&
      column.field === 'status'
    ) {
      return 'bg-red-400 text-white'
    }
  }

  return {
    formRef,
    formData,
    queryLoading,
    pager,
    queryParams,
    defaultTime,
    handleQuery,
    handleReset,
    visible,
    setVisible,
    tableRef,
    pagerRef,
    tableData,
    maxHeight,
    setRejectedCellClassName
  }
}

const {
  formRef,
  formData,
  queryLoading,
  pager,
  queryParams,
  defaultTime,
  handleQuery,
  handleReset,
  visible,
  setVisible,
  tableRef,
  pagerRef,
  tableData,
  maxHeight,
  setRejectedCellClassName
} = useQuery()

// 版本记录弹窗
const useVersionDialog = () => {
  const versionDialogVisible = ref(false)
  const setVersionDialogVisible = () => {
    versionDialogVisible.value = !unref(versionDialogVisible)
  }
  return {
    versionDialogVisible,
    setVersionDialogVisible
  }
}

// 启用/禁用弹窗
const useStatusDialog = () => {
  const statusDialogVisible = ref(false)
  const setStatusDialogVisible = () => {
    statusDialogVisible.value = !unref(statusDialogVisible)
  }
  return {
    statusDialogVisible,
    setStatusDialogVisible
  }
}

const useOperation = () => {
  const currentRow = ref<SizeListPageAPI.Row>()
  const selectedRows = ref<SizeListPageAPI.List>([])

  // 导出
  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = () => {
    const selectedRows: SizeListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    let reqParam: string
    if (selectedRows && selectedRows.length > 0) {
      reqParam = JSON.stringify({ idList: selectedRows.map((e) => e.id) })
    } else {
      reqParam = JSON.stringify(queryParams.value)
    }
    exportFn({
      exportType: 'size-export',
      reqParam
    })
  }

  // 修改尺码集
  const handleEditSize = () => {
    const selectedRows: SizeListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    const status = selectedRows[0].statusCode
    if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
      ElMessage.warning('审批中/禁用数据不允许修改')
      return
    }
    router.push({
      name: 'EditSize',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 复制尺码集
  const handleCopySize = () => {
    const selectedRows: SizeListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    router.push({
      name: 'CopySize',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 启用/禁用
  const { statusDialogVisible, setStatusDialogVisible } = useStatusDialog()
  const handleChangeStatus = () => {
    const checkedRecords: SizeListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!checkedRecords?.length) {
      ElMessage.warning('请选择数据')
      return
    }
    const hasApproving = checkedRecords.some(
      (item) => item.status === statusMap[StatusEnum.APPROVING]
    )
    if (hasApproving) {
      ElMessage.warning('存在审批中的数据')
      return
    }
    selectedRows.value = checkedRecords
    setStatusDialogVisible()
  }

  // 新增尺码集
  const handleCreateSize = () => {
    router.push({
      name: 'CreateSize'
    })
  }

  // 版本记录
  const { versionDialogVisible, setVersionDialogVisible } = useVersionDialog()
  const handleOpenVersionDialog = (row: SizeListPageAPI.Row) => {
    currentRow.value = row
    setVersionDialogVisible()
  }

  return {
    currentRow,
    selectedRows,
    handleExport,
    exportLoading,
    handleCopySize,
    handleEditSize,
    handleCreateSize,
    versionDialogVisible,
    handleOpenVersionDialog,
    handleChangeStatus,
    statusDialogVisible
  }
}

const {
  handleExport,
  exportLoading,
  handleCopySize,
  handleEditSize,
  handleCreateSize,
  versionDialogVisible,
  handleOpenVersionDialog,
  currentRow,
  selectedRows,
  handleChangeStatus,
  statusDialogVisible
} = useOperation()

Promise.all([handleQuery()])
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_250px)] overflow-x-hidden overflow-y-auto">
        <ElForm ref="formRef" :label-width="formLabelLength" :model="formData">
          <div class="grid grid-cols-3 items-center">
            <ElFormItem label="尺码集名称" prop="name">
              <ElInput
                v-model="formData.name"
                clearable
                placeholder="请输入尺码集名称，支持模糊查询"
                @change="handleQuery"
              />
            </ElFormItem>
            <ElFormItem label="性别" prop="sexCode">
              <SelectPlus
                v-model="formData.sexCode"
                api-key="SIZE_SEX"
                clearable
                collapse-tags
                collapse-tags-tooltip
                multiple
                placeholder="请选择性别"
                @change="handleQuery"
              />
            </ElFormItem>
            <ElFormItem label="" label-width="0">
              <ElButton text @click="setVisible">
                {{ visible ? '收起' : '展开' }}
                <Icon
                  :class="visible ? 'rotate-90' : ''"
                  class="transform transition duration-400"
                  icon="ant-design:down-outlined"
                />
              </ElButton>
              <ElButton :loading="queryLoading" type="primary" @click="handleQuery">
                <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
                查询
              </ElButton>
              <ElButton :loading="queryLoading" @click="handleReset">
                <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
                重置
              </ElButton>
              <ElButton :loading="exportLoading" type="primary" @click="handleExport">
                <Icon v-show="!exportLoading" class="mr-1" icon="ep:upload-filled" />
                导出
              </ElButton>
            </ElFormItem>
            <ElFormItem label="尺码分类" prop="sizeTypeCode">
              <SelectPlus
                v-model="formData.sizeTypeCode"
                api-key="SIZE_TYPE"
                cache
                checkbox
                checkbox-button
                @change="handleQuery"
              />
            </ElFormItem>
            <ElFormItem label="尺码集分类" prop="sizeType">
              <SelectPlus
                v-model="formData.sizeType"
                api-key="SIZE_CODE_TYPE"
                cache
                checkbox
                checkbox-button
                @change="handleQuery"
              />
            </ElFormItem>
            <ElCollapseTransition>
              <div v-show="visible" class="col-span-2 grid grid-cols-1 items-center">
                <ElFormItem label="状态" prop="status">
                  <ElCheckboxGroup
                    v-model="formData.statusCode"
                    class="flex flex-nowrap"
                    @change="handleQuery"
                  >
                    <ElCheckboxButton
                      v-for="item in statusList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </ElCheckboxGroup>
                </ElFormItem>
                <ElFormItem label="尺码段" prop="segment">
                  <ElInput
                    v-model="formData.segment"
                    class="w-48"
                    clearable
                    placeholder="请输入尺码段信息，支持模糊查询"
                    @change="handleQuery"
                  />
                </ElFormItem>
                <ElFormItem label="操作时间段" prop="date">
                  <ElDatePicker
                    v-model="formData.date"
                    :default-time="defaultTime"
                    class="max-w-96"
                    clearable
                    end-placeholder="结束时间"
                    range-separator="~"
                    start-placeholder="开始时间"
                    type="daterange"
                    unlink-panels
                    value-format="YYYY-MM-DD"
                  />
                </ElFormItem>
              </div>
            </ElCollapseTransition>
          </div>
        </ElForm>
        <div class="mb-2 min-h-8">
          <ElButton v-hasPermi="['createSize']" type="primary" @click="handleCreateSize">
            <Icon icon="ep:plus" />
            <span>新增尺码集</span>
          </ElButton>
          <ElButton v-hasPermi="['editSize']" type="primary" @click="handleEditSize">
            <Icon icon="ep:edit" />
            <span>修改尺码集</span>
          </ElButton>
          <ElButton v-hasPermi="['size:changeStatus']" type="primary" @click="handleChangeStatus">
            <Icon icon="ep:share" />
            <span>启用/禁用</span>
          </ElButton>
          <ElButton v-hasPermi="['copySize']" type="primary" @click="handleCopySize">
            <Icon icon="ep:copy-document" />
            <span>复制尺码集</span>
          </ElButton>
        </div>
        <div>
          <VxeTable
            ref="tableRef"
            :cell-class-name="setRejectedCellClassName"
            :data="tableData"
            :loading="queryLoading"
            :max-height="maxHeight - 75"
            :show-header-overflow="false"
            :show-overflow="false"
          >
            <VxeColumn fixed="left" type="checkbox" width="40" />
            <VxeColumn title="序号" fixed="left" type="seq" width="60" />
            <VxeColumn field="code" fixed="left" show-overflow title="尺码集编码" width="90">
              <template #default="{ row }: { row: SizeListPageAPI.Row }">
                <router-link :to="{ name: 'ViewSize', query: { id: row.id } }">
                  <span class="p-0 max-w-full cursor-pointer text-blue-500">
                    {{ row.code }}
                  </span>
                </router-link>
              </template>
            </VxeColumn>
            <VxeColumn fixed="left" field="sexCode" title="性别" width="80">
              <template #default="{ row }: { row: SizeListPageAPI.Row }">
                {{ getSizeSex(row.sexCode) }}
              </template>
            </VxeColumn>
            <VxeColumn field="sizeType" min-width="100" title="尺码集分类">
              <template #default="{ row }: { row: SizeListPageAPI.Row }">
                {{ getSizeCodeType(row.sizeType) }}
              </template>
            </VxeColumn>
            <VxeColumn field="name" min-width="100" title="尺码集名称" />
            <VxeColumn field="segment" min-width="100" title="尺码段" />
            <VxeColumn
              align="left"
              field="sizeValue"
              header-align="center"
              min-width="200"
              title="尺码值"
            />
            <VxeColumn field="standardValue" min-width="100" title="标准码" />
            <VxeColumn field="status" fixed="right" title="状态" width="80" />
            <VxeColumn field="operator" title="操作人" width="120" />
            <VxeColumn field="modifyTime" title="操作时间" width="90" />
            <VxeColumn :show-overflow="false" fixed="right" title="操作" width="120">
              <template #default="{ row }">
                <ElButton size="small" text type="primary" @click="handleOpenVersionDialog(row)">
                  版本记录
                </ElButton>
              </template>
            </VxeColumn>
          </VxeTable>
        </div>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
      <VersionDialog v-model="versionDialogVisible" :current-row="currentRow" />
      <StatusDialog
        v-model="statusDialogVisible"
        :selected-rows="selectedRows"
        @refresh="handleQuery"
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped></style>
