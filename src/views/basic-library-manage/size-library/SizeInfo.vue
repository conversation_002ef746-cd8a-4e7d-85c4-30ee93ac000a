<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import type { VxeTableInstance } from 'vxe-table'
import {
  createSize,
  editSize,
  SizeInfoAPI,
  viewSize,
  viewSizeByVersionId
} from '@/views/basic-library-manage/size-library/api/sizeInfo'
import { statusConst, StatusEnum } from '@/views/basic-library-manage/const'
import Sortable from 'sortablejs'
import { Icon } from '@/components/Icon'
import { useBasicLibraryDictStore } from '@/views/basic-library-manage/store/dict'

defineOptions({
  name: 'SizeInfo'
})

const route = useRoute()

const useType = () => {
  const isCreate = computed(() => {
    return route.name === 'CreateSize'
  })

  const isEdit = computed(() => {
    return route.name === 'EditSize'
  })

  const isView = computed(() => {
    return route.name === 'ViewSize'
  })

  const isCopy = computed(() => {
    return route.name === 'CopySize'
  })

  return {
    isCreate,
    isEdit,
    isView,
    isCopy
  }
}

const { isCreate, isEdit, isView, isCopy } = useType()

const id = computed(() => {
  return route.query.id
})

const versionId = computed(() => {
  return route.query.versionId
})

const useConst = () => {
  const dictStore = useBasicLibraryDictStore()
  const store = computed(() => ({
    sizeTypeList: dictStore.sizeTypeList,
    sizeSexList: dictStore.sizeSexList
  }))

  // 状态枚举
  const statusList = statusConst.statusList
  const statusMap = statusConst.statusMap

  return {
    store,
    statusMap,
    statusList
  }
}

const { store, statusMap, statusList } = useConst()

const activeNames = ref(['1', '2'])

type FormModel = SizeInfoAPI.Data & {
  needApprove?: boolean
}
const formRef = ref<FormInstance>()
const formData = ref<FormModel>({
  segment: '',
  statusCode: '',
  id: undefined,
  needApprove: undefined,
  name: '',
  sexCode: '',
  typeCode: '',
  remark: '',
  sizeValueList: []
})
const formCouldEdit = computed(() => {
  return (
    isCreate.value ||
    isCopy.value ||
    formData.value.statusCode === StatusEnum.DRAFT ||
    (!formData.value.referenced && formData.value.statusCode === StatusEnum.START)
  )
})
const referencedWatcher = watch(formData, () => {
  if (!formCouldEdit.value) {
    ElMessage.warning('当前尺码集信息已被产品或楦型等引用')
  }
})

onBeforeRouteLeave(referencedWatcher)

const formRules = ref<FormRules<FormModel>>({
  sexCode: [
    {
      required: true,
      message: '请选择性别',
      trigger: 'change'
    }
  ],
  name: [
    {
      required: true,
      message: '请输入尺码集名称',
      trigger: 'blur'
    }
  ],
  segment: [
    {
      required: true,
      message: '请输入尺码段',
      trigger: 'blur'
    },
    {
      validator: (_, value, callback) => {
        if (typeof value === 'string' && value.length <= 100) {
          callback()
        } else {
          callback('尺码段最大长度为100!')
        }
      },
      trigger: 'blur'
    }
  ],
  typeCode: [
    {
      required: true,
      message: '请选择尺码分类',
      trigger: 'change'
    }
  ],
  sizeType: [
    {
      required: true,
      message: '请选择尺码集分类',
      trigger: 'change'
    }
  ]
})

const submitFn = computed(() => {
  if (isCreate.value || isCopy.value) {
    return createSize
  }
  if (isEdit.value) {
    return editSize
  }
  return createSize
})

const handleClose = () => {
  useClosePage('SizeLibrary')
}
const submitLoading = ref(false)
const router = useRouter()

async function handleConfirm() {
  if (isView.value) {
    router.push({
      name: 'EditSize',
      query: {
        id: id.value
      }
    })
    return
  }
  await handleSubmit()
}

async function handleSubmit() {
  const valid = await formRef.value?.validate()
  if (valid) {
    const hasStandard = formData.value.sizeValueList?.some((e) => {
      return e.standard
    })
    if (!hasStandard) {
      ElMessage.error('请选择标准码')
      return
    }
    const hasSizeValueRepeat = formData.value.sizeValueList?.some((e) => {
      return (
        !e.sizeValue ||
        new Set(formData.value.sizeValueList?.map((item) => item.sizeValue)).size !==
          formData.value.sizeValueList?.length
      )
    })
    if (hasSizeValueRepeat) {
      ElMessage.error('请检查尺码值是否填写或重复')
      return
    }
    submitLoading.value = true
    if (isCreate.value || isCopy.value || formData.value.statusCode === StatusEnum.DRAFT) {
      let isClose = false
      await ElMessageBox({
        title: '操作确认',
        message: '确认发起飞书审批',
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '保存为草稿状态',
        showCancelButton: true,
        distinguishCancelAndClose: true,
        beforeClose: (action, _, done) => {
          if (action === 'confirm') {
            formData.value.needApprove = true
            done()
          } else if (action === 'cancel') {
            formData.value.needApprove = false
            done()
          } else {
            isClose = true
            done()
          }
        }
      }).catch(() => {})
      if (isClose) {
        submitLoading.value = false
        return
      }
    }
    if (isEdit.value) {
      formData.value.id = Number(id.value)
    }
    const [error, result] = await submitFn.value(formData.value)
    submitLoading.value = false
    if (error === null && result) {
      handleClose()
      ElMessage.success(result.msg || '保存成功')
    } else {
      ElMessage.error(error?.message || '保存失败')
    }
  }
}

const useGetSizeInfo = async () => {
  if ((isEdit.value || isView.value || isCopy.value) && (id.value || versionId.value)) {
    const loading = ElLoading.service({
      fullscreen: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    const [error, result] = await (id.value
      ? viewSize(+id.value)
      : viewSizeByVersionId(+versionId.value!))
    loading.close()
    if (error === null && result?.datas) {
      formData.value = result.datas
      formData.value.sizeValueList = result.datas.sizeValueList?.map((e) => {
        return {
          ...e,
          disabled: !(formData.value.statusCode === StatusEnum.DRAFT) && !isCopy.value
        }
      })
    }
  }
}

const tableRef = ref<VxeTableInstance>()
const bindRowDrop = () => {
  let sortable: Sortable
  onMounted(() => {
    const { refTableBody } = tableRef.value?.getRefMaps() || {}
    const el = refTableBody?.value?.$el
    const tableBody = el?.querySelector('.vxe-table--body tbody')
    sortable = Sortable.create(tableBody, {
      handle: '.row-drag',
      onEnd: ({ newIndex, oldIndex }) => {
        if (
          newIndex === oldIndex ||
          newIndex === null ||
          oldIndex === null ||
          !formData.value.sizeValueList ||
          oldIndex === undefined ||
          newIndex === undefined ||
          isView.value
        ) {
          tableRef.value?.loadData([]).then(() => {
            tableRef.value?.loadData(formData.value.sizeValueList || [])
          })
          return
        }
        const currRow = formData.value.sizeValueList[oldIndex]
        formData.value.sizeValueList.splice(oldIndex, 1)
        formData.value.sizeValueList.splice(newIndex, 0, currRow!)
        tableRef.value?.loadData(formData.value.sizeValueList)
      }
    })
  })
  onBeforeUnmount(() => {
    sortable.destroy()
  })
}
const handleStandardChange = ({ checked, row }) => {
  if (checked) {
    // 清除其他行选中
    tableRef.value?.clearCheckboxRow()
    tableRef.value?.setCheckboxRow(row, true)
  } else {
    // 如果没有其他选中行，不允许取消选中
    const checked = tableRef.value?.getCheckboxRecords()
    if (!checked?.length) {
      tableRef.value?.setCheckboxRow(row, true)
    }
  }
}

!isView.value && bindRowDrop()
const handleEditSize = () => {
  const status = formData.value.statusCode
  if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
    ElMessage.warning('审批中/禁用数据不允许修改')
    return
  }
  router.push({
    name: 'EditSize',
    query: {
      id: formData.value.id
    }
  })
}
Promise.all([useGetSizeInfo()])
onActivated(useGetSizeInfo)
</script>

<template>
  <ContentWrap class="info-wrapper">
    <ElCollapse v-model="activeNames">
      <ElCollapseItem name="1">
        <template #title>
          <div class="font-bold text-base">基础信息</div>
        </template>
        <ElForm
          ref="formRef"
          :disabled="isView"
          :model="formData"
          :rules="formRules"
          :scroll-into-view-options="{ behavior: 'smooth' }"
          class="mt-2"
          labelWidth="auto"
          scroll-to-error
        >
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="性别" prop="sexCode">
                <ElSelect
                  v-model="formData.sexCode"
                  :disabled="!formCouldEdit"
                  clearable
                  placeholder="请选择性别"
                >
                  <ElOption
                    v-for="e in store.sizeSexList"
                    :key="e.dictValue"
                    :label="e.dictCnName"
                    :value="e.dictValue!"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="尺码集名称" prop="name">
                <ElInput
                  v-model="formData.name"
                  :disabled="!formCouldEdit"
                  class="w-48"
                  clearable
                  maxlength="100"
                  placeholder="女鞋尺码集-单码-美码"
                  showWordLimit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="尺码段" prop="segment">
                <ElInput
                  v-model="formData.segment"
                  :disabled="!formCouldEdit"
                  class="w-48"
                  clearable
                  placeholder="请输入尺码段，例如：36-49(EUR)"
                  showWordLimit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="尺码分类" prop="typeCode">
                <ElSelect
                  v-model="formData.typeCode"
                  :disabled="!formCouldEdit"
                  clearable
                  placeholder="请选择尺码分类"
                >
                  <ElOption
                    v-for="e in store.sizeTypeList"
                    :key="e.dictValue"
                    :label="e.dictCnName"
                    :value="e.dictValue!"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="尺码集分类" prop="sizeType">
                <SelectPlus
                  v-model="formData.sizeType"
                  api-key="SIZE_CODE_TYPE"
                  clearable
                  placeholder="请选择尺码集分类"
                />
              </ElFormItem>
            </ElCol>
            <ElCol v-if="isView || isEdit" :span="12">
              <ElFormItem label="状态" prop="status">
                <ElRadioGroup v-model="formData.statusCode" disabled>
                  <ElRadio
                    v-for="e in statusList"
                    :key="e.value"
                    :label="e.label"
                    :value="e.value"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :offset="isView || isEdit ? 0 : 12" :span="12">
              <ElFormItem label="备注" prop="remark">
                <ElInput
                  v-model="formData.remark"
                  :autosize="{ minRows: 4, maxRows: 6 }"
                  :resize="isView ? 'none' : undefined"
                  class="w-48"
                  maxlength="500"
                  show-word-limit
                  type="textarea"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
      </ElCollapseItem>
      <ElCollapseItem name="2">
        <template #title>
          <div class="font-bold text-base">尺码值信息</div>
        </template>
        <ElButton
          v-if="!isView"
          class="my-2"
          text
          type="primary"
          @click="
            () => {
              formData.sizeValueList?.push({
                standard: false,
                status: StatusEnum.START
              })
              tableRef?.loadData(formData.sizeValueList || [])
            }
          "
        >
          添加
        </ElButton>
        <VxeTable
          ref="tableRef"
          :checkbox-config="{
            highlight: true,
            checkField: 'standard',
            showHeader: false,
            checkMethod: () => !isView && formCouldEdit
          }"
          :data="formData.sizeValueList!"
          :row-config="{ useKey: true }"
          align="center"
          border
          @checkbox-change="handleStandardChange"
        >
          <VxeColumn
            :show-header-overflow="false"
            :show-overflow="false"
            class-name="drag-cell"
            header-class-name="drag-header-cell"
            width="40"
          >
            <template #default>
              <span
                :class="isView || !formCouldEdit ? 'cursor-not-allowed' : 'cursor-move'"
                class="row-drag inline-flex"
              >
                <Icon icon="ep:rank" />
              </span>
            </template>
            <template #header>
              <ElTooltip content="按住后可以上下拖动排序！" enterable>
                <Icon icon="ep:question-filled" />
              </ElTooltip>
            </template>
          </VxeColumn>
          <VxeColumn title="序号" type="seq" />
          <VxeColumn v-if="!isCreate" field="code" title="尺码值编码" />
          <VxeColumn title="尺码值">
            <template #default="{ row }: { row: SizeInfoAPI.BaseSizeValueResp }">
              <span v-if="isView">{{ row.sizeValue }}</span>
              <ElInput
                v-else
                v-model="row.sizeValue"
                :disabled="row.disabled"
                clearable
                placeholder="请输入尺码值"
              />
            </template>
          </VxeColumn>
          <VxeColumn title="标准码" type="checkbox" />
          <VxeColumn field="status" title="状态">
            <template #default="{ row }: { row: SizeInfoAPI.BaseSizeValueResp }">
              <ElSelect
                v-model="row.status"
                :disabled="isCreate || isView"
                filterable
                placeholder="请选择状态"
              >
                <template v-for="(value, key) in statusMap" :key="key">
                  <ElOption :label="value" :value="key" />
                </template>
              </ElSelect>
            </template>
          </VxeColumn>
          <VxeColumn v-if="!isCreate" field="wmsSizeValue" title="WMS尺码值" />
          <VxeColumn v-if="!isCreate" field="wmsCode" title="WMS尺码编码" />
          <VxeColumn :show-overflow="false" title="操作">
            <template #default="{ row, rowIndex }">
              <span v-if="isView"></span>
              <ElButton
                v-else
                :disabled="row.disabled"
                text
                type="danger"
                @click="() => {
                  formData.sizeValueList!.splice(rowIndex, 1);
                  tableRef?.loadData(formData.sizeValueList!)
                }"
              >
                删除
              </ElButton>
            </template>
          </VxeColumn>
        </VxeTable>
      </ElCollapseItem>
    </ElCollapse>
  </ContentWrap>
  <ContentWrap class="mt-2">
    <div class="text-center">
      <ElButton @click="handleClose">返回</ElButton>
      <ElButton v-if="!isView" :loading="submitLoading" type="primary" @click="handleSubmit">
        确定
      </ElButton>
      <ElButton
        v-if="
          formData.statusCode !== StatusEnum.APPROVING &&
          formData.statusCode !== StatusEnum.BAN &&
          isView
        "
        v-hasPermi="['editSize']"
        type="primary"
        @click="handleEditSize"
      >
        <Icon size="20" icon="ep:edit" />
        修改尺码集
      </ElButton>
    </div>
  </ContentWrap>
</template>

<style scoped>
:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}
</style>

<style lang="less">
.info-wrapper {
  max-height: calc(100vh - 250px);
  overflow: auto;
}

.drag-cell {
  & > .vxe-cell {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.drag-header-cell > .vxe-cell {
  &,
  & > .vxe-cell--title {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
