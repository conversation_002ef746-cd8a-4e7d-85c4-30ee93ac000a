import { useConstants } from '@/hooks/web/useConstants'

export enum ColorLibraryEnum {
  /**
   * 潘通色库
   */
  PANTONE = 'PANTONE',
  /**
   * 品牌色库
   */
  BRAND = 'BRAND',
  /**
   * JF色库
   */
  JF = 'JF'
}

export const colorLibraryMap = {
  [ColorLibraryEnum.PANTONE]: '潘通色库',
  [ColorLibraryEnum.BRAND]: '品牌色库',
  [ColorLibraryEnum.JF]: 'JF色库'
}

const { list: colorLibraryList } = useConstants(colorLibraryMap)

export const colorLibraryConst = {
  colorLibraryMap,
  colorLibraryList
}

export interface ColorCategoryAttr {
  label: string
  value: {
    [key in keyof typeof colorLibraryMap]?: string
  }
}

export const COMB_COLOR = 'CC'
