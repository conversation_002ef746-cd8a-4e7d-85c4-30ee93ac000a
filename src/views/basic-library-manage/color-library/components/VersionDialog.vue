<script setup lang="ts">
import { ColorListPageAPI } from '../api/color-list'
import { getColorVersionList } from '../api/versionDialog'
import { VersionListAPI } from '../../api/common'

defineOptions({
  name: 'VersionDialog'
})

const props = defineProps<{
  modelValue: boolean
  currentRow: ColorListPageAPI.Row | undefined
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const tableData = ref<VersionListAPI.Data[]>([])
const queryLoading = ref(false)
const getVersionList = async () => {
  if (!props.currentRow) {
    return
  }
  queryLoading.value = true
  const [error, result] = await getColorVersionList(props.currentRow.id!)
  queryLoading.value = false
  if (error === null && result?.datas) {
    tableData.value = result.datas
  }
}

watch(visible, () => {
  if (visible.value) {
    getVersionList()
  }
})

const handleClose = () => {
  visible.value = false
  tableData.value = []
}

const router = useRouter()
const handleViewDetail = (row: VersionListAPI.Data) => {
  router.push({
    name: 'ViewColor',
    query: {
      versionId: row.id
    }
  })
  handleClose()
}
</script>

<template>
  <Dialog v-model="visible" title="版本记录" :before-close="handleClose">
    <VxeTable :loading="queryLoading" :data="tableData" :max-height="500">
      <VxeColumn field="versionCode" title="版本号" />
      <VxeColumn field="versionType" title="版本变更类型" />
      <VxeColumn field="operator" title="修改者" />
      <VxeColumn field="dataTime" title="修改时间" />
      <VxeColumn field="versionTime" title="版本生效时间" />
      <VxeColumn field="status" title="版本状态" />
      <VxeColumn field="versionRemark" title="版本说明" />
      <VxeColumn title="修改明细">
        <template #default="{ row }">
          <el-button type="primary" text size="small" @click="handleViewDetail(row)">
            查看详情
          </el-button>
        </template>
      </VxeColumn>
    </VxeTable>
  </Dialog>
</template>

<style scoped lang="less"></style>
