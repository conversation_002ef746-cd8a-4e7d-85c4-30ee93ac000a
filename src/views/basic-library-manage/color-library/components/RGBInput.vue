<script setup lang="ts">
defineOptions({
  name: 'RGBInput'
})

interface Props {
  modelValue?: string
  red?: number
  green?: number
  blue?: number
  rgb?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  red: 0,
  green: 0,
  blue: 0,
  rgb: ''
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'update:red', value: number): void
  (e: 'update:green', value: number): void
  (e: 'update:blue', value: number): void
  (e: 'update:rgb', value: string): void
}>()

const hexValue = computed(() => {
  return props.modelValue.replace(/^#/, '')
})

const rgbValue = computed(() => {
  return props.rgb?.split(',') || []
})

const convertToHex = (value: number) => {
  return (value || '').toString(16).padStart(2, '0')
}

const getColor = (variant: 'red' | 'green' | 'blue') => {
  let val: number
  const index = variant === 'red' ? 0 : variant === 'green' ? 1 : 2

  if (props.modelValue !== '') {
    val = parseInt(
      hexValue.value.slice(
        variant === 'red' ? 0 : variant === 'green' ? 2 : 4,
        variant === 'red' ? 2 : variant === 'green' ? 4 : 6
      ),
      16
    )
  } else if (props.rgb) {
    val = +rgbValue.value[index]
  } else {
    val = variant === 'red' ? props.red : variant === 'green' ? props.green : props.blue
  }
  return isNaN(val) ? 0 : val
}

const redValue = computed({
  get() {
    return getColor('red')
  },
  set(val) {
    const color = `#${convertToHex(val)}${convertToHex(greenValue.value)}${convertToHex(
      blueValue.value
    )}`
    emit('update:modelValue', color)
    emit('update:red', val)
    emit('update:rgb', `${val},${greenValue.value},${blueValue.value}`)
  }
})

const greenValue = computed({
  get() {
    return getColor('green')
  },
  set(val) {
    const color = `#${convertToHex(redValue.value)}${convertToHex(val)}${convertToHex(
      blueValue.value
    )}`
    emit('update:modelValue', color)
    emit('update:green', val)
    emit('update:rgb', `${redValue.value},${val},${blueValue.value}`)
  }
})

const blueValue = computed({
  get() {
    return getColor('blue')
  },
  set(val) {
    const color = `#${convertToHex(redValue.value)}${convertToHex(greenValue.value)}${convertToHex(
      val
    )}`
    emit('update:modelValue', color)
    emit('update:blue', val)
    emit('update:rgb', `${redValue.value},${greenValue.value},${val}`)
  }
})

watch(
  () => props.modelValue,
  (val) => {
    if (!val) return
    emit('update:red', redValue.value)
    emit('update:green', greenValue.value)
    emit('update:blue', blueValue.value)
    emit('update:rgb', `${redValue.value},${greenValue.value},${blueValue.value}`)
  }
)

watch(
  () => props.rgb,
  (val) => {
    if (!val) return
    emit('update:red', redValue.value)
    emit('update:green', greenValue.value)
    emit('update:blue', blueValue.value)
    emit(
      'update:modelValue',
      `#${convertToHex(redValue.value)}${convertToHex(greenValue.value)}${convertToHex(
        blueValue.value
      )}`
    )
  }
)
</script>

<template>
  <div class="flex flex-nowrap">
    <div>
      <div class="text-center text-red-500">R</div>
      <ElInputNumber
        class="!w-20"
        :min="0"
        :max="255"
        controls-position="right"
        v-model="redValue"
      />
    </div>
    <div class="ml-2">
      <div class="text-center text-red-500">G</div>
      <ElInputNumber
        class="!w-20"
        :min="0"
        :max="255"
        controls-position="right"
        v-model="greenValue"
      />
    </div>
    <div class="ml-2">
      <div class="text-center text-red-500">B</div>
      <ElInputNumber
        class="!w-20"
        :min="0"
        :max="255"
        controls-position="right"
        v-model="blueValue"
      />
    </div>
  </div>
</template>

<style scoped lang="less"></style>
