<template>
  <div :class="ns.be('dropdown', 'main-wrapper')" class="flex">
    <sv-panel ref="sv" :color="color" :disabled="colorDisabled" />
    <hue-slider
      ref="hue"
      :color="color"
      :disabled="colorDisabled"
      class="hue-slider ml-2"
      vertical
    />
  </div>
  <alpha-slider v-if="showAlpha" ref="alpha" :color="color" />
  <predefine v-if="predefine" ref="predefine" :color="color" :colors="predefine" />
  <div :class="ns.be('dropdown', 'btns')" class="!flex items-center ml-2">
    <span :class="ns.be('dropdown', 'value')">
      <el-input
        ref="inputRef"
        v-model="customInput"
        :validate-event="false"
        size="small"
        @change="handleConfirm"
        @keyup.enter="handleConfirm"
      />
    </span>
    <el-button :class="ns.be('dropdown', 'link-btn')" size="small" text @click="clear">
      {{ t('el.colorpicker.clear') }}
    </el-button>
    <el-button :class="ns.be('dropdown', 'btn')" size="small" @click="confirmValue">
      {{ t('el.colorpicker.confirm') }}
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, provide, reactive, ref, watch } from 'vue'
import { debounce } from 'lodash-es'
import { ElButton, ElInput } from 'element-plus'
import { useFormDisabled, useFormItem } from 'element-plus/es/components/form/index.mjs'
import { useLocale, useNamespace } from 'element-plus/es/hooks/index.mjs'
import { UPDATE_MODEL_EVENT } from 'element-plus/es/constants/index.mjs'
import { debugWarn } from 'element-plus/es/utils/index.mjs'
import AlphaSlider from 'element-plus/es/components/color-picker/src/components/alpha-slider.mjs'
import HueSlider from '@/views/basic-library-manage/color-library/components/HueSlider.vue'
import Predefine from 'element-plus/es/components/color-picker/src/components/predefine.mjs'
import SvPanel from '@/views/basic-library-manage/color-library/components/SvPanel.vue'
import Color from 'element-plus/es/components/color-picker/src/utils/color.mjs'
import {
  colorPickerContextKey,
  colorPickerEmits,
  colorPickerProps
} from 'element-plus/es/components/color-picker/index.mjs'
import 'element-plus/theme-chalk/el-color-picker.css'

defineOptions({
  name: 'ElColorPicker'
})
const props = defineProps(colorPickerProps)
const emit = defineEmits(colorPickerEmits)

const { t } = useLocale()
const ns = useNamespace('color')
const { formItem } = useFormItem()
const colorDisabled = useFormDisabled()

const hue = ref<InstanceType<typeof HueSlider>>()
const sv = ref<InstanceType<typeof SvPanel>>()
const alpha = ref<InstanceType<typeof AlphaSlider>>()
const triggerRef = ref()
const inputRef = ref()

// active-change is used to prevent modelValue changes from triggering.
let shouldActiveChange = true

const color = reactive(
  new Color({
    enableAlpha: props.showAlpha,
    format: props.colorFormat || '',
    value: props.modelValue
  })
) as Color

const showPicker = ref(false)
const showPanelColor = ref(false)
const customInput = ref('')

const currentColor = computed(() => {
  return !props.modelValue && !showPanelColor.value ? '' : color.value
})

function setShowPicker(value: boolean) {
  showPicker.value = value
}

const debounceSetShowPicker = debounce(setShowPicker, 100, { leading: true })

function show() {
  if (colorDisabled.value) return
  setShowPicker(true)
}

function hide() {
  debounceSetShowPicker(false)
  resetColor()
}

function resetColor() {
  nextTick(() => {
    if (props.modelValue) {
      color.fromString(props.modelValue)
    } else {
      color.value = ''
      nextTick(() => {
        showPanelColor.value = false
      })
    }
  })
}

function handleConfirm() {
  color.fromString(customInput.value)
}

function confirmValue() {
  const value = color.value
  emit(UPDATE_MODEL_EVENT, value)
  emit('change', value)
  if (props.validateEvent) {
    formItem?.validate('change').catch((err) => debugWarn(err))
  }
  debounceSetShowPicker(false)
  // check if modelValue change, if not change, then reset color.
  nextTick(() => {
    const newColor = new Color({
      enableAlpha: props.showAlpha,
      format: props.colorFormat || '',
      value: props.modelValue
    })
    if (!color.compare(newColor)) {
      resetColor()
    }
  })
}

function clear() {
  debounceSetShowPicker(false)
  emit(UPDATE_MODEL_EVENT, null)
  emit('change', null)
  if (props.modelValue !== null && props.validateEvent) {
    formItem?.validate('change').catch((err) => debugWarn(err))
  }
  resetColor()
}

function focus() {
  triggerRef.value.focus()
}

function blur() {
  triggerRef.value.blur()
}

onMounted(() => {
  if (props.modelValue) {
    customInput.value = currentColor.value
  }
})

watch(
  () => props.modelValue,
  (newVal) => {
    if (!newVal) {
      showPanelColor.value = false
    } else if (newVal && newVal !== color.value) {
      //      shouldActiveChange = false
      color.fromString(newVal)
    }
  }
)

watch(
  () => currentColor.value,
  (val) => {
    customInput.value = val
    shouldActiveChange && emit('activeChange', val)
    shouldActiveChange = true
  }
)

watch(
  () => color.value,
  () => {
    if (!props.modelValue && !showPanelColor.value) {
      showPanelColor.value = true
    }
  }
)

watch(
  () => showPicker.value,
  () => {
    nextTick(() => {
      hue.value?.update()
      sv.value?.update()
      alpha.value?.update()
    })
  }
)

provide(colorPickerContextKey, {
  currentColor
})

defineExpose({
  /**
   * @description current color object
   */
  color,
  /**
   * @description manually show ColorPicker
   */
  show,
  /**
   * @description manually hide ColorPicker
   */
  hide,
  /**
   * @description focus the input element
   */
  focus,
  /**
   * @description blur the input element
   */
  blur
})
</script>

<style lang="less" scoped>
.disabled > :deep(.el-color-svpanel__cursor) {
  display: none;
}
</style>
