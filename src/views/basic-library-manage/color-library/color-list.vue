<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { ref } from 'vue'
import { Icon } from '@/components/Icon'
import {
  ColorCategoryListAPI,
  ColorListPageAPI,
  getColorCategoryList,
  getColorListByPage
} from './api/color-list'
import { Pager } from '../api/common'
import { colorLibraryConst, ColorLibraryEnum } from './const'
import type { ElPagination, FormInstance } from 'element-plus'
import { ElCollapseTransition, ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import type { VxeTableInstance } from 'vxe-table'
import { approvalStatusConst, ApprovalStatusEnum, statusConst, StatusEnum } from '../const'
import type { VxeTablePropTypes } from 'vxe-table/types'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import VersionDialog from './components/VersionDialog.vue'
import StatusDialog from './components/StatusDialog.vue'
import { isEqual } from 'lodash-es'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
const { t } = useI18n()

defineOptions({
  name: 'ColorLibrary'
})

const props = defineProps<{
  isEmbed?: boolean
}>()

const router = useRouter()

const useConst = () => {
  // 颜色库枚举
  const colorLibraryList = colorLibraryConst.colorLibraryList

  // 状态枚举
  const statusMap = statusConst.statusMap
  const statusList = statusConst.statusList

  // 审批状态枚举
  const approvalStatusMap = approvalStatusConst.approvalStatusMap

  // 色系下拉列表
  const colorCategoryList = ref<ColorCategoryListAPI.Data[]>([])
  const useFetchColorCategoryList = async () => {
    const [error, result] = await getColorCategoryList()
    if (error === null && result?.datas) {
      colorCategoryList.value = result.datas
    }
  }

  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '113'
    },
    {
      formLabelLength: '180'
    }
  ])

  return {
    colorLibraryList,
    statusMap,
    statusList,
    approvalStatusMap,
    colorCategoryList,
    useFetchColorCategoryList,
    formLabelLength
  }
}

const {
  colorLibraryList,
  statusMap,
  statusList,
  approvalStatusMap,
  useFetchColorCategoryList,
  colorCategoryList,
  formLabelLength
} = useConst()

const useQuery = () => {
  interface FormModel
    extends Omit<ColorListPageAPI.Params, 'startTime' | 'endTime' | 'current' | 'size'> {
    date?: [string, string]
  }

  const defaultFormData: FormModel = {
    categoryCode: [],
    code: '',
    englishName: '',
    library: [ColorLibraryEnum.BRAND, ColorLibraryEnum.JF],
    name: '',
    status: props.isEmbed ? [StatusEnum.START] : []
  }
  let lastFormData = {
    ...defaultFormData
  }
  const formData = ref<FormModel>({
    ...defaultFormData
  })
  const formRef = ref<FormInstance>()
  const tableRef = ref<VxeTableInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const tableData = ref<ColorListPageAPI.List>([])
  const queryLoading = ref(false)
  const pager = ref<Pager>({
    current: 1,
    size: 10,
    total: 0
  })
  const queryParams = computed<ColorListPageAPI.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1],
      date: undefined
    }
  })
  const defaultTime: [Date, Date] = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ]

  const brandCategoryList = computed(() => {
    return colorCategoryList.value
      .filter((e) => e.value.BRAND)
      .map((e) => ({ ...e, value: e.value.BRAND }))
  })

  const pantoneCategoryList = computed(() => {
    return colorCategoryList.value
      .filter((e) => e.value.PANTONE)
      .map((e) => ({ ...e, value: e.value.PANTONE }))
  })

  const categoryList = computed(() => {
    if (formData.value.library?.length === 1) {
      if (formData.value.library[0] === ColorLibraryEnum.BRAND) {
        return brandCategoryList.value
      } else {
        return pantoneCategoryList.value
      }
    } else {
      return colorCategoryList.value.map((e) => {
        const list: string[] = []
        if (e.value.BRAND) {
          list.push(e.value.BRAND)
        }
        if (e.value.PANTONE) {
          list.push(e.value.PANTONE)
        }
        return {
          ...e,
          value: list.join(',')
        }
      })
    }
  })

  watch(
    () => formData.value.library,
    () => {
      formData.value.categoryCode = []
    }
  )

  let controller: AbortController | null = null
  const handleQuery = async () => {
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await getColorListByPage(queryParams.value, controller.signal)
    queryLoading.value = false
    if (error === null && result?.datas) {
      lastFormData = { ...formData.value }
      const { records } = result.datas
      tableData.value = records
      pager.value.total = result.datas.pager.total
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
    handleQuery()
  }

  onActivated(handleQuery)

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = () => {
    const selected: ColorListPageAPI.List | undefined = tableRef.value?.getCheckboxRecords()
    let reqParam: string
    if (selected && selected.length > 0) {
      reqParam = JSON.stringify({ idList: selected.map((item) => item.id) })
    } else {
      reqParam = JSON.stringify(queryParams.value)
    }
    exportFn({
      exportType: 'color-export',
      reqParam
    })
  }

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef,
    offsetBottom: props.isEmbed ? 50 + 20 + 32 + 10 + 30 : 0
  })

  const setRejectedCellClassName: VxeTablePropTypes.CellClassName = ({ row, column }) => {
    if (
      row.dataStatus === approvalStatusMap[ApprovalStatusEnum.REJECTION] &&
      column.field === 'status'
    ) {
      return 'bg-red-400 text-white'
    }
  }

  return {
    formRef,
    formData,
    queryLoading,
    pager,
    defaultTime,
    handleQuery,
    handleReset,
    handleExport,
    exportLoading,
    visible,
    setVisible,
    tableRef,
    pagerRef,
    tableData,
    maxHeight,
    categoryList,
    setRejectedCellClassName
  }
}

const {
  formRef,
  formData,
  queryLoading,
  pager,
  defaultTime,
  handleQuery,
  handleReset,
  handleExport,
  exportLoading,
  visible,
  setVisible,
  tableRef,
  pagerRef,
  tableData,
  maxHeight,
  categoryList,
  setRejectedCellClassName
} = useQuery()

// 版本记录弹窗
const useVersionDialog = () => {
  const versionDialogVisible = ref(false)
  const setVersionDialogVisible = () => {
    versionDialogVisible.value = !unref(versionDialogVisible)
  }
  return {
    versionDialogVisible,
    setVersionDialogVisible
  }
}

// 启用/禁用弹窗
const useStatusDialog = () => {
  const statusDialogVisible = ref(false)
  const setStatusDialogVisible = () => {
    statusDialogVisible.value = !unref(statusDialogVisible)
  }
  return {
    statusDialogVisible,
    setStatusDialogVisible
  }
}

const useOperation = () => {
  const currentRow = ref<ColorListPageAPI.Row>()
  const selectedRows = ref<ColorListPageAPI.List>([])

  // 修改颜色
  const handleEditColor = () => {
    const selectedRows: ColorListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    const status = selectedRows[0].statusCode
    if (status === StatusEnum.APPROVING || status === StatusEnum.BAN) {
      ElMessage.warning('审批中/禁用数据不允许修改')
      return
    }
    router.push({
      name: 'EditColor',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 复制颜色
  const handleCopyColor = () => {
    const selectedRows: ColorListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    router.push({
      name: 'CopyColor',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 启用/禁用
  const { statusDialogVisible, setStatusDialogVisible } = useStatusDialog()
  const handleChangeStatus = () => {
    const checkedRecords: ColorListPageAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!checkedRecords?.length) {
      ElMessage.warning('请选择数据')
      return
    }
    const hasApproving = checkedRecords.some((item) => item.status === statusMap['approving'])
    if (hasApproving) {
      ElMessage.warning('存在审批中的数据')
      return
    }
    selectedRows.value = checkedRecords
    setStatusDialogVisible()
  }

  // 新增颜色
  const handleCreateColor = () => {
    router.push({
      name: 'CreateColor'
    })
  }

  // 版本记录
  const { versionDialogVisible, setVersionDialogVisible } = useVersionDialog()
  const handleOpenVersionDialog = (row: ColorListPageAPI.Row) => {
    currentRow.value = row
    setVersionDialogVisible()
  }

  return {
    currentRow,
    selectedRows,
    handleCopyColor,
    handleEditColor,
    handleCreateColor,
    versionDialogVisible,
    handleOpenVersionDialog,
    handleChangeStatus,
    statusDialogVisible
  }
}

const {
  handleCopyColor,
  handleEditColor,
  handleCreateColor,
  versionDialogVisible,
  handleOpenVersionDialog,
  currentRow,
  selectedRows,
  handleChangeStatus,
  statusDialogVisible
} = useOperation()

Promise.all([handleQuery(), useFetchColorCategoryList()])

defineExpose({
  tableRef
})
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_250px)] overflow-x-hidden overflow-y-auto">
        <ElForm ref="formRef" :label-width="formLabelLength" :model="formData">
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="颜色英文名称" prop="englishName">
                <ElInput
                  v-model="formData.englishName"
                  clearable
                  placeholder="请输入颜色英文名称，支持模糊查询"
                  @change="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="颜色编码" prop="code">
                <ElInput
                  v-model="formData.code"
                  clearable
                  placeholder="请输入颜色编码，支持模糊查询"
                  @change="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="" label-width="0">
                <ElButton text @click="setVisible">
                  {{ visible ? '收起' : '展开' }}
                  <Icon
                    :class="visible ? 'rotate-90' : ''"
                    class="transform transition duration-400"
                    icon="ant-design:down-outlined"
                  />
                </ElButton>
                <ElButton :loading="queryLoading" type="primary" @click="handleQuery">
                  <Icon class="mr-1" icon="ep:search" />
                  查询
                </ElButton>
                <ElButton @click="handleReset">
                  <Icon class="mr-1" icon="ep:refresh-right" />
                  重置
                </ElButton>
                <ElButton
                  v-if="!isEmbed"
                  :loading="exportLoading"
                  type="primary"
                  @click="handleExport"
                >
                  <Icon class="mr-1" icon="ep:upload-filled" />
                  导出
                </ElButton>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="颜色库" prop="library">
                <ElScrollbar>
                  <ElCheckboxGroup
                    v-model="formData.library"
                    :disabled="isEmbed"
                    class="flex flex-nowrap"
                    @change="handleQuery"
                  >
                    <ElCheckboxButton
                      v-for="item in colorLibraryList"
                      :key="item.value"
                      :value="item.value"
                      >{{ t(item.label) }}</ElCheckboxButton
                    >
                  </ElCheckboxGroup>
                </ElScrollbar>
              </ElFormItem>
            </ElCol>
            <ElCollapseTransition>
              <div v-show="visible" class="flex flex-wrap w-full">
                <ElCol :span="8">
                  <ElFormItem label="状态" prop="status">
                    <ElScrollbar>
                      <ElCheckboxGroup
                        v-model="formData.status"
                        :disabled="isEmbed"
                        class="flex flex-nowrap"
                        @change="handleQuery"
                      >
                        <ElCheckboxButton
                          v-for="item in statusList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </ElCheckboxGroup>
                    </ElScrollbar>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="9">
                  <ElFormItem label="色系" prop="categoryCode">
                    <ElSelect
                      v-model="formData.categoryCode"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      multiple
                      placeholder="请选择色系"
                    >
                      <ElOption
                        v-for="item in categoryList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value!"
                      />
                    </ElSelect>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="颜色名称" prop="name">
                    <ElInput
                      v-model="formData.name"
                      clearable
                      placeholder="请输入颜色名称，支持模糊查询"
                      @change="handleQuery"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem label="操作时间段" prop="date">
                    <ElDatePicker
                      v-model="formData.date"
                      :default-time="defaultTime"
                      class="max-w-96"
                      clearable
                      end-placeholder="结束时间"
                      range-separator="~"
                      start-placeholder="开始时间"
                      type="daterange"
                      unlink-panels
                      value-format="YYYY-MM-DD"
                    />
                  </ElFormItem>
                </ElCol>
              </div>
            </ElCollapseTransition>
          </ElRow>
        </ElForm>
        <div v-show="!isEmbed" class="mb-[10px] min-h-8">
          <ElButton v-hasPermi="['createColor']" type="primary" @click="handleCreateColor">
            <Icon icon="ep:plus" />
            <span class="text-[14px]">新增颜色</span>
          </ElButton>
          <ElButton v-hasPermi="['editColor']" type="primary" @click="handleEditColor">
            <Icon icon="ep:edit" />
            <span class="text-[14px]">修改颜色</span>
          </ElButton>
          <ElButton v-hasPermi="['changeStatus']" type="primary" @click="handleChangeStatus">
            <Icon icon="ep:share" />
            <span class="text-[14px]">启用/禁用</span>
          </ElButton>
          <ElButton v-hasPermi="['copyColor']" type="primary" @click="handleCopyColor">
            <Icon icon="ep:copy-document" />
            <span class="text-[14px]">复制颜色</span>
          </ElButton>
        </div>
        <div>
          <VxeTable
            ref="tableRef"
            :cell-class-name="setRejectedCellClassName"
            :cell-config="{ height: 80 }"
            :data="tableData"
            :loading="queryLoading"
            :max-height="maxHeight - 125"
            :show-header-overflow="false"
          >
            <VxeColumn fixed="left" type="checkbox" width="40" />
            <VxeColumn title="序号" fixed="left" type="seq" width="60" />
            <VxeColumn field="code" fixed="left" show-overflow title="颜色编码" width="80">
              <template #default="{ row }: { row: ColorListPageAPI.Row }">
                <router-link :to="{ name: 'ViewColor', query: { id: row.id } }">
                  <span class="p-0 max-w-full cursor-pointer text-blue-500">
                    {{ row.code }}
                  </span>
                </router-link>
              </template>
            </VxeColumn>
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="thumbnail"
              fixed="left"
              title="缩略图"
              width="100"
            />
            <VxeColumn field="library" title="颜色库" width="80" />
            <VxeColumn field="name" min-width="100" title="颜色名称" />
            <VxeColumn field="englishName" min-width="120" title="颜色英文名称" />
            <VxeColumn field="category" min-width="100" title="色系" />
            <VxeColumn field="categoryCode" title="色系编码" width="80" />
            <VxeColumn field="standardShade" min-width="100" title="标准色号" />
            <VxeColumn field="rgb" title="RGB值" width="100" />
            <VxeColumn field="number" title="颜色码" width="100" />
            <VxeColumn field="status" fixed="right" title="状态" width="80" />
            <VxeColumn field="wmsName" title="WMS颜色名称" width="120" />
            <VxeColumn field="wmsCode" title="WMS颜色编码" width="120" />
            <VxeColumn field="operator" title="操作人" width="120" />
            <VxeColumn :show-overflow="false" field="modifyTime" title="操作时间" width="90" />
            <VxeColumn :show-overflow="false" fixed="right" title="操作" width="120">
              <template #default="{ row }">
                <ElButton
                  v-if="!isEmbed"
                  size="small"
                  text
                  type="primary"
                  @click="handleOpenVersionDialog(row)"
                >
                  版本记录
                </ElButton>
              </template>
            </VxeColumn>
          </VxeTable>
        </div>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
      <VersionDialog v-model="versionDialogVisible" :current-row="currentRow" />
      <StatusDialog
        v-model="statusDialogVisible"
        :selected-rows="selectedRows"
        @refresh="handleQuery"
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
:deep(.el-checkbox-button.is-checked .el-checkbox-button__inner) {
  color: var(--el-checkbox-button-checked-text-color);
  background-color: var(--el-checkbox-button-checked-bg-color);
  border-color: var(--el-checkbox-button-checked-border-color);
  box-shadow: -1px 0 0 0 var(--el-color-primary-light-7);
}
</style>
