import { service } from '@/config/axios/service'
import to from 'await-to-js'
import { ProductListPageAPI } from '../../product-library/api/product-list'

/**
 * 查看颜色详情
 */
export namespace ColorInfoAPI {
  export interface CombineDetailDTO {
    /**
     * 颜色id
     */
    colorId?: number
    /**
     * 颜色名称
     */
    name?: string
    /**
     * 占比数量
     */
    percentage?: number
    /**
     * rgb
     */
    rgb?: string
  }
  export interface ColorInfo {
    /**
     * 色系
     */
    categoryCode?: string
    /**
     * 颜色编码
     */
    code?: string
    /**
     * 组合色详情
     */
    combineDetail?: CombineDetailDTO[]
    /**
     * 颜色英文名称
     */
    englishName?: string
    errorInfo?: string[]
    id?: number
    /**
     * 颜色库
     */
    libraryCode?: string
    /**
     * 颜色名称
     */
    name?: string
    /**
     * 16进制颜色码
     */
    number?: string
    /**
     * 颜色参考图的oss地址
     */
    reference?: BaseFileDTO[]
    /**
     * 是否被产品库，材料库引用
     */
    referenced?: boolean
    /**
     * 备注
     */
    remark?: string
    /**
     * RGB值,逗号分隔
     */
    rgb?: string
    /**
     * 标准色号
     */
    standardShade?: string
    /**
     * 状态
     */
    status?: string
    /**
     * 状态编码
     */
    statusCode?: string
    thumbnail?: BaseFileDTO
    /**
     * wms颜色编码
     */
    wmsCode?: string
    /**
     * wms颜色名称
     */
    wmsName?: string
    /**
     * 关联的产品库
     */
    productList?: ProductListPageAPI.List
  }
  export type Response = ResponseData<ColorInfo>
}
export function viewColor(id: number) {
  return to<ColorInfoAPI.Response>(service.get(`/pdm-base/color/detail/${id}`))
}

/**
 * 创建颜色
 */
export namespace CreateColorAPI {
  export type Params = Omit<
    ColorInfoAPI.ColorInfo,
    'id' | 'wmsName' | 'wmsCode' | 'status' | 'statusCode'
  > & {
    needApprove?: boolean
  }
  export type Response = BasicResponseData
}
export function createColor(data: CreateColorAPI.Params) {
  return to<CreateColorAPI.Response>(service.post('/pdm-base/color/save', data))
}

/**
 * 通过版本id查看颜色
 * @param id
 */
export function viewColorByVersionId(id: number) {
  return to<ColorInfoAPI.Response>(service.get(`/pdm-base/color/versionDetail/${id}`))
}

/**
 * 编辑颜色
 */
export namespace EditColorAPI {
  export type Params = CreateColorAPI.Params & {
    id: number
    status: string
  }
  export type Response = BasicResponseData
}
export function editColor(data: EditColorAPI.Params) {
  return to<EditColorAPI.Response>(service.post('/pdm-base/color/update', data))
}

/**
 * 获取带有rgb值的颜色
 */
export namespace ColorRGBListAPI {
  export interface Data {
    id?: number
    name?: string
    rgb?: string
  }
  export type Response = ResponseData<Data[]>
}
export function getColorRGBList() {
  return to<ColorRGBListAPI.Response>(service.get('/pdm-base/color/colorRgb'))
}

/**
 * 根据colorId查询关联的材料信息
 */
export namespace MaterialByColorIdAPI {
  export interface Params {
    colorId?: number
  }
  export interface Row {
    /**
     * 分类中文名
     */
    categoryCnName?: string
    /**
     * 色卡库位
     */
    colorLocation?: string
    id?: number
    /**
     * 材料中文名
     */
    materialCnName?: string
    /**
     * 材料编码
     */
    materialCode?: string
    /**
     * 材料颜色
     */
    materialColor?: string
    /**
     * 材料英文名
     */
    materialEnName?: string
    /**
     * 供应商名称
     */
    materialVendorName?: string
    /**
     * 状态
     */
    status?: string
    /**
     * 状态code
     */
    statusCode?: string
    /**
     * 缩略图
     */
    thumbnailAddress?: string
  }
  export type List = Row[]
  export type Request = Params & PageParams
  export type Response = PagedResponseData<Row>
}
export function queryMaterialByColor(params: MaterialByColorIdAPI.Request) {
  return to<MaterialByColorIdAPI.Response>(
    service.get('/pdm-base/material/pageByColor', { params })
  )
}
