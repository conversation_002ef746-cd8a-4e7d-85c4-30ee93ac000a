import { service } from '@/config/axios/service'
import to from 'await-to-js'
import type { ColorCategoryAttr } from '../const'

export namespace ColorListPageAPI {
  export interface Params {
    /**
     * 色系
     */
    categoryCode?: string[]
    /**
     * 颜色编码
     */
    code?: string
    /**
     * 页码
     */
    current?: number
    /**
     * 结束时间
     */
    endTime?: string
    /**
     * 颜色英文名称
     */
    englishName?: string
    idList?: string[]
    /**
     * 颜色库
     */
    library?: string[]
    /**
     * 颜色名称
     */
    name?: string
    /**
     * 页面尺寸
     */
    size?: number
    /**
     * 开始时间
     */
    startTime?: string
    /**
     * 启用状态
     */
    status?: string[]
  }
  export interface Row {
    /**
     * 色系
     */
    category?: string
    /**
     * 色系编码
     */
    categoryCode?: string
    /**
     * 颜色编码
     */
    code?: string
    /**
     * 审批状态
     */
    dataStatus?: string
    /**
     * 审批状态code
     */
    dataStatusCode?: string
    /**
     * 颜色英文名称
     */
    englishName?: string
    /**
     * id
     */
    id?: number
    /**
     * 颜色库
     */
    library?: string
    /**
     * 颜色库编码
     */
    libraryCode?: string
    /**
     * 操作时间
     */
    modifyTime?: Date
    /**
     * 颜色名称
     */
    name?: string
    /**
     * 颜色码
     */
    number?: string
    /**
     * 操作人
     */
    operator?: string
    /**
     * RGB值
     */
    rgb?: string
    /**
     * 标准色号
     */
    standardShade?: string
    /**
     * 状态
     */
    status?: string
    /**
     * 状态编码
     */
    statusCode?: string
    thumbnail?: BaseFileDTO
    /**
     * 颜色缩略图的下载地址
     */
    thumbnailAddress?: string
    /**
     * wms颜色编码
     */
    wmsCode?: string
    /**
     * wms颜色名称
     */
    wmsName?: string
  }
  export type List = Row[]
  export type Request = Params & Partial<PageParams>
  export type Response = PagedResponseData<Row>
}

export function getColorListByPage(params: ColorListPageAPI.Request, signal?: AbortSignal) {
  return to<ColorListPageAPI.Response>(service.get('/pdm-base/color/page', { params, signal }))
}

export namespace ColorCategoryListAPI {
  export type Data = ColorCategoryAttr
  export type Response = ResponseData<Data[]>
}
export function getColorCategoryList() {
  return to<ColorCategoryListAPI.Response>(service.get('/pdm-base/color/category'))
}
