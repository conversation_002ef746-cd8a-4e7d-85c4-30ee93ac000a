<script lang="ts" setup>
import { ElMessage, ElMessageBox, ElSwitch } from 'element-plus'
import {
  getMaterialCategoryList,
  MaterialCategoryListAPI,
  updateMaterialCategoryStatus
} from './api/materialCategory'
import { statusConst, StatusEnum } from '../const'
import { MaterialCategoryUpdateEnum } from './const'
import EditCategoryDialog from './components/EditCategoryDialog.vue'
import { ContentWrap } from '@/components/ContentWrap'

defineOptions({
  name: 'MaterialCategory'
})

const { statusMap, statusList } = statusConst

interface TableRow extends MaterialCategoryListAPI.Row {
  beforeStatus?: string
}

const tableData = ref<MaterialCategoryListAPI.List>([])
const formData = ref<MaterialCategoryListAPI.Params>({
  status: [StatusEnum.START],
  categoryName: ''
})
const queryLoading = ref(false)
const handleQuery = async () => {
  queryLoading.value = true
  const [error, result] = await getMaterialCategoryList(formData.value)
  queryLoading.value = false
  if (error === null && result?.datas) {
    tableData.value = result?.datas.map((v) => Object.assign(v, { beforeStatus: v.status }))
  }
}

const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
const handleExport = () => {
  exportFn({
    exportType: 'material-category-report',
    reqParam: JSON.stringify(formData.value)
  })
}

onActivated(handleQuery)

const useTableOperate = () => {
  const editVisible = ref(false)
  const editType = ref<number>(MaterialCategoryUpdateEnum.ADD_ROOT)
  const selectedRow = ref<TableRow>()
  const handleSwitchChange = (): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      ElMessageBox.confirm('当前分类及对应的子层级分类都会同时更新状态', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return resolve(true)
        })
        .catch(() => {
          return reject(false)
        })
    })
  }
  const handleStatusChange = async (status: string, row: TableRow) => {
    const { beforeStatus, id } = row
    const [error, result] = await updateMaterialCategoryStatus({
      beforeStatus: beforeStatus!,
      id: id!,
      status
    })
    if (error === null && result) {
      ElMessage.success(result.msg || '操作成功')
      handleQuery()
    }
  }
  const openEditDialog = (type: number, row?: TableRow) => {
    editType.value = type
    selectedRow.value = row || ({} as TableRow)
    editVisible.value = true
  }
  return {
    handleSwitchChange,
    handleStatusChange,
    openEditDialog,
    editVisible,
    selectedRow,
    editType
  }
}
const {
  handleSwitchChange,
  handleStatusChange,
  openEditDialog,
  editVisible,
  selectedRow,
  editType
} = useTableOperate()

Promise.all([handleQuery()])
</script>
<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_200px)] overflow-x-hidden overflow-y-auto">
        <ElForm
          :model="formData"
          @submit="
            (e) => {
              e.preventDefault()
            }
          "
        >
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="分类名称" prop="categoryName">
                <ElInput v-model="formData.categoryName" clearable placeholder="请输入" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="状态" prop="status">
                <ElScrollbar>
                  <ElCheckboxGroup
                    v-model="formData.status"
                    class="flex flex-nowrap"
                    @change="handleQuery"
                  >
                    <ElCheckboxButton
                      v-for="item in statusList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </ElCheckboxGroup>
                </ElScrollbar>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElButton
                :loading="queryLoading"
                type="primary"
                @click="handleQuery"
                native-type="submit"
              >
                <Icon class="mr-1" icon="ep:search" />
                查询
              </ElButton>
              <ElButton :loading="exportLoading" type="primary" @click="handleExport">
                导出
              </ElButton>
              <ElButton type="primary" @click="openEditDialog(MaterialCategoryUpdateEnum.ADD_ROOT)">
                新增分类
              </ElButton>
            </ElCol>
          </ElRow>
        </ElForm>
        <VxeTable
          ref="tableRef"
          :data="tableData"
          :loading="queryLoading"
          :row-config="{ keyField: 'id' }"
          :tree-config="{ childrenField: 'childList', reserve: true }"
        >
          <VxeColumn field="categoryCnName" title="分类名称" tree-node />
          <VxeColumn field="categoryEnName" title="分类英文名称" />
          <VxeColumn field="categoryCode" title="分类编码" />
          <VxeColumn field="enNameAbbreviation" title="材料小类简称" />
          <VxeColumn field="materialClassification" title="材质分类" />
          <VxeColumn field="status" title="状态">
            <template #default="{ row }: { row: TableRow }">
              <span>{{ statusMap[row.status!] }}</span>
            </template>
          </VxeColumn>
          <VxeColumn field="wmsCategoryName" title="WMS材料分类" />
          <VxeColumn field="wmsCategoryCode" title="WMS材料编码" />
          <VxeColumn :show-overflow="false" title="操作">
            <template #default="{ row }: { row: TableRow }">
              <div class="flex justify-around items-center">
                <ElSwitch
                  v-if="row.status === StatusEnum.START || row.status === StatusEnum.BAN"
                  v-model="row.status"
                  :before-change="handleSwitchChange"
                  active-value="start"
                  inactive-value="ban"
                  @change="(val:string) => handleStatusChange(val, row)"
                />
                <ElButton
                  v-if="row.status === StatusEnum.DRAFT"
                  size="small"
                  text
                  type="primary"
                  @click="
                    openEditDialog(
                      row.parentId
                        ? MaterialCategoryUpdateEnum.UPDATE_LEAF
                        : MaterialCategoryUpdateEnum.UPDATE_ROOT,
                      row
                    )
                  "
                >
                  修改
                </ElButton>
                <!-- TODO: Mindy说产品上次说层级最多两层，prd暂未更新。应该需要根据层级隐藏新增分类按钮 -->
                <ElButton
                  size="small"
                  text
                  v-if="row.childList && row.childList?.length > 0"
                  type="primary"
                  @click="openEditDialog(MaterialCategoryUpdateEnum.ADD_LEAF, row)"
                >
                  新增分类
                </ElButton>
              </div>
            </template>
          </VxeColumn>
        </VxeTable>
        <EditCategoryDialog
          v-model="editVisible"
          :edit-type="editType"
          :selected-row="selectedRow"
          @refresh="handleQuery"
        />
      </div>
    </div>
  </ContentWrap>
</template>
