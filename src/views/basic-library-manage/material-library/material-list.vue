<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import StatusDialog from './components/StatusDialog.vue'
import { ref } from 'vue'
import { Icon } from '@/components/Icon'
import { downloadLabel, getMaterialListByPage, MaterialListAPI } from './api/material-list'
import type { CascaderProps, ElPagination, FormInstance } from 'element-plus'
import { ElCollapseTransition, ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import type { VxeTableInstance } from 'vxe-table'
import { approvalStatusConst, ApprovalStatusEnum, statusConst, StatusEnum } from '../const'
import type { VxeTablePropTypes } from 'vxe-table/types'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { isEqual, omit } from 'lodash-es'
import VersionDialog from './components/VersionDialog.vue'
import CardView from './components/CardView.vue'
import { Pager } from '../api/common'
import { useBasicLibraryDictStore } from '../store/dict'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useMaterial } from '@/views/basic-library-manage/sample-manage/components/hooks'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'

defineOptions({
  name: 'MaterialLibrary'
})

const router = useRouter()

const useConst = () => {
  // 状态枚举
  const statusMap = statusConst.statusMap
  const statusList = statusConst.statusList

  // 审批状态枚举
  const approvalStatusMap = approvalStatusConst.approvalStatusMap

  // 材料分类级联
  const { materialCategoryList } = useMaterial()

  // 品牌
  const dictStore = useBasicLibraryDictStore()
  const brandList = computed(() => dictStore.brandList)

  const cascaderProps: CascaderProps = {
    label: 'selectorValue',
    value: 'selectorKey',
    children: 'childList',
    expandTrigger: 'hover' as const,
    multiple: true,
    emitPath: false
  }

  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '145'
    },
    {
      formLabelLength: '180'
    }
  ])

  return {
    statusMap,
    statusList: statusList.filter((e) => e.value !== StatusEnum.APPROVING),
    approvalStatusMap,
    materialCategoryList,
    brandList,
    cascaderProps,
    formLabelLength
  }
}

const {
  statusMap,
  statusList,
  approvalStatusMap,
  materialCategoryList,
  brandList,
  cascaderProps,
  formLabelLength
} = useConst()

const useQuery = () => {
  interface FormModel
    extends Omit<MaterialListAPI.Params, 'startTime' | 'endTime' | 'current' | 'size'> {
    date?: [string, string]
  }

  const defaultFormData: FormModel = {
    brandIdList: [],
    colorLocation: '',
    colorName: '',
    materialCategoryIdList: [],
    materialGrainList: [],
    materialTextureList: [],
    materialCode: '',
    materialEnName: '',
    materialCnName: '',
    materialVendorName: '',
    statusList: [StatusEnum.DRAFT, StatusEnum.START],
    productCodeList: []
  }
  const formData = ref<FormModel>({
    ...defaultFormData
  })
  let lastFormData = {
    ...defaultFormData
  }
  const formRef = ref<FormInstance>()
  const tableRef = ref<VxeTableInstance>()
  const cardRef = ref<InstanceType<typeof CardView>>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const tableData = ref<MaterialListAPI.List>([])
  const queryLoading = ref(false)
  const pager = ref<Pager>({
    current: 1,
    size: 10,
    total: 0
  })
  const queryParams = computed<MaterialListAPI.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1]
    }
  })
  const defaultTime = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ] as [Date, Date]

  let controller: AbortController | null = null
  const handleQuery = async () => {
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await getMaterialListByPage(
      omit(queryParams.value, 'date'),
      controller.signal
    )
    queryLoading.value = false
    if (error === null && result?.datas) {
      lastFormData = { ...formData.value }
      const { records } = result.datas
      tableData.value = records
      pager.value.total = result.datas.total
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
    handleQuery()
  }

  onActivated(handleQuery)

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
  const handleExport = () => {
    const selected: MaterialListAPI.List | undefined = tableRef.value?.getCheckboxRecords()
    let reqParam: string
    if (selected && selected?.length > 0) {
      reqParam = JSON.stringify({ idList: selected.map((e) => e.id) })
    } else {
      reqParam = JSON.stringify(queryParams.value)
    }
    exportFn({
      exportType: 'material-export',
      reqParam
    })
  }

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef: cardRef,
    pagerRef
  })

  const setRejectedCellClassName: VxeTablePropTypes.CellClassName = ({ row, column }) => {
    if (
      row.dataStatus === approvalStatusMap[ApprovalStatusEnum.REJECTION] &&
      column.field === 'status'
    ) {
      return 'bg-red-400 text-white'
    }
  }

  return {
    formRef,
    formData,
    queryLoading,
    pager,
    defaultTime,
    handleQuery,
    handleReset,
    handleExport,
    exportLoading,
    visible,
    setVisible,
    cardRef,
    tableRef,
    pagerRef,
    tableData,
    maxHeight,
    setRejectedCellClassName
  }
}

const {
  formRef,
  formData,
  queryLoading,
  pager,
  cardRef,
  defaultTime,
  handleQuery,
  handleReset,
  handleExport,
  exportLoading,
  visible,
  setVisible,
  tableRef,
  pagerRef,
  tableData,
  maxHeight,
  setRejectedCellClassName
} = useQuery()

// 版本记录弹窗
const useVersionDialog = () => {
  const versionDialogVisible = ref(false)
  const setVersionDialogVisible = () => {
    versionDialogVisible.value = !unref(versionDialogVisible)
  }
  return {
    versionDialogVisible,
    setVersionDialogVisible
  }
}

// 启用/禁用弹窗
const useStatusDialog = () => {
  const statusDialogVisible = ref(false)
  const setStatusDialogVisible = () => {
    statusDialogVisible.value = !unref(statusDialogVisible)
  }
  return {
    statusDialogVisible,
    setStatusDialogVisible
  }
}

const useOperation = () => {
  const currentRow = ref<MaterialListAPI.Row>()
  const selectedRows = ref<MaterialListAPI.List>([])
  const isShowList = ref(false)

  // 切换图片/列表
  const handleToggleList = () => {
    isShowList.value = !unref(isShowList)
  }

  // 修改材料
  const handleEditMaterial = (row?: MaterialListAPI.Row) => {
    let selectedRows: MaterialListAPI.List | undefined
    if (isShowList.value) {
      selectedRows = unref(tableRef)?.getCheckboxRecords()
    } else {
      selectedRows = row ? [row] : []
    }
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    const status = selectedRows[0].statusCode
    if (
      status?.toLowerCase() === StatusEnum.APPROVING ||
      status?.toLowerCase() === StatusEnum.BAN
    ) {
      ElMessage.warning('审批中/禁用数据不允许修改')
      return
    }
    router.push({
      name: 'EditMaterial',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 复制材料
  const handleCopyMaterial = (row?: MaterialListAPI.Row) => {
    let selectedRows: MaterialListAPI.List | undefined
    if (isShowList.value) {
      selectedRows = unref(tableRef)?.getCheckboxRecords()
    } else {
      selectedRows = row ? [row] : []
    }
    if (!selectedRows?.length || selectedRows.length > 1) {
      ElMessage.warning('请选择一条数据')
      return
    }
    router.push({
      name: 'CopyMaterial',
      query: {
        id: selectedRows[0].id
      }
    })
  }

  // 启用/禁用
  const { statusDialogVisible, setStatusDialogVisible } = useStatusDialog()
  const handleChangeStatus = (row?: MaterialListAPI.Row) => {
    let selected: MaterialListAPI.List | undefined
    if (isShowList.value) {
      selected = unref(tableRef)?.getCheckboxRecords()
    } else {
      selected = row ? [row] : []
    }
    if (!selected?.length) {
      ElMessage.warning('请选择数据')
      return
    }
    const hasApproving = selected.some((item) => item.status === statusMap['approving'])
    if (hasApproving) {
      ElMessage.warning('存在审批中的数据')
      return
    }
    selectedRows.value = selected
    setStatusDialogVisible()
  }

  // 新增材料
  const handleCreateMaterial = () => {
    router.push({
      name: 'CreateMaterial'
    })
  }

  const downloadLoading = ref(false)
  // 下载贴标
  const handleDownloadLabel = async () => {
    const selected: MaterialListAPI.List | undefined = unref(tableRef)?.getCheckboxRecords()
    if (!selected?.length) {
      ElMessage.warning('请选择数据')
      return
    }
    downloadLoading.value = true
    const [error, result] = await downloadLabel({
      idList: selected.map((e) => e.id!)
    }).finally(() => {
      downloadLoading.value = false
    })
    if (error === null && result?.datas) {
      window.open(result.datas)
    }
  }

  // 版本记录
  const { versionDialogVisible, setVersionDialogVisible } = useVersionDialog()
  const handleOpenVersionDialog = (row: MaterialListAPI.Row) => {
    currentRow.value = row
    setVersionDialogVisible()
  }

  return {
    currentRow,
    selectedRows,
    isShowList,
    handleToggleList,
    handleCopyMaterial,
    handleEditMaterial,
    handleCreateMaterial,
    versionDialogVisible,
    handleOpenVersionDialog,
    handleDownloadLabel,
    downloadLoading,
    handleChangeStatus,
    statusDialogVisible
  }
}

const {
  isShowList,
  handleToggleList,
  handleCopyMaterial,
  handleEditMaterial,
  handleCreateMaterial,
  versionDialogVisible,
  handleOpenVersionDialog,
  currentRow,
  handleDownloadLabel,
  downloadLoading,
  selectedRows,
  handleChangeStatus,
  statusDialogVisible
} = useOperation()

Promise.all([handleQuery()])
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_200px)] overflow-x-hidden overflow-y-auto">
        <ElForm ref="formRef" :label-width="formLabelLength" :model="formData">
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="材料编码" prop="materialCode">
                <ElInput
                  v-model="formData.materialCode"
                  clearable
                  placeholder="请输入材料编码信息，支持模糊搜索"
                  @change="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="材料分类" prop="materialCategoryIdList">
                <ElCascader
                  v-model="formData.materialCategoryIdList"
                  :options="materialCategoryList"
                  :props="cascaderProps"
                  clearable
                  placeholder="请选择材料分类"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="" label-width="0">
                <ElButton text @click="setVisible">
                  {{ visible ? '收起' : '展开' }}
                  <Icon
                    :class="visible ? 'rotate-90' : ''"
                    class="transform transition duration-400"
                    icon="ant-design:down-outlined"
                  />
                </ElButton>
                <ElButton :loading="queryLoading" type="primary" @click="handleQuery">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
                  查询
                </ElButton>
                <ElButton :loading="queryLoading" @click="handleReset">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
                  重置
                </ElButton>
                <ElButton :loading="exportLoading" type="primary" @click="handleExport">
                  <Icon v-show="!exportLoading" class="mr-1" icon="ep:upload-filled" />
                  导出
                </ElButton>
              </ElFormItem>
            </ElCol>
            <ElCol :span="16">
              <ElFormItem label="适用品牌" prop="brandIdList">
                <ElScrollbar class="mb-2">
                  <ElCheckboxGroup
                    v-model="formData.brandIdList"
                    class="flex flex-nowrap"
                    @change="handleQuery"
                  >
                    <ElCheckboxButton
                      v-for="item in brandList"
                      :key="item.dictValue"
                      :label="item.dictCnName"
                      :value="item.dictValue"
                    />
                  </ElCheckboxGroup>
                </ElScrollbar>
              </ElFormItem>
            </ElCol>
            <ElCollapseTransition>
              <div v-show="visible" class="flex flex-wrap w-full">
                <ElCol :span="24">
                  <ElFormItem label="状态" prop="statusList">
                    <ElCheckboxGroup v-model="formData.statusList" @change="handleQuery">
                      <ElCheckboxButton
                        v-for="item in statusList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </ElCheckboxGroup>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="材料英文名称" prop="materialEnName">
                    <ElInput
                      v-model="formData.materialEnName"
                      clearable
                      placeholder="请输入材料英文名称信息，支持模糊查询"
                      @change="handleQuery"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="材料名称" prop="materialCnName">
                    <ElInput
                      v-model="formData.materialCnName"
                      clearable
                      placeholder="请输入材料名称信息，支持模糊查询"
                      @change="handleQuery"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8" />
                <ElCol :span="8">
                  <ElFormItem label="操作时间段" prop="date">
                    <ElDatePicker
                      v-model="formData.date"
                      :default-time="defaultTime"
                      class="max-w-96"
                      clearable
                      end-placeholder="结束时间"
                      range-separator="~"
                      start-placeholder="开始时间"
                      type="daterange"
                      unlink-panels
                      value-format="YYYY-MM-DD"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="材料颜色" prop="colorName">
                    <ElInput
                      v-model="formData.colorName"
                      clearable
                      placeholder="请输入材料颜色信息，支持模糊查询"
                      @change="handleQuery"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8" />
                <ElCol :span="8">
                  <ElFormItem label="供应商材料名称" prop="materialVendorName">
                    <ElInput
                      v-model="formData.materialVendorName"
                      clearable
                      placeholder="请输入供应商材料名称名称信息，支持模糊搜索"
                      @change="handleQuery"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="色卡库位" prop="colorLocation">
                    <ElInput
                      v-model="formData.colorLocation"
                      clearable
                      placeholder="请输入色卡库位信息，支持模糊搜索"
                      @change="handleQuery"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="供应商材料编号/模号" prop="vendorMaterialMouldCode">
                    <ElInput
                      v-model="formData.vendorMaterialMouldCode"
                      clearable
                      placeholder="请输入供应商材料编号/模号，支持模糊搜索"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="24">
                  <ElFormItem label="关联的产品" prop="productCodeList">
                    <SelectPlus
                      v-model="formData.productCodeList"
                      api-key="getProductNumberList"
                      filterable
                      multiple
                      virtualized
                      @change="handleQuery"
                    />
                  </ElFormItem>
                </ElCol>
              </div>
            </ElCollapseTransition>
          </ElRow>
        </ElForm>
        <div class="mb-[10px] min-h-8">
          <ElButton v-hasPermi="['createMaterial']" type="primary" @click="handleCreateMaterial">
            <Icon icon="ep:plus" />
            <span class="text-[14px]">新增材料</span>
          </ElButton>
          <ElButton
            v-show="isShowList"
            v-hasPermi="['editMaterial']"
            type="primary"
            @click="handleEditMaterial()"
          >
            <Icon icon="ep:edit" />
            <span class="text-[14px]">修改材料</span>
          </ElButton>
          <ElButton
            v-show="isShowList"
            v-hasPermi="['material:changeStatus']"
            type="primary"
            @click="handleChangeStatus()"
          >
            <Icon icon="ep:share" />
            <span class="text-[14px]">启用/禁用</span>
          </ElButton>
          <ElButton
            v-show="isShowList"
            v-hasPermi="['copyMaterial']"
            type="primary"
            @click="handleCopyMaterial()"
          >
            <Icon icon="ep:copy-document" />
            <span class="text-[14px]">复制材料</span>
          </ElButton>
          <ElButton
            v-show="isShowList"
            v-hasPermi="['downloadMaterialLabel']"
            :loading="downloadLoading"
            type="primary"
            @click="handleDownloadLabel()"
          >
            <Icon icon="ep:edit" />
            <span class="text-[14px]">下载贴标</span>
          </ElButton>
          <ElButton class="float-right" text type="primary" @click="handleToggleList">
            <Icon :icon="isShowList ? 'ep:menu' : 'tabler:list'" />
            <span class="text-black text-xs">
              {{ isShowList ? '图片' : '列表' }}
            </span>
          </ElButton>
        </div>
        <CardView
          v-if="!isShowList"
          ref="cardRef"
          :loading="queryLoading"
          :max-height="maxHeight"
          :table-data="tableData"
          @change-status="handleChangeStatus"
          @copy-material="handleCopyMaterial"
          @edit-material="handleEditMaterial"
          @view-material="(row) => router.push({ name: 'ViewMaterial', query: { id: row.id } })"
        />
        <div v-if="isShowList">
          <VxeTable
            ref="tableRef"
            :cell-class-name="setRejectedCellClassName"
            :cell-config="{ height: 80 }"
            :data="tableData"
            :loading="queryLoading"
            :max-height="maxHeight - 75"
            :show-header-overflow="false"
          >
            <VxeColumn fixed="left" type="checkbox" width="40" />
            <VxeColumn fixed="left" title="序号" type="seq" width="60" />
            <VxeColumn field="materialCode" fixed="left" show-overflow title="材料编码" width="80">
              <template #default="{ row }: { row: MaterialListAPI.Row }">
                <router-link :to="{ name: 'ViewMaterial', query: { id: row.id } }">
                  <span class="p-0 max-w-full cursor-pointer text-blue-500">
                    {{ row.materialCode }}
                  </span>
                </router-link>
              </template>
            </VxeColumn>
            <VxeColumn
              :cell-render="{ name: 'Image' }"
              field="thumbnailUrl"
              fixed="left"
              title="缩略图"
              width="100"
            />
            <VxeColumn field="categoryCnName" fixed="left" title="材料分类" width="80" />
            <VxeColumn field="brandName" min-width="100" title="适用品牌" />
            <VxeColumn field="materialCnName" min-width="240" title="材料名称" />
            <VxeColumn field="materialEnName" min-width="100" title="材料英文名称" />
            <VxeColumn field="materialPartItemName" min-width="100" title="部位" />
            <VxeColumn field="materialBreadth" title="宽幅(cm)" width="80" />
            <VxeColumn field="materialThickness" title="厚度(mm)" width="80" />
            <VxeColumn field="materialGramWeight" title="克重" width="80" />
            <VxeColumn field="materialGrain" title="纹路" width="80" />
            <VxeColumn field="materialColor" title="材料颜色" width="120" />
            <VxeColumn field="productSkcCodeList" title="关联的SKC编号" width="120" />
            <VxeColumn field="materialVendorName" title="材料供应商名称" width="120" />
            <VxeColumn field="materialVendorIdentification" title="材料供应商标识" width="120" />
            <VxeColumn field="materialVendorPhone" title="供应商联系方式" width="120" />
            <VxeColumn field="vendorMaterialMouldCode" title="供应商材料编号/模号" width="130" />
            <VxeColumn field="status" fixed="right" title="状态" width="80" />
            <VxeColumn field="skcCodeCount" fixed="right" title="关联SKC数" width="120" />
            <VxeColumn field="colorLocation" title="色卡库位" width="120" />
            <VxeColumn field="modifyByName" title="操作人" width="120" />
            <VxeColumn :show-overflow="false" field="modifyTime" title="操作时间" width="90" />
            <VxeColumn :show-overflow="false" fixed="right" title="操作" width="120">
              <template #default="{ row }">
                <ElButton size="small" text type="primary" @click="handleOpenVersionDialog(row)">
                  版本记录
                </ElButton>
              </template>
            </VxeColumn>
          </VxeTable>
        </div>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
      <StatusDialog
        v-model="statusDialogVisible"
        :selected-rows="selectedRows"
        @refresh="handleQuery"
      />
      <VersionDialog v-model="versionDialogVisible" :current-row="currentRow" />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped></style>
