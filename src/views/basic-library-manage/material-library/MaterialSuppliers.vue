<script lang="ts" setup>
import { ElButton, ElMessage, ElPagination, FormInstance } from 'element-plus'
import { Pager } from '@/views/basic-library-manage/api/common'
import type { VxeTableInstance } from 'vxe-table'
import dayjs from 'dayjs'
import { statusConst, StatusEnum } from '@/views/basic-library-manage/const'
import {
  getVendorListByPage,
  ModelVendorListApi,
  OperationTypeEnum
} from '@/views/basic-library-manage/material-library/api/material-suppliers'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import EditSupplierDialog from '@/views/basic-library-manage/material-library/components/EditSupplierDialog.vue'
import EditStatusDialog from '@/views/basic-library-manage/material-library/components/EditStatusDialog.vue'
import MaterialSuppliersVersionDialog from '@/views/basic-library-manage/material-library/components/MaterialSuppliersVersionDialog.vue'
import { isEqual } from 'lodash-es'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { ContentWrap } from '@/components/ContentWrap'

interface FormModel
  extends Omit<ModelVendorListApi.Params, 'startTime' | 'endTime' | 'current' | 'size'> {
  date?: [string, string]
}

defineOptions({
  name: 'MaterialSuppliers'
})

const { handleExport: exportFn, loading: exportLoading } = useOmsExport()

let controller: AbortController | null = null
const formRef = ref<FormInstance>()
const statusList = statusConst.statusList
const vxeTableRef = ref<VxeTableInstance>()
const pagerRef = ref<InstanceType<typeof ElPagination>>()
const selectedRows = ref<ModelVendorListApi.Data[]>([])
const currentRow = ref<ModelVendorListApi.Data | null>(null)
const queryLoading = ref(false)
const editSupplierVisible = ref(false)
const editStatusVisible = ref(false)
const operationType = ref<OperationTypeEnum>(OperationTypeEnum.ADD)
const materialSuppliersVersionVisible = ref(false)
const visible = ref(false)
const tableData = ref([{}])
const pager = ref<Pager>({
  current: 1,
  size: 10,
  total: 0
})
const maxHeight = useTableHeight({
  tableRef: vxeTableRef,
  pagerRef
})

const defaultTime: [Date, Date] = [
  dayjs('00:00:00', 'HH:mm:ss').toDate(),
  dayjs('23:59:59', 'HH:mm:ss').toDate()
]

const defaultFormData: FormModel = {
  code: '',
  name: '',
  region: [],
  status: [StatusEnum.DRAFT, StatusEnum.START],
  nature: [],
  type: []
}

let lastFormData = {
  ...defaultFormData
}

const formData = ref<FormModel>({
  ...defaultFormData
})

const queryParams = computed<ModelVendorListApi.Request>(() => {
  const { date, ...rest } = formData.value
  const { current, size } = pager.value
  return {
    ...rest,
    current,
    size,
    startTime: date?.[0],
    endTime: date?.[1]
  }
})

const setVisible = () => {
  visible.value = !unref(visible)
}

const handleReset = () => {
  formData.value = {
    ...defaultFormData
  }
  handleQuery()
}

const handleQuery = async () => {
  if (controller) {
    controller.abort()
    controller = null
    setTimeout(() => {
      handleQuery()
    })
    return
  }
  queryLoading.value = true
  controller = new AbortController()
  if (!isEqual(lastFormData, formData.value)) {
    pager.value.current = 1
  }
  const [error, result] = await getVendorListByPage(queryParams.value, controller.signal)
  queryLoading.value = false
  selectedRows.value = []
  if (error === null && result?.datas) {
    lastFormData = { ...formData.value }
    const { records } = result.datas
    tableData.value = records
    pager.value.total = result.datas.pager.total
  }
}

const handleEditSupplier = (type: OperationTypeEnum) => {
  if (type === OperationTypeEnum.EDIT) {
    const rows = vxeTableRef.value?.getCheckboxRecords() as ModelVendorListApi.Data[]
    const hasBan = rows.some((row) => row.status === StatusEnum.BAN)
    if (rows.length === 0) {
      ElMessage.error('请勾选一条记录，再进行内容修改')
      return
    }

    if (rows.length > 1) {
      ElMessage.error('只能选择一行进行修改')
      return
    }

    if (hasBan) {
      ElMessage.error('禁用状态下的数据不允许操作')
      return
    }
    currentRow.value = rows[0]
  }
  operationType.value = type
  editSupplierVisible.value = true
}

const handleStatusSupplier = () => {
  const rows = vxeTableRef.value?.getCheckboxRecords() as ModelVendorListApi.Data[]
  if (rows.length === 0) {
    ElMessage.error('启用或禁用材料信息，需勾选一条或多条信息')
    return
  }
  editStatusVisible.value = true
  selectedRows.value = rows as ModelVendorListApi.Data[]
}

const handleVersionRecord = (row: ModelVendorListApi.Data) => {
  currentRow.value = row
  materialSuppliersVersionVisible.value = true
}

const handleVersionDialogColse = (row: ModelVendorListApi.Data) => {
  currentRow.value = row
  handleEditSupplier(OperationTypeEnum.VIEW)
}
const handleExport = async () => {
  const valid = await formRef.value?.validate()

  if (valid) {
    let reqParam: string
    const rows = vxeTableRef.value?.getCheckboxRecords() as ModelVendorListApi.Data[]
    if (rows && rows?.length > 0) {
      const idList = rows.map((item) => item.id)
      reqParam = JSON.stringify({ idList })
    } else {
      reqParam = JSON.stringify(queryParams.value)
    }
    exportFn({
      exportType: 'materialVendor-export',
      reqParam
    })
  }
}

onMounted(handleQuery)
</script>
<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_200px)] overflow-x-hidden overflow-y-auto">
        <ElForm ref="formRef" :model="formData" labelWidth="120">
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="供应商编码" prop="code">
                <ElInput
                  class="w-48"
                  v-model="formData.code"
                  clearable
                  placeholder="输入材料编码信息，支持模糊搜索"
                  @blur="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="供应商名称" prop="name">
                <ElInput
                  class="w-56"
                  v-model="formData.name"
                  clearable
                  placeholder="输入供应商名称信息，支持模糊搜索"
                  @blur="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="" label-width="0">
                <ElButton text @click="setVisible">
                  {{ visible ? '收起' : '展开' }}
                  <Icon
                    :class="visible ? 'rotate-90' : ''"
                    class="transform transition duration-400"
                    icon="ant-design:down-outlined"
                  />
                </ElButton>
                <ElButton :loading="queryLoading" type="primary" class="w-16" @click="handleQuery">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
                  查询
                </ElButton>
                <ElButton @click="handleReset" :loading="queryLoading" class="w-16">
                  <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />重置
                </ElButton>
                <ElButton
                  v-hasPermi="['material-suppliers:export']"
                  :loading="exportLoading"
                  class="w-16"
                  type="primary"
                  @click="handleExport"
                >
                  <Icon class="mr-1" icon="ep:upload-filled" />
                  导出
                </ElButton>
              </ElFormItem>
            </ElCol>
            <ElCol :span="16">
              <ElFormItem label="区域" prop="region">
                <SelectPlus
                  v-model="formData.region"
                  api-key="COMMON_REGION"
                  cache
                  checkbox
                  checkbox-button
                  @change="handleQuery"
                />
              </ElFormItem>
            </ElCol>
            <ElCollapseTransition>
              <ElCol :span="16">
                <div v-show="visible" class="grid grid-cols-2 items-start w-full">
                  <ElFormItem label="状态" prop="status">
                    <ElCheckboxGroup v-model="formData.status" @change="handleQuery">
                      <ElCheckboxButton
                        v-for="item in statusList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </ElCheckboxGroup>
                  </ElFormItem>
                  <ElFormItem label="性质" prop="nature">
                    <SelectPlus
                      v-model="formData.nature"
                      api-key="MATERIAL_VENDOR_NATURE"
                      class="w-full"
                      clearable
                      filterable
                      multiple
                      placeholder="请选择性质"
                    />
                  </ElFormItem>
                  <ElFormItem label="类别" prop="type">
                    <SelectPlus
                      v-model="formData.type"
                      api-key="MATERIAL_VENDOR_TYPE"
                      class="w-full"
                      clearable
                      filterable
                      multiple
                      placeholder="请选择类别"
                    />
                  </ElFormItem>
                  <ElFormItem label="操作时间段" prop="date">
                    <ElDatePicker
                      class="min-w-48"
                      v-model="formData.date"
                      :default-time="defaultTime"
                      clearable
                      end-placeholder="结束时间"
                      range-separator="~"
                      start-placeholder="开始时间"
                      type="daterange"
                      unlink-panels
                      value-format="YYYY-MM-DD"
                    />
                  </ElFormItem>
                  <ElFormItem label="操作人" prop="modifyById">
                    <CascadeSelector
                      class="w-48"
                      v-model="formData.modifyById"
                      :props="{ emitPath: false }"
                      api-key="allUsers"
                      cache
                    />
                  </ElFormItem>
                </div>
              </ElCol>
            </ElCollapseTransition>
          </ElRow>
        </ElForm>
        <div class="mb-[10px] min-h-8">
          <ElButton
            v-hasPermi="['material-suppliers:add']"
            type="primary"
            @click="handleEditSupplier(OperationTypeEnum.ADD)"
          >
            <Icon icon="ep:plus" />
            <span class="text-[14px]">新增供应商</span>
          </ElButton>
          <ElButton
            v-hasPermi="['material-suppliers:edit']"
            type="primary"
            @click="handleEditSupplier(OperationTypeEnum.EDIT)"
          >
            <Icon icon="ep:edit" />
            <span class="text-[14px]">修改供应商</span>
          </ElButton>
          <ElButton
            v-hasPermi="['material-suppliers:status']"
            type="primary"
            @click="handleStatusSupplier"
          >
            <Icon icon="ep:share" />
            <span class="text-[14px]">启用/禁用</span>
          </ElButton>
        </div>
        <div>
          <VxeTable
            ref="vxeTableRef"
            :cell-config="{ height: 40 }"
            :data="tableData"
            :loading="queryLoading"
            :max-height="maxHeight - 100"
          >
            <VxeColumn type="checkbox" width="50" />
            <VxeColumn show-overflow title="序号" type="seq" width="80" />
            <VxeColumn field="code" show-overflow title="供应商编码" />
            <VxeColumn field="name" show-overflow title="供应商名称" />
            <VxeColumn field="englishName" show-overflow title="供应商英文名称" />
            <VxeColumn field="regionItemName" show-overflow title="区域" />
            <VxeColumn field="natureItemName" show-overflow title="性质" />
            <VxeColumn field="typeItemName" show-overflow title="类别" />
            <VxeColumn field="contactPerson" show-overflow title="联系人" />
            <VxeColumn field="telephone" show-overflow title="电话" />
            <VxeColumn field="address" show-overflow title="地址" />
            <VxeColumn field="experienceRange" show-overflow title="经验范围" />
            <VxeColumn field="remark" show-overflow title="备注" />
            <VxeColumn field="statusItemName" show-overflow title="状态" />
            <VxeColumn field="modifyByIdItemName" show-overflow title="操作人" />
            <VxeColumn field="modifyTime" show-overflow title="操作时间" />
            <VxeColumn :show-overflow="false" title="操作">
              <template #default="{ row }: { row: ModelVendorListApi.Data }">
                <div class="flex justify-around items-center">
                  <ElButton size="small" text type="primary" @click="handleVersionRecord(row)">
                    版本记录
                  </ElButton>
                </div>
              </template>
            </VxeColumn>
          </VxeTable>
        </div>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
      <!-- 新建/编辑/详情 -->
      <EditSupplierDialog
        v-model="editSupplierVisible"
        v-model:current-row="currentRow"
        :type="operationType"
        @refresh="handleQuery"
      />
      <!-- 修改状态 -->
      <EditStatusDialog
        v-model="editStatusVisible"
        v-model:currentRows="selectedRows"
        @refresh="handleQuery"
      />
      <!-- 版本 -->
      <MaterialSuppliersVersionDialog
        v-model="materialSuppliersVersionVisible"
        :currentRow="currentRow"
        @close="handleVersionDialogColse"
      />
    </div>
  </ContentWrap>
</template>
<style lang="less" scoped>
.el-checkbox-group {
  display: flex;
}
</style>
