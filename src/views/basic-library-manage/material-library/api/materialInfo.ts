import { service } from '@/config/axios/service'
import to from 'await-to-js'
import { ProductSkcInfoPageAPI } from '@/api/productSkcInfo/types'
import { MaterialListAPI } from './material-list'

export namespace CreateMaterialAPI {
  export interface Params {
    /**
     * 利用率%
     */
    useRate?: string
    /**
     * 张幅（SF)
     */
    increaseRate?: string
    /**
     * 细类
     */
    subclass?: string
    /**
     * 部位
     */
    materialPart?: string
    materialPartCnName?: string
    /**
     * 品牌ID列表
     */
    brandIdList?: number[]
    checkParam?: string[]
    /**
     * 材料颜色编码 4位
     */
    colorCode?: string
    /**
     * 材料颜色ID
     */
    colorId?: number
    colorCnName?: string
    /**
     * 垫心材质
     */
    cushionMaterial?: string
    /**
     * 鞋垫材质
     */
    insoleMaterial?: string
    /**
     * 宽幅(CM)
     */
    materialBreadth?: string
    materialParentId?: number
    /**
     * 材料分类ID(最小级)
     */
    materialCategoryId?: number
    /**
     * 材料中文名称
     */
    materialCnName?: string
    /**
     * 材料英文名称
     */
    materialEnName?: string
    /**
     * 纹路
     */
    materialGrain?: string
    /**
     * 克重
     */
    materialGramWeight?: string
    /**
     * 高度
     */
    materialHeight?: number
    /**
     * 成分
     */
    materialIngredient?: string
    /**
     * 尺码段
     */
    materialSize?: number
    /**
     * 材质
     */
    materialTexture?: string
    /**
     * 厚度(MM)
     */
    materialThickness?: string
    /**
     * 单位
     */
    materialUnit?: string
    /**
     * 关联供应商信息
     */
    materialVendorList?: MaterialVendorAddReq[]
    /**
     * 其他图片地址列表
     */
    otherImageFileList?: BaseFileDTO[]
    /**
     * 材料一级分类编码
     */
    parentCategoryCode?: string
    /**
     * 工艺描述
     */
    processDescription?: string
    /**
     * 产品编码列表
     */
    productCodeList?: string[]
    /**
     * skc 产品列表
     */
    productSkcCodeList?: number[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 材料二级分类编码
     */
    secondCategoryCode?: string
    /**
     * 底材材质
     */
    substrateMaterial?: string
    thumbnailFile?: BaseFileDTO
  }
  export interface MaterialVendorAddReq {
    /**
     * 色卡库位
     */
    colorLocation?: string
    /**
     * 交货周期(天)
     */
    deliveryCycle?: number
    /**
     * 材料价格(￥)
     */
    materialPrice?: number
    /**
     * 材料供应商标识
     */
    materialVendorIdentification?: string
    /**
     * 材料供应商名称
     */
    materialVendorName?: string
    /**
     * 供应商联系方式
     */
    materialVendorPhone?: string
    /**
     * 供应商材料编号/模号
     */
    vendorMaterialMouldCode?: string
    /**
     * 供应商材料名称
     */
    vendorMaterialName?: string
  }
  export type Request = Params
  export type Response = ResponseData<MaterialListAPI.List>
}
export function createMaterial(data: CreateMaterialAPI.Request) {
  return to<CreateMaterialAPI.Response>(service.post('/pdm-base/material/add', data))
}

export namespace MaterialInfoAPI {
  export interface BaseMaterialVendor {
    colorLocation?: string
    /**
     * 创建人
     */
    createById?: number
    createByName?: string
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    deliveryCycle?: number
    id?: number
    materialCode?: string
    materialId?: number
    materialPrice?: number
    materialVendorIdentification?: string
    materialVendorName?: string
    materialVendorPhone?: string
    /**
     * 操作人
     */
    modifyById?: number
    modifyByName?: string
    /**
     * 操作时间
     */
    modifyTime?: string
    vendorMaterialMouldCode?: string
    vendorMaterialName?: string
    // 联系人
    contactPerson?: string
  }
  export interface RefProductResp {
    /**
     * 品牌
     */
    brand?: string
    /**
     * 状态
     */
    dataStatus?: string
    /**
     * 设计师
     */
    designerId?: number
    /**
     * 产品配色
     */
    initialSampleColorId?: number
    /**
     * 上市季节
     */
    launchSeason?: string
    /**
     * 选品会结果
     */
    meetingResult?: string
    /**
     * 操作人
     */
    modifyId?: string
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 产品分类
     */
    productCategory?: number
    productColor?: string
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 产品定位
     */
    productPositioning?: string
    /**
     * 尺码段
     */
    sizeRangeId?: number[]
    /**
     * 适用人群
     */
    targetAudience?: string
    thumbnail?: BaseFileDTO
  }
  export interface BaseMaterial {
    brandId?: string
    brandIdList?: number[]
    /**
     * 宽幅下拉框是否可修改
     */
    breadthEnableFlag?: boolean
    categoryCnName?: string
    categoryEnName?: string
    colorCnName?: string
    colorEnName?: string
    colorId?: number
    /**
     * 创建人
     */
    createById?: number
    createByName?: string
    /**
     * 创建时间
     */
    createTime?: string
    cushionMaterial?: string
    delFlag?: number
    /**
     * 纹路下拉框是否可修改
     */
    grainEnableFlag?: boolean
    /**
     * 克重下拉框是否可修改
     */
    gramWeightEnableFlag?: boolean
    /**
     * 高度下拉框是否可修改
     */
    heightEnableFlag?: boolean
    id?: number
    /**
     * 成分下拉框是否可修改
     */
    ingredientEnableFlag?: boolean
    insoleMaterial?: string
    materialBreadth?: string
    materialCategoryCode?: string
    materialCategoryId?: number
    materialCnName?: string
    materialCode?: string
    materialEnName?: string
    materialGrain?: string
    materialGramWeight?: string
    materialHeight?: number
    materialIngredient?: string
    materialSize?: number
    materialTexture?: string
    materialThickness?: string
    materialUnit?: string
    materialVendorList?: BaseMaterialVendor[]
    /**
     * 操作人
     */
    modifyById?: number
    modifyByName?: string
    /**
     * 操作时间
     */
    modifyTime?: Date
    otherImageFileList?: BaseFileDTO[]
    processDescription?: string
    productCode?: string
    productCodeList?: string[]
    productRespList?: RefProductResp[]
    productSkcRespList?: ProductSkcInfoPageAPI.List[]
    remark?: string
    status?: string
    substrateMaterial?: string
    /**
     * 材质下拉框是否可修改
     */
    textureEnableFlag?: boolean
    /**
     * 厚度下拉框是否可修改
     */
    thicknessEnableFlag?: boolean
    thumbnailFile?: BaseFileDTO
    useFlag?: boolean
  }
  export type Response = ResponseData<BaseMaterial>
}

// 供应商下拉框数组
export namespace ModelVendorSelectApi {
  export interface Data {
    /**
     * 供应商编码
     */
    code?: string
    /**
     * 联系人
     */
    contactPerson?: string
    /**
     * 供应商名称
     */
    name?: string
    /**
     * 电话
     */
    telephone?: string
    dictValue?: string
    dictCnName?: string
    useStatus?: Status
  }
  export enum Status {
    USE = 1,
    UNUSE = 0
  }
  export type Response = ResponseData<Data[]>
}

export function viewMaterial(id: number) {
  return to<MaterialInfoAPI.Response>(service.get(`/pdm-base/material/detail?materialId=${id}`))
}

export function viewMaterialByVersion(versionId: number) {
  return to<MaterialInfoAPI.Response>(service.get(`/pdm-base/material/versionDetail/${versionId}`))
}

export namespace EditMaterialAPI {
  export interface Params extends CreateMaterialAPI.Params {
    id?: number
    status?: string
  }
  export type Request = Params
  export type Response = ResponseData<MaterialListAPI.List>
}
export function editMaterial(data: EditMaterialAPI.Request) {
  return to<EditMaterialAPI.Response>(service.post('/pdm-base/material/update', data))
}

export function getVendorList() {
  return to<ModelVendorSelectApi.Response>(service.get(`/pdm-base/modelVendor/vendorList`))
}
