import { service } from '@/config/axios/service'
import to from 'await-to-js'

export namespace UpdateStatusAPI {
  export interface StatusData {
    beforeStatus: string
    id: number
    status: string
  }
  export type Request = StatusData[]
  export type Response = BasicResponseData
}

export function changeStatus(data: UpdateStatusAPI.Request) {
  return to<UpdateStatusAPI.Response>(service.post(`/pdm-base/material/status`, data))
}
