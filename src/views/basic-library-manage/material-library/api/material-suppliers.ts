import { service } from '@/config/axios/service'
import to from 'await-to-js'
// 供应商列表
export namespace ModelVendorListApi {
  export interface Params {
    /**
     * 供应商编码
     */
    code?: string
    /**
     * 页码
     */
    current?: number
    /**
     * 结束时间
     */
    endTime?: string
    idList?: string[]
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 供应商名称
     */
    name?: string
    /**
     * 性质
     */
    nature?: string[]
    /**
     * 区域
     */
    region?: string[]
    /**
     * 页面尺寸
     */
    size?: number
    /**
     * 开始时间
     */
    startTime?: string
    /**
     * 状态
     * 状态code
     */
    status?: string[]
    /**
     * 类别
     */
    type?: string[]
  }

  export interface Data {
    /**
     * 地址
     */
    address?: string
    /**
     * 供应商编码
     */
    code?: string
    /**
     * 联系人
     */
    contactPerson?: string
    /**
     * 操作人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    /**
     * 供应商英文名称
     */
    englishName?: string
    /**
     * 经验范围
     */
    experienceRange?: string
    /**
     * id
     */
    id?: number
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 供应商名称
     */
    name?: string
    /**
     * 性质
     */
    nature?: string
    /**
     * 区域
     */
    region?: string
    /**
     * 相关说明文档
     */
    relatedDocumentation?: BaseFileDTO[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 状态
     * 状态code
     */
    status?: string
    /**
     * 电话
     */
    telephone?: string
    /**
     * 类别
     */
    type?: string
  }

  /**
   * BaseFileDTO
   */
  export interface BaseFileDTO {
    bucketName?: string
    businessId?: number
    /**
     * 文件名
     */
    fileName?: string
    /**
     * 文件类型
     */
    fileType?: string
    /**
     * 文件相对路径
     */
    fileUrl?: string
    id?: number
    /**
     * 文件签名下载地址
     */
    signatureUrl?: string
    /**
     * 水印种类
     */
    watermarkType?: string
    [property: string]: any
  }
  export type Request = Params & PageParams
  export type Response = PagedResponseData<Data>
}

// 材料供应商分页查询
export function getVendorListByPage(params, signal?: AbortSignal) {
  return to<ModelVendorListApi.Response>(
    service.get('/pdm-base/modelVendor/page', { params, signal })
  )
}

// 启用/禁用
export namespace ModelVendorStartOrBanApi {
  export interface Params {
    updateStatusReqList?: BaseUpdateStatusReq[]
  }

  export interface BaseUpdateStatusReq {
    /**
     * id
     */
    id?: number
    /**
     * 修改后的状态
     */
    newStatus?: string
    /**
     * 修改前的状态
     */
    oldStatus?: string
  }

  /**
   * 修改后的状态
   *
   * 修改前的状态
   */
  export enum Status {
    Approving = 'APPROVING',
    Ban = 'BAN',
    Draft = 'DRAFT',
    Start = 'START'
  }

  export type Request = Params
  export type Response = BasicResponseData
}
export function updateVendorStatus(data: ModelVendorStartOrBanApi.Request) {
  return to<ModelVendorStartOrBanApi.Response>(
    service.post('/pdm-base/modelVendor/startOrBan', data)
  )
}

// 新增材料供应商
export namespace ModelVendorSaveApi {
  export interface Params {
    /**
     * 地址
     */
    address?: string
    /**
     * 供应商编码
     */
    code?: string
    /**
     * 联系人
     */
    contactPerson?: string
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    /**
     * 供应商英文名称
     */
    englishName?: string
    /**
     * 经验范围
     */
    experienceRange?: string
    id?: number
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 供应商名称
     */
    name?: string
    /**
     * 性质
     */
    nature?: string
    /**
     * 区域
     */
    region?: string
    /**
     * 相关说明文档
     */
    relatedDocumentation?: BaseFileDTO[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 状态
     * 状态code
     */
    status?: string
    /**
     * 电话
     */
    telephone?: string
    /**
     * 类别
     */
    type?: string[]
  }
  /**
   * BaseFileDTO
   */
  export interface BaseFileDTO {
    bucketName?: string
    businessId?: number
    /**
     * 文件名
     */
    fileName?: string
    /**
     * 文件类型
     */
    fileType?: string
    /**
     * 文件相对路径
     */
    fileUrl?: string
    id?: number
    /**
     * 文件签名下载地址
     */
    signatureUrl?: string
    /**
     * 水印种类
     */
    watermarkType?: string
    [property: string]: any
  }

  export type Request = Params
  export type Response = BasicResponseData
}

export function saveModelVendor(data: ModelVendorSaveApi.Request) {
  return to<ModelVendorSaveApi.Response>(service.post('/pdm-base/modelVendor/save', data))
}

// 修改材料供应商
export function updateModelVendor(data: ModelVendorSaveApi.Request) {
  return to<ModelVendorSaveApi.Response>(service.post('/pdm-base/modelVendor/update', data))
}

// 材料供应商详情
export namespace VendorDetailApi {
  export interface Data {
    /**
     * 地址
     */
    address?: string
    /**
     * 供应商编码
     */
    code?: string
    /**
     * 联系人
     */
    contactPerson?: string
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    /**
     * 供应商英文名称
     */
    englishName?: string
    /**
     * 经验范围
     */
    experienceRange?: string
    /**
     * id
     */
    id?: number
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 供应商名称
     */
    name?: string
    /**
     * 性质
     */
    nature?: string
    /**
     * 区域
     */
    region?: string
    /**
     * 相关说明文档
     */
    relatedDocumentation?: BaseFileDTO[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 状态
     * 状态code
     */
    status?: string
    /**
     * 电话
     */
    telephone?: string
    /**
     * 类别
     */
    type?: string
  }

  /**
   * BaseFileDTO
   */
  export interface BaseFileDTO {
    bucketName?: string
    businessId?: number
    /**
     * 文件名
     */
    fileName?: string
    /**
     * 文件类型
     */
    fileType?: string
    /**
     * 文件相对路径
     */
    fileUrl?: string
    id?: number
    /**
     * 文件签名下载地址
     */
    signatureUrl?: string
    /**
     * 水印种类
     */
    watermarkType?: string
    [property: string]: any
  }
  export type Response = ResponseData<Data>
}

export function getModelVendorDetailById(id: string) {
  return to<VendorDetailApi.Response>(service.get(`/pdm-base/modelVendor/detail/${id}`))
}

// 版本记录
export namespace ModelVendorVersionApi {
  export interface Data {
    /**
     * 数据变更时间
     */
    dataTime?: string
    id?: number
    /**
     * 操作人
     */
    operator?: string
    /**
     * 操作人id
     */
    operatorId?: number
    /**
     * 状态
     */
    status?: string
    /**
     * 版本号
     */
    versionCode?: string
    /**
     * 版本说明
     */
    versionRemark?: string
    /**
     * 版本生效时间
     */
    versionTime?: string
    /**
     * 版本变更类型
     */
    versionType?: string
  }
  export type Response = ResponseData<Data[]>
}

export function getVersionListById(id: number) {
  return to<ModelVendorVersionApi.Response>(service.get(`/pdm-base/modelVendor/version/${id}`))
}

// 版本记录详情 /modelVendor/versionDetail/{versionId}
export namespace ModelVendorVersionDetailApi {
  export interface Params {
    versionId: string
  }
  export interface Data {
    /**
     * 地址
     */
    address?: string
    /**
     * 供应商编码
     */
    code?: string
    /**
     * 联系人
     */
    contactPerson?: string
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    /**
     * 供应商英文名称
     */
    englishName?: string
    /**
     * 经验范围
     */
    experienceRange?: string
    /**
     * id
     */
    id?: number
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 供应商名称
     */
    name?: string
    /**
     * 性质
     */
    nature?: string
    /**
     * 区域
     */
    region?: string
    /**
     * 相关说明文档
     */
    relatedDocumentation?: BaseFileDTO[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 状态
     * 状态code
     */
    status?: string
    /**
     * 电话
     */
    telephone?: string
    /**
     * 类别
     */
    type?: string
  }
  /**
   * BaseFileDTO
   */
  export interface BaseFileDTO {
    bucketName?: string
    businessId?: number
    /**
     * 文件名
     */
    fileName?: string
    /**
     * 文件类型
     */
    fileType?: string
    /**
     * 文件相对路径
     */
    fileUrl?: string
    id?: number
    /**
     * 文件签名下载地址
     */
    signatureUrl?: string
    /**
     * 水印种类
     */
    watermarkType?: string
    [property: string]: any
  }
  export type Request = Params
  export type Response = ResponseData<Data>
}

export function getVersionDetailById(versionId: string) {
  return to<ModelVendorVersionDetailApi.Response>(
    service.get(`/pdm-base/modelVendor/versionDetail/${versionId}`)
  )
}

export enum OperationTypeEnum {
  EDIT = 'edit',
  VIEW = 'view',
  ADD = 'add'
}
