import { service } from '@/config/axios/service'
import to from 'await-to-js'

export namespace MaterialListAPI {
  export interface Params {
    /**
     * 供应商材料编号/模号
     */
    vendorMaterialMouldCode?: string
    /**
     * 品牌列表
     */
    brandIdList?: number[]
    /**
     * 色卡库位
     */
    colorLocation?: string
    /**
     * 材料颜色
     */
    colorName?: string
    /**
     * 操作结束时间
     */
    endTime?: string
    /**
     * 材料分类ID列表
     */
    materialCategoryIdList?: number[]
    /**
     * 材料编码
     */
    materialCode?: string
    /**
     * 材料英文名称
     */
    materialEnName?: string
    materialCnName?: string
    /**
     * 纹路列表
     */
    materialGrainList?: string[]
    /**
     * 材质列表
     */
    materialTextureList?: string[]
    /**
     * 材料供应商名称
     */
    materialVendorName?: string
    /**
     * 产品编号
     */
    productCodeList?: string[]
    /**
     * 操作开始时间
     */
    startTime?: string
    /**
     * 状态列表
     */
    statusList?: string[]
  }
  export interface Row {
    /**
     * 品牌
     */
    brandName?: string
    /**
     * 分类中文名
     */
    categoryCnName?: string
    /**
     * 分类英文名
     */
    categoryEnName?: string
    /**
     * 色卡库位
     */
    colorLocation?: string
    /**
     * 数据ID
     */
    id?: number
    /**
     * 宽幅
     */
    materialBreadth?: string
    /**
     * 材料中文名
     */
    materialCnName?: string
    /**
     * 材料编码
     */
    materialCode?: string
    /**
     * 材料颜色
     */
    materialColor?: string
    /**
     * 材料英文名
     */
    materialEnName?: string
    /**
     * 纹路
     */
    materialGrain?: string
    /**
     * 克重
     */
    materialGramWeight?: string
    /**
     * 成分
     */
    materialIngredient?: string
    /**
     * 材质
     */
    materialTexture?: string
    /**
     * 厚度
     */
    materialThickness?: string
    /**
     * 材料供应商标识
     */
    materialVendorIdentification?: string
    /**
     * 供应商名称
     */
    materialVendorName?: string
    /**
     * 供应商联系方式
     */
    materialVendorPhone?: string
    /**
     * 操作人
     */
    modifyByName?: string
    /**
     * 操作时间
     */
    modifyTime?: Date
    /**
     * 产品编号
     */
    productCode?: string
    /**
     * 产品配色
     */
    productColor?: string
    /**
     * 状态
     */
    status?: string
    /**
     * 状态code
     */
    statusCode?: string
    /**
     * 缩略图
     */
    thumbnailUrl?: string
    /**
     * 供应商材料编号/模具编号
     */
    vendorMaterialMouldCode?: string
  }
  export type List = Row[]
  export type Request = Params & PageParams
  export type Response = PagedResponseData<Row>
}
export function getMaterialListByPage(data: MaterialListAPI.Request, signal?: AbortSignal) {
  return to<MaterialListAPI.Response>(service.post('/pdm-base/material/page', data, { signal }))
}

export namespace MaterialCategoryListAPI {
  export interface Data {
    childList?: Data[]
    parentKey?: number
    selectorCode?: string
    selectorEnValue?: string
    selectorKey?: number
    selectorValue?: string
    status?: string
    [key: string]: any
  }
  export type Response = ResponseData<Data[]>
}
export function getMaterialCategoryList() {
  return to<MaterialCategoryListAPI.Response>(service.get('/pdm-base/material/category/tree'))
}

export function downloadLabel(params: { idList: number[] }) {
  return to<ResponseData<string>>(service.get('/pdm-base/material/downloadLabelling', { params }))
}
