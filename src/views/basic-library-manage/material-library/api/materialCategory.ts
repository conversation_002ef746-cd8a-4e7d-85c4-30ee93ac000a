import { service } from '@/config/axios/service'
import to from 'await-to-js'

export namespace MaterialCategoryListAPI {
  export interface Params {
    status?: string[]
    categoryName?: string
    parentId?: number
  }
  export interface Row {
    enNameAbbreviation?: string
    categoryCnName?: string
    categoryCode?: string
    materialClassificationCodeList: string[]
    categoryEnName?: string
    categoryParentCnName?: string
    categoryParentEnName?: string
    childList?: Row[]
    /**
     * 创建人
     */
    createById?: number
    createByName?: string
    /**
     * 创建时间
     */
    createTime?: Date
    delFlag?: number
    id?: number
    lastDataStatusEnum?: string
    /**
     * 操作人
     */
    modifyById?: number
    modifyByName?: string
    /**
     * 操作时间
     */
    modifyTime?: Date
    parentId?: number
    status?: string
    wmsCategoryCode?: string
    wmsCategoryName?: string
  }
  export type List = Row[]
  export type Response = ResponseData<List>
  export type Request = Params
}
export function getMaterialCategoryList(data: MaterialCategoryListAPI.Request) {
  return to<MaterialCategoryListAPI.Response>(
    service.post('/pdm-base/material/category/lists', data)
  )
}
export interface IResCategory {
  isSuccess: boolean
  msg: string
}

export namespace UpdateMaterialCategoryStatusAPI {
  export interface Params {
    beforeStatus: string
    id: number
    status: string
  }
  export type Response = BasicResponseData
  export type Request = Params
}
export function updateMaterialCategoryStatus(data: UpdateMaterialCategoryStatusAPI.Request) {
  return to<UpdateMaterialCategoryStatusAPI.Response>(
    service.post('/pdm-base/material/category/status', data)
  )
}

export namespace AddMaterialCategoryAPI {
  export interface Params {
    /**
     * 材料小类英文简称
     */
    enNameAbbreviation?: string
    /**
     * 材料分类名称
     */
    categoryCnName?: string
    /**
     * 材料分类编码
     */
    categoryCode?: string
    /**
     * 材料分类英文名称
     */
    categoryEnName?: string
    errorParam?: string[]
    /**
     * 上级ID 一级分类固定为0
     */
    parentId?: number
    /**
     * 材质分类
     */
    materialClassificationCodeList?: string[]
    /**
     * 状态
     */
    status?: string
  }
  export type Response = BasicResponseData
  export type Request = Params
}
export function addMaterialCategory(data: AddMaterialCategoryAPI.Request) {
  return to<AddMaterialCategoryAPI.Response>(service.post('/pdm-base/material/category/add', data))
}

export namespace EditMaterialCategoryAPI {
  export interface Params {
    /**
     * 材料分类名称
     */
    categoryCnName?: string
    /**
     * 材料分类编码
     */
    categoryCode?: string
    /**
     * 材料分类英文名称
     */
    categoryEnName?: string
    errorParam?: string[]
    /**
     * 数据ID
     */
    id?: number
    /**
     * 状态
     */
    status?: string
  }
  export type Response = BasicResponseData
  export type Request = Params
}
export function editMaterialCategory(data: EditMaterialCategoryAPI.Request) {
  return to<EditMaterialCategoryAPI.Response>(
    service.post('/pdm-base/material/category/update', data)
  )
}
