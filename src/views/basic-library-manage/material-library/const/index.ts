export interface MaterialCategoryAttr {
  label: string
  value: string
}

export enum MaterialCategoryUpdateEnum {
  /**
   * 新增大类
   */
  ADD_ROOT = 1,
  /**
   * 新增小类
   */
  ADD_LEAF = 2,
  /**
   * 修改大类
   */
  UPDATE_ROOT = 3,
  /**
   * 修改小类
   */
  UPDATE_LEAF = 4
}

export enum MaterialNameEnum {
  /**
   * 皮料
   */
  LEATHER = '皮料',
  /**
   * PU
   */
  PU = 'PU',
  /**
   * 纺织物
   */
  TEXTILE = '纺织物',
  /**
   * 塑胶
   */
  PLASTIC = '塑胶'
}
