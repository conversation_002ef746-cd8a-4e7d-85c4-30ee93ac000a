<script lang="ts" setup>
import { ElForm, ElMessage, FormRules } from 'element-plus'
import {
  getModelVendorDetailById,
  getVersionDetailById,
  ModelVendorSaveApi,
  OperationTypeEnum,
  saveModelVendor,
  updateModelVendor
} from '@/views/basic-library-manage/material-library/api/material-suppliers'

type FormData = Partial<ModelVendorSaveApi.Params>

defineOptions({
  name: 'EditSupplierDialog'
})

const props = defineProps<{
  modelValue: boolean
  currentRow: any | undefined
  type: OperationTypeEnum
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'update:currentRow', val: any): void
  (e: 'refresh'): void
}>()

const defaultFormData: FormData = {
  code: '',
  name: '',
  englishName: '',
  region: '',
  nature: '',
  type: [],
  telephone: '',
  contactPerson: '',
  experienceRange: '',
  address: '',
  remark: '',
  relatedDocumentation: []
}

const formRules: FormRules<ModelVendorSaveApi.Params> = {
  name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
  englishName: [{ required: true, message: '请输入供应商英文名称', trigger: 'blur' }],
  region: [{ required: true, message: '请输入区域', trigger: 'change' }],
  nature: [{ required: true, message: '请输入性质', trigger: 'change' }],
  type: [{ required: true, message: '请输入类别', trigger: 'change' }],
  telephone: [{ required: true, message: '请输入电话', trigger: 'blur' }]
}

const formRef = ref<ComponentRef<typeof ElForm>>()
const formData = ref<FormData>({ ...defaultFormData })
const submitLoading = ref(false)

const isEdit = computed(() => props.type === OperationTypeEnum.EDIT)
const isView = computed(() => props.type === OperationTypeEnum.VIEW)
const isAdd = computed(() => props.type === OperationTypeEnum.ADD)

const getTitle = computed(() =>
  isEdit.value ? '编辑供应商' : isView.value ? '查看供应商' : '新增供应商'
)
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
  formData.value = { ...defaultFormData }
  emit('update:currentRow', null)
}

const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (valid) {
    const api = isEdit.value ? updateModelVendor : saveModelVendor
    const params = {
      ...formData.value
    }
    submitLoading.value = true
    const [error, result] = await api(params)
    submitLoading.value = false
    if (!error && result) {
      ElMessage.success(result.msg || '操作成功')
      handleClose()
      emit('refresh')
    } else {
      ElMessage.error(error?.message || '操作失败')
    }
  }
}

const getDetail = async () => {
  if (isAdd.value) return
  const { id } = props.currentRow
  const [error, result] = isView.value
    ? await getVersionDetailById(id)
    : await getModelVendorDetailById(id)
  if (!error && result) {
    const {
      id,
      code,
      name,
      englishName,
      region,
      nature,
      type,
      telephone,
      contactPerson,
      experienceRange,
      address,
      remark,
      relatedDocumentation
    } = result.datas
    Object.assign(formData.value, {
      id,
      code,
      name,
      englishName,
      region,
      nature,
      type,
      telephone,
      contactPerson,
      experienceRange,
      address,
      remark,
      relatedDocumentation
    })
  }
}

watch(visible, () => {
  if (visible.value) {
    getDetail()
  }
})
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :title="getTitle"
    width="60%"
    :parent-scroll="false"
  >
    <ElForm
      class="mt-2"
      ref="formRef"
      :disabled="isView"
      :model="formData"
      :rules="formRules"
      labelWidth="130px"
    >
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="供应商编码" prop="code">
            <ElInput
              class="w-48"
              v-model="formData.code"
              clearable
              disabled
              maxlength="3"
              placeholder="请输入"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="供应商名称" prop="name">
            <ElInput class="w-48" v-model="formData.name" clearable placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="供应商英文名称" prop="englishName">
            <ElInput class="w-48" v-model="formData.englishName" clearable placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="区域" prop="region">
            <SelectPlus
              v-model="formData.region"
              api-key="COMMON_REGION"
              cache
              class="w-full"
              clearable
              collapse-tags
              collapse-tags-tooltip
              filterable
              placeholder="请选择"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="性质" prop="nature">
            <SelectPlus
              v-model="formData.nature"
              api-key="MATERIAL_VENDOR_NATURE"
              class="w-full"
              clearable
              filterable
              placeholder="请选择"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="类别" prop="type">
            <SelectPlus
              v-model="formData.type"
              api-key="MATERIAL_VENDOR_TYPE"
              class="w-full"
              clearable
              collapse-tags
              collapse-tags-tooltip
              filterable
              multiple
              placeholder="请选择"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="电话" prop="telephone">
            <ElInput class="w-48" v-model="formData.telephone" clearable placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="联系人" prop="contactPerson">
            <ElInput class="w-48" v-model="formData.contactPerson" clearable placeholder="请输入" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="经验范围" prop="experienceRange">
            <ElInput
              class="w-48"
              v-model="formData.experienceRange"
              :resize="isView ? 'none' : undefined"
              clearable
              maxlength="500"
              placeholder="请输入"
              type="textarea"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="地址" prop="address">
            <ElInput
              class="w-48"
              v-model="formData.address"
              :resize="isView ? 'none' : undefined"
              clearable
              maxlength="500"
              placeholder="请输入"
              type="textarea"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="相关说明文档" prop="relatedDocumentation">
            <OssUpload
              v-model="formData.relatedDocumentation"
              :limit="20"
              :size-limit="1024 * 1024 * 100"
              drag
              list-type="text"
              muliple
            >
              <template #trigger>
                <el-button>
                  <Icon icon="material-symbols:upload" />
                  <div class="leading-4">上传文件</div>
                </el-button>
              </template>
              <template #tip>支持所有文件格式，单个文件不能超过100MB</template>
            </OssUpload>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="备注" prop="remark">
            <ElInput
              class="w-48"
              v-model="formData.remark"
              :resize="isView ? 'none' : undefined"
              clearable
              maxlength="500"
              placeholder="请输入备注"
              type="textarea"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton v-if="!isView" :loading="submitLoading" type="primary" @click="handleSubmit">
        确定
      </ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
