<script lang="ts" setup>
import {
  addMaterialCategory,
  AddMaterialCategoryAPI,
  editMaterialCategory,
  MaterialCategoryListAPI
} from '@/views/basic-library-manage/material-library/api/materialCategory'
import { statusConst, StatusEnum } from '@/views/basic-library-manage/const'
import { MaterialCategoryUpdateEnum } from '@/views/basic-library-manage/material-library/const'
import { ElForm, ElMessage, ElRadio, ElRadioGroup, FormRules } from 'element-plus'
import { omit } from 'lodash-es'

defineOptions({
  name: 'EditCategoryDialog'
})

const props = defineProps<{
  modelValue: boolean
  selectedRow: MaterialCategoryListAPI.Row | undefined
  editType: number
}>()

const isRootNode = computed(() =>
  [MaterialCategoryUpdateEnum.ADD_ROOT, MaterialCategoryUpdateEnum.UPDATE_ROOT].includes(
    props.editType
  )
)

const isAdd = computed(() =>
  [MaterialCategoryUpdateEnum.ADD_ROOT, MaterialCategoryUpdateEnum.ADD_LEAF].includes(
    props.editType
  )
)

const isUpdate = computed(() =>
  [MaterialCategoryUpdateEnum.UPDATE_ROOT, MaterialCategoryUpdateEnum.UPDATE_LEAF].includes(
    props.editType
  )
)

const title = computed(() => (isRootNode.value ? '大' : '小'))

const statusList = statusConst.statusList.filter((e) => e.value !== StatusEnum.APPROVING)

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
type FormData = Partial<
  AddMaterialCategoryAPI.Params & {
    id: number
    categoryParentCnName: string
  }
>
const defaultFormData = {
  categoryCnName: '',
  categoryCode: '',
  categoryEnName: '',
  errorParam: [],
  parentId: 0,
  status: StatusEnum.DRAFT,
  id: 0,
  categoryParentCnName: '',
  enNameAbbreviation: ''
}
const formRef = ref<ComponentRef<typeof ElForm>>()
const formData = ref<FormData>({ ...defaultFormData })
const formRules = ref<
  FormRules<
    Partial<AddMaterialCategoryAPI.Params> & {
      id?: number
    }
  >
>({
  enNameAbbreviation: [{ required: true, message: '请输入材料小类简称', trigger: 'blur' }],
  status: [{ required: true }]
})
watch(visible, (val) => {
  if (val) {
    const {
      categoryCnName,
      categoryParentCnName,
      id,
      parentId,
      categoryCode,
      categoryEnName,
      status,
      materialClassificationCodeList,
      enNameAbbreviation
    } = props.selectedRow!

    if (!isRootNode.value) {
      formData.value.categoryParentCnName = isAdd.value ? categoryCnName : categoryParentCnName
      formData.value.parentId = isAdd.value ? id : parentId
    }
    if (isUpdate.value) {
      formData.value.categoryCnName = categoryCnName
      formData.value.categoryCode = categoryCode
      formData.value.categoryEnName = categoryEnName
      formData.value.status = status
      formData.value.id = id
      formData.value.enNameAbbreviation = enNameAbbreviation
      formData.value.materialClassificationCodeList = materialClassificationCodeList
    }
  }
})

const handleClose = () => {
  visible.value = false
  formData.value = { ...defaultFormData }
  unref(formRef)?.resetFields()
}

const submitLoading = ref(false)
const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (valid) {
    if (isAdd.value) {
      submitLoading.value = true
      const params = omit(formData.value, ['id', 'categoryParentCnName'])
      const [error, result] = await addMaterialCategory(params)
      submitLoading.value = false
      if (error === null && result) {
        ElMessage.success(result.msg || '添加成功')
        handleClose()
        emit('refresh')
      } else {
        ElMessage.error(error?.message || '添加失败')
      }
    } else {
      submitLoading.value = true
      const params = omit(formData.value, ['parentId', 'categoryParentCnName'])
      const [error, result] = await editMaterialCategory(params)
      submitLoading.value = false
      if (error === null && result) {
        ElMessage.success(result.msg || '添加成功')
        handleClose()
        emit('refresh')
      } else {
        ElMessage.error(error?.message || '添加失败')
      }
    }
  }
}
</script>

<template>
  <Dialog v-model="visible" :before-close="handleClose" title="操作确认" width="600px">
    <ElForm ref="formRef" :model="formData" :rules="formRules" class="mt-2 w-96" label-width="160">
      <ElFormItem v-if="!isRootNode" label="材料大类名称" prop="categoryParentCnName">
        <ElInput
          class="w-48"
          v-model="formData.categoryParentCnName"
          disabled
          maxlength="50"
          placeholder="请输入材料大类名称"
          showWordLimit
        />
      </ElFormItem>
      <ElFormItem
        :label="`材料${title}类名称`"
        :rules="[{ required: true, message: `请输入材料${title}类名称` }]"
        prop="categoryCnName"
      >
        <ElInput
          class="w-48"
          v-model="formData.categoryCnName"
          :placeholder="`请输入材料${title}类名称`"
          maxlength="50"
          showWordLimit
        />
      </ElFormItem>
      <ElFormItem
        :label="`材料${title}类英文名称`"
        :rules="[{ required: true, message: `请输入材料${title}类英文名称` }]"
        prop="categoryEnName"
      >
        <ElInput
          class="w-48"
          v-model="formData.categoryEnName"
          :placeholder="`请输入材料${title}类英文名称`"
          maxlength="100"
          showWordLimit
        />
      </ElFormItem>
      <ElFormItem
        v-if="isRootNode"
        :label="`材料${title}类编码`"
        :rules="[{ required: true, message: `请输入材料${title}类编码` }]"
        prop="categoryCode"
      >
        <ElInput
          class="w-48"
          v-model="formData.categoryCode"
          :placeholder="`请输入材料${title}类编码`"
          maxlength="2"
          showWordLimit
        />
      </ElFormItem>
      <ElFormItem v-if="!isRootNode" label="材料小类简称" prop="enNameAbbreviation">
        <ElInput
          class="w-48"
          v-model="formData.enNameAbbreviation"
          clearable
          maxlength="100"
          placeholder="请输入材料小类简称"
          show-word-limit
        />
      </ElFormItem>
      <ElFormItem
        :rules="[{ required: true, message: '请选择材质分类', trigger: 'change' }]"
        v-if="!isRootNode"
        label="材质分类"
        prop="materialClassificationCodeList"
      >
        <SelectPlus
          v-model="formData.materialClassificationCodeList"
          multiple
          api-key="Material_Category"
          cache
          clearable
          filterable
          placeholder="请选择材质分类"
        />
      </ElFormItem>
      <ElFormItem label="状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio
            v-for="item in statusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
