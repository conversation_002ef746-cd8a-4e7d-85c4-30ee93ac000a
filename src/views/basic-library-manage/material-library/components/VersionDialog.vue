<script setup lang="ts">
import { MaterialListAPI } from '../api/material-list'
import { getMaterialVersionList } from '../api/versionDialog'
import { VersionListAPI } from '../../api/common'

defineOptions({
  name: 'VersionDialog'
})

const props = defineProps<{
  modelValue: boolean
  currentRow: MaterialListAPI.Row | undefined
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const tableData = ref<VersionListAPI.Data[]>([])
const queryLoading = ref(false)
const getVersionList = async () => {
  if (!props.currentRow) {
    return
  }
  queryLoading.value = true
  const [error, result] = await getMaterialVersionList(props.currentRow.id!)
  queryLoading.value = false
  if (error === null && result?.datas) {
    tableData.value = result.datas
  }
}

watch(visible, () => {
  if (visible.value) {
    getVersionList()
  }
})

const handleClose = () => {
  visible.value = false
  tableData.value = []
}

const router = useRouter()
const handleViewDetail = (row: VersionListAPI.Data) => {
  router.push({
    name: 'ViewMaterial',
    query: {
      versionId: row.id
    }
  })
  handleClose()
}
</script>

<template>
  <Dialog v-model="visible" title="版本记录" :before-close="handleClose">
    <VxeTable :loading="queryLoading" :data="tableData" :max-height="500">
      <VxeColumn field="versionCode" title="版本号" />
      <VxeColumn field="versionType" title="版本变更类型" />
      <VxeColumn field="operator" title="修改者" />
      <VxeColumn field="dataTime" title="修改时间" />
      <VxeColumn field="versionRemark" title="版本说明" />
      <VxeColumn title="修改明细">
        <template #default="{ row }">
          <el-button type="primary" text size="small" @click="handleViewDetail(row)">
            查看详情
          </el-button>
        </template>
      </VxeColumn>
    </VxeTable>
  </Dialog>
</template>

<style scoped lang="less"></style>
