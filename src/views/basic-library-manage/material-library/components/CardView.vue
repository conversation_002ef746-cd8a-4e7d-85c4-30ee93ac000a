<script lang="ts" setup>
import { Icon } from '@/components/Icon'
import { MaterialListAPI } from '../api/material-list'
import { StatusEnum } from '../../const'
import { BaseClamp, MouseoverEvent } from '@/components/BaseClamp'
import { ElTooltip } from 'element-plus'

defineOptions({
  name: 'CardView'
})

defineProps<{
  tableData: MaterialListAPI.List
  loading: boolean
  maxHeight?: number
}>()

const emit = defineEmits<{
  (e: 'change-status', val: MaterialListAPI.Row): void
  (e: 'edit-material', val: MaterialListAPI.Row): void
  (e: 'copy-material', val: MaterialListAPI.Row): void
  (e: 'view-material', val: MaterialListAPI.Row): void
}>()

const contentRef = ref<EventTarget>()
const content = ref<string>('')
const tooltipRef = ref<InstanceType<typeof ElTooltip>>()

const handleMouseover = (e: MouseoverEvent) => {
  if (!e.clamped) {
    return
  }
  contentRef.value = e.currentTarget
  content.value = e.text
}
</script>

<template>
  <ElScrollbar>
    <div
      v-loading="loading"
      :style="{ maxHeight: maxHeight + 'px' }"
      class="grid justify-center min-h-[140px] grid-gap-[10px] grid-cols-[repeat(auto-fit,minmax(300px,1fr))]"
    >
      <div v-for="item in tableData" :key="item.id" class="p-2">
        <ElCard
          :body-style="{ padding: '0px', width: '100%', height: '100%' }"
          class="card-container"
          shadow="always"
          @click="emit('view-material', item)"
        >
          <div class="card-body">
            <div class="card-content">
              <div class="card-img-container">
                <ElImage
                  :src="item.thumbnailUrl"
                  hide-on-click-modal
                  class="card-img"
                  fit="cover"
                  loading="lazy"
                />
                <ElTag class="card-status" effect="dark">
                  {{ item.status }}
                </ElTag>
              </div>
              <div class="card-button-container">
                <ElButton
                  v-hasPermi="['editMaterial']"
                  class="card-button"
                  size="small"
                  text
                  type="primary"
                  @click.stop="emit('edit-material', item)"
                >
                  <span class="card-button-content">
                    <Icon class="mb-1" icon="ep:edit" />
                    修改
                  </span>
                </ElButton>
                <ElButton
                  v-hasPermi="['copyMaterial']"
                  class="card-button"
                  size="small"
                  text
                  type="primary"
                  @click.stop="emit('copy-material', item)"
                >
                  <span class="card-button-content">
                    <Icon class="mb-1" icon="ep:copy-document" />
                    复制
                  </span>
                </ElButton>
                <ElButton
                  v-hasPermi="['material:changeStatus']"
                  class="card-button"
                  size="small"
                  text
                  type="primary"
                  @click.stop="emit('change-status', item)"
                >
                  <span class="card-button-content">
                    <Icon
                      :icon="item.statusCode === StatusEnum.START ? 'mdi:ban' : 'ep:video-play'"
                      class="mb-1"
                    />
                    {{ item.statusCode === StatusEnum.START ? '禁用' : '启用' }}
                  </span>
                </ElButton>
              </div>
            </div>
            <div class="card-content">
              <div class="card-info-container">
                <div class="h-1/2">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.materialCode"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.materialCode }}
                  </BaseClamp>
                </div>
                <div class="w-full h-1/2 flex items-center">
                  <div class="w-1/2">
                    <BaseClamp
                      :max-lines="1"
                      :text-content="item.materialCnName"
                      autoresize
                      @mouseover="handleMouseover"
                    >
                      {{ item.materialCnName }}
                    </BaseClamp>
                  </div>
                  <div class="w-1/2">
                    <BaseClamp
                      :max-lines="1"
                      :text-content="item.materialEnName"
                      autoresize
                      @mouseover="handleMouseover"
                    >
                      {{ item.materialEnName }}
                    </BaseClamp>
                  </div>
                </div>
              </div>
              <div class="card-info-container">
                <BaseClamp
                  :max-lines="1"
                  :text-content="item.categoryCnName"
                  autoresize
                  @mouseover="handleMouseover"
                >
                  {{ item.categoryCnName }}
                </BaseClamp>
              </div>
              <div class="card-info-container">
                <BaseClamp
                  :max-lines="1"
                  :text-content="item.brandName"
                  autoresize
                  @mouseover="handleMouseover"
                >
                  {{ item.brandName }}
                </BaseClamp>
              </div>
              <div class="card-info-container !border-bottom-0">
                <BaseClamp
                  :max-lines="1"
                  :text-content="item.materialVendorName"
                  autoresize
                  @mouseover="handleMouseover"
                >
                  {{ item.materialVendorName }}
                </BaseClamp>
                <BaseClamp
                  :max-lines="1"
                  :text-content="item.materialColor"
                  autoresize
                  @mouseover="handleMouseover"
                >
                  {{ item.materialColor }}
                </BaseClamp>
              </div>
              <div
                class="card-info-container !border-bottom-0 border-top-1 border-left-1 border-solid mt-[-2px] ml-[-1px]"
              >
                <div class="flex items-center text-center h-full w-full">
                  <div class="w-[40%]">
                    <BaseClamp
                      :max-lines="1"
                      :text-content="item.modifyByName"
                      autoresize
                      @mouseover="handleMouseover"
                    >
                      {{ item.modifyByName }}
                    </BaseClamp>
                  </div>
                  <div class="w-[60%]">
                    <BaseClamp
                      :max-lines="1"
                      :text-content="item.modifyByName"
                      autoresize
                      @mouseover="handleMouseover"
                    >
                      {{ item.modifyTime }}
                    </BaseClamp>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ElCard>
      </div>
      <ElTooltip
        ref="tooltipRef"
        :content="content"
        :popper-options="{
          modifiers: [
            {
              name: 'computeStyles',
              options: {
                adaptive: false,
                enabled: false
              }
            }
          ]
        }"
        :virtual-ref="contentRef as HTMLElement"
        popper-class="singleton-tooltip"
        virtual-triggering
      />
    </div>
  </ElScrollbar>
</template>

<style lang="less" scoped>
.card-container {
  height: 200px;
  max-width: 330px;

  &:hover {
    cursor: pointer;
  }

  .card-body {
    display: flex;
    height: 100%;
    justify-content: space-between;

    > .card-content {
      width: 50%;
      font-size: 12px;

      > .card-img-container {
        position: relative;
        width: 100%;
        height: 80%;
        overflow: hidden;

        > .card-img {
          width: 100%;
          height: 100%;
        }

        > .card-status {
          position: absolute;
          top: 0;
          left: 0;
          border-radius: 0 var(--el-card-border-radius) var(--el-card-border-radius)
            var(--el-card-border-radius);
        }
      }

      > .card-button-container {
        height: 20%;

        > .card-button {
          width: 33.33%;
          height: auto;

          .card-button-content {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
        }

        > .card-button + .card-button {
          margin-left: 0;
        }
      }

      > .card-info-container {
        display: flex;
        width: 100%;
        height: 20%;
        padding: 0 8px;
        text-align: center;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
