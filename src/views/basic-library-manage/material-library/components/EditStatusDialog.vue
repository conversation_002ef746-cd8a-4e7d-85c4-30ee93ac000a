<script setup lang="ts">
import { ModelVendorListApi } from '../api/material-suppliers'
import { updateVendorStatus } from '../api/material-suppliers'
import { ElMessage } from 'element-plus'
import { statusConst, StatusEnum } from '../../const'

type Row = ModelVendorListApi.Data & {
  // 原始状态
  oldStatus?: string
  // 新状态
  newStatus?: string
}

defineOptions({
  name: 'EditStatusDialog'
})

const props = defineProps<{
  modelValue: boolean
  currentRows: Row[]
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'update:currentRows', val: any): void
  (e: 'refresh'): void
}>()

// 状态枚举
const statusList = statusConst.statusList.filter((item) =>
  [StatusEnum.DRAFT, StatusEnum.START, StatusEnum.BAN].includes(item.value as StatusEnum)
)
const submitLoading = ref(false)
const tableData = ref<Row[]>([])

const handleClose = () => {
  visible.value = false
  submitLoading.value = false
  tableData.value = []
}

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const handleSubmit = async () => {
  const hasError = tableData.value.some((item) => {
    return item.newStatus === undefined || item.newStatus === ''
  })
  if (hasError) {
    ElMessage.warning('请选择状态')
    return
  }
  const updateStatusReqList = tableData.value.map((item) => {
    const { id, oldStatus, newStatus } = item
    return {
      id,
      oldStatus,
      newStatus
    }
  })
  submitLoading.value = true
  const [error, result] = await updateVendorStatus({ updateStatusReqList })
  if (!error && result) {
    ElMessage.success(result.msg || '操作成功')
    handleClose()
    emit('refresh')
    emit('update:currentRows', [])
  } else {
    submitLoading.value = false
  }
}

const getStatusList = (row: Row) => {
  if (row.status === StatusEnum.DRAFT) {
    return statusList.filter((e) => e.value === StatusEnum.START || e.value === StatusEnum.BAN)
  }
  if (row.status === StatusEnum.START) {
    return statusList.filter((e) => e.value === StatusEnum.BAN)
  }
  if (row.status === StatusEnum.BAN) {
    return statusList.filter((e) => e.value === StatusEnum.START)
  }
}

watch(visible, () => {
  if (visible.value) {
    tableData.value =
      props.currentRows?.map((item) => {
        return {
          ...item,
          oldStatus: item.status,
          newStatus: ''
        }
      }) || []
  }
})
</script>
<template>
  <Dialog v-model="visible" title="操作确认" :before-close="handleClose" width="70%">
    <VxeTable :data="tableData" :max-height="500">
      <VxeColumn type="seq" title="序号" width="60" />
      <VxeColumn field="code" title="供应商编码" />
      <VxeColumn field="name" title="供应商名称" />
      <VxeColumn field="englishName" title="供应商英文名称" />
      <VxeColumn field="regionItemName" title="区域" />
      <VxeColumn field="natureItemName" title="性质" />
      <VxeColumn field="typeItemName" title="类别" />
      <VxeColumn field="contactPerson" title="联系人" />
      <VxeColumn field="telephone" title="电话" />
      <VxeColumn field="address" title="备注" />
      <VxeColumn field="statusItemName" title="当前状态" />
      <VxeColumn title="修改状态">
        <template #default="{ row }">
          <ElSelect size="small" v-model="row.newStatus" clearable>
            <ElOption
              v-for="item in getStatusList(row)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>
