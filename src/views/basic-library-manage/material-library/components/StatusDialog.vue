<script setup lang="ts">
import { MaterialListAPI } from '../api/material-list'
import { statusConst, StatusEnum } from '../../const'
import { changeStatus, UpdateStatusAPI } from '../api/statusDialog'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'StatusDialog'
})

const props = defineProps<{
  modelValue: boolean
  selectedRows: MaterialListAPI.List
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

type TableRow = Partial<MaterialListAPI.Row & UpdateStatusAPI.StatusData & { updateStatus: string }>

const tableData = ref<TableRow[]>([])

watch(visible, (val) => {
  if (val) {
    tableData.value = props.selectedRows.slice()
  }
})

const handleClose = () => {
  visible.value = false
  tableData.value = []
}

const submitLoading = ref(false)
const handleSubmit = async () => {
  const hasError = tableData.value.some((item) => {
    return item.updateStatus === undefined || item.updateStatus === ''
  })
  if (hasError) {
    ElMessage.warning('请选择状态')
    return
  }
  submitLoading.value = true
  const statusList = tableData.value.map((e) => ({
    id: e.id!,
    beforeStatus: e.statusCode!,
    status: e.updateStatus!
  }))
  const [error, result] = await changeStatus(statusList)
  submitLoading.value = false
  if (error === null && result) {
    ElMessage.success(result.msg || '操作成功')
    handleClose()
    emit('refresh')
  }
}

// 状态枚举
const statusList = statusConst.statusList

const getStatusList = (row: MaterialListAPI.Row) => {
  if (row.statusCode === StatusEnum.DRAFT) {
    return statusList.filter((e) => e.value === StatusEnum.START || e.value === StatusEnum.BAN)
  }
  if (row.statusCode === StatusEnum.START) {
    return statusList.filter((e) => e.value === StatusEnum.BAN)
  }
  if (row.statusCode === StatusEnum.BAN) {
    return statusList.filter((e) => e.value === StatusEnum.START)
  }
}
</script>

<template>
  <Dialog v-model="visible" title="操作确认" :before-close="handleClose">
    <VxeTable :data="tableData" :max-height="500">
      <VxeColumn type="seq" title="序号" width="60" />
      <VxeColumn field="materialCode" title="材料编码" />
      <VxeColumn field="categoryCnName" title="材料分类" />
      <VxeColumn field="materialCnName" title="材料名称" />
      <VxeColumn field="brandName" title="品牌" />
      <VxeColumn field="materialVendorName" title="材料供应商名称" />
      <VxeColumn field="materialVendorIdentification" title="材料供应商编码" />
      <VxeColumn field="materialColor" title="材料颜色" />
      <VxeColumn field="status" title="当前状态" />
      <VxeColumn title="修改状态">
        <template #default="{ row }">
          <ElSelect v-model="(row as TableRow).updateStatus" size="small">
            <ElOption
              v-for="item in getStatusList(row)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>
