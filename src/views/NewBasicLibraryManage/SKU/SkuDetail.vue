<script setup lang="ts">
import { useHook } from '@/views/NewBasicLibraryManage/components/Help/useHook'
import { layoutAttribute } from '@/components/PlmBase/type'
import { CuType } from '@/views/NewBasicLibraryManage/components/Help/Status'
import { getMultiProductBaseDetail } from '@/api/NewProductLibraryManage/detail'
import { queryFormDataValue } from '@/components/PlmBase/help'
const route = useRoute()
const currentRoute = useRouter()?.currentRoute
const otherMeta = currentRoute.value.meta?.otherMeta as string
const typeId = computed(() => JSON.parse(otherMeta).productId)
const skcId = computed(() => JSON.parse(otherMeta).skcId)
const skuId = computed(() => JSON.parse(otherMeta).skuId)
const skcPageLayoutCode = computed(() => JSON.parse(otherMeta).skcPageLayoutCode)
const skuPageLayoutCode = computed(() => JSON.parse(otherMeta).skuPageLayoutCode)
const { AllAttribute, pageLoading, formData, getLayout } = useHook({
  needProductAttribute: false,
  needSkcAttribute: true,
  needSkuAttribute: true
})
const pageLayout = ref<layoutAttribute>()
const skuPageLayout = ref<layoutAttribute>()
const activeName = ref('0')
onMounted(async () => {
  pageLayout.value = (await getLayout(skcPageLayoutCode.value, skcId.value))?.[0] || {}
  skuPageLayout.value = (await getLayout(skuPageLayoutCode.value, skuId.value))?.[0] || {}
  getskuData()
})
const getskuData = async () => {
  const [error, result] = await getMultiProductBaseDetail({
    id: route.query.id,
    typeId: skuId.value,
    type: CuType.SKU,
    pageLayoutCode: skuPageLayoutCode.value
  })
  const skcDetailId = result?.data?.skcId
  const [errorSkc, resultSkc] = await getMultiProductBaseDetail({
    id: skcDetailId,
    typeId: skcId.value,
    type: CuType.SKC,
    pageLayoutCode: skcPageLayoutCode.value
  })
  if (!error && result.data) {
    formData[skuId.value] = {
      ...formData[skuId.value],
      ...result.data
    }
  }
  if (!errorSkc) {
    formData[skcId.value] = {
      ...formData[skcId.value],
      ...resultSkc.data
    }
  }
  queryFormDataValue(AllAttribute[skcId.value], formData[skcId.value])
  queryFormDataValue(AllAttribute[skuId.value], formData[skuId.value])
}
</script>

<template>
  <ContentWrap v-loading="pageLoading">
    <el-tabs v-model="activeName">
      <el-tab-pane :label="pageLayout?.name" name="0">
        <Form-render v-model="formData[skcId]" :gridItems="pageLayout?.gridItems" />
      </el-tab-pane>
      <el-tab-pane :label="skuPageLayout?.name" name="1">
        <Form-render v-model="formData[skuId]" :gridItems="skuPageLayout?.gridItems" />
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<style scoped lang="less"></style>
