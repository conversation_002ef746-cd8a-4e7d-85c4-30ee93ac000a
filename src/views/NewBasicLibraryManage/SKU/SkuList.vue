<script setup lang="ts">
import Search from '@/views/NewBasicLibraryManage/components/Search.vue'
import TableList from '@/views/NewBasicLibraryManage/components/TableList.vue'
import StatusDialog from '@/views/NewBasicLibraryManage/Product/components/StatusDialog.vue'
import { CuType } from '@/views/NewBasicLibraryManage/components/Help/Status'
import { AdvancedCondition } from '@/components/PlmBase/AdvancedSearchForm'
import { Icon } from '@/components/Icon'
import { AttributeData } from '@/components/PlmBase/type'
import EditSkuDialog from '@/views/NewBasicLibraryManage/SKU/EditSkuDialog.vue'
import { updateSkuStatus } from '@/api/NewProductLibraryManage/detail'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { ElButton, ElDropdown } from 'element-plus'
import ImportDialog from '@/views/NewBasicLibraryManage/components/ImportDialog.vue'
const TableListRef = ref<InstanceType<typeof TableList>>()
defineOptions({
  name: 'SkuList'
})
const SearchRef = ref<InstanceType<typeof Search>>()
const statusModal = reactive({
  visible: false,
  selectedRows: []
})
const { handleExport: exportFn, loading } = useOmsExport()
const currentRoute = useRouter()?.currentRoute
const otherMeta = currentRoute.value.meta?.otherMeta as string
const typeId = computed(() => {
  return JSON.parse(otherMeta)?.skuId
})
const type = computed(() => {
  return JSON.parse(otherMeta)?.type
})
const handleSearch = (condition: AdvancedCondition[]) => {
  TableListRef.value?.refreshTable(condition)
}
const turnOpenClose = () => {
  const selectRows = TableListRef.value?.getCheckBoxRecords()
  if (selectRows && selectRows.length > 0) {
    statusModal.selectedRows = selectRows
    statusModal.visible = true
  }
}
const handleReset = () => {
  TableListRef.value?.refreshTable()
}
const SkuDialog = reactive({
  visible: false,
  currentRow: {}
})
const handleEditRow = (row: AttributeData) => {
  SkuDialog.visible = true
  SkuDialog.currentRow = row
}
const saveStatusFunc = async (tableData: AttributeData[]) => {
  const [error] = await updateSkuStatus({
    list: tableData
  })
  if (!error) {
    ElMessage.success('操作成功')
    statusModal.visible = false
    TableListRef.value?.refreshTable()
  }
}
const getParams = () => {
  const selected: AttributeData[] | undefined = TableListRef.value?.getCheckBoxRecords()

  let reqParam: string
  if (selected && selected?.length > 0) {
    const idList = selected.map((item) => item.id)
    reqParam = JSON.stringify({ idList })
  } else {
    reqParam = JSON.stringify(TableListRef.value?.getParams())
  }
  return reqParam
}
const handleExport = () => {
  exportFn({
    exportType: 'mini_product_skc_export',
    reqParam: getParams()
  })
}
const importDialogVisible = ref(false)
const handleOperate = (command: string) => {
  if (command === 'download') {
    importDialogVisible.value = true
  } else if (command === 'export') {
    exportFn({
      exportType: 'mini_product_sku_package_export',
      reqParam: getParams()
    })
  }
}
const handleQuery = () => {
  SearchRef.value?.AdvancedSearchFormRef?.handleSearch()
}
onActivated(() => {
  SearchRef.value?.AdvancedSearchFormRef?.handleSearch()
})
</script>

<template>
  <ContentWrap>
    <Search
      :typeId="typeId"
      :type="type"
      ref="SearchRef"
      @handle-search="handleSearch"
      @handle-reset="handleReset"
    />
    <div class="operation"> </div>
    <TableList ref="TableListRef" @edit-row="handleEditRow" :typeId="typeId" :type="type">
      <template #button>
        <ElButton v-hasPermi="['change:skuStatus']" type="primary" @click="turnOpenClose"
          ><Icon icon="ion:switch" /><span>生效/作废</span></ElButton
        >
        <ElDropdown class="mr-1 ml-1" @command="(command:string)=>handleOperate(command)">
          <ElButton type="primary" :loading="loading"
            ><Icon icon="ep:upload-filled" /><span>操作</span>
          </ElButton>
          <template #dropdown>
            <ElDropdownMenu>
              <div v-hasPermi="'product:package-import'">
                <ElDropdownItem command="download">
                  <span>导入包装信息</span>
                </ElDropdownItem>
              </div>
              <div v-hasPermi="'product:package-export'">
                <ElDropdownItem command="export">
                  <span>导出包装信息</span>
                </ElDropdownItem>
              </div>
            </ElDropdownMenu>
          </template>
        </ElDropdown>

        <ElButton
          v-hasPermi="['export:sku']"
          type="primary"
          :loading="loading"
          @click="handleExport"
          ><Icon icon="ep:upload-filled" /><span>导出</span></ElButton
        >
      </template>
    </TableList>
  </ContentWrap>
  <status-dialog
    :typeId="typeId"
    :type="CuType.SKU"
    @confirm="saveStatusFunc"
    v-model="statusModal.visible"
    :selected-rows="statusModal.selectedRows"
  />
  <EditSkuDialog
    :typeId="typeId"
    @refresh="handleReset"
    :type="type"
    v-model="SkuDialog.visible"
    :current-row="SkuDialog.currentRow"
  />
  <ImportDialog v-model="importDialogVisible" type="package" @refresh="handleQuery" />
</template>
<style scoped lang="less"></style>
