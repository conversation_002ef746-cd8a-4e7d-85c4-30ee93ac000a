<script setup lang="ts">
import Search from '@/views/NewBasicLibraryManage/components/Search.vue'
import TableList from '@/views/NewBasicLibraryManage/components/TableList.vue'
import StatusDialog from '@/views/NewBasicLibraryManage/Product/components/StatusDialog.vue'
import { CuType } from '@/views/NewBasicLibraryManage/components/Help/Status'
import { AdvancedCondition } from '@/components/PlmBase/AdvancedSearchForm'
import { Icon } from '@/components/Icon'
import EditSkcDialog from '@/views/NewBasicLibraryManage/SKC/EditSkcDialog.vue'
import { AttributeData } from '@/components/PlmBase/type'
import { updateSkcStatus } from '@/api/NewProductLibraryManage/detail'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
const currentRoute = useRouter()?.currentRoute
const otherMeta = currentRoute.value.meta?.otherMeta as string
const TableListRef = ref<InstanceType<typeof TableList>>()
defineOptions({
  name: 'SkcList'
})
const { handleExport: exportFn, loading } = useOmsExport()
const SearchRef = ref<InstanceType<typeof Search>>()
const statusModal = reactive({
  visible: false,
  selectedRows: []
})
const typeId = computed(() => {
  return JSON.parse(otherMeta)?.skcId
})
const type = computed(() => {
  return JSON.parse(otherMeta)?.type
})
const handleSearch = (condition: AdvancedCondition[]) => {
  TableListRef.value?.refreshTable(condition)
}
const turnOpenClose = () => {
  const selectRows = TableListRef.value?.getCheckBoxRecords()
  if (selectRows && selectRows.length > 0) {
    statusModal.selectedRows = selectRows
    statusModal.visible = true
  }
}
const handleReset = () => {
  TableListRef.value?.refreshTable()
}
const SkcDialog = reactive({
  visible: false,
  currentRow: {}
})
const handleEditRow = (row: AttributeData) => {
  SkcDialog.visible = true
  SkcDialog.currentRow = row
}
const saveStatusFunc = async (tableData: AttributeData[]) => {
  const [error] = await updateSkcStatus({
    list: tableData
  })
  if (!error) {
    ElMessage.success('操作成功')
    statusModal.visible = false
    TableListRef.value?.refreshTable()
  }
}
const handleExport = () => {
  const selected: AttributeData[] | undefined = TableListRef.value?.getCheckBoxRecords()

  let reqParam: string
  if (selected && selected?.length > 0) {
    const idList = selected.map((item) => item.id)
    reqParam = JSON.stringify({ idList })
  } else {
    reqParam = JSON.stringify(TableListRef.value?.getParams())
  }
  exportFn({
    exportType: 'mini_product_skc_export',
    reqParam
  })
}
onActivated(() => {
  SearchRef.value?.AdvancedSearchFormRef?.handleSearch()
})
</script>

<template>
  <ContentWrap>
    <Search
      :typeId="typeId"
      ref="SearchRef"
      :type="type"
      @handle-search="handleSearch"
      @handle-reset="handleReset"
    />
    <div class="operation"> </div>
    <TableList ref="TableListRef" @edit-row="handleEditRow" :typeId="typeId" :type="type">
      <template #button>
        <ElButton type="primary" v-hasPermi="['change:skcStatus']" @click="turnOpenClose"
          ><Icon icon="ion:switch" /><span>生效/作废</span></ElButton
        >
        <ElButton
          type="primary"
          v-hasPermi="['export:skc']"
          :loading="loading"
          @click="handleExport"
          ><Icon icon="ep:upload-filled" /><span>导出</span></ElButton
        >
      </template>
    </TableList>
    <status-dialog
      :typeId="typeId"
      @confirm="saveStatusFunc"
      :type="CuType.SKC"
      v-model="statusModal.visible"
      :selected-rows="statusModal.selectedRows"
    />
    <EditSkcDialog
      :typeId="typeId"
      @refresh="handleReset"
      :type="type"
      v-model="SkcDialog.visible"
      :current-row="SkcDialog.currentRow"
    />
  </ContentWrap>
</template>
<style scoped lang="less"></style>
