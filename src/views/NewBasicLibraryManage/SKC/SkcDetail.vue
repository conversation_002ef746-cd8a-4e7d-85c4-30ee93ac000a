<script setup lang="ts">
import { useHook } from '@/views/NewBasicLibraryManage/components/Help/useHook'
import { layoutAttribute } from '@/components/PlmBase/type'
import { skuMessageConfig } from '@/views/NewBasicLibraryManage/components/Help/Detail'
import {
  getMultiProductBaseDetail,
  skuDetailsBySkcCode
} from '@/api/NewProductLibraryManage/detail'
import { CuType } from '@/views/NewBasicLibraryManage/components/Help/Status'
import { queryFormDataValue } from '@/components/PlmBase/help'
const route = useRoute()
const id = ref<string>(route.query.id as string)
const currentRoute = useRouter()?.currentRoute
const otherMeta = currentRoute.value.meta?.otherMeta as string
const typeId = computed(() => JSON.parse(otherMeta).productId)
const skcId = computed(() => JSON.parse(otherMeta).skcId)
const skuId = computed(() => JSON.parse(otherMeta).skuId)
const type = computed(() => JSON.parse(otherMeta).type)
const skcPageLayoutCode = computed(() => JSON.parse(otherMeta).skcPageLayoutCode)
const imgPageLayoutCode = computed(() => JSON.parse(otherMeta).imgPageLayoutCode)
const prodcutPageLayoutCode = computed(() => JSON.parse(otherMeta).prodcutPageLayoutCode)
const skuPageLayoutCode = computed(() => JSON.parse(otherMeta).skuPageLayoutCode)
const { pageLoading, formData, AllAttribute, getLayout } = useHook({
  needProductAttribute: true,
  needSkcAttribute: true,
  needSkuAttribute: true
})
const pageLayout = ref<layoutAttribute>()
const skcPageLayout = ref<layoutAttribute>()
// const imgPageLayout = ref<layoutAttribute>()
const activeName = ref('0')
onMounted(async () => {
  pageLayout.value = (await getLayout(prodcutPageLayoutCode.value, typeId.value))?.[0] || {}
  skcPageLayout.value = (await getLayout(skcPageLayoutCode.value, skcId.value))?.[0] || {}
  // imgPageLayout.value = (await getLayout(imgPageLayoutCode.value, skcId.value))?.[0] || {}
  getSkcData()
})
const getSkcData = async () => {
  const [errorSkc, resultSkc] = await getMultiProductBaseDetail({
    id: route.query.id,
    typeId: skcId.value,
    type: type.value,
    pageLayoutCode: skcPageLayoutCode.value
  })
  const productId = resultSkc?.data.productId
  const [error, result] = await getMultiProductBaseDetail({
    id: productId,
    typeId: typeId.value,
    type: CuType.Prodcut,
    pageLayoutCode: prodcutPageLayoutCode.value
  })
  if (!errorSkc && resultSkc.data) {
    formData[skcId.value] = {
      ...formData[skcId.value],
      ...resultSkc.data
    }
  }
  queryFormDataValue(AllAttribute[skcId.value], formData[skcId.value])
  if (!error && result.data) {
    formData[typeId.value] = {
      ...formData[typeId.value],
      ...result.data
    }
  }
  queryFormDataValue(AllAttribute[typeId.value], formData[typeId.value])
  skuDetailsBySkcCode({ skcCode: route.query.code }).then((res) => {
    skuDataList.value = res.data
  })
  console.log(formData[typeId.value], 'formData[typeId.value]')
}
const skuDataList = ref([])
</script>

<template>
  <ContentWrap>
    <div v-loading="pageLoading">
      <el-tabs v-model="activeName">
        <el-tab-pane :label="pageLayout?.name" name="0">
          <Form-render v-model="formData[typeId]" :gridItems="pageLayout?.gridItems" />
        </el-tab-pane>
        <el-tab-pane :label="skcPageLayout?.name" name="1">
          <Form-render v-model="formData[skcId]" :gridItems="skcPageLayout?.gridItems" />
        </el-tab-pane>
        <el-tab-pane label="SKU信息" name="2">
          <vxeGrid :data="skuDataList" :columns="skuMessageConfig" />
        </el-tab-pane>
        <!--        <el-tab-pane :label="imgPageLayout?.name" name="3">-->
        <!--          <Form-render v-model="formData[skcId]" :gridItems="imgPageLayout?.gridItems" />-->
        <!--        </el-tab-pane>-->
      </el-tabs>
    </div>
  </ContentWrap>
</template>

<style scoped lang="less"></style>
