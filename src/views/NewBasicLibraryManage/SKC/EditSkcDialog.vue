<script setup lang="ts">
import { useHook } from '@/views/NewBasicLibraryManage/components/Help/useHook'
import FormRender from '@/components/PlmBase/component/FormRender.vue'
import { AttributeData, layoutAttribute } from '@/components/PlmBase/type'
import {
  getMultiProductBaseDetail,
  multiProductBaseUpdate
} from '@/api/NewProductLibraryManage/detail'
import { queryFormDataValue, saveFormDataValue } from '@/components/PlmBase/help'

defineOptions({
  name: 'EditSkuDialog'
})

const props = defineProps<{
  modelValue: boolean
  currentRow: AttributeData | null
  typeId: string
  type: string
  versionId?: number
  readonly?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const currentRoute = useRouter()?.currentRoute
const otherMeta = currentRoute.value.meta?.otherMeta as string
const skcPageLayoutCode = computed(() => JSON.parse(otherMeta).skcPageLayoutCode)
const handleClose = () => {
  visible.value = false
}
// 使用重构后的 useHook
const { AllAttribute, pageLoading, getLayout, formData, getAttributeList } = useHook(
  {
    needProductAttribute: false,
    needSkcAttribute: true,
    needSkuAttribute: false
  },
  {
    productId: '',
    skcId: props.typeId,
    skuId: ''
  }
)
// 版本预览模式下禁用编辑
const isReadonly = computed(() => props.readonly || !!props.versionId)

// 获取详细信息
const getProductMessage = async () => {
  // 如果有 versionId，说明是版本预览，直接使用传入的数据
  if (props.versionId && props.currentRow) {
    formData[props.typeId] = {
      ...formData[props.typeId],
      ...props.currentRow
    }
    queryFormDataValue(AllAttribute[props.typeId], formData[props.typeId])
    return
  }

  // 正常编辑模式，调用详情接口
  const [error, result] = await getMultiProductBaseDetail({
    id: props.currentRow?.id,
    typeId: props.typeId,
    type: props.type,
    pageLayoutCode: skcPageLayoutCode.value
  })
  if (!error && result.data) {
    formData[props.typeId] = {
      ...formData[props.typeId],
      ...result.data
    }
  }
  queryFormDataValue(AllAttribute[props.typeId], formData[props.typeId])
}
const pageLayout = ref<layoutAttribute[]>([])

// 监听弹窗显示状态，初始化数据
watch(
  () => props.modelValue,
  async (visible) => {
    if (visible && props.currentRow) {
      // 确保属性数据已加载
      if (!AllAttribute[props.typeId]) {
        await getAttributeList(props.typeId)
      }

      // 获取页面布局
      pageLayout.value = (await getLayout(skcPageLayoutCode.value, props.typeId)) || []

      // 获取详情数据
      await getProductMessage()
    }
  },
  { immediate: true }
)
const loading = ref(false)
//保存
const handleSubmit = async () => {
  loading.value = true
  // 5. 处理表单数据
  saveFormDataValue(AllAttribute[props.typeId], formData[props.typeId])
  const [error] = await multiProductBaseUpdate({
    id: props.currentRow?.id,
    typeId: props.typeId,
    type: props.type,
    pageLayoutCode: skcPageLayoutCode.value,
    attributes: formData[props.typeId]
  })
  if (!error) {
    emit('refresh')
    loading.value = false
    handleClose()
  }
}
</script>
<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :title="isReadonly ? 'SKC版本详情预览' : '编辑SKC'"
    width="700"
  >
    <div style="height: 500px" v-loading="pageLoading">
      <form-render
        ref="formRenderRef"
        v-model="formData[typeId]"
        :gridItems="pageLayout?.[0]?.gridItems"
        :readonly="isReadonly"
      />
    </div>
    <template #footer v-if="!isReadonly">
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit" :loading="loading">确定</ElButton>
    </template>
    <template #footer v-else>
      <ElButton @click="handleClose">关闭</ElButton>
    </template>
  </Dialog>
</template>
