<script lang="ts" setup>
import { BaseClamp } from '@/components/BaseClamp'
import type { Measurable } from 'element-plus'
import { ElTooltip } from 'element-plus'
import { AttributeData } from '@/components/PlmBase/type'
import { CuType } from '@/views/NewBasicLibraryManage/components/Help/Status'

defineOptions({
  name: 'CardView'
})

defineProps<{
  tableData: AttributeData[]
  loading: boolean
  type: string
  maxHeight?: number
}>()

const emit = defineEmits<{
  (e: 'change-status', val: AttributeData): void
  (e: 'edit-row', val: AttributeData): void
  (e: 'copy-row', val: AttributeData): void
  (e: 'view-product', val: AttributeData): void
  (e: 'checkbox-change'): void
  (e: 'version-log', val: AttributeData): void
}>()

const getColorList = (row: AttributeData) => {
  return row.colorUrlList?.map((e) => e?.signatureUrl || '').filter((e) => e) || []
}

const contentRef = ref<HTMLElement | null>(null)
const content = ref<string>('')
const tooltipRef = ref<InstanceType<typeof ElTooltip>>()

const handleMouseover = (e: { clamped: boolean; text: string; currentTarget: HTMLElement }) => {
  if (!e.clamped) {
    return
  }
  contentRef.value = e.currentTarget
  content.value = e.text
}

const checkedList = ref<AttributeData[]>([])
const toggleCheck = (item: AttributeData) => {
  const index = checkedList.value.findIndex((e) => e === item)
  if (index !== -1) {
    checkedList.value = checkedList.value.splice(index, 1)
  } else {
    checkedList.value.push(item)
  }
  emit('checkbox-change')
}

const getCheckboxRecords = () => {
  return checkedList.value
}

defineExpose({
  getCheckboxRecords
})
</script>

<template>
  <ElScrollbar>
    <div
      v-loading="loading"
      :style="{ maxHeight: maxHeight + 'px' }"
      class="grid justify-center min-h-[140px] grid-gap-[10px] grid-cols-[repeat(auto-fit,minmax(300px,1fr))]"
    >
      <div v-for="item in tableData" :key="item.id!" class="p-2">
        <ElCard
          :body-style="{ padding: '0px', width: '100%', height: '100%' }"
          class="card-container"
          v-if="type == CuType.Prodcut"
          shadow="always"
          @click="emit('view-product', item)"
        >
          <div class="card-body">
            <div class="card-content">
              <div class="card-img-container">
                <ElImage
                  :src="item?.productDesignUrlItemName"
                  class="card-img"
                  fit="cover"
                  hide-on-click-modal
                  loading="lazy"
                />
                <ElCheckbox class="!absolute !h-6 right-2 top-0" @click.stop="toggleCheck(item)" />
              </div>
              <div class="card-button-container">
                <ElButton
                  v-hasPermi="['edit', 'edit:newSkc']"
                  class="card-button"
                  size="small"
                  text
                  type="primary"
                  @click.stop="emit('edit-row', item)"
                >
                  <span class="card-button-content">修改</span>
                  <span class="card-button-content">产品</span>
                </ElButton>
                <ElButton
                  class="card-button"
                  size="small"
                  style="margin-left: 10px"
                  v-hasPermi="['version']"
                  text
                  type="primary"
                  @click.stop="emit('version-log', item)"
                >
                  <span class="card-button-content">版本</span>
                  <span class="card-button-content">记录</span>
                </ElButton>

                <ElButton
                  class="card-button"
                  size="small"
                  v-hasPermi="['version']"
                  text
                  v-if="type == CuType.Prodcut"
                  type="primary"
                  @click.stop="emit('copy-row', item)"
                >
                  <span class="card-button-content">复制</span>
                  <span class="card-button-content">产品</span>
                </ElButton>
              </div>
            </div>
            <div class="card-content">
              <!--              <div class="card-info-container">-->
              <!--&lt;!&ndash;                <div class="flex flex-nowrap items-center w-full h-full">&ndash;&gt;-->
              <!--&lt;!&ndash;                  <ElScrollbar>&ndash;&gt;-->
              <!--&lt;!&ndash;                    <ElImage&ndash;&gt;-->
              <!--&lt;!&ndash;                      v-for="img in getColorList(item)"&ndash;&gt;-->
              <!--&lt;!&ndash;                      :key="img"&ndash;&gt;-->
              <!--&lt;!&ndash;                      :preview-src-list="getColorList(item)"&ndash;&gt;-->
              <!--&lt;!&ndash;                      :src="img"&ndash;&gt;-->
              <!--&lt;!&ndash;                      hide-on-click-modal&ndash;&gt;-->
              <!--&lt;!&ndash;                      fit="contain"&ndash;&gt;-->
              <!--&lt;!&ndash;                      loading="lazy"&ndash;&gt;-->
              <!--&lt;!&ndash;                      style="width: 30px; height: 30px"&ndash;&gt;-->
              <!--&lt;!&ndash;                    />&ndash;&gt;-->
              <!--&lt;!&ndash;                  </ElScrollbar>&ndash;&gt;-->
              <!--&lt;!&ndash;                </div>&ndash;&gt;-->
              <!--              </div>-->
              <div class="card-info-container">
                <BaseClamp
                  :max-lines="1"
                  :text-content="item.code"
                  autoresize
                  @mouseover="handleMouseover"
                >
                  {{ item.code }}
                </BaseClamp>
              </div>
              <div class="card-info-container !flex-row flex-wrap">
                <div v-if="item.yearItemName">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.yearItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.yearItemName }}；
                  </BaseClamp>
                </div>
                <div v-if="item.applicableSeasonItemName">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.applicableSeasonItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.applicableSeasonItemName }}；
                  </BaseClamp>
                </div>
              </div>
              <div class="card-info-container !border-bottom-0 !flex-row">
                <div v-if="item.modifyByName">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.modifyByName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.modifyByName }}
                  </BaseClamp>
                </div>
              </div>
              <div class="card-info-container !border-bottom-0 !flex-row">
                <div class="w-full">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.referenceBrandItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.referenceBrandItemName }}
                  </BaseClamp>
                </div>
              </div>
              <div class="card-info-container">
                <div class="flex text-left h-1/2 w-full">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="`状态: ${item.dataStatusItemName}`"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    状态: {{ item.dataStatusItemName }}
                  </BaseClamp>
                </div>
              </div>
            </div>
          </div>
        </ElCard>

        <ElCard
          :body-style="{ padding: '0px', width: '100%', height: '100%' }"
          class="card-container"
          v-else-if="type == CuType.SKC"
          shadow="always"
          @click="emit('view-product', item)"
        >
          <div class="card-body">
            <div class="card-content">
              <div class="card-img-container">
                <ElImage
                  :src="item?.productDesignUrlItemName || item?.sampleImagesItemName || ''"
                  class="card-img"
                  fit="cover"
                  hide-on-click-modal
                  loading="lazy"
                />
                <ElCheckbox class="!absolute !h-6 right-2 top-0" @click.stop="toggleCheck(item)" />
              </div>
              <div class="card-button-container">
                <ElButton
                  v-hasPermi="['edit', 'edit:newSkc']"
                  class="card-button"
                  size="small"
                  text
                  type="primary"
                  @click.stop="emit('edit-row', item)"
                >
                  <span class="card-button-content">修改</span>
                  <span class="card-button-content">SKC</span>
                </ElButton>
                <ElButton
                  class="card-button"
                  size="small"
                  style="margin-left: 10px"
                  v-hasPermi="['version']"
                  text
                  type="primary"
                  @click.stop="emit('version-log', item)"
                >
                  <span class="card-button-content">版本</span>
                  <span class="card-button-content">记录</span>
                </ElButton>
              </div>
            </div>
            <div class="card-content">
              <div class="card-info-container">
                <div class="flex flex-nowrap items-center w-full h-full">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.colorItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.colorItemName }}
                  </BaseClamp>
                </div>
              </div>
              <div class="card-info-container">
                <BaseClamp
                  :max-lines="1"
                  :text-content="item.code"
                  autoresize
                  @mouseover="handleMouseover"
                >
                  {{ item.code }}
                </BaseClamp>
              </div>
              <div class="card-info-container !flex-row flex-wrap">
                <div class="w-1/2">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.mainFabricItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.mainFabricItemName }}
                  </BaseClamp>
                </div>
                <div class="w-1/2">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.createByName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.createByName }}
                  </BaseClamp>
                </div>
              </div>
              <div class="card-info-container !border-bottom-0 !flex-row">
                <div class="w-full">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.referenceBrandItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.referenceBrandItemName }}
                  </BaseClamp>
                </div>
              </div>
              <div
                class="card-info-container !border-bottom-0 border-top-1 border-left-1 border-solid mt-[-2px] ml-[-1px]"
              >
                <div class="flex text-left h-1/2 w-full">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="`状态: ${item.dataStatusItemName}`"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    状态: {{ item.dataStatusItemName }}
                  </BaseClamp>
                </div>
              </div>
            </div>
          </div>
        </ElCard>
        <ElCard
          :body-style="{ padding: '0px', width: '100%', height: '100%' }"
          class="card-container"
          v-else-if="type == CuType.SKU"
          shadow="always"
          @click="emit('view-product', item)"
        >
          <div class="card-body">
            <div class="card-content">
              <div class="card-img-container">
                <ElImage
                  :src="item?.productDesignUrlItemName || item?.sampleImagesItemName || ''"
                  class="card-img"
                  fit="cover"
                  hide-on-click-modal
                  loading="lazy"
                />
                <ElCheckbox class="!absolute !h-6 right-2 top-0" @click.stop="toggleCheck(item)" />
              </div>
              <div class="card-button-container">
                <ElButton
                  v-hasPermi="['edit', 'edit:newSkc']"
                  class="card-button"
                  size="small"
                  text
                  type="primary"
                  @click.stop="emit('edit-row', item)"
                >
                  <span class="card-button-content">修改</span>
                  <span class="card-button-content"></span>
                </ElButton>
                <ElButton
                  class="card-button"
                  size="small"
                  style="margin-left: 10px"
                  v-hasPermi="['version']"
                  text
                  type="primary"
                  @click.stop="emit('version-log', item)"
                >
                  <span class="card-button-content">版本记录</span>
                </ElButton>

                <ElButton
                  class="card-button"
                  size="small"
                  v-hasPermi="['version']"
                  text
                  v-if="type === CuType.Prodcut"
                  type="primary"
                  @click.stop="emit('copy-row', item)"
                >
                  <span class="card-button-content">复制</span>
                </ElButton>
              </div>
            </div>
            <div class="card-content">
              <div class="card-info-container">
                <div class="flex flex-nowrap items-center w-full h-full">
                  <ElScrollbar>
                    <ElImage
                      v-for="img in getColorList(item)"
                      :key="img"
                      :preview-src-list="getColorList(item)"
                      :src="img"
                      hide-on-click-modal
                      fit="contain"
                      loading="lazy"
                      style="width: 30px; height: 30px"
                    />
                  </ElScrollbar>
                </div>
              </div>
              <div class="card-info-container">
                <BaseClamp
                  :max-lines="1"
                  :text-content="item.code"
                  autoresize
                  @mouseover="handleMouseover"
                >
                  {{ item.code }}
                </BaseClamp>
              </div>
              <div class="card-info-container !flex-row flex-wrap">
                <div class="w-1/2">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.createByName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.createByName }}
                  </BaseClamp>
                </div>
                <div class="w-1/2">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.assignedFactoryItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.assignedFactoryItemName }}
                  </BaseClamp>
                </div>
              </div>
              <div class="card-info-container !border-bottom-0 !flex-row">
                <div class="w-full">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="item.referenceBrandItemName"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    {{ item.referenceBrandItemName }}
                  </BaseClamp>
                </div>
              </div>
              <div
                class="card-info-container !border-bottom-0 border-top-1 border-left-1 border-solid mt-[-2px] ml-[-1px]"
              >
                <div class="flex items-center h-1/2 w-full">
                  <div class="w-1/2">
                    <BaseClamp
                      :max-lines="1"
                      :text-content="item.selectedSizeItemName"
                      autoresize
                      @mouseover="handleMouseover"
                    >
                      尺码段：{{ item.selectedSizeItemName }}
                    </BaseClamp>
                  </div>
                </div>
                <div class="flex text-left h-1/2 w-full">
                  <BaseClamp
                    :max-lines="1"
                    :text-content="`状态: ${item.dataStatusItemName}`"
                    autoresize
                    @mouseover="handleMouseover"
                  >
                    状态: {{ item.dataStatusItemName }}
                  </BaseClamp>
                </div>
              </div>
            </div>
          </div>
        </ElCard>
      </div>
      <ElTooltip
        ref="tooltipRef"
        :content="content"
        :popper-options="{
          modifiers: [
            {
              name: 'computeStyles',
              options: {
                adaptive: false,
                enabled: false
              }
            }
          ]
        }"
        :virtual-ref="contentRef as Measurable"
        popper-class="singleton-tooltip"
        virtual-triggering
      />
    </div>
  </ElScrollbar>
</template>

<style lang="less" scoped>
.card-container {
  height: 200px;
  max-width: 330px;

  &:hover {
    cursor: pointer;
  }

  .card-body {
    display: flex;
    height: 100%;
    justify-content: space-between;

    > .card-content {
      width: 50%;
      font-size: 12px;

      > .card-img-container {
        position: relative;
        width: 100%;
        height: 80%;
        overflow: hidden;

        > .card-img {
          width: 100%;
          height: 100%;
        }

        > .card-status {
          position: absolute;
          top: 0;
          left: 0;
          border-radius: 0 var(--el-card-border-radius) var(--el-card-border-radius)
            var(--el-card-border-radius);
        }
      }

      > .card-button-container {
        height: 20%;

        > .card-button {
          width: 30%;
          height: 100%;

          > :deep(span) {
            flex-direction: column;
          }

          > .card-button-content {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
        }

        > .card-button + .card-button {
          margin-left: 0;
        }
      }

      > .card-info-container {
        display: flex;
        width: 100%;
        height: 20%;
        padding: 0 8px;
        text-align: center;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        :deep(.el-image + .el-image) {
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
