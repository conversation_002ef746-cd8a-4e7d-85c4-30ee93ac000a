<script lang="ts" setup>
import { ElMessage, ElImage } from 'element-plus'
import { utils, writeFile } from 'xlsx'
import { Icon } from '@/components/Icon'
import {
  importMiniProductStyle,
  importPackage,
  importPackageExcel
} from '@/api/NewProductLibraryManage/detail'
import { SuccessEnum } from '@/views/NewBasicLibraryManage/components/Help/List'
const ImportTypeEnum = {
  PACKAGE: 'package',
  PRODUCT: 'product'
}
defineOptions({
  name: 'ImportDialog'
})
const props = defineProps<{
  modelValue: boolean
  type: string
  typeCategoryId?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const isImportPackage = computed(() => {
  return props.type === ImportTypeEnum.PACKAGE
})

// 动态表格列配置
const dynamicColumns = computed(() => {
  if (props.type === ImportTypeEnum.PACKAGE) {
    return [] // PACKAGE类型使用固定列
  }

  const { fieldNameList, fieldValueList } = dynamicHeaders.value
  if (!fieldNameList.length || !fieldValueList.length) {
    return []
  }

  return fieldNameList.map((name, index) => ({
    field: fieldValueList[index],
    title: name,
    width: name.length > 10 ? 150 : 120,
    minWidth: 100
  }))
})

// 计算显示的数据统计
const displayStats = computed(() => {
  if (props.type === ImportTypeEnum.PACKAGE) {
    return {
      successCount: successNum.value,
      errorCount: errorNum.value,
      totalCount: tableData.value.length
    }
  } else {
    return {
      successCount: tableData.value.length,
      errorCount: 0,
      totalCount: tableData.value.length
    }
  }
})
const configMap = reactive({
  [ImportTypeEnum.PACKAGE]: {
    title: '批量小商品包装信息',
    importApi: importPackage,
    listApi: importPackageExcel,
    templateUrl:
      'https://mmt-sz.oss-cn-shenzhen.aliyuncs.com/c0c96721b4ed4848b4bac5e895e8c78b/964c994591124f0d882b60ceec9312bd.xlsx',
    header: {
      errorMsg: '错误信息',
      skcCode: 'SKC编码',
      productCode: '产品编码',
      brand: '品牌',
      productCategory: '商品类目',
      color: '产品配色',
      mainFabric: '主要面料',
      selectedSize: '选中尺码',
      outerBoxLong: '外箱长(cm)',
      outerBoxWidth: '鞋盒宽(cm)',
      outerBoxHeight: '外箱高(cm)',
      outerBoxRoughWeight: '外箱毛重(kg)',
      innerBoxLong: '内盒长(cm)',
      innerBoxWidth: '内盒宽(cm)',
      innerBoxHeight: '内盒高(cm)',
      innerBoxRoughWeight: '内盒毛重(kg)',
      boxNumber: '装箱数',
      dataStatus: '状态'
    }
  },
  [ImportTypeEnum.PRODUCT]: {
    title: '批量导入产品',
    importApi: importMiniProductStyle,
    listApi: importMiniProductStyle,
    templateUrl:
      'https://mmt-sz.oss-cn-shenzhen.aliyuncs.com/c0c96721b4ed4848b4bac5e895e8c78b/964c994591124f0d88'
  }
})

const active = ref(1)
const currentTypeConfig = computed(() => {
  return configMap[props.type]
})
const importTemplateUrl = computed(() => {
  return currentTypeConfig.value.templateUrl
})
const fileList = ref<BaseFileDTO[]>([])
const queryLoading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const percentage = ref(0)
const isSuccess = ref(false)
const colors = [
  { color: '#f56c6c', percentage: 40 },
  { color: '#e6a23c', percentage: 80 },
  { color: '#5cb87a', percentage: 100 }
]

const successNum = ref(0)
const errorNum = ref(0)

// 动态header数据
const dynamicHeaders = ref({
  fieldNameList: [],
  fieldValueList: [],
  displayJsonList: [],
  saveJsonList: []
})
const handleUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请上传文件')
    return
  }
  queryLoading.value = true
  let error, result
  if (props.type === ImportTypeEnum.PACKAGE) {
    ;[error, result] = await currentTypeConfig.value.listApi(fileList.value[0])
  } else {
    ;[error, result] = await currentTypeConfig.value.listApi({
      signatureUrl: fileList.value[0].signatureUrl,
      typeCategoryId: props.typeCategoryId
    })
  }
  queryLoading.value = false
  if (!error && result?.data) {
    // 处理PACKAGE类型的数据
    if (props.type === ImportTypeEnum.PACKAGE) {
      tableData.value = result.data
      successNum.value = (tableData.value as { success: SuccessEnum }[]).filter(
        (e) => e.success === SuccessEnum.SUCCESS
      ).length
      errorNum.value = (tableData.value as { success: SuccessEnum }[]).filter(
        (e) => e.success === SuccessEnum.ERROR
      ).length
    } else {
      // 处理其他类型的数据，包含动态header信息
      const { displayJsonList, fieldNameList, fieldValueList, saveJsonList } = result.data

      console.log('动态导入数据结构:', {
        displayJsonList,
        fieldNameList,
        fieldValueList,
        saveJsonList
      })

      // 保存动态header数据
      dynamicHeaders.value = {
        fieldNameList: fieldNameList || [],
        fieldValueList: fieldValueList || [],
        displayJsonList: displayJsonList || [],
        saveJsonList: saveJsonList || []
      }

      // 设置表格数据为displayJsonList
      tableData.value = displayJsonList || []
      successNum.value = tableData.value.length
      errorNum.value = 0
    }
    active.value = 2
  }
}
const handleDownloadError = () => {
  let data = []

  if (props.type === ImportTypeEnum.PACKAGE) {
    // PACKAGE类型使用固定header
    const header = currentTypeConfig.value.header
    data = tableData.value.map((e) => {
      const obj = {}
      for (const key in e) {
        if (header[key]) {
          obj[header[key]] = e[key]
        }
      }
      return obj
    })
  } else {
    // 其他类型使用动态header
    const { fieldNameList, fieldValueList } = dynamicHeaders.value
    data = tableData.value.map((row) => {
      const obj = {}
      fieldValueList.forEach((field, index) => {
        const headerName = fieldNameList[index]
        let value = row[field]

        // 特殊处理图片字段
        if (field === 'productDesignUrl' && value && typeof value === 'object') {
          value = value.fileName || value.signatureUrl || JSON.stringify(value)
        }

        obj[headerName] = value
      })
      return obj
    })
  }

  const workBook = utils.book_new()
  const workSheet = utils.json_to_sheet(data)
  utils.book_append_sheet(workBook, workSheet)
  writeFile(workBook, `${currentTypeConfig.value.title}错误信息.xlsx`, {
    bookType: 'xlsx'
  })
}

const handleSubmit = async () => {
  if (displayStats.value.errorCount >= 1) {
    ElMessage.warning('请先处理错误数据')
    return
  }
  active.value = 3
  percentage.value = 0
  submitLoading.value = true
  const timer = setInterval(() => {
    percentage.value = (percentage.value % 100) + 10
    if (percentage.value >= 100) {
      percentage.value = 99
    }
  }, 500)

  // 准备提交数据
  let submitData
  if (props.type === ImportTypeEnum.PACKAGE) {
    // PACKAGE类型直接使用tableData
    submitData = { reqList: tableData.value }
  } else {
    // 其他类型使用saveJsonList数据
    submitData = { reqList: dynamicHeaders.value.saveJsonList }
  }

  const [error, result] = await currentTypeConfig.value.importApi(submitData)
  clearInterval(timer)
  percentage.value = 100
  submitLoading.value = false
  if (!error) {
    isSuccess.value = true
    ElMessage.success(result?.msg || '导入成功')
    emit('refresh')
  } else {
    isSuccess.value = false
  }
}

const handleClose = () => {
  active.value = 1
  fileList.value = []
  tableData.value = []
  percentage.value = successNum.value = errorNum.value = 0
  isSuccess.value = visible.value = false

  // 重置动态header数据
  dynamicHeaders.value = {
    fieldNameList: [],
    fieldValueList: [],
    displayJsonList: [],
    saveJsonList: []
  }
}
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :title="currentTypeConfig.title"
    top="5vh"
    width="1000px"
  >
    <ElSteps :active="active" align-center class="mb-2">
      <ElStep title="上传文件" />
      <ElStep title="数据预览" />
      <ElStep title="导入数据" />
    </ElSteps>
    <template v-if="active === 1">
      <div class="flex h-28 border">
        <div class="w-28 bg-gray-200/50 flex items-center justify-center">
          <Icon :size="48" icon="ep:edit" />
        </div>
        <div class="flex flex-1 flex-col justify-center p-2">
          <div class="font-bold py-1 text-base"> 填写导入数据信息 </div>
          <div class="text-gray-500/70 text-sm py-1">
            请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除
          </div>
          <div v-if="importTemplateUrl" class="py-1">
            <ElLink :href="importTemplateUrl" :underline="false" target="_blank" type="primary">
              下载模板
            </ElLink>
          </div>
        </div>
      </div>
      <div class="flex h-28 border mt-4">
        <div class="w-28 bg-gray-200/50 flex items-center justify-center">
          <Icon :size="48" icon="ep:upload" />
        </div>
        <div class="flex flex-1 flex-col justify-center p-2">
          <div class="font-bold py-1 text-base"> 上传填好的信息表 </div>
          <div class="text-gray-500/70 text-sm py-1">
            文件后缀名必须为xls或xlsx(即Excel格式),文件大小不不得大于10M,最多支持导入3000条数据
          </div>
          <div class="py-1">
            <OssUpload
              v-model="fileList"
              :limit="1"
              :size-limit="1024 * 1024 * 10"
              accept=".xls,.xlsx"
              drag
              list-type="text"
              @error="submitLoading = false"
              @progress="submitLoading = true"
              @success="submitLoading = false"
            >
              <template #trigger>
                <ElLink :underline="false" type="primary"> 上传文件 </ElLink>
              </template>
            </OssUpload>
          </div>
        </div>
      </div>
      <div
        class="flex h-28 mt-4 bg-$el-color-warning-light-9 border-1 border-$el-color-warning-light-5 rounded-md"
      >
        <div class="w-28 flex items-center justify-center">
          <Icon :size="48" color="var(--el-color-warning)" icon="ep:warning" />
        </div>
        <div class="flex flex-1 flex-col justify-center p-2">
          <div class="font-bold py-1 text-base"> 特别提示 </div>
          <div class="text-gray-500/70 text-sm py-1">
            导入过程中如发现个别数据校验不通过，则全量回滚修正后再重新操作导入
          </div>
        </div>
      </div>
    </template>
    <template v-if="active === 2">
      <div class="flex h-28 border">
        <div class="w-28 bg-gray-200/50 flex items-center justify-center">
          <Icon :size="48" icon="ep:circle-close" />
        </div>
        <div class="flex flex-1 flex-col justify-center p-2">
          <div class="font-bold py-1">
            正常数量条数:
            <span class="text-$el-color-success">
              {{ displayStats.successCount }}
            </span>
          </div>
          <div class="font-bold py-1">
            异常数量条数:
            <span class="text-$el-color-danger">
              {{ displayStats.errorCount }}
            </span>
            <span
              v-if="displayStats.errorCount > 0"
              class="ml-4 font-normal text-blue-500 cursor-pointer"
              @click="handleDownloadError"
            >
              下载异常数据详情提示
            </span>
          </div>
          <div class="font-bold py-1 text-gray-600"> 总计条数: {{ displayStats.totalCount }} </div>
        </div>
      </div>
      <!-- PACKAGE类型：使用固定列 -->
      <VxeTable
        v-if="isImportPackage"
        :data="tableData"
        :loading="queryLoading"
        :max-height="500"
        :scroll-x="{
          enabled: false
        }"
        class="mt-4"
      >
        <VxeColumn class-name="text-red-500" field="errorMsg" min-width="100" title="错误信息" />
        <VxeColumn field="skcCode" title="SKC编码" width="100" />
        <VxeColumn field="productCode" title="产品编码" width="100" />
        <VxeColumn field="brand" title="品牌" width="100" />
        <VxeColumn field="productCategory" title="商品类目" width="100" />
        <VxeColumn field="color" title="产品配色" width="100" />
        <VxeColumn field="mainFabric" title="主要面料" width="100" />
        <VxeColumn field="selectedSize" title="外箱长(cm)" width="100" />
        <VxeColumn field="outerBoxWidth" title="鞋盒宽(cm)" width="100" />
        <VxeColumn field="outerBoxHeight" title="外箱高(cm)" width="100" />
        <VxeColumn field="outerBoxRoughWeight" title="外箱毛重(kg)" width="100" />
        <VxeColumn field="innerBoxLong" title="内盒长(cm)" width="100" />
        <VxeColumn field="innerBoxWidth" title="内盒宽(cm)" width="100" />
        <VxeColumn field="innerBoxHeight" title="内盒高(cm)" width="100" />
        <VxeColumn field="innerBoxRoughWeight" title="内盒毛重(kg)" width="100" />
        <VxeColumn field="boxNumber" title="装箱数" width="100" />
        <VxeColumn field="dataStatus" title="状态" width="100" />
      </VxeTable>

      <!-- 其他类型：使用动态列 -->
      <VxeTable
        v-else
        :data="tableData"
        :loading="queryLoading"
        :max-height="500"
        :scroll-x="{
          enabled: true
        }"
        class="mt-4"
      >
        <VxeColumn
          v-for="column in dynamicColumns"
          :key="column.field"
          :field="column.field"
          :title="column.title"
          :width="column.width"
          :min-width="column.minWidth"
        >
          <template #default="{ row }">
            <div v-if="column.field === 'productDesignUrl' && row[column.field]">
              <!-- 图片类型字段的特殊处理 -->
              <ElImage
                v-if="row[column.field].signatureUrl"
                :src="row[column.field].signatureUrl"
                :preview-src-list="[row[column.field].signatureUrl]"
                fit="cover"
                style="width: 50px; height: 50px"
                preview-teleported
              />
              <span v-else-if="row[column.field].fileName">
                {{ row[column.field].fileName }}
              </span>
              <span v-else>{{ row[column.field] }}</span>
            </div>
            <div v-else>
              {{ row[column.field] }}
            </div>
          </template>
        </VxeColumn>
      </VxeTable>
    </template>
    <template v-if="active === 3">
      <div v-if="submitLoading" class="h-96 flex flex-col items-center justify-center">
        <el-progress :color="colors" :percentage="percentage" :stroke-width="16" class="w-1/2" />
        <span class="mt-8">正在导入数据</span>
      </div>
      <el-result
        v-else-if="isSuccess"
        :sub-title="`您已成功导入${tableData.length}条数据`"
        icon="success"
        title="批量导入完成"
      />
      <el-result v-else icon="error" sub-title="请检查数据后重新导入" title="批量导入失败" />
    </template>
    <template #footer>
      <ElButton v-if="active === 1" @click="handleClose">取消</ElButton>
      <ElButton
        v-if="active === 1"
        :loading="submitLoading || queryLoading"
        type="primary"
        @click="handleUpload"
      >
        下一步
      </ElButton>
      <ElButton v-if="active === 2" :loading="submitLoading" @click="active = 1">上一步</ElButton>
      <ElButton v-if="active === 2" :loading="submitLoading" type="primary" @click="handleSubmit">
        提交
      </ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
