const SKCConfig = {
  status: [
    { title: 'SKC编号', field: 'code' },
    { title: '产品编号', field: 'productCode' },
    // { title: '品牌', field: 'brandItemName' },
    { title: '选中尺码', field: 'selectedSize' },
    { title: '创建者', field: 'createByName' },
    { title: 'SKC下单状态', field: 'orderStatus' },
    { title: '当前状态', field: 'dataStatus' },
    { title: '修改状态', field: 'dataStatus', slots: { default: 'edit' } },
    { title: '变更原因', field: 'invalidReason', slots: { default: 'invalidReason' } }
  ]
}
const PRODUCTConfig = {
  status: [
    { title: '产品编码', field: 'code' },
    // { title: '品牌', field: 'referenceBrand' },
    { title: '年份', field: 'year' },
    { title: '创建者', field: 'createByName' },
    { title: '创建时间', field: 'createTime' },
    { title: '下单状态', field: 'orderStatus' },
    { title: '当前状态', field: 'dataStatus' },
    { title: '修改状态', field: 'dataStatus', slots: { default: 'edit' } },
    { title: '变更原因', field: 'invalidReason', slots: { default: 'invalidReason' } }
  ]
}
const SKUConfig = {
  status: [
    { title: 'SKU编号', field: 'code' },
    { title: '产品编号', field: 'productCode' },
    // { title: '品牌', field: 'brandItemName' },
    { title: '选中尺码', field: 'selectedSize' },
    { title: '创建者', field: 'createByName' },
    { title: 'SKU下单状态', field: 'orderStatus' },
    { title: '当前状态', field: 'dataStatus' },
    { title: '修改状态', field: 'dataStatus', slots: { default: 'edit' } },
    { title: '变更原因', field: 'invalidReason', slots: { default: 'invalidReason' } }
  ]
}
export enum CuType {
  SKC = 'SKC',
  SKU = 'SKU',
  Prodcut = 'SPU'
}
export const statusConfig = {
  [CuType.SKC]: SKCConfig,
  [CuType.SKU]: SKUConfig,
  [CuType.Prodcut]: PRODUCTConfig
}
