// 小商品对应的相关内容字段
import { VxeTableDefines } from 'vxe-table'
import { AttributeData } from '@/components/PlmBase/type'
import router from '@/router'
import { CuType } from '@/views/NewBasicLibraryManage/components/Help/Status'
export interface Column<D = any> extends VxeTableDefines.ColumnOptions<D> {
  editable?: boolean
}
export const defaultColumn = {
  [CuType.Prodcut]: [
    {
      type: 'checkbox',
      fixed: 'left',
      width: 40
    },
    { type: 'seq', fixed: 'left', width: 60, title: '序号' },
    {
      title: '编码',
      field: 'code',
      minWidth: 150,
      fixed: 'left',
      cellRender: {
        name: 'Link',
        props: {
          clickFn: ({ row }: { row: AttributeData }) => {
            router.push({
              name: 'NewProductDetail',
              query: {
                typeId: row.typeId,
                code: row.code,
                id: row.id,
                type: row.type
              }
            })
          }
        }
      }
    }
  ],
  [CuType.SKC]: [
    {
      type: 'checkbox',
      fixed: 'left',
      width: 40
    },
    { type: 'seq', fixed: 'left', width: 60, title: '序号' },
    {
      title: '编码',
      field: 'code',
      minWidth: 150,
      fixed: 'left',
      cellRender: {
        name: 'Link',
        props: {
          clickFn: ({ row }: { row: AttributeData }) => {
            router.push({
              name: 'skcDetail',
              query: {
                typeId: row.typeId,
                code: row.code,
                id: row.id,
                type: row.type
              }
            })
          }
        }
      }
    }
  ],
  [CuType.SKU]: [
    {
      type: 'checkbox',
      fixed: 'left',
      width: 40
    },
    { type: 'seq', fixed: 'left', width: 60, title: '序号' },
    {
      title: '编码',
      field: 'code',
      minWidth: 150,
      fixed: 'left',
      cellRender: {
        name: 'Link',
        props: {
          clickFn: ({ row }: { row: AttributeData }) => {
            router.push({
              name: 'skuDetail',
              query: {
                typeId: row.typeId,
                code: row.code,
                id: row.id,
                type: row.type
              }
            })
          }
        }
      }
    }
  ]
}

export const defaultRightColumn = [
  {
    title: '操作',
    fixed: 'right',
    width: 80,
    slots: {
      default: 'operation'
    }
  }
]
export enum VisibleEnum {
  VISIBLE = 0,
  HIDDEN = 1
}
export enum EditableEnum {
  EDITABLE = 0,
  NOT_EDITABLE = 1
}
export enum SuccessEnum {
  SUCCESS = 0,
  ERROR = 1
}
