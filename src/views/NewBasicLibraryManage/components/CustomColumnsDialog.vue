<script lang="tsx" setup>
import { Icon } from '@/components/Icon'
import Sortable from 'sortablejs'
import type { VxeTableInstance } from 'vxe-table'
import { ElForm, ElFormItem, ElInput, ElMessage, ElMessageBox } from 'element-plus'
import { getPageAttributeList, LayoutProperty } from '@/api/systemConfiguration/type'
import { saveCommonViewConfiguration, TableListApi } from '@/api/NewProductLibraryManage/list'
import { EditableEnum, VisibleEnum } from './Help/List'

defineOptions({
  name: 'CustomColumnsDialog'
})

const props = defineProps<{
  modelValue: boolean
  currentView: string
  typeId: string
  viewList: TableListApi.QueryViewConfigurationDetailResp[]
}>()
const formData = ref<TableListApi.QueryViewConfigurationDetailResp>({})
const AllColumnConfig = ref<LayoutProperty.PlmBasePageResp[]>([])
const ColumnConfig = ref<LayoutProperty.PlmBasePageResp[]>([])
const viewListComputed = computed(() => {
  // 去除图形视图
  return props.viewList.filter((item) => item.id !== 'image')
})
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const getAttributeList = async () => {
  if (!props.typeId) {
    return false
  }
  const [error, result] = await getPageAttributeList({ typeCategoryId: props.typeId })
  if (error === null && result?.data) {
    AllColumnConfig.value = result.data
    if (props.modelValue) selectedColumnViewId.value = props.currentView
  }
}
const tableRef = ref<VxeTableInstance>()
const selectedColumnViewId = ref<string>()
const selectedColumnView = computed(() => {
  return props.viewList.find((item) => item.id === selectedColumnViewId.value)
})

// 拖拽排序后,刷新tableData,保留scroll信息
async function refreshTableConfigData() {
  const tableRefVal = tableRef.value
  if (!tableRefVal) {
    return
  }
  const scrollInfo = tableRefVal.getScroll()
  // tableData设置为空数组,不然不触发重新渲染
  await tableRefVal.loadData([])
  await tableRefVal.loadData(ColumnConfig.value)
  await nextTick()
  await tableRefVal.scrollTo(scrollInfo.scrollLeft, scrollInfo.scrollTop)
}

function bindRowDrop() {
  let sortable: Sortable | null = null
  watch(
    () => visible.value,
    async (val) => {
      if (!val) {
        sortable?.destroy()
        return
      }
      await nextTick()
      const { refTableBody } = tableRef.value?.getRefMaps() || {}
      const el = refTableBody?.value?.$el
      const tableBody = el?.querySelector('.vxe-table--body tbody')
      sortable = Sortable.create(tableBody, {
        handle: '.row-drag',
        filter: '.cursor-not-allowed',
        onEnd: async ({ newIndex, oldIndex }) => {
          const dragToNewRow =
            newIndex === oldIndex ||
            newIndex === null ||
            oldIndex === null ||
            !ColumnConfig.value ||
            oldIndex === undefined ||
            newIndex === undefined
          if (dragToNewRow) {
            return
          }
          const currRow = ColumnConfig.value[oldIndex]
          const nextRow = ColumnConfig.value[newIndex]
          if (
            currRow.editable === EditableEnum.NOT_EDITABLE ||
            nextRow.editable === EditableEnum.NOT_EDITABLE
          ) {
            await refreshTableConfigData()
            return
          }
          ColumnConfig.value.splice(oldIndex, 1)
          ColumnConfig.value.splice(newIndex, 0, currRow!)
          await nextTick()
          await refreshTableConfigData()
        }
      })
    }
  )
}
watch(
  () => props.modelValue,
  () => {
    if (props.modelValue) {
      getAttributeList()
    }
  },
  {
    immediate: true,
    deep: true
  }
)
watch(
  () => selectedColumnViewId.value,
  () => {
    if (!selectedColumnView.value) {
      return
    }
    const savedSortedColumns = Array.isArray(selectedColumnView.value.fieldAttr)
      ? selectedColumnView.value.fieldAttr
      : []
    console.log(AllColumnConfig.value, ' AllColumnConfig.value')
    ColumnConfig.value = AllColumnConfig.value
      .map((e) => ({
        ...e,
        visible: e.value
          ? savedSortedColumns.includes(e.value)
            ? VisibleEnum.VISIBLE
            : VisibleEnum.HIDDEN
          : VisibleEnum.HIDDEN
      }))
      /**
       * selectedColumnView只保留了勾选的数据
       * 因此,排序columnConfig中把勾选的数据提前,再按attrField在view中的index排序
       */
      .toSorted((a, b) => {
        if (a.visible !== b.visible) {
          return a.visible ? 1 : -1
        }
        if (a.value && b.value) {
          const indexA = savedSortedColumns.indexOf(a.value)
          const indexB = savedSortedColumns.indexOf(b.value)
          return indexA - indexB
        }

        return 0
      })
  },
  {
    immediate: true,
    deep: true
  }
)

const handleClose = () => {
  visible.value = false
  selectedColumnViewId.value = ''
  ColumnConfig.value = AllColumnConfig.value
}
const handleSubmit = (type: 'add' | 'edit') => {
  if (type === 'edit') {
    if (!selectedColumnViewId.value) {
      ElMessage.warning('请选择要修改的视图')
      return
    }
    formData.value.name = selectedColumnView.value?.name
    formData.value.id = selectedColumnView.value?.id as string
  } else {
    formData.value.name = ''
  }
  const length = ColumnConfig.value.filter((e) => e.visible === VisibleEnum.VISIBLE).length
  if (length < 5) {
    ElMessage.warning('至少需要5个字段')
    return
  }
  ElMessageBox({
    title: type === 'add' ? '新增视图' : '修改视图',
    message: (
      <>
        <ElForm>
          <ElFormItem label="视图名称">
            <ElInput v-model={formData.value.name} placeholder="请输入视图名称" />
          </ElFormItem>
        </ElForm>
        <span>本次选择了{length}个字段</span>
      </>
    ),
    draggable: true,
    closeOnClickModal: false,
    beforeClose: async (action, instance, done) => {
      if (action !== 'confirm') {
        done()
        return
      }
      if (!formData.value.name?.trim()) {
        ElMessage.warning('请输入视图名称')
        return
      }
      instance.confirmButtonLoading = instance.cancelButtonLoading = true
      const [error, result] = await saveCommonViewConfiguration({
        ...formData.value,
        type: props.typeId,
        relationDetailResps: ColumnConfig.value.filter((e) => e.visible === VisibleEnum.VISIBLE)
      })
      instance.confirmButtonLoading = instance.cancelButtonLoading = false
      if (error === null && result) {
        ElMessage.success('保存成功')
        emit('refresh')
        done()
        handleClose()
      }
    }
  }).catch(() => {})
}

bindRowDrop()
defineExpose({
  AllColumnConfig
})
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    title="自定义配置视图"
    top="5vh"
    width="700"
  >
    <template #header>
      <div class="text-lg">自定义配置视图</div>
      <span class="text-sm text-gray-500/50">
        列项显示不得少于5项，灰色选中列不支持隐藏和排序
      </span>
    </template>
    <VxeTable ref="tableRef" :data="ColumnConfig" :max-height="500" row-id="attrField">
      <VxeColumn title="显示" width="60">
        <template #default="{ row }">
          <VxeCheckbox
            v-model="row.visible"
            :checked-value="VisibleEnum.VISIBLE"
            :disabled="row.editable === EditableEnum.NOT_EDITABLE"
            :unchecked-value="VisibleEnum.HIDDEN"
          />
        </template>
      </VxeColumn>
      <VxeColumn field="name" title="列名" />
      <VxeColumn
        class-name="drag-cell"
        field="key"
        header-class-name="drag-header-cell"
        title="拖动调整顺序"
      >
        <template #default="{ row }">
          <span
            :class="
              row.editable === EditableEnum.NOT_EDITABLE ? 'cursor-not-allowed' : 'cursor-move'
            "
            class="row-drag inline-flex"
          >
            <Icon icon="ep:rank" />
          </span>
        </template>
      </VxeColumn>
    </VxeTable>
    <template #footer>
      <ElSelect v-model="selectedColumnViewId" value-key="id">
        <ElOption
          v-for="item in viewListComputed"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </ElSelect>
      <ElButton class="ml-4" @click="handleSubmit('edit')"> 修改视图</ElButton>
      <ElButton type="primary" @click="handleSubmit('add')"> 新增视图</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.el-dialog__body) {
  padding-top: 5px;
}
</style>
