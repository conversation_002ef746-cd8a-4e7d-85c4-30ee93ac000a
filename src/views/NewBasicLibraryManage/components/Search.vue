<script setup lang="ts">
import { Icon } from '@/components/Icon'
import { AdvancedCondition, AdvancedSearchForm } from '@/components/PlmBase/AdvancedSearchForm'
const exportLoading = ref(false)
const modelValue = ref({})
defineOptions({
  name: 'NewSearch'
})
const emit = defineEmits<{
  (e: 'handleSearch', val: any): void
  (e: 'handleReset'): void
}>()
defineProps<{
  typeId: string
}>()
const AdvancedSearchFormRef = ref<InstanceType<typeof AdvancedSearchForm>>()
const handleSearch = () => {
  emit('handleSearch', {
    formData: modelValue.value
  })
}
const handleReset = () => {
  emit('handleReset')
}
const handleAdvancedSearch = (conditions: AdvancedCondition[]) => {
  emit('handleSearch', conditions)
}

// 暴露方法给父组件调用
defineExpose({
  handleSearch,
  handleReset,
  AdvancedSearchFormRef
})
</script>

<template>
  <AdvancedSearchForm
    @advanced-search="handleAdvancedSearch"
    @search="handleSearch"
    ref="AdvancedSearchFormRef"
    @reset="handleReset"
    v-model="modelValue"
    :typeId="typeId"
    :showAdvancedSearch="true"
  >
    <div class="flex">
      <ElButton type="primary" :loading="exportLoading">
        <Icon class="mr-0.5" icon="ep:upload-filled" />
        导出</ElButton
      >
    </div>
  </AdvancedSearchForm>
</template>

<style scoped lang="less"></style>
