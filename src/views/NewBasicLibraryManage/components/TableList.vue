<script setup lang="ts">
import { <PERSON><PERSON><PERSON><PERSON>, ElS<PERSON>ct, ElO<PERSON>, ElDropdown } from 'element-plus'
import { Icon } from '@/components/Icon'
import { defaultColumn, defaultRightColumn } from './Help/List'
import type { VxeTableDefines } from 'vxe-table'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import CardView from '@/views/NewBasicLibraryManage/components/CardView.vue'
import {
  allViewByLogin,
  getMultiProductBase,
  TableListApi
} from '@/api/NewProductLibraryManage/list'
import CustomColumnsDialog from './CustomColumnsDialog.vue'
import { constraintMap, FileType, keyMap, tableFixedType } from '@/components/PlmBase/const'
import { Attribute } from '@/components/PlmBase/type'
import router from '@/router'
import { AdvancedCondition } from '@/components/PlmBase/AdvancedSearchForm'
import { hasPermission } from '@/directives/permission/hasPermi'
import { CuType } from '@/views/NewBasicLibraryManage/components/Help/Status'
defineOptions({
  name: 'NewTableList'
})
const props = defineProps<{
  typeId: string
  type: string
}>()
const emit = defineEmits<{
  (e: 'viewChange', val: string): void
  (e: 'editRow', val: any): void
  (e: 'copyRow', val: any): void
}>()

interface TableColumn extends VxeTableDefines.ColumnOptions {
  field?: string
  title?: string
  width?: number | string
  visible?: boolean
  editable?: boolean
}
// 响应式数据
const viewList = ref<TableListApi.QueryViewConfigurationDetailResp[]>([])
const currentView = ref<string>('')
const customerDialog = reactive({
  visible: false
})
const pager = ref({
  current: 1,
  size: 10,
  total: 0
})
const tableColumns = ref<TableColumn[]>([])
const tableData = ref<any[]>([])
const loading = ref(false)
const tableRef = ref()
const cardViewRef = ref<InstanceType<typeof CardView>>()
// 计算属性：当前视图的表格配置
const currentTableConfig = computed(() => {
  const { plmFieldAttr = [] } = selectedColumnView.value || {}
  return handleTableAttribute(plmFieldAttr)
})
const handleTableAttribute = (fieldList: Attribute[]) => {
  const fixedLeftColumn: Attribute[] = []
  const fixedRightColumn: Attribute[] = []
  const defaultCenterColumn: Attribute[] = []
  fieldList = fieldList.filter((item) => {
    return !defaultColumn[props.type].find((defaultItem) => defaultItem.field === item.value)
  })
  fieldList.forEach((item: Attribute) => {
    const constraint = (item.constraintValue && JSON.parse(item.constraintValue)) || []
    const isImgColumn =
      item.type &&
      constraintMap.typeMap?.[item.type] === constraintMap.typeMap.ATTACHMENT &&
      constraint?.[keyMap.dataType]?.value === FileType.IMAGE
    const isObjectColumn =
      item.type && constraintMap.typeMap?.[item.type] === constraintMap.typeMap.OBJECT
    const clumnConfig = {
      field: isObjectColumn || isImgColumn ? `${item.value}ItemName` : item.value,
      title: item?.constraintDefaultName || item?.label,
      minWidth: item.tableWidth || 100,
      ...(isImgColumn && {
        cellRender: { name: 'Image' }
      })
    }
    if (item.tableFixedType === tableFixedType.left) {
      fixedLeftColumn.push({
        ...clumnConfig,
        fixed: 'left'
      })
    } else if (item.tableFixedType === tableFixedType.right) {
      fixedRightColumn.push({
        ...clumnConfig,
        fixed: 'right'
      })
    } else {
      defaultCenterColumn.push(clumnConfig)
    }
  })
  return [
    ...defaultColumn[props.type],
    ...fixedLeftColumn,
    ...defaultCenterColumn,
    ...fixedRightColumn,
    ...defaultRightColumn
  ]
}
// 获取所有的配置视图
const queryAllConfigViews = async () => {
  if (!props.typeId) {
    return false
  }
  const [error, response] = await allViewByLogin(props.typeId)
  if (!error && response?.data) {
    if (props.type === CuType.SKU) {
      viewList.value = response.data
    } else {
      viewList.value = [
        ...response.data,
        {
          name: '图形视图',
          id: 'image'
        }
      ]
    }

    currentView.value = viewList.value[0]?.id
  }
  getTableData()
}
const selectedColumnView = computed(() => {
  // 去除图形视图
  return viewList.value.find((item) => item.id === currentView.value)
})
// 配置视图
const handleConfigView = () => {
  customerDialog.visible = true
}
const getParams = () => {
  return {
    current: pager.value.current,
    size: pager.value.size,
    typeId: props.typeId,
    type: props.type,
    viewConfigId: currentView.value === 'image' ? viewList.value?.[0].id : currentView.value,
    conditionList: formData.value
  } as TableListApi.searchRes
}
// 获取列表数据
const getTableData = async () => {
  if (loading.value) return false
  if (!currentView.value) return false
  loading.value = true
  const params = getParams()
  const [error, result] = await getMultiProductBase(params)
  if (error === null && result) {
    tableData.value = result.data?.records || []
    pager.value.total = result.data?.total || 0
  }
  loading.value = false
}
const formData = ref()
// 暴露给父组件的方法
const refreshTable = (condition?: AdvancedCondition[]) => {
  formData.value = condition
  getTableData()
}

// 获取表格实例
const getTableRef = () => tableRef.value

const getCheckBoxRecords = () => {
  return currentView.value === 'image'
    ? cardViewRef.value?.getCheckboxRecords()
    : tableRef.value?.getCheckboxRecords()
}
const handleQuery = () => {
  getTableData()
}

// 暴露方法给父组件
defineExpose({
  refreshTable,
  getParams,
  getTableRef,
  getCheckBoxRecords,
  currentView,
  tableColumns,
  tableData
})
const handleOperate = (row: any, command: string) => {
  if (command === 'version') {
    router.push({
      name: 'versionLog',
      query: {
        id: row.id,
        typeId: props.typeId,
        type: props.type
      }
    })
  } else if (command === 'edit') {
    emit('editRow', row)
  } else if (command === 'copy') {
    emit('copyRow', row)
  }
}
const viewChange = () => {
  emit('viewChange', currentView.value)
}
const maxHeight = useTableHeight({ tableRef: tableRef })

//组件挂载时初始化
onMounted(() => {
  // 默认选择表格视图
  queryAllConfigViews()
})
</script>

<template>
  <div class="table-list-container">
    <!-- 顶部操作区域 -->
    <div class="header-section">
      <div class="button-section">
        <slot name="button"></slot>
      </div>

      <div class="view-controls">
        <ElSelect
          @change="viewChange"
          v-model="currentView"
          placeholder="请选择视图"
          filterable
          class="view-selector"
        >
          <ElOption v-for="item in viewList" :key="item.id" :label="item.name" :value="item.id" />
        </ElSelect>

        <ElButton type="primary" @click="handleConfigView" class="config-btn">
          <Icon icon="uil:setting" />
          <span>配置视图</span>
        </ElButton>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <div v-if="selectedColumnView?.id === 'image'" class="card-view">
        <CardView
          :tableData="tableData"
          ref="cardViewRef"
          :type="type"
          @edit-row="(item: any)=>handleOperate(item,'edit')"
          @version-log="(item: any)=>handleOperate(item,'version')"
          @copy-row="(item: any)=>handleOperate(item,'copy')"
          :maxHeight="maxHeight"
          :loading="loading"
        />
      </div>
      <div v-else class="table-view">
        <VxeGrid
          ref="tableRef"
          :data="tableData"
          :columns="currentTableConfig"
          :loading="loading"
          :height="maxHeight - 55"
          border
          stripe
          :cell-config="{ height: 55 }"
          resizable
          show-overflow
          class="custom-table"
        >
          <!-- 自定义空数据状态 -->
          <template #empty>
            <div class="empty-data">
              <Icon icon="ep:document" size="48" />
              <p>暂无数据</p>
            </div>
          </template>
          <template #operation="{ row }">
            <ElDropdown class="m-1" @command="(command:string)=>handleOperate(row,command)">
              <ElButton text type="primary"> 操作 </ElButton>
              <template #dropdown>
                <ElDropdownMenu>
                  <template v-if="hasPermission(['version', 'skc:version', 'sku:version'])">
                    <ElDropdownItem command="version">
                      <span>版本记录</span>
                    </ElDropdownItem>
                  </template>
                  <slot name="operation" :row="row"></slot>
                  <template
                    v-if="
                      hasPermission(['edit', 'edit:newSkc', 'edit:newSku']) &&
                      row.dataStatus?.[0]?.toLowerCase() !== 'invalid'
                    "
                  >
                    <ElDropdownItem command="edit">
                      <span>修改</span>
                    </ElDropdownItem>
                  </template>
                </ElDropdownMenu>
              </template>
            </ElDropdown>
          </template>
        </VxeGrid>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
    </div>
  </div>
  <CustomColumnsDialog
    v-model="customerDialog.visible"
    :view-list="viewList"
    :type-id="typeId"
    :current-view="currentView"
    @refresh="queryAllConfigViews"
  />
</template>

<style scoped lang="less">
.table-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;

  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid var(--el-border-color-light);

    .button-section {
      flex: 1;
    }

    .view-controls {
      display: flex;
      align-items: center;
      gap: 12px;

      .view-selector {
        width: 160px;
      }

      .config-btn {
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }
  }

  .table-section {
    flex: 1;
    min-height: 0;

    .card-view,
    .default-view {
      height: 100%;
      align-items: center;
      justify-content: center;
    }

    .table-view {
      height: 100%;

      .custom-table {
        height: 100%;
      }
    }

    .empty-state,
    .empty-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      color: var(--el-text-color-secondary);

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}
</style>
