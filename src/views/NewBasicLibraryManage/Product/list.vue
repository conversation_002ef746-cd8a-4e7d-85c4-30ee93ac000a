<script setup lang="ts">
import Search from '@/views/NewBasicLibraryManage/components/Search.vue'
import TableList from '@/views/NewBasicLibraryManage/components/TableList.vue'
import StatusDialog from '@/views/NewBasicLibraryManage/Product/components/StatusDialog.vue'
import { CuType } from '@/views/NewBasicLibraryManage/components/Help/Status'
import { AdvancedCondition } from '@/components/PlmBase/AdvancedSearchForm'
import { Icon } from '@/components/Icon'
import { AttributeData } from '@/components/PlmBase/type'
import { convertProducts, updateProductStatus } from '@/api/NewProductLibraryManage/detail'
import { hasPermission } from '@/directives/permission/hasPermi'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import ImportDialog from '@/views/NewBasicLibraryManage/components/ImportDialog.vue'
defineOptions({
  name: 'NewProductList'
})
const router = useRouter()
defineEmits<{
  (e: 'transform'): void
}>()
const TableListRef = ref<InstanceType<typeof TableList>>()
const SearchRef = ref<InstanceType<typeof Search>>()
const statusModal = reactive({
  visible: false,
  selectedRows: []
})
const currentRoute = useRouter()?.currentRoute
const otherMeta = currentRoute.value.meta?.otherMeta as string
const typeId = computed(() => {
  return JSON.parse(otherMeta)?.productId
})
const type = computed(() => {
  return JSON.parse(otherMeta)?.type
})
const addFunc = () => {
  router.push({
    name: 'NewAddProduct',
    query: {
      typeId: typeId.value,
      type: type.value
    }
  })
}
const turnOpenClose = () => {
  const selectRows = TableListRef.value?.getCheckBoxRecords()
  if (selectRows && selectRows.length > 0) {
    statusModal.selectedRows = selectRows
    statusModal.visible = true
  } else {
    ElMessage.warning('请选择数据')
  }
}
const handleEditRow = (row: AttributeData) => {
  router.push({
    name: 'NewEditProduct',
    query: {
      typeId: typeId.value,
      code: row.code,
      id: row.id,
      type: type.value
    }
  })
}
const addColor = () => {
  const selectRows = TableListRef.value?.getCheckBoxRecords()
  if (!selectRows || selectRows.length <= 0) {
    ElMessage.warning('只能选择一条数据')
    return
  }
  router.push({
    name: 'ColorNewProduct',
    query: {
      code: selectRows[0].code,
      id: selectRows[0].id
    }
  })
}
const transformFunc = async () => {
  const selectRows = TableListRef.value?.getCheckBoxRecords()
  if (!selectRows || selectRows.length <= 0) {
    ElMessage.warning('只能选择一条数据')
    return
  }
  const ids = selectRows.map((item) => item.id)
  const [error] = await convertProducts(ids)
  if (!error) {
    ElMessage.success('转化成功')
    TableListRef.value?.refreshTable()
  }
}
const handleCopyRow = (row: AttributeData) => {
  console.log('复制')
  router.push({
    name: 'NewCopyProduct',
    query: {
      id: row.id,
      code: row.code
    }
  })
}
const saveStatusFunc = async (attributes: AttributeData[]) => {
  const [error] = await updateProductStatus({
    list: attributes
  })
  if (!error) {
    ElMessage.success('操作成功')
    statusModal.visible = false
    TableListRef.value?.refreshTable()
  }
}
const isShowButton = computed(() => {
  const selectRows = TableListRef.value?.getCheckBoxRecords()
  if (!selectRows || selectRows.length <= 0) {
    return true
  }
  return selectRows.findIndex((item: AttributeData) => item.dataStatus?.[0] === 'invalid') === -1
})
const handleReset = () => {
  TableListRef.value?.refreshTable()
}
const handleSearch = (condition?: AdvancedCondition[]) => {
  TableListRef.value?.refreshTable(condition)
}
const { handleExport: exportFn, loading } = useOmsExport()
const handleExport = () => {
  const selected: AttributeData[] | undefined = TableListRef.value?.getCheckBoxRecords()

  let reqParam: string
  if (selected && selected?.length > 0) {
    const idList = selected.map((item) => item.id)
    reqParam = JSON.stringify({ idList })
  } else {
    reqParam = JSON.stringify(TableListRef.value?.getParams())
  }
  exportFn({
    exportType: 'mini_product_style_export',
    reqParam
  })
}
const handleQuery = () => {
  SearchRef.value?.AdvancedSearchFormRef?.handleSearch()
}
const importDialogVisible = ref<boolean>(false)
onActivated(() => {
  SearchRef.value?.AdvancedSearchFormRef?.handleSearch()
})
</script>

<template>
  <ContentWrap>
    <Search
      ref="SearchRef"
      :typeId="typeId"
      :type="type"
      @handle-search="handleSearch"
      @handle-reset="handleReset"
    />
    <div class="operation"> </div>
    <TableList
      ref="TableListRef"
      @copy-row="handleCopyRow"
      @edit-row="handleEditRow"
      :typeId="typeId"
      :type="type"
    >
      <template #button>
        <ElButton type="primary" v-hasPermi="'add:newProduct'" @click="addFunc"
          ><Icon icon="ep:plus" /><span>新增</span></ElButton
        >
        <ElButton
          type="primary"
          v-if="isShowButton && hasPermission(['color:product'])"
          @click="addColor"
          ><Icon icon="ep:plus" /><span>产品配色</span></ElButton
        >
        <!--        <ElButton type="primary" v-hasPermi="'add:colorSize'" @click="addFunc"-->
        <!--          ><Icon icon="ep:plus" /><span>加色加码</span></ElButton-->
        <!--        >-->
        <ElButton
          v-if="isShowButton && hasPermission(['trans-product'])"
          type="primary"
          @click="transformFunc"
          ><Icon icon="ep:plus" /><span>转化为商品</span></ElButton
        >
        <ElButton type="primary" v-hasPermi="'change:status'" @click="turnOpenClose"
          ><Icon icon="ion:switch" /><span>生效/作废</span></ElButton
        >
        <ElButton type="primary" v-hasPermi="'import:product'" @click="importDialogVisible = true"
          ><Icon icon="ant-design:import-outlined" /><span>导入</span></ElButton
        >
        <ElButton
          type="primary"
          v-hasPermi="'export:product'"
          :loading="loading"
          @click="handleExport"
          ><Icon icon="ep:upload-filled" /><span>导出</span></ElButton
        >
      </template>
      <template #operation="{ row }">
        <template
          v-if="hasPermission(['copy']) && row.dataStatus?.[0]?.toLowerCase() !== 'invalid'"
        >
          <ElDropdownItem command="copy">
            <span>复制</span>
          </ElDropdownItem>
        </template>
      </template>
    </TableList>
    <status-dialog
      :typeId="typeId"
      @confirm="saveStatusFunc"
      :type="CuType.Prodcut"
      v-model="statusModal.visible"
      :selected-rows="statusModal.selectedRows"
    />
    <ImportDialog
      v-model="importDialogVisible"
      :typeCategoryId="typeId"
      type="product"
      @refresh="handleQuery"
    />
  </ContentWrap>
</template>
<style scoped lang="less"></style>
