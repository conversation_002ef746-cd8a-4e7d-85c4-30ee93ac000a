<script setup lang="ts">
import { CuType, statusConfig } from '@/views/NewBasicLibraryManage/components/Help/Status'
import { VxeGridInstance } from 'vxe-table'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { useHook } from '@/views/NewBasicLibraryManage/components/Help/useHook'
import { Attribute, AttributeData, tableColumn } from '@/components/PlmBase/type'
import FormItemRender from '@/components/PlmBase/component/FormItemRender.vue'
import { constraintMap } from '@/components/PlmBase/const'
import { ElMessage, ElMessageBox, ElButton, ElAlert } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { VxeTablePropTypes } from 'vxe-table'
import { queryFormDataValue, saveFormDataValue } from '@/components/PlmBase/help'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'StatusDialogOptimized'
})

// Props接口定义
interface Props {
  modelValue: boolean
  selectedRows: AttributeData[]
  typeId: string

  type: CuType
  loading?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits接口定义
interface Emits {
  (e: 'update:modelValue', val: boolean): void
  (e: 'update:selectedRows', val: AttributeData[]): void
  (e: 'refresh'): void
  (e: 'confirm', data: AttributeData[]): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const allAttributes = ref<Attribute[]>([])
const tableRef = ref<VxeGridInstance>()
const tableLoading = ref(false)
const fieldRefs = ref<Record<string, any>>({})
// 表格数据（深拷贝，避免直接修改props）
const tableData = ref<AttributeData[]>([])

// 是否有数据变更
const hasChanges = computed(() => {
  return JSON.stringify(tableData.value) !== JSON.stringify(props.selectedRows)
})

// 表格配置
const maxHeight = useTableHeight({ tableRef })

// 优化后的useHook使用 - 利用全局缓存和新功能
const currentRoute = useRouter()?.currentRoute
const routeMeta = computed(() => {
  return currentRoute?.value?.meta?.otherMeta
    ? JSON.parse(currentRoute.value.meta.otherMeta as string)
    : {}
})

const { AllAttribute, filterAttributeTableList, isDataLoaded, waitForData } = useHook(
  {
    needProductAttribute: props.type === CuType.Prodcut,
    needSkcAttribute: props.type === CuType.SKC,
    needSkuAttribute: props.type === CuType.SKU
  },
  routeMeta.value
)
// 简化的获取属性配置方法
const getAllAttribute = async () => {
  try {
    // 检查数据是否已加载
    if (isDataLoaded(props.typeId)) {
      // 数据已缓存，直接使用
      const statusList: tableColumn[] = statusConfig[props.type]?.status || []
      allAttributes.value = filterAttributeTableList(statusList, props.typeId) as Attribute[]
      return
    }
    await waitForData(props.typeId, 3000) // 最多等待3秒

    const statusList: tableColumn[] = statusConfig[props.type]?.status || []
    allAttributes.value = filterAttributeTableList(statusList, props.typeId) as Attribute[]
    console.log('属性配置加载完成:', allAttributes.value)
  } catch (error) {
    console.error('获取属性配置失败:', error)
    allAttributes.value = []
  }
}
watch(
  () => props.modelValue,
  async (isVisible) => {
    if (isVisible && props.typeId && props.selectedRows?.length > 0) {
      try {
        // 获取属性配置
        await getAllAttribute()

        // 初始化表格数据
        tableData.value = JSON.parse(JSON.stringify(props.selectedRows))
        tableData.value.forEach((item) => {
          queryFormDataValue(AllAttribute[props.typeId], item)
        })

        console.log('StatusDialog初始化完成')
      } catch (error) {
        console.error('StatusDialog初始化失败:', error)
      }
    }
  }
)
// 确认提交
const handleConfirm = async () => {
  const errors = await tableRef.value?.validate?.(true).catch(() => false)
  if (errors) {
    ElMessage.error(`信息校验失败，请检查表格数据`)
    return
  }
  if (props.type === CuType.Prodcut) {
    if (!hasChanges.value) {
      ElMessage.warning('没有数据变更')
      return
    }
  }
  await ElMessageBox.confirm(`确认要变更 ${tableData.value.length} 条数据的状态吗？`, '确认变更', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const attributes: AttributeData[] = []
      const detailData = JSON.parse(JSON.stringify(tableData.value))
      detailData.forEach((item) => {
        saveFormDataValue(AllAttribute[props.typeId], item)
        attributes.push({
          id: item.id,
          attributes: {
            ...item
          }
        })
      })
      emit('confirm', attributes)
    })
    .catch(() => {
      // 用户取消
    })
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
  visible.value = false
}
// 动态生成表格验证规则
const tableRules = computed<VxeTablePropTypes.EditRules>(() => {
  const rules: VxeTablePropTypes.EditRules = {
    // 固定的必填字段
    dataStatus: [
      {
        required: true,
        content: '请选择状态',
        trigger: 'change'
      }
    ],
    invalidReason: [
      {
        required: true,
        content: '请输入原因',
        trigger: 'blur'
      }
    ]
  }
  return rules
})
// 获取约束类型
const getConstraintType = (attribute: Attribute): string => {
  return (attribute && attribute.type && constraintMap.typeMap?.[attribute.type]) || ''
}
// 获取字段值
const getFieldValue = (row: AttributeData, column: Attribute) => {
  const isObject = column?.type === 'OBJECT'
  return isObject ? row[`${column.field}ItemName`] : row[column.field]
}
</script>

<template>
  <Dialog
    v-model="visible"
    title="状态变更"
    width="80%"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <!-- 提示信息 -->
    <ElAlert
      v-if="tableData.length > 0"
      :title="`共选择 ${tableData.length} 条数据进行状态变更`"
      type="info"
      :closable="false"
      class="mb-4"
    />

    <!-- 表格 -->
    <VxeTable
      :data="tableData"
      :max-height="maxHeight - 60"
      :min-height="300"
      ref="tableRef"
      :edit-config="{
        showIcon: false,
        mode: 'cell',
        trigger: 'click'
      }"
      :valid-config="{ showMessage: true, autoPos: true }"
      :edit-rules="tableRules"
    >
      <VxeColumn type="seq" title="序号" width="60" />
      <!-- 动态生成的材料属性列 -->
      <template v-for="column in allAttributes" :key="column.field">
        <VxeColumn
          :field="column.field"
          :title="column.title"
          :width="column?.tableWidth"
          :min-width="column?.tableWidth || 200"
          :header-class-name="column.constraintList.required?.value ? 'header-required' : ''"
        >
          <template #default="{ row }: { row: any }">
            <!-- 编辑模式 -->
            <FormItemRender
              v-if="column.slots"
              :ref="(el) => { if (el) fieldRefs[column.field!] = el }"
              v-model="row[column.field]"
              :constraintType="getConstraintType(column)"
              :constraintList="column.constraintList"
              class="table-cell-editor"
            />
            <template v-else><span v-html="getFieldValue(row, column)"></span></template>
          </template>
        </VxeColumn>
      </template>
    </VxeTable>
    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel"> 取消 </ElButton>
        <ElButton
          type="primary"
          @click="handleConfirm"
          :loading="tableLoading"
          :disabled="!hasChanges"
        >
          确认
        </ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style scoped lang="less">
.status-edit-cell {
  width: 100%;

  .el-alert {
    margin-bottom: 8px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.vxe-table) {
  .vxe-body--column {
    .vxe-cell {
      padding: 8px;
    }
  }
}
</style>
