<script setup lang="ts">
import { ref, computed, reactive, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import FormRender from '@/components/PlmBase/component/FormRender.vue'
import { Attribute, AttributeData, layoutAttribute } from '@/components/PlmBase/type'
import AddMaterial from './AddMaterial.vue'
import { productSave, productUpdate } from '@/api/NewProductLibraryManage/detail'
import { saveFormDataValue } from '@/components/PlmBase/help'
import { VxeTableDefines } from 'vxe-table'
import { FormatSizeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import { hasPermission } from '@/directives/permission/hasPermi'
const props = defineProps<{
  pageLayout: layoutAttribute[]
  addMaterialAttribute: Attribute[]
  formData: AttributeData
  allAttribute: Record<string, Attribute[]>
}>()
defineOptions({
  name: 'NewProductMessage'
})
defineEmits<{
  (e: 'update:modelValue', val: Attribute): void
}>()
const router = useRouter()
const currentRoute = useRouter()?.currentRoute
const otherMeta = currentRoute.value.meta?.otherMeta as string
const typeId = computed(() => JSON.parse(otherMeta).productId)
const skcId = computed(() => JSON.parse(otherMeta).skcId)
const skcType = computed(() => JSON.parse(otherMeta).skcType)
const type = computed(() => JSON.parse(otherMeta).type)
const pageLayoutCode = computed(() => JSON.parse(otherMeta).pageLayoutCode)
const skcPageLayoutCode = computed(() => JSON.parse(otherMeta).skcPageLayoutCode)
const isCopy = computed(() => {
  return route.name === 'NewCopyProduct'
})
const isDetail = computed(() => {
  return route.name === 'NewProductDetail'
})

const activeName = ref<string>('')
const saving = ref(false)
// 本地值管理
//编辑
const route = useRoute()
const isEdit = computed(() => {
  return route.name === 'NewEditProduct'
})
// 查看
const isView = computed(() => {
  return route.name === 'NewProductDetail'
})
const localFormData = reactive({
  productReq: {},
  skcReq: []
})
watch(
  () => [props.pageLayout, props.formData],
  () => {
    if (props.pageLayout && props.formData) {
      // 深拷贝避免引用问题
      localFormData.productReq = JSON.parse(JSON.stringify(props.formData))
      localFormData.skcReq = localFormData.productReq?.skcInfoDetailResp
      activeName.value = props.pageLayout[0]?.name
    }
  },
  { immediate: true, deep: true }
)

// 监听props变化，但只在特定情况下更新（比如切换了不同的产品）
watch(
  () => typeId.value,
  (newTypeId, oldTypeId) => {
    if (newTypeId !== oldTypeId && newTypeId) {
      if (props.formData) {
        localFormData.productReq = JSON.parse(JSON.stringify(props.formData))
      }
    }
  }
)
const formRenderRef = ref<InstanceType<typeof FormRender>>()
const tableRef = ref<InstanceType<typeof AddMaterial>>()
// 保存提交
const submit = async () => {
  if (saving.value) return

  saving.value = true
  // 2. 校验表格数据
  try {
    // 1. 校验基本信息表单
    const formValid = await formRenderRef.value?.[0]?.validate()
    if (!formValid) {
      ElMessage.error('基本信息表单校验失败，请检查必填项')
      return false
    }
    const errorMsg: VxeTableDefines.ValidatorErrorMapParams | undefined | boolean =
      await tableRef.value?.validate?.(true).catch(() => false)
    if (errorMsg) {
      ElMessage.error('产品配色信息校验失败，请检查表格数据')
      return false
    }
    // 3. 创建保存数据的副本，避免影响原始数据
    const saveData = {
      productReq: JSON.parse(JSON.stringify(localFormData.productReq)),
      skcReq: JSON.parse(JSON.stringify(tableRef.value?.localFormData || []))
    }
    // 4. 处理表单数据
    saveFormDataValue(props.allAttribute[typeId.value], saveData.productReq)
    saveData.skcReq.forEach((item: any) => {
      saveFormDataValue(props.allAttribute[skcId.value], item)
    })
    const params = {
      productReq: {
        typeId: typeId.value,
        type: type.value,
        pageLayoutCode: pageLayoutCode.value,
        attributes: saveData.productReq
      },
      skcReq: {
        attributes: saveData.skcReq,
        typeId: typeId.value,
        type: skcType.value,
        pageLayoutCode: skcPageLayoutCode.value
      }
    }
    if (isCopy.value) {
      params.productReq.attributes.id = null
      params.skcReq.attributes.forEach((item: any) => {
        delete item.id
      })
    }
    // 5. 提交保存
    let error
    if (isEdit.value) {
      ;[error] = await productUpdate(params)
    } else {
      ;[error] = await productSave(params)
    }
    if (!error) {
      ElMessage.success('保存成功')
      handleClose()
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}
const sizeOptions = ref<FormatSizeListAPI.Data[]>([])
const handleSizeChange = (formOption: FormatSizeListAPI.Data[]) => {
  sizeOptions.value = formOption
  setTimeout(() => {
    tableRef.value?.clearField('selectedSize')
  })
}
const handleClose = () => {
  useClosePage('NewProductList')
}
const editFunc = () => {
  router.push({
    name: 'NewEditProduct',
    query: {
      typeId: typeId.value,
      code: props.formData.code,
      id: props.formData.id,
      type: type.value
    }
  })
}
const editColor = () => {
  router.push({
    name: 'ColorNewProduct',
    query: {
      code: props.formData.code,
      id: props.formData.id
    }
  })
}
</script>

<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in pageLayout" :key="item.id" :label="item.name" :name="item.name">
        <form-render
          ref="formRenderRef"
          @size-change="handleSizeChange"
          v-model="localFormData.productReq"
          :gridItems="item.gridItems"
        />
      </el-tab-pane>
      <el-tab-pane v-if="isDetail" label="产品配色信息" name="产品配色信息">
        <div>
          <AddMaterial
            :addMaterialAttribute="addMaterialAttribute"
            ref="tableRef"
            :sizeOptions="sizeOptions"
            :modelValue="localFormData.skcReq"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-tabs model-value="产品配色信息" v-if="!isDetail">
      <el-tab-pane label="产品配色信息" name="产品配色信息">
        <div>
          <AddMaterial
            :addMaterialAttribute="addMaterialAttribute"
            ref="tableRef"
            :sizeOptions="sizeOptions"
            :modelValue="localFormData.skcReq"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
    <div class="mt-4 text-center">
      <ElButton @click="handleClose" class="mr-4">返回</ElButton>
      <ElButton
        type="primary"
        :loading="saving"
        @click="submit"
        v-if="!isView && hasPermission(['edit:save', 'new:save', 'copy:save'])"
      >
        确定
      </ElButton>
      <ElButton
        type="primary"
        v-if="
          isDetail &&
          activeName === '产品基本信息' &&
          isView &&
          hasPermission(['edit:newProduct']) &&
          formData?.dataStatus?.toLowerCase() !== 'invalid'
        "
        :loading="saving"
        @click="editFunc"
      >
        修改产品
      </ElButton>
      <ElButton
        type="primary"
        @click="editColor"
        v-if="
          isDetail &&
          activeName === '产品配色信息' &&
          hasPermission(['edit:newProductColor']) &&
          formData?.dataStatus?.toLowerCase() !== 'invalid'
        "
        :loading="saving"
        >修改产品配色</ElButton
      >
    </div>
  </div>
</template>

<style scoped lang="less"></style>
