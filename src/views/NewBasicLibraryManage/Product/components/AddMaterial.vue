<script lang="tsx" setup>
import { ref, computed, watch, onMounted } from 'vue'
import { VxeTableInstance, VxeTablePropTypes } from 'vxe-table'
import { ElMessage, ElMessageBox } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { VXE_TABLE_ROW_KEY } from '@/constants'
import FormItemRender from '@/components/PlmBase/component/FormItemRender.vue'
// 导入MaterialConfig类型
import { MaterialConfig, needAttribute } from '@/views/NewBasicLibraryManage/components/Help/Detail'
import { ProductDataStatusEnum } from '@/views/basic-library-manage/const'
import { Attribute, AttributeData } from '@/components/PlmBase/type'
import { useHook } from '@/views/NewBasicLibraryManage/components/Help/useHook'
import { constraintMap, keyMap } from '@/components/PlmBase/const'
import { FormatSizeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import StyleCopyDialog from '@/views/NewBasicLibraryManage/Product/components/StyleCopyDialog.vue'
import { copyColorByProduct } from '@/api/NewProductLibraryManage/detail'
import { queryFormDataValue } from '@/components/PlmBase/help'
const currentRoute = useRouter()?.currentRoute
const route = useRoute()
const otherMeta = currentRoute.value.meta?.otherMeta as string
const routeMeta = JSON.parse(otherMeta)

interface Props {
  modelValue: any[] // 外部传入的表格数据
  isEdit?: boolean
  type?: string
  sizeOptions?: FormatSizeListAPI.Data[]
  typeId?: string
}
const props = withDefaults(defineProps<Props>(), {
  isEdit: false,
  type: '',
  sizeOptions: () => [],
  modelValue: () => []
})
const emit = defineEmits<{
  add: [row: any]
  delete: [row: any, index: number]
  copy: [row: any, index: number]
  change: [data: any[]]
}>()

// 本地数据管理，支持双向绑定
const localFormData = ref<AttributeData[]>([])
const materialAttributes = ref<Attribute[]>([])

const skcId = computed(() => routeMeta?.skcId as string)
const getSizeOptions = computed(() => props.sizeOptions)
const isDetail = computed(() => route.name === 'NewProductDetail')

// 优化后的useHook使用 - 利用全局缓存
const { AllAttribute, formData, isDataLoaded, waitForData } = useHook(
  {
    needSkcAttribute: true,
    needProductAttribute: false,
    needSkuAttribute: false
  },
  routeMeta
)
// 优化后的获取SKC属性方法
const getSKCAttribute = async () => {
  try {
    // 确保SKC属性数据已加载
    if (!isDataLoaded(skcId.value)) {
      await waitForData(skcId.value, 3000)
    }

    if (!AllAttribute[skcId.value]) {
      console.warn('SKC属性数据未加载:', skcId.value)
      return
    }

    materialAttributes.value = MaterialConfig.map((config) => {
      const attributeOne: Attribute | undefined = AllAttribute[skcId.value].find(
        (attribute: Attribute) => attribute.value === config.field
      )
      return {
        ...config,
        ...attributeOne,
        constraintList: attributeOne && JSON.parse(attributeOne.constraintValue || '{}')
      } as Attribute
    })

    console.log('SKC属性配置加载完成:', materialAttributes.value.length)
  } catch (error) {
    console.error('获取SKC属性失败:', error)
    materialAttributes.value = []
  }
}
// 获取约束类型
const getConstraintType = (attribute: Attribute): string => {
  return (attribute.type && constraintMap.typeMap[attribute.type]) || ''
}

// 生成表格列配置
const generateTableColumns = computed(() => {
  return materialAttributes.value.map((attribute) => {
    const constraints = attribute.constraintList
    return {
      field: attribute.field,
      title: attribute.name,
      type: attribute.type,
      tableWidth: attribute.tableWidth || 300,
      isRequired: constraints?.[keyMap.required]?.value,
      attribute,
      constraintList: constraints
    } as Attribute
  })
})
const tableRef = ref<VxeTableInstance>()

// 动态生成表格验证规则
const tableRules = computed<VxeTablePropTypes.EditRules>(() => {
  const rules: VxeTablePropTypes.EditRules = {
    // 固定的必填字段
    color: [
      {
        required: true,
        content: '请选择产品配色',
        trigger: 'blur'
      }
    ],
    selectedSize: [
      {
        required: true,
        content: '请选择尺码',
        trigger: 'blur'
      }
    ]
  }
  return rules
})
// 新增SKC行
const handleAddSKC = () => {
  // 创建新行数据，包含所有必要字段的默认值
  const newRow = {
    [VXE_TABLE_ROW_KEY]: undefined,
    id: undefined,
    skcCode: '',
    dataStatus: ProductDataStatusEnum.DRAFT,
    dataStatusItemName: '草稿',
    colorCode: '',
    colorId: '',
    colorCodeItemName: '',
    thumbnail: '',
    sampleUrl: [],
    // 根据materialAttributes动态添加字段默认值
    ...materialAttributes.value.reduce((acc, attr) => {
      acc[attr.field] = attr.type && getDefaultValueByType(attr.type)
      return acc
    }, {})
  }

  // 更新本地数据并通知外部
  const newData = [...localFormData.value, newRow]
  localFormData.value = newData

  // 刷新表格
  tableRef.value?.loadData(localFormData.value)

  // 触发事件
  emit('add', newRow)

  emit('change', localFormData.value) // 通知外部数据变化
  ElMessage.success('已添加新行')
}

// 根据字段类型获取默认值
const getDefaultValueByType = (type: string) => {
  const typeMap = constraintMap.typeMap
  switch (type) {
    case typeMap.INT:
    case typeMap.FLOAT:
      return null
    case typeMap.BOOLEAN:
      return false
    case typeMap.OBJECT:
      return null
    case typeMap.DATE_TIME:
      return null
    case typeMap.ATTACHMENT:
      return []
    default:
      return ''
  }
}

// 删除单行
const handleDelSKC = (row: any, index: number) => {
  if (localFormData.value.length === 1) {
    ElMessage.warning('至少保留一行数据')
    return
  }
  ElMessageBox.confirm('确认要删除这行数据吗？', '删除确认', {
    confirmButtonText: '确认删除',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 更新本地数据并通知外部
      const newData = [...localFormData.value]
      newData.splice(index, 1)
      localFormData.value = newData

      // 刷新表格
      tableRef.value?.loadData(localFormData.value)

      // 触发事件
      emit('delete', row, index)
      emit('change', localFormData.value) // 通知外部数据变化
      ElMessage.success('已删除行数据')
    })
    .catch(() => {
      // 用户取消
    })
}
// 复制SKC行
const handleCopySKC = (row: any, index: number) => {
  const copyRow = cloneDeep(row)

  // 清除不应该复制的字段
  const newRow = {
    ...copyRow,
    id: undefined,
    [VXE_TABLE_ROW_KEY]: undefined,
    code: '',
    selectedSize: [],
    sampleUrl: [],
    dataStatus: ProductDataStatusEnum.DRAFT,
    dataStatusItemName: '草稿',
    // 清除其他唯一性字段
    velvetApplied: undefined,
    velvetRequirements: undefined
  }

  // 更新本地数据并通知外部
  const newData = [...localFormData.value]
  newData.splice(index + 1, 0, newRow)
  localFormData.value = newData

  // 刷新表格
  tableRef.value?.loadData(localFormData.value)

  // 触发事件
  emit('copy', row, index)
  emit('change', localFormData.value) // 通知外部数据变化
  ElMessage.success('已复制行数据')
}
// 格式化约束配置为FormItemRender需要的格式
const formatConstraintList = computed(() => {
  return (
    selectedAttribute.value &&
    materialAttributes.value.find((item) => item.field === selectedAttribute.value?.field)
  )
})

// 表格编辑相关
const editingCells = ref(new Set())

// 开始编辑单元格
const handleCellClick = ({ row, column }) => {
  const cellKey = `${row[VXE_TABLE_ROW_KEY] || row}_${column.field}`
  editingCells.value.add(cellKey)
}
// 检查单元格是否可编辑
const isCellEditable = (row: any, field: string) => {
  if (row.dataStatus === ProductDataStatusEnum.EFFECT) {
    return field === 'selectedSize' || field === 'sampleImages'
  }
  if (row.dataStatus === ProductDataStatusEnum.DRAFT) {
    return true
  }
  return row.dataStatus !== ProductDataStatusEnum.INVALID
}

// 批量修改相关
const selectedAttribute = ref<{
  field: string
  title: string
}>()
const selectedValue = ref<any>(null)
// 初始化默认数据
const initializeDefaultData = async () => {
  try {
    // 确保数据已加载
    if (!isDataLoaded(skcId.value)) {
      await waitForData(skcId.value, 3000)
    }

    if (formData[skcId.value]) {
      localFormData.value = [
        {
          ...formData[skcId.value],
          dataStatus: ProductDataStatusEnum.DRAFT,
          dataStatusItemName: '草稿'
        }
      ]
    }
  } catch (error) {
    localFormData.value = []
  }
}
// 监听属性切换，清空值
watch(
  () => selectedAttribute.value,
  (newAttr, oldAttr) => {
    if (newAttr?.field !== oldAttr?.field) {
      selectedValue.value = null
      console.log('属性切换，清空值:', newAttr?.field)
    }
  }
)

const handleBatchUpdate = () => {
  const selected = tableRef.value?.getCheckboxRecords() || []
  if (!selected.length) {
    ElMessage.warning('请先勾选数据')
    return
  }

  if (!selectedAttribute.value?.field || selectedValue.value === null) {
    ElMessage.warning('请先选择属性和值')
    return
  }

  // 普通字段直接设置值
  selected.forEach((row) => {
    if (isCellEditable(row, formatConstraintList.value!.field)) {
      row[formatConstraintList.value!.field] = selectedValue.value
      if (formatConstraintList.value!.field === 'color') {
        row.colorItemName = formatName
      }
    }
  })

  // 刷新表格
  tableRef.value?.loadData(localFormData.value)

  emit('change', localFormData.value) // 通知外部数据变化
  ElMessage.success(`批量更新成功，共更新 ${selected.length} 行数据`)
}

const handleResetBatch = () => {
  const selected = tableRef.value?.getCheckboxRecords() || []
  if (!selected.length) {
    ElMessage.warning('请先勾选数据')
    return
  }
  if (!selectedAttribute.value?.field) {
    ElMessage.warning('请先选择属性')
    return
  }

  // 重置选中行的数据
  selected.forEach((row) => {
    row[formatConstraintList.value!.field] = null
  })
  // 刷新表格
  tableRef.value?.loadData(localFormData.value)
  emit('change', localFormData.value) // 通知外部数据变化
  ElMessage.success('批量重置成功')
}
const validate = computed(() => tableRef.value?.validate)
const scrollToRow = computed(() => tableRef.value?.scrollToRow)
const setSelectCell = computed(() => tableRef.value?.setSelectCell)
watch(
  () => props.modelValue,
  async (newValue, oldValue) => {
    // 深度比较，只有在外部真正改变时才更新localFormData
    const newValueStr = JSON.stringify(newValue)
    const oldValueStr = JSON.stringify(oldValue)

    if (newValueStr !== oldValueStr) {
      if (newValue && newValue.length > 0) {
        localFormData.value = [...newValue]
      } else {
        // 初始化默认数据
        await initializeDefaultData()
      }
    }
  },
  { immediate: true, deep: true }
)
// 初始化组件
const initializeComponent = async () => {
  await getSKCAttribute()
  if (props.modelValue.length === 0) {
    await initializeDefaultData()
  }
}
// 组件挂载时初始化
onMounted(() => {
  initializeComponent()
})
//产品配色
const isNewColor = computed(() => {
  return route.name === 'ColorNewProduct'
})
//code
const productCode = computed(() => {
  return route.query.code as string
})
const styleCopyDialogVisible = ref<boolean>(false)
const handleStyleCopy = async (formData: any) => {
  const [err, result] = await copyColorByProduct({
    productCode: productCode.value,
    copyProductCode: formData.copyProductNumber
  })
  if (!err) {
    const data = result?.data || []
    data.forEach((item: AttributeData, index: number) => {
      queryFormDataValue(AllAttribute[skcId.value], item)
      item[VXE_TABLE_ROW_KEY] = Date.now() + index
    })
    localFormData.value = data
    tableRef.value?.loadData(localFormData.value)
  }
  styleCopyDialogVisible.value = false
}
const formatName = ref('')
const changeDialogName = (name: string) => {
  formatName.value = name
}
//对外清空数据
const clearField = (field: string) => {
  localFormData.value.forEach((item) => {
    const existArr = getSizeOptions.value.filter((inner) =>
      item.selectedSize?.includes(inner.value!)
    )
    if (!existArr || existArr.length === 0) {
      item[field] = null
    } else {
      item[field] = existArr.map((item) => item.value)
    }
  })
  tableRef.value?.loadData(localFormData.value)
}

// 暴露方法给父组件
defineExpose({
  validate,
  scrollToRow,
  localFormData,
  setSelectCell,
  clearField,
  // 新增的实用方法
  refreshData: () => {
    // 刷新表格数据
    tableRef.value?.loadData(localFormData.value)
  }
})
</script>

<template>
  <div class="mb-2 w-full">
    <!-- 批量修改区域 -->
    <div class="flex items-center gap-2 p-3 bg-gray-50 rounded">
      <ElButton v-if="!isDetail" type="primary" @click="handleAddSKC">
        <Icon icon="ep:plus" />
        <span> 添加</span>
      </ElButton>
      <ElButton v-if="isNewColor" type="primary" @click="styleCopyDialogVisible = true">
        <Icon icon="ep:copy-document" />
        <span>同款复制</span>
      </ElButton>
      <template v-if="!isNewColor && !isDetail">
        <h4 class="mb-0 text-sm font-medium">批量修改属性</h4>
        <!-- 选择属性 -->
        <ElSelect
          v-model="selectedAttribute"
          :validate-event="false"
          class="!w-48"
          filterable
          placeholder="请选择属性"
          value-key="field"
        >
          <ElOption
            v-for="attr in needAttribute"
            :key="attr.field"
            :label="attr.title"
            :value="attr"
          />
        </ElSelect>
        <!-- 设置值 -->
        <div v-if="selectedAttribute?.field" class="flex items-center gap-2">
          <FormItemRender
            v-model="selectedValue"
            @change-dialog-name="changeDialogName"
            :options="selectedAttribute.field === 'selectedSize' ? getSizeOptions : []"
            :constraintType="formatConstraintList?.type || 'string'"
            :constraintList="formatConstraintList && formatConstraintList.constraintList"
            :placeholder="`请设置${formatConstraintList && formatConstraintList.title}`"
            class="!w-48"
          />
          <ElButton type="primary" @click="handleBatchUpdate">一键设置</ElButton>
          <ElButton @click="handleResetBatch">重置</ElButton>
        </div>
      </template>
    </div>
  </div>
  <VxeTable
    ref="tableRef"
    :data="localFormData"
    :edit-config="{
      showIcon: false,
      mode: 'cell',
      trigger: 'click'
    }"
    :cellConfig="{
      height: 120
    }"
    :edit-rules="tableRules"
    :max-height="500"
    :scroll-x="{ enabled: true }"
    :show-overflow="false"
    :valid-config="{ showMessage: true, autoPos: true }"
    @cell-click="handleCellClick"
    class="w-full"
  >
    <VxeColumn fixed="left" type="checkbox" width="40" />
    <VxeColumn fixed="left" title="序号" type="seq" width="60" />
    <VxeColumn field="code" fixed="left" title="SKC编号" width="200" />

    <!-- 动态生成的材料属性列 -->
    <template v-for="column in generateTableColumns" :key="column.field">
      <VxeColumn
        :field="column.field"
        :title="column.title"
        :width="column?.tableWidth || 250"
        :header-class-name="column.isRequired ? 'header-required' : ''"
      >
        <template #default="{ row }: { row: any }">
          <!-- 编辑模式 -->
          <FormItemRender
            v-model="row[column.field]"
            :constraintType="getConstraintType(column.attribute)"
            :constraintList="column.constraintList"
            :options="column.field === 'selectedSize' ? getSizeOptions : []"
            :disabled="!isCellEditable(row, column.field) || isDetail"
            class="table-cell-editor"
            @change-dialog-name="(name:string)=>{
                 row[`${column.field}ItemName`] = name
            }"
            :formData="row"
            :field="column.field"
          />
        </template>
      </VxeColumn>
    </template>
    <VxeColumn field="dataStatusItemName" title="状态" width="100" />
    <VxeColumn :show-overflow="false" title="操作" width="180" fixed="right">
      <template #default="{ row, rowIndex }: { row: any, rowIndex: number }">
        <div class="flex flex-col gap-1" v-if="!isDetail">
          <!-- 第一行操作 -->
          <div class="flex justify-center gap-1">
            <ElButton
              v-if="!row?.code"
              text
              type="danger"
              size="small"
              @click="handleDelSKC(row, rowIndex)"
            >
              <Icon icon="ep:delete" />
              删除
            </ElButton>

            <ElButton text type="primary" size="small" @click="handleCopySKC(row, rowIndex)">
              <Icon icon="ep:copy-document" />
              复制
            </ElButton>
          </div>
        </div>
      </template>
    </VxeColumn>
  </VxeTable>
  <StyleCopyDialog v-model="styleCopyDialogVisible" @submit="handleStyleCopy" />
</template>

<style lang="less" scoped>
// 响应式样式
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    align-items: stretch;

    .el-button {
      justify-content: center;
    }
  }
}

:deep(.el-upload-list--picture-card) {
  .el-upload-list__item {
    width: 40px;
    height: 40px;
  }

  .el-upload--picture-card {
    width: 40px;
    height: 40px;
  }

  .el-icon {
    font-size: 12px;
  }
}

:deep(.col--valid-error .vxe-cell) {
  .el-select__wrapper,
  .el-input__wrapper {
    box-shadow: 0 0 0 1px red inset !important;
  }
}

// 表格单元格样式
:deep(.vxe-table) {
  .vxe-body--column {
    .vxe-cell {
      padding: 4px 8px;

      .table-cell-editor {
        width: 100%;

        .el-input,
        .el-select {
          width: 100%;
        }
      }

      .cell-display {
        display: block;
        width: 100%;
        min-height: 20px;
        line-height: 20px;
        word-break: break-all;
      }
    }
  }

  // 复选框列样式
  .vxe-checkbox--column {
    .vxe-cell {
      padding: 8px;
    }
  }

  // 序号列样式
  .vxe-seq--column {
    .vxe-cell {
      font-weight: 500;
      color: var(--el-text-color-secondary);
    }
  }
}

// 批量操作区域样式
.batch-operations {
  padding: 12px;
  margin-bottom: 16px;
  background-color: var(--el-fill-color-extra-light);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;

  .operation-title {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
}

// 操作按钮样式
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;

  .el-button {
    display: flex;
    align-items: center;
    gap: 4px;

    &:disabled {
      opacity: 0.5;
    }
  }
}

// 表格操作列样式
:deep(.vxe-table .operation-column) {
  .vxe-cell {
    padding: 4px;

    .el-button {
      padding: 4px 8px;
      margin: 1px;
      font-size: 12px;

      .el-icon {
        font-size: 12px;
      }
    }
  }
} // 表格验证错误样式
</style>
