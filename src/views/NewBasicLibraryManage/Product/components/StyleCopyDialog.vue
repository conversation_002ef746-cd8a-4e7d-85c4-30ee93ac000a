<script lang="ts" setup>
import { FormRules } from 'element-plus'
import { getDictByCodeApi } from '@/api/common'

const COPY_TYPE_CONST = {
  PRODUCT_COLOR: 0,
  MATERIAL_INFO: 1,
  MATERIAL_RATIO: 2,
  VELVET_INFO: 3
}

defineOptions({
  name: 'ColorInfoDialog'
})

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', v: { copyProductNumber: string; type: number[] }): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const handleSubmit = async () => {
  const valid = await formRef.value.validate()
  if (valid) {
    emit('submit', formData.value)
    handleClose()
  }
}

const handleClose = () => {
  visible.value = false
}

const formRef = ref()

const formData = ref({
  copyProductNumber: '',
  type: [COPY_TYPE_CONST.PRODUCT_COLOR]
})

const formRules = ref<FormRules<typeof formData.value>>({
  copyProductNumber: [
    {
      required: true,
      message: '请选择产品编号'
    }
  ],
  type: [
    {
      asyncValidator: async (_rule, value, callback) => {
        if (value.indexOf(COPY_TYPE_CONST.PRODUCT_COLOR) < 0) {
          callback(new Error('产品配色为必选项!'))
        } else {
          callback()
        }
      }
    }
  ]
})

watch(visible, () => {
  formData.value = {
    copyProductNumber: '',
    type: [COPY_TYPE_CONST.PRODUCT_COLOR]
  }
})
</script>

<template>
  <Dialog v-model="visible" :before-close="handleClose" append-to-body title="同款复制" width="800">
    <ElForm class="mt-2" ref="formRef" :model="formData" :rules="formRules" @submit="handleSubmit">
      <ElFormItem prop="copyProductNumber" label="产品编码">
        <ApiSelect
          v-model="formData.copyProductNumber"
          :params="{ dictCode: 'API_MINI_PRODUCT' }"
          filterable
          placeholder="请选择产品编码"
          virtualized
          clearable
          :api-config="{
            api: getDictByCodeApi,
            config: {
              label: 'label',
              value: 'value'
            }
          }"
        />
      </ElFormItem>
      <ElFormItem prop="type">
        <template #label><span></span></template>
        <ElCheckboxGroup v-model="formData.type">
          <ElCheckbox label="产品配色" :value="COPY_TYPE_CONST.PRODUCT_COLOR" />
        </ElCheckboxGroup>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(.el-form-item__label) {
  width: 200px;
}
</style>
