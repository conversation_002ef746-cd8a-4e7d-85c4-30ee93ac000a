<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElButton } from 'element-plus'
import { scrollProp } from '@/plugins/vxeTable'
import { VxeGridInstance, VxeGridProps } from 'vxe-table'
import {
  getVersionByBusinessId,
  showVersionDiffByIdList,
  getVersionDetail,
  TableDetailApi
} from '@/api/NewProductLibraryManage/detail'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { VersionAttribute } from '@/views/NewBasicLibraryManage/components/Help/Detail'
import { ContentWrap } from '@/components/ContentWrap'
import { Dialog } from '@/components/Dialog'
import { CuType } from '@/views/NewBasicLibraryManage/components/Help/Status'
import EditProductDialog from './EditProductDialog.vue'
import EditSkuDialog from '../SKU/EditSkuDialog.vue'
import EditSkcDialog from '../SKC/EditSkcDialog.vue'
const enumItemList = ref<TableDetailApi.VersionResp[]>([])
const enumItemLoading = ref(false)
const route = useRoute()
const enumItemTableRef = ref<VxeGridInstance>()
const maxHeight = useTableHeight({ tableRef: enumItemTableRef })
const tableOptions = computed(
  () =>
    ({
      border: true,
      showOverflow: true,
      height: maxHeight.value - 55,
      loading: enumItemLoading.value,
      data: enumItemList.value,
      columns: VersionAttribute,
      checkboxConfig: {
        highlight: true,
        range: true
      },
      ...scrollProp
    } as VxeGridProps)
)
const templateMap = {
  [CuType.Prodcut]: EditProductDialog,
  [CuType.SKU]: EditSkuDialog,
  [CuType.SKC]: EditSkcDialog
}

// 版本详情弹窗状态
const detailDialog = reactive({
  visible: false,
  loading: false,
  currentRow: null as TableDetailApi.VersionResp | null,
  component: null as any,
  versionData: null as any
})
const id = computed(() => {
  return route.query.id as string
})
const typeId = computed(() => {
  return route.query.typeId as string
})
const type = computed(() => {
  return route.query.type as string
})
// 版本对比弹窗状态
const compareDialog = reactive({
  visible: false,
  loading: false,
  selectedVersions: [] as TableDetailApi.VersionResp[],
  compareData: [] as any[],
  compareFields: [] as string[],
  compareValue: [] as string[]
})
// 选中的版本数量
const selectedCount = computed(() => {
  return enumItemTableRef.value?.getCheckboxRecords()?.length || 0
})
// 查询
const fetchEnumItems = async () => {
  if (enumItemLoading.value) return
  enumItemLoading.value = true
  try {
    const [error, result] = await getVersionByBusinessId({
      businessId: id.value,
      typeId: typeId.value
    })
    if (error === null && result) {
      enumItemList.value = result.data || []
    } else {
      ElMessage.error('获取枚举值列表失败')
    }
  } catch (error) {
    console.error('获取枚举值列表出错:', error)
    ElMessage.error('获取枚举值列表出错')
  } finally {
    enumItemLoading.value = false
  }
}
// 版本对比功能
const compareFunc = () => {
  const selectedVersions = enumItemTableRef.value?.getCheckboxRecords() || []

  if (selectedVersions.length < 2) {
    ElMessage.warning('请至少选择2个版本进行对比')
    return
  }
  if (selectedVersions.length > 5) {
    ElMessage.warning('最多只能选择5个版本进行对比')
    return
  }

  compareDialog.selectedVersions = selectedVersions
  compareDialog.visible = true
  getCompareData()
}

// 获取对比数据
const getCompareData = async () => {
  compareDialog.loading = true
  try {
    // 模拟获取对比数据的API调用
    const [error, result] = await showVersionDiffByIdList({
      businessId: id.value,
      typeCategoryId: typeId.value,
      idList: compareDialog.selectedVersions.map((item: any) => item.id)
    })
    if (!error) {
      const { fieldDetailList, fieldValue, filedTitle } = result.data
      compareDialog.compareFields = filedTitle
      compareDialog.compareValue = fieldValue
      compareDialog.compareData = fieldDetailList
      console.log(compareDialog, 'compareDialog')
    }
  } catch (error) {
    console.error('获取对比数据失败:', error)
    ElMessage.error('获取对比数据失败')
  } finally {
    compareDialog.loading = false
  }
}

// 关闭对比弹窗
const handleCloseCompare = () => {
  compareDialog.visible = false
  compareDialog.selectedVersions = []
  compareDialog.compareData = []
}
// 获取字段值
const getFieldValue = (index: number, versionIndex: number) => {
  const key = compareDialog.compareValue?.[index]
  const { diffKeys = [], originalData = {} } = compareDialog.compareData?.[versionIndex] || {}
  if (diffKeys.includes(key)) {
    return `<span style="color:red">${originalData[key]}</span>`
  } else {
    return originalData[key]
  }
}

// 查看版本详情
const handleViewDetail = async (row: TableDetailApi.VersionResp) => {
  detailDialog.currentRow = row
  detailDialog.loading = true
  detailDialog.visible = true

  try {
    // 调用 getVersionDetail 接口获取版本详情
    const [error, result] = await getVersionDetail({
      businessId: id.value,
      typeId: typeId.value,
      versionCode: row.versionCode
    })

    if (!error && result?.data) {
      detailDialog.versionData = result.data
      // 根据数据类型选择对应的弹窗组件
      detailDialog.component = templateMap[type.value] || templateMap[CuType.Prodcut]
    } else {
      ElMessage.error('获取版本详情失败')
      detailDialog.visible = false
    }
  } catch (error) {
    console.error('获取版本详情出错:', error)
    ElMessage.error('获取版本详情出错')
    detailDialog.visible = false
  } finally {
    detailDialog.loading = false
  }
}

// 关闭详情弹窗
const handleCloseDetail = () => {
  detailDialog.visible = false
  detailDialog.currentRow = null
  detailDialog.component = null
  detailDialog.versionData = null
}
onMounted(() => {
  fetchEnumItems()
})
</script>

<template>
  <ContentWrap>
    <div class="operation mb-2 flex items-center justify-between">
      <div class="flex items-center gap-3">
        <ElButton type="primary" @click="compareFunc" :disabled="selectedCount < 2">
          版本对比
        </ElButton>
      </div>
      <div class="text-sm text-gray-400"> 提示：可选择2-5个版本进行对比 </div>
    </div>

    <VxeGrid ref="enumItemTableRef" v-bind="tableOptions">
      <template #operation="{ row }">
        <ElButton type="primary" text size="small" @click="handleViewDetail(row)">
          查看详情
        </ElButton>
      </template>
    </VxeGrid>
  </ContentWrap>

  <!-- 版本对比弹窗 -->
  <Dialog
    v-model="compareDialog.visible"
    title="版本对比"
    width="90%"
    :before-close="handleCloseCompare"
  >
    <div v-loading="compareDialog.loading" class="version-compare">
      <!-- 对比表头 -->
      <div class="compare-header">
        <div class="field-column">字段</div>
        <div
          v-for="version in compareDialog.selectedVersions"
          :key="version.id"
          class="version-column"
        >
          <div class="version-info">
            <div class="version-code">{{ version.versionCode }}</div>
            <div class="version-meta"> {{ version.operator }} · {{ version.dataTime }} </div>
          </div>
        </div>
      </div>

      <!-- 对比内容 -->
      <div class="compare-content">
        <div class="compare-row" v-for="(field, index) in compareDialog.compareFields" :key="field">
          <div class="field-name">{{ field }}</div>
          <div
            v-for="(version, versionIndex) in compareDialog.selectedVersions"
            :key="`${version.id}-${field}`"
            class="field-value"
          >
            <span v-html="getFieldValue(index, versionIndex)"></span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <ElButton @click="handleCloseCompare">关闭</ElButton>
    </template>
  </Dialog>

  <!-- 版本详情弹窗 -->
  <component
    v-if="detailDialog.component && detailDialog.visible"
    :is="detailDialog.component"
    v-model="detailDialog.visible"
    :current-row="detailDialog.versionData"
    :type-id="typeId"
    :type="detailDialog.currentRow?.dataType || CuType.Prodcut"
    :version-id="detailDialog.currentRow?.id"
    :readonly="true"
    @close="handleCloseDetail"
  />
</template>
<style scoped lang="less">
.operation {
  .text-gray-500 {
    color: #6b7280;
  }

  .text-gray-400 {
    color: #9ca3af;
  }
}

.version-compare {
  height: 430px;
  overflow-y: auto;

  .compare-header {
    display: flex;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-bottom: none;

    .field-column {
      width: 150px;
      padding: 12px;
      font-weight: 600;
      background-color: #f1f3f4;
      border-right: 1px solid #e9ecef;
    }

    .version-column {
      padding: 12px;
      text-align: center;
      border-right: 1px solid #e9ecef;
      flex: 1;

      &:last-child {
        border-right: none;
      }

      .version-info {
        .version-code {
          margin-bottom: 4px;
          font-weight: 600;
          color: #1f2937;
        }

        .version-meta {
          font-size: 12px;
          color: #6b7280;
        }
      }
    }
  }

  .compare-content {
    border: 1px solid #e9ecef;

    .compare-row {
      display: flex;
      border-bottom: 1px solid #e9ecef;

      &:last-child {
        border-bottom: none;
      }

      &:nth-child(even) {
        background-color: #f8f9fa;
      }

      .field-name {
        width: 150px;
        padding: 12px;
        font-weight: 500;
        background-color: #f1f3f4;
        border-right: 1px solid #e9ecef;
      }

      .field-value {
        padding: 12px;
        word-break: break-word;
        border-right: 1px solid #e9ecef;
        flex: 1;

        &:last-child {
          border-right: none;
        }
      }
    }
  }
}
</style>
