<script lang="ts" setup>
import { ref } from 'vue'
import type { RecordElement } from '@/api/notice/types'
import { PublicNoticeAggregateDTO } from '@/api/notice/types'
import { getQMSDict } from '@/utils'
import { useValidator } from '@/hooks/web/useValidator'
import { Dialog } from '@/components/Dialog'
import { NoticeInfo } from './index'
import type { FormInstance } from 'element-plus'
import { ElCarousel, ElCarouselItem, ElImage, ElMessage } from 'element-plus'
import { validateEmail } from '@/utils/validate'
import type { CurrentType } from '@/utils/enum'
import { PageTypeEnum } from '@/utils/enum'

const { required } = useValidator()
interface Props {
  data: any
  currentType: CurrentType
}
interface VendorArea {
  label: string
  value: string
}
const emit = defineEmits(['update', 'setUploadLoading'])
const loading = ref(false)
const props = withDefaults(defineProps<Props>(), {
  data: () => ({} as PublicNoticeAggregateDTO)
})
const ruleForm = ref(props.data)

// const ruleForm = computed({
//   get: () => props.data,
//   set: (val) => {
//     emit('update', val)
//   }
// })

const NotMassProduction = computed(() => {
  return ruleForm.value.mouldHasProductDict === '0'
})
const MassProduction = computed(() => {
  return ruleForm.value.mouldHasProductDict === '1'
})
const ruleFormRef = ref<FormInstance>()
const PRODUCT_ZH_CATEGORY = ref(getQMSDict('PRODUCT_ZH_CATEGORY'))
const STYLE_BODY = ref(getQMSDict('STYLE_BODY'))
const SHOES_TREE = ref(getQMSDict('SHOES_TREE'))
const MODEL = ref(getQMSDict('MODEL'))
const vendorAreaList = ref<Array<VendorArea>>([])

// const rules = ref({
//   vendorAreaCode: [required()],
//   'attachment.mainPic': [required()],
//   companyName: [required()],
//   contactName: [required()],
//   contactPhone: [required()],
//   contactEmail: [required(), { validator: validateEmail, trigger: 'change' }],
//   moq: [required()],
//   sizeStart: [required()],
//   sizeEnd: [required()],
//   productZhCategoryDict: [required()],
//   productName: [required()],
//   mouldTypeDict: [required()],
//   bottomCycle: [required()]
// })
const dialogVisible = ref(false)
const NoticeRef = ref()
const handleAdd = async () => {
  const datas: RecordElement[] = await NoticeRef.value.getSelections()
  if (datas?.length !== 1) return ElMessage.warning('请勾选一条数据')
  const { code: searchCode, noticeTopic, id: publicNoticeId } = datas[0]
  emit('update', { searchCode, noticeTopic, publicNoticeId })
  dialogVisible.value = false
}
const handleSuccess = ({ datas, uid }, list) => {
  if (datas && datas.url) {
    const { objectName, url } = datas
    list.push({ uid, url, objectName })
  }
}

const handleRemove = ({ uploadFile }, list) => {
  // 删除新增的，uid匹配index
  const index = list?.findIndex((item) => item.uid === uploadFile.uid)
  index !== -1 && list?.splice(index, 1)
}
const handleFileRemove = ({ uploadFiles }) => {
  let field = uploadFiles?.id ? 'id' : 'uid'
  const index = ruleForm.value.attachment.referFiles.findIndex(
    (item) => item[field] === uploadFiles[field]
  )
  index !== -1 && ruleForm.value.attachment.referFiles.splice(index, 1)
}
const handleFileSuccess = (val) => {
  if (val) {
    ruleForm.value.attachment.referFiles.push(val)
  }
}
const handleChangeAreaCode = (val) => {
  ruleForm.value.vendorAreaName = vendorAreaList.value.find(
    (ele: VendorArea) => ele.value === val
  )?.label
}
const viewImgDialogVisible = ref(false)
const currentIndex = ref(0)
const handleImgView = (idx) => {
  currentIndex.value = idx
  viewImgDialogVisible.value = true
}
const setLoading = (val) => {
  loading.value = val
  emit('setUploadLoading', val)
}
defineExpose({
  ruleFormRef
})
</script>
<template>
  <ElImageViewer
    v-if="viewImgDialogVisible"
    :initial-index="currentIndex"
    :url-list="ruleForm.imgList?.map((v) => v.url)"
    @close="viewImgDialogVisible = false"
  />
  <Dialog v-model="dialogVisible" title="公告信息" width="90%">
    <NoticeInfo ref="NoticeRef" />
    <template #footer>
      <ElButton @click="dialogVisible = false">关闭</ElButton>
      <ElButton type="primary" @click="handleAdd">添加</ElButton>
    </template>
  </Dialog>
  <ElForm
    ref="ruleFormRef"
    :model="ruleForm"
    center
    label-width="140px"
    scroll-to-error
    @submit.prevent
    disabled
  >
    <el-row>
      <ElDivider content-position="left">供应商信息</ElDivider>
      <ElCol :span="12">
        <ElFormItem label="公司名称" prop="companyName">
          <ElInput
            v-model="ruleForm.companyName"
            :disabled="currentType !== PageTypeEnum.ADD"
            maxlength="100"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="联系人" prop="contactName">
          <ElInput v-model="ruleForm.contactName" :disabled="currentType !== PageTypeEnum.ADD" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="联系电话" prop="contactPhone">
          <ElInput v-model="ruleForm.contactPhone" :disabled="currentType !== PageTypeEnum.ADD" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="邮箱" prop="contactEmail">
          <ElInput v-model="ruleForm.contactEmail" :disabled="currentType !== PageTypeEnum.ADD" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="区域" prop="vendorAreaCode">
          <Selection
            class="w-full"
            v-model="ruleForm.vendorAreaCode"
            :clearable="false"
            :disabled="currentType !== PageTypeEnum.ADD"
            api-key="AREA_CODE"
            filterable
            @change="handleChangeAreaCode"
            @response-data="(val) => (vendorAreaList = val)"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="联系地址">
          <ElInput
            v-model="ruleForm.contactAddress"
            :disabled="currentType !== PageTypeEnum.ADD"
            maxlength="100"
          />
        </ElFormItem>
      </ElCol>
      <ElDivider content-position="left">产品信息</ElDivider>

      <ElCol v-if="currentType === PageTypeEnum.ADD" :span="24">
        <ElFormItem label="公告信息">
          <div style="color: #409eff">
            <Icon icon="ep:search" @click="dialogVisible = true" />
            <span class="ml-4 mr-4">{{ ruleForm.searchCode }}</span> {{ ruleForm.noticeTopic }}</div
          >
        </ElFormItem>
      </ElCol>
      <ElCol v-else :span="24">
        <ElFormItem label="图片信息">
          <ElCarousel arrow="always" class="w-4/5" indicator-position="none">
            <ElCarouselItem v-for="(item, idx) in ruleForm.imgList" :key="idx">
              <div class="w-full flex justify-center">
                <ElImage
                  :src="item.url"
                  hide-on-click-modal
                  :title="item.bannerSourceLabel"
                  class="w-[500px] h-[320px]"
                  fit="contain"
                  @click="handleImgView(idx)"
                />
              </div>
            </ElCarouselItem>
          </ElCarousel>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="产品名称" prop="productName">
          <ElInput
            v-model="ruleForm.productName"
            maxlength="100"
            @input="() => emit('update', ruleForm)"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="产品大类" prop="productZhCategoryDict">
          <ElRadioGroup
            v-model="ruleForm.productZhCategoryDict"
            @change="() => emit('update', ruleForm)"
          >
            <ElRadio
              v-for="(item, index) in PRODUCT_ZH_CATEGORY"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElRadioGroup>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="产品分类">
          <ElInput v-model="ruleForm.productClassify" maxlength="100" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="风格">
          <ElCheckboxGroup v-model="ruleForm.styleBody">
            <ElCheckbox
              v-for="(item, index) in STYLE_BODY"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElCheckboxGroup>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="主要材质">
          <ElInput v-model="ruleForm.mainMaterial" maxlength="100" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="楦型">
          <ElRadioGroup v-model="ruleForm.shoesTreeDict">
            <ElRadio
              v-for="(item, index) in SHOES_TREE"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElRadioGroup>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="型体">
          <ElCheckboxGroup v-model="ruleForm.model">
            <ElCheckbox
              v-for="(item, index) in MODEL"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElCheckboxGroup>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="是否有底楦" prop="containBottomDict">
          <ElRadioGroup v-model="ruleForm.containBottomDict">
            <ElRadio
              v-for="(item, index) in getQMSDict('YES_NO')"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElRadioGroup>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="模具是否量产">
          <ElRadioGroup v-model="ruleForm.mouldHasProductDict">
            <ElRadio
              v-for="(item, index) in getQMSDict('YES_NO')"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElRadioGroup>
        </ElFormItem>
      </ElCol>
      <ElCol v-if="NotMassProduction" :span="12">
        <ElFormItem label="大底开模周期（天）" label-width="180px" prop="bottomCycle">
          <ElInputNumber
            v-model="ruleForm.bottomCycle"
            :controls="false"
            :max="99999999"
            :min="1"
            :precision="0"
          />
        </ElFormItem>
      </ElCol>

      <ElCol v-if="MassProduction" :span="12">
        <ElFormItem label="模具类型" prop="mouldTypeDict">
          <ElRadioGroup v-model="ruleForm.mouldTypeDict">
            <ElRadio
              v-for="(item, index) in getQMSDict('MOULD_TYPE')"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElRadioGroup>
        </ElFormItem>
      </ElCol>

      <ElCol v-if="MassProduction" :span="24" class="!flex flex-nowrap">
        <ElFormItem label="尺码段" prop="sizeStart">
          <ElInput v-model="ruleForm.sizeStart" maxlength="10" style="width: 150px" />
        </ElFormItem>
        <ElFormItem label-width="32" label="~" class="const-range-max-form-item" prop="sizeEnd">
          <ElInput v-model="ruleForm.sizeEnd" maxlength="10" style="width: 150px" />
        </ElFormItem>
        <ElFormItem label="包含半码" label-width="80px">
          <ElRadioGroup v-model="ruleForm.containHalfDict" class="ml-4">
            <ElRadio
              v-for="(item, index) in getQMSDict('YES_NO')"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElRadioGroup>
        </ElFormItem>
      </ElCol>
      <ElCol v-if="MassProduction" :span="12">
        <ElFormItem label="MOQ" prop="moq">
          <ElInputNumber
            v-model="ruleForm.moq"
            :controls="false"
            :max="99999999"
            :min="1"
            :precision="0"
            class="w-full"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="24" class="!flex flex-nowrap">
        <ElFormItem label="成本区间" prop="costStart">
          <ElInputNumber
            v-model="ruleForm.costStart"
            :controls="false"
            :min="0.01"
            :precision="2"
          />
        </ElFormItem>
        <ElFormItem label-width="32" label="~" class="const-range-max-form-item" prop="costEnd">
          <ElInputNumber v-model="ruleForm.costEnd" :controls="false" :min="0.01" :precision="2" />
        </ElFormItem>
        <ElFormItem label="包含税" label-width="80">
          <ElRadioGroup v-model="ruleForm.containTaxDict">
            <ElRadio
              v-for="(item, index) in getQMSDict('YES_NO')"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElRadioGroup>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="已出口地区">
          <Selection
            v-model="ruleForm.hasSellCountryDict"
            api-key="EXPORT_AREA"
            filterable
            multiple
            class="w-full"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="已售卖品牌">
          <ElInput v-model="ruleForm.hasSellBrand" maxlength="100" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="现有配色">
          <ElInput v-model="ruleForm.containColor" maxlength="100" />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="生产周期">
          <ElInputNumber
            v-model="ruleForm.lifeCycle"
            :controls="false"
            :max="99999999"
            :min="1"
            :precision="0"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem label="产品卖点">
          <ElInput
            v-model="ruleForm.remark.productPoint"
            :autosize="{ minRows: 1 }"
            :maxlength="1000"
            show-word-limit
            type="textarea"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem label="特殊功能说明">
          <ElInput
            v-model="ruleForm.remark.specialRemark"
            :autosize="{ minRows: 1 }"
            :maxlength="1000"
            show-word-limit
            type="textarea"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem label="推款说明">
          <ElInput
            v-model="ruleForm.remark.pushRemark"
            :autosize="{ minRows: 1 }"
            :maxlength="1000"
            show-word-limit
            type="textarea"
          />
        </ElFormItem>
      </ElCol>

      <ElRow v-if="currentType === PageTypeEnum.ADD">
        <ElCol :span="8">
          <ElFormItem label="款式主图" prop="attachment.mainPic">
            <OssImage
              ref="uploadMainPicRef"
              v-model="ruleForm.attachment.mainPic"
              :file-list="ruleForm.attachment.mainPic"
              :limit="1"
              :multiple="false"
              @remove="(e) => handleRemove(e, ruleForm.attachment.mainPic)"
              @success="(e) => handleSuccess(e, ruleForm.attachment.mainPic)"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem label="大底图" prop="attachment.bottomPic">
            <OssImage
              ref="uploadBottomPicRef"
              v-model="ruleForm.attachment.bottomPic"
              :file-list="ruleForm.attachment.bottomPic"
              :limit="10"
              :multiple="true"
              @remove="(e) => handleRemove(e, ruleForm.attachment.bottomPic)"
              @success="(e) => handleSuccess(e, ruleForm.attachment.bottomPic)"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem label="正面图">
            <OssImage
              v-model="ruleForm.attachment.frontPic"
              :file-list="ruleForm.attachment.frontPic"
              :limit="10"
              :multiple="true"
              @remove="(e) => handleRemove(e, ruleForm.attachment.frontPic)"
              @success="(e) => handleSuccess(e, ruleForm.attachment.frontPic)"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem label="侧面图">
            <OssImage
              v-model="ruleForm.attachment.sidePics"
              :file-list="ruleForm.attachment.sidePics"
              :limit="10"
              @remove="(e) => handleRemove(e, ruleForm.attachment.sidePics)"
              @success="(e) => handleSuccess(e, ruleForm.attachment.sidePics)"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem label="配色图">
            <OssImage
              v-model="ruleForm.attachment.colorPics"
              :file-list="ruleForm.attachment.colorPics"
              :limit="30"
              @remove="(e) => handleRemove(e, ruleForm.attachment.colorPics)"
              @success="(e) => handleSuccess(e, ruleForm.attachment.colorPics)"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem label="其他图片">
            <OssImage
              v-model="ruleForm.attachment.otherPics"
              :file-list="ruleForm.attachment.otherPics"
              :limit="30"
              @remove="(e) => handleRemove(e, ruleForm.attachment.otherPics)"
              @success="(e) => handleSuccess(e, ruleForm.attachment.otherPics)"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElCol v-if="currentType !== PageTypeEnum.ADD" :span="12">
        <ElFormItem label="样品寄出日期">
          <ElInput v-model="ruleForm.sendTime" disabled />
        </ElFormItem>
      </ElCol>
      <ElCol v-if="currentType !== PageTypeEnum.ADD" :span="12">
        <ElFormItem label="推款时间">
          <ElInput v-model="ruleForm.createTime" disabled />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElForm :model="ruleForm" label-width="140px">
          <ElFormItem label="参考文件">
            <UploadFile
              :disabled="
                !ruleForm.attachment.referFiles ||
                (ruleForm.attachment.referFiles && !ruleForm.attachment.referFiles.length)
              "
              v-model="ruleForm.attachment.referFiles"
              :file-list="ruleForm.attachment.referFiles"
              :limit="20"
              :size="500"
              accept="*"
              @success="handleFileSuccess"
              @set-loading="setLoading"
              @handle-file-remove="handleFileRemove"
            />
          </ElFormItem>
        </ElForm>
      </ElCol>
    </el-row>
  </ElForm>
</template>
<style lang="less" scoped>
:deep(.const-range-max-form-item .el-form-item__label::before) {
  content: none !important;
}
</style>
