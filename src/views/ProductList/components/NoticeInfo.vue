<script setup lang="ts">
import { Search } from '@/components/Search'
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { getNoticeListApi } from '@/api/notice'
import { getListBrandApi } from '@/api/common'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
type ParamsType = {
  states: string[]
  size: number
  current: number
}
const formParams = reactive<ParamsType>({ states: ['3'], size: 1000, current: 1 })

const { register, tableObject, methods } = useTable({
  getListApi: getNoticeListApi,
  noPaging: true
})
const { getList, getSelections } = methods
onMounted(() => {
  getList(formParams)
})
const loading = ref(false)
const crudSchemas = reactive<CrudSchema[]>([
  {
    field: '#',
    type: 'checkbox',
    label: '',
    width: 50
  },
  {
    type: 'seq',
    label: '序号',
    width: 50
  },
  {
    field: 'code',
    label: '公告编号',
    minWidth: 180
  },
  {
    field: 'brandName',
    label: '品牌',
    'min-width': 90,
    search: {
      field: 'brandIds',
      show: true,
      component: 'Select',
      index: 3,
      api: getListBrandApi,
      value: [],
      componentProps: {
        multiple: true,
        filterable: true,
        optionsAlias: {
          valueField: 'id',
          labelField: 'brandName'
        }
      }
    }
  },

  {
    field: 'seekingStage_zh',
    label: '公告阶段',
    'min-width': 100
  },
  {
    field: 'noticeCategoryDict_zh',
    label: '公告分类',
    'min-width': 100
  },
  {
    field: 'noticeTopic',
    label: '公告主题',
    minWidth: 120,
    colProps: { span: 8 },
    search: {
      show: true,
      index: 0,
      componentProps: {
        placeholder: '输入主题信息，支持模糊搜索'
      }
    }
  },
  {
    field: 'developSeason_zh',
    label: '开发季节',
    minWidth: 80,
    colProps: { span: 8 },
    search: {
      field: 'developSeasonList',
      show: true,
      component: 'Select',
      index: 2,
      dictName: 'DEVELOP_SEASON',
      componentProps: {
        filterable: true,
        multiple: true,
        collapseTags: true
      }
    }
  },
  {
    field: 'productTypeList',
    label: '产品类别',
    'min-width': 100,
    formatter: ({ row }) => {
      let templateString = ``
      row.productType?.map((v) => {
        templateString += `${v.nameArray}
    
    `
      })
      return templateString
    }
  }
])
const { allSchemas } = useCrudSchemas(crudSchemas)
defineExpose({
  getSelections
})
</script>
<template>
  <Search
    ref="searchRef"
    :colNum="3"
    :schema="allSchemas.searchSchema"
    :loading="loading"
    @search="(e) => getList(Object.assign({}, e, formParams))"
    @reset="(e) => getList(Object.assign({}, e, formParams))"
    :expand="false"
  />
  <Table
    :columns="allSchemas.tableColumns"
    :data="tableObject.tableList"
    :loading="tableObject.loading"
    @register="register"
  />
</template>
