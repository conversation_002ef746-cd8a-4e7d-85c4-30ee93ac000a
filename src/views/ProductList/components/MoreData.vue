<script lang="ts" setup>
import { ref } from 'vue'
import { PublicNoticeAggregateDTO } from '@/api/notice/types'
import { getQMSDict } from '@/utils'
import { useValidator } from '@/hooks/web/useValidator'
import type { FormInstance } from 'element-plus'

const { required } = useValidator()

interface Props {
  data: any
  currentType: 'view' | 'add' | 'first' | 'comment'
}

const emit = defineEmits(['update'])

const props = withDefaults(defineProps<Props>(), {
  data: () =>
    ({
      mainPic: [{}],
      referPics: [{}],
      mainPic_url: [{}],
      referPics_url: [{}]
    } as PublicNoticeAggregateDTO)
})

const ruleForm: any = computed({
  get: () => props.data,
  set: (val) => {
    emit('update', val)
  }
})
const ruleFormRef = ref<FormInstance>()
const rules = ref({
  area: [required()],
  mainPic: [required()],
  referPics: [required()]
})

defineExpose({
  ruleFormRef
})
</script>
<template>
  <ElForm
    ref="ruleFormRef"
    :disabled="currentType !== 'add'"
    :model="ruleForm"
    :rules="rules"
    center
    label-width="140px"
    @submit.prevent
  >
    <el-row>
      <el-divider content-position="left">补充信息</el-divider>
      <el-col :span="12">
        <ElFormItem label="模具类型">
          <ElRadioGroup v-model="ruleForm.mouldTypeDict" class="ml-4">
            <ElRadio
              v-for="(item, index) in getQMSDict('MOULD_TYPE')"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElRadioGroup>
        </ElFormItem>
      </el-col>
      <el-col :span="12">
        <ElFormItem label="模具是否量产">
          <ElRadioGroup v-model="ruleForm.mouldHasProductDict">
            <ElRadio
              v-for="(item, index) in getQMSDict('YES_NO')"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElRadioGroup>
        </ElFormItem>
      </el-col>
      <ElCol :span="12">
        <ElFormItem label="生产周期">
          <ElInputNumber
            v-model="ruleForm.lifeCycle"
            :controls="false"
            :max="99999999"
            :min="1"
            :precision="0"
          />
        </ElFormItem>
      </ElCol>
      <ElCol v-if="currentType !== 'add'" :span="12">
        <ElFormItem label="最快出货日期">
          <ElInput v-model="ruleForm.date" disabled />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="大底开模周期（天）" label-width="180px">
          <ElInputNumber
            v-model="ruleForm.bottomCycle"
            :controls="false"
            :max="99999999"
            :min="1"
            :precision="0"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem label="产品卖点">
          <ElInput
            v-model="ruleForm.remark.productPoint"
            :autosize="{ minRows: 1 }"
            :maxlength="1000"
            show-word-limit
            type="textarea"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem label="特殊功能说明">
          <ElInput
            v-model="ruleForm.remark.specialRemark"
            :autosize="{ minRows: 1 }"
            :maxlength="1000"
            show-word-limit
            type="textarea"
          />
        </ElFormItem>
      </ElCol>
    </el-row>
  </ElForm>
</template>
<style lang="less" scoped>
.full-width {
  width: 100%;
}
</style>
