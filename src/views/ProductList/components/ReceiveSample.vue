<template>
  <div>
    <ElDialog
      title="操作确认"
      :model-value="modelValue"
      @close="closed"
      center
      width="90%"
      :close-on-click-modal="false"
    >
      <Table :columns="tableColumns" :data="data">
        <template #chooseFlag="{ row }">
          <ElRadioGroup v-model="row.acceptDict">
            <ElRadio
              v-for="(item, index) in getQMSDict('YES_NO')"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </ElRadioGroup>
        </template>
        <template #acceptTime="{ row }">
          <ElDatePicker
            v-model="row.acceptTime"
            type="date"
            valueFormat="YYYY-MM-DD"
            :disabled-date="(e) => disabledDate(e, row)"
          />
        </template>
        <template #sampleCount="{ row }">
          <ElInput v-model="row.sampleCount" />
        </template>
        <template #remark="{ row }">
          <ElInput
            v-model="row.acceptRemark"
            :autosize="{ minRows: 1 }"
            :maxlength="500"
            show-word-limit
            type="textarea"
          />
        </template>
        <template #changeState="{ row }">
          <Selection :selectOptions="handleSelectList()" v-model="row.changeState" />
        </template>
      </Table>
      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="closed">取消</ElButton>
          <ElButton type="primary" @click="onConfirm" :loading="loading">确认</ElButton>
        </span>
      </template>
    </ElDialog>
  </div>
</template>
<script setup lang="ts">
import { PushAggregateResponse, ResumeItemDTO } from '@/api/notice/types'
import { Table } from '@/components/Table'
import { acceptApi, resumeApi } from '@/api/notice'
import { getQMSDict } from '@/utils'
import { TableColumn } from '@/types/table'
import { PropType } from 'vue'
import { useRoute } from 'vue-router'
import { pick } from 'lodash-es'
import { ElDialog, ElDatePicker, ElButton, ElInput, ElMessage } from 'element-plus'
import { CurrentType, STATUSENUM } from '@/utils/enum'
const emits = defineEmits(['update:modelValue', 'update'])
type AcceptRequest = Pick<PushAggregateResponse, 'acceptTime' | 'id' | 'sampleCount'>[]

const currentType: CurrentType = useRoute().params.pageType as CurrentType

const props = defineProps({
  data: {
    type: Array as PropType<PushAggregateResponse[]>,
    default: () => []
  },
  tableColumns: {
    type: Array as PropType<TableColumn[]>,
    default: () => []
  },
  modelValue: {
    type: Boolean,
    default: () => false
  },
  sampleType: {
    type: String,
    default: ''
  }
})

const handleSelectList = () => {
  const list = getQMSDict('PUSH_STYLE_STATE')
  return list?.filter((v) => ['1', '4'].includes(v.value as string))
}
const disabledDate = (time: Date, row) => {
  return time.getTime() < new Date(row.sendTime).getTime() - 1 * 24 * 3600 * 1000
}

const closed = () => {
  emits('update:modelValue', false)
  !currentType && emits('update')
}
const loading = ref(false)
const onConfirm = async () => {
  try {
    // if (!valid) return
    loading.value = true
    if (props.sampleType === STATUSENUM.SAMPLE) {
      let list: AcceptRequest = []
      list = props.data?.map((ele: PushAggregateResponse) => {
        return pick(ele, ['acceptTime', 'id', 'sampleCount', 'acceptRemark', 'acceptDict'])
      })
      if (
        list.some(
          (ele: PushAggregateResponse) => !ele.acceptTime || !ele.sampleCount || !ele.acceptDict
        )
      )
        return ElMessage.warning('请填写接收日期、样品数量、是否选中')
      await acceptApi({ list })
    } else {
      let list: ResumeItemDTO[] = []
      list = props.data?.map((ele: PushAggregateResponse) => {
        return {
          resumePrimary: ele.changeState === '1',
          resumeJudge: ele.changeState === '4',
          id: ele.id
        }
      })
      if (props.data?.some((ele: PushAggregateResponse) => !ele.changeState))
        return ElMessage.warning('请填写变更状态')
      await resumeApi({ list })
    }
    emits('update:modelValue', false)
    emits('update')
  } finally {
    loading.value = false
  }
}
</script>
