<script setup lang="ts">
import { ref } from 'vue'
import { propTypes } from '@/utils/propTypes'
import { returnApi } from '@/api/notice/index'
import { useValidator } from '@/hooks/web/useValidator'
import { ReturnRequest } from '@/api/notice/types'
import { closeCurrentTag } from '@/utils/routerHelper'
import { useRouter } from 'vue-router'
import type { FormInstance } from 'element-plus'
import { ElDialog, ElButton, ElInput, ElForm, ElFormItem } from 'element-plus'

const { push } = useRouter()
const { required } = useValidator()
const props = defineProps({
  id: propTypes.number.def(),
  modelValue: propTypes.bool.def(false)
})
const emits = defineEmits(['update:modelValue'])

const ruleFormRef = ref<FormInstance>()
const loading = ref(false)
const ruleForm = reactive<ReturnRequest>({
  returnReasonDict: '',
  returnReasonRemark: ''
})

const rules = ref({
  returnReasonDict: [required()],
  returnReasonRemark: [required()]
})

const submitParams = computed(() => {
  return Object.assign({}, ruleForm, { id: props.id })
})
const onConfirm = () => {
  ruleFormRef.value?.validate(async (valid) => {
    try {
      if (!valid) return
      loading.value = true
      await returnApi(submitParams.value)
      emits('update:modelValue', false)
      closeCurrentTag()
      push({
        name: 'ProductListPage'
      })
    } finally {
      loading.value = false
    }
  })
}

const closed = () => {
  ruleFormRef.value?.clearValidate()
  ruleFormRef.value?.resetFields()
  emits('update:modelValue', false)
}
</script>
<template>
  <ElDialog
    title="操作确认"
    :model-value="modelValue"
    @close="closed"
    center
    width="700px"
    :close-on-click-modal="false"
  >
    <ElForm :model="ruleForm" label-width="150px" ref="ruleFormRef" :rules="rules" center>
      <ElFormItem label="退回原因" prop="returnReasonDict">
        <Selection api-key="RETURN_REASON" v-model="ruleForm.returnReasonDict" filterable />
      </ElFormItem>

      <ElFormItem label="退回说明" prop="returnReasonRemark">
        <ElInput
          v-model="ruleForm.returnReasonRemark"
          show-word-limit
          :autosize="{ minRows: 1 }"
          :maxlength="1000"
          type="textarea"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="closed">取消</ElButton>
        <ElButton type="primary" @click="onConfirm" :loading="loading">确认</ElButton>
      </span>
    </template>
  </ElDialog>
</template>
<style lang="less" scoped></style>
