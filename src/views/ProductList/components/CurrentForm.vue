<script lang="ts" setup>
import { ref } from 'vue'
import { propTypes } from '@/utils/propTypes'
import {
  commentPassApi,
  commentRejectApi,
  getBrandAddressApi,
  primaryApi
} from '@/api/notice/index'
import { useValidator } from '@/hooks/web/useValidator'
import { UnSelectReasonEnum } from './columns'

import {
  JudgePassRequest,
  JudgeRejectRequest,
  ProductTypeDTO,
  PushAggregateResponse
} from '@/api/notice/types'
import { closeCurrentTag } from '@/utils/routerHelper'
import { getCategaryApi } from '@/api/common'
import { omit } from 'lodash-es'
import { useRouter } from 'vue-router'
import { PageTypeEnum } from '@/utils/enum'
import {
  ElButton,
  ElCascader,
  ElCol,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElRadio,
  ElRadioGroup,
  ElRow,
  FormInstance
} from 'element-plus'
import { BrandDTO } from '@/api/common/type'
import { getQMSDict } from '@/utils'
import { ConfigAcceptAddressCityDictDTO } from '@/api/notice/types'
const { push } = useRouter()
const { required } = useValidator()
const props = defineProps({
  params: propTypes.object.def({}),
  modelValue: propTypes.bool.def(false),
  currentType: propTypes.string.def(''),
  isPass: propTypes.bool.def(true)
})
const emits = defineEmits(['update:modelValue'])

const ruleFormRef = ref<FormInstance>()
const loading = ref(false)
const ruleForm = reactive<PushAggregateResponse>({
  planer: [],
  designer: [],
  unSelectDescription: ''
})

watch(
  () => props.modelValue,
  (val: boolean) => {
    if (val) {
      ruleForm.primaryResult = props.isPass ? '1' : '0'
      const { brandId, planer = [], designer = [] } = props.params
      Object.assign(ruleForm, { planer, brandId, designer })
      brandId && handlePushStyleAddress(brandId)
    }
  },
  {
    immediate: true
  }
)

const dataMethod = (val) => {
  return val.map((v) => {
    return {
      userName: v.nameEn,
      email: v.emailAddress,
      feishuUserId: v.userId,
      disabled: v.userId === ''
    }
  })
}
const firstSubmitParams = computed<PushAggregateResponse>(() => {
  const { id, vendorId, vendorName, productZhCategoryDict, vendorAreaCode } = props.params
  return {
    list: [
      Object.assign({}, ruleForm, {
        id,
        vendorId,
        vendorName,
        productZhCategoryDict,
        vendorAreaCode
      })
    ]
  }
})
const commentPassParams: JudgePassRequest = computed(() => {
  const { id, vendorId, vendorName } = props.params
  const { productType } = ruleForm

  let obj: ProductTypeDTO = {}
  const productTypeIdArray: string[] = []
  const productTypeNameArray: string[] = []

  Array.isArray(productType) &&
    productType.map((list) => {
      const arr = list.split('&')
      productTypeIdArray.push(arr[0])
      productTypeNameArray.push(arr[1])
    })
  Object.assign(obj, {
    productTypeIdArray,
    productTypeNameArray,
    productTypeLastId: productTypeIdArray[productTypeIdArray.length - 1]
  })
  return Object.assign({}, omit(ruleForm, 'productType'), {
    id,
    vendorId,
    vendorName,
    ...obj
  })
})
const commentRejectedParams = computed<JudgeRejectRequest>(() => {
  const { id } = props.params
  const { unSelectDescription, unSelectReasonDict } = ruleForm
  return Object.assign({}, { unSelectDescription, unSelectReasonDict, id })
})
const rules = ref({
  primaryResult: [required()],
  brandId: [required()],
  planer: [required()],
  designer: [required()],
  pushStyleAddressId: [required()],
  unSelectReasonDict: [required()],
  publishDevelop: [required()],
  productType: [required()],
  publishSeasonDict: [required()]
})
const categoryTree = ref([])
const categorProps = ref({})
const handleTree = (data: any) => {
  if (!Array.isArray(data)) return []
  data.map((item) => {
    const { categoryName, id, sonCategory = [] } = item
    Object.assign(item, {
      children: sonCategory,
      label: categoryName,
      value: `${id}&${categoryName}`
    })
    handleTree(item.children)
  })
}
const getCategoryTree = async () => {
  const { datas } = await getCategaryApi()
  handleTree(datas)
  categoryTree.value = datas
}

const unSelectReasonList = computed(() => {
  const list = getQMSDict('UN_SELECT_REASON')
  const firstUnSelectList = list.filter((v) =>
    [UnSelectReasonEnum.Need, UnSelectReasonEnum.Style, UnSelectReasonEnum.SoleMismatch].includes(
      v.value
    )
  )
  const commentUnselectList = list.filter(
    (v) =>
      ![UnSelectReasonEnum.Cost, UnSelectReasonEnum.Mould, UnSelectReasonEnum.Moq].includes(v.value)
  )
  return props.currentType === PageTypeEnum.FIRST ? firstUnSelectList : commentUnselectList
})

getCategoryTree()
const onConfirm = () => {
  ruleFormRef.value?.validate(async (valid) => {
    try {
      if (!valid) return
      loading.value = true
      if (props.currentType === PageTypeEnum.FIRST) {
        await primaryApi(firstSubmitParams.value)
      }
      if (props.currentType === PageTypeEnum.COMMENT) {
        props.isPass
          ? await commentPassApi(commentPassParams.value)
          : await commentRejectApi(commentRejectedParams.value)
      }
      emits('update:modelValue', false)
      closeCurrentTag()
      push({
        name: 'ProductListPage'
      })
    } finally {
      loading.value = false
    }
  })
}
const brandOptions = ref<Array<BrandDTO>>([])
const changeBrand = (val) => {
  ruleForm.brandName = brandOptions.value?.find((v) => v.id === val)?.brandName
  ruleForm.pushStyleAddressId = undefined
  handlePushStyleAddress(val)
}
const stylePushAddressList = ref<Array<ConfigAcceptAddressCityDictDTO>>([])
const handlePushStyleAddress = async (val) => {
  const { datas } = await getBrandAddressApi(val)
  stylePushAddressList.value = datas
}
const closed = () => {
  ruleFormRef.value?.clearValidate()
  ruleFormRef.value?.resetFields()
  emits('update:modelValue', false)
}
</script>
<template>
  <ElDialog
    :close-on-click-modal="false"
    :model-value="modelValue"
    center
    title="操作确认"
    width="80%"
    @close="closed"
  >
    <ElForm ref="ruleFormRef" :model="ruleForm" :rules="rules" center label-width="150px">
      <ElRow>
        <ElCol :span="12">
          <ElFormItem
            :label="currentType === PageTypeEnum.FIRST ? '初选通过' : '评审通过'"
            prop="primaryResult"
          >
            <ElRadioGroup v-model="ruleForm.primaryResult" disabled>
              <ElRadio
                v-for="(item, index) in getQMSDict('YES_NO')"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </ElRadioGroup>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <!-- 通过 -->
      <div v-if="props.isPass">
        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="归属品牌" prop="brandId">
              <Selection
                class="w-full"
                v-model="ruleForm.brandId"
                api-key="brandList"
                @change="changeBrand"
                @response-data="(e) => (brandOptions = e)"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品企划" prop="planer">
              <Selection
                class="w-full"
                v-model="ruleForm.planer"
                :dataMethod="dataMethod"
                api-key="getProductCenterListApi"
                filterable
                multiple
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品设计师" prop="designer">
              <Selection
                class="w-full"
                v-model="ruleForm.designer"
                :dataMethod="dataMethod"
                api-key="getProductCenterListApi"
                filterable
                multiple
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow v-if="currentType === PageTypeEnum.FIRST">
          <ElCol :span="12">
            <ElFormItem label="初选说明" prop="primaryRemark">
              <ElInput
                v-model="ruleForm.primaryRemark"
                :autosize="{ minRows: 1 }"
                :maxlength="1000"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow v-if="currentType === PageTypeEnum.COMMENT">
          <ElCol :span="12">
            <ElFormItem label="样品寄送地" prop="pushStyleAddressId">
              <el-select v-model="ruleForm.pushStyleAddressId" clearable filterable class="w-full">
                <el-option
                  v-for="item in stylePushAddressList"
                  :key="item.cityDict"
                  :label="item.cityDict_zh"
                  :value="item.cityDict"
                />
              </el-select>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="销售市场" prop="sellMarket">
              <ElInput
                v-model="ruleForm.sellMarket"
                :autosize="{ minRows: 1 }"
                :maxlength="500"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品类别" prop="productType">
              <ElCascader
                v-model="ruleForm.productType"
                :options="categoryTree"
                :props="categorProps"
                class="w-full"
                clearable
                filterable
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="评审意见" prop="judgeRemark">
              <ElInput
                v-model="ruleForm.judgeRemark"
                :autosize="{ minRows: 1 }"
                :maxlength="1000"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="上市季节" prop="publishSeasonDict">
              <Selection v-model="ruleForm.publishSeasonDict" api-key="DEVELOP_SEASON" filterable />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </div>
      <!-- 未通过 -->
      <ElRow v-else>
        <ElCol :span="12">
          <ElFormItem label="未选中原因" prop="unSelectReasonDict">
            <ElCheckboxGroup v-model="ruleForm.unSelectReasonDict">
              <ElCheckbox
                v-for="item in unSelectReasonList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElCheckboxGroup>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="未选中说明" prop="unSelectDescription">
            <ElInput
              v-model="ruleForm.unSelectDescription"
              :autosize="{ minRows: 1 }"
              :maxlength="1000"
              show-word-limit
              type="textarea"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="closed">取消</ElButton>
        <ElButton :loading="loading" type="primary" @click="onConfirm">确认</ElButton>
      </span>
    </template>
  </ElDialog>
</template>
<style lang="less" scoped></style>
