<script lang="ts" setup>
import { ref } from 'vue'
import { PublicNoticeAggregateDTO } from '@/api/notice/types'
import { getQMSDict } from '@/utils'
import { getBrandAddressApi } from '@/api/notice'
import { useValidator } from '@/hooks/web/useValidator'
import { VendorDTO } from '@/api/common/type'
import type { FormInstance } from 'element-plus'
import type { CurrentType } from '@/utils/enum'
import { PageTypeEnum } from '@/utils/enum'

const { required } = useValidator()

interface Props {
  data: any
  currentType: CurrentType
}

const emit = defineEmits(['update'])

const props = withDefaults(defineProps<Props>(), {
  data: () =>
    ({
      mainPic: [{}],
      referPics: [{}],
      mainPic_url: [{}],
      referPics_url: [{}]
    } as PublicNoticeAggregateDTO)
})

const ruleForm: any = computed({
  get: () => {
    const { unSelectReasonDTO, brandId, cityDict } = props.data

    const unSelectReasonDict_zh = unSelectReasonDTO
      ?.map((item) => item.unSelectReasonDict_zh)
      .join(',')
    Object.assign(props.data, { unSelectReasonDict_zh })
    if (props.currentType === PageTypeEnum.VIEW && cityDict && brandId) {
      handlePushStyleAddress(brandId, props.data)
      Object.assign(props.data, { pushStyleAddressId: cityDict })
    }

    return props.data
  },
  set: (val) => {
    emit('update', val)
  }
})
const ruleFormRef = ref<FormInstance>()

const rules = ref({
  hasVendor: [required()],
  mainPic: [required()],
  referPics: [required()],
  vendorName: [required()]
})

const showManageAll = computed(() => {
  return props.currentType === PageTypeEnum.VIEW
})
const showManagePart = computed(() => {
  return [PageTypeEnum.VIEW].includes(props.currentType)
})
const handleChange = (val) => {
  if (val === '0') {
    ruleForm.value.vendorId = ''
    ruleForm.value.vendorName = ''
  }

  emit('update', ruleForm.value)
}
const vendorOptions = ref<Array<VendorDTO>>([])
const changeVendor = (val) => {
  ruleForm.value.vendorId = vendorOptions.value?.find(
    (v: VendorDTO) => v.vendorName === val
  )?.vendorId
}

const handlePushStyleAddress = async (val, row) => {
  const { datas } = await getBrandAddressApi(val)
  Object.assign(row, { stylePushAddressList: datas })
}

defineExpose({
  ruleFormRef
})
</script>
<template>
  <ElForm
    ref="ruleFormRef"
    :disabled="[PageTypeEnum.VIEW].includes(currentType)"
    :model="ruleForm"
    :rules="rules"
    center
    label-width="140px"
    scroll-to-error
    @submit.prevent
  >
    <ElRow>
      <ElDivider content-position="left">跟进信息</ElDivider>
      <ElCol :span="12">
        <ElFormItem label="推款信息来源">
          <Selection v-model="ruleForm.sourceFromDict" api-key="SOURCE_FROM" disabled />
        </ElFormItem>
      </ElCol>
      <ElCol v-if="showManagePart" :span="12">
        <ElFormItem label="样品接收时间">
          <ElInput v-model="ruleForm.acceptTime" disabled />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="是否合作供应商" prop="hasVendor">
          <ElRadioGroup v-model="ruleForm.hasVendor">
            <ElRadio
              v-for="(item, index) in getQMSDict('YES_NO')"
              :key="index"
              :disabled="ruleForm.sourceFromDict === 'SRM'"
              :label="item.label"
              :value="item.value"
              @change="handleChange"
            />
          </ElRadioGroup>
        </ElFormItem>
      </ElCol>
      <el-col v-if="showManagePart" :span="12">
        <ElFormItem label="样品接收数量">
          <ElInput v-model="ruleForm.sampleCount" disabled />
        </ElFormItem>
      </el-col>
      <el-col v-if="ruleForm.hasVendor === '1'" :span="12">
        <ElFormItem label="供应商名称" prop="vendorName">
          <Selection
            v-model="ruleForm.vendorName"
            :disabled="ruleForm.sourceFromDict === 'SRM'"
            :params="{ useStatus: '1' }"
            api-key="enableVendorList"
            filterable
            @change="changeVendor"
            @response-data="(e) => (vendorOptions = e)"
          />
        </ElFormItem>
      </el-col>
      <ElRow v-if="showManageAll">
        <ElCol :span="12">
          <ElFormItem label="样品寄送地">
            <el-select
              v-model="ruleForm.pushStyleAddressId"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="item in ruleForm.stylePushAddressList"
                :key="item.cityDict"
                :label="item.cityDict_zh"
                :value="item.cityDict"
              />
            </el-select>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="初选通过">
            <ElInput v-model="ruleForm.hasPassPrimary" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="初选说明">
            <ElInput v-model="ruleForm.remark.primaryRemark" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="评审通过">
            <ElInput v-model="ruleForm.hasPassJudge" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="评审意见">
            <ElInput v-model="ruleForm.remark.judgeRemark" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="归属品牌">
            <ElInput v-model="ruleForm.brandName" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="产品企划">
            <ElInput v-model="ruleForm.planerName" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="产品设计师">
            <ElInput v-model="ruleForm.designerName" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="产品类别">
            <ElInput v-model="ruleForm.productTypeNameArray" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="上市季节">
            <ElInput v-model="ruleForm.publishSeasonDict_zh" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="未选中原因">
            <ElInput v-model="ruleForm.unSelectReasonDict_zh" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="未选中说明">
            <ElInput v-model="ruleForm.remark.unSelectDescription" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElRow>
  </ElForm>
</template>
<style lang="less" scoped></style>
