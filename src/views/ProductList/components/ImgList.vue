<script lang="ts" setup>
import { computed, PropType, type WritableComputedRef } from 'vue'
import { PushAggregateResponse } from '@/api/notice/types'
import { ElButton, ElCard, ElCheckbox, ElSkeleton, ElSkeletonItem, ElTooltip } from 'element-plus'

const emit = defineEmits(['update', 'first'])

const props = defineProps({
  data: {
    type: Array as PropType<PushAggregateResponse[]>,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: true
  }
})
const tableData: WritableComputedRef<Array<PushAggregateResponse>> = computed({
  get: () => props.data,
  set: (val) => {
    emit('update', val)
  }
})

const handleFirst = (currentType, item) => {
  emit('first', { currentType, item })
}
const handleUrl = (item) => {
  return item.attachment?.mainPic_url && item.attachment?.mainPic_url[0]?.url
}
defineExpose({})
</script>
<template>
  <div
    v-for="(item, index) in tableData"
    :key="index"
    :gutter="20"
    class="flex flex-wrap justify-start"
  >
    <ElCard
      :body-style="{ padding: '0px', width: '100%', height: '100%' }"
      class="w-[260px] m-2"
      style="position: relative"
    >
      <ElSkeleton :loading="props.loading" animated>
        <template #template>
          <div class="flex justify-center">
            <ElSkeletonItem style="width: 110px; height: 100px; margin: 24px" variant="image" />
          </div>
          <div class="flex flex-col justify-center items-center">
            <ElSkeletonItem style="width: 40%; margin: 4px 0" variant="text" />
            <ElSkeletonItem style="width: 30%; margin: 4px 0" variant="text" />
            <ElSkeletonItem style="width: 60%; margin: 4px 0" variant="text" />
            <ElSkeletonItem style="width: 60%; margin: 4px 0" variant="text" />
          </div>
        </template>
        <template #default>
          <div>
            <div class="h-4 flex flex-row-reverse mr-3">
              <ElCheckbox v-model="item.checked" />
            </div>
            <div class="flex justify-center">
              <el-image
                :preview-src-list="[handleUrl(item)]"
                :src="item?.attachment?.mainPic_url && item.attachment.mainPic_url[0]?.url"
                class="h-[100px] w-[110px] pb-2"
                lazy
              />
              <div class="flag flag-left">{{ item.stateDict_zh }}</div>
            </div>
            <div
              class="flex flex-col text-sm leading-loose info-border text-left border-top-2 border-bottom-2 border-solid border-gray-200 p-2"
            >
              <div
                class="overflow-hidden text-ellipsis whitespace-nowrap w-[100%] text-[#409eff] cursor-pointer"
                @click="handleFirst('view', item)"
              >
                产品名称：
                <ElTooltip :content="item.productName" effect="dark" placement="top-start">
                  {{ item.productName }}
                </ElTooltip>
              </div>
              <div class="overflow-hidden text-ellipsis whitespace-nowrap w-[100%]"
                >公司名称：
                <ElTooltip :content="item.companyName" effect="dark" placement="top-start">{{
                  item.companyName
                }}</ElTooltip></div
              >
              <div class="text-gray-500">推款时间：{{ item.pushTime }}</div>
              <div class="mt-1 flex items-center w-full text-center"
                ><div class="mr-1 font-medium">{{ item.sourceFromDict_zh }}</div
                ><div class="mr-1">{{ item.productZhCategoryDict_zh }}</div>
                <div class="mr-1 overflow-hidden text-ellipsis whitespace-nowrap w-[40px]">
                  <ElTooltip
                    :content="item.styleBody_zh?.join()"
                    effect="dark"
                    placement="top-start"
                  >
                    {{ item.styleBody_zh?.join() }}</ElTooltip
                  ></div
                >
                <div class="mr-1 overflow-hidden text-ellipsis whitespace-nowrap w-[40px]"
                  ><ElTooltip
                    :content="item.model_zh?.join()"
                    effect="dark"
                    placement="top-start"
                    >{{ item.model_zh?.join() }}</ElTooltip
                  ></div
                >
                <div>{{ item.shoesTreeDict_zh }}</div>
              </div>
            </div>
            <div class="flex h-[40px] leading-9 items-align text-center">
              <div class="w-1/2 border-right-2 border-solid border-gray-200">
                <ElButton
                  :disabled="item.stateDict !== '1'"
                  :type="item.stateDict === '1' ? 'primary' : 'info'"
                  link
                  size="large"
                  @click="handleFirst('first', item)"
                  >初选</ElButton
                >
              </div>
              <div class="w-1/2">
                <ElButton
                  :disabled="item.stateDict !== '4'"
                  :type="item.stateDict === '4' ? 'primary' : 'info'"
                  link
                  @click="handleFirst('comment', item)"
                  >评审</ElButton
                >
              </div>
            </div>
          </div>
        </template>
      </ElSkeleton>
    </ElCard>
  </div>
</template>
<style lang="less" scoped>
.flag {
  position: absolute;
  width: 80px;
  height: 26px;
  font-size: 12px;
  line-height: 26px;
  color: #fff;
  text-align: center;
  background: #409eff;
  opacity: 0.85;
  box-shadow: 0px 3px 3px #aaa;
}

.flag-left {
  top: 0px;
  left: 0px;
  // -moz-transform: rotate(-45deg);
  // -ms-transform: rotate(-45deg);
  // -o-transform: rotate(-45deg);
  // -webkit-transform: rotate(-45deg);
  // transform: rotate(-45deg);
}
</style>
