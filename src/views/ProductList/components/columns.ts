import { getQMSDict } from '@/utils'

export const handleStep = (state: string) => {
  // const arr = [
  //   { title: state === '7' ? '初选未通过' : '初选中' },
  //   { title: '待寄送样品' },
  //   { title: '待接收样品' },
  //   { title: '评审中' },
  //   { title: '待补充信息' }
  // ]
  const arr = [
    { title: '待初选' },
    { title: '待补充信息' },
    { title: '待评审' },
    { title: '待寄送样品' },
    { title: '待接收样品' }
  ]
  switch (state) {
    case ProductStateEnum.UN_SELECT_PRIMARY:
    case ProductStateEnum.JUDGING_NOT_PASS:
    case ProductStateEnum.UN_SELECT_ACCEPT:
      arr.push({
        title: '未选中'
      })
      break
    default:
      arr.push({
        title: '选中'
      })
      break
  }
  return arr
}

export const ReceiveSampleColumns = [
  {
    type: 'seq',
    label: '序号',
    width: 50,
    field: 'seq'
  },
  {
    field: 'productName',
    label: '产品名称',
    'min-width': 100
  },
  {
    field: 'productZhCategoryDict_zh',
    label: '产品大类',
    'min-width': 100
  },
  {
    field: 'companyName',
    label: '公司名称',
    'min-width': 100
  },
  {
    field: 'sendTime',
    label: '样品寄送日期',
    'min-width': 140
  },
  {
    field: 'chooseFlag',
    label: '是否选中',
    'min-width': 150
  },
  {
    field: 'acceptTime',
    label: '样品接收日期',
    'min-width': 240
  },
  {
    field: 'sampleCount',
    label: '样品数量',
    'min-width': 130
  },
  {
    field: 'remark',
    label: '备注',
    'min-width': 160,
    'show-overflow': false
  }
]

export const ChangeStatusColumns = [
  {
    type: 'seq',
    label: '序号',
    width: 50,
    field: 'seq'
  },
  {
    field: 'productName',
    label: '产品名称',
    'min-width': 100
  },
  {
    field: 'productZhCategoryDict_zh',
    label: '产品大类',
    'min-width': 100
  },
  {
    field: 'companyName',
    label: '公司名称',
    'min-width': 100
  },

  {
    field: 'stateDict_zh',
    label: '当前状态',
    'min-width': 100
  },
  {
    field: 'modifyTime',
    label: '关闭日期',
    'min-width': 240
  },
  {
    field: 'changeState',
    label: '变更状态',
    'min-width': 120
  }
]

export const FirstColumns = [
  {
    type: 'seq',
    label: '序号',
    width: 50,
    field: 'seq'
  },
  {
    field: 'productName',
    label: '产品名称',
    width: 120
  },
  {
    field: 'productZhCategoryDict_zh',
    label: '产品大类',
    width: 120
  },
  {
    field: 'image',
    label: '产品主图',
    width: 120,
    cellRender: {
      name: 'ImageColumn',
      props: {
        url: ({ row }) => {
          return row.attachment?.mainPic_url[0]?.url
        }
      }
    }
  },
  {
    field: 'companyName',
    label: '公司名称',
    width: 120
  },
  {
    field: 'hasVendor',
    label: '是否合作供应商',
    width: 160
  },
  {
    field: 'vendorName',
    label: '供应商名称',
    width: 160
  },
  {
    field: 'primaryResult',
    label: '初选通过',
    width: 160
  },
  {
    field: 'resultDetail',
    label: '初选结果记录',
    minWidth: 500
  }
]

export const ModifyColumns = [
  { field: 'productName', title: '产品名称' },
  { field: 'productZhCategoryDict', title: '产品大类' },
  { field: 'productClassify', title: '产品分类' },
  { field: 'styleBody', title: '风格' },
  { field: 'mainMaterial', title: '主要材质' },
  { field: 'shoesTreeDict', title: '楦型' },
  { field: 'model', title: '型体' },
  { field: 'containBottomDict', title: '是否有底楦' },
  { field: 'mouldHasProductDict', title: '模具是否量产' },
  { field: 'bottomCycle', title: '大底开模周期' },
  { field: 'mouldTypeDict', title: '模具类型' },
  { field: 'sizeStart', title: '尺码段(开始)' },
  { field: 'sizeEnd', title: '尺码段(结束)' },
  { field: 'containHalfDict', title: '包含半码' },
  { field: 'moq', title: 'MOQ' },
  { field: 'costStart', title: '成本区间(开始)' },
  { field: 'costEnd', title: '成本区间(结束)' },
  { field: 'containTaxDict', title: '包含税' },
  { field: 'hasSellCountryDict', title: '已出口地区' },
  { field: 'hasSellBrand', title: '已售卖品牌' },
  { field: 'containColor', title: '现有配色' },
  { field: 'lifeCycle', title: '生产周期' },
  { field: 'productPoint', title: '产品卖点' },
  { field: 'remark', title: '特殊功能说明' },
  { field: 'attachment', title: '参考文件' }
]

export enum ProductStateEnum {
  /**
   * @description 待初选
   */
  PRIMARY_SELECT = '1',
  /**
   * @description 待寄送样品
   */
  WAIT_SEND = '2',
  /**
   * @description 待补充信息
   */
  WAIT_INFOMATION = '5',
  /**
   * @description 待接收样品
   */
  WAIT_ACCEPT = '3',
  /**
   * @description 待评审
   */
  JUDGING = '4',
  /**
   * @description 初选未选中
   */
  UN_SELECT_PRIMARY = '7',
  /**
   * @description 评审未通过
   */
  JUDGING_NOT_PASS = '8',
  /**
   * @description 产品未选中
   */
  UN_SELECT_ACCEPT = '9',
  /**
   * @description 已选中
   */
  HAS_SELECTED = '6',
  /**
   * @description 产品已选中
   */
  HAS_SELECT_ACCEPT = '10'
}

export enum UnSelectReasonEnum {
  /**
   * @description 款式不匹配
   */
  Style = '10',
  /**
   * @description 鞋底不匹配
   */
  SoleMismatch = '4',
  /**
   * @description 需求不匹配
   */
  Need = '6',
  /**
   * @description 成本原因
   */
  Cost = '1',
  /**
   * @description 模具不匹配
   */
  Mould = '3',
  /**
   * @description MOQ不匹配
   */
  Moq = '8'
}

export const StateDictList = [
  {
    value: `${getQMSDict('PUSH_STYLE_STATE')
      .map((item) => item.value)
      .join(',')}`,
    label: '全部',
    key: 'all'
  },
  {
    value: ProductStateEnum.PRIMARY_SELECT,
    label: '待初选',
    key: 'primarySelect'
  },
  {
    value: ProductStateEnum.WAIT_SEND,
    label: '待寄送样品',
    key: 'waitSend'
  },
  {
    value: ProductStateEnum.WAIT_INFOMATION,
    label: '待补充信息',
    key: 'waitInformation'
  },
  {
    value: ProductStateEnum.WAIT_ACCEPT,
    label: '待接收样品',
    key: 'waitAccept'
  },
  {
    value: ProductStateEnum.JUDGING,
    label: '待评审',
    key: 'judging'
  },

  {
    value: `${ProductStateEnum.UN_SELECT_PRIMARY},${ProductStateEnum.UN_SELECT_ACCEPT},${ProductStateEnum.JUDGING_NOT_PASS}`,
    label: '未选中',
    key: 'unSelected'
  },
  {
    value: `${ProductStateEnum.HAS_SELECTED},${ProductStateEnum.HAS_SELECT_ACCEPT}`,
    label: '已选中',
    key: 'hasSelected'
  }
]

export enum StepActiveEnum {
  /**
   * @description 待初选
   */
  PRIMARY_SELECT = 1,
  /**
   * @description 待补充信息
   */
  WAIT_INFOMATION,
  /**
   * @description 待评审
   */
  JUDGING,
  /**
   * @description 待寄送样品
   */
  WAIT_SEND,
  /**
   * @description 待接收样品
   */
  WAIT_ACCEPT,
  /**
   * @description 未选中||选中
   */
  FINISH
}
