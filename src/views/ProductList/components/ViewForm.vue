<script lang="ts" setup>
import { ref } from 'vue'
import { propTypes } from '@/utils/propTypes'
import {
  commentPassApi,
  commentRejectApi,
  getBrandAddressApi,
  primaryApi
} from '@/api/notice/index'
import { useValidator } from '@/hooks/web/useValidator'
import { JudgePassRequest, ProductTypeDTO, PushAggregateResponse } from '@/api/notice/types'
import { getQMSDict } from '@/utils'
import { closeCurrentTag } from '@/utils/routerHelper'
import { getCategaryApi } from '@/api/common'
import { omit } from 'lodash-es'
import { useRouter } from 'vue-router'
import type { FormInstance } from 'element-plus'
import { PageTypeEnum } from '@/utils/enum'
import { UnSelectReasonEnum } from './columns'
import { BrandDTO, VendorDTO } from '@/api/common/type'

const { push } = useRouter()
const { required } = useValidator()
const props = defineProps({
  params: propTypes.object.def({}),
  modelValue: propTypes.bool.def(false),
  flag: propTypes.string.def('')
})
const emits = defineEmits(['update:modelValue'])

const ruleFormRef = ref<FormInstance>()
const loading = ref(false)
const ruleForm = reactive<PushAggregateResponse>({
  planer: [],
  designer: [],
  primaryResult: ''
})

watch(
  () => props.modelValue,
  (val: boolean) => {
    if (val) {
      if (props.params.sourceFromDict === 'SRM') {
        const { hasVendor, vendorId, vendorName } = props.params
        Object.assign(ruleForm, { hasVendor, vendorId, vendorName })
      }
      const { brandId, planer = [], designer = [] } = props.params
      Object.assign(ruleForm, { planer, brandId, designer })
      // 根据品牌获取寄送地
      brandId && handlePushStyleAddress(brandId)
    }
  },
  {
    immediate: true
  }
)

const dataMethod = (val) => {
  return val.map((v) => {
    return {
      userName: v.nameEn,
      email: v.emailAddress,
      feishuUserId: v.userId,
      disabled: v.userId === ''
    }
  })
}
const firstSubmitParams = computed<PushAggregateResponse>(() => {
  const { id, productZhCategoryDict, vendorAreaCode } = props.params
  return { list: [Object.assign({}, ruleForm, { id, productZhCategoryDict, vendorAreaCode })] }
})
const commentPassParams: JudgePassRequest = computed(() => {
  const { productType } = ruleForm
  let obj: ProductTypeDTO = {}
  const productTypeIdArray: string[] = []
  const productTypeNameArray: string[] = []
  Array.isArray(productType) &&
    productType.map((list) => {
      const arr = list.split('&')
      productTypeIdArray.push(arr[0])
      productTypeNameArray.push(arr[1])
    })
  Object.assign(obj, {
    productTypeIdArray,
    productTypeNameArray,
    productTypeLastId: productTypeIdArray[productTypeIdArray.length - 1]
  })
  return Object.assign({}, omit(ruleForm, 'productType'), { ...obj, id: props.params.id })
})

const rules = ref({
  hasVendor: [required()],
  primaryResult: [required()],
  brandId: [required()],
  planer: [required()],
  designer: [required()],
  pushStyleAddressId: [required()],
  unSelectReasonDict: [required()],
  vendorName: [required()],
  productType: [required()],
  publishSeasonDict: [required()]
})
const categoryTree = ref([])

const categorProps = ref()
const handleTree = (data: any) => {
  if (!Array.isArray(data)) return []
  data.map((item) => {
    const { categoryName, id, sonCategory = [] } = item
    Object.assign(item, {
      children: sonCategory,
      label: categoryName,
      value: `${id}&${categoryName}`
    })
    handleTree(item.children)
  })
}
const getCategoryTree = async () => {
  const { datas } = await getCategaryApi()
  handleTree(datas)
  categoryTree.value = datas
}
getCategoryTree()
const onConfirm = () => {
  ruleFormRef.value?.validate(async (valid) => {
    try {
      if (!valid) return
      loading.value = true
      if (props.flag === PageTypeEnum.FIRST) {
        await primaryApi(firstSubmitParams.value)
      }
      if (props.flag === PageTypeEnum.COMMENT) {
        ruleForm.primaryResult === '1'
          ? await commentPassApi(commentPassParams.value)
          : await commentRejectApi(commentPassParams.value)
      }
      emits('update:modelValue', false)
      closeCurrentTag()
      push({
        name: 'ProductListPage'
      })
    } finally {
      loading.value = false
    }
  })
}
const brandOptions = ref<Array<BrandDTO>>([])
const changeBrand = (val) => {
  ruleForm.brandName = brandOptions.value?.find((v) => v.id === val)?.brandName
  ruleForm.pushStyleAddressId = undefined
  handlePushStyleAddress(val)
}
const stylePushAddressList = ref([] as any)
const handlePushStyleAddress = async (val) => {
  const { datas } = await getBrandAddressApi(val)
  stylePushAddressList.value = datas
}
const closed = () => {
  ruleFormRef.value?.clearValidate()
  ruleFormRef.value?.resetFields()
  emits('update:modelValue', false)
}
const vendorOptions = ref<VendorDTO[]>([])
const changeVendor = (val) => {
  ruleForm.vendorId = vendorOptions.value?.find((v) => v.vendorName === val)?.vendorId as number
}
const unSelectReasonList = computed(() => {
  const list = getQMSDict('UN_SELECT_REASON')
  const firstUnSelectList = list.filter((v) =>
    [UnSelectReasonEnum.Need, UnSelectReasonEnum.Style, UnSelectReasonEnum.SoleMismatch].includes(
      v.value
    )
  )
  const commentUnselectList = list.filter(
    (v) =>
      ![UnSelectReasonEnum.Cost, UnSelectReasonEnum.Mould, UnSelectReasonEnum.Moq].includes(v.value)
  )
  return props.flag === PageTypeEnum.FIRST ? firstUnSelectList : commentUnselectList
})
</script>
<template>
  <ElDialog
    :close-on-click-modal="false"
    :model-value="modelValue"
    center
    title="操作确认"
    width="80%"
    @close="closed"
  >
    <ElForm ref="ruleFormRef" :model="ruleForm" :rules="rules" center label-width="150px">
      <ElRow>
        <ElCol :span="12">
          <ElFormItem label="是否合作供应商" prop="hasVendor">
            <ElRadioGroup v-model="ruleForm.hasVendor">
              <ElRadio
                v-for="(item, index) in getQMSDict('YES_NO')"
                :key="index"
                :disabled="props.params.sourceFromDict === 'SRM'"
                :label="item.label"
                :value="item.value"
              />
            </ElRadioGroup>
          </ElFormItem>
        </ElCol>
        <ElCol v-if="ruleForm.hasVendor === '1'" :span="12">
          <ElFormItem label="供应商名称" prop="vendorName">
            <Selection
              v-model="ruleForm.vendorName"
              :disabled="props.params.sourceFromDict === 'SRM'"
              :params="{ useStatus: '1' }"
              api-key="enableVendorList"
              filterable
              @change="changeVendor"
              @response-data="(e) => (vendorOptions = e)"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            :label="flag === PageTypeEnum.FIRST ? '初选通过' : '评审通过'"
            prop="primaryResult"
          >
            <ElRadioGroup v-model="ruleForm.primaryResult">
              <ElRadio
                v-for="(item, index) in getQMSDict('YES_NO')"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </ElRadioGroup>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <div v-if="ruleForm.primaryResult === '1'">
        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="归属品牌" prop="brandId">
              <Selection
                v-model="ruleForm.brandId"
                api-key="brandList"
                @change="changeBrand"
                @response-data="(e) => (brandOptions = e)"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品企划" prop="planer">
              <Selection
                v-model="ruleForm.planer"
                :dataMethod="dataMethod"
                api-key="getProductCenterListApi"
                filterable
                multiple
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品设计师" prop="designer">
              <Selection
                v-model="ruleForm.designer"
                :dataMethod="dataMethod"
                api-key="getProductCenterListApi"
                filterable
                multiple
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow v-if="flag === PageTypeEnum.FIRST">
          <ElCol :span="12">
            <ElFormItem label="初选说明" prop="primaryRemark">
              <ElInput
                v-model="ruleForm.primaryRemark"
                :autosize="{ minRows: 1 }"
                :maxlength="1000"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow v-if="flag === PageTypeEnum.COMMENT">
          <ElCol :span="12">
            <ElFormItem label="销售市场" prop="sellMarket">
              <ElInput
                v-model="ruleForm.sellMarket"
                :autosize="{ minRows: 1 }"
                :maxlength="500"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品类别" prop="productType">
              <ElCascader
                v-model="ruleForm.productType"
                :options="categoryTree"
                :props="categorProps"
                class="w-full"
                clearable
                filterable
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="样品寄送地" prop="pushStyleAddressId">
              <el-select
                v-model="ruleForm.pushStyleAddressId"
                clearable
                filterable
                style="width: 100%"
                ><el-option
                  v-for="item in stylePushAddressList"
                  :key="item.cityDict"
                  :label="item.cityDict_zh"
                  :value="item.cityDict"
              /></el-select>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="评审意见" prop="judgeRemark">
              <ElInput
                v-model="ruleForm.judgeRemark"
                :autosize="{ minRows: 1 }"
                :maxlength="1000"
                show-word-limit
                type="textarea"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="上市季节" prop="publishSeasonDict">
              <Selection v-model="ruleForm.publishSeasonDict" api-key="DEVELOP_SEASON" filterable />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </div>
      <ElRow v-if="ruleForm.primaryResult === '0'">
        <ElCol :span="12">
          <ElFormItem label="未选中原因" prop="unSelectReasonDict">
            <ElCheckboxGroup v-model="ruleForm.unSelectReasonDict">
              <ElCheckbox
                v-for="item in unSelectReasonList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElCheckboxGroup>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="未选中说明" prop="unSelectDescription">
            <ElInput
              v-model="ruleForm.unSelectDescription"
              :autosize="{ minRows: 1 }"
              :maxlength="1000"
              show-word-limit
              type="textarea"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="closed">取消</ElButton>
        <ElButton :loading="loading" type="primary" @click="onConfirm">确认</ElButton>
      </span>
    </template>
  </ElDialog>
</template>
<style lang="less" scoped>
.full-width {
  width: 100%;
}
</style>
