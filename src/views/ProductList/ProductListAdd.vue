<script lang="ts" setup>
import { CurrentForm, PushData, ReceiveSample, Return, ViewForm } from './components/index'
import type { CurrentType } from '@/utils/enum'
import { bannerSourceToLabel, PageTypeEnum } from '@/utils/enum'
import {
  handleStep,
  ProductStateEnum,
  ReceiveSampleColumns,
  StepActiveEnum
} from './components/columns'
import { closeCurrentTag } from '@/utils/routerHelper'
import { getProductApi, InsertApi } from '@/api/notice'
import { PushAggregateResponse } from '@/api/notice/types'
import { useRoute, useRouter } from 'vue-router'
import {
  ElButton,
  ElCol,
  ElCollapse,
  ElCollapseItem,
  ElForm,
  ElMessage,
  ElRow,
  ElStep,
  ElSteps
} from 'element-plus'
import VendorData from '@/views/ProductList/components/VendorData.vue'

interface FileItem {
  objectName?: string
  id?: number
  [propName: string]: any
}
const currentType: CurrentType = useRoute().params.pageType as CurrentType
let id: number | null = useRoute().query.id as number | null
const { push } = useRouter()
const active = ref(StepActiveEnum.PRIMARY_SELECT)
const localKey = ref('PDM_ID_LIST')
const VendorDataRef = ref()
const PushDataRef = ref()
const returnDialogVisible = ref(false)
const idList = ref<Array<number>>([])

const showManageAll = computed(() => {
  return currentType === PageTypeEnum.VIEW
})

const showManage = computed(() => {
  return [PageTypeEnum.COMMENT, PageTypeEnum.FIRST, PageTypeEnum.VIEW].includes(currentType)
})

const showPassBtn = computed(() => {
  return [PageTypeEnum.COMMENT, PageTypeEnum.FIRST].includes(currentType)
})

const ruleForm: any = reactive({
  remark: {
    pushRemark: ''
  },
  containHalfDict: '0',
  containTaxDict: '1',
  attachment: {
    otherPics: [],
    colorPics: [],
    sidePics: [],
    frontPic: [],
    bottomPic: [],
    mainPic: [],
    referFiles: []
  },
  stateDict: ''
})
onMounted(() => {
  const storedValue = sessionStorage.getItem(localKey.value)
  idList.value = storedValue ? JSON.parse(storedValue) : []

  if (currentType !== PageTypeEnum.ADD) {
    handleView()
  }
})

const handlePreAndNext = (flag) => {
  if (!Array.isArray(idList.value) || idList.value.length === 0) {
    ElMessage.warning('暂无更多数据')
    return
  }

  const index = idList.value.findIndex((ele) => ele + '' === id + '')

  if (
    !((flag === 'previous' && index > 0) || (flag === 'next' && index < idList.value.length - 1))
  ) {
    ElMessage.warning('暂无更多数据')
    return
  }

  id = flag === 'next' ? idList.value[index + 1] : idList.value[index - 1]

  push({
    name: 'ProductListAdd',
    params: { pageType: PageTypeEnum.VIEW },
    query: { id }
  })
}
const processAttachments = (attachment) => {
  const list = [
    'bottomPic_url',
    'colorPics_url',
    'frontPic_url',
    'mainPic_url',
    'otherPics_url',
    'sidePics_url'
  ]

  for (const [key, value] of Object.entries(attachment)) {
    if (list.includes(key)) {
      const arr =
        (Array.isArray(value) &&
          value.map((v) => ({ ...v, bannerSourceLabel: bannerSourceToLabel[key] }))) ||
        []
      value && ruleForm.imgList.push(...arr)
    }
    if (key === 'referFiles_url') {
      ruleForm.attachment.referFiles =
        (Array.isArray(value) && value.map((v) => ({ ...v, name: v.originFileName }))) || []
    }
  }
}
const handleView = async () => {
  if (!id) return
  const { datas } = await getProductApi({ id })

  const { vendorId, attachment = {}, designer = [], planer = [] } = datas
  Object.assign(ruleForm, datas, {
    hasVendor: vendorId ? '1' : '0',
    designerName: designer?.map((v) => v.userName),
    planerName: planer?.map((v) => v.userName)
  })

  ruleForm.imgList = []
  processAttachments(attachment)
  VendorDataRef.value.ruleFormRef.clearValidate()
  PushDataRef.value.ruleFormRef.clearValidate()
  if (currentType === PageTypeEnum.VIEW) {
    switch (ruleForm.stateDict) {
      case ProductStateEnum.PRIMARY_SELECT:
        active.value = StepActiveEnum.PRIMARY_SELECT
        break
      case ProductStateEnum.WAIT_INFOMATION:
        active.value = StepActiveEnum.WAIT_INFOMATION
        break
      case ProductStateEnum.JUDGING:
        active.value = StepActiveEnum.JUDGING
        break
      case ProductStateEnum.WAIT_SEND:
        active.value = StepActiveEnum.WAIT_SEND
        break
      case ProductStateEnum.WAIT_ACCEPT:
        active.value = StepActiveEnum.WAIT_ACCEPT
        break
      case ProductStateEnum.UN_SELECT_PRIMARY:
      case ProductStateEnum.JUDGING_NOT_PASS:
      case ProductStateEnum.UN_SELECT_ACCEPT:
      case ProductStateEnum.HAS_SELECT_ACCEPT:
      case ProductStateEnum.HAS_SELECTED:
        active.value = StepActiveEnum.FINISH
        break
    }
  }
}
const dialogVisible = ref(false)
const dialogViewOperateVisible = ref(false)
const isPass = ref(true)
const operateFlag = ref('')
const handleViewOprate = (flag: string) => {
  operateFlag.value = flag
  dialogViewOperateVisible.value = true
}
const handlePass = async (flag) => {
  PushDataRef.value.ruleFormRef?.validate((valid) => {
    if (valid) {
      isPass.value = flag
      dialogVisible.value = true
    }
  })
}
const activeNames = ref(['0', '1', '2', '3', '4'])
const handleUpdate = (val) => {
  Object.assign(ruleForm, val)
}

const submitParams = computed(() => {
  const { attachment: list } = ruleForm
  let attachment = {}
  for (const [key, value] of Object.entries(list)) {
    attachment[key] = (value as FileItem[])?.map((v: FileItem) => v.objectName)
  }
  return Object.assign({}, ruleForm, { attachment })
})
const loading = ref(false)
const handleSubmit = () => {
  const basicValid = VendorDataRef.value.ruleFormRef?.validate()
  Promise.all([basicValid]).then(async () => {
    try {
      loading.value = true
      await InsertApi(submitParams.value)
      handleClose()
    } finally {
      loading.value = false
    }
  })
}
const sampleDialogVisible = ref(false)
const sampleSelectedData = ref<PushAggregateResponse[]>([])
const handleSample = () => {
  sampleSelectedData.value = [ruleForm]
  sampleDialogVisible.value = true
}
const handleClose = () => {
  closeCurrentTag()
  push({
    name: 'ProductListPage'
  })
}
</script>
<template>
  <!-- 退回供应商 -->
  <Return :id="ruleForm.id" v-model="returnDialogVisible" />
  <!-- 评审 初选按钮操作确认  -->
  <CurrentForm
    v-model="dialogVisible"
    :currentType="currentType"
    :isPass="isPass"
    :params="ruleForm"
  />
  <ViewForm v-model="dialogViewOperateVisible" :flag="operateFlag" :params="ruleForm" />
  <ContentWrap>
    <ElCollapse v-model="activeNames">
      <ElForm ref="ruleFormRef" :model="ruleForm" center label-width="140px" @submit.prevent>
        <ElCollapseItem v-if="showManageAll" name="0">
          <template #title>
            <span class="text-[14px]">推款产品进度信息</span>
          </template>
          <ElSteps :active="active" align-center class="justify-center" finish-status="success">
            <ElStep
              v-for="(item, index) in handleStep(ruleForm.stateDict)"
              :key="index"
              :title="item.title"
            />
          </ElSteps>
        </ElCollapseItem>
        <ElCollapseItem name="1">
          <template #title>
            <span class="text-[14px]">推款信息</span>
          </template>
          <VendorData
            ref="VendorDataRef"
            :current-type="currentType"
            :data="ruleForm"
            @update="handleUpdate"
            @set-upload-loading="(val) => (loading = val)"
          />
        </ElCollapseItem>

        <ElCollapseItem v-if="showManage" name="2">
          <template #title>
            <span class="text-[14px]">推款管理</span>
          </template>
          <PushData
            ref="PushDataRef"
            :current-type="currentType"
            :data="ruleForm"
            @update="handleUpdate"
          />
        </ElCollapseItem>
      </ElForm>
    </ElCollapse>
  </ContentWrap>
  <div
    v-if="!showManageAll"
    class="fixed w-full z-40 bottom-0 bg-[var(--app-content-bg-color)] h-[70px] flex items-center"
  >
    <div class="w-full flex justify-center">
      <ElButton
        v-if="currentType === PageTypeEnum.ADD"
        :loading="loading"
        type="primary"
        @click="handleSubmit"
      >
        <Icon class="mr-1" icon="ep:circle-check-filled" />
        提交
      </ElButton>

      <ElButton v-if="showPassBtn" type="primary" @click="handlePass(true)">
        <Icon class="mr-1" icon="ep:circle-check-filled" />
        {{ currentType === PageTypeEnum.FIRST ? '通过' : '选中' }}
      </ElButton>
      <ElButton v-if="showPassBtn" type="primary" @click="handlePass(false)">
        <Icon class="mr-1" icon="ep:circle-close-filled" />
        {{ currentType === PageTypeEnum.FIRST ? '未通过' : '未选中' }}
      </ElButton>

      <ElButton
        v-if="currentType === PageTypeEnum.COMMENT"
        type="primary"
        @click="returnDialogVisible = true"
      >
        <Icon class="mr-1" icon="ep:shopping-cart" />
        需供应商完善
      </ElButton>
      <ElButton @click="handleClose">
        <Icon class="mr-1" icon="ep:circle-close-filled" />
        关闭
      </ElButton>
    </div>
  </div>
  <div
    v-if="showManageAll"
    class="fixed w-full flex justify-center z-40 bottom-0 bg-[var(--app-content-bg-color)] h-[70px] items-center pl-6"
  >
    <ElRow class="w-full">
      <ElCol :span="12" class="flex justify-center">
        <ElButton class="mr-4" size="large" @click="handlePreAndNext('previous')">
          <Icon class="mr-1" icon="ep:arrow-left-bold" />
          上一个
        </ElButton>
        <ElButton size="large" @click="handlePreAndNext('next')">
          下一个
          <Icon class="ml-1" icon="ep:arrow-right-bold" />
        </ElButton>
      </ElCol>
      <ElCol :span="12" class="flex justify-start">
        <ElButton class="mr-4" size="large" type="primary" @click="handleClose">
          <Icon class="mr-1" icon="ep:circle-close-filled" />
          退出
        </ElButton>
        <ElButton
          v-if="ruleForm.stateDict === ProductStateEnum.PRIMARY_SELECT"
          class="mr-4"
          size="large"
          type="primary"
          @click="handleViewOprate(PageTypeEnum.FIRST)"
        >
          <Icon class="mr-1" icon="ep:star-filled" />
          初选
        </ElButton>
        <ElButton
          v-if="ruleForm.stateDict === ProductStateEnum.JUDGING"
          class="mr-4"
          size="large"
          type="primary"
          @click="returnDialogVisible = true"
        >
          <Icon class="mr-1" icon="ep:shopping-cart" />
          需供应商完善
        </ElButton>
        <ElButton
          v-if="ruleForm.stateDict === ProductStateEnum.WAIT_ACCEPT"
          class="mr-4"
          size="large"
          type="primary"
          @click="handleSample"
        >
          <Icon class="mr-1" icon="ep:circle-check-filled" />
          接收样品
        </ElButton>

        <ElButton
          v-if="ruleForm.stateDict === ProductStateEnum.JUDGING"
          class="mr-4"
          size="large"
          type="primary"
          @click="handleViewOprate(PageTypeEnum.COMMENT)"
        >
          <Icon class="mr-1" icon="ep:comment" />
          评审
        </ElButton>
      </ElCol>
    </ElRow>
  </div>
  <ReceiveSample
    v-model="sampleDialogVisible"
    :data="sampleSelectedData"
    :tableColumns="ReceiveSampleColumns"
    sampleType="sample"
    @update="handleClose"
  />
</template>
<style lang="less" scoped></style>
