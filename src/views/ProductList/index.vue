<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { Search } from '@/components/Search'
import { ImgList, ReceiveSample } from './components/index'
import {
  ChangeStatusColumns,
  ReceiveSampleColumns,
  ProductStateEnum,
  StateDictList
} from './components/columns'
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { reactive, ref } from 'vue'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { getCategaryApi, getListBrandApi, getProductCenterListApi } from '@/api/common'
import { useRouter } from 'vue-router'
import { getPushExportApi, getPushListApi, pageAggregateCountApi } from '@/api/notice'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import type { PushAggregateResponse, PushStyleAggregateStateDTO } from '@/api/notice/types'
import type { CurrentType } from '@/utils/enum'
import { PageTypeEnum, STATUSENUM } from '@/utils/enum'
import { omit } from 'lodash-es'

const { push } = useRouter()
const { register, tableObject, methods } = useTable({
  getListApi: getPushListApi,
  noPaging: true,
  processedParameter: {
    timeField: {
      pushTime: ['pushTimeStart', 'pushTimeEnd']
    },
    lastCategoryId: ['productTypeLastIds']
  }
})
const searchRef = ref()
const { getList, getSelections, setSearchParams, getSearchParams } = methods
const pager = reactive<pager>({
  current: 1,
  size: 50,
  total: 0
})

let countObj = ref<PushStyleAggregateStateDTO>({})
const getStateCount = async (params) => {
  const { datas } = await pageAggregateCountApi(params)
  countObj.value = datas
}
const handleRefresh = () => {
  const { states } = form
  const newArr = states
    .map((item) => item.split(','))
    .flat()
    .map(String)
  getList({ ...pager, states: newArr })
  getStateCount({ ...pager })
}
onMounted(() => {
  handleRefresh()
})

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: '#',
    type: 'checkbox',
    label: '',
    width: 50
  },
  {
    type: 'seq',
    label: '序号',
    width: 50
  },
  {
    field: 'code',
    label: '推款编号',
    width: 100
  },
  {
    field: 'productName',
    label: '产品名称',
    width: 100,
    cellRender: {
      name: 'LinkColumn',
      events: { click: ({ row }) => handleAdd(PageTypeEnum.VIEW, row) }
    },
    search: {
      show: true,
      index: 8,
      componentProps: {
        placeholder: '输入产品名称，支持模糊搜索'
      }
    }
  },
  {
    field: 'image',
    label: '产品主图',
    width: 78,
    cellRender: {
      name: 'ImageColumn',
      props: {
        url: ({ row }) => {
          return row.attachment?.mainPic_url[0]?.url
        }
      }
    }
  },
  {
    field: 'productZhCategoryDict_zh',
    label: '产品大类',
    width: 100,
    search: {
      field: 'productZhCategoryDictList',
      show: true,
      component: 'CheckboxButton',
      index: 2,
      colProps: { span: 24 },
      value: [],
      dictName: 'PRODUCT_ZH_CATEGORY'
    }
  },
  {
    field: 'productClassify',
    label: '产品分类',
    width: 100
  },
  {
    field: 'styleBody_zh',
    label: '风格',
    'min-width': 80,
    search: {
      field: 'styleBody',
      show: true,
      component: 'CheckboxButton',
      index: 6,
      dictName: 'STYLE_BODY',
      colProps: { span: 24 },
      value: []
    }
  },
  {
    field: 'companyName',
    label: '公司名称',
    'min-width': 80,
    search: {
      show: true,
      index: 9,
      componentProps: {
        placeholder: '输入公司名称，支持模糊搜索'
      }
    }
  },
  {
    field: 'shoesTreeDict_zh',
    label: '楦型',
    minWidth: 80
  },
  {
    field: 'model_zh',
    label: '型体',
    'min-width': 80
  },
  {
    field: 'size',
    label: '尺码段',
    width: 100,
    formatter: ({ row }) => {
      return `${row.sizeStart ?? ''}-${row.sizeEnd ?? ''}`
    }
  },
  {
    field: 'mainMaterial',
    label: '主要材质',
    'min-width': 80,
    search: {
      show: true,
      index: 13,
      componentProps: {
        placeholder: '输入主要材质，支持模糊搜索'
      }
    }
  },
  {
    field: 'pushRemark',
    label: '推款说明',
    'min-width': 80,
    search: {
      show: true,
      index: 14,
      componentProps: {
        placeholder: '输入推款说明，支持模糊搜索'
      }
    },
    formatter: ({ row }) => {
      return row.remark?.pushRemark ? row.remark.pushRemark : ''
    }
  },
  {
    field: 'productPoint',
    label: '产品卖点',
    'min-width': 80,
    search: {
      show: true,
      index: 15,
      componentProps: {
        placeholder: '输入产品卖点，支持模糊搜索'
      }
    },
    formatter: ({ row }) => {
      return row.remark?.productPoint ? row.remark.productPoint : ''
    }
  },
  {
    field: 'returnFactoryTypeI18',
    label: '成本区间（￥）',
    'min-width': 120,
    formatter: ({ row }) => {
      return `${row.costStart ?? ''}-${row.costEnd ?? ''}`
    }
  },
  {
    field: 'moq',
    label: 'MOQ',
    minWidth: 100
  },
  {
    field: 'containColor',
    label: '现有配色',
    minWidth: 100,
    search: {
      show: true,
      index: 15,
      componentProps: {
        placeholder: '输入现有配色，支持模糊搜索'
      }
    }
  },
  {
    field: 'brandName',
    label: '归属品牌',
    minWidth: 100,
    search: {
      field: 'brandIds',
      show: true,
      component: 'CheckboxButton',
      index: 5,
      api: getListBrandApi,
      colProps: { span: 24 },
      value: [],
      componentProps: {
        optionsAlias: {
          valueField: 'brandName',
          labelField: 'id'
        }
      }
    }
  },
  {
    field: 'productTypeNameArray',
    label: '产品类别',
    minWidth: 100,
    search: {
      field: 'productTypeLastIds',
      show: true,
      index: 10,
      component: 'Cascader',
      api: getCategaryApi,
      value: [],
      componentProps: {
        props: {
          multiple: true,
          label: 'categoryName',
          value: 'id',
          children: 'sonCategory'
        },
        collapseTags: true,
        filterable: true
      }
    }
  },
  {
    label: '产品设计师',
    minWidth: 100,
    search: {
      field: 'designer',
      show: true,
      component: 'Select',
      index: 11,
      api: getProductCenterListApi,
      componentProps: {
        filterable: true,
        multiple: true,
        collapseTags: true,
        optionsAlias: {
          valueField: 'userId',
          labelField: 'nameEn'
        }
      }
    },
    formatter: ({ row }) => {
      return row.designer.map((v) => v.userName)
    }
  },
  {
    label: '产品企划',
    minWidth: 100,
    field: 'planer',
    search: {
      field: 'planerList',
      show: true,
      component: 'Select',
      index: 12,
      api: getProductCenterListApi,
      componentProps: {
        filterable: true,
        multiple: true,
        collapseTags: true,
        optionsAlias: {
          valueField: 'userId',
          labelField: 'nameEn'
        }
      }
    },
    formatter: ({ row }) => {
      return row.planer.map((v) => v.userName)
    }
  },
  {
    field: 'hasVendor',
    label: '是否合作供应商',
    minWidth: 140,
    search: {
      field: 'hasVendor',
      show: true,
      component: 'CheckboxButton',
      index: 8,
      dictName: 'YES_NO',
      value: []
    },
    formatter: ({ row }) => {
      return row.vendorId ? '是' : '否'
    }
  },
  {
    field: 'vendorAreaName',
    label: '区域',
    minWidth: 100,
    search: {
      field: 'vendorAreaCodeList',
      show: true,
      component: 'CheckboxButton',
      index: 3,
      colProps: { span: 12 },
      value: [],
      dictName: 'PDM_AREA'
    }
  },
  {
    field: 'sourceFromDict_zh',
    label: '推款信息来源',
    labelWidth: 120,
    minWidth: 130,
    search: {
      field: 'sourceFromDictList',
      show: true,
      component: 'CheckboxButton',
      index: 7,
      colProps: { span: 24 },
      value: [],
      dictName: 'SOURCE_FROM'
    }
  },
  {
    field: 'pushTime',
    label: '推款时间',
    minWidth: 100,
    search: {
      label: '推款时间段',
      show: true,
      index: 4,
      colProps: { span: 12 },
      component: 'DatePicker',
      componentProps: {
        type: 'daterange',
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD'
      }
    }
  },
  {
    field: 'stateDict_zh',
    label: '推款状态',
    minWidth: 100,
    // search: {
    //   label: '推款状态',
    //   field: 'states',
    //   show: true,
    //   component: 'CheckboxButton',
    //   dictName: 'PUSH_STYLE_STATE',
    //   index: -1,
    //   colProps: { span: 24 },
    //   value: [],
    //   componentProps: {}
    // },
    fixed: 'right'
  },
  {
    field: 'url',
    label: '查看产品链接',
    minWidth: 140,
    cellRender: {
      name: 'LinkColumn',
      events: { click: ({ row }) => window.open(row.url, '_blank') }
    }
  },
  {
    field: 'modifyByName',
    label: '操作人',
    minWidth: 100
  },
  {
    field: 'modifyTime',
    label: '操作时间',
    minWidth: 100
  }
])
watch(
  () => tableObject.total,
  (total) => {
    pager.total = total
  },
  {
    immediate: true,
    deep: true
  }
)
const { allSchemas } = useCrudSchemas(crudSchemas)
const isImage = ref(true)
const handleSearch = async () => {
  const { states } = form
  const newArr = states
    .map((item) => item.split(','))
    .flat()
    .map(String)

  const formData = (await searchRef?.value?.getFormData()) || {}
  const params = Object.assign({}, formData, pager)
  const designer = params.designer ? params.designer : []
  const planerList = params.planerList ? params.planerList : []
  Object.assign(params, { designer, planerList, states: newArr })

  setSearchParams(params)
  const obj = getSearchParams(params)

  // 更新状态统计数据
  getStateCount(omit(obj, ['states']))
}
const handleReset = () => {
  form.states = ['1']
  handleSearch()
}
const localKey = 'PDM_ID_LIST'
const handleAdd = async (pageType: CurrentType, row?: PushAggregateResponse) => {
  const selectionList: PushAggregateResponse[] = await getSelections()

  const showMessageAndReturn = (message: string) => {
    return ElMessage.warning(message)
  }

  if (!isImage.value) {
    if (
      pageType === PageTypeEnum.FIRST &&
      (selectionList.length !== 1 || selectionList[0].stateDict !== ProductStateEnum.PRIMARY_SELECT)
    ) {
      return showMessageAndReturn('请勾选一条【初选中】状态数据操作')
    }

    if (
      pageType === PageTypeEnum.COMMENT &&
      (selectionList.length !== 1 || selectionList[0].stateDict !== ProductStateEnum.JUDGING)
    ) {
      return showMessageAndReturn('请勾选一条【评审中】状态数据操作')
    }
  }

  if (pageType === PageTypeEnum.VIEW) {
    const ids = tableObject.tableList.map((v) => v.id)
    sessionStorage.setItem(localKey, JSON.stringify(ids))
  }

  const idToUse = row?.id || selectionList[0]?.id

  push({
    name: 'ProductListAdd',
    params: { pageType },
    query: { id: idToUse }
  })
}

const handleUpdate = (val) => {
  Object.assign(pager, val)
  handleSearch()
}
const sampleDialogVisible = ref(false)
const sampleType = ref('')
const sampleSelectedData = ref<PushAggregateResponse[]>([])
const handleReceive = async (type) => {
  let selectionList: Array<PushAggregateResponse> = []

  selectionList = isImage.value
    ? tableObject.tableList.filter((ele) => ele.checked)
    : await getSelections()
  if (type === STATUSENUM.SAMPLE) {
    if (
      !selectionList.length ||
      selectionList.some((ele) => ele.stateDict !== ProductStateEnum.WAIT_ACCEPT)
    ) {
      return ElMessage.warning('请勾选【待接收样品】状态数据操作')
    }
  }
  if (type === STATUSENUM.STATUS) {
    if (
      !selectionList.length ||
      selectionList.some(
        (ele: PushAggregateResponse) =>
          ![
            ProductStateEnum.UN_SELECT_PRIMARY,
            ProductStateEnum.UN_SELECT_ACCEPT,
            ProductStateEnum.JUDGING_NOT_PASS
          ].includes(ele.stateDict as string)
      )
    ) {
      return ElMessage.warning('请勾选【未选中】状态数据操作')
    }
  }
  sampleSelectedData.value = selectionList.map((v) => {
    return omit(v, ['acceptTime', 'sampleCount'])
  })

  sampleType.value = type
  sampleDialogVisible.value = true
}
const loading = ref(false)
const handleExportSure = async (data) => {
  try {
    loading.value = true
    await getPushExportApi(data)
    ElMessage.success('导出成功，请前往导出页面下载')
  } finally {
    loading.value = false
  }
}
const showExportConfirmation = async (exportData) => {
  const confirmed = await ElMessageBox.confirm(
    '全部数据导出缓慢，不建议导出， 是否继续操作?',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )

  if (confirmed) {
    handleExportSure(exportData)
  }
}
const handleExport = async () => {
  const { states } = form
  const newArr = states
    .map((item) => item.split(','))
    .flat()
    .map(String)
  const isImageValue = isImage.value
  const selectionList = isImageValue
    ? tableObject.tableList.filter((ele) => ele.checked)
    : await getSelections()
  const ids = selectionList?.map((v) => v.id) || []
  const formData = (await searchRef?.value?.getFormData()) || {}
  const designer = formData.designer ? formData.designer : []
  const planerList = formData.planerList ? formData.planerList : []
  const valuesArray = Object.values(formData).filter(
    (value) => (Array.isArray(value) && value.length) || (!Array.isArray(value) && value)
  )

  const exportData = { ...formData, ids, states: newArr, designer, planerList }

  if (!valuesArray.length && !selectionList.length) {
    await showExportConfirmation(exportData)
  } else {
    handleExportSure(exportData)
  }
}
const form = reactive({
  states: [ProductStateEnum.PRIMARY_SELECT]
})
</script>

<template>
  <ReceiveSample
    v-if="sampleDialogVisible"
    v-model="sampleDialogVisible"
    :data="sampleSelectedData"
    :sampleType="sampleType"
    :tableColumns="sampleType === STATUSENUM.SAMPLE ? ReceiveSampleColumns : ChangeStatusColumns"
    @update="handleRefresh()"
  />
  <ContentWrap>
    <ElForm :model="form">
      <ElFormItem label="状态" label-width="68px">
        <ElCheckboxGroup v-model="form.states" class="status">
          <ElCheckboxButton v-for="item in StateDictList" :label="item.value" :key="item.value">
            <ElBadge :value="(countObj && countObj[item.key]) || ''" class="badge_itemm">{{
              item.label
            }}</ElBadge></ElCheckboxButton
          >
        </ElCheckboxGroup>
      </ElFormItem>
    </ElForm>
    <Search
      ref="searchRef"
      :colNum="3"
      :export-permission="['productList:export']"
      :queryLoading="tableObject.loading"
      :schema="allSchemas.searchSchema"
      :show-export="true"
      @reset="handleReset"
      @search="handleSearch"
      @handle-export="handleExport"
    />

    <div class="mb-[24px] flex justify-between">
      <div>
        <ElButton v-if="!isImage" type="primary" @click="handleAdd(PageTypeEnum.FIRST)">
          <Icon class="mr-1" icon="ep:search" />
          初选
        </ElButton>
        <ElButton type="primary" @click="handleReceive(STATUSENUM.SAMPLE)">
          <Icon class="mr-1" icon="ep:check" />
          接收样品
        </ElButton>
        <ElButton v-if="!isImage" type="primary" @click="handleAdd(PageTypeEnum.COMMENT)">
          <Icon class="mr-1" icon="ep:edit" />
          评审
        </ElButton>
        <ElButton type="primary" @click="handleReceive(STATUSENUM.STATUS)">
          <Icon class="mr-1" icon="ep:switch-button" />
          变更状态
        </ElButton>
      </div>
      <div class="flex">
        <Paging v-if="isImage" :pager="pager" class="mt-0 mr-2" @update="handleUpdate" />

        <ElButton link type="primary" @click="isImage = !isImage">
          <Icon :icon="isImage ? 'ep:list' : 'ep:picture-filled'" class="mr-1" />
          {{ isImage ? '列表' : '图片' }}
        </ElButton>
      </div>
    </div>
    <div v-if="!isImage">
      <Table
        :columns="allSchemas.tableColumns"
        :data="tableObject.tableList"
        :loading="tableObject.loading"
        @register="register"
      />
    </div>
    <div v-else class="flex flex-wrap justify-start items-center pic">
      <ImgList
        :data="tableObject.tableList"
        :loading="tableObject.loading"
        @first="({ currentType, item }) => handleAdd(currentType, item)"
        @update="(val) => (tableObject.tableList = val)"
      />
    </div>
    <Paging :pager="pager" @update="handleUpdate" />
  </ContentWrap>
</template>

<style lang="less" scoped>
:deep(.el-checkbox-button) {
  margin: 0 12px 4px 0 !important;

  .el-checkbox-button__inner {
    border: none;
  }
}

:deep(.status) {
  .el-checkbox-button__inner {
    border: 1px solid #dcefd6;
  }
}

:deep(.el-checkbox-button:last-child),
:deep(.el-checkbox-button:first-child) {
  .el-checkbox-button__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }
}

:deep(.badge_itemm) {
  .el-badge__content {
    top: -8px !important;
    right: 8px !important;
    z-index: 999;
  }
}
</style>
