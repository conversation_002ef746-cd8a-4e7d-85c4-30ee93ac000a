<script lang="ts" setup>
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { Tinymce } from '@/components/Tinymce'
import {
  createNotification,
  CreateNotificationAPI,
  getNotificationInfo,
  updateNotification
} from '../api'
import { NotificationStatusEnums, notificationStatusMap } from '../const'
import dayjs from 'dayjs'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'

dayjs.extend(isSameOrAfter)

defineOptions({
  name: 'NotificationInfo'
})

const route = useRoute()

const isEdit = computed(() => {
  return route.name === 'EditNotification'
})

const formData = ref<CreateNotificationAPI.Request>({
  title: '',
  content: '',
  cutoffDate: '',
  //附件
  attachmentUrl: []
})
const formRules = ref<FormRules<CreateNotificationAPI.Request>>({
  title: [{ required: true, message: '请输入通知标题', trigger: 'blur' }],
  cutoffDate: [{ required: true, message: '请选择通知截止日', trigger: 'blur' }],
  content: [{ required: true, message: '请输入通知详情', trigger: 'blur' }]
})
const formRef = ref<FormInstance>()

const queryLoading = ref(false)
const fetchData = async () => {
  if (!route.query.id) {
    return
  }
  queryLoading.value = true
  const [error, result] = await getNotificationInfo({ id: route.query.id as string })
  queryLoading.value = false
  if (!error && result?.datas) {
    formData.value = result.datas
  }
}

fetchData()
onActivated(fetchData)

const router = useRouter()
const submitLoading = ref(false)
const submitFn = computed(() => {
  return isEdit.value ? updateNotification : createNotification
})
const handleSubmit = async (status: NotificationStatusEnums) => {
  const valid = await formRef.value?.validate()
  if (!valid) {
    return
  }
  submitLoading.value = true
  const [error, result] = await submitFn.value({
    ...formData.value,
    title: formData.value.title?.trim(),
    status
  })
  submitLoading.value = false
  if (!error && result) {
    ElMessage.success(result.msg || '提交成功')
    useClosePage('NotificationList')
  }
}
</script>

<template>
  <ContentWrap>
    <ElForm
      ref="formRef"
      v-loading="queryLoading"
      :model="formData"
      :rules="formRules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      label-width="auto"
      scroll-to-error
    >
      <ElFormItem label="通知标题" prop="title">
        <ElInput
          v-model="formData.title"
          maxlength="200"
          placeholder="请输入通知标题"
          show-word-limit
        />
      </ElFormItem>
      <ElFormItem label="通知截止日" prop="cutoffDate">
        <ElDatePicker
          v-model="formData.cutoffDate"
          :disabled-date="(time: Date) => dayjs(dayjs().format('YYYY-MM-DD')).isAfter(dayjs(time).format('YYYY-MM-DD'))"
          placeholder="请选择通知截止日"
          value-format="YYYY-MM-DD"
        />
      </ElFormItem>
      <ElFormItem v-if="isEdit" label="状态" prop="status">
        <ElText>{{ notificationStatusMap[formData.status!] }}</ElText>
      </ElFormItem>
      <ElFormItem label="通知详情" prop="content">
        <Tinymce v-model="formData.content" use-oss />
      </ElFormItem>
      <ElFormItem label="附件" prop="attachmentUrl">
        <OssUpload
          v-model="formData.attachmentUrl"
          :limit="20"
          :size-limit="1024 * 1024 * 50"
          drag
          list-type="text"
          mutiple
        />
      </ElFormItem>
    </ElForm>
  </ContentWrap>
  <ContentWrap class="mt-2">
    <div class="flex items-center justify-center">
      <ElButton :loading="submitLoading" @click="router.go(-1)"> 返回 </ElButton>
      <ElButton
        :loading="submitLoading"
        type="primary"
        @click="handleSubmit(NotificationStatusEnums.DRAFT)"
      >
        保存
      </ElButton>
      <ElButton
        :loading="submitLoading"
        type="primary"
        @click="handleSubmit(NotificationStatusEnums.PUBLISHED)"
      >
        保存并发布
      </ElButton>
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped></style>
