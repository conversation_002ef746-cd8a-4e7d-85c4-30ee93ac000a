<script lang="ts" setup>
import { CreateNotificationAPI, getNotificationInfo } from '../api'

defineOptions({
  name: 'ViewNotification'
})

const route = useRoute()
const router = useRouter()
const content = ref<HTMLElement>()
const formData = ref<CreateNotificationAPI.Request>({
  title: '',
  content: '',
  cutoffDate: '',
  publishTime: '',
  //附件
  attachmentUrl: []
})
const queryLoading = ref(false)
const fetchData = async () => {
  if (!route.query.id) {
    return
  }
  queryLoading.value = true
  const [error, result] = await getNotificationInfo({ id: route.query.id as string })
  queryLoading.value = false
  if (!error && result?.datas) {
    formData.value = result.datas
  }
}

function processNode(node: ChildNode) {
  if (node.nodeType === Node.TEXT_NODE) {
    const urlRegex =
      /^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/gi
    const textContent = node.textContent
      ?.trim()
      .replace(
        /&nbsp;|&ensp;|&emsp;|&thinsp;|&ZeroWidthSpace;|&Tab;|&NewLine;|&NoBreak;|&InvisibleTimes;|&InvisibleComma;|&InvisiblePlus;|&InvisibleSeparator;|&zwnj;|&zwj;|&lrm;|&rlm;/g,
        ' '
      )

    // 如果文本内容包含URL，进行替换
    if (textContent && urlRegex.test(textContent)) {
      const wrappedText = textContent.replace(urlRegex, (url) => {
        return `<a class="text-blue-400" href="${url}" target="_blank">${url}</a>`
      })

      // 创建一个包含替换结果的新HTML节点
      const newFragment = document.createElement('span')
      newFragment.innerHTML = wrappedText

      // 将替换后的HTML节点插入原文本节点的位置
      node.replaceWith(...Array.from(newFragment.childNodes))
    }
  } else if (node.nodeType === Node.ELEMENT_NODE) {
    // 递归处理所有子节点
    Array.from(node.childNodes).forEach(processNode)
  }
}

const showViewer = ref(false)
const initialIndex = ref(0)
const imgUrlList = ref<string[]>([])
watch(
  () => formData.value.content,
  () => {
    nextTick(() => {
      const imgList = content.value?.querySelectorAll('img')
      Array.from(content.value?.childNodes || []).forEach(processNode)
      if (imgList) {
        imgList.forEach((img, i) => {
          imgUrlList.value.push(img.src)
          img.onclick = () => {
            initialIndex.value = i
            showViewer.value = true
          }
        })
      }
    })
  }
)

fetchData()
onActivated(fetchData)
</script>

<template>
  <ContentWrap>
    <p class="text-center text-xl font-bold">{{ formData.title }}</p>
    <p v-if="formData.publishTime" class="text-center text-sm font-semibold mt-2">
      通知发布日期：{{ formData.publishTime }}
    </p>
    <ElDivider />
    <div ref="content" class="p-4" v-html="formData.content"></div>
    <div class="p-4 flex justify-center">
      <div class="whitespace-nowrap"> 附件文档： </div>
      <OssUpload v-model="formData.attachmentUrl" class="" disabled list-type="text" />
    </div>
    <ElImageViewer
      v-if="showViewer"
      :initial-index="initialIndex"
      :url-list="imgUrlList"
      @close="showViewer = false"
    />
  </ContentWrap>
  <ContentWrap class="mt-2">
    <div class="flex items-center justify-center">
      <ElButton @click="router.go(-1)"> 返回 </ElButton>
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
:deep(.el-upload-list) {
  margin-top: 0;
}

:deep(a) {
  color: #409eff;
}
</style>
