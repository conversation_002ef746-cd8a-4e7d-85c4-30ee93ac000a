export enum NotificationStatusEnums {
  /**
   * 已发布
   */
  PUBLISHED = 'published',
  /**
   * 已关闭
   */
  CLOSED = 'closed',
  /**
   * 草稿
   */
  DRAFT = 'draft',
  /**
   * 已结束
   */
  END = 'end'
}

export const notificationStatusMap = {
  [NotificationStatusEnums.PUBLISHED]: '已发布',
  [NotificationStatusEnums.CLOSED]: '已关闭',
  [NotificationStatusEnums.DRAFT]: '草稿',
  [NotificationStatusEnums.END]: '已结束'
}

export const notificationStatusOptions = [
  {
    label: '已发布',
    value: NotificationStatusEnums.PUBLISHED
  },
  {
    label: '已关闭',
    value: NotificationStatusEnums.CLOSED
  },
  {
    label: '草稿',
    value: NotificationStatusEnums.DRAFT
  },
  {
    label: '已结束',
    value: NotificationStatusEnums.END
  }
]
