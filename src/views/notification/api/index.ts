import to from 'await-to-js'
import request from '@/config/fetch'

export namespace NotificationListAPI {
  export interface Params {
    /**
     * 通知时间段-结束时间
     */
    endDate?: string
    /**
     * 通知时间段-开始时间
     */
    startDate?: string
    /**
     * 状态
     */
    status?: string[]
    /**
     * 通知标题
     */
    title?: string
  }

  export interface PagePdmBaseNoticeResp {
    records?: Row[]
    /**
     * 总数
     */
    total?: number
    [property: string]: any
  }

  export interface Row {
    /**
     * 通知创建时间
     */
    createTime?: string
    /**
     * 通知截止时间
     */
    cutoffDate?: string
    /**
     * id
     */
    id?: number
    /**
     * 操作人
     */
    modifyByName?: string
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 通知编号
     */
    noticeCode?: string
    /**
     * 通知发布时间
     */
    publishTime?: string
    /**
     * 状态
     */
    status?: string
    /**
     * 通知标题
     */
    title?: string
    [property: string]: any
  }
  export type List = Row[]

  export type Request = Params & PageParams
  export type Response = ResponseData<PagePdmBaseNoticeResp>
}
export function getNotificationList(data: NotificationListAPI.Request) {
  return to<NotificationListAPI.Response>(
    request.post({
      url: '/pdm-base/workBench/notice/page',
      data
    })
  )
}

export namespace NotificationDetailAPI {
  export interface Params {
    id?: string
  }
  export interface PdmBaseNoticeDetailResp {
    /**
     * 附件
     */
    attachmentUrl?: BaseFileDTO[]
    /**
     * 通知内容
     */
    content?: string
    /**
     * 通知截止日
     */
    cutoffDate?: string
    /**
     * id
     */
    id?: number
    /**
     * 通知状态
     */
    status?: string
    /**
     * 通知标题
     */
    title?: string
    /**
     * 发布时间
     */
    publishTime?: string
    [property: string]: any
  }

  export type Response = ResponseData<PdmBaseNoticeDetailResp>
  export type Request = Params
}
export function getNotificationInfo(params: NotificationDetailAPI.Request) {
  return to<NotificationDetailAPI.Response>(
    request.get({
      url: '/pdm-base/workBench/notice/detail',
      params
    })
  )
}

export namespace CreateNotificationAPI {
  export interface Request {
    /**
     * 附件
     */
    attachmentUrl?: BaseFileDTO[]
    /**
     * 通知内容
     */
    content?: string
    /**
     * 通知截止时间
     */
    cutoffDate?: string
    /**
     * id
     */
    id?: number
    /**
     * 通知状态
     */
    status?: string
    /**
     * 通知标题
     */
    title?: string
    /**
     * 发布时间
     */
    publishTime?: string
    [property: string]: any
  }
  export type Response = BasicResponseData
}
export function createNotification(data: CreateNotificationAPI.Request) {
  return to<CreateNotificationAPI.Response>(
    request.post({
      url: '/pdm-base/workBench/notice/add',
      data
    })
  )
}
export function updateNotification(data: CreateNotificationAPI.Request) {
  return to<CreateNotificationAPI.Response>(
    request.post({
      url: '/pdm-base/workBench/notice/update',
      data
    })
  )
}

export namespace CloseNotificationAPI {
  export type Params = number[]

  export type Response = BasicResponseData
  export type Request = Params
}
export function closeNotification(data: CloseNotificationAPI.Request) {
  return to<CloseNotificationAPI.Response>(
    request.post({
      url: '/pdm-base/workBench/notice/close',
      data
    })
  )
}
