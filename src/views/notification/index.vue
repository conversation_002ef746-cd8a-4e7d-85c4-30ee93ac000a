<script lang="ts" setup>
import { ElButton, ElMessage, ElMessageBox, ElPagination, FormInstance } from 'element-plus'
import { Icon } from '@/components/Icon'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { VxeTableInstance } from 'vxe-table'
import { ref } from 'vue'
import { closeNotification, getNotificationList, NotificationListAPI } from './api'
import { cloneDeep } from 'lodash-es'
import { NotificationStatusEnums, notificationStatusMap, notificationStatusOptions } from './const'
import dayjs from 'dayjs'
import { watchDebounced } from '@vueuse/core'

defineOptions({
  name: 'NotificationList'
})

const useQuery = () => {
  const formRef = ref<FormInstance>()
  const defaultForm: NotificationListAPI.Params & { date: [string, string] } = {
    title: '',
    date: ['', ''],
    status: []
  }
  const formData = ref(cloneDeep(defaultForm))
  const pager = ref<BasicPage>({
    current: 1,
    size: 50,
    total: 0
  })
  const tableData = ref<NotificationListAPI.List>([])
  const tableRef = ref<VxeTableInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const queryLoading = ref(false)
  const queryParams = computed(() => {
    const [startDate, endDate] = formData.value.date
    return {
      ...formData.value,
      startDate: dayjs(startDate).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      endDate: dayjs(endDate).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
      date: undefined,
      current: pager.value.current,
      size: pager.value.size
    }
  })
  const handleQuery = async () => {
    queryLoading.value = true
    const [error, result] = await getNotificationList(queryParams.value)
    queryLoading.value = false
    if (!error && result?.datas) {
      tableData.value = result.datas.records || []
      pager.value.total = result.datas.total || 0
    }
  }
  onActivated(handleQuery)
  const handleReset = () => {
    formData.value = cloneDeep(defaultForm)
    handleQuery()
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef
  })

  const handleSelectedChange = () => {
    selectedRows.value = tableRef.value?.getCheckboxRecords() || []
  }

  return {
    formRef,
    formData,
    pager,
    tableRef,
    pagerRef,
    tableData,
    maxHeight,
    queryLoading,
    handleQuery,
    handleReset,
    handleSelectedChange
  }
}

const router = useRouter()

const useOperation = () => {
  const selectedRows = ref<NotificationListAPI.List>([])
  onDeactivated(() => {
    selectedRows.value = []
  })
  const handleCreateNotification = () => {
    router.push({ name: 'CreateNotification' })
  }
  const handleEditNotification = () => {
    if (!selectedRows.value.length) {
      ElMessage.warning('请选择一条通知记录进行编辑')
      return
    }
    if (selectedRows.value.length > 1) {
      ElMessage.warning('只能选择一条通知记录进行编辑')
      return
    }
    router.push({ name: 'EditNotification', query: { id: selectedRows.value[0].id } })
  }
  const handleCloseNotification = () => {
    if (!selectedRows.value.length) {
      ElMessage.warning('请选择一条通知记录进行编辑')
      return
    }
    const hasClosedOrFinished = selectedRows.value.filter(
      (e) =>
        e.status === notificationStatusMap[NotificationStatusEnums.CLOSED] ||
        e.status === notificationStatusMap[NotificationStatusEnums.END]
    )
    if (hasClosedOrFinished.length) {
      ElMessage.warning('已关闭或已结束的通知无法进行关闭操作')
      return
    }
    ElMessageBox({
      title: '提示',
      message: `您选择了「${selectedRows.value.length}」条通知信息操作关闭，请确认！`,
      type: 'warning',
      closeOnClickModal: false,
      showCancelButton: true,
      beforeClose: async (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = instance.cancelButtonLoading = true
          const [error, result] = await closeNotification(
            selectedRows.value.map((item) => item.id!)
          )
          instance.confirmButtonLoading = instance.cancelButtonLoading = false
          if (!error && result) {
            ElMessage.success(result.msg || '操作成功')
            done()
            await handleQuery()
          }
        } else {
          done()
        }
      }
    })
  }
  const handleViewNotification = (row: NotificationListAPI.Row) => {
    router.push({ name: 'ViewNotification', query: { id: row.id } })
  }

  return {
    selectedRows,
    handleCreateNotification,
    handleEditNotification,
    handleCloseNotification,
    handleViewNotification
  }
}

const {
  formRef,
  formData,
  pager,
  tableRef,
  pagerRef,
  tableData,
  maxHeight,
  queryLoading,
  handleQuery,
  handleReset,
  handleSelectedChange
} = useQuery()

const {
  selectedRows,
  handleCreateNotification,
  handleEditNotification,
  handleCloseNotification,
  handleViewNotification
} = useOperation()

onMounted(() => {
  handleQuery()
  watchDebounced(
    formData,
    async () => {
      await handleQuery()
    },
    { deep: true, debounce: 400 }
  )
})
</script>

<template>
  <ContentWrap>
    <ElForm ref="formRef" :model="formData" label-width="auto">
      <ElRow :gutter="20">
        <ElCol :span="8">
          <ElFormItem label="通知标题" prop="title">
            <ElInput
              v-model="formData.title"
              clearable
              placeholder="输入通知标题信息，支持模糊搜索"
              @change="handleQuery"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem label="通知时间段" prop="date">
            <ElDatePicker
              v-model="formData.date"
              end-placeholder="结束时间"
              placeholder="请选择"
              start-placeholder="开始时间"
              type="daterange"
              value-format="YYYY-MM-DD"
              @change="handleQuery"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem>
            <ElButton :loading="queryLoading" type="primary" @click="handleQuery"> 查询 </ElButton>
            <ElButton :loading="queryLoading" @click="handleReset"> 重置 </ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem label="状态" prop="status">
            <ElSelect
              v-model="formData.status"
              clearable
              collapse-tags
              collapse-tags-tooltip
              multiple
              placeholder="请选择"
            >
              <ElOption
                v-for="item in notificationStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <div class="mb-[10px] min-h-8 flex justify-between items-center">
      <div>
        <ElButton
          v-hasPermi="['createNotification']"
          class="!m-1"
          type="primary"
          @click="handleCreateNotification"
        >
          <Icon icon="ep:plus" />
          <span class="text-[14px]">发布通知</span>
        </ElButton>
        <ElButton
          v-hasPermi="['editNotification']"
          class="!m-1"
          type="primary"
          @click="handleEditNotification"
        >
          <Icon icon="ep:edit" />
          <span class="text-[14px]">修改通知</span>
        </ElButton>
        <ElButton
          v-hasPermi="['closeNotification']"
          class="!m-1"
          type="primary"
          @click="handleCloseNotification"
        >
          <Icon icon="ep:close" />
          <span class="text-[14px]">关闭通知</span>
        </ElButton>
      </div>
    </div>
    <VxeTable
      ref="tableRef"
      :data="tableData"
      :loading="queryLoading"
      :max-height="maxHeight - 75"
      align="center"
      @checkbox-change="handleSelectedChange"
      @checkbox-all="handleSelectedChange"
    >
      <VxeColumn type="checkbox" width="40" />
      <VxeColumn title="序号" type="seq" width="60" />
      <VxeColumn field="noticeCode" title="通知编号">
        <template #default="{ row }: { row: NotificationListAPI.Row }">
          <ElButton link type="primary" @click="handleViewNotification(row)">
            {{ row.noticeCode }}
          </ElButton>
        </template>
      </VxeColumn>
      <VxeColumn field="title" title="通知标题" />
      <VxeColumn field="createTime" title="通知创建时间" />
      <VxeColumn field="publishTime" title="通知发布时间" />
      <VxeColumn field="cutoffDate" title="通知截止日" />
      <VxeColumn field="status" title="通知状态" />
      <VxeColumn field="modifyByName" title="操作人" />
      <VxeColumn field="modifyTime" title="操作时间" />
    </VxeTable>
    <ElPagination
      ref="pagerRef"
      v-model:current-page="pager.current"
      v-model:page-size="pager.size"
      :total="pager.total"
      background
      class="mt-4"
      layout="total, sizes, prev, pager, next, jumper"
      @change="handleQuery"
    />
  </ContentWrap>
</template>

<style lang="less" scoped></style>
