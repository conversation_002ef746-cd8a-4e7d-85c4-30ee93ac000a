<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { Search } from '@/components/Search'
import { ElButton } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { exportPageApi } from '@/api/common'

const { register, tableObject, methods } = useTable({
  getListApi: exportPageApi,
  processedParameter: {
    timeField: {
      createTime: ['createTimeStart', 'createTimeEnd']
    }
  }
})
const { getList, setSearchParams } = methods

const handleSearch = async () => {
  const formData = (await searchRef?.value?.getFormData()) || {}

  return setSearchParams(formData)
}

onMounted(() => {
  getList()
})

const searchRef = ref()
const crudSchemas = reactive<CrudSchema[]>([
  {
    type: 'seq',
    label: '序号',
    width: 50
  },
  {
    field: 'moduleName',
    label: '导出任务名称',
    minWidth: 120
  },
  {
    field: 'createTime',
    label: '创建时间',
    minWidth: 120,
    search: {
      show: true,
      index: 2,
      label: '创建日期',
      component: 'DatePicker',
      componentProps: {
        type: 'daterange',
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD'
      }
    }
  },
  {
    field: 'stateI18',
    label: '状态',
    minWidth: 80
  },
  {
    field: 'modifyTime',
    label: '导出完成时间',
    minWidth: 120,
    formatter: ({ row }) => {
      return row.state == 1 ? row.modifyTime : ' '
    }
  },
  {
    field: 'errorMsg',
    label: '异常信息',
    minWidth: 100
  },
  {
    field: 'needFeishuNotice',
    label: '是否飞书通知',
    minWidth: 100,
    formatter: ({ row }) => {
      return row.needFeishuNotice == 0 ? '否' : '是'
    }
  },
  {
    field: 'operate',
    label: '操作',
    minWidth: 100
  }
])
const { allSchemas } = useCrudSchemas(crudSchemas)

const handleDownload = (url) => {
  url && window.open(url, '_blank')
}
</script>

<template>
  <ContentWrap>
    <Search
      ref="searchRef"
      :colNum="2"
      :expand="false"
      :schema="allSchemas.searchSchema"
      @reset="setSearchParams"
      @search="handleSearch"
    />
    <Table
      v-model:currentPage="tableObject.current"
      v-model:pageSize="tableObject.size"
      :columns="allSchemas.tableColumns"
      :data="tableObject.tableList"
      :loading="tableObject.loading"
      :pagination="{ total: tableObject.total }"
      @register="register"
    >
      <template #operate="{ row }">
        <ElButton v-if="row.state == 1" link type="primary" @click="handleDownload(row.ossUrl)"
          ><Icon class="mr-1" icon="ep:bottom" />下载</ElButton
        >
      </template>
    </Table>
  </ContentWrap>
</template>
<style lang="less" scoped></style>
