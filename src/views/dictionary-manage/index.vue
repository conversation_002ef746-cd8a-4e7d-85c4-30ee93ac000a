<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { FormInstance } from 'element-plus'
import { ref } from 'vue'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import type { VxeTableInstance } from 'vxe-table'
import { DictListAPI, getDictList } from '@/views/basic-library-manage/api/common'
import AddDialog from './AddDialog.vue'

defineOptions({
  name: 'DictionaryManage'
})

const formRef = ref<FormInstance>()

const queryLoading = ref(false)
const handleQuery = async () => {
  queryLoading.value = true
  const [error, result] = await getDictList()
  if (!error) {
    tableData.value = result?.datas || []
  }
  queryLoading.value = false
}

const addDialogVisible = ref(false)

const currentRow = ref<DictListAPI.Row | null>(null)
const handleAdd = (row: DictListAPI.Row | null) => {
  currentRow.value = row
  addDialogVisible.value = true
}

const tableRef = ref<VxeTableInstance>()

const tableData = ref<DictListAPI.List>([])
const maxHeight = useTableHeight({
  tableRef
})

handleQuery()

onActivated(handleQuery)
</script>

<template>
  <ContentWrap>
    <AddDialog v-model="addDialogVisible" :current-row="currentRow" @refresh="handleQuery" />
    <ElForm ref="formRef" label-width="auto">
      <ElRow :gutter="20">
        <ElCol :span="8">
          <ElFormItem label-width="0">
            <ElButton :loading="queryLoading" type="primary" @click="handleQuery">
              <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
              查询
            </ElButton>
            <ElButton type="primary" @click="handleAdd(null)">
              <Icon class="mr-1" icon="ep:upload-filled" />
              新增
            </ElButton>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <VxeTable
      ref="tableRef"
      :data="tableData"
      :loading="queryLoading"
      :max-height="maxHeight - 50"
      :show-header-overflow="false"
    >
      <VxeColumn type="expand" width="40">
        <template #content="{ row }: { row: DictListAPI.Row }">
          <VxeTable :data="row.dictValueList" :max-height="400" :show-header-overflow="false">
            <VxeColumn field="dictCnName" title="字典中文" />
            <VxeColumn field="dictEnName" title="字典英文" />
            <VxeColumn field="dictValue" title="字典值" />
          </VxeTable>
        </template>
      </VxeColumn>
      <VxeColumn field="typeName" title="字典名称" />
      <VxeColumn field="dictItem" title="字典Key" />
      <VxeColumn :show-overflow="false" fixed="right" title="操作" width="100">
        <template #default="{ row }: { row: DictListAPI.Row }">
          <ElButton size="small" text type="primary" @click="handleAdd(row)"> 修改 </ElButton>
        </template>
      </VxeColumn>
    </VxeTable>
  </ContentWrap>
</template>

<style lang="less" scoped></style>
