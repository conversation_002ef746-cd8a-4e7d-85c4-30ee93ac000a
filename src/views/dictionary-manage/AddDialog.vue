<script setup lang="ts">
import {
  DictListAPI,
  DictValueAPI,
  saveDictList,
  SaveDictListAPI
} from '@/views/basic-library-manage/api/common'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { VxeTableInstance } from 'vxe-table'

defineOptions({
  name: 'AddDialog'
})

const props = defineProps<{
  modelValue: boolean
  currentRow: DictListAPI.Row | null
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const isUpdate = computed(() => !!props.currentRow?.id) // 是否为编辑

const title = computed(() => {
  return isUpdate.value ? '编辑字典' : '新增字典'
})

type FormType = Omit<DictListAPI.Row, 'dictValueList'> & {
  dictValueList?: {
    /**
     * 展示值-中文
     */
    dictCnName?: string
    /**
     * 展示值-英文
     */
    dictEnName?: string
    /**
     * 传值
     */
    dictValue?: string | number
    id?: number
    disabled?: boolean
  }[]
}

const formData = ref<FormType>({})
const formRules = ref<FormRules<FormType>>({
  dictItem: [{ required: true, message: '请输入字典Key', trigger: 'blur' }],
  typeName: [{ required: true, message: '请输入字典名称', trigger: 'blur' }]
})
const formRef = ref<FormInstance>()
const tableRef = ref<VxeTableInstance | null>(null)

watch(
  () => visible.value,
  (val) => {
    if (val) {
      if (isUpdate.value) {
        if (!props.currentRow) return
        formData.value = props.currentRow
        formData.value.dictValueList = (props.currentRow?.dictValueList || []).map((e) => ({
          ...e,
          disabled: true
        }))
      } else {
        formData.value = {}
      }
    }
  }
)

const handleAdd = () => {
  formData.value.dictValueList = formData.value.dictValueList || []
  formData.value.dictValueList.push({})
  tableRef.value?.loadData(formData.value.dictValueList)
}
const handleRemove = (index: number) => {
  formData.value.dictValueList?.splice(index, 1)
  tableRef.value?.loadData(formData.value.dictValueList || [])
}

const submitLoading = ref(false)
const submitParams = computed<SaveDictListAPI.Request>(() => ({
  typeCode: formData.value.dictItem,
  typeName: formData.value.typeName,
  dictItemList: formData.value.dictValueList,
  id: formData.value.id
}))
const handleSubmit = async () => {
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) return
  submitLoading.value = true
  const [error, result] = await saveDictList(submitParams.value)
  submitLoading.value = false
  if (!error) {
    ElMessage.success(result?.msg || '保存成功')
    emit('refresh')
    handleClose()
  }
}

const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
}
</script>

<template>
  <Dialog v-model="visible" :title="title" :before-close="handleClose" top="5vh" width="1200">
    <ElForm ref="formRef" :model="formData" :rules="formRules" label-width="auto">
      <ElFormItem label="字典Key" prop="dictItem">
        <ElInput v-model="formData.dictItem" :disabled="isUpdate" placeholder="请输入" clearable />
      </ElFormItem>
      <ElFormItem label="字典名称" prop="typeName">
        <ElInput v-model="formData.typeName" :disabled="isUpdate" placeholder="请输入" clearable />
      </ElFormItem>
      <ElFormItem label="字典值" prop="dictValueList">
        <ElButton class="mb-4" type="primary" @click="handleAdd"> 添加 </ElButton>
        <VxeTable
          ref="tableRef"
          class="w-full"
          :data="formData.dictValueList"
          max-height="500"
          :show-overflow="false"
        >
          <VxeColumn field="dictCnName" title="字典中文">
            <template #default="{ row }: { row: DictValueAPI.Data }">
              <ElInput v-model="row.dictCnName" placeholder="请输入" clearable />
            </template>
          </VxeColumn>
          <VxeColumn field="dictEnName" title="字典英文">
            <template #default="{ row }: { row: DictValueAPI.Data }">
              <ElInput v-model="row.dictEnName" placeholder="请输入" clearable />
            </template>
          </VxeColumn>
          <VxeColumn field="dictValue" title="字典值">
            <template
              #default="{
                row,
                rowIndex
              }: {
                row: DictValueAPI.Data & { disabled: boolean },
                rowIndex: number
              }"
            >
              <ElFormItem
                :prop="`dictValueList.${rowIndex}.dictValue`"
                :show-message="false"
                :rules="[{ required: true, message: '请输入字典值', trigger: 'blur' }]"
              >
                <ElInput
                  v-model="row.dictValue"
                  :disabled="row.disabled"
                  placeholder="请输入"
                  clearable
                />
              </ElFormItem>
            </template>
          </VxeColumn>
          <VxeColumn title="操作" fixed="right" width="100" :show-overflow="false">
            <template
              #default="{
                row,
                rowIndex
              }: {
                row: DictValueAPI.Data & { disabled: boolean },
                rowIndex: number
              }"
            >
              <ElButton
                size="small"
                type="primary"
                :disabled="row.disabled"
                text
                @click="handleRemove(rowIndex)"
              >
                删除
              </ElButton>
            </template>
          </VxeColumn>
        </VxeTable>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton :loading="submitLoading" @click="handleClose">取消</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleSubmit"> 确定 </ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>
