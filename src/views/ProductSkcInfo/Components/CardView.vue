<script lang="ts" setup>
import { BaseClamp } from '@/components/BaseClamp'
import { ElMessage, ElTooltip, Measurable } from 'element-plus'
import type { ProductSkcInfoPageAPI } from '@/api/productSkcInfo/types'
import { ProductDataStatusEnums } from '@/enums'
import { productSkcInfoUpdateStatus } from '@/api/productSkcInfo'
import { RouteNameEnums } from '../helper'

const props = defineProps<{
  row: ProductSkcInfoPageAPI.List
}>()

const dataStatus = ref<ProductDataStatusEnums>(props.row.dataStatus as ProductDataStatusEnums)
const loading = ref(false)

const handleChangeStatus = async (row: ProductSkcInfoPageAPI.List) => {
  try {
    loading.value = true
    const dataStatus = row.dataStatus

    // 反选
    const reqDataStatus =
      dataStatus === ProductDataStatusEnums.Effective
        ? ProductDataStatusEnums.Invalid
        : ProductDataStatusEnums.Effective
    const { msg } = await productSkcInfoUpdateStatus({
      reqs: [{ id: props.row.id, dataStatus: reqDataStatus }]
    })

    ElMessage.success(msg)
    emits('update')
  } catch {
    dataStatus.value = row.dataStatus as ProductDataStatusEnums
  } finally {
    loading.value = false
  }
}
const imageUrl = computed(() => {
  return props.row.sampleUrl?.at(0)?.signatureUrl || ''
})

const contentRef = ref<HTMLElement | null>(null)
const content = ref<string>('')
const tooltipRef = ref<InstanceType<typeof ElTooltip>>()

const handleMouseover = (e: { clamped: boolean; text: string; currentTarget: HTMLElement }) => {
  if (!e.clamped) {
    return
  }
  contentRef.value = e.currentTarget
  content.value = e.text
}

const router = useRouter()
const emits = defineEmits(['update'])
</script>

<template>
  <ElCard
    :body-style="{ padding: '0px', width: '100%', height: '100%' }"
    class="card-container m-2"
    shadow="always"
    @click="router.push({ name: RouteNameEnums.Detail, params: { routerId: row.id } })"
  >
    <div class="card-body">
      <div class="card-content">
        <div class="card-img-container">
          <ElImage
            :src="imageUrl"
            class="card-img"
            hide-on-click-modal
            fit="cover"
            loading="lazy"
          />
        </div>
        <div class="card-button-container flex items-center justify-center">
          <ElSwitch
            v-model="dataStatus"
            v-hasPermi="['skc:changeStatus']"
            :active-value="ProductDataStatusEnums.Effective"
            :inactive-value="ProductDataStatusEnums.Invalid"
            :loading="loading"
            active-text="生效"
            inactive-text="作废"
            @click="handleChangeStatus(row)"
            @click.stop="() => {}"
          />
        </div>
      </div>
      <div class="card-content">
        <div class="card-info-container !flex-row">
          <div class="w-1/3">
            <ElImage
              :preview-src-list="[row.thumbnail?.signatureUrl!]"
              :src="row.thumbnail?.signatureUrl"
              fit="contain"
              hide-on-click-modal
              loading="lazy"
              style="width: 30px; height: 30px"
            />
          </div>
          <div class="w-1/3">
            <BaseClamp
              :max-lines="1"
              :text-content="row.colorName"
              autoresize
              @mouseover="handleMouseover"
            >
              {{ row.colorName }}
            </BaseClamp>
          </div>
          <div class="w-1/3">
            <BaseClamp
              :max-lines="1"
              :text-content="row.colorCode"
              autoresize
              @mouseover="handleMouseover"
            >
              {{ row.colorCode }}
            </BaseClamp>
          </div>
        </div>
        <div class="card-info-container">
          <BaseClamp
            :max-lines="1"
            :text-content="row.skcCode"
            autoresize
            @mouseover="handleMouseover"
          >
            {{ row.skcCode }}
          </BaseClamp>
        </div>
        <div class="card-info-container !flex-row flex-wrap">
          <div class="w-1/2">
            <BaseClamp
              :max-lines="1"
              :text-content="row.launchSeasonItemName"
              autoresize
              @mouseover="handleMouseover"
            >
              {{ row.launchSeasonItemName }}
            </BaseClamp>
          </div>
          <div class="w-1/2">
            <BaseClamp
              :max-lines="1"
              :text-content="row.developmentTypeItemName"
              autoresize
              @mouseover="handleMouseover"
            >
              {{ row.developmentTypeItemName }}
            </BaseClamp>
          </div>
          <div class="w-full">
            <BaseClamp
              :max-lines="1"
              :text-content="row.designerIdItemName"
              autoresize
              @mouseover="handleMouseover"
            >
              {{ row.designerIdItemName }}
            </BaseClamp>
          </div>
        </div>
        <div class="card-info-container !border-bottom-0 !flex-row">
          <div class="w-full">
            <BaseClamp
              :max-lines="1"
              :text-content="row.brandItemName"
              autoresize
              @mouseover="handleMouseover"
            >
              {{ row.brandItemName }}
            </BaseClamp>
          </div>
        </div>
        <div
          class="card-info-container !border-bottom-0 border-top-1 border-left-1 border-solid mt-[-2px] ml-[-1px]"
        >
          <div class="flex items-center h-1/2 w-full">
            <BaseClamp
              :max-lines="1"
              :text-content="`尺码段：${row.sizeRangeIdItemName?.join(',')}`"
              autoresize
              @mouseover="handleMouseover"
            >
              尺码段：{{ row.sizeRangeIdItemName?.join(',') }}
            </BaseClamp>
          </div>
          <div class="flex items-center h-1/2 w-full">
            <BaseClamp
              :max-lines="1"
              :text-content="`状态：${row.dataStatusItemName}`"
              autoresize
              @mouseover="handleMouseover"
            >
              状态：{{ row.dataStatusItemName }}
            </BaseClamp>
          </div>
        </div>
      </div>
    </div>
  </ElCard>
  <ElTooltip
    ref="tooltipRef"
    :content="content"
    :popper-options="{
      modifiers: [
        {
          name: 'computeStyles',
          options: {
            adaptive: false,
            enabled: false
          }
        }
      ]
    }"
    :virtual-ref="contentRef as Measurable"
    popper-class="singleton-tooltip"
    virtual-triggering
  />
</template>

<style lang="less" scoped>
.card-container {
  height: 200px;
  max-width: 330px;

  &:hover {
    cursor: pointer;
  }

  .card-body {
    display: flex;
    height: 100%;
    justify-content: space-between;

    > .card-content {
      width: 50%;
      font-size: 12px;

      > .card-img-container {
        position: relative;
        width: 100%;
        height: 80%;
        overflow: hidden;

        > .card-img {
          width: 100%;
          height: 100%;
        }

        > .card-status {
          position: absolute;
          top: 0;
          left: 0;
          border-radius: 0 var(--el-card-border-radius) var(--el-card-border-radius)
            var(--el-card-border-radius);
        }
      }

      > .card-button-container {
        height: 20%;
      }

      > .card-info-container {
        display: flex;
        width: 100%;
        height: 20%;
        padding: 0 8px;
        text-align: center;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        :deep(.el-image + .el-image) {
          margin-left: 8px;
        }
      }
    }
  }

  :deep(.el-switch) {
    --el-switch-off-color: #ff4949;
  }
}
</style>
