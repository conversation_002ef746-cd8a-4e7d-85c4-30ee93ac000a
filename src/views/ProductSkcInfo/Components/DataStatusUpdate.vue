<script lang="ts" setup>
import type {
  ProductSkcInfoPageAPI,
  ProductSkcInfoUpdateStatusAPI
} from '@/api/productSkcInfo/types'
import { productSkcInfoUpdateStatus } from '@/api/productSkcInfo'
import { PropType } from 'vue'
import { ElMessage } from 'element-plus'
import { CommonYesNoEnums, ProductDataStatusEnums } from '@/enums'
import { storeToRefs } from 'pinia'
import { useBasicLibraryDictStore } from '@/views/basic-library-manage/store/dict'

const useConst = () => {
  const { productDataStatusList } = storeToRefs(useBasicLibraryDictStore())

  const getProductDataStatusListByRow = (row: ProductSkcInfoPageAPI.List) => {
    const list: string[] = [ProductDataStatusEnums.Invalid, ProductDataStatusEnums.Effective]
    if (row.dataStatus && list.includes(row.dataStatus)) {
      return productDataStatusList.value.filter((e) => e.dictValue !== ProductDataStatusEnums.Draft)
    }
    return productDataStatusList.value
  }

  return {
    productDataStatusList,
    getProductDataStatusListByRow
  }
}

const { getProductDataStatusListByRow } = useConst()

const props = defineProps({
  modelValue: Boolean,
  selectedList: Array as PropType<ProductSkcInfoPageAPI.List[]>
})
const loading = ref(false)
const localSelectedList = ref<ProductSkcInfoPageAPI.List[]>([])

const productSkcInfoUpdateStatusParams = computed<ProductSkcInfoUpdateStatusAPI.Params>(() => {
  return {
    reqs: localSelectedList.value?.map((item) => ({
      id: item.id,
      dataStatus: item.dataStatus,
      invalidReason: item.invalidReason
    }))
  }
})

const handleEffectiveOrInvalidated = async () => {
  try {
    const isEmpty = localSelectedList.value.some((item) => !item.invalidReason)
    if (isEmpty) {
      return ElMessage.error('请填写变更原因！')
    }
    loading.value = true
    const { msg } = await productSkcInfoUpdateStatus(productSkcInfoUpdateStatusParams.value)
    ElMessage.success(msg)
    emits('done', productSkcInfoUpdateStatusParams.value?.reqs || [])
    emits('update:modelValue', false)
  } finally {
    loading.value = false
  }
}

watchEffect(() => {
  localSelectedList.value = []
  localSelectedList.value =
    props.selectedList?.map((item) => ({
      ...item,
      invalidReason: '',
      dataStatus:
        item.dataStatus === ProductDataStatusEnums.Draft
          ? ProductDataStatusEnums.Effective
          : item.dataStatus
    })) ?? []
})

const emits = defineEmits(['update:modelValue', 'done'])
</script>
<template>
  <Dialog
    :model-value="modelValue"
    title="操作确认"
    width="90vw"
    :parent-scroll="false"
    @update:model-value="emits('update:modelValue', $event)"
  >
    <div>
      <VTable :data="localSelectedList" max-height="600">
        <VxeColumn field="skcCode" min-width="110px" title="SKC编号" />
        <VxeColumn field="productNumber" min-width="110px" title="产品编号" />
        <VxeColumn field="brandItemName" min-width="80px" title="品牌" />
        <VxeColumn field="launchSeason" min-width="120px" title="开发季节" />
        <VxeColumn field="designerIdItemName" min-width="140px" title="设计师" />
        <VxeColumn field="developStageItemName" min-width="130px" title="产品阶段" />
        <VxeColumn field="sendWmsItemName" min-width="140px" title="最后一次下发WMS状态" />
        <VxeColumn field="downOrderFlag" min-width="100px" title="SKC下单状态" />
        <VxeColumn field="dataStatusItemName" min-width="80px" title="当前状态" />
        <VxeColumn min-width="200px" title="修改状态">
          <template #header> <span class="text-red-500 !mr-1">*</span>修改状态 </template>
          <template #default="{ row }: { row: ProductSkcInfoPageAPI.List }">
            <ElSelect
              v-if="row.orderPlaced === CommonYesNoEnums.No"
              v-model="row.dataStatus"
              :clearable="false"
            >
              <ElOption
                v-for="item in getProductDataStatusListByRow(row)"
                :key="item.dictValue"
                :label="item.dictCnName"
                :value="item.dictValue!"
              />
            </ElSelect>
            <span v-else>已下单SKC信息，需发起线上流程审批通过后，再修改数据；</span>
          </template>
        </VxeColumn>
        <VxeColumn min-width="200px">
          <template #header> <span class="text-red-500 !mr-1">*</span>变更原因 </template>
          <template #default="{ row }: { row: ProductSkcInfoPageAPI.List }">
            <ElInput v-model="row.invalidReason" maxlength="200" />
          </template>
        </VxeColumn>
      </VTable>
    </div>
    <template #footer>
      <ElButton @click="emits('update:modelValue', false)">取消</ElButton>
      <ElButton :loading="loading" type="primary" @click="handleEffectiveOrInvalidated">
        确认
      </ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped></style>
