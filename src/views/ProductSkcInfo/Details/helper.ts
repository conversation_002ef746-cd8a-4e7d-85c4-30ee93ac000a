import { DescriptionsSchema } from '@/types/descriptions'
import { ProductTaskNodeEnum } from '@/views/basic-library-manage/product-library/const'
export enum ActiveNameStatus {
  ProductDetails = 'productDetailsInfo',
  SkcDetails = 'skcDetailsInfo',
  ProductSelection = 'productSelectionInfo',
  Material = 'materialInfo',
  Customs = 'customsInfo',
  Sku = 'skuInfo',
  ImageInfo = 'imageInfo'
}

export const productDetailsInfo: DescriptionsSchema[] = [
  { field: 'productNumber', label: '产品编号', span: 2 },
  { field: 'brandItemName', label: '品牌' },
  { field: 'productCategoryItemName', label: '产品类目' },
  { field: 'launchSeasonItemName', label: '开发季节' },
  { field: 'productStyleItemName', label: '产品风格' },
  { field: 'targetAudienceItemName', label: '适用人群' },
  { field: 'styleStructureItemName', label: '款式结构' },
  { field: 'applicableSeasonItemName', label: '适用季节' },
  { field: 'expectedLaunchDate', label: '期望上架日期' },
  { field: 'lastsStandardItemName', label: '楦型标准' },
  { field: 'dtcItemName', label: '是否S2C' },
  { field: 'toeStandardItemName', label: '楦头标准' },
  { field: 'costRange', label: '成本区间($)' },
  { field: 'designPersonIdItemName', label: '产品企划' },
  { field: 'estimatedPrice', label: '预估售价($)' },
  { field: 'productPositioningItemName', label: '产品目标定级' },
  { field: 'vocOriginalStyleNumber', label: '母Style' },
  { field: 'productActualPositionItemName', label: '产品实际定级' },
  { field: 'productType', label: '产品类型' },
  { field: 'stylePositioningItemName', label: '款式定位' },
  { field: 'developmentChannelItemName', label: '开发渠道' },
  { field: 'childrenCrowdItemName', label: '儿童人群' },
  { field: 'childrenAvgGroupItemName', label: '儿童年龄段' },
  { field: 'sizeRangeIdItemName', label: '尺码段' },
  { field: 'sizeStandValue', label: '标准码' },
  { field: 'selectedSizeItemName', label: '尺码值' },
  { field: 'developmentTypeItemName', label: '开发类型' },
  { field: 'openingTypeItemName', label: '开口类型' },
  { field: 'developmentStrategyItemName', label: '开发策略' },
  { field: 'closureMethodItemName', label: '穿脱方式' },
  { field: 'safetyFeaturesItemName', label: '是否含金属鞋头' },
  { field: 'hasFeatureTagItemName', label: '是否有功能吊牌挂牌' },
  { field: 'ankleCoverageItemName', label: '是否过踝/过膝' },
  { field: 'specialPackagingMethod', label: '特殊包装方式' },
  { field: 'remarks', label: '备注' },
  { field: 'derivedType', label: '产品衍生类型' },
  { field: 'styleNumber', label: 'Style编号' },
  { field: 'modifyByIdItemName', label: '操作人' },
  { field: 'modifyTime', label: '操作时间' }
]
export const skcDetailsInfo = [
  { field: 'skcCode', label: 'SKC编号' },
  { field: 'colorCodeItemName', label: '产品配色' },
  { field: 'colorCode', label: '颜色编号' },
  // 对于包含图片的项，你可能需要特殊处理，这里只提供字段名
  { field: 'thumbnail', label: '颜色缩略图' },
  { field: 'mainFabricItemName', label: '主要面料' },
  { field: 'colorTypeItemName', label: '配色类型' },
  { field: 'createTime', label: '创建时间' },
  { field: 'modifyByIdItemName', label: '操作人' },
  // 注意'操作人'这个字段已经在前面的列表中出现过，确保不要重复添加
  { field: 'sendWmsItemName', label: '最后一次下发WMS状态' },
  { field: 'sendTime', label: '数据下发时间' },
  { field: 'downOrderFlag', label: 'SKC下单状态' },
  { field: 'orderCount', label: 'SKC下单数量' },
  { field: 'wmsColorName', label: 'WMS色号名称' },
  { field: 'errorDataMsg', label: '数据下发失败信息', span: 2 }
]

export const productSelectionInfo = [
  { field: 'skcCode', label: 'SKC编号' },
  { field: 'colorCodeItemName', label: '产品配色' },
  { field: 'colorCode', label: '颜色编号' },
  // 颜色缩略图字段包含图片，可能需要特殊处理
  { field: 'thumbnail', label: '颜色缩略图' },
  { field: 'mainFabricItemName', label: '主要面料' },
  { field: 'colorTypeItemName', label: '配色类型' },
  { field: 'totalChooseChannel', label: '选品渠道' },
  { field: 'totalExpectedOrder', label: '预估订单量' },
  { field: 'selectableItemName', label: '是否选中' },
  { field: 'skcStatusItemName', label: '状态' },
  {
    field: 'operationTime',
    label: '操作时间'
  },
  {
    field: 'operationPerson',
    label: '操作人'
  }
]
export const materialInfo = [
  { field: 'skcCode', label: 'SKC编号' },
  { field: 'colorCodeItemName', label: '产品配色' },
  { field: 'colorCode', label: '颜色编号' },
  // 包含图片的字段可能需要特殊处理
  { field: 'thumbnail', label: '颜色缩略图' },
  { field: 'materialItemName1', label: '面料1' },
  { field: 'materialItemName2', label: '面料2' },
  { field: 'materialItemName3', label: '面料3' },
  { field: 'materialItemName4', label: '面料4' },
  { field: 'liningMaterialCodeItemName', label: '里材料' },
  { field: 'liningSituationItemName', label: '里绒情况' },
  { field: 'paddingMaterialSurfaceCodeItemName', label: '垫材料' },
  { field: 'soleMaterialOutsoleCodeItemName', label: '底材料' },
  { field: 'percentPu', label: '橡胶/塑料%' },
  { field: 'percentTextile', label: '纺织物%' },
  { field: 'percentCorium', label: '真皮%' },
  { field: 'percentOther', label: '其他%' },
  {
    field: 'operationTime',
    label: '操作时间'
  },
  {
    field: 'operationPerson',
    label: '操作人'
  }
]
export const customsInfo = [
  { field: 'skcCode', label: 'SKC编号' },
  { field: 'colorCodeItemName', label: '产品配色' },
  { field: 'colorCode', label: '颜色编号' },
  // 包含图片的字段可能需要特殊处理
  { field: 'thumbnail', label: '颜色缩略图' },
  { field: 'mainFabricItemName', label: '主要面料' },
  { field: 'colorTypeItemName', label: '配色类型' },
  { field: 'createTime', label: '创建时间' },
  { field: 'velvetAppliedItemName', label: '是否植绒' },
  { field: 'velvetRequirements', label: '植绒要求' },
  { field: 'velvetTariff', label: '植绒/植皮关税率' },
  { field: 'unVelvetTariff', label: '不植关税率' },
  { field: 'operationTime', label: '操作时间' },
  { field: 'operationPerson', label: '操作人' }
]

export const skuInfo = (index: number): DescriptionsSchema[] => {
  const skcList = [
    { field: 'skcCode', label: 'SKC编号' },
    { field: 'colorEnName', label: '产品配色' },
    { field: 'colorCode', label: '颜色编号' },
    // 包含图片的字段可能需要特殊处理
    { field: 'thumbnail', label: '颜色缩略图' },
    { field: 'mainFabric', label: '主要面料' },
    { field: 'colorType', label: '配色类型' }
  ]
  const skuList = [
    { field: 'skuCode', label: 'SKU编号' },
    { field: 'skuPartnerCode', label: 'SKU Partner Code' },
    { field: 'sizeName', label: '选中尺码' },
    { field: 'outerBoxLong', label: '外箱长(cm)' },
    { field: 'outerBoxWidth', label: '外箱宽(cm)' },
    { field: 'outerBoxHeight', label: '外箱高(cm)' },
    { field: 'outerBoxRoughWeight', label: '外箱毛重(kg)' },
    { field: 'boxNumberText', label: '箱规' },
    { field: 'boxVolume', label: '外箱体积(m³)' },
    { field: 'shoeBoxWidth', label: '鞋盒宽(cm)' },
    { field: 'shoeBoxHeight', label: '鞋盒高(cm)' },
    { field: 'shoeBoxLong', label: '鞋盒长(cm)' },
    { field: 'shoeBoxRoughWeight', label: '鞋盒毛重(kg)' },
    { field: 'shoeBoxVolume', label: '鞋盒体积(m³)' },
    { field: 'sendWmsItemName', label: '最后一次下发WMS状态' },
    { field: 'sendTime', label: '数据下发时间' },
    { field: 'errorDataMsg', label: '数据下发失败信息' },
    { field: 'dataStatusItemName', label: 'SKU状态' }
  ]
  return index === 0 ? skcList.concat(skuList) : skuList
}
const ImageInfo = [
  { field: 'colorSchemeDraft', label: '配色图稿', span: 2 },
  { field: 'sample', label: '样品图', span: 2 },
  { field: 'initialProofResultImg', label: '初样样品图', span: 2 },
  { field: 'colorProofResultImg', label: '齐色样品图', span: 2 },
  { field: 'confirmProofResultImg', label: '确认样品图', span: 2 },
  { field: 'erpSkcImg', label: '白底图', span: 2 }
]

export const tabPanes = [
  { name: ActiveNameStatus.ProductDetails, label: '产品详细信息', schema: productDetailsInfo },
  { name: ActiveNameStatus.SkcDetails, label: 'SKC详细信息', schema: skcDetailsInfo },
  {
    name: ActiveNameStatus.ProductSelection,
    label: '选品会信息',
    schema: productSelectionInfo,
    productStage: [ProductTaskNodeEnum.PRODUCT_CONCLUSION]
  },
  {
    name: ActiveNameStatus.Material,
    label: '材质信息',
    schema: materialInfo,
    productStage: [ProductTaskNodeEnum.MATERIAL_INFORMATION]
  },
  { name: ActiveNameStatus.Customs, label: '关务信息', schema: customsInfo },
  { name: ActiveNameStatus.Sku, label: 'SKU信息', schema: skuInfo },
  { name: ActiveNameStatus.ImageInfo, label: '相关图片', schema: ImageInfo }
]
