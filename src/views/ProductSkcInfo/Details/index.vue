<script lang="ts" setup>
import { ActiveNameStatus, tabPanes } from './helper'
import { getSkcImgData, productSkcInfoDetail } from '@/api/productSkcInfo'
import { ProductSkcInfoDetailAPI, ProductSkcInfoImageInfoAPI } from '@/api/productSkcInfo/types'
import { DescriptionsSchema } from '@/types/descriptions'

defineOptions({
  name: 'ProductSkcInfoDetails'
})
const { currentRoute } = useRouter()
const routerId: string = currentRoute.value.params.routerId
const activeName = ref<ActiveNameStatus>(ActiveNameStatus.ProductDetails)
const detailsData = ref<ProductSkcInfoDetailAPI.Data>({
  skcInfoDetailResp: [{}]
})
const loading = ref(false)
const formData = ref<ProductSkcInfoImageInfoAPI.Params>()

const _productSkcInfoDetail = async () => {
  const { datas } = await productSkcInfoDetail(routerId)
  detailsData.value = datas ?? { skcInfoDetailResp: [{}] }
  detailsData.value.materialInfoList?.forEach((e, i) => {
    Object.keys(e).forEach((key) => {
      detailsData.value[key + (i + 1)] = e[key]
    })
  })
}

const _getImageContent = async () => {
  if (!routerId) return
  loading.value = true
  const [error, result] = await getSkcImgData(routerId)
  loading.value = false
  if (!error && result?.datas) {
    formData.value = result?.datas
  }
}
onMounted(() => {
  if (routerId) {
    _productSkcInfoDetail()
    _getImageContent()
  }
})
</script>

<template>
  <ContentWrap>
    <ElTabs v-model="activeName">
      <ElTabPane v-for="item in tabPanes" :key="item.name" :label="item.label" :name="item.name">
        <Descriptions
          label-width="8rem"
          v-if="item.name === ActiveNameStatus.ProductDetails"
          :data="detailsData.productInfoDetailResp"
          :schema="item.schema as DescriptionsSchema[]"
        >
          <template #costRange="{ row }: { row: ProductSkcInfoDetailAPI.Data }">
            {{ row.costRangeMin }} ~ {{ row.costRangeMax }}
          </template>
        </Descriptions>
        <Descriptions
          v-else-if="item.name === ActiveNameStatus.Sku"
          label-width="8rem"
          v-for="(detail, i) in detailsData?.skuList"
          :key="detail.id!"
          :data="detail"
          :schema="(item.schema as Function)(i) as DescriptionsSchema[]"
        >
          <template #skcCode>
            <span>{{ detailsData.skcCode }}</span>
          </template>
          <template #colorEnName>
            <span>{{ detailsData.colorCodeItemName }}</span>
          </template>
          <template #colorCode>
            <span>{{ detailsData.colorCode }}</span>
          </template>
          <template #mainFabric>
            <span>{{ detailsData.mainFabricItemName }}</span>
          </template>
          <template #colorType>
            <span>{{ detailsData.colorTypeItemName }}</span>
          </template>
          <template #thumbnail>
            <ElImage
              hide-on-click-modal
              :preview-src-list="[detailsData?.thumbnail?.signatureUrl!]"
              :src="detailsData?.thumbnail?.signatureUrl"
              class="!h-[80px]"
              loading="lazy"
            />
          </template>
        </Descriptions>
        <Descriptions
          label-width="8rem"
          :data="formData"
          :schema="item.schema as DescriptionsSchema[]"
          v-else-if="item.name == ActiveNameStatus.ImageInfo"
        >
          <template v-for="(imageItem, index) in item.schema" :key="index" #[imageItem.field]>
            <template
              v-for="itemScheme in formData?.[(imageItem as DescriptionsSchema).field]"
              :Key="itemScheme.id"
            >
              <ElImage
                :preview-src-list="
                 formData?.[(imageItem as DescriptionsSchema).field]?.map((e) => e.signatureUrl || '') || []
                "
                :src="itemScheme?.signatureUrl"
                class="!h-[80px]"
                hide-on-click-modal
                loading="lazy"
              />
            </template>
          </template>
        </Descriptions>

        <Descriptions
          label-width="8rem"
          v-else
          :data="detailsData"
          :schema="item.schema as DescriptionsSchema[]"
        >
          <template #operationTime="{ row }: { row: ProductSkcInfoDetailAPI.Data }">
            <div v-for="(e, i) in item.productStage" :key="e">
              <span
                v-if="
                  item.productStage &&
                  !row.productInfoDetailResp?.taskTimeRecord?.[item.productStage[i - 1]]
                "
              >
                {{ row.productInfoDetailResp?.taskTimeRecord?.[e]?.createTime }}
              </span>
            </div>
          </template>
          <template #operationPerson="{ row }: { row: ProductSkcInfoDetailAPI.Data }">
            <div v-for="(e, i) in item.productStage" :key="e">
              <span
                v-if="
                  item.productStage &&
                  !row.productInfoDetailResp?.taskTimeRecord?.[item.productStage[i - 1]]
                "
              >
                {{ row.productInfoDetailResp?.taskTimeRecord?.[e]?.createByIdItemName }}
              </span>
            </div>
          </template>
          <template #thumbnail="{ row }: { row: ProductSkcInfoDetailAPI.Data }">
            <ElImage
              hide-on-click-modal
              :preview-src-list="[row.thumbnail?.signatureUrl!]"
              :src="row.thumbnail?.signatureUrl"
              class="!h-[80px]"
              loading="lazy"
            />
          </template>
          <!--齐色样图片-->
          <template #sampleUrl="{ row }: { row: ProductSkcInfoDetailAPI.Data }">
            <ElImage
              v-for="(uploadValue, index) in row.sampleUrl || []"
              :key="index"
              hide-on-click-modal
              :preview-src-list="row.sampleUrl?.map((e) => e.signatureUrl!) || []"
              :src="uploadValue?.signatureUrl"
              class="!h-[80px] mr-1 mb-1"
              loading="lazy"
            />
          </template>
          <!--确认样图片-->
          <template #confirmSampleUrl="{ row }: { row: ProductSkcInfoDetailAPI.Data }">
            <ElImage
              v-for="(uploadValue, index) in row.confirmSampleUrl || []"
              :key="index"
              hide-on-click-modal
              :preview-src-list="row.confirmSampleUrl?.map((e) => e.signatureUrl!) || []"
              :src="uploadValue?.signatureUrl"
              class="!h-[80px]"
              loading="lazy"
            />
          </template>
          <!--跟进开发人员-->
          <template #developmentContactPersonId="{ row }: { row: ProductSkcInfoDetailAPI.Data }">
            {{ row.productInfoDetailResp?.developmentContactPersonIdItemName }}
          </template>
          <!--跟进技术人员-->
          <template #technicalContactPersonId="{ row }: { row: ProductSkcInfoDetailAPI.Data }">
            {{ row.productInfoDetailResp?.technicalContactPersonIdItemName }}
          </template>
        </Descriptions>
      </ElTabPane>
    </ElTabs>
  </ContentWrap>
</template>
<style scoped lang="less"></style>
