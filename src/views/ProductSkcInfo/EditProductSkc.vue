<script setup lang="ts">
import { DescriptionsSchema } from '@/types/descriptions'
import { ProductSkcInfoDetailAPI, ProductSkcInfoPageAPI } from '@/api/productSkcInfo/types'
import { ContentWrap } from '@/components/ContentWrap'
import type { FormInstance, FormRules } from 'element-plus'
import { editSkcImg, productSkcInfoDetail } from '@/api/productSkcInfo'

const skcDetailsInfo = [
  { field: 'skcCode', label: 'SKC编号' },
  { field: 'colorCodeItemName', label: '产品配色' },
  { field: 'colorCode', label: '颜色编号' },
  // 对于包含图片的项，你可能需要特殊处理，这里只提供字段名
  { field: 'thumbnail', label: '颜色缩略图' },
  { field: 'mainFabricItemName', label: '主要面料' },
  { field: 'colorTypeItemName', label: '配色类型' },
  { field: 'createTime', label: '创建时间' },
  { field: 'modifyByIdItemName', label: '操作人' },
  // 注意'操作人'这个字段已经在前面的列表中出现过，确保不要重复添加
  { field: 'sendWmsItemName', label: '最后一次下发WMS状态' },
  { field: 'sendTime', label: '数据下发时间' },
  { field: 'downOrderFlag', label: 'SKC下单状态' },
  { field: 'orderCount', label: 'SKC下单数量' },
  { field: 'wmsColorName', label: 'WMS色号名称' },
  { field: 'errorDataMsg', label: '数据下发失败信息', span: 2 }
]
defineOptions({
  name: 'EditProductSkc'
})
const { currentRoute } = useRouter()
const routerId = currentRoute.value.query.id as unknown as number
const submitLoading = ref<boolean>(false)
const formRef = ref<FormInstance>()
const detailsData = ref<ProductSkcInfoDetailAPI.Data>({
  colorSchemeDraftUrl: [],
  sampleUrl: []
})
const formRules = computed<FormRules<ProductSkcInfoPageAPI.Request>>(() => ({
  sampleUrl: [
    {
      required: true,
      message: '请上传样品图',
      trigger: 'blur'
    }
  ]
}))
const _GetSkcInfo = async () => {
  const { datas } = await productSkcInfoDetail(routerId)
  detailsData.value = datas ?? {
    skcInfoDetailResp: [{}]
  }
}
const handleSubmit = async () => {
  const valid = await formRef.value?.validate().catch(() => false)
  if (!valid) return
  const { colorSchemeDraftUrl, sampleUrl, id: skcId } = detailsData.value
  try {
    submitLoading.value = true
    await editSkcImg({ colorSchemeDraftUrl, sampleUrl, skcId })
    handleClose()
  } finally {
    submitLoading.value = false
  }
}
const handleClose = () => {
  useClosePage('ProductSkcInfo', {
    state: {
      skcCode: detailsData.value.skcCode
    }
  })
}
onBeforeMount(() => {
  if (routerId) {
    _GetSkcInfo()
  }
})
</script>

<template>
  <ContentWrap class="info-wrapper">
    <el-collapse :model-value="['1', '2']">
      <el-collapse-item name="1">
        <template #title>
          <div class="font-bold text-base">基本信息</div>
        </template>
        <Descriptions
          label-width="8rem"
          :data="detailsData"
          :schema="skcDetailsInfo as DescriptionsSchema[]"
        >
          <template #thumbnail>
            <ElImage
              :preview-src-list="[detailsData?.thumbnail?.signatureUrl!]"
              :src="detailsData?.thumbnail?.signatureUrl"
              class="!h-[80px]"
              hide-on-click-modal
              loading="lazy"
            />
          </template>
        </Descriptions>
      </el-collapse-item>
      <el-collapse-item name="2">
        <template #title>
          <div class="font-bold text-base">修改内容</div>
        </template>
        <ElForm ref="formRef" :model="detailsData" :rules="formRules">
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="配色图稿" prop="safeguardRightsStrength">
                <OssUpload
                  v-model="detailsData.colorSchemeDraftUrl"
                  :limit="10"
                  :size-limit="1024 * 1024 * 100"
                  accept="image/*"
                  drag
                  listType="picture-card"
                  multiple
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="样品图" prop="sampleUrl">
                <OssUpload
                  v-model="detailsData.sampleUrl"
                  :limit="10"
                  :size-limit="1024 * 1024 * 100"
                  accept="image/*"
                  drag
                  listType="picture-card"
                  multiple
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
      </el-collapse-item>
    </el-collapse>
  </ContentWrap>
  <ContentWrap class="mt-2">
    <div class="text-center">
      <ElButton @click="handleClose">返回</ElButton>
      <ElButton type="primary" :loading="submitLoading" @click="handleSubmit"> 确定 </ElButton>
    </div>
  </ContentWrap>
</template>

<style scoped lang="less">
:deep(.el-collapse-item__header) {
  margin-bottom: 10px;
  border-bottom: 2px solid var(--el-color-primary-light-3);
}
</style>
