<script lang="tsx" setup>
import {
  getAllSkcViews,
  productSkcInfoPage,
  productSkcInfoUpdateStatus
} from '@/api/productSkcInfo'
import { ProductSkcInfoPageAPI } from '@/api/productSkcInfo/types'
import {
  ElButton,
  ElCollapseTransition,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElMessage,
  ElPagination,
  ElRow,
  type FormInstance,
  type FormRules
} from 'element-plus'
import CardView from './Components/CardView.vue'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { ProductDataStatusEnum, YesNoEnum } from '@/views/basic-library-manage/const'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { sort as sortSize } from '@/utils'
import { Icon } from '@/components/Icon'
import { ref } from 'vue'
import type { VxeGridInstance, VxeGridProps } from 'vxe-table'
import CustomColumnsDialog from '@/views/basic-library-manage/product-library/components/CustomColumnsDialog.vue'
import { Column } from '@/views/basic-library-manage/product-library/const'
import type { Pager } from '@/views/basic-library-manage/api/common'
import { isEqual, omit } from 'lodash-es'
import { watchDebounced } from '@vueuse/core'
import { useHelpStore } from '@/views/basic-library-manage/store'
import { RouteNameEnums } from '@/views/ProductSkcInfo/helper'
import { SelectPlus } from '@/components/Business/SelectPlus'
import DataStatusUpdate from '@/views/ProductSkcInfo/Components/DataStatusUpdate.vue'
import { ViewListAPI } from '@/views/basic-library-manage/product-library/api/product-list'
import { hasPermission } from '@/directives/permission/hasPermi'
const sort = sortSize

defineOptions({
  name: 'ProductSkcInfo'
})

const props = defineProps<{
  isEmbed?: boolean
  isMaterial?: boolean
  isConfirm?: boolean
  productNumber?: string[]
}>()

const statusVisible = ref(false)
const productSkcInfoUpdateStatusParams = computed(() => {
  return {
    id: selectedRows.value.map((item) => item.id),
    sendWms: true
  }
})

const handleEffectiveOrInvalidated = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择一条数据')
    return
  }
  statusVisible.value = true
}

const handleIssue = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择一条数据')
    return
  }
  const { msg } = await productSkcInfoUpdateStatus(productSkcInfoUpdateStatusParams.value)
  ElMessage.success(msg)
}

const { formLabelLength } = useLocaleConfig([
  {
    formLabelLength: '150px'
  },
  {
    formLabelLength: '180px'
  }
])

const router = useRouter()

const { setSkcFormData } = useHelpStore()

type ITableItem = ProductSkcInfoPageAPI.List & { $formatedSizeStr: string }
const useQuery = () => {
  type FormModel = ProductSkcInfoPageAPI.Params
  const formRef = ref<FormInstance>()
  const tableRef = ref<VxeGridInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const formRules = ref<FormRules<FormModel>>({})
  const defaultFormData: FormModel & {
    date: [string, string]
  } = {
    brand: [],
    productNumber: [],
    launchSeason: [],
    date: ['', ''],
    designerId: [],
    productCategory: [],
    styleNumber: '',
    productName: '',
    developmentDirection: [],
    chooseChannel: [],
    mainChannelMark: [],
    developmentChannel: [],
    colorCode: []
  }
  let lastFormData = ref({
    ...defaultFormData
  })

  const formData = ref<ProductSkcInfoPageAPI.Params & { date: [string, string] }>({
    productNumber: props.isEmbed ? props.productNumber : [],
    skcStatus: props.isConfirm
      ? [ProductDataStatusEnum.EFFECT]
      : props.isEmbed
      ? [ProductDataStatusEnum.EFFECT, ProductDataStatusEnum.DRAFT]
      : [],
    date: ['', ''],
    brand: [],
    filterSkcCode: !props.isEmbed || props.isConfirm ? 1 : 0,
    colorCode: []
  })

  const handleDetail = (row: ProductSkcInfoPageAPI.List, type: 'product' | 'skc') => {
    setSkcFormData(lastFormData.value)
    if (type === 'skc') {
      router.push({ name: RouteNameEnums.Detail, params: { routerId: row.id } })
    } else {
      router.push({ name: 'ViewProduct', query: { id: row.productId } })
    }
  }

  const columnSettingRef = ref<InstanceType<typeof CustomColumnsDialog>>()
  const defaultColumns: Column[] = [
    {
      type: 'checkbox',
      fixed: 'left',
      width: 60
    },
    {
      type: 'seq',
      title: '序号',
      fixed: 'left',
      width: 60
    },
    {
      title: '产品编号',
      field: 'productNumber',
      fixed: 'left',
      minWidth: 150,
      cellRender: {
        name: 'Link',
        props: {
          clickFn: ({ row }: { row: ProductSkcInfoPageAPI.List }) => handleDetail(row, 'product')
        }
      }
    },
    {
      title: 'brandItemName',
      field: '品牌',
      minWidth: 80
    },
    {
      title: '产品类目',
      field: 'productCategoryItemName',
      minWidth: 120
    },
    {
      title: '开发季节',
      field: 'launchSeason',
      minWidth: 120
    },
    {
      title: 'SKC编号',
      field: 'skcCode',
      minWidth: 225,
      cellRender: {
        name: 'Link',
        props: {
          clickFn: ({ row }: { row: ProductSkcInfoPageAPI.List }) => handleDetail(row, 'skc')
        }
      }
    },
    {
      title: '产品配色',
      field: 'colorName',
      minWidth: 120
    },
    {
      title: 'WMS色号名称',
      field: 'wmsColorName',
      minWidth: 120
    },
    {
      title: '颜色编号',
      field: 'colorCode',
      minWidth: 120
    },
    {
      title: '颜色缩略图',
      field: 'thumbnail',
      minWidth: 150,
      cellRender: {
        name: 'Image'
      }
    },
    {
      title: '主要面料',
      field: 'mainFabricItemName',
      minWidth: 120
    },
    {
      title: '配色类型',
      field: 'colorTypeItemName',
      minWidth: 120
    },
    {
      title: '选品会结论',
      field: 'selectionMeetingResultItemName',
      minWidth: 120
    },
    {
      title: '初样样品图',
      field: 'initialProofResultImg',
      minWidth: 150,
      cellRender: {
        name: 'Image'
      }
    },
    {
      title: '齐色样样品图',
      field: 'colorProofResultImg',
      minWidth: 150,
      cellRender: {
        name: 'Image'
      }
    },
    {
      title: '确认样样品图',
      field: 'confirmProofResultImg',
      minWidth: 150,
      cellRender: {
        name: 'Image'
      }
    },
    {
      title: '白底图',
      field: 'erpSkcImgUrl',
      minWidth: 150,
      cellRender: {
        name: 'Image'
      }
    },
    {
      title: '尺码段',
      field: 'sizeRangeIdItemName',
      minWidth: 120
    },
    {
      title: '选中尺码',
      field: 'selectedSizeItemName',
      minWidth: 120
    },
    {
      title: '是否植绒',
      field: 'velvetAppliedItemName',
      minWidth: 120
    },
    {
      title: '植绒要求',
      field: 'velvetRequirements',
      minWidth: 120
    },
    {
      title: '植绒/植皮关税率',
      field: 'velvetTariff',
      minWidth: 120
    },
    {
      title: '不植关税率',
      field: 'unVelvetTariff',
      minWidth: 120
    },
    {
      title: '关务植绒/植皮备注',
      field: 'customsVelvetRemark',
      minWidth: 120
    },
    {
      title: '关务植绒/植皮建议',
      field: 'customsVelvetSuggestion',
      minWidth: 120
    },
    {
      title: '最后一次下发WMS状态',
      field: 'sendWmsItemName',
      minWidth: 120
    },
    {
      title: 'SKC下单状态',
      field: 'downOrderFlag',
      minWidth: 120
    },
    {
      title: 'SKC下单数量',
      field: 'orderCount',
      minWidth: 120
    },
    {
      title: '数据下发时间',
      field: 'sendTime',
      minWidth: 120
    },
    {
      title: '数据下发失败说明',
      field: 'errorDataMsg',
      minWidth: 120
    },
    {
      title: '创建时间',
      field: 'createTime',
      minWidth: 120
    },
    {
      title: '产品企划',
      field: 'designPersonIdItemName',
      minWidth: 120
    },
    {
      title: '产品设计师',
      field: 'designerIdItemName',
      minWidth: 120
    },
    {
      title: '产品阶段',
      field: 'developStageItemName',
      minWidth: 120
    },
    {
      title: '阶段完成说明',
      field: 'stageCompDesc',
      minWidth: 120
    },
    {
      title: '状态',
      field: 'dataStatusItemName',
      minWidth: 120
    },
    {
      title: '操作人',
      field: 'modifyByIdItemName',
      minWidth: 120
    },
    {
      title: '操作',
      field: 'operation',
      fixed: 'right',
      width: '80',
      slots: {
        default: ({ row }: { row: ProductSkcInfoPageAPI.Params }) => (
          <>
            {hasPermission('skc:edit') && row.ableUpdate ? (
              <ElDropdown>
                {{
                  default: () => (
                    <span class="el-dropdown-link">
                      操作
                      <Icon icon="ep:arrow-down" />
                    </span>
                  ),
                  dropdown: () => {
                    return (
                      <ElDropdownMenu>
                        {hasPermission('skc:edit') ? (
                          <ElDropdownItem onClick={() => handleEditSkc(row)}>
                            修改SKC
                          </ElDropdownItem>
                        ) : null}
                      </ElDropdownMenu>
                    )
                  }
                }}
              </ElDropdown>
            ) : null}
          </>
        )
      }
    }
  ]
  const columns = ref([...defaultColumns])
  const tableData = ref<ITableItem[]>([])
  const pager = ref<Pager>({
    current: 1,
    size: 50,
    total: 0
  })
  const tableOptions = computed<VxeGridProps<ProductSkcInfoPageAPI.List>>(() => ({
    minHeight: 100,
    columns: columns.value,
    data: tableData.value,
    maxHeight: maxHeight.value - 30,
    showOverflow: 'tooltip',
    scrollX: {
      enabled: true
    },
    scrollY: {
      enabled: true,
      gt: 20
    },
    loading: queryLoading.value,
    cellConfig: {
      height: 60
    }
  }))
  const queryLoading = ref(false)
  const queryParams = computed<ProductSkcInfoPageAPI.Request>(() => {
    const formValue = formData.value
    const formDate = formValue.date
    return {
      ...omit(formValue, 'date'),
      ...pager.value,
      productCategory: formValue.productCategory?.map((item) => item.toString()),
      startTime: formDate?.[0],
      endTime: formDate?.[1],
      idList: formData.value?.idList || []
    }
  })

  let controller: AbortController | null = null
  const handleQuery = async () => {
    const valid = await formRef.value?.validate()
    if (!valid) {
      return
    }
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData.value, formData.value)) {
      pager.value.current = 1
    }
    const result = await productSkcInfoPage(queryParams.value, controller.signal)
    queryLoading.value = false
    selectedRows.value = []
    if (result?.datas) {
      lastFormData.value = { ...formData.value }
      const { records } = result.datas
      if (Array.isArray(records)) {
        tableData.value = records.map((item) => {
          // 避免用cellRender处理selectedSizeItemName
          let formatedSizeStr = ''
          if (item.selectedSizeItemName) {
            formatedSizeStr = sort(item.selectedSizeItemName).join(', ')
          }
          return {
            ...item,
            $formatedSizeStr: formatedSizeStr
          }
        })
      } else {
        tableData.value = []
      }
      await nextTick()
      await tableRef.value?.loadData([])
      await tableRef.value?.loadData(tableData.value)
      pager.value.total = result.datas?.pager?.total || 0
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
  }

  const handleEditSkc = (row: ProductSkcInfoPageAPI.Params) => {
    router.push({
      name: 'EdiProductSkc',
      query: {
        id: row?.id
      }
    })
  }
  enum ExportTypeEnum {
    SKC_DATA = 'skc-export',
    SKC_IMAGE = 'skc-img-export'
  }

  const exportList = [
    {
      label: '导出SKC数据',
      value: ExportTypeEnum.SKC_DATA
    }
  ]
  exportList.push({
    label: '导出SKC图片',
    value: ExportTypeEnum.SKC_IMAGE
  })

  const { handleExport: exportFn, loading: dataExportLoading } = useOmsExport()

  function handleSkcDataExport() {
    let reqParam: string
    if (selectedRows && selectedRows.value?.length > 0) {
      const idList = selectedRows.value.map((item) => item.id)
      reqParam = JSON.stringify({ idList, exportConfigViewId: currentView.value.id })
    } else {
      reqParam = JSON.stringify({
        ...queryParams.value,
        exportConfigViewId: currentView.value.id
      })
    }
    exportFn({
      exportType: ExportTypeEnum.SKC_DATA,
      reqParam
    })
  }

  const imageExportLoading = ref(false)

  async function handleSkcImageExport() {
    let reqParam: string
    if (selectedRows && selectedRows.value?.length > 0) {
      const idList = selectedRows.value.map((item) => item.id)
      reqParam = JSON.stringify({ idList })
    } else {
      reqParam = JSON.stringify({
        ...queryParams.value
      })
    }
    exportFn({
      exportType: ExportTypeEnum.SKC_IMAGE,
      reqParam
    })
  }

  async function handleExport(exportType: ExportTypeEnum) {
    const valid = await formRef.value?.validate()
    if (!valid) {
      return
    }
    if (exportType === ExportTypeEnum.SKC_DATA) {
      handleSkcDataExport()
    } else {
      handleSkcImageExport()
    }
  }

  const visible = ref(false)
  const setVisible = async () => {
    visible.value = !visible.value
    await nextTick()
    tableRef.value?.recalculate()
  }

  const maxHeight = useTableHeight({
    pagerRef,
    tableRef: tableRef,
    offsetBottom: props.isEmbed ? 50 + 20 + 32 + 10 + 30 + 50 : 50
  })

  return {
    formRef,
    tableRef,
    pagerRef,
    formRules,
    formData,
    lastFormData,
    tableData,
    tableOptions,
    pager,
    queryLoading,
    queryParams,
    handleQuery,
    handleReset,
    exportList,
    handleExport,
    dataExportLoading,
    imageExportLoading,
    visible,
    setVisible,
    maxHeight,
    columns,
    handleDetail,
    defaultColumns,
    columnSettingRef
  }
}

const {
  formRef,
  tableRef,
  pagerRef,
  formRules,
  formData,
  tableData,
  tableOptions,
  pager,
  queryLoading,
  handleQuery,
  handleReset,
  exportList,
  handleExport,
  dataExportLoading,
  imageExportLoading,
  visible,
  setVisible,
  maxHeight,
  columns,
  defaultColumns,
  columnSettingRef
} = useQuery()

onMounted(() => {
  handleQuery()
  watchDebounced(
    formData,
    async () => {
      await handleQuery()
    },
    { deep: true, debounce: 400 }
  )
})
onActivated(() => {
  history.state?.skcCode && (formData.value.skcCode = history.state?.skcCode)
})
const useOperation = () => {
  const selectedRows = ref<ProductSkcInfoPageAPI.List[]>([])
  const defaultViewList: ViewListAPI.List = [
    {
      name: '图形视图',
      fieldAttr: []
    }
  ]
  const viewList = ref<ViewListAPI.List>([...defaultViewList])
  const currentView = ref<ViewListAPI.Row>(viewList.value[0])
  const isShowList = computed(() => {
    return currentView.value.name !== '图形视图'
  })
  const customColumnsDialogVisible = ref(false)
  const fetchAllViews = async (viewId?: number) => {
    const [error, result] = await getAllSkcViews()
    if (error === null && result?.datas) {
      viewList.value = defaultViewList.concat(result.datas || [])
      if (typeof viewId === 'number') {
        const updatedView = viewList.value.find((item) => item.id === viewId)
        if (updatedView) {
          currentView.value = updatedView
          return
        }
      }
      currentView.value = viewList.value[1] || viewList.value[0]
    }
  }

  onMounted(() => {
    fetchAllViews()
  })

  watch(
    () => currentView.value,
    (val) => {
      selectedRows.value = []
      const allColumns = columnSettingRef.value?.allColumns
      const currentViewColumns = Array.isArray(val.fieldAttr)
        ? val.fieldAttr.map((field, index) => {
            const column = defaultColumns.find((item) => item.field === field)
            if (column) {
              return column
            }
            const columnImgFlag = allColumns?.find((item) => {
              return item.attrField === field
            })?.imgFlag
            const isImgColumn = columnImgFlag === parseInt(YesNoEnum.Y, 10)

            const defaultColumnConfig = defaultColumns.find((item) => item.field === field)
            const fieldTitle = val.fieldTitle?.[index] || field
            const minWidth = defaultColumnConfig?.minWidth || 100
            const itemRender = {
              ...defaultColumnConfig,
              field: field,
              title: fieldTitle,
              minWidth,
              ...(isImgColumn && {
                cellRender: { name: 'Image' }
              })
            }
            if (index == 1 || index == 2) {
              itemRender.fixed = 'left'
            }
            return itemRender
          })
        : []
      currentViewColumns.unshift(defaultColumns[0])
      columns.value = currentViewColumns
    }
  )

  const handleCheckedChange = () => {
    const checked: ProductSkcInfoPageAPI.List[] | undefined = tableRef.value?.getCheckboxRecords()
    selectedRows.value = checked || []
  }

  // 配置视图
  const handleConfigView = () => {
    customColumnsDialogVisible.value = true
  }

  return {
    isShowList,
    viewList,
    currentView,
    selectedRows,
    fetchAllViews,
    handleConfigView,
    customColumnsDialogVisible,
    handleCheckedChange
  }
}
const {
  isShowList,
  viewList,
  currentView,
  selectedRows,
  fetchAllViews,
  handleConfigView,
  customColumnsDialogVisible,
  handleCheckedChange
} = useOperation()
const handleDone = (data: []) => {
  const idList = data.map((item) => item.id)
  formData.value.idList = idList
  handleQuery()
}
defineExpose({
  tableRef: tableRef
})
</script>
<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_220px)] overflow-x-hidden overflow-y-auto">
        <ElForm
          ref="formRef"
          :model="formData"
          :rules="formRules"
          :label-width="formLabelLength"
          @submit="
            (e) => {
              e.preventDefault()
            }
          "
        >
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="产品编号" prop="productNumber">
                <SelectPlus
                  v-model="formData.productNumber"
                  :disabled="isEmbed && !isMaterial"
                  api-key="getProductNumberList"
                  filterable
                  multiple
                  virtualized
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="开发季节" prop="launchSeason">
                <SelectPlus
                  v-model="formData.launchSeason"
                  api-key="COMMON_MARKET_SEASON"
                  cache
                  filterable
                  multiple
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElScrollbar class="w-full" style="height: 32px">
                <ElFormItem class="form-item-no-wrap" label="" label-width="0">
                  <ElButton text @click="setVisible">
                    {{ visible ? '收起' : '展开' }}
                    <Icon
                      :class="visible ? 'rotate-90' : ''"
                      class="transform transition duration-400"
                      icon="ant-design:down-outlined"
                    />
                  </ElButton>
                  <ElButton
                    :loading="queryLoading"
                    type="primary"
                    @click="handleQuery"
                    class="w-16"
                    native-type="submit"
                  >
                    <Icon v-show="!queryLoading" class="mr-1" icon="ep:search" />
                    查询
                  </ElButton>
                  <ElButton
                    :loading="queryLoading"
                    @click="handleReset"
                    class="w-16"
                    native-type="reset"
                  >
                    <Icon v-show="!queryLoading" class="mr-1" icon="ep:refresh-right" />
                    重置
                  </ElButton>
                  <ElDropdown v-if="!isEmbed" class="ml-3" @command="handleExport">
                    <ElButton
                      :loading="dataExportLoading || imageExportLoading"
                      type="primary"
                      class="w-16"
                    >
                      <Icon class="mr-1" icon="ep:upload-filled" />
                      导出
                    </ElButton>
                    <template #dropdown>
                      <ElDropdownMenu>
                        <ElDropdownItem
                          v-for="item in exportList"
                          :key="item.value"
                          :command="item.value"
                        >
                          {{ item.label }}
                        </ElDropdownItem>
                      </ElDropdownMenu>
                    </template>
                  </ElDropdown>
                </ElFormItem>
              </ElScrollbar>
            </ElCol>
            <ElCol :span="16">
              <ElFormItem label="品牌" prop="brand">
                <SelectPlus
                  v-model="formData.brand"
                  api-key="baseBrand"
                  cache
                  checkbox
                  checkbox-button
                  filterable
                  multiple
                />
              </ElFormItem>
            </ElCol>

            <ElCollapseTransition>
              <ElCol :span="16">
                <div v-show="visible" class="grid grid-cols-2 items-start w-full">
                  <ElFormItem label="Style编号" prop="styleNumber">
                    <SelectPlus
                      v-model="formData.styleNumber"
                      api-key="getStyleNumberList"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      placeholder="请选择"
                      virtualized
                    />
                  </ElFormItem>
                  <ElFormItem label="产品配色">
                    <SelectPlus
                      v-model="formData.colorCode"
                      :params="{ productNumber: formData.productNumber }"
                      api-key="colorDrop"
                      filterable
                      ganged
                      multiple
                      not-required-params
                      virtualized
                    />
                  </ElFormItem>

                  <ElFormItem label="SKC编号" prop="skcCode">
                    <ElInput
                      class="w-48"
                      v-model="formData.skcCode"
                      clearable
                      placeholder="请输入"
                    />
                  </ElFormItem>
                  <ElFormItem label="产品尺码段">
                    <SelectPlus
                      v-model="formData.sizeRangeId"
                      api-key="sizeList"
                      filterable
                      multiple
                    />
                  </ElFormItem>
                  <ElFormItem label="设计师">
                    <CascadeSelector
                      v-model="formData.designerId"
                      :props="{ emitPath: false, multiple: true }"
                      api-key="allUsers"
                      cache
                    />
                  </ElFormItem>
                  <ElFormItem label="创建时间">
                    <ElDatePicker
                      class="max-w-48"
                      v-model="formData.date"
                      end-placeholder="结束时间"
                      placeholder="请选择"
                      start-placeholder="开始时间"
                      type="daterange"
                      value-format="YYYY-MM-DD"
                    />
                  </ElFormItem>
                  <ElFormItem label="开发渠道" prop="developmentChannel">
                    <SelectPlus
                      v-model="formData.developmentChannel"
                      api-key="PRODUCT_DEV_CHANNELS"
                      multiple
                      clearable
                      filterable
                      placeholder="请选择开发渠道"
                    />
                  </ElFormItem>
                  <ElFormItem label="产品类目">
                    <CascadeSelector
                      v-model="formData.productCategory"
                      :props="{ emitPath: false, multiple: true }"
                      api-key="productCategoryDrop"
                    />
                  </ElFormItem>
                  <ElFormItem label="主渠道标识" prop="mainChannelMark">
                    <SelectPlus
                      v-model="formData.mainChannelMark"
                      api-key="PRODUCT_CHANNEL_IDENTIFICATION"
                      multiple
                      cache
                      clearable
                      filterable
                      placeholder="请选择主渠道标识"
                    />
                  </ElFormItem>
                  <ElFormItem label="选品渠道" prop="chooseChannel">
                    <SelectPlus
                      v-model="formData.chooseChannel"
                      api-key="PRODUCT_DEV_CHANNELS"
                      multiple
                      cache
                      clearable
                      filterable
                      placeholder="请选择选品渠道"
                    />
                  </ElFormItem>
                  <ElFormItem label="产品系列" prop="developmentDirection">
                    <SelectPlus
                      v-model="formData.developmentDirection"
                      multiple
                      api-key="PRODUCT_SERIES"
                      cache
                      clearable
                      filterable
                      placeholder="请选择产品系列"
                    />
                  </ElFormItem>
                  <ElFormItem label="PRODUCT NAME" prop="productName">
                    <ElInput
                      class="w-48"
                      v-model="formData.productName"
                      clearable
                      maxlength="100"
                      placeholder="请输入"
                      show-word-limit
                    />
                  </ElFormItem>
                  <ElFormItem label="产品阶段">
                    <SelectPlus
                      v-model="formData.developStage"
                      api-key="PRODUCT_STAGE"
                      cache
                      filterable
                      multiple
                    />
                  </ElFormItem>
                  <ElFormItem label="状态">
                    <SelectPlus
                      v-model="formData.skcStatus"
                      :disabled="isEmbed"
                      api-key="PRODUCT_DATA_STATUS"
                      cache
                      filterable
                      multiple
                    />
                  </ElFormItem>
                  <ElFormItem label="Style(WMS)" prop="styleWms">
                    <SelectPlus
                      v-model="formData.styleWms"
                      api-key="getStyleWmsList"
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      filterable
                      multiple
                      placeholder="请选择"
                      virtualized
                    />
                  </ElFormItem>
                </div>
              </ElCol>
            </ElCollapseTransition>
          </ElRow>
        </ElForm>
        <VxeToolbar v-if="!isEmbed">
          <template #buttons>
            <ElButton
              v-hasPermi="['skc:changeStatus']"
              type="primary"
              @click="handleEffectiveOrInvalidated"
            >
              <Icon icon="ion:switch" />
              <span class="text-[14px]">生效/作废</span>
            </ElButton>
            <ElButton v-hasPermi="['sendWMS']" type="primary" @click="handleIssue">
              <Icon icon="ep:position" />
              <span class="text-[14px]"> 下发SKC / SKU 到WMS</span>
            </ElButton>
          </template>
          <template #tools>
            <div class="flex flex-nowrap">
              <ElSelect v-model="currentView" class="min-w-36 mr-1" filterable value-key="name">
                <ElOption
                  v-for="item in viewList"
                  :key="item.name"
                  :label="item.name"
                  :value="item"
                />
              </ElSelect>
              <ElButton type="primary" @click="handleConfigView">
                <Icon icon="uil:setting" />
                <span class="text-[14px]">配置视图</span>
              </ElButton>
            </div>
          </template>
        </VxeToolbar>
        <div>
          <template v-if="!isShowList">
            <div
              :style="{ maxHeight: maxHeight + 'px' }"
              class="grid justify-center min-h-[140px] grid-gap-[10px] grid-cols-[repeat(auto-fit,minmax(300px,1fr))] overflow-auto"
            >
              <CardView v-for="row in tableData" :key="row.id" :row="row" @update="handleQuery" />
            </div>
          </template>
          <template v-else>
            <VxeGrid
              ref="tableRef"
              v-bind="tableOptions"
              @checkbox-change="handleCheckedChange"
              @checkbox-all="handleCheckedChange"
            />
          </template>
        </div>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
      <CustomColumnsDialog
        ref="columnSettingRef"
        v-model="customColumnsDialogVisible"
        :view-list="viewList"
        column-type="skc"
        @refresh="(viewId) => fetchAllViews(viewId)"
      />
      <DataStatusUpdate v-model="statusVisible" :selected-list="selectedRows" @done="handleDone" />
    </div>
  </ContentWrap>
</template>
