<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { Search } from '@/components/Search'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { reactive, ref } from 'vue'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { getCategaryApi, getListBrandApi } from '@/api/common'
import { getNoticeListApi, releaseApi } from '@/api/notice'
import type { RecordElement } from '@/api/notice/types'
import { useRouter } from 'vue-router'
import { Close } from './components/index'
import { PageTypeEnum } from '@/utils/enum'

type CurrentType =
  | PageTypeEnum.VIEW
  | PageTypeEnum.EDIT
  | PageTypeEnum.ADD
  | PageTypeEnum.RESET
  | PageTypeEnum.ALLOCATE

const { push } = useRouter()
const searchRef = ref()
const { register, tableObject, methods } = useTable({
  getListApi: getNoticeListApi,
  processedParameter: {
    timeField: {
      releaseTime: ['releaseTimeStart', 'releaseTimeEnd']
    },
    inputRange: {
      cost: ['costStartStart', 'costStartEnd']
    },
    lastCategoryId: ['productTypeList']
  }
})
const { getList, setSearchParams, getSelections } = methods
onMounted(() => {
  getList()
})
const crudSchemas = reactive<CrudSchema[]>([
  {
    field: '#',
    type: 'checkbox',
    label: '',
    width: 50
  },
  {
    type: 'seq',
    label: '序号',
    width: 50
  },
  {
    field: 'code',
    label: '公告编号',
    minWidth: 180,
    cellRender: {
      name: 'LinkColumn',
      events: { click: ({ row }) => handleAdd(PageTypeEnum.VIEW, row) }
    }
  },
  {
    field: 'image',
    label: '公告主图',
    width: 78,
    cellRender: {
      name: 'ImageColumn',
      props: {
        url: ({ row }) => {
          return row?.mainPic_url[0]?.url
        }
      }
    }
  },
  {
    field: 'brandName',
    label: '品牌',
    'min-width': 90,
    search: {
      field: 'brandIds',
      show: true,
      component: 'CheckboxButton',
      index: 3,
      api: getListBrandApi,
      colProps: { span: 24 },
      value: [],
      componentProps: {
        optionsAlias: {
          valueField: 'brandName',
          labelField: 'id'
        }
      }
    }
  },

  {
    field: 'seekingStage_zh',
    label: '公告阶段',
    'min-width': 100,
    search: {
      field: 'seekingStageList',
      show: true,
      component: 'CheckboxButton',
      index: 5,
      colProps: { span: 24 },
      value: [],
      dictName: 'SEEKING_STAGE',
      componentProps: {}
    }
  },
  {
    field: 'noticeCategoryDict_zh',
    label: '公告分类',
    'min-width': 100,
    search: {
      field: 'noticeCategoryDictList',
      show: true,
      component: 'CheckboxButton',
      dictName: 'SEEKING_CATEGORY',
      index: 4,
      colProps: { span: 24 },
      value: [],
      componentProps: {}
    }
  },
  {
    field: 'noticeTopic',
    label: '公告主题',
    minWidth: 120,
    colProps: { span: 8 },
    search: {
      show: true,
      index: 0,
      componentProps: {
        placeholder: '输入主题信息，支持模糊搜索',
        onBlur: () => {
          handleBlur()
        }
      }
    }
  },
  {
    field: 'developSeason_zh',
    label: '开发季节',
    minWidth: 80,
    colProps: { span: 8 },
    search: {
      field: 'developSeasonList',
      show: true,
      component: 'Select',
      index: 2,
      dictName: 'DEVELOP_SEASON',
      componentProps: {
        filterable: true,
        multiple: true,
        collapseTags: true
      }
    }
  },
  {
    field: 'productTypeList',
    label: '产品类别',
    'min-width': 100,
    search: {
      show: true,
      index: 20,
      component: 'Cascader',
      api: getCategaryApi,
      colProps: { span: 8 },
      value: [],
      componentProps: {
        props: {
          multiple: true,
          collapseTags: true,
          label: 'categoryName',
          value: 'id',
          children: 'sonCategory'
        },
        filterable: true
      }
    },
    formatter: ({ row }) => {
      let templateString = ``
      row.productType?.map((v) => {
        templateString += `${v.nameArray}

    `
      })
      return templateString
    }
  },
  {
    field: 'returnTypeI18',
    label: '年龄段',
    width: 100,
    formatter: ({ row }) => {
      return `${row.ageStart ?? ''}-${row.ageEnd ?? ''}`
    }
  },
  {
    field: 'styleBody_zh',
    label: '风格',
    'min-width': 80,
    search: {
      field: 'styleBodyList',
      show: true,
      component: 'CheckboxButton',
      dictName: 'STYLE_BODY',
      index: 6,
      colProps: { span: 24 },
      value: [],
      componentProps: {}
    }
  },
  {
    field: 'position_zh',
    label: '定位',
    'min-width': 80
  },

  {
    field: 'scene',
    label: '场景',
    'min-width': 80
  },
  {
    field: 'shoesTree_zh',
    label: '楦型',
    minWidth: 80
  },

  {
    field: 'model_zh',
    label: '型体',
    'min-width': 80
  },

  {
    field: 'colorRequire',
    label: '颜色要求',
    width: 100
  },
  {
    field: 'returnFactoryTypeI18',
    label: '尺码段',
    'min-width': 80,
    formatter: ({ row }) => {
      return `${row.sizeStart || ''}-${row.sizeEnd || ''}`
    }
  },
  {
    field: 'popularElement',
    label: '流行元素',
    'min-width': 80
  },
  {
    field: 'mainMaterial',
    label: '主要材质',
    'min-width': 80
  },
  {
    field: 'returnFactoryTypeI18',
    label: '首单量(双)',
    'min-width': 90,
    formatter: ({ row }) => {
      return `${row.firstPairStart || ''}-${row.firstPairEnd || ''}`
    }
  },
  {
    field: 'cost',
    label: '成本区间(¥)',
    'min-width': 100,
    search: {
      colProps: { span: 8 },
      show: true,
      component: 'ZoneTime',
      index: 12,
      componentProps: {
        precision: 1
      }
    },
    formatter: ({ row }) => {
      return `${row.costStart || ''}-${row.costEnd || ''}`
    }
  },
  {
    field: 'noticeManager',
    label: '对接供管',
    'min-width': 80,
    formatter: ({ row }) => {
      return row.noticeManager.map((v) => v.userName)
    }
  },
  {
    field: 'area_zh',
    label: '区域',
    'min-width': 80
  },
  {
    field: 'vendor',
    label: '分配供应商',
    'min-width': 100,
    formatter: ({ row }) => {
      return row.vendor.map((v) => v.vendorName)
    }
  },
  {
    field: 'releaseTime',
    label: '公告发布时间',
    minWidth: 120,
    colProps: { span: 8 },
    search: {
      label: '公告时间段',
      show: true,
      index: 1,
      component: 'DatePicker',
      componentProps: {
        type: 'daterange',
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        onChange: () => {
          handleBlur()
        }
      }
    }
  },
  {
    field: 'endTime',
    label: '公告截止日',
    minWidth: 100
  },
  {
    field: 'pushNumber',
    label: '已推款数量',
    minWidth: 100
  },

  {
    field: 'modifyByName',
    label: '操作人',
    minWidth: 100
  },

  {
    field: 'modifyTime',
    label: '操作时间',
    minWidth: 100
  },
  {
    field: 'stateDict_zh',
    label: '公告状态',
    search: {
      field: 'states',
      show: true,
      component: 'CheckboxButton',
      dictName: 'NOTICE_STATE',
      index: 7,
      colProps: { span: 24 },
      value: [],
      componentProps: {}
    },
    minWidth: 100,
    fixed: 'right'
  }
])
const { allSchemas } = useCrudSchemas(crudSchemas)
const multipleTable = ref<Array<RecordElement>>([])
const closeDialogVisible = ref(false)
const handleClose = async () => {
  const selectionList: RecordElement[] = await getSelections()

  const isAnyClosedOrEnded = selectionList.some((v) => ['4', '9'].includes(v.stateDict as string))

  if (selectionList.length === 0 || isAnyClosedOrEnded) {
    ElMessage.warning('请选择【非关闭、非结束】的寻源信息状态数据操作')
    return
  }

  multipleTable.value = selectionList
  closeDialogVisible.value = true
}

const handleRelease = async () => {
  const selectionList: Array<RecordElement> = await getSelections()
  if (!selectionList.length || selectionList.some((v) => v.stateDict !== '0'))
    return ElMessage.warning('请勾选【草稿】状态数据操作')
  const ids: Array<number> = []
  selectionList.map((v) => ids.push(v.id as number))
  ElMessageBox({
    showCancelButton: true,
    title: '操作确认',
    message: h('p', null, [
      h(
        'div',
        { style: 'font-weight:600;font-size:15px' },
        `您选择了【${selectionList.length}】条公告信息，确定进行发布吗？`
      ),
      h('div', null, '发布信息成功后会推送到供管进行分派和跟进。')
    ])
  }).then(async () => {
    await releaseApi({ ids })
    ElMessage.success('操作成功')
    getList()
  })
}
const handleAdd = async (pageType: CurrentType, row: RecordElement | void) => {
  const warningMessages: Record<CurrentType, string> = {
    view: '',
    add: '',
    edit: '请勾选一条【草稿】状态数据操作',
    allocate: '请勾选一条【已发布 / 已分配 / 生效中】状态数据操作',
    reset: '请勾选一条【已结束 / 已关闭】状态数据操作'
  }

  const selectionList: Array<RecordElement> = await getSelections()

  const isValidSelection = (expectedState: string[]) =>
    selectionList.length === 1 && expectedState.includes(selectionList[0].stateDict as string)

  switch (pageType) {
    case 'edit':
      if (!isValidSelection(['0'])) return ElMessage.warning(warningMessages[pageType])
      break
    case 'allocate':
      if (!isValidSelection(['1', '2', '3'])) return ElMessage.warning(warningMessages[pageType])
      break
    case 'reset':
      if (!isValidSelection(['4', '9'])) return ElMessage.warning(warningMessages[pageType])
      break
    default:
      break
  }

  push({
    name: 'SourceRequirementAdd',
    params: { pageType },
    query: { id: row?.id || selectionList[0]?.id }
  })
}

const handleBlur = async () => {
  handleSearch()
}

const handleSearch = async () => {
  const formData = (await searchRef?.value?.getFormData()) || {}
  const params = Object.assign({}, formData)
  const developSeasonList = params.developSeasonList ? params.developSeasonList : []
  params.developSeasonList = developSeasonList
  setSearchParams(params)
}
</script>

<template>
  <ContentWrap>
    <Search
      ref="searchRef"
      :colNum="3"
      :queryLoading="tableObject.loading"
      :schema="allSchemas.searchSchema"
      label-width="90px"
      @reset="handleSearch"
      @search="handleSearch"
    />

    <div class="mb-[10px]">
      <ElButton type="primary" @click="handleAdd(PageTypeEnum.ADD)">
        <Icon class="mr-1" icon="ep:plus" />
        新增公告
      </ElButton>
      <ElButton type="primary" @click="handleAdd(PageTypeEnum.EDIT)">
        <Icon class="mr-1" icon="ep:edit" />
        修改公告
      </ElButton>
      <ElButton type="primary" @click="handleRelease">
        <Icon class="mr-1" icon="ep:position" />
        发布公告
      </ElButton>
      <ElButton type="primary" @click="handleAdd(PageTypeEnum.ALLOCATE)">
        <Icon class="mr-1" icon="ep:message-box" />
        分配公告
      </ElButton>
      <ElButton type="primary" @click="handleClose">
        <Icon class="mr-1" icon="ep:close" />
        关闭公告
      </ElButton>
      <ElButton type="primary" @click="handleAdd(PageTypeEnum.RESET)">
        <Icon class="mr-1" icon="ep:open" />
        重启公告
      </ElButton>
    </div>

    <Table
      v-model:currentPage="tableObject.current"
      v-model:pageSize="tableObject.size"
      :columns="allSchemas.tableColumns"
      :data="tableObject.tableList"
      :loading="tableObject.loading"
      :pagination="{ total: tableObject.total }"
      @register="register"
    />
    <Close v-model="closeDialogVisible" :multipleTable="multipleTable" @update="getList" />
  </ContentWrap>
</template>
<style lang="less" scoped>
:deep(.el-checkbox-button) {
  margin: 0 8px 4px 0 !important;

  .el-checkbox-button__inner {
    border: none;
  }
}

:deep(.el-checkbox-button:last-child),
:deep(.el-checkbox-button:first-child) {
  .el-checkbox-button__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }
}
</style>
