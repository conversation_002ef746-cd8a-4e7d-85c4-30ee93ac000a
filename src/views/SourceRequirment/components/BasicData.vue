<script lang="ts" setup>
import { ref } from 'vue'
import { PublicNoticeAggregateDTO } from '@/api/notice/types'
import AvatarCropper from './AvatarCropper.vue'
import { getCategaryApi } from '@/api/common'
import { useValidator } from '@/hooks/web/useValidator'
import { BrandDTO } from '@/api/common/type'
import type { CurrentType } from '@/utils/enum'
import { PageTypeEnum } from '@/utils/enum'
import type { FormInstance } from 'element-plus'
import {
  ElCarousel,
  ElCarouselItem,
  ElCol,
  ElForm,
  ElFormItem,
  ElImage,
  ElInput,
  ElRow
} from 'element-plus'

const { required } = useValidator()
interface Props {
  data: PublicNoticeAggregateDTO
  currentType: CurrentType
}
const emit = defineEmits(['update'])

const props = withDefaults(defineProps<Props>(), {
  data: () =>
    ({
      mainPic: [{}],
      referPics: [{}],
      mainPic_url: [{}],
      referPics_url: [{}]
    } as PublicNoticeAggregateDTO)
})
const uploadPic = computed(() => {
  return [PageTypeEnum.ADD, PageTypeEnum.EDIT, PageTypeEnum.RESET].includes(props.currentType)
})

const isReset = computed(() => {
  return [PageTypeEnum.RESET].includes(props.currentType)
})

const ruleForm: any = computed({
  get: () => {
    Object.assign(props.data, { noticeCategoryDict: '1' })
    return props.data
  },

  set: (val) => {
    emit('update', val)
  }
})

const ruleFormRef = ref<FormInstance>()

const rules = ref({
  brandId: [required()],
  mainPic: [required()],
  referPics: [required()],
  seekingStage: [required()],
  noticeCategoryDict: [required()],
  noticeTopic: [required()],
  developSeason: [required()],
  productType: [required()],
  age: [required()],
  styleBody: [required()],
  cost: [required()],
  sizeEnd: [required()],
  sizeStart: [required()]
})
const brandOptions = ref<Array<BrandDTO>>([])
const dialogVisible = ref(false)
const disabledForm = computed(() => {
  return [PageTypeEnum.VIEW, PageTypeEnum.ALLOCATE].includes(props.currentType)
})
const closeAvatarDialog = ({ datas }) => {
  ruleForm.mainPic = datas && [datas]
  emit('update', { mainPic: datas && [datas] })
}
const handleSuccess = ({ datas, uid }, list) => {
  if (datas && datas.url) {
    const { objectName, url } = datas
    list.push({ uid, url, objectName })
  }
}

const handleRemove = ({ uploadFile }, list) => {
  // 删除新增的，uid匹配index
  const index = list?.findIndex((item) => item.uid === uploadFile.uid)
  index !== -1 && list?.splice(index, 1)
}
const categoryTree = ref([])
const categorProps = ref({ multiple: true, filterable: true })
const handleTree = (data: any) => {
  if (!Array.isArray(data)) return []
  data.map((item) => {
    const { categoryName, id, sonCategory = [] } = item
    Object.assign(item, {
      children: sonCategory,
      label: categoryName,
      value: `${id}&${categoryName}`
    })
    handleTree(item.children)
  })
}
const getCategoryTree = async () => {
  const { datas } = await getCategaryApi()
  handleTree(datas)
  categoryTree.value = datas
}

getCategoryTree()

const viewImgDialogVisible = ref(false)
const currentIndex = ref(0)
const handleImgView = (idx) => {
  currentIndex.value = idx
  viewImgDialogVisible.value = true
}
defineExpose({
  ruleFormRef,
  brandOptions
})
</script>
<template>
  <ElImageViewer
    v-if="viewImgDialogVisible"
    :initial-index="currentIndex"
    :url-list="ruleForm.imgList?.map((v) => v.url)"
    @close="viewImgDialogVisible = false"
  />
  <ElForm
    ref="ruleFormRef"
    :disabled="disabledForm"
    :model="ruleForm"
    :rules="currentType === PageTypeEnum.VIEW ? undefined : rules"
    center
    label-width="140px"
    scroll-to-error
    @submit.prevent
  >
    <ElRow>
      <ElCol :span="24">
        <ElFormItem v-if="uploadPic" label="公告主图" prop="mainPic">
          <div>
            <ElImage
              v-if="ruleForm.mainPic[0]?.url"
              :preview-src-list="[ruleForm.mainPic[0]?.url]"
              :src="ruleForm.mainPic[0]?.url"
              hide-on-click-modal
              style="width: 100px; height: 100px"
            />
            <div>
              <ElButton link type="primary" @click="dialogVisible = true">上传主图</ElButton>
            </div>
          </div>
        </ElFormItem>
        <ElFormItem v-else label="图片">
          <!-- <Swiper :list="[...ruleForm.mainPic_url, ...ruleForm.referPics_url]" /> -->
          <ElCarousel arrow="always" class="w-4/5">
            <ElCarouselItem v-for="(item, idx) in ruleForm.imgList" :key="idx">
              <ElImage
                :src="item.url"
                hide-on-click-modal
                :title="item.bannerSourceLabel"
                class="w-[500px] h-[320px]"
                fit="contain"
                @click="handleImgView(idx)"
              />
            </ElCarouselItem>
          </ElCarousel>
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem v-if="uploadPic" label="参考图片" prop="referPics">
          <OssImage
            ref="uploadPageRef"
            v-model="ruleForm.referPics"
            :file-list="ruleForm.referPics"
            :limit="20"
            @remove="(e) => handleRemove(e, ruleForm.referPics)"
            @success="(e) => handleSuccess(e, ruleForm.referPics)"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="公告阶段" prop="seekingStage">
          <Selection
            v-model="ruleForm.seekingStage"
            :disabled="isReset"
            api-key="SEEKING_STAGE"
            filterable
            multiple
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="公告分类" prop="noticeCategoryDict">
          <Selection
            v-model="ruleForm.noticeCategoryDict"
            :disabled="isReset"
            api-key="SEEKING_CATEGORY"
            filterable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="品牌" prop="brandId">
          <Selection
            v-model="ruleForm.brandId"
            :disabled="isReset"
            api-key="brandList"
            filterable
            @response-data="(e) => (brandOptions = e)"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="公告主题" prop="noticeTopic">
          <ElInput
            v-model="ruleForm.noticeTopic"
            :autosize="{ minRows: 1 }"
            :disabled="isReset"
            :maxlength="200"
            placeholder="请输入"
            show-word-limit
            type="textarea"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="开发季节" prop="developSeason">
          <Selection
            v-model="ruleForm.developSeason"
            :disabled="isReset"
            api-key="DEVELOP_SEASON"
            filterable
            multiple
          />
        </ElFormItem>
      </ElCol>

      <ElCol :span="12">
        <ElFormItem label="产品类别" prop="productType">
          <el-cascader
            v-model="ruleForm.productType"
            :disabled="isReset"
            :options="categoryTree"
            :props="categorProps"
            class="w-full"
            clearable
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="风格" prop="styleBody">
          <Selection v-model="ruleForm.styleBody" api-key="STYLE_BODY" filterable multiple />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="楦型">
          <Selection v-model="ruleForm.shoesTree" api-key="SHOES_TREE" filterable multiple />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="年龄段" prop="age">
          <ZoneTime v-model="ruleForm.age" :precision="0" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12" class="!flex flex-nowrap">
        <ElFormItem label="尺码段" prop="sizeStart">
          <ElInput v-model="ruleForm.sizeStart" maxlength="10" />
        </ElFormItem>
        <ElFormItem
          label="~"
          label-width="32"
          prop="sizeEnd"
          required
          class="const-range-max-form-item"
        >
          <ElInput v-model="ruleForm.sizeEnd" maxlength="10" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="型体" prop="model">
          <Selection v-model="ruleForm.model" api-key="MODEL" filterable multiple />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="成本区间（￥）" prop="cost">
          <ZoneTime v-model="ruleForm.cost" :precision="1" />
        </ElFormItem>
      </ElCol>
    </ElRow>
  </ElForm>
  <AvatarCropper v-model="dialogVisible" @success="closeAvatarDialog" />
</template>
<style lang="less" scoped>
:deep(.const-range-max-form-item .el-form-item__label::before) {
  content: none !important;
}
</style>
