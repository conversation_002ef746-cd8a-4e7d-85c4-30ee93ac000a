<script setup lang="ts">
import { ref } from 'vue'
import { PublicNoticeGrantRequest } from '@/api/notice/types'
import { getSupplierMangerUserApi } from '@/api/config'

import { useValidator } from '@/hooks/web/useValidator'
import type { FormInstance } from 'element-plus'
import type { CurrentType } from '@/utils/enum'
import { PageTypeEnum } from '@/utils/enum'
const { required } = useValidator()
interface Props {
  data: any
  currentType: CurrentType
  paramsForm: any
}
const emit = defineEmits(['update'])

const props = withDefaults(defineProps<Props>(), {
  data: () => ({} as PublicNoticeGrantRequest),
  paramsForm: () => ({} as PublicNoticeGrantRequest)
})
let brandIdInfo = ref('')
const ruleForm = computed({
  get: () => {
    const { brandId } = props.data
    brandIdInfo.value = brandId
    getNoticeManagerList()
    return props.data
  },
  set: (val) => {
    emit('update', val)
  }
})

const ruleFormRef = ref<FormInstance>()

const rules = ref({
  area: [required()],
  noticeManager: [required()],
  vendor: [required()]
})
const configurationVendor = reactive({
  key: 'vendorId',
  label: 'vendorName',
  value: 'vendorId',
  valueKey: 'vendorId'
})
const dataVendorMethod = (val) => {
  return val.map((v) => {
    return { vendorId: v.vendorId, vendorName: v.vendorName }
  })
}
const isDisabled = (item) => {
  return props.paramsForm.vendor?.some((v) => v.vendorId === item.vendorId)
}

const noticeManagerList = ref([] as any)
const getNoticeManagerList = async () => {
  const { datas } = await getSupplierMangerUserApi({ brandId: Number(brandIdInfo.value) })
  noticeManagerList.value = datas?.map((v) => {
    return {
      userName: v.nameEn,
      email: v.emailAddress,
      feishuUserId: v.userId
    }
  })
}

defineExpose({
  ruleFormRef
})
</script>
<template>
  <ElForm
    @submit.prevent
    :model="ruleForm"
    label-width="140px"
    ref="ruleFormRef"
    :rules="currentType === PageTypeEnum.VIEW ? undefined : rules"
    center
    scroll-to-error
    :disabled="currentType === PageTypeEnum.VIEW"
  >
    <ElRow>
      <ElCol :span="12">
        <ElFormItem label="对接供管" prop="noticeManager">
          <el-select
            v-model="ruleForm.noticeManager"
            filterable
            multiple
            style="width: 100%"
            value-key="feishuUserId"
          >
            <el-option
              v-for="item in noticeManagerList"
              :label="item.userName"
              :value="item"
              :key="item.feishuUserId"
            />
          </el-select>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12" v-if="ruleForm.noticeCategoryDict === '1'">
        <ElFormItem label="定向供应商" prop="vendor">
          <!-- 定向供应商只能新增，不能删除原来已推送信息的供应商 -->
          <Selection
            api-key="enableVendorList"
            v-model="ruleForm.vendor"
            filterable
            multiple
            :configuration="configurationVendor"
            :params="{ useStatus: '1' }"
            :isDisabled="isDisabled"
            :clearable="false"
            :dataMethod="dataVendorMethod"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="跟进说明">
          <ElInput
            v-model="ruleForm.followRemark"
            show-word-limit
            :maxlength="500"
            type="textarea"
            :autosize="{ minRows: 1 }"
            placeholder="请输入"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="区域" prop="area">
          <Selection api-key="AREA_CODE" v-model="ruleForm.area" filterable multiple />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12" v-if="currentType === 'view'">
        <ElFormItem label="寻源状态">
          <Selection api-key="NOTICE_STATE" v-model="ruleForm.stateDict" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12" v-if="currentType === 'view'">
        <ElFormItem label="关闭原因" prop="closeReasonDict">
          <Selection api-key="SEEKING_CLOSE_REASON" v-model="ruleForm.closeReasonDict" multiple />
        </ElFormItem>
      </ElCol>
    </ElRow>
  </ElForm>
</template>
