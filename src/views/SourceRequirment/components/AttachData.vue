<script setup lang="ts">
import { ref } from 'vue'
import { PublicNoticeAggregateDTO } from '@/api/notice/types'
import { ElForm, ElFormItem, ElRow, ElCol, ElInput, ElDatePicker } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { useValidator } from '@/hooks/web/useValidator'
import type { CurrentType } from '@/utils/enum'
import { PageTypeEnum } from '@/utils/enum'
const { required } = useValidator()
interface Props {
  data: PublicNoticeAggregateDTO
  currentType: CurrentType
}

const props = withDefaults(defineProps<Props>(), {
  data: () =>
    ({
      referFiles: []
    } as PublicNoticeAggregateDTO)
})
const emit = defineEmits(['update', 'setUploadLoading'])

const ruleForm: any = computed({
  get: () => props.data,
  set: (val) => {
    emit('update', val)
  }
})
const viewAndAllocate = computed(() => {
  return [PageTypeEnum.VIEW, PageTypeEnum.ALLOCATE].includes(props.currentType)
})

const ruleFormRef = ref<FormInstance>()
const loading = ref(false)
const rules = ref({
  endTime: [required()]
})
const handleFileRemove = ({ uploadFiles }) => {
  let field = uploadFiles?.id ? 'id' : 'uid'
  const index = ruleForm.value.referFiles.findIndex((item) => item[field] === uploadFiles[field])
  index !== -1 && ruleForm.value.referFiles.splice(index, 1)
}
const handleFileSuccess = (val) => {
  val && ruleForm.value.referFiles?.push(val)
}
const disabledDate = (time) => {
  return time < Date.now()
}
const setLoading = (val) => {
  loading.value = val
  emit('setUploadLoading', val)
}
defineExpose({
  ruleFormRef
})
</script>
<template>
  <ElForm
    @submit.prevent
    :model="ruleForm"
    label-width="140px"
    ref="ruleFormRef"
    center
    :rules="rules"
    scroll-to-error
    :disabled="viewAndAllocate"
  >
    <ElRow>
      <ElCol :span="12">
        <ElFormItem label="定位">
          <Selection api-key="POSITION" v-model="ruleForm.position" filterable multiple />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="场景">
          <ElInput
            v-model="ruleForm.scene"
            show-word-limit
            :maxlength="200"
            type="textarea"
            :autosize="{ minRows: 1 }"
            placeholder="请输入"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="颜色要求">
          <ElInput
            v-model="ruleForm.colorRequire"
            show-word-limit
            :maxlength="200"
            type="textarea"
            :autosize="{ minRows: 1 }"
            placeholder="请输入"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="主要材质">
          <ElInput
            v-model="ruleForm.mainMaterial"
            show-word-limit
            :maxlength="200"
            type="textarea"
            :autosize="{ minRows: 1 }"
            placeholder="请输入"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="流行元素">
          <ElInput
            v-model="ruleForm.popularElement"
            show-word-limit
            :maxlength="200"
            type="textarea"
            :autosize="{ minRows: 1 }"
            placeholder="请输入"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="推款数量" prop="pushNumber">
          <ZoneTime :precision="0" v-model="ruleForm.pushNumber" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="首单量（双）" prop="firstPair">
          <ZoneTime :precision="0" v-model="ruleForm.firstPair" />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="公告说明">
          <ElInput
            v-model="ruleForm.noticeDescription"
            show-word-limit
            :maxlength="500"
            type="textarea"
            :autosize="{ minRows: 1 }"
            placeholder="请输入"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12" v-if="viewAndAllocate">
        <ElFormItem label="公告发布日">
          <ElDatePicker
            v-model="ruleForm.releaseTime"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="公告截止日" prop="endTime">
          <ElDatePicker
            v-model="ruleForm.endTime"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="参考文件">
          <UploadFile
            v-model="ruleForm.referFiles"
            ref="uploadRef"
            :file-list="ruleForm.referFiles"
            @handle-file-remove="handleFileRemove"
            @success="handleFileSuccess"
            accept="*"
            :limit="20"
            @set-loading="setLoading"
            :size="500"
          />
        </ElFormItem>
      </ElCol>
    </ElRow>
  </ElForm>
</template>
<style lang="less" scoped>
.full-width {
  width: 100%;
}
</style>
