<script setup lang="ts">
import { ref } from 'vue'
import { propTypes } from '@/utils/propTypes'
import { PublicNoticeCloseDTO, RecordElement } from '@/api/notice/types'
import { useValidator } from '@/hooks/web/useValidator'
import { closeApi } from '@/api/notice'
import { ElDialog, ElButton, ElFormItem, ElForm } from 'element-plus'
import type { FormInstance } from 'element-plus'

const { required } = useValidator()
const props = defineProps({
  modelValue: propTypes.bool.def(false),
  multipleTable: propTypes.array.def([])
})
const ruleFormRef = ref<FormInstance>()

const loading = ref(false)
const ruleForm = reactive({
  state: '9',
  closeReasonDict: ''
})

const rules = ref({
  closeReasonDict: [required()]
})
const emits = defineEmits(['update:modelValue', 'update'])

const onConfirm = () => {
  ruleFormRef.value?.validate(async (valid) => {
    try {
      if (!valid) return
      loading.value = true
      const ids = [] as number[]
      props.multipleTable.map((item: RecordElement) => ids.push(item.id as number))
      const { closeReasonDict } = ruleForm
      let submitParams: PublicNoticeCloseDTO = Object.assign({}, { ids, closeReasonDict })
      await closeApi(submitParams)
      emits('update:modelValue', false)
      emits('update')
    } finally {
      loading.value = false
    }
  })
}
const closed = () => {
  ruleFormRef.value?.clearValidate()
  ruleFormRef.value?.resetFields()
  emits('update:modelValue', false)
  emits('update')
}
</script>
<template>
  <ElDialog
    title="操作确认"
    :model-value="modelValue"
    @close="closed"
    center
    width="480px"
    :close-on-click-modal="false"
  >
    <ElForm :model="ruleForm" label-width="80px" ref="ruleFormRef" :rules="rules" center>
      <ElFormItem label="公告状态" prop="state">
        <Selection style="width: 100%" api-key="NOTICE_STATE" v-model="ruleForm.state" disabled />
      </ElFormItem>
      <ElFormItem label="关闭原因" prop="closeReasonDict">
        <Selection
          style="width: 100%"
          api-key="SEEKING_CLOSE_REASON"
          v-model="ruleForm.closeReasonDict"
        />
      </ElFormItem>
    </ElForm>
    <ElRow> 您选择了「{{ multipleTable.length }}」条公告信息操作 关闭，请确认 </ElRow>
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="closed">取消</ElButton>
        <ElButton type="primary" @click="onConfirm" :loading="loading">确认</ElButton>
      </span>
    </template>
  </ElDialog>
</template>
<style lang="less" scoped>
.full-width {
  width: 100%;
}
</style>
