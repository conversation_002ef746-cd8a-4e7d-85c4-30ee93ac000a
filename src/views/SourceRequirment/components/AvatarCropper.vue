<template>
  <ElDialog
    title="上传公告主图"
    :model-value="modelValue"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    width="600px"
  >
    <div class="avatar-container">
      <!-- 待上传图片 -->
      <div v-show="!options.img">
        <ElUpload
          class="upload"
          ref="elUpload"
          :headers="uploadHeaders"
          :on-change="handleUpload"
          accept="image/png, image/jpeg, image/jpg"
          :show-file-list="false"
          :auto-upload="false"
        >
          <el-button type="primary" ref="uploadBtn"> 选择图片 </el-button>
        </ElUpload>
        <div>支持jpg、png格式的图片，大小不超过100M</div>
      </div>
      <!-- <img :src="options.img" alt=""> -->
      <!-- 已上传图片 -->
      <div v-show="options.img" class="avatar-crop">
        <vueCropper
          class="crop-box"
          ref="cropper"
          :img="options.img"
          :autoCrop="options.autoCrop"
          :fixedBox="options.fixedBox"
          :canMoveBox="options.canMoveBox"
          :autoCropWidth="options.autoCropWidth"
          :autoCropHeight="options.autoCropHeight"
          :centerBox="options.centerBox"
          :fixed="options.fixed"
          :fixedNumber="options.fixedNumber"
          :canMove="options.canMove"
          :canScale="options.canScale"
        />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <div class="reupload" @click="reupload">
          <span v-show="options.img">重新上传</span>
        </div>
        <div>
          <ElButton @click="closeDialog">取 消</ElButton>
          <ElButton type="primary" @click="getCrop">确 定</ElButton>
        </div>
      </span>
    </template>
  </ElDialog>
</template>

<script>
import { ref, reactive, defineComponent } from 'vue'
import { VueCropper } from 'vue-cropper'
import 'vue-cropper/dist/index.css'
import { ElMessage, ElDialog, ElUpload, ElButton } from 'element-plus'
import { getToken } from '@/utils/auth'
import { uploadByPreUrlApi } from '@/api/system'
import axios from 'axios'
export default defineComponent({
  components: {
    VueCropper
  },
  props: {
    modelValue: {
      type: Boolean
    }
  },
  setup(props, { emit }) {
    const uploadHeaders = reactive({
      authorization: getToken()
    })
    const cropper = ref()
    const uploadBtn = ref()
    const elUpload = ref()
    const options = reactive({
      img: '', // 原图文件
      autoCrop: true, // 默认生成截图框
      fixedBox: true, // 固定截图框大小
      canMoveBox: true, // 截图框可以拖动
      autoCropWidth: 240, // 截图框宽度
      autoCropHeight: 240, // 截图框高度
      fixed: true, // 截图框宽高固定比例
      fixedNumber: [1, 1], // 截图框的宽高比例
      centerBox: true, // 截图框被限制在图片里面
      canMove: false, // 上传图片不允许拖动
      canScale: false // 上传图片不允许滚轮缩放
    })
    // vueCropper组件 裁剪配置信息

    // 读取原图
    const handleUpload = (file, uploadFiles) => {
      const isIMAGE = file.raw.type === 'image/jpeg' || file.raw.type === 'image/png'
      const isLt5M = file.raw.size / 1024 / 1024 < 100
      if (!isIMAGE) {
        ElMessage.warning('请选择 jpg、png 格式的图片')
        return false
      }
      if (!isLt5M) {
        ElMessage.warning('图片大小不能超过100MB')
        return false
      }
      let reader = new FileReader()

      reader.readAsDataURL(file.raw)

      reader.onload = (e) => {
        options.img = e?.target.result // base64
      }
      elUpload.value.clearFiles() //这里处理重新上传时，upload组件change事件错误问题
    }
    // 获取截图信息
    const getCrop = () => {
      // 获取截图的 blob 数据
      cropper.value.getCropBlob(async (data) => {
        // let formData = new FormData()
        // formData.append('file', data, 'chris.jpg')

        // blob转file
        const blobToFile = (blob, fileName) => {
          return new window.File([blob], fileName, { type: blob.type })
        }
        let file = blobToFile(data, '主图.png')

        const { size: kbSize, type: fileType } = file
        const params = {
          fileName: '主图1',
          kbSize: Math.floor(kbSize / 1024),
          fileType,
          configCode: '0001'
        }
        try {
          const { datas: res } = await uploadByPreUrlApi(params)
          const {
            originName: name,
            objectName: key,
            policy,
            accessid: OSSAccessKeyId,
            callback,
            signature,
            host: url
          } = res
          const form = Object.assign(
            {},
            { name, key, policy, OSSAccessKeyId, callback, signature },
            { file }
          )
          const formData = new FormData()
          Object.keys(form).forEach((key) => formData.set(`${key}`, form[key]))
          const result = await axios({
            method: 'post',
            url,
            data: formData
          })
          if (!result) return ElMessage.error('上传失败')
          emit('success', { datas: result.data.datas })

          closeDialog()
        } catch (e) {
          ElMessage.error(e?.message || '上传失败')
        }
      })
    }
    // 重新上传
    const reupload = () => {
      uploadBtn.value.ref.click()
    }

    // 关闭弹框
    const closeDialog = () => {
      emit('update:modelValue', false)
      options.img = ''
    }
    return {
      options,
      cropper,
      uploadBtn,
      elUpload,
      handleUpload,
      getCrop,
      reupload,
      closeDialog,
      uploadHeaders
    }
  }
})
</script>

<style lang="less" scoped>
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;

  .reupload {
    color: #409eff;
    cursor: pointer;
  }
}

.avatar-container {
  display: flex;
  width: 560px;
  height: 350px;
  margin-right: 10px;
  background-color: #f0f2f5;
  border-radius: 4px;
  justify-content: center;
  align-items: center;

  .upload {
    margin-bottom: 24px;
    text-align: center;
  }

  .avatar-crop {
    position: relative;
    width: 560px;
    height: 350px;

    .crop-box {
      width: 100%;
      height: 100%;
      overflow: hidden;
      border-radius: 4px;
    }
  }
}
</style>
