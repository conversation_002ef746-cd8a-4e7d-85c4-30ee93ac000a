<script lang="ts" setup>
import { computed } from 'vue'
import { closeCurrentTag } from '@/utils/routerHelper'
import { AttachData, BasicData, FollowData } from './components/index'
import { draftApi, getApi, grantApi, reopenApi, saveApi } from '@/api/notice'
import { PublicNoticeProductTypeDTO, RecordElement } from '@/api/notice/types'
import { useRoute, useRouter } from 'vue-router'
import { ElButton, ElCollapse, ElCollapseItem, ElForm } from 'element-plus'
import type { CurrentType } from '@/utils/enum'
import { bannerSourceToLabel, PageTypeEnum } from '@/utils/enum'
import { pick } from 'lodash-es'
import { TableColumn } from '@/types/table'

const { push } = useRouter()
const currentType: CurrentType = useRoute().params.pageType as CurrentType
const id: number | null = useRoute().query.id as number | null
interface FileItem {
  id?: number
  objectName?: string
  [property: string]: any
}
interface ImgItem {
  url: string
  bannerSourceLabel: string
  [property: string]: any
}
const basicDataRef = ref()
const followDataRef = ref()
const attachDataRef = ref()
const ruleForm: any = reactive({
  referFiles: [],
  mainPic: [],
  referPics: [],
  mainPic_url: [],
  referPics_url: [],
  seekingStage: [],
  developSeason: [],
  model: [],
  styleBody: []
})
const showSaveBtn = computed(() => {
  return [PageTypeEnum.ADD, PageTypeEnum.EDIT].includes(currentType)
})
const showSubmitBtn = computed(() => {
  return [PageTypeEnum.ADD, PageTypeEnum.EDIT, PageTypeEnum.RESET].includes(currentType)
})
const paramsForm = reactive({
  vendor: []
})
onMounted(() => {
  if (currentType !== PageTypeEnum.ADD) {
    handleView()
  }
})

const rangeList = ['age', 'cost', 'pushNumber', 'firstPair']
const fieldList = ['mainPic_url', 'referPics_url']
const handleView = async () => {
  if (!id) return

  const { datas }: { datas: RecordElement } = await getApi({ id })
  const { referFiles_url, productType = [], mainPic_url = [], referPics_url, vendor = [] } = datas
  Object.assign(paramsForm, { vendor })
  rangeList.forEach((v) => {
    datas[v] = [datas[`${v}Start`] ?? '', datas[`${v}End`] ?? '']
  })
  const category: Array<string> = productType.map((v: PublicNoticeProductTypeDTO) =>
    (v.codeArray?.split(',') || []).map((item, index) => {
      const categoryNameList = (v.nameArray as string)?.split('>') || []
      return `${item}&${categoryNameList[index] || ''}`
    })
  )

  let imgList: Array<ImgItem> = []
  const processedFields = fieldList.reduce((acc, v) => {
    const processedData = (datas[v] || []).map((ele) => ({
      ...ele,
      bannerSourceLabel: bannerSourceToLabel[v]
    }))
    acc[v] = processedData
    imgList.push(...processedData)
    return acc
  }, {})

  Object.assign(ruleForm, datas, {
    referFiles: (referFiles_url || []).map((v) => ({ ...v, name: v.originFileName })),
    productType: category,
    mainPic: mainPic_url,
    referPics: referPics_url,
    imgList,
    ...processedFields
  })
}

interface Brand {
  id?: number
  brandName?: string
}

// 提交参数处理
const submitParams = computed(() => {
  const { productType: categoryList, mainPic, referPics, referFiles, brandId } = ruleForm
  const brandName: string = basicDataRef.value.brandOptions?.find(
    (v: Brand) => v.id + '' === brandId + ''
  )?.brandName as string
  rangeList.map((v) => {
    ;[ruleForm[`${v}Start`], ruleForm[`${v}End`]] = ruleForm[v] || ['', '']
  })

  const productType: PublicNoticeProductTypeDTO[] = Array.isArray(categoryList)
    ? Array.from(categoryList, (list) => {
        const [codeArray, nameArray] = list.reduce(
          ([codes, names], v) => {
            const [code, name] = v.split('&')
            return [
              [...codes, code],
              [...names, name]
            ]
          },
          [[], []]
        )

        return {
          codeArray: codeArray.join(),
          nameArray: nameArray.join('>'),
          categoryCode: codeArray[codeArray.length - 1],
          categoryName: nameArray[nameArray.length - 1]
        }
      })
    : []

  return Object.assign({}, ruleForm, {
    mainPic: mainPic?.map((v: FileItem) => v.objectName),
    referPics: referPics?.map((v: FileItem) => v.objectName),
    referFiles: referFiles?.map((v: FileItem) => v.objectName),
    brandName,
    productType
  })
})
const allocateParams = computed(() => {
  const fieldsToExtract = ['area', 'id', 'noticeManager', 'vendor', 'followRemark']
  return pick(ruleForm, fieldsToExtract)
})

const activeNames = ref(['1', '2', '3', '4'])
const loading = ref(false)
const handleAllocate = () => {
  followDataRef.value.ruleFormRef?.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        await grantApi(allocateParams.value)
        handleClose()
      } finally {
        loading.value = false
      }
    }
  })
}
const handleUpdate = (val) => {
  Object.assign(ruleForm, val)
}

const handleSubmit = async (api) => {
  const attachValid = unref(attachDataRef)?.ruleFormRef?.validate()
  const basicValid = unref(basicDataRef)?.ruleFormRef?.validate()
  Promise.all([basicValid, attachValid]).then(async () => {
    try {
      loading.value = true
      currentType === PageTypeEnum.RESET
        ? await reopenApi(submitParams.value)
        : await api(submitParams.value)
      handleClose()
    } finally {
      loading.value = false
    }
  })
}

const handleSampleNo = (row) => {
  push({
    name: 'ProductListAdd',
    params: { pageType: PageTypeEnum.VIEW },
    query: { id: row?.id }
  })
}

const columns: TableColumn[] = [
  {
    type: 'seq',
    label: '序号',
    field: '',
    width: 50
  },
  {
    label: '主图',
    field: 'img',
    width: 80,
    cellRender: {
      name: 'ImageColumn',
      props: {
        url: ({ row }) => {
          return row.attachment?.mainPic_url[0]?.url
        }
      }
    }
  },
  {
    field: 'productName',
    label: '产品名称',
    width: 100,
    cellRender: { name: 'LinkColumn', events: { click: ({ row }) => handleSampleNo(row) } }
  },
  {
    label: '产品分类',
    field: 'productClassify',
    'min-width': 100
  },
  {
    label: '风格',
    field: 'styleBody_zh',
    'min-width': 100
  },
  {
    label: '主要材质',
    field: 'mainMaterial',
    'min-width': 90
  },
  {
    label: '型体',
    field: 'model_zh',
    'min-width': 120
  },
  {
    label: '楦型',
    field: 'shoesTreeDict_zh',
    'min-width': 90
  },
  {
    label: '现有尺码段',
    field: 'size',
    'min-width': 100,
    formatter: ({ row }) => {
      return `${row.sizeStart ?? ''}-${row.sizeEnd ?? ''}`
    }
  },
  {
    label: '成本区间（￥）',
    field: 'cost',
    'min-width': 120,
    formatter: ({ row }) => {
      return `${row.costStart ?? ''}-${row.costEnd ?? ''}`
    }
  },
  {
    label: 'MOQ',
    field: 'moq',
    'min-width': 90
  },
  {
    label: '现有配色',
    field: 'containColor',
    'min-width': 90
  },
  {
    label: '推款时间',
    field: 'pushTime',
    'min-width': 90
  },
  {
    label: '推款来源',
    field: 'sourceFromDict_zh',
    'min-width': 90
  },
  {
    label: '产品状态',
    field: 'stateDict_zh',
    'min-width': 90
  }
]

const handleClose = () => {
  closeCurrentTag()
  push({
    name: 'SourceRequirementPage'
  })
}
</script>
<template>
  <ContentWrap>
    <ElCollapse v-model="activeNames">
      <ElForm
        ref="ruleFormRef"
        :disabled="currentType === PageTypeEnum.VIEW"
        :model="ruleForm"
        center
        label-width="140px"
        @submit.prevent
      >
        <ElCollapseItem name="1" title="基础信息">
          <BasicData
            ref="basicDataRef"
            :current-type="currentType"
            :data="ruleForm"
            @update="handleUpdate"
          />
        </ElCollapseItem>

        <ElCollapseItem name="2" title="附加信息">
          <AttachData
            ref="attachDataRef"
            :current-type="currentType"
            :data="ruleForm"
            @update="handleUpdate"
            @set-upload-loading="(val) => (loading = val)"
          />
        </ElCollapseItem>

        <ElCollapseItem
          v-if="[PageTypeEnum.VIEW, PageTypeEnum.ALLOCATE].includes(currentType)"
          name="3"
          title="公告跟进信息"
        >
          <FollowData
            ref="followDataRef"
            :current-type="currentType"
            :data="ruleForm"
            :paramsForm="paramsForm"
            @update="handleUpdate"
          />
        </ElCollapseItem>
        <ElCollapseItem v-if="currentType === PageTypeEnum.VIEW" name="4" title="推款信息">
          <Table :columns="columns" :data="ruleForm.pushStyleList" />
        </ElCollapseItem>
      </ElForm>
    </ElCollapse>
    <div class="mt-4 flex justify-center">
      <ElButton
        v-if="showSaveBtn"
        :loading="loading"
        type="primary"
        @click="handleSubmit(draftApi)"
      >
        <Icon class="mr-1" icon="ep:circle-check-filled" />
        保存
      </ElButton>

      <ElButton
        v-if="showSubmitBtn"
        :loading="loading"
        type="primary"
        @click="handleSubmit(saveApi)"
      >
        <Icon class="mr-1" icon="ep:upload" />
        保存并发布
      </ElButton>
      <ElButton
        v-if="currentType === PageTypeEnum.ALLOCATE"
        :loading="loading"
        type="primary"
        @click="handleAllocate"
      >
        <Icon class="mr-1" icon="ep:circle-check-filled" />
        提交
      </ElButton>
      <ElButton @click="handleClose">
        <Icon class="mr-1" icon="ep:circle-close-filled" />
        关闭
      </ElButton>
    </div>
  </ContentWrap>
</template>
<style lang="less" scoped>
:deep(.el-carousel__item) {
  display: flex;
  justify-content: center !important;
}
</style>
