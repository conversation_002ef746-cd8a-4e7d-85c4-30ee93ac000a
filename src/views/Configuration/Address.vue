<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { ref } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { VxeTablePropTypes } from 'vxe-table'
import { getQMSDict, getQMSDictLabel } from '@/utils'
import { batchSaveAddressApi, getAddressListApi } from '@/api/config'
import { getListBrandApi, getProductCenterListApi } from '@/api/common'
import type { BrandDTO, EmployeeDTO } from '@/api/common/type'
import type { AddressSaveRequest, ConfigAcceptAddressDTO } from '@/api/config/types'
import { useValidator } from '@/hooks/web/useValidator'

const { required } = useValidator()
const validRules = ref({
  cityDict: [required()],
  brandId: [required()],
  userId: [required()],
  stateDict: [required()],
  concatPhone: [required(), { max: 50, message: '不能超过50字符', trigger: 'change' }],
  acceptAddress: [required(), { max: 1000, message: '不能超过1000字符' }]
} as VxeTablePropTypes.EditRules)

const loading = ref(false)
const vxTableRef = ref()

const tableData = ref<Array<ConfigAcceptAddressDTO>>([])

const getList = async () => {
  const { datas } = await getAddressListApi({})
  tableData.value = datas
}
const userList = ref<EmployeeDTO[]>([])
const getUserList = async () => {
  const { datas } = await getProductCenterListApi()
  userList.value = datas
}
const brandOptions = ref<BrandDTO[]>([])
const getBrandList = async () => {
  const { datas } = await getListBrandApi()
  brandOptions.value = datas
}
const validAllEvent = async () => {
  const errMap = await unref(vxTableRef)?.validate(true)
  if (errMap) {
    ElMessage.warning('校验不通过！')
    return false
  } else {
    return true
  }
}

onMounted(() => {
  getUserList()
  getBrandList()
  getList()
})

const isTableSaved = ref(true)

function enterTableEdit() {
  isTableSaved.value = false
}

// eslint
onBeforeRouteLeave((_to, _from, next) => {
  if (vxTableRef.value && !isTableSaved.value) {
    const shouldLeave = confirm('你有未保存的更改，确定要离开吗？')
    if (shouldLeave) {
      next()
    } else {
      next(false)
    }
  } else {
    next()
  }
})

const save = async () => {
  try {
    if (await validAllEvent()) {
      loading.value = true
      const list: Array<AddressSaveRequest> = vxTableRef.value?.getTableData().tableData
      list.map((i) => {
        const brandName = brandOptions.value?.find((v) => v.id === i.brandId)?.brandName
        return Object.assign(i, { brandName })
      })
      await batchSaveAddressApi(list)
      isTableSaved.value = true
      ElMessage.success('保存成功')
      getList()
    }
  } catch (error) {
    console.log('error: ', error)
  } finally {
    loading.value = false
  }
}
const insertEvent = async () => {
  const $table = vxTableRef.value
  if ($table) {
    const record = {
      stateDict: '1'
    }
    const { row: newRow } = await $table.insertAt(record)
    await $table.setEditCell(newRow)
  }
}
const handleChangeUser = ({ value }, row) => {
  row.userName = userList.value.find((ele) => ele.userId === value)?.nameEn
}
</script>

<template>
  <ContentWrap>
    <div class="mb-[10px]">
      <ElButton link type="primary" @click="insertEvent()">
        <Icon class="mr-1" icon="ep:circle-plus" />
        添加
      </ElButton>
    </div>

    <vxe-table
      ref="vxTableRef"
      :column-config="{ resizable: true }"
      :data="tableData"
      :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
      :edit-rules="validRules"
      align="center"
      border
      class="mb-5"
      max-height="500px"
      @edit-activated="enterTableEdit()"
    >
      <vxe-column title="序号" type="seq" width="80" />
      <vxe-column :edit-render="{}" field="brandId" title="品牌">
        <template #default="{ row }">
          <span>{{ brandOptions.find((v) => v.id === row.brandId)?.brandName }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.brandId" transfer>
            <vxe-option
              v-for="item in brandOptions"
              :key="item.id"
              :label="item.brandName"
              :value="item.id"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="cityDict" title="样品寄送地">
        <template #default="{ row }">
          <span>{{ getQMSDictLabel('ACCEPT_CITY', row.cityDict) }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.cityDict" transfer>
            <vxe-option
              v-for="item in getQMSDict('ACCEPT_CITY')"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="userId" title="联系人">
        <template #default="{ row }">
          <span>{{ userList.find((v) => v.userId + '' === row.userId + '')?.nameEn }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select
            v-model="row.userId"
            filterable
            transfer
            @change="(e) => handleChangeUser(e, row)"
          >
            <vxe-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nameEn"
              :value="item.userId"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="concatPhone" title="联系电话">
        <template #edit="{ row }">
          <vxe-input v-model="row.concatPhone" type="text" />
        </template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="acceptAddress" min-width="120px" title="收件地址">
        <template #edit="{ row }">
          <vxe-input v-model="row.acceptAddress" type="text" />
        </template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="stateDict" title="状态">
        <template #default="{ row }">
          <span>{{ getQMSDictLabel('CONFIG_STATE', row.stateDict) }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.stateDict" transfer>
            <vxe-option
              v-for="item in getQMSDict('CONFIG_STATE')"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column field="modifyByName" title="操作人" />
      <vxe-column field="modifyTime" title="操作时间" />
    </vxe-table>
    <ElRow :gutter="20">
      <ElCol :offset="10" :span="4">
        <ElButton :loading="loading" class="mt-[30px]" type="primary" @click="save">
          <Icon class="mr-1" icon="ep:circle-check-filled" />
          保存
        </ElButton>
      </ElCol>
    </ElRow>
  </ContentWrap>
</template>
<style lang="less" scoped>
.vxe-cell--valid {
  .vxe-cell--valid-msg {
    display: none !important;
  }
}
</style>
