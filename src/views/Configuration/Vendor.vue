<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { ref } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { VxeTablePropTypes } from 'vxe-table'
import { getQMSDict, getQMSDictLabel } from '@/utils'
import { getListBrandApi, getSupplierManagerListApi } from '@/api/common'
import { batchSaveVendorApi, getVendorManagerListApi } from '@/api/config'
import type { BrandDTO, EmployeeDTO } from '@/api/common/type'
import { useValidator } from '@/hooks/web/useValidator'
import type { ConfigVendorManagerDTO, ConfigVendorManagerUpdateDTO } from '@/api/config/types'

const { required } = useValidator()
const validRules = ref({
  areaDict: [required()],
  brandId: [required()],
  stateDict: [required()],
  userIds: [required()]
} as VxeTablePropTypes.EditRules)

const loading = ref(false)
const vxTableRef = ref()

const tableData = ref<ConfigVendorManagerDTO[]>([])

const getList = async () => {
  const { datas } = await getVendorManagerListApi({})
  tableData.value = datas
}
const userList = ref<EmployeeDTO[]>([])
const getUserList = async () => {
  const { datas } = await getSupplierManagerListApi()
  userList.value = datas
}
const brandOptions = ref<BrandDTO[]>([])
const getBrandList = async () => {
  const { datas } = await getListBrandApi()
  brandOptions.value = datas
}
const validAllEvent = async () => {
  const errMap = await unref(vxTableRef)?.validate(true)
  if (errMap) {
    ElMessage.warning('校验不通过！')
    return false
  } else {
    return true
  }
}

onMounted(() => {
  getUserList()
  getBrandList()
  getList()
})
const handleVendor = (list = []) => {
  if (!list) return

  return list.map((ele) => userList.value.find((v) => v.userId === ele)?.nameEn).join()
}
const save = async () => {
  try {
    if (await validAllEvent()) {
      loading.value = true
      let list: Array<ConfigVendorManagerUpdateDTO> = []
      list = vxTableRef.value?.getTableData().tableData
      list.map((i) => {
        const brandName = brandOptions.value?.find((v) => v.id === i.brandId)?.brandName
        return Object.assign(i, { brandName })
      })
      await batchSaveVendorApi(list)
      isTableSaved.value = true
      ElMessage.success('保存成功')
      getList()
    }
  } catch (error) {
    console.error('error: ', error)
  } finally {
    loading.value = false
  }
}
const insertEvent = async () => {
  const $table = vxTableRef.value
  if ($table) {
    const record = {
      stateDict: '1'
    }
    const { row: newRow } = await $table.insertAt(record)
    await $table.setEditCell(newRow)
  }
}
const handleChangeVendor = ({ value }, row) => {
  const email: Array<string> = []
  const userNames: Array<string> = []
  value.map((v) => {
    const { emailAddress, nameEn } = userList.value.find((ele) => ele.userId === v) as EmployeeDTO
    email.push(emailAddress as string)
    userNames.push(nameEn as string)
  })
  row.email = email
  row.userNames = userNames
}

const isTableSaved = ref(true)

function enterTableEdit() {
  isTableSaved.value = false
}

// eslint
onBeforeRouteLeave((_to, _from, next) => {
  if (vxTableRef.value && !isTableSaved.value) {
    const shouldLeave = confirm('你有未保存的更改，确定要离开吗？')
    if (shouldLeave) {
      next()
    } else {
      next(false)
    }
  } else {
    next()
  }
})
</script>

<template>
  <ContentWrap>
    <div class="mb-[10px]">
      <ElButton link type="primary" @click="insertEvent()">
        <Icon class="mr-1" icon="ep:circle-plus" />
        添加
      </ElButton>
    </div>

    <vxe-table
      ref="vxTableRef"
      :column-config="{ resizable: true }"
      :data="tableData"
      :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
      :edit-rules="validRules"
      align="center"
      border
      class="mb-5"
      max-height="500px"
      @edit-activated="enterTableEdit()"
    >
      <vxe-column title="序号" type="seq" width="80" />
      <vxe-column :edit-render="{}" field="brandId" title="品牌">
        <template #default="{ row }">
          <span>{{ brandOptions.find((v) => v.id === row.brandId)?.brandName }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.brandId" transfer>
            <vxe-option
              v-for="item in brandOptions"
              :key="item.id"
              :label="item.brandName"
              :value="item.id"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="areaDict" title="区域">
        <template #default="{ row }">
          <span>{{ getQMSDictLabel('PDM_AREA', row.areaDict) }}</span>
        </template>
        <template #edit="scope">
          <vxe-select v-model="scope.row.areaDict" transfer>
            <vxe-option
              v-for="item in getQMSDict('PDM_AREA')"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="userIds" title="对应供管">
        <template #default="{ row }">
          <span>{{ handleVendor(row.userIds) }}</span>
        </template>
        <template #edit="scope">
          <vxe-select
            v-model="scope.row.userIds"
            filterable
            multiple
            transfer
            @change="(e) => handleChangeVendor(e, scope.row)"
          >
            <vxe-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nameEn"
              :value="item.userId"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="stateDict" title="状态">
        <template #default="{ row }">
          <span>{{ getQMSDictLabel('CONFIG_STATE', row.stateDict) }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.stateDict" transfer>
            <vxe-option
              v-for="item in getQMSDict('CONFIG_STATE')"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </vxe-select>
        </template>
      </vxe-column>

      <vxe-column field="modifyByName" title="操作人" />
      <vxe-column field="modifyTime" title="操作时间" />
    </vxe-table>
    <ElRow :gutter="20">
      <ElCol :offset="10" :span="4">
        <ElButton :loading="loading" class="mt-[30px]" type="primary" @click="save">
          <Icon class="mr-1" icon="ep:circle-check-filled" />
          保存
        </ElButton>
      </ElCol>
    </ElRow>
  </ContentWrap>
</template>
<style lang="less" scoped>
.vxe-cell--valid {
  .vxe-cell--valid-msg {
    display: none !important;
  }
}
</style>
