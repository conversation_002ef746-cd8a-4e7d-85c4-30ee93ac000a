<script setup lang="ts">
import { VxeGridInstance, VxeGridProps } from 'vxe-table'
import { scrollProp } from '@/plugins/vxeTable'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { Icon } from '@/components/Icon'
import {
  PropertyApi,
  propertyPage,
  saveOrUpdateProperty,
  deleteProperty
} from '@/api/systemConfiguration/Property'
import Left from '@/views/system-configuration/components/Left.vue'
import PropertyDialog from '@/views/system-configuration/components/PropertyDialog.vue'
import type { AdvancedCondition } from '@/components/PlmBase/AdvancedSearchForm'
import { AdvancedSearchForm } from '@/components/PlmBase/AdvancedSearchForm'
import {
  EnumConfig,
  LeftType,
  PropertyConfig,
  selectType
} from '@/views/system-configuration/Config/help'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { EnumTypeApi } from '@/api/systemConfiguration/Enum'
import { watchDebounced } from '@vueuse/core'
import type { ElPagination } from 'element-plus'
import { LayoutForm } from '@/components/LayoutForm'

defineOptions({
  name: 'Property'
})
const propertyTableRef = ref<VxeGridInstance>()
const pagerRef = ref<InstanceType<typeof ElPagination>>()
const propertyList = ref<PropertyApi.Row[]>([])
const propertyLoading = ref(false)
const propertyPager = reactive({
  current: 1,
  size: 10,
  total: 0
})
const maxHeight = useTableHeight({ tableRef: propertyTableRef, pagerRef })
const selectedCategory = ref<selectType | null>(null)
const propertyDialogVisible = ref(false)
const propertyDialogMode = ref<'add' | 'edit'>('add')
const propertyDialogData = ref<PropertyApi.Row>({})
const formData = reactive<PropertyApi.Params>({
  name: '',
  value: ''
})
const advancedConditions = ref<AdvancedCondition[]>([])
// table
const tableOptions = computed(
  () =>
    ({
      border: true,
      showOverflow: true,
      height: maxHeight.value - 55,
      loading: propertyLoading.value,
      data: propertyList.value,
      columns: PropertyConfig.PropertyTableConfig,
      ...scrollProp
    } as VxeGridProps)
)
//搜索
const fetchPropertyItems = async () => {
  if (propertyLoading.value) return false
  propertyLoading.value = true
  const params: PropertyApi.pageRequest = getParams()

  const [error, result] = await propertyPage(params)
  if (error === null && result) {
    propertyList.value = result.data?.records || []
    propertyPager.total = result.data?.total || 0
  } else {
    ElMessage.error('获取属性列表失败')
  }
  propertyLoading.value = false
}

// 新增属性
const handleAddProperty = () => {
  propertyDialogMode.value = 'add'
  propertyDialogData.value = {
    categoryId: selectedCategory.value?.id,
    categoryName: selectedCategory.value?.label
  }
  propertyDialogVisible.value = true
}

// 编辑属性
const handleEditProperty = (row: PropertyApi.Row) => {
  propertyDialogMode.value = 'edit'
  propertyDialogData.value = { ...row }
  propertyDialogVisible.value = true
}
const getParams = () => {
  return {
    current: propertyPager.current,
    size: propertyPager.size,
    categoryId: selectedCategory.value?.id,
    name: formData.name,
    value: formData.value
  }
}
// 删除属性
const handleDeleteProperty = (row: PropertyApi.Row) => {
  ElMessageBox.confirm('确定要删除该属性吗？删除后将无法恢复', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        if (!row.id) {
          ElMessage.error('属性ID不能为空')
          return
        }

        const [error, result] = await deleteProperty(String(row.id))
        if (error === null && result && result.success) {
          ElMessage.success('删除成功')
          fetchPropertyItems()
        }
      } catch (error) {
        console.error('删除属性出错:', error)
        ElMessage.error('删除属性出错')
      }
    })
    .catch(() => {})
}
//保存属性
const handleSaveProperty = async (data: PropertyApi.Row, callback: () => Promise<any>) => {
  const [error, result] = await saveOrUpdateProperty(data)

  if (error === null && result && result.success) {
    ElMessage.success(propertyDialogMode.value === 'add' ? '添加成功' : '修改成功')
    propertyDialogVisible.value = false
    fetchPropertyItems()
  }
  callback()
}
const handleSearch = () => {
  propertyPager.current = 1
  fetchPropertyItems()
}
const { handleExport: exportFn, loading: exportLoading } = useOmsExport(false)
const downloadFunc = () => {
  const selected: EnumTypeApi.Row[] | undefined = propertyTableRef.value?.getCheckboxRecords()
  let reqParam: string
  if (selected && selected?.length > 0) {
    reqParam = JSON.stringify({ idList: selected.map((e) => e.id) })
  } else {
    reqParam = JSON.stringify(getParams())
  }

  exportFn({
    exportType: 'property-export',
    reqParam
  })
}
watch(
  () => selectedCategory.value,
  () => {
    propertyPager.current = 1
    fetchPropertyItems()
  }
)
onMounted(() => {
  watchDebounced(
    formData,
    () => {
      handleSearch()
    },
    { deep: true, debounce: 400 }
  )
})
const layoutFormRef = ref<InstanceType<typeof LayoutForm>>()
// 重置表单
const handleReset = () => {
  if (!layoutFormRef.value) return
  layoutFormRef.value?.formRef.resetFields()
}
</script>

<template>
  <ContentWrap>
    <div class="property-manager">
      <div class="search">
        <div class="panel-header">
          <LayoutForm
            ref="layoutFormRef"
            :model="formData"
            :loading="propertyLoading"
            query-form
            :span="6"
            @reset="handleReset"
            @search="handleSearch"
          >
            <template v-for="(attribute, index) in PropertyConfig.PropertyFormConfig" :key="index">
              <ElFormItem :label="attribute.label" :prop="attribute.field">
                <component
                  :is="attribute.component"
                  v-model="formData[attribute.field]"
                  v-bind="attribute.props || {}"
                />
              </ElFormItem>
            </template>
          </LayoutForm>

          <AdvancedSearchForm
            v-model="formData"
            :form-config="PropertyConfig.PropertyFormConfig"
            :advanced-conditions="advancedConditions"
            :loading="propertyLoading"
            :showAdvancedSearch="false"
            @search="handleSearch"
            @advanced-search="handleSearch"
          />
          <div class="flex">
            <ElButton type="primary" @click="handleAddProperty">
              <Icon class="mr-0.5" icon="ep:plus" />
              新增
            </ElButton>
            <ElButton type="primary" @click="downloadFunc" :loading="exportLoading">
              <Icon class="mr-0.5" icon="ep:upload-filled" />
              导出</ElButton
            >
          </div>
        </div>
      </div>

      <ElRow>
        <ElCol :span="5">
          <Left v-model:selected="selectedCategory" :type="LeftType.property" />
        </ElCol>

        <ElCol :span="19">
          <div class="property-container">
            <div class="property-items-panel">
              <VxeGrid ref="propertyTableRef" v-bind="tableOptions">
                <template #operation="{ row }">
                  <el-dropdown>
                    <el-button type="text"> 操作 </el-button>
                    <template #dropdown>
                      <el-dropdown-item>
                        <ElButton link @click="handleEditProperty(row)">
                          <Icon icon="ep:edit" />
                          编辑
                        </ElButton></el-dropdown-item
                      >
                      <el-dropdown-item>
                        <ElButton type="danger" link @click="handleDeleteProperty(row)">
                          <Icon icon="ep:delete" />
                          删除
                        </ElButton></el-dropdown-item
                      >
                    </template>
                  </el-dropdown>
                </template>
              </VxeGrid>
            </div>
            <div class="flex justify-end mt-1 md-1">
              <ElPagination
                ref="pagerRef"
                v-model:current-page="propertyPager.current"
                v-model:page-size="propertyPager.size"
                :total="propertyPager.total"
                background
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="fetchPropertyItems"
                @current-change="fetchPropertyItems"
              />
            </div>
          </div>
        </ElCol>
      </ElRow>
    </div>
    <PropertyDialog
      v-model="propertyDialogVisible"
      :mode="propertyDialogMode"
      :data="propertyDialogData"
      @save="handleSaveProperty"
    />
  </ContentWrap>
</template>

<style scoped lang="less">
.property-manager {
  width: 100%;
  height: 100%;
}

.search {
  margin-bottom: 16px;
}

.property-container {
  padding: 10px 10px 5px;
  overflow: hidden;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
}

.property-items-panel {
  width: 100%;
  height: 100%;
}

:deep(.el-form-item) {
  .el-cascader,
  .el-select,
  .el-autocomplete {
    width: 100%;
  }

  .select-plus {
    display: block !important;
    width: 100%;
  }
}
</style>
