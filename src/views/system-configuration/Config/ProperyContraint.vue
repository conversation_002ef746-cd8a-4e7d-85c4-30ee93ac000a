<script setup lang="ts">
import ContraitTemplate from '@/views/system-configuration/components/ContraitTemplate.vue'
const currentRoute = useRouter().currentRoute
const router = useRouter()
const constraintAttribute = computed(() => {
  return (currentRoute.value?.query.type as string) || ''
})
const constraintId = computed(() => {
  return (currentRoute.value?.query.id as string) || ''
})
</script>

<template>
  <ContraitTemplate :constraint-attribute="constraintAttribute" :constraint-id="constraintId" />
  <div class="flex mt-4 justify-center center">
    <ElButton @click="router.go(-1)">返回</ElButton>
  </div>
</template>
<style scoped lang="less">
.operation {
  margin-bottom: 10px;
}
</style>
