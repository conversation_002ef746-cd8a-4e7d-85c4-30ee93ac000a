<script setup lang="ts">
import { ref } from 'vue'
import { VxeGridInstance, VxeGridProps } from 'vxe-table'
import ContraitTemplateDialog from '@/views/system-configuration/components/ContraitTemplateDialog.vue'
import { getAllConstraints } from '@/api/systemConfiguration/type'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { Attribute } from '@/components/PlmBase/type'
const PlmBaseConstraintInstanceResp = ref<Attribute[]>([])
const loading = ref(false)
const getList = async () => {
  loading.value = true
  const [error, result] = await getAllConstraints()
  if (!error && result?.data) {
    PlmBaseConstraintInstanceResp.value = result?.data || []
  }
  loading.value = false
}
getList()
const gridRef = ref<VxeGridInstance>()

// 模态框相关
const dialogContent = reactive({
  visible: false,
  constraintType: ''
})

const handleEdit = (row: any) => {
  dialogContent.constraintType = row.constraintType
  dialogContent.visible = true
}
const maxHeight = useTableHeight({ tableRef: gridRef })
const tableOptions = computed(
  () =>
    ({
      border: true,
      showOverflow: true,
      loading: loading.value,
      maxHeight: maxHeight.value,
      data: PlmBaseConstraintInstanceResp.value,
      columns: [
        { field: 'constraintType', title: '类型' },
        {
          field: 'createByIdName',
          title: '创建人'
        },
        {
          title: '操作',
          width: 80,
          slots: {
            default: 'operation'
          }
        }
      ]
    } as VxeGridProps)
)
</script>

<template>
  <ContentWrap>
    <div>
      <VxeGrid ref="gridRef" v-bind="tableOptions">
        <template #operation="{ row }">
          <ElButton type="text" @click="handleEdit(row)">修改</ElButton>
        </template>
      </VxeGrid>
    </div>
  </ContentWrap>
  <ContraitTemplateDialog
    @save-success="getList"
    v-model="dialogContent.visible"
    :constraintType="dialogContent.constraintType"
  />
</template>

<style scoped lang="less">
.modal {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
}

.modal-content {
  padding: 20px;
  background-color: white;
  border-radius: 5px;
}
</style>
