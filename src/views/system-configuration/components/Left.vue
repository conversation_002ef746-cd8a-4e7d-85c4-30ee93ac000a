<script setup lang="ts">
import { Icon } from '@/components/Icon'
import {
  deleteClassification,
  EnumApi,
  EnumTypeApi,
  listClassification,
  saveClassification,
  updateClassification
} from '@/api/systemConfiguration/Enum'
import { ElMessage, ElMessageBox, ElTree } from 'element-plus'
import EditDialog from '@/views/system-configuration/components/EditDialog.vue'
import {
  categoryData,
  deleteCategory,
  PropertyApi,
  saveOrUpdateCategory
} from '@/api/systemConfiguration/Property'
import { LeftType, entityName } from '@/views/system-configuration/Config/help'
import type { selectType } from '@/views/system-configuration/Config/help'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { deleteType, saveOrUpdateType, typeTree } from '@/api/systemConfiguration/type'
import { Attribute } from '@/components/PlmBase/type'
const SearchKeyword = ref('')
const Loading = ref(false)
const DialogMode = ref<'add' | 'edit'>('add')
const DialogData = ref<Partial<EnumTypeApi.Row | PropertyApi.categoryRow>>({})
const DialogVisible = ref(false)
const treeData = ref<selectType[]>([])
const treeRef = ref<InstanceType<typeof ElTree>>()
const advancedSearchConditions = ref<EnumApi.AdvancedCondition[]>([])
const emits = defineEmits(['update:selected'])

defineOptions({
  name: 'Left'
})
const props = defineProps<{
  selected: selectType | null
  type: LeftType
}>()
const selectType = computed({
  get: () => props.selected,
  set: (val) => emits('update:selected', val)
})
// 过滤树节点
const filterNode = (value: string, data: any) => {
  if (!value) return true
  const keyword = value.toLowerCase()
  return data.label?.toLowerCase().includes(keyword)
}

const maxHeight = useTableHeight({ tableRef: treeRef })
// 监听搜索关键词变化
watch(SearchKeyword, (val) => {
  treeRef.value?.filter(val)
})

// 添加枚举类型
const handleAdd = (data?: selectType) => {
  DialogMode.value = 'add'
  DialogData.value = {
    parentId: data?.typeId
  }
  DialogVisible.value = true
}
// 选择枚举类型
const handleNodeClick = (data: selectType) => {
  selectType.value = data
  advancedSearchConditions.value = []
}
// 编辑枚举类型
const handleEdit = (data: EnumTypeApi.Row | PropertyApi.categoryRow) => {
  DialogData.value =
    props.type === LeftType.enum ? (data as EnumTypeApi.Row) : (data as PropertyApi.categoryRow)
  DialogMode.value = 'edit'
  DialogVisible.value = true
}

// 删除枚举类型或属性分类
const handleDelete = (data: EnumTypeApi.Row | PropertyApi.categoryRow) => {
  ElMessageBox.confirm(`确定要删除该${entityName[props.type]}吗？删除后将无法恢复`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      let error, result

      // 根据类型使用不同的API
      if (props.type === LeftType.property) {
        ;[error, result] = await deleteCategory(String(data.id))
      } else if (props.type === LeftType.type) {
        ;[error, result] = await deleteType(String(data.id))
      } else {
        ;[error, result] = await deleteClassification({ id: String(data.id) })
      }

      if (error === null && result && result.success) {
        ElMessage.success('删除成功')
        fetchData()
        if (selectType.value?.id === data.id) {
          selectType.value = treeData.value?.[0] as selectType
        }
      }
    } catch (error) {
      console.error(`删除${entityName[props.type]}出错:`, error)
      ElMessage.error(`删除${entityName[props.type]}出错`)
    }
  })
}
// 保存枚举类型或属性分类
const handleSave = async (
  data: EnumTypeApi.Row | PropertyApi.categoryRow,
  callback: () => Promise<any>
) => {
  let error, result
  // 属性分类使用相同的API接口，因为没有单独的API
  if (DialogMode.value === 'add') {
    ;[error, result] =
      props.type === LeftType.property
        ? await saveOrUpdateCategory(data as PropertyApi.Row)
        : props.type === LeftType.type
        ? await saveOrUpdateType(data as Attribute)
        : await saveClassification(data as EnumTypeApi.Row)
  } else {
    ;[error, result] =
      props.type === LeftType.property
        ? await saveOrUpdateCategory(data as PropertyApi.Row)
        : props.type === LeftType.type
        ? await saveOrUpdateType(data as Attribute)
        : await updateClassification(data as EnumTypeApi.Row)
  }

  if (error === null && result && result.success) {
    ElMessage.success(DialogMode.value === 'add' ? '添加成功' : '修改成功')
    DialogVisible.value = false
    await fetchData()
    SearchKeyword.value = ''
  }
  callback()
}
// 获取枚举类型或属性分类列表
const fetchData = async () => {
  Loading.value = true
  try {
    let error, result
    // 根据类型使用不同的API
    if (props.type === LeftType.property) {
      ;[error, result] = await categoryData()
    } else if (props.type === LeftType.type) {
      ;[error, result] = await typeTree()
    } else {
      ;[error, result] = await listClassification()
    }

    if (error === null && result) {
      // 转换为树形结构
      if (props.type === LeftType.type) {
        treeData.value = [...translateData(result.data, props.type)] as selectType[]
      } else {
        treeData.value = [
          {
            children: translateData(result.data, props.type) as selectType[],
            value: '根节点',
            label: '根节点',
            id: ''
          }
        ]
      }
      if (treeData.value.length > 0 && !selectType.value) {
        selectType.value = treeData.value[0] as selectType
      }
    } else {
      ElMessage.error(`获取${entityName[props.type]}列表失败`)
    }
  } catch (error) {
    console.error(`获取${entityName[props.type]}列表出错:`, error)
    ElMessage.error(`获取${entityName[props.type]}列表出错`)
  } finally {
    Loading.value = false
  }
}
//处理属性和枚举不同的label value
const translateData = (data: EnumTypeApi.Row[] | PropertyApi.categoryRow[], type: LeftType) => {
  const ListMap = {
    [LeftType.enum]: {
      label: 'enumeratedClassificationDesc',
      value: 'enumeratedClassificationCode',
      id: 'id'
    },
    [LeftType.property]: {
      label: 'selectorKey',
      value: 'selectorValue',
      id: 'selectorValue'
    },
    [LeftType.type]: {
      label: 'typeShowName',
      value: 'typeId',
      id: 'typeId'
    }
  }
  function deepData(content) {
    content.forEach((item) => {
      item.label = item[ListMap[type].label]
      item.value = item[ListMap[type].value]
      item.id = item[ListMap[type].id]
      item.children = item.typeChild
      if (item.children) {
        deepData(item.children)
      }
    })
  }
  deepData(data)
  return data
}

onMounted(() => {
  fetchData()
})
</script>

<template>
  <!-- Left Panel - Enum Types or Property Categories -->
  <div class="enum-types-panel" :style="{ height: maxHeight + 20 + 'px' }">
    <div class="search-box">
      <ElInput
        v-model="SearchKeyword"
        :placeholder="'请输入' + entityName[props.type]"
        clearable
        prefix-icon="ep:search"
      />
    </div>

    <div class="tree-container" v-loading="Loading">
      <ElTree
        ref="treeRef"
        :data="treeData"
        :filter-node-method="filterNode"
        :expand-on-click-node="false"
        node-key="id"
        highlight-current
        :current-node-key="selectType?.id"
        default-expand-all
        @node-click="handleNodeClick"
      >
        <template #default="{ data }">
          <div class="custom-tree-node">
            <div class="node-content">
              <span class="node-label" :title="data.label">{{ data.label }}</span>
            </div>
            <div class="node-actions" v-if="props.type !== LeftType.type">
              <template v-if="data.id">
                <ElButton type="primary" link @click.stop="handleEdit(data)" title="编辑">
                  <Icon icon="ep:edit" />
                </ElButton>
                <ElButton type="danger" link @click.stop="handleDelete(data)" title="删除">
                  <Icon icon="ep:delete" />
                </ElButton>
              </template>
              <ElButton type="primary" v-else link @click.stop="handleAdd()" title="新增">
                <Icon icon="ep:plus" />
              </ElButton>
            </div>
            <div class="node-actions" v-else>
              <ElButton
                type="primary"
                v-if="data.id && data.parentId == 0"
                link
                @click.stop="handleAdd(data)"
                title="新增"
              >
                <Icon icon="ep:plus" />
              </ElButton>
              <template v-if="data.id && data.parentId !== 0">
                <ElButton type="primary" link @click.stop="handleEdit(data)" title="编辑">
                  <Icon icon="ep:edit" />
                </ElButton>
                <ElButton type="danger" link @click.stop="handleDelete(data)" title="删除">
                  <Icon icon="ep:delete" />
                </ElButton>
              </template>
            </div>
          </div>
        </template>
      </ElTree>
      <div v-if="treeData.length === 0" class="no-data">暂无{{ entityName[props.type] }}</div>
    </div>
  </div>

  <!-- Enum Type Dialog -->
  <EditDialog
    v-model="DialogVisible"
    :mode="DialogMode"
    :data="DialogData"
    :type="type"
    @save="handleSave"
  />
</template>

<style scoped lang="less">
.enum-types-panel {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border: 1px solid var(--el-border-color-light);
  border-right: none;
  flex-direction: column;
  border-radius: 4px 0 0 4px;
}

.panel-header {
  display: flex;
  padding: 12px 16px;
  background-color: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color-light);
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 16px;
  font-weight: 500;
}

.search-box {
  padding: 12px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.tree-container {
  padding: 8px;
  overflow-y: auto;
  flex: 1;
}

.custom-tree-node {
  display: flex;
  width: 88%;
  padding-right: 8px;
  font-size: 14px;
  flex: 1;
  align-items: center;
  justify-content: space-between;
}

.node-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.node-label {
  overflow: hidden;
  font-weight: 500;
  text-overflow: ellipsis;
}

.node-code {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.node-actions {
  gap: 4px;
  flex-shrink: 0;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: var(--el-text-color-secondary);
}
</style>
