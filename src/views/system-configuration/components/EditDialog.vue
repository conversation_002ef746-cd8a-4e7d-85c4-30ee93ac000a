<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { EnumTypeApi } from '@/api/systemConfiguration/Enum'
import {
  PropertyConfig,
  EnumConfig,
  LeftType,
  entityName,
  TypeConfig
} from '@/views/system-configuration/Config/help'
import { PropertyApi } from '@/api/systemConfiguration/Property'
defineOptions({
  name: 'EditDialog'
})
const props = defineProps<{
  modelValue: boolean
  mode: 'add' | 'edit'
  data: EnumTypeApi.Row | PropertyApi.categoryRow
  type: LeftType
}>()
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'save', data: EnumTypeApi.Row | PropertyApi.categoryRow, callback: () => void): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const title = computed(() => {
  const typeText = entityName[props.type]
  return props.mode === 'add' ? `新增${typeText}` : `编辑${typeText}`
})

const formRef = ref<FormInstance>()
const formData = reactive<EnumTypeApi.Row | PropertyApi.categoryRow>({
  selectorKey: '',
  enumeratedClassificationDesc: '',
  categoryId: ''
})
const formLoading = ref<boolean>(false)
// Handle close
const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate((valid) => {
    if (valid) {
      formLoading.value = true
      emit('save', { ...formData }, () => {
        formLoading.value = false
      })
    }
  })
}
watch(
  () => props.data,
  (newVal: EnumTypeApi.Row | PropertyApi.categoryRow) => {
    if (newVal) {
      Object.assign(formData, {
        ...newVal,
        id: newVal.id,
        enumeratedClassificationDesc: newVal.label || '',
        categoryName: newVal.label || '',
        categoryId: newVal.parentId,
        shoeName: newVal.typeShowName || '',
        name: newVal.name || '',
        remark: newVal.remark || ''
      })
    }
  },
  { immediate: true, deep: true }
)
const comfig = {
  [LeftType.enum]: EnumConfig.EnumTypeConfig,
  [LeftType.property]: PropertyConfig.PropertyTypeConfig,
  [LeftType.type]: TypeConfig.typeDialogFormConfig
}
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :title="title"
    width="500px"
  >
    <ElForm class="mt-4" ref="formRef" :model="formData" label-position="right" label-width="100px">
      <template v-for="item in comfig[type]" :key="item.field">
        <ElFormItem :label="item.label" :prop="item.field" :rules="item.rules">
          <component
            :disabled="item?.needDisable && mode === 'edit'"
            :is="item.component"
            @blur="formData[item.field] = formData[item.field].trim()"
            v-model="formData[item.field]"
            v-bind="{ ...item.props }"
          />
        </ElFormItem>
      </template>
    </ElForm>

    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit" :loading="formLoading">确定</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less">
.el-form-item .el-select {
  width: 100%;
}
</style>
