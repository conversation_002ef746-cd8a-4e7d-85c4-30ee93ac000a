<script setup lang="ts">
import { getConstraintByType, updateConstraint } from '@/api/systemConfiguration/type'
import { VxeGridProps } from 'vxe-table'
import { Attribute } from '@/components/PlmBase/type'

const props = defineProps<{
  modelValue: boolean
  constraintType: string
}>()
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'saveSuccess'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const gridRef = ref()
const loading = ref(false)
const rowDetail = ref<Attribute>()
const detailContent = computed(() => {
  const content: any[] = []
  const list = rowDetail.value?.constraintValue || {}
  for (const key in list) {
    if (Object.prototype.hasOwnProperty.call(list, key)) {
      content.push(list[key])
    }
  }
  return content
})
const gridOptions = computed(
  () =>
    ({
      border: true,
      rowConfig: {
        drag: true
      },
      editConfig: {
        trigger: 'click',
        mode: 'cell'
      },
      columns: [
        { field: 'label', title: 'label', editRender: { name: 'input' }, dragSort: true },
        { field: 'key', title: 'key', editRender: { name: 'input' } },
        { field: 'type', title: 'type' },
        { field: 'value', title: 'value', editRender: { name: 'input' }, dragSort: true }
      ],
      data: detailContent.value
    } as VxeGridProps)
)
const getDetail = async () => {
  if (!props.constraintType) {
    return false
  }
  loading.value = true
  const [err, result] = await getConstraintByType(props.constraintType)
  if (!err && result?.data) {
    rowDetail.value = {
      ...result.data,
      constraintValue: JSON.parse(result.data?.constraintValue || '{}')
    }
  }
  loading.value = false
}
const submit = async () => {
  loading.value = true
  const tableData = gridRef.value?.getFullData()
  const map = {}
  tableData.forEach((item) => {
    map[item.key] = item
  })
  rowDetail.value?.constraintType &&
    (await updateConstraint({
      ...rowDetail.value,
      constraintValue: JSON.stringify(map)
    }))
  emit('saveSuccess')
  visible.value = false
  loading.value = false
}
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      getDetail()
    }
  },
  {
    immediate: true,
    deep: true
  }
)
</script>

<template>
  <Dialog v-model="visible" :close-on-click-modal="false" title="修改约束" width="800px">
    <vxe-grid ref="gridRef" v-bind="gridOptions" />
    <template #footer>
      <div>
        <ElButton type="text" @click="visible = false">取消</ElButton>
        <ElButton type="primary" @click="submit" :loading="loading">确定</ElButton>
      </div>
    </template>
  </Dialog>
</template>

<style scoped lang="less"></style>
