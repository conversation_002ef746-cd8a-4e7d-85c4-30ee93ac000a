<script setup lang="ts">
import { TypeConfig, TypeOrPropertyType } from '@/views/system-configuration/Config/help'
import { scrollProp } from '@/plugins/vxeTable'
import { VxeGridInstance, VxeGridProps } from 'vxe-table'
import {
  deletePropertyRelation,
  deleteTypeRelation,
  getRelationDetail,
  getTypeRelationDetail
} from '@/api/systemConfiguration/type'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import AddPropertyDialog from '@/views/system-configuration/components/Type/AddPropertyDialog.vue'
import constraint from '@/views/system-configuration/components/Type/constraint.vue'
import { Attribute } from '@/components/PlmBase/type'
import { constraintMap } from '@/components/PlmBase/const'

const typeTableRef = ref<VxeGridInstance>()
const maxHeight = useTableHeight({ tableRef: typeTableRef })
const loading = ref(false)
const removeLoading = ref(false)
const contraintList = ref<Attribute[]>([])
const tableOptions = computed(
  () =>
    ({
      border: true,
      showOverflow: true,
      height: maxHeight.value - 20,
      loading: loading.value,
      data: contraintList.value,
      radioConfig: {
        checkMethod({ row }) {
          return !row.isExtends && !row.defaultProperty
        }
      },
      columns: TypeConfig.TypeTableConfig,
      ...scrollProp
    } as VxeGridProps)
)
const addDialog = reactive({
  visible: false,
  data: {}
})
defineOptions({
  name: 'ContraitTemplate'
})
const props = defineProps({
  constraintAttribute: {
    type: String,
    default: ''
  },
  constraintId: {
    type: String,
    default: ''
  }
})
const fetachRelationProperty = async () => {
  if (!props.constraintId) {
    return false
  }
  loading.value = true
  const [error, result] =
    props.constraintAttribute == TypeOrPropertyType.type
      ? await getRelationDetail(props.constraintId)
      : await getTypeRelationDetail(props.constraintId)
  if (error === null && result.data) {
    contraintList.value = result.data || []
    if (contraintList.value[0]) {
      openDetail(contraintList.value[0])
    } else {
      constraintDialog.visible = false
    }
  }
  loading.value = false
}
const addPropertyDialog = () => {
  addDialog.visible = true
}
//删除
const removeProperty = () => {
  const row = typeTableRef.value?.getRadioRecord()
  if (!row) {
    ElMessage.error('请选择一条数据')
    return
  }
  if (row.isExtends) {
    ElMessage.error('扩展属性不能删除')
    return
  }
  if (row.defaultProperty) {
    ElMessage.error('自有属性不能删除')
    return
  }
  ElMessageBox.confirm('确定要删除该属性的依赖关系？删除后将无法恢复', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    removeLoading.value = true
    props.constraintAttribute == TypeOrPropertyType.type
      ? await deletePropertyRelation({
          relationId: row.relationId,
          businessId: props.constraintId
        })
      : await deleteTypeRelation({
          relationId: row.relationId,
          businessId: props.constraintId
        })
    ElMessage.success('删除成功')
    removeLoading.value = false
    fetachRelationProperty()
  })
}
const constraintDialog = reactive({
  visible: false,
  id: 0,
  type: '',
  dataType: '', /// 数据真实类型,区分整数和实数
  name: '',
  isExtends: false, //是否是继承属性
  defaultProperty: false,
  relationId: '',
  reload: false
})
//约束
const openDetail = (row: Attribute) => {
  constraintDialog.visible = true
  constraintDialog.reload = !constraintDialog.reload
  constraintDialog.id = row.propertyId || 0
  constraintDialog.type = (row.type && constraintMap.typeMap?.[row.type]) || ''
  constraintDialog.name = row.name || ''
  constraintDialog.isExtends = row.isExtends || false
  constraintDialog.defaultProperty = row.defaultProperty || false
  constraintDialog.relationId = row.relationId || ''
}
watch(
  () => props.constraintId,
  async () => {
    await fetachRelationProperty()
  },
  {
    immediate: true,
    deep: true
  }
)
</script>
<template>
  <ContentWrap>
    <ElRow :gutter="20" ref="colRef">
      <ElCol :span="constraintDialog.visible ? 16 : 24" :style="{ height: maxHeight + 'px' }">
        <div class="operation">
          <ElButton @click="addPropertyDialog" type="primary"
            ><Icon icon="ep:plus" />添加属性</ElButton
          >
          <ElButton @click="removeProperty" :loading="removeLoading" type="primary"
            ><Icon icon="ep:delete" />移除</ElButton
          >
        </div>
        <VxeGrid ref="typeTableRef" v-bind="tableOptions">
          <template #isExtends="{ row }">
            <ElTag v-if="row.isExtends" type="success">是</ElTag>
            <span v-else>否</span>
          </template>
          <template #operation="{ row }">
            <ElButton link type="primary" @click="openDetail(row)">详情</ElButton>
          </template>
        </VxeGrid>
      </ElCol>
      <ElCol :span="8" :style="{ height: maxHeight + 'px' }">
        <!--如果是扩展属性不能进行编辑-->
        <constraint
          v-if="constraintDialog.visible"
          :constraint-attribute-id="constraintDialog.id"
          :constraintType="constraintDialog.type"
          :constraintAttribute="constraintAttribute"
          :name="constraintDialog.name"
          :defaultProperty="constraintDialog.defaultProperty"
          :isExtends="constraintDialog.isExtends"
          :relationId="constraintDialog.relationId"
          :dataType="constraintDialog.dataType"
          v-model:visible="constraintDialog.reload"
        />
      </ElCol>
    </ElRow>
  </ContentWrap>
  <AddPropertyDialog
    :constraintAttribute="constraintAttribute"
    :constraintId="constraintId"
    v-model="addDialog.visible"
    @update-success="fetachRelationProperty"
  />
</template>
<style scoped lang="less">
.operation {
  margin-bottom: 10px;
}
</style>
