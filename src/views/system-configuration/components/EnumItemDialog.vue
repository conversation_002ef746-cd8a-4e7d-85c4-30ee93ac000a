<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { EnumApi } from '@/api/systemConfiguration/Enum'
import { EnumConfig, StartOrBan } from '@/views/system-configuration/Config/help'

defineOptions({
  name: 'EnumItemDialog'
})

const props = defineProps<{
  modelValue: boolean
  mode: 'add' | 'edit'
  data: EnumApi.Row | null
}>()
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'save', data: EnumApi.Row, callback: () => void): void
}>()
const submitLoading = ref<boolean>(false)
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const title = computed(() => {
  return props.mode === 'add' ? '新增枚举值' : '编辑枚举值'
})

const formRef = ref<FormInstance>()
const formData = reactive<EnumApi.Row>({
  enumeratedClassificationId: '',
  enumeratedValue: '',
  enumeratedDesc: '',
  enumeratedCode: '',
  statusCode: StartOrBan.start
})

const formRules = reactive<FormRules>({
  enumeratedValue: [
    { required: true, message: '请输入枚举值', trigger: 'blur' },
    {
      pattern: /^[A-Z0-9_]{1,32}$/,
      message: '必须为英文字母or数字or_，字母必须大写，特殊符号只能是_',
      trigger: 'blur'
    }
  ],
  enumeratedCode: [
    {
      pattern: /^[A-Z0-9]+$/,
      message: '字母、数字、字母数字组合',
      trigger: 'blur'
    }
  ],
  enumeratedDesc: [{ required: true, message: '请输入枚举名称', trigger: 'blur' }],
  enumeratedClassificationId: [{ required: true, message: '请选择分类', trigger: 'change' }],
  statusCode: [{ required: true, message: '请选择状态', trigger: 'change' }]
})

// Watch for data changes
watch(
  () => props.data,
  (newVal) => {
    if (newVal) {
      Object.assign(formData, {
        id: newVal.id,
        enumeratedClassificationId: newVal.enumeratedClassificationId || '',
        enumeratedValue: newVal.enumeratedValue || '',
        enumeratedDesc: newVal.enumeratedDesc || '',
        enumeratedCode: newVal.enumeratedCode || '',
        statusCode: newVal.statusCode || StartOrBan.start
      })
    }
  },
  { immediate: true, deep: true }
)

// Handle close
const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
}

// Handle submit
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true
      emit('save', { ...formData }, () => {
        submitLoading.value = false
      })
    }
  })
}
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :title="title"
    width="500px"
  >
    <ElForm
      ref="formRef"
      class="mt-4"
      :model="formData"
      :rules="formRules"
      label-position="right"
      label-width="100px"
    >
      <template v-for="item in EnumConfig.EnumEditConfig" :key="item.field">
        <ElFormItem :label="item.label" :prop="item.field">
          <component
            :disabled="item.needDisable && mode === 'edit'"
            :is="item.component"
            v-model="formData[item.field]"
            @blur="formData[item.field] = formData[item.field].trim()"
            v-bind="item.props"
          />
        </ElFormItem>
      </template>
    </ElForm>

    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" :loading="submitLoading" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less">
/* Additional styles if needed */
</style>
