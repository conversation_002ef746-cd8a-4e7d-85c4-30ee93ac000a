<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { PropertyApi } from '@/api/systemConfiguration/Property'
import { PropertyConfig } from '@/views/system-configuration/Config/help'

defineOptions({
  name: 'PropertyDialog'
})

const props = defineProps<{
  modelValue: boolean
  mode: 'add' | 'edit'
  data: PropertyApi.Row
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'save', data: PropertyApi.Row, callback: () => void): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const title = computed(() => {
  return props.mode === 'add' ? '新增属性' : '编辑属性'
})
const submitLoading = ref<boolean>(false)
const formRef = ref<FormInstance>()
const formData = reactive<PropertyApi.Row>({
  name: '',
  value: '',
  description: '',
  type: '',
  categoryId: '',
  status: 'start'
})
const formRules = reactive<FormRules>({
  name: [{ required: true, message: '请输入属性名称', trigger: 'blur' }],
  value: [
    { required: true, message: '请输入属性内部值', trigger: 'blur' },
    {
      pattern: /^[A-Za-z0-9_]{1,32}$/,
      message: '必须为英文字母or数字or_，特殊符号只可以为_',
      trigger: 'blur'
    }
  ],
  description: [{ max: 500, message: '长度不能超过500个字符', trigger: 'blur' }],
  type: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
  categoryId: [{ required: true, message: '请选择分类', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
})

// Watch for data changes
watch(
  () => props.data,
  (newVal) => {
    if (newVal) {
      Object.assign(formData, {
        id: newVal.id,
        name: newVal.name || '',
        value: newVal.value || '',
        description: newVal.description || '',
        type: newVal.type,
        categoryId: newVal.categoryId || '',
        categoryName: newVal.categoryName || ''
      })
    }
  },
  { immediate: true, deep: true }
)

const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true
      emit('save', { ...formData }, () => {
        submitLoading.value = false
      })
    }
  })
}
</script>

<template>
  <Dialog
    v-model="visible"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :title="title"
    width="500px"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      class="mt-4"
      label-position="right"
      label-width="100px"
    >
      <template v-for="item in PropertyConfig.PropertyEditConfig" :key="item.field">
        <ElFormItem :label="item.label" :prop="item.field">
          <component
            :disabled="item.needDisable && mode === 'edit'"
            :is="item.component"
            @blur="formData[item.field] = formData[item.field].trim()"
            v-model="formData[item.field]"
            v-bind="item.props"
          />
        </ElFormItem>
      </template>
    </ElForm>

    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" :loading="submitLoading" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less">
/* Additional styles if needed */
</style>
