<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { sourceEnum } from '@/views/system-configuration/Config/help'
import { scrollProp } from '@/plugins/vxeTable'
import { VxeGridInstance, VxeGridProps, VxeGrid } from 'vxe-table'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { AddSourceDataItem } from '@/api/systemConfiguration/Enum'
import { EnumApi } from '@/api/systemConfiguration/Enum'
import { ApiSelect } from '@/components/ApiSelect'
import { Dialog } from '@/components/Dialog'
import { ElRadioGroup, ElRadio, ElMessage, ElInput, ElButton } from 'element-plus'
import { Icon } from '@/components/Icon'
import { getDictByCodeApi } from '@/api/common'
import { constArr } from '@/views/system-configuration/Config/help'

const enumItemTableRef = ref<VxeGridInstance>()
const enumItemLoading = ref(false)
const enumItemList = ref<AddSourceDataItem[]>([])
const originalEnumItemList = ref<AddSourceDataItem[]>([]) // 保存原始数据
const enumItemColumns = ref([])
const searchKeyword = ref('') // 搜索关键词
interface Content {
  type: string
  value: string
  label: string
}
const props = defineProps<{
  modelValue: boolean
  content: Content
}>()
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'submit', val: EnumApi.Row): void
}>()
const formData = reactive({
  name: '',
  source: ''
})
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const handleClose = () => {
  // 重置搜索
  searchKeyword.value = ''
  emit('update:modelValue', false)
}

// 监听弹窗打开状态和数据变化，自动选中行
watch(
  () => [props.modelValue, enumItemList.value, props.content],
  ([isVisible, tableData, content]) => {
    if (isVisible && tableData && tableData.length > 0 && content?.value) {
      // 延迟执行，确保表格已完全渲染
      setTimeout(() => {
        autoSelectRows()
      }, 100)
    }
  },
  { deep: true }
)

// 监听来源变化
watch(
  () => formData.source,
  (newSource) => {
    if (newSource) {
      changeSource()
    }
  }
)

// 防抖搜索
let searchTimer: NodeJS.Timeout | null = null

const handleSearch = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  searchTimer = setTimeout(() => {
    performSearch()
  }, 300) // 300ms 防抖
}

// 执行搜索
const performSearch = () => {
  if (!searchKeyword.value.trim()) {
    // 如果搜索关键词为空，显示所有数据
    enumItemList.value = [...originalEnumItemList.value]
    return
  }

  const keyword = searchKeyword.value.trim().toLowerCase()

  // 递归搜索函数
  const searchInTree = (items: AddSourceDataItem[]): AddSourceDataItem[] => {
    const result: AddSourceDataItem[] = []

    for (const item of items) {
      // 检查当前项是否匹配
      const matchesCode = (item.categoryCode || item.code || '').toLowerCase().includes(keyword)
      const matchesName = (item.name || item.categoryCnName || item.label || '')
        .toLowerCase()
        .includes(keyword)

      let matchedItem: AddSourceDataItem | null = null

      // 如果当前项匹配，包含整个子树
      if (matchesCode || matchesName) {
        matchedItem = { ...item }
      } else if (item.childList && item.childList.length > 0) {
        // 如果当前项不匹配，但子项可能匹配，递归搜索子项
        const matchedChildren = searchInTree(item.childList)
        if (matchedChildren.length > 0) {
          matchedItem = {
            ...item,
            childList: matchedChildren
          }
        }
      }

      if (matchedItem) {
        result.push(matchedItem)
      }
    }

    return result
  }

  const searchResult = searchInTree(originalEnumItemList.value)
  enumItemList.value = searchResult

  console.log(`搜索关键词: "${keyword}", 找到 ${searchResult.length} 条结果`)
}

// 清空搜索
const clearSearch = () => {
  searchKeyword.value = ''
  handleSearch()
}

// 自动选中表格行
const autoSelectRows = () => {
  if (!enumItemTableRef.value || !props.content?.value) {
    return
  }

  // // 检查是否是表格模式（有复选框配置）
  // const hasCheckbox = tableOptions.value.checkboxConfig
  // if (!hasCheckbox) {
  //   return
  // }

  // 解析content.value，支持字符串数组或逗号分隔的字符串
  let selectedValues: string[] = []
  try {
    // 尝试解析JSON数组
    selectedValues = JSON.parse(props.content.value)
  } catch {
    // 如果不是JSON，按逗号分割
    selectedValues = props.content.value
      .split(',')
      .map((v) => v.trim())
      .filter((v) => v)
  }

  if (selectedValues.length === 0) {
    return
  }

  // 递归查找匹配的行
  const findMatchingRows = (items: AddSourceDataItem[]): AddSourceDataItem[] => {
    const matchedRows: AddSourceDataItem[] = []

    for (const item of items) {
      // 检查当前行是否匹配
      const itemValue = item.value || item.code || item.categoryCode || item.id
      if (itemValue && selectedValues.includes(String(itemValue))) {
        matchedRows.push(item)
      }

      // 递归检查子项
      if (item.childList && item.childList.length > 0) {
        const childMatches = findMatchingRows(item.childList)
        matchedRows.push(...childMatches)
      }
    }
    return matchedRows
  }

  // 查找匹配的行
  const matchingRows = findMatchingRows(enumItemList.value)

  if (matchingRows.length > 0) {
    // 使用nextTick确保表格已渲染
    nextTick(() => {
      try {
        // 设置选中的行
        enumItemTableRef.value?.setCheckboxRow(matchingRows, true)
        enumItemTableRef.value?.setRadioRow(matchingRows[0])
      } catch (error) {
        console.error('自动选中行失败:', error)
      }
    })
  }
}
const handleSubmit = () => {
  if (!sourceEnum[formData.source].columns) {
    emit('update:modelValue', false)
    emit('submit', {
      type: formData.source,
      value: formData.source
    })
    return
  }
  let checked: AddSourceDataItem[] = []

  const rows =
    (enumItemTableRef.value?.getRadioRecord() && [enumItemTableRef.value?.getRadioRecord()]) ||
    enumItemTableRef.value?.getCheckboxRecords(true) ||
    []
  if (rows.length === 0) {
    ElMessage.warning('请选择来源')
    return
  }
  if (rows.length === 0) {
    // 尝试获取表格中所有数据并过滤出选中的行
    const allData = enumItemTableRef.value?.getData() || []
    checked = allData.filter((row) => enumItemTableRef.value?.isCheckedByCheckboxRow(row))
  } else {
    checked = rows
  }
  checked = rows
  // 简单过滤：排除有父节点也被选中的子节点
  const filteredNodes: AddSourceDataItem[] = []

  // 创建选中节点的ID集合
  const selectedIds = new Set()
  checked.forEach((node) => {
    const nodeId = node.id
    if (nodeId) selectedIds.add(nodeId)
  })
  // 过滤：如果节点的父节点也被选中，就排除该节点
  checked.forEach((node) => {
    const parentId = node.parentId || node.parent_id || node.pid
    // 如果没有父节点，或父节点没有被选中，就保留该节点
    if (
      !parentId ||
      parentId === 0 ||
      parentId === '0' ||
      parentId === '' ||
      !selectedIds.has(parentId)
    ) {
      filteredNodes.push(node)
    }
  })
  // 提取过滤后节点的 code 编码集合
  const filteredCodes = filteredNodes
    .map((item) => {
      return item.categoryCode || item.code
    })
    .filter((code) => code) // 过滤掉空值

  // 关闭对话框并提交结果
  emit('update:modelValue', false)
  emit('submit', {
    type: formData.source,
    value: filteredCodes.join(','), // 兼容原有格式，使用第一个值
    label: filteredNodes.map((item) => item.name || item.categoryCnName || item.label).join(',')
  })
}
watch(
  () => props.modelValue,
  () => {
    if (props.modelValue) {
      formData.source = props.content?.type || sourceEnum['ENUM'].type
    }
  }
)
watch(
  () => formData.source,
  () => {
    changeSource()
  }
)

//切换来源
const changeSource = () => {
  // 重置搜索
  searchKeyword.value = ''

  const source = sourceEnum[formData.source]
  const { apiConfig, params, columns = [] } = source
  enumItemColumns.value = columns
  enumItemLoading.value = true

  if (columns.length <= 0) {
    enumItemLoading.value = false
    enumItemList.value = []
    originalEnumItemList.value = []
    return
  }

  apiConfig
    .api(params)
    .then((res) => {
      const result = Array.isArray(res) ? res[1] : res
      const data = result.data || result.datas || []
      const processedData = addParentId(data)

      // 保存原始数据和显示数据
      originalEnumItemList.value = processedData
      enumItemList.value = [...processedData]
      enumItemLoading.value = false
    })
    .catch((error) => {
      console.error('获取数据失败:', error)
      enumItemLoading.value = false
      originalEnumItemList.value = []
      enumItemList.value = []
    })
}
const addParentId = (tree, parentId = null) => {
  return tree.map((node) => {
    const newNode = { ...node, parentId }

    if (node.childList && node.childList.length > 0) {
      newNode.childList = addParentId(node.childList, node.id)
    }

    return newNode
  })
}
const tableOptions = computed(() => {
  const props = {
    border: true,
    showOverflow: true,
    height: 280,
    loading: enumItemLoading.value,
    data: enumItemList.value,
    treeConfig: { childrenField: 'childList' },
    columns: enumItemColumns.value,
    ...scrollProp
  } as VxeGridProps
  if (formData.source === sourceEnum['MATERIAL'].type) {
    props['checkboxConfig'] = { visibleMethod: ({ row }) => !row.parentId }
  }
  return props
})
</script>
<template>
  <Dialog v-model="visible" :before-close="handleClose" title="添加来源" width="800px">
    <template #header>
      <div>添加来源</div>
    </template>
    <div class="mb-4">
      <ApiSelect
        v-model="formData.source"
        v-bind="{
          multiple: false,
          component: ElRadioGroup,
          childComponent: ElRadio,
          apiConfig: { api: getDictByCodeApi, config: { label: 'label', value: 'value' } },
          params: { dictCode: constArr.source }
        }"
      />
    </div>

    <div v-if="sourceEnum[formData.source].columns?.length > 0" class="search-container">
      <!-- 搜索框 -->
      <div class="mb-3 flex items-center gap-2">
        <ElInput
          v-model="searchKeyword"
          placeholder="请输入编码或名称进行搜索"
          clearable
          class="flex-1"
          @input="handleSearch"
          @clear="clearSearch"
          @keyup.enter="performSearch"
        >
          <template #prefix>
            <Icon icon="ep:search" />
          </template>
        </ElInput>
        <ElButton @click="clearSearch" v-if="searchKeyword" type="info" plain>
          <Icon icon="ep:refresh" />
          重置
        </ElButton>
      </div>

      <!-- 搜索统计信息 -->
      <div v-if="searchKeyword" class="mb-2 text-sm text-gray-600">
        <span v-if="enumItemList.length > 0">
          找到 <strong>{{ enumItemList.length }}</strong> 条匹配结果
        </span>
        <span v-else class="text-orange-500">
          <Icon icon="ep:warning" class="mr-1" />
          未找到匹配的数据，请尝试其他关键词
        </span>
      </div>

      <!-- 搜索结果为空的提示 -->
      <div v-if="searchKeyword && enumItemList.length === 0" class="search-result-tip">
        <Icon icon="ep:search" class="text-2xl mb-2" />
        <div
          >未找到包含 "<strong>{{ searchKeyword }}</strong
          >" 的数据</div
        >
        <div class="text-xs mt-1">请尝试使用其他关键词或检查拼写</div>
      </div>

      <VxeGrid
        ref="enumItemTableRef"
        :key="`${formData.source}-${searchKeyword}`"
        v-bind="tableOptions"
        v-show="enumItemList.length > 0"
      />
    </div>
    <div class="text-center" style="height: 100px; line-height: 100px" v-else>
      该类型不需要选择
    </div>
    <template #footer>
      <ElButton @click="handleClose">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>

<style scoped lang="less">
.search-container {
  .el-input {
    .el-input__wrapper {
      border-radius: 6px;
    }
  }

  .search-result-tip {
    padding: 16px;
    color: var(--el-text-color-secondary);
    text-align: center;
    background-color: var(--el-fill-color-extra-light);
    border-radius: 6px;

    .el-icon {
      color: var(--el-color-warning);
    }
  }
}

// 表格样式优化
:deep(.vxe-table) {
  .vxe-body--row {
    &:hover {
      background-color: var(--el-fill-color-light);
    }
  }

  // 搜索高亮样式
  .search-highlight {
    padding: 1px 2px;
    color: var(--el-color-warning-dark-2);
    background-color: var(--el-color-warning-light-9);
    border-radius: 2px;
  }
}
</style>
