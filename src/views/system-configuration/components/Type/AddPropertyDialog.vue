<script setup lang="ts">
import { LayoutForm } from '@/components/LayoutForm'
import { SelectPlus } from '@/components/Business/SelectPlus'
import { PropertyApi, propertyList } from '@/api/systemConfiguration/Property'
import { getMaxHeight, useTableHeight } from '@/hooks/web/useTableHeight'
import { VxeGridInstance } from 'vxe-table'
import { saveProperty, saveTypeProperty } from '@/api/systemConfiguration/type'
import { ElMessage } from 'element-plus'
import { TypeOrPropertyType } from '@/views/system-configuration/Config/help'
const searchConfig = [
  {
    label: '属性内部值',
    field: 'value',
    component: ElInput,
    span: 6,
    props: {
      placeholder: '请输入属性内部值'
    }
  },
  {
    label: '属性名称',
    field: 'name',
    component: ElInput,
    span: 6,
    props: {
      placeholder: '请输入属性名称'
    }
  },
  {
    label: '修改时间',
    field: 'modifyTime',
    component: ElDatePicker,
    span: 6,
    props: {
      type: 'daterange',
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD'
    }
  },
  {
    label: '属性类型',
    field: 'type',
    component: SelectPlus,
    span: 6,
    props: {
      apiKey: 'PROPERTY_DATA_TYPE',
      filterable: true,
      virtualized: true
    }
  },
  {
    label: '分类',
    field: 'categoryId',
    component: SelectPlus,
    span: 6,
    props: {
      apiKey: 'getCategoryListApi',
      filterable: true,
      virtualized: true,
      configuration: {
        key: 'selectorValue',
        value: 'selectorValue',
        label: 'selectorKey'
      }
    }
  }
]
const TableConfig: any[] = [
  { type: 'checkbox', fixed: 'left', width: 40 },
  { type: 'seq', width: 60, title: '序号' },
  { field: 'value', title: '属性内部值', sortable: true, minWidth: 120 },
  { field: 'categoryName', title: '分类', minWidth: 120 },
  { field: 'typeItemName', title: '数据类型', sortable: true, minWidth: 100 },
  { field: 'name', title: '属性名称', sortable: true, minWidth: 120 },
  { field: 'description', title: '属性描述', minWidth: 150 },
  { field: 'statusItemName', title: '状态', minWidth: 150 }
]
const props = defineProps<{
  modelValue: boolean
  constraintAttribute: string
  constraintId: string
}>()
const propertyTableRef = ref<VxeGridInstance>()
const layoutFormRef = ref()
const submitLoading = ref()
const maxHeight = useTableHeight({ tableRef: propertyTableRef, pagerRef: layoutFormRef })
watch(
  () => maxHeight.value,
  (val) => {
    console.log(val)
  },
  {
    immediate: true,
    deep: true
  }
)
const formData = reactive<PropertyApi.Params>({
  name: '',
  value: '',
  startTime: '',
  endeTime: '',
  modifyTime: '',
  type: '',
  categoryId: ''
})
const propertyDialogData = ref<PropertyApi.Row[]>([])
const propertyLoading = ref()
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'updateSuccess'): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
//搜索
const getParams = () => {
  return {
    name: formData.name,
    value: formData.value,
    startTime: formData.modifyTime?.[0],
    endTime: formData.modifyTime?.[1],
    type: formData.type,
    categoryId: formData.categoryId,
    businessType: props.constraintAttribute,
    businessId: props.constraintId
  }
}
const fetchPropertyItems = async () => {
  if (propertyLoading.value) return false
  propertyLoading.value = true
  const params = getParams()
  const [error, result] = await propertyList(params)
  if (error === null && result) {
    propertyDialogData.value = result.data || []
  } else {
    ElMessage.error('获取属性列表失败')
  }
  propertyLoading.value = false
}
// 重置表单
const handleReset = () => {
  if (!layoutFormRef.value) return
  layoutFormRef.value?.formRef.resetFields()
  fetchPropertyItems()
}
watch(
  () => visible.value,
  (val) => {
    handleReset()
    if (val) {
      fetchPropertyItems()
    }
  },
  {
    immediate: true,
    deep: true
  }
)
const handleSubmit = async () => {
  const perpertListCheck = propertyTableRef.value?.getCheckboxRecords() || []
  if (!perpertListCheck.length) {
    ElMessage.warning('请选择属性')
    return
  }
  submitLoading.value = true
  let error, result
  if (props.constraintAttribute == TypeOrPropertyType.property) {
    ;[error, result] = await saveTypeProperty({
      propertyId: perpertListCheck.map((e) => e.id),
      typeId: props.constraintId || 0
    })
  } else {
    ;[error, result] = await saveProperty({
      propertyId: perpertListCheck.map((e) => e.id),
      productCategoryId: props.constraintId || 0
    })
  }
  submitLoading.value = false
  if (error === null && result) {
    propertyDialogData.value = result.data || []
    emit('updateSuccess')
    visible.value = false
  }
}
const updateHeight = () => {
  maxHeight.value = getMaxHeight({ tableRef: propertyTableRef })
}
</script>

<template>
  <Dialog
    @fullscreen="updateHeight"
    v-model="visible"
    :parentScroll="false"
    title="添加属性"
    width="800px;"
  >
    <div :style="{ height: maxHeight + 'px' }">
      <div>
        <LayoutForm
          ref="layoutFormRef"
          :model="formData"
          :loading="propertyLoading"
          query-form
          :span="8"
          @reset="handleReset"
          @search="fetchPropertyItems"
        >
          <template v-for="(field, index) in searchConfig" :key="index">
            <ElFormItem :label="field.label" :prop="field.field">
              <!-- 根据组件类型渲染不同的表单控件 -->
              <component
                :is="field.component"
                v-model="formData[field.field]"
                v-bind="field.props || {}"
              />
            </ElFormItem>
          </template>
        </LayoutForm>
      </div>
      <VxeGrid
        ref="propertyTableRef"
        :loading="propertyLoading"
        :max-height="maxHeight - 100 + 'px'"
        :min-height="100"
        :data="propertyDialogData"
        :columns="TableConfig"
      />
    </div>
    <template #footer>
      <ElButton @click="visible = false">取消</ElButton>
      <ElButton type="primary" :loading="submitLoading" @click="handleSubmit">确定</ElButton>
    </template>
  </Dialog>
</template>
