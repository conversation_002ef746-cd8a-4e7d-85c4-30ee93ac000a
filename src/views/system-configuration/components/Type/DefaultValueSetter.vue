<script setup lang="ts">
import { computed, watch, ref } from 'vue'
import { ElInput, ElFormItem, ElButton, ElAlert } from 'element-plus'
import FormItemRender from '@/components/PlmBase/component/FormItemRender.vue'
import { constraintMap, keyMap } from '@/components/PlmBase/const'
import AddSourceDialog from '@/views/system-configuration/components/Type/AddSourceDialog.vue'
const { typeMap } = constraintMap

// TypeScript interfaces
interface ConstraintItem {
  value: any
  [key: string]: any
}

interface ConstraintList {
  [key: string]: ConstraintItem
}

interface SourceOptions {
  value?: string
  label?: string
  type?: string
  [key: string]: any
}
type DefaultValueType = string | number | boolean | any[] | Record<string, any>
// Props with proper typing
interface Props {
  constraintType: string
  constraintList: ConstraintList
  defaultValue?: DefaultValueType
  defaultName?: string
  options?: SourceOptions
  disabled?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  defaultValue: '',
  defaultName: '',
  options: () => ({}),
  disabled: false,
  loading: false
})

// Emits with proper typing
interface Emits {
  (e: 'update:defaultValue', value: DefaultValueType): void
  (e: 'update:defaultName', value: string): void
  (e: 'update:options', value: SourceOptions): void
}

const emit = defineEmits<Emits>()

// 本地响应式数据
const localDefaultValue = computed({
  get: (): DefaultValueType => props.defaultValue,
  set: (val: DefaultValueType) => emit('update:defaultValue', val)
})

const localDefaultName = computed({
  get: (): string => props.defaultName || '',
  set: (val: string) => emit('update:defaultName', val)
})

const localOptions = computed({
  get: (): SourceOptions => props.options || {},
  set: (val: SourceOptions) => emit('update:options', val)
})

// 数据源选择对话框
const sourceDialogVisible = ref(false)
// 打开数据源选择对话框
const openSourceDialog = () => {
  sourceDialogVisible.value = true
}

// 处理数据源选择
const handleSourceSelect = (sourceData) => {
  localOptions.value = sourceData
  sourceDialogVisible.value = false
}

// 是否需要显示数据源选择
const needDataSource = computed(() => {
  return props.constraintType === typeMap.OBJECT
})

watch(
  () => props.options,
  (newOptions: SourceOptions) => {
    console.log('检测到选项变化:', newOptions)
  },
  {
    deep: true
  }
)
</script>

<template>
  <div class="default-value-setter">
    <!-- 数据源选择（仅对象类型需要） -->
    <div v-if="needDataSource" class="source-selector">
      <ElFormItem label="数据来源">
        <div class="source-controls">
          <ElButton @click="openSourceDialog" :disabled="disabled" size="small">
            选择数据来源
          </ElButton>
          <span v-if="localOptions?.value" class="source-value">
            {{ localOptions?.label }} ({{ localOptions?.value }})
            <Icon icon="ep:close" @click="localOptions = {}" />
          </span>
          <ElAlert v-else type="info" :closable="false" show-icon> 请先选择数据来源 </ElAlert>
        </div>
      </ElFormItem>
    </div>

    <!-- 根据约束类型渲染不同的输入组件 -->
    <div class="component-container">
      <ElFormItem label="默认值" v-if="constraintType !== typeMap.ATTACHMENT">
        <FormItemRender
          :constraintType="constraintType"
          :constraintList="constraintList"
          v-model="localDefaultValue"
          :disabled="disabled"
        />
      </ElFormItem>
    </div>

    <!-- 默认别名输入 -->
    <div class="default-name-container">
      <ElFormItem label="默认别名">
        <ElInput v-model="localDefaultName" placeholder="请输入默认别名" :disabled="disabled" />
      </ElFormItem>
    </div>

    <!-- 数据源选择对话框 -->
    <AddSourceDialog
      :content="localOptions"
      v-model="sourceDialogVisible"
      @submit="handleSourceSelect"
    />
  </div>
</template>
<style scoped lang="less">
.default-value-setter {
  width: 100%;

  .component-container {
    margin-bottom: 16px;
  }

  .source-selector {
    margin-bottom: 16px;

    .source-controls {
      display: flex;
      align-items: center;
      gap: 12px;

      .source-value {
        position: relative;
        padding: 4px 8px;
        font-size: 14px;
        color: var(--el-text-color-regular);
        background-color: var(--el-fill-color-light);
        border: 1px solid var(--el-border-color-light);
        border-radius: 4px;

        .el-icon {
          position: absolute;
          top: 0;
          right: 0;
          color: red;
          cursor: pointer;
        }
      }
    }
  }

  .default-name-container {
    margin-top: 16px;
  }
}
</style>
