<script setup lang="ts">
import draggable from 'vuedraggable'
import { Plus, Delete } from '@element-plus/icons-vue'
import {
  ElForm,
  ElFormItem,
  ElButton,
  ElIcon,
  ElTooltip,
  ElPopover,
  ElOption,
  ElSelect,
  ElMessage
} from 'element-plus'
import { getDictByCodeApi } from '@/api/common'
import { constArr } from '@/views/system-configuration/Config/help'
import { ApiSelect } from '@/components/ApiSelect'
import { Icon } from '@/components/Icon'
import { constraintMap } from '@/components/PlmBase/const'

const { typeMap, componentRap } = constraintMap

// TypeScript interfaces
interface GridConfig {
  columns: number
  defaultSpan: number
  gutter: number
}

interface FormElement {
  value: string
  label: string
  type: string
  span?: number
  visibility?: string
  constraintValue?: string
}

interface FormData {
  [key: string]: any
}
interface Props {
  modelValue: FormElement[][]
  gridConfig: GridConfig
  formData: FormData
  columnWidth: string
}
const props = defineProps<Props>()

interface Emits {
  (e: 'update:modelValue', value: FormElement[][]): void
  (e: 'add-row'): void
  (e: 'insert-row', payload: { position: number }): void
  (
    e: 'remove-element',
    payload: { rowIndex: number; elementIndex: number; element: FormElement }
  ): void
  (e: 'update:formData', value: FormData): void
}

const emit = defineEmits<Emits>()

// 响应式状态
const dragOverRowIndex = ref<number | null>(null)
const dragOverPosition = ref<'before' | 'after' | null>(null)
const isDragging = ref(false)

// 根据约束条件获取表单项属性
const getFormItemProps = (element: FormElement): Record<string, any> => {
  if (!element.constraintValue) return {}

  try {
    const constraint = JSON.parse(element.constraintValue)
    const formProps: Record<string, any> = {}
    const needArr = ['disabled', 'placeholder', 'clearable', 'imageSize']

    needArr.forEach((item: string) => {
      formProps[item] = constraint[item]?.value || ''
    })

    // 图片需要处理
    if (element.type === typeMap.ATTACHMENT) {
      formProps.listType = 'picture-card'
      formProps.autoUpload = 'false'
    }

    // textarea需要处理
    if (element.type === typeMap.RICH_TEXT) {
      formProps.type = 'textarea'
      formProps.autoSize = `{ minRows:${constraint['rows']?.value} }`
    }

    if (element.visibility === 'readOnly') {
      // 根据可见性设置禁用状态
      formProps.disabled = true
    }

    return formProps
  } catch (error) {
    console.error('Error parsing constraint value:', error)
    return {}
  }
}

// 计算每行的总列数，用于检查是否超出限制
const getRowColumns = (row: FormElement[]): number => {
  return row.reduce((sum: number, item: FormElement) => sum + (item.span || 1), 0)
}

// 调整元素的列宽
const adjustElementSpan = (element: FormElement, newSpan: number, rowIndex: number): void => {
  const row = props.modelValue[rowIndex]
  const currentSpan = element.span || 1
  const otherColumnsUsed = getRowColumns(row) - currentSpan

  // 确保不超过行的总列数
  const maxAllowedSpan = props.gridConfig.columns - otherColumnsUsed
  element.span = Math.min(newSpan, maxAllowedSpan)
}

// 更新元素的可见性
const updateElementVisibility = (element: FormElement, visibility: string): void => {
  element.visibility = visibility
}

// 删除元素
const removeElement = (rowIndex: number, elementIndex: number): void => {
  const element = props.modelValue[rowIndex][elementIndex]
  const newValue = [...props.modelValue]
  newValue[rowIndex].splice(elementIndex, 1)

  // 如果行为空且不是唯一的行，则删除该行
  if (newValue[rowIndex].length === 0 && newValue.length > 1) {
    newValue.splice(rowIndex, 1)
  }
  emit('update:modelValue', newValue)

  // 从表单数据中删除 - 使用emit更新formData而不是直接修改
  if (element && element.value) {
    const updatedFormData = { ...props.formData }
    delete updatedFormData[element.value]
    emit('update:formData', updatedFormData)
  }

  // 通知父组件元素被删除，以便更新左侧可用属性
  emit('remove-element', { rowIndex, elementIndex, element })
}
// 添加新行
const addRow = (): void => {
  const newValue = [...props.modelValue, []]
  emit('update:modelValue', newValue)
  emit('add-row')
}

// 在指定位置插入新行
const insertRowAt = (position: number): void => {
  const newValue = [...props.modelValue]
  newValue.splice(position, 0, [])
  emit('update:modelValue', newValue)
  emit('insert-row', { position })
}

// 删除行
const removeRow = (rowIndex: number): void => {
  if (props.modelValue.length <= 1) {
    ElMessage.warning('至少需要保留一行')
    return
  }

  const newValue = [...props.modelValue]
  const removedRow = newValue.splice(rowIndex, 1)[0]

  // 清理被删除行中元素的表单数据
  const updatedFormData = { ...props.formData }
  removedRow.forEach((element) => {
    if (element.value) {
      delete updatedFormData[element.value]
    }
  })

  emit('update:modelValue', newValue)
  emit('update:formData', updatedFormData)
}

// 处理拖拽开始
const onDragStart = (): void => {
  isDragging.value = true
}

// 处理拖拽结束
const onDragEnd = (): void => {
  isDragging.value = false
  dragOverRowIndex.value = null
  dragOverPosition.value = null
}
// 简化的拖拽组配置
const getDragGroupConfig = () => {
  return {
    name: 'form-elements',
    pull: true,
    put: true
  }
}

// 添加计算属性来判断行是否已满
const isRowFull = (rowIndex: number): boolean => {
  const row = props.modelValue[rowIndex]
  const usedColumns = row.reduce((sum: number, item: FormElement) => sum + (item.span || 1), 0)
  return usedColumns >= props.gridConfig.columns
}
const updateRowValue = (rowIndex: number, newValue: FormElement[]) => {
  //为了防止立马更新导致拖动到另外一行不成功
  setTimeout(() => {
    const updatedValue = [...props.modelValue]
    // 只设置默认属性，不做容量检查（让用户自己调整）
    updatedValue[rowIndex] = newValue
    emit('update:modelValue', updatedValue)
  }, 0)
}
</script>

<template>
  <div class="draggable-panel">
    <ElForm label-position="left" :model="formData">
      <!-- 网格布局区域 -->
      <div class="grid-layout">
        <div class="grid-container">
          <!-- 每一行 -->
          <div v-for="(row, rowIndex) in modelValue" :key="rowIndex" class="grid-row-wrapper">
            <!-- 行间插入区域 (上方) -->
            <div
              v-if="dragOverRowIndex === rowIndex && dragOverPosition === 'before'"
              class="row-insert-zone active"
              @click="insertRowAt(rowIndex)"
            >
              <div class="insert-line">
                <Icon icon="ep:plus" class="insert-icon" />
                <span class="insert-text">在此处插入新行</span>
              </div>
            </div>

            <!-- 实际的行容器 -->
            <div
              class="grid-row"
              :class="{
                'row-full': isRowFull(rowIndex)
              }"
            >
              <!-- 行控制按钮 -->
              <div class="row-controls">
                <ElTooltip content="在上方插入行" placement="top">
                  <ElButton type="text" @click="insertRowAt(rowIndex)" class="row-control-btn">
                    <Icon icon="ep:plus" />
                  </ElButton>
                </ElTooltip>

                <ElTooltip content="删除此行" placement="top">
                  <ElButton
                    type="text"
                    @click="removeRow(rowIndex)"
                    class="row-control-btn delete-btn"
                    :disabled="modelValue.length <= 1"
                  >
                    <Icon icon="ep:delete" />
                  </ElButton>
                </ElTooltip>

                <span class="row-index">第 {{ rowIndex + 1 }} 行</span>
              </div>

              <!-- 拖拽区域 -->
              <draggable
                :model-value="row"
                @update:model-value="(val) => updateRowValue(rowIndex, val)"
                :group="getDragGroupConfig()"
                item-key="value"
                @start="onDragStart"
                @end="onDragEnd"
                class="grid-row-items"
                :class="{ 'row-full': isRowFull(rowIndex) }"
                :disabled="false"
                animation="300"
                ghost-class="sortable-ghost"
                chosen-class="sortable-chosen"
                drag-class="sortable-drag"
                :force-fallback="false"
                :fallback-tolerance="0"
              >
                <template #item="{ element, index }">
                  <div
                    class="grid-item"
                    :style="{
                      width: `calc(${element.span || 6} * ${columnWidth} + ${
                        (element.span || 6) - 1.5
                      } * ${gridConfig.gutter}px)`,
                      marginRight: `${gridConfig.gutter}px`
                    }"
                  >
                    <div class="grid-item-content">
                      <ElFormItem :label="element.label" :prop="element.value">
                        <!-- 根据类型渲染不同的表单控件 -->
                        <component
                          :is="componentRap[element.type.toUpperCase()]"
                          v-bind="getFormItemProps(element)"
                        />
                      </ElFormItem>

                      <!-- 控制按钮 -->
                      <div class="grid-item-controls">
                        <!-- 调整宽度 -->
                        <div class="control-buttons">
                          <ElTooltip content="调整宽度" placement="top">
                            <ElSelect
                              v-model="element.span"
                              size="small"
                              style="width: 54px; margin-right: 4px"
                              @change="(val) => adjustElementSpan(element, val, rowIndex)"
                            >
                              <ElOption
                                v-for="i in gridConfig.columns"
                                :key="i"
                                :value="i"
                                :label="`${i}列`"
                              />
                            </ElSelect>
                          </ElTooltip>

                          <!-- 可见性设置 -->
                          <ElTooltip content="可见性设置" placement="top">
                            <ElPopover placement="bottom" :width="200" trigger="click">
                              <template #reference>
                                <ElButton type="primary" size="small" circle>
                                  <Icon
                                    v-if="element.visibility === 'HIDDEN'"
                                    icon="mdi:hide-outline"
                                  />
                                  <Icon
                                    v-if="element.visibility === 'READONLY'"
                                    icon="mdi:eye-outline"
                                  />
                                  <Icon
                                    v-if="element.visibility === 'READWRITE'"
                                    icon="jam:write"
                                  />
                                  <Icon v-if="!element.visibility" icon="uil:setting" />
                                </ElButton>
                              </template>
                              <div class="visibility-settings">
                                <p class="setting-title">可见性设置</p>
                                <ApiSelect
                                  v-model="element.visibility"
                                  @change="(val:string) => updateElementVisibility(element, val)"
                                  v-bind="{
                                    multiple: false,
                                    component: ElSelect,
                                    apiConfig: {
                                      api: getDictByCodeApi,
                                      config: {
                                        label: 'label',
                                        value: 'value'
                                      }
                                    },
                                    params: {
                                      dictCode: constArr['visibility']
                                    },
                                    clearable: true
                                  }"
                                />
                              </div>
                            </ElPopover>
                          </ElTooltip>
                        </div>

                        <!-- 删除按钮 -->
                        <ElButton
                          type="danger"
                          size="small"
                          circle
                          @click="removeElement(rowIndex, index)"
                        >
                          <ElIcon><Delete /></ElIcon>
                        </ElButton>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>

            <!-- 行间插入区域 (下方) -->
            <div
              v-if="dragOverRowIndex === rowIndex && dragOverPosition === 'after'"
              class="row-insert-zone active"
              @click="insertRowAt(rowIndex + 1)"
            >
              <div class="insert-line">
                <Icon icon="ep:plus" class="insert-icon" />
                <span class="insert-text">在此处插入新行</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 添加新行按钮 -->
      <div class="add-row mb-1">
        <ElButton type="primary" @click="addRow">
          <ElIcon><Plus /></ElIcon> 添加新行
        </ElButton>
      </div>
      <!-- 空状态提示 -->
      <div v-if="modelValue.every((row) => row.length === 0)" class="empty-target">
        拖拽左侧属性到此区域生成表单
      </div>
    </ElForm>
  </div>
</template>

<style scoped lang="less">
.draggable-panel {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;

  .grid-layout {
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
  }

  .grid-container {
    flex: 1;
    padding-right: 8px;
    margin-bottom: 12px;
  }

  .grid-row-wrapper {
    position: relative;
    margin-bottom: 8px;
  }

  .grid-row {
    position: relative;
    display: flex;
    min-height: 80px;
    padding: 8px 8px 8px 60px; // 为行控制按钮留出空间
    background-color: rgba(0, 0, 0, 0.02);
    border: 2px solid transparent;
    border-radius: 8px;
    transition: all 0.3s ease;
    flex-wrap: wrap;

    &.drag-over {
      background-color: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary);
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);

      .row-controls {
        opacity: 1;
      }
    }
  }

  .row-controls {
    position: absolute;
    top: 50%;
    left: 8px;
    z-index: 10;
    display: flex;
    opacity: 0.6;
    transform: translateY(-50%);
    transition: opacity 0.3s ease;
    flex-direction: column;
    align-items: center;
    gap: 4px;

    .row-control-btn {
      display: flex;
      width: 24px;
      height: 24px;
      padding: 0;
      margin-left: 0;
      border-radius: 50%;
      align-items: center;
      justify-content: center;

      &:hover {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }

      &.delete-btn:hover {
        color: var(--el-color-danger);
        background-color: var(--el-color-danger-light-9);
      }
    }

    .row-index {
      margin-top: 4px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      writing-mode: vertical-rl;
      text-orientation: mixed;
    }
  }

  .row-insert-zone {
    position: relative;
    height: 4px;
    margin: 4px 0;
    cursor: pointer;
    border-radius: 2px;
    transition: all 0.3s ease;

    &:hover,
    &.active {
      height: 40px;
      background-color: var(--el-color-primary-light-9);
      border: 2px dashed var(--el-color-primary);

      .insert-line {
        opacity: 1;
      }
    }

    .insert-line {
      position: absolute;
      top: 50%;
      left: 50%;
      display: flex;
      font-size: 14px;
      color: var(--el-color-primary);
      white-space: nowrap;
      opacity: 0;
      transform: translate(-50%, -50%);
      transition: opacity 0.3s ease;
      align-items: center;
      gap: 8px;

      .insert-icon {
        font-size: 16px;
      }
    }
  }

  .empty-row-hint {
    display: flex;
    padding: 20px;
    margin: 8px 0;
    font-size: 14px;
    color: var(--el-text-color-placeholder);
    border: 2px dashed var(--el-border-color-lighter);
    border-radius: 6px;
    transition: all 0.3s ease;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &:hover {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary-light-5);
    }
  }

  .grid-row-items {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }

  .grid-item {
    margin-bottom: 12px;
    transition: all 0.3s;
  }

  .grid-item-content {
    position: relative;
    padding: 12px;
    overflow: hidden;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .grid-item-controls {
    display: flex;
    padding-top: 8px;
    margin-top: 8px;
    border-top: 1px dashed #eee;
    justify-content: space-between;
    align-items: center;
  }

  .control-buttons {
    display: flex;
    align-items: center;
  }

  .visibility-settings {
    padding: 8px;

    .setting-title {
      margin-top: 0;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: bold;
    }
  }

  .add-to-row {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80px;
  }

  .add-placeholder {
    display: flex;
    width: 100%;
    height: 100%;
    min-height: 80px;
    color: #999;
    background-color: rgba(0, 0, 0, 0.02);
    border: 1px dashed #ccc;
    border-radius: 4px;
    align-items: center;
    justify-content: center;
  }

  .add-row {
    display: flex;
    margin-top: 12px;
    justify-content: center;
  }

  .empty-target {
    padding: 40px 0;
    font-size: 14px;
    color: #999;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.02);
    border: 1px dashed #ccc;
    border-radius: 4px;
  }
}

/* 拖拽时的视觉效果 */
.sortable-chosen {
  z-index: 1000;
  background-color: #ebf5fb !important;
  opacity: 0.8;
  transform: rotate(5deg) scale(1.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.sortable-ghost {
  background: #c8ebfb !important;
  border: 2px dashed var(--el-color-primary);
  opacity: 0.5;
}

.sortable-drag {
  opacity: 0.8;
  transform: rotate(2deg);
}

/* 跨行拖拽指示器 */
.cross-row-indicator {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 4px;
  pointer-events: none;
  background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-success));
  opacity: 0;
  transition: opacity 0.3s ease;

  &.active {
    opacity: 1;
  }
}

:deep(.el-form-item__content) {
  width: 100%;

  > div {
    width: 100%;

    .el-upload-list--picture-card {
      width: 100%;
    }
  }
}

.row-full {
  position: relative;
  background-color: var(--el-color-warning-light-9) !important;
  border: 2px solid var(--el-color-warning-light-5) !important;

  &::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;
    background-color: rgba(255, 193, 7, 0.1);
    content: '';
  }

  &:hover::before {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 2;
    padding: 8px 12px;
    font-size: 12px;
    color: var(--el-color-warning);
    white-space: nowrap;
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid var(--el-color-warning-light-5);
    border-radius: 6px;
    content: '此行已满，不能再添加元素';
    transform: translate(-50%, -50%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 拖拽时的行状态指示
.grid-row {
  &.drag-target-valid {
    background-color: var(--el-color-success-light-9) !important;
    border-color: var(--el-color-success) !important;

    &::before {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 10;
      padding: 4px 8px;
      font-size: 11px;
      color: var(--el-color-success);
      background-color: var(--el-color-success-light-8);
      border-radius: 4px;
      content: '可以放置到此行';
    }
  }

  &.drag-target-invalid {
    background-color: var(--el-color-danger-light-9) !important;
    border-color: var(--el-color-danger) !important;

    &::before {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 10;
      padding: 4px 8px;
      font-size: 11px;
      color: var(--el-color-danger);
      background-color: var(--el-color-danger-light-8);
      border-radius: 4px;
      content: '此行已满，无法放置';
    }
  }
}

.empty-row-hint {
  display: flex;
  padding: 20px;
  margin: 8px 0;
  font-size: 14px;
  color: var(--el-text-color-placeholder);
  border: 2px dashed var(--el-border-color-lighter);
  border-radius: 6px;
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover {
    color: var(--el-color-primary);
    border-color: var(--el-color-primary-light-5);
  }
}
</style>
