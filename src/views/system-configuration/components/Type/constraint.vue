<script setup lang="ts">
import { getConstraint, updateConstraintInstance } from '@/api/systemConfiguration/type'
import { constArr } from '@/views/system-configuration/Config/help'
import type { FormInstance, FormRules } from 'element-plus'
import AddSourceDialog from '@/views/system-configuration/components/Type/AddSourceDialog.vue'
import { ApiSelect } from '@/components/ApiSelect'
import DefaultValueSetter from '@/views/system-configuration/components/Type/DefaultValueSetter.vue'
import { FormatFormDataValue } from '@/components/PlmBase/help'
import { Attribute } from '@/components/PlmBase/type'
import { constraintMap, keyMap } from '@/components/PlmBase/const'
import { getDictByCodeApi, queryCascade } from '@/api/common'

const { typeMap, componentTypeMap, componentRap } = constraintMap

// TypeScript interfaces
interface ConstraintItem {
  key: string
  label: string
  type: string
  value: any
  [key: string]: any
}

interface SourceData {
  value: string
  label: string
  type: string
}

interface SourceDialog {
  visible: boolean
}
type FormDataType = {
  constraintDefaultValue?: string | number | boolean | any[] | Record<string, any>
  constraintDefaultName?: string
  options?: Record<string, any>
  [key: string]: any
}

interface Props {
  constraintAttributeId: number
  constraintType: string
  constraintAttribute: string
  name: string
  defaultProperty: boolean // 是否是默认的属性，只能修改默认值
  isExtends: boolean
  relationId?: string
  visible?: boolean
}

const props = defineProps<Props>()

const loading = ref<boolean>(false)
const saveLoading = ref<boolean>(false)
const constraintList = ref<ConstraintItem[]>([])
const instanceId = ref<number | undefined>()
const formRef = ref<FormInstance>()
const formData = ref<FormDataType>({}) // 用于表单绑定的数据对象
const activeName = ref<string>('constraint')

// 获取约束数据
const fetachContaint = async (): Promise<void> => {
  if (!props.constraintType) {
    ElMessage.error('约束类型不正确')
    return
  }
  loading.value = true
  const [error, result] = await getConstraint({
    constraintAttributeId: props.relationId,
    constraintType: componentTypeMap?.[props.constraintType],
    constraintAttribute: props.constraintAttribute
  })
  if (!error && result?.data) {
    constraintList.value =
      (result.data?.constraintValue && JSON.parse(result.data?.constraintValue)) || []
    instanceId.value = result.data?.constraintAttributeId
    // 初始化表单数据
    initFormData(result.data)
  }
  loading.value = false
}

// 初始化表单数据
const initFormData = (content: Attribute): void => {
  const data: Record<string, any> = {}
  Object.keys(constraintList.value).forEach((item: string) => {
    data[item] = constraintList.value[item]?.value
  })
  formData.value = {
    ...data,
    constraintDefaultValue: getValueFormat(content),
    constraintDefaultName: content.constraintDefaultName,
    tableSearch: content.tableSearch,
    tableFixedType: content.tableFixedType,
    tableWidth: content.tableWidth
  }
  formateConstraintValue()
}

const getValueFormat = (content: Attribute): any => {
  // 初始化是JSON字符串的内容
  try {
    const obj = JSON.parse(content.constraintDefaultValue || '')
    if (typeof obj === 'object' && obj) {
      return obj
    }
    return content.constraintDefaultValue
  } catch (error) {
    return content.constraintDefaultValue
  }
}

// 监听属性ID变化，重新获取数据
watch(
  () => props.visible,
  async () => {
    if (props.defaultProperty) {
      activeName.value = 'defaultValue'
    }
    await fetachContaint()
  },
  {
    deep: true,
    immediate: true
  }
)
// 监听表单数据变化，同步到constraintList
watch(
  () => formData.value,
  (newVal: FormDataType) => {
    if (!newVal) return
    Object.keys(newVal).forEach((key: string) => {
      const item = Object.values(constraintList.value).find((i: ConstraintItem) => i.key === key)
      if (item) {
        item.value = newVal[key]
      }
    })
  },
  { deep: true }
)
// 保存配置
const saveConstaint = async (): Promise<void> => {
  if (!props.defaultProperty) {
    if (!formRef.value) return
  }

  try {
    // 使用表单校验
    if (!props.defaultProperty) await formRef.value?.validate()

    saveLoading.value = true
    const [error, result] = await updateConstraintInstance({
      relationId: props.relationId,
      constraintAttribute: props.constraintAttribute,
      constrain: {
        constraintAttribute: props.constraintAttribute,
        constraintAttributeId: props.constraintAttributeId,
        constraintType: componentTypeMap[props.constraintType],
        constraintValue: JSON.stringify(constraintList.value),
        constraintDefaultValue:
          typeof formData.value.constraintDefaultValue === 'string'
            ? formData.value.constraintDefaultValue
            : JSON.stringify(formData.value.constraintDefaultValue),
        constraintDefaultName: formData.value.constraintDefaultName,
        tableSearch: formData.value.tableSearch,
        tableFixedType: formData.value.tableFixedType,
        tableWidth: formData.value.tableWidth
      }
    })
    saveLoading.value = false
    if (error === null && result) {
      ElMessage.success('保存成功')
    }
  } catch (error) {
    console.error('表单校验失败', error)
    saveLoading.value = false
  }
}

// 表单校验规则
const rules = computed<FormRules>(() => {
  const formRules: FormRules = {}

  // 定义需要配对校验的字段
  const minMaxPairs: [string, string][] = [
    ['minLength', 'maxLength'], // 字符串长度范围
    ['min', 'max'], // 数值范围
    ['minRange', 'maxRange'] // 范围值
  ]

  // 为每个约束项生成校验规则
  Object.values(constraintList.value).forEach((item: ConstraintItem) => {
    const itemRules: any[] = []

    // 添加最大最小值配对校验
    for (const [minKey, maxKey] of minMaxPairs) {
      if (item.key === minKey) {
        // 最小值校验
        itemRules.push({
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            const maxValue = formData.value[maxKey]
            if (maxValue !== undefined && Number(value) > Number(maxValue)) {
              callback(new Error(`${item.label}不能大于${maxKey}`))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        })
      } else if (item.key === maxKey) {
        // 最大值校验
        itemRules.push({
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            const minValue = formData.value[minKey]
            if (minValue !== undefined && Number(value) < Number(minValue)) {
              callback(new Error(`${item.label}不能小于${minKey}`))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        })
      }
    }

    if (itemRules.length > 0) {
      formRules[item.key] = itemRules
    }
  })

  return formRules
})
// 选择来源
const sourceDialog = reactive<SourceDialog>({
  visible: false
})

const chooseSource = (): void => {
  sourceDialog.visible = true
}

//针对来源进行关联数据
const sourceRelate = (sourceData: SourceData): void => {
  sourceDialog.visible = false
  formData.value.options = sourceData
}

//单个值进行绑定
const bindItemProps = computed(() => (element: ConstraintItem): Record<string, any> => {
  // 如果是链接，必须只读才能输入
  if (element.key === keyMap.link) {
    if (!constraintList.value[keyMap.readonly]?.value) {
      formData.value[keyMap.link] = ''
      return {
        disabled: true
      }
    } else {
      return {
        disabled: false
      }
    }
  }

  // 如果是整数，不能输入小数位数
  if (element.key === keyMap.precision && props.constraintType === typeMap.INT) {
    return {
      disabled: true
    }
  }

  // 如果是数字类型，需要绑定最小值和最大值
  if (componentRap[element.type.toUpperCase()]?.name === componentRap.NUMBER.name) {
    let min = constraintList.value[keyMap.minLength] || 0
    //行数最小为1
    element.key === keyMap.rows && (min = 1)
    return {
      min: min,
      max: constraintList.value[keyMap.maxLength]
    }
  }

  return {}
})
const formateConstraintValue = () => {
  FormatFormDataValue(props.constraintType, formData, formData.value, 'constraintDefaultValue')
}
</script>
<template>
  <div class="content">
    <p class="title">属性名称 - {{ name }}</p>
    <div class="constraint" v-loading="loading">
      <ElTabs v-model="activeName">
        <ElTabPane label="约束" v-if="!defaultProperty" name="constraint">
          <ElForm
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-width="90px"
            label-position="right"
          >
            <ElFormItem
              v-for="item in constraintList"
              :key="item.key"
              :label="item.label"
              :prop="item.key"
            >
              <!--如果key是options--->
              <div v-if="item.key === 'options'">
                <ElButton @click="chooseSource" :disabled="isExtends">选择来源</ElButton>

                <span> {{ formData[item.key]?.label || formData[item.key]?.value || '' }}</span>
              </div>
              <!--              -如果是constArr里面配置的可选择的- -->

              <ApiSelect
                v-else-if="constArr[item.key]"
                v-model="formData[item.key]"
                :disabled="isExtends"
                @change="formateConstraintValue"
                v-bind="{
                  multiple: false,
                  component: ElSelect,
                  apiConfig: {
                    api: getDictByCodeApi,
                    config: {
                      label: 'label',
                      value: 'value'
                    }
                  },
                  params: {
                    dictCode: constArr[item.key]
                  },
                  clearable: true
                }"
              />
              <component
                v-else
                :disabled="isExtends"
                @change="formateConstraintValue"
                :is="componentRap[item.type.toUpperCase()]"
                v-model="formData[item.key]"
                v-bind="bindItemProps(item)"
              />
            </ElFormItem>
          </ElForm>
        </ElTabPane>
        <ElTabPane label="默认值" name="defaultValue">
          <ElForm v-if="activeName === 'defaultValue'" label-width="90px" label-position="right">
            <!-- 使用新的DefaultValueSetter组件 -->
            <DefaultValueSetter
              :constraintType="constraintType"
              :constraintList="constraintList"
              v-model:defaultValue="formData.constraintDefaultValue"
              v-model:defaultName="formData.constraintDefaultName"
              v-model:options="formData.options"
              :loading="loading"
            />
          </ElForm>
        </ElTabPane>
        <ElTabPane label="字段列表配置" name="Setting">
          <ElForm v-if="activeName === 'Setting'" label-width="90px" label-position="right">
            <ElFormItem label="配置搜索">
              <ApiSelect
                v-model="formData.tableSearch"
                v-bind="{
                  multiple: false,
                  component: ElSelect,
                  apiConfig: {
                    api: getDictByCodeApi,
                    config: {
                      label: 'label',
                      value: 'value'
                    }
                  },
                  params: {
                    dictCode: constArr.TableSearch
                  },
                  clearable: true
                }"
              />
            </ElFormItem>
            <ElFormItem label="列表字段固定类型">
              <ApiSelect
                v-model="formData.tableFixedType"
                v-bind="{
                  multiple: false,
                  component: ElSelect,
                  apiConfig: {
                    api: getDictByCodeApi,
                    config: {
                      label: 'label',
                      value: 'value'
                    }
                  },
                  params: {
                    dictCode: constArr.TableFixedType
                  },
                  clearable: true
                }"
              />
            </ElFormItem>
            <ElFormItem label="列表字段宽度">
              <ElInput v-model="formData.tableWidth" />
            </ElFormItem>
          </ElForm>
        </ElTabPane>
      </ElTabs>
    </div>
    <div class="flex justify-center">
      <ElButton class="mt-2" type="primary" @click="saveConstaint" :loading="saveLoading"
        >保存</ElButton
      >
    </div>
  </div>
  <AddSourceDialog
    :content="formData[keyMap.options]"
    @submit="sourceRelate"
    v-model="sourceDialog.visible"
  />
</template>

<style scoped lang="less">
.title {
  width: 100%;
  padding: 4px 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.constraint {
  height: calc(100% - 60px);
  padding: 10px;
  border: 1px solid #e5e7eb;

  .el-tabs {
    height: 100%;

    .el-tab-pane {
      height: 100%;
      overflow-y: auto;
    }
  }
}

.content {
  height: 100%;
}
</style>
