<script lang="ts" setup>
import { Todo } from './components/Todo'
import { Popular } from './components/Popular'
import { Notice } from './components/Notice'
import { ProductList } from './components/ProductList'
import { TaskList } from './components/TaskList'
import { VisitorTrends } from './components/Visitor'
import { getLatestSeason } from './api'

defineOptions({
  name: 'Home'
})

const defaultSeason = ref<string>()
const queryLatestSeason = async () => {
  const [error, result] = await getLatestSeason()
  if (!error && result?.datas) {
    defaultSeason.value = result.datas
  }
}
queryLatestSeason()
</script>

<template>
  <ElRow :gutter="10">
    <ElCol :lg="8" :md="24" class="xl:mt-0 mt-2">
      <Todo />
    </ElCol>
    <ElCol :lg="8" :md="24" class="xl:mt-0 mt-2">
      <Popular />
    </ElCol>
    <ElCol :lg="8" :md="24" class="xl:mt-0 mt-2">
      <Notice />
    </ElCol>
    <ElCol :span="24" class="mt-2">
      <ElCard body-class="!h-auto">
        <template #header>
          <div class="flex items-center">
            <Icon color="#409efc" icon="ep:histogram" />
            <span class="ml-1 font-bold">数据统计</span>
          </div>
        </template>
        <ProductList :default-season="defaultSeason" />
        <TaskList :default-season="defaultSeason" />
      </ElCard>
    </ElCol>
    <ElCol :span="24" class="mt-2">
      <VisitorTrends />
    </ElCol>
  </ElRow>
</template>
<style lang="less" scoped>
:deep(.el-table .el-table__body tr:hover > td) {
  background-color: transparent !important;
}

:deep(.el-card__body) {
  height: 30vh;
  min-height: 232px;
}

:deep(.el-card__header),
:deep(.el-card__footer) {
  padding-top: 10px;
  padding-bottom: 10px;
}

:deep(.el-card__footer) {
  height: 44px;
  text-align: right;
}
</style>
