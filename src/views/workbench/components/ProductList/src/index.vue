<script lang="ts" setup>
import * as echarts from 'echarts'
import { getProductList, ProductListAPI } from '@/views/workbench/api'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { Echart } from '@/components/Echart'

defineOptions({
  name: 'ProductList'
})

const props = defineProps<{
  defaultSeason?: string
}>()

const productSeason = ref<string[]>([])

const processData = computed<{
  xAxis: string[]
  series: echarts.BarSeriesOption[]
}>(() => {
  const data = productList.value
  const groupedData = data.reduce((acc, item) => {
    const { brand, launchSeason, quantity } = item
    if (!brand || !launchSeason) {
      return {}
    }
    if (!acc[brand]) {
      acc[brand] = {}
    }
    if (!acc[brand][launchSeason]) {
      acc[brand][launchSeason] = 0
    }
    acc[brand][launchSeason] += quantity || 0
    return acc
  }, {} as Record<string, Record<string, number>>)
  const launchSeasons = Array.from(new Set(data.map((item) => item.launchSeason)))

  const xAxis = Object.keys(groupedData)
  const series = launchSeasons.map(
    (launchSeason) =>
      ({
        name: launchSeason!,
        type: 'bar',
        barMaxWidth: 30,
        label: {
          show: launchSeasons.length <= 3,
          position: 'top'
        },
        data: xAxis.map((brand) => groupedData[brand][launchSeason!] || 0)
      } as echarts.BarSeriesOption)
  )

  return { xAxis, series }
})

const echartRef = ref<InstanceType<typeof Echart>>()
const queryLoading = ref(false)
const productList = ref<ProductListAPI.List>([])

const fetchProductList = async () => {
  if (!productSeason.value.length) {
    productList.value = []
    return
  }
  echartRef.value?.echartRef?.showLoading()
  queryLoading.value = true
  const [error, result] = await getProductList({
    launchSeasonList: productSeason.value
  })
  queryLoading.value = false
  echartRef.value?.echartRef?.hideLoading()
  if (!error && result?.datas) {
    productList.value = result.datas
  } else {
    productList.value = []
  }
}
onActivated(fetchProductList)

const productInfoOptions = computed<echarts.EChartsOption>(() => ({
  color: ['#53C8EB', '#5390EB', '#53EBAD', '#FFBF5E', '#E086F1', '#E01F54', '#24A7FF'],
  title: {
    text: '产品清单数据汇总',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: processData.value.xAxis,
      axisLabel: {
        interval: 'auto'
      },
      axisTick: {
        alignWithLabel: true
      }
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  legend: {
    // 图例的位置
    orient: 'horizontal',
    left: 'right',
    top: 25,
    type: 'scroll',
    data: productSeason.value
  },
  series: processData.value.series
}))

watch(
  () => props.defaultSeason,
  (val) => {
    if (val) {
      productSeason.value = [val]
      fetchProductList()
    }
  }
)
const { handleExport: exportFn, loading } = useOmsExport()

const handleExport = () => {
  const reqParam = JSON.stringify({ launchSeasonList: productSeason.value })
  exportFn({
    exportType: 'product-list-summary-export',
    reqParam
  })
}
</script>

<template>
  <div class="flex items-center justify-start gap-2">
    <SelectPlus
      v-model="productSeason"
      :clearable="false"
      api-key="COMMON_MARKET_SEASON"
      class="!w-48"
      collapse-tags
      collapse-tags-tooltip
      multiple
      placeholder="开发季节"
      @change="fetchProductList"
    />
    <ElButton :loading="loading" link type="primary" @click="handleExport" class="mb-2">
      导出Excel
    </ElButton>
  </div>
  <Echart ref="echartRef" :height="350" :options="productInfoOptions" />
</template>

<style lang="less" scoped></style>
