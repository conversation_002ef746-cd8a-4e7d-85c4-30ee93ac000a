<script lang="ts" setup>
import * as echarts from 'echarts'
import { TableColumnCtx } from 'element-plus'
import { getTaskList, TaskListAPI } from '@/views/workbench/api'
import { useBasicLibraryDictStore } from '@/views/basic-library-manage/store/dict'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { Echart } from '@/components/Echart'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'TaskList'
})

const props = defineProps<{
  defaultSeason?: string
}>()
const { push } = useRouter()
const taskSeason = ref<string>('')

const taskList = ref<TaskListAPI.List>([])
const processData = computed<{
  xAxis: string[]
  brands: string[]
  series: echarts.LineSeriesOption[]
}>(() => {
  if (!Object.keys(brandMap.value)) {
    return {
      xAxis: [],
      brands: [],
      series: []
    }
  }
  const data = taskList.value
  const xAxis = Array.from(
    new Set(data.flatMap((item) => item.summary?.map((summary) => summary.taskNode!) || []))
  )
  const brands = data
    .map((item) => item.brand!)
    .map((e) => (brandMap.value[e] as string) || e.toString())
  const series = brands.map(
    (brand) =>
      ({
        name: brand!,
        type: 'line',
        data: xAxis.map((taskNode) => {
          const brandData = data.find((item) => brandMap.value[item.brand!] === brand)
          const taskSummary = brandData?.summary?.find((summary) => summary.taskNode === taskNode)
          return taskSummary ? taskSummary.undoCount || 0 : 0
        })
      } as echarts.LineSeriesOption)
  )
  return {
    xAxis,
    brands,
    series
  }
})

const echartRef = ref<InstanceType<typeof Echart>>()
const queryLoading = ref(false)
const fetchTaskList = async () => {
  if (!taskSeason.value) {
    taskList.value = []
    return
  }
  echartRef.value?.echartRef?.showLoading()
  queryLoading.value = true
  const [error, result] = await getTaskList({
    launchSeason: taskSeason.value
  })
  echartRef.value?.echartRef?.hideLoading()
  queryLoading.value = false
  if (!error && result?.datas) {
    taskList.value = result.datas
  } else {
    taskList.value = []
  }
}
onActivated(fetchTaskList)

watch(
  () => props.defaultSeason,
  (val) => {
    if (val) {
      taskSeason.value = val
      fetchTaskList()
    }
  }
)

const brandMap = computed(() => useBasicLibraryDictStore().brandMap)

const taskInfoOptions = computed<echarts.EChartsOption>(() => ({
  title: {
    text: `【${taskSeason.value}】各品牌各阶段任务未完成情况数据统计`,
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: processData.value.xAxis,
      axisLabel: {
        interval: 'auto'
      },
      axisTick: {
        alignWithLabel: true
      }
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  legend: {
    orient: 'horizontal',
    left: 'right',
    top: 25,
    type: 'scroll',
    data: processData.value.brands
  },
  series: processData.value.series
}))

interface DataItem {
  [key: string]: {
    value: number | string
    link?: boolean
    productIdList?: string[]
  }
}
type TableConfig = {
  brand: number
  brandName: string
  header: Partial<TableColumnCtx<TaskListAPI.Row>>[]
  data: DataItem[]
}

const tableConfig = computed<TableConfig[]>(() => {
  return taskList.value.map((brandEntry) => {
    const brandName = brandEntry.brand!
    const taskNodes = brandEntry.summary?.map((item) => item.taskNode!) || []

    const doneData: DataItem = {}
    const undoneData: DataItem = {}
    const undonePercentData: DataItem = {}

    brandEntry.summary?.forEach((item) => {
      const { taskNode, doneProductIdList, undoProductIdList } = item
      const _doneProductIdList = doneProductIdList || []
      const _undoProductIdList = undoProductIdList || []

      doneData[taskNode!] = {
        link: Boolean(_doneProductIdList.length),
        value: item.doneCount || 0,
        productIdList: _doneProductIdList
      }
      undoneData[taskNode!] = {
        link: Boolean(_undoProductIdList.length),
        value: item.undoCount || 0,
        productIdList: _undoProductIdList
      }
      undonePercentData[taskNode!] = {
        value: item.undoPercent || '0%'
      }
    })

    return {
      brand: brandEntry.brand!,
      brandName: brandMap.value[brandName] as string,
      header: [{ prop: 'stage', label: '阶段' }, ...taskNodes.map((e) => ({ prop: e, label: e }))],
      data: [
        {
          stage: { value: '已完成' },
          ...doneData
        },
        {
          stage: { value: '未完成' },
          ...undoneData
        },
        {
          stage: { value: '未完成占比' },
          ...undonePercentData
        }
      ]
    }
  })
})

const { handleExport: exportFn, loading } = useOmsExport()

const handleExport = () => {
  const reqParam = JSON.stringify({ launchSeason: taskSeason.value })
  exportFn({
    exportType: 'product-undo-summary-export',
    reqParam
  })
}

const handleSearchByIdList = (row: DataItem[string]) => {
  const idList = row.productIdList
  push({ path: '/product-library/list', query: { idList: idList?.join() } })
}
</script>

<template>
  <div class="flex items-center justify-start gap-2">
    <SelectPlus
      v-model="taskSeason"
      :clearable="false"
      api-key="COMMON_MARKET_SEASON"
      class="!w-48"
      placeholder="开发季节"
      @change="fetchTaskList"
    />
    <ElButton :loading="loading" link type="primary" @click="handleExport" class="mb-2">
      导出Excel
    </ElButton>
  </div>
  <Echart ref="echartRef" :height="350" :options="taskInfoOptions" />
  <ElTable
    v-for="item in tableConfig"
    :key="item.brand"
    v-loading="queryLoading"
    :data="item.data"
    :span-method="
      ({ columnIndex, rowIndex }) => {
        if (columnIndex === 0) {
          if (rowIndex === 0) {
            return { rowspan: 3, colspan: 1 }
          } else {
            return { rowspan: 0, colspan: 0 }
          }
        }
      }
    "
    border
    class-name="my-2 w-full"
  >
    <ElTableColumn align="center" label="品牌" width="160">
      <template #default>
        <span>{{ item.brandName }}</span>
      </template>
    </ElTableColumn>
    <ElTableColumn
      v-for="col in item.header"
      :key="col.prop"
      :label="col.label"
      :prop="col.prop"
      align="center"
      min-width="110"
    >
      <template #default="{ row }: { row: DataItem }">
        <ElButton
          v-if="row[col.prop as string].link"
          link
          type="primary"
          @click="handleSearchByIdList(row[col.prop as string])"
        >
          {{ row[col.prop as string].value }}
        </ElButton>
        <span v-else>{{ row[col.prop as string].value }}</span>
      </template>
    </ElTableColumn>
  </ElTable>
</template>

<style lang="less" scoped></style>
