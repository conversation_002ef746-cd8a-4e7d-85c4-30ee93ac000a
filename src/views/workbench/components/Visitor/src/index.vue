<script lang="ts" setup>
import { Echart } from '@/components/Echart'
import { getVisitorTrends, VisitorTrendsAPI } from '@/views/workbench/api'
import * as echarts from 'echarts'

defineOptions({
  name: 'Visitor'
})

const echartRef = ref<InstanceType<typeof Echart>>()
const queryLoading = ref(false)
const data = ref<VisitorTrendsAPI.Data>({})
const fetchVisitorTrends = async () => {
  echartRef.value?.echartRef?.showLoading()
  queryLoading.value = true
  const [error, result] = await getVisitorTrends()
  echartRef.value?.echartRef?.hideLoading()
  queryLoading.value = false
  if (!error && result?.datas) {
    data.value = JSON.parse(result.datas)
  } else {
    data.value = {}
  }
}

const xAxis = computed(() => Object.keys(data.value))
const yAxis = computed(() => Object.values(data.value))

onActivated(fetchVisitorTrends)
onMounted(fetchVisitorTrends)

const visitorOptions = computed<echarts.EChartsOption>(() => ({
  color: ['#eebe77'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    top: '10%',
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: xAxis.value,
    axisLabel: {
      interval: 0
    },
    axisTick: {
      alignWithLabel: true
    }
  },
  yAxis: {
    type: 'value',
    splitNumber: 4
  },
  series: [
    {
      data: yAxis.value,
      type: 'line',
      label: {
        show: true,
        position: 'top'
      }
    }
  ]
}))
</script>

<template>
  <ElCard body-class="!h-auto">
    <template #header>
      <div class="flex items-center">
        <Icon color="#409efc" icon="charm:chart-line" />
        <span class="ml-1 font-bold">访客趋势</span>
      </div>
    </template>
    <Echart ref="echartRef" :height="220" :options="visitorOptions" />
  </ElCard>
</template>

<style lang="less" scoped></style>
