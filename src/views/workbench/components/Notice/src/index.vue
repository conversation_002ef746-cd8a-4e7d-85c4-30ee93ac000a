<script lang="ts" setup>
import { ToDoListAPI } from '@/views/workbench/api'
import { getNotificationList, NotificationListAPI } from '@/views/notification/api'
import { NotificationStatusEnums } from '@/views/notification/const'

defineOptions({
  name: 'Notice'
})

const router = useRouter()
const useQueryNoticeList = () => {
  const noticeList = ref<ToDoListAPI.List>([])
  const noticeLoading = ref(false)
  const fetchNoticeList = async () => {
    noticeLoading.value = true
    const [error, result] = await getNotificationList({
      status: [NotificationStatusEnums.PUBLISHED],
      current: 1,
      size: 10
    })
    noticeLoading.value = false
    if (!error && result?.datas) {
      noticeList.value = result.datas.records || []
    }
  }
  fetchNoticeList()
  onActivated(fetchNoticeList)
  const handleViewNotification = (row: NotificationListAPI.Row) => {
    router.push({ name: 'ViewNotification', query: { id: row.id } })
  }
  const handleViewMore = () => {
    router.push({ name: 'NotificationList' })
  }

  return {
    noticeList,
    noticeLoading,
    handleViewMore,
    handleViewNotification
  }
}

const { noticeList, noticeLoading, handleViewMore, handleViewNotification } = useQueryNoticeList()
</script>

<template>
  <ElCard v-loading="noticeLoading" body-class="!p-0">
    <template #header>
      <div class="flex items-center">
        <Icon color="#409efc" icon="ep:calendar" />
        <span class="ml-1 font-bold">通知</span>
      </div>
    </template>
    <ElTable
      :data="noticeList"
      :max-height="190"
      :row-class-name="
        ({ rowIndex }) =>
          rowIndex % 2 === 0
            ? '!bg-blue-50 text-sky-500 font-bold cursor-pointer'
            : 'text-sky-500 font-bold cursor-pointer'
      "
      :show-header="false"
      class="w-full"
      size="small"
      @row-click="handleViewNotification"
    >
      <ElTableColumn align="center" label="Date" prop="date" width="50">
        <template #default>
          <div class="flex items-center justify-center ml-2.5 h-full">
            <Icon color="#409efc" icon="ep:opportunity" />
          </div>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="title" show-overflow-tooltip />
      <ElTableColumn prop="publishTime" show-overflow-tooltip width="150" />
    </ElTable>
    <template #footer>
      <ElButton
        class="!justify-end !font-bold"
        link
        size="small"
        type="primary"
        @click="handleViewMore"
      >
        查看更多
      </ElButton>
    </template>
  </ElCard>
</template>

<style lang="less" scoped></style>
