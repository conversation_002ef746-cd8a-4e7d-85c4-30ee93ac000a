<script lang="ts" setup>
import { getToDoList, routerMap, RouterTypeEnum, ToDoListAPI } from '@/views/workbench/api'
import { storeToRefs } from 'pinia'
import { useUserInfoStore } from '@/store/modules/userInfo'
import {
  getSampleList,
  SampleListPageAPI
} from '@/views/basic-library-manage/sample-manage/api/SampleList'
import { ref } from 'vue'
import { Pager } from '@/views/basic-library-manage/api/common'
import { StatusEnum } from '@/views/basic-library-manage/const'
import { SampleMap, sampleStatusMap } from '@/views/basic-library-manage/sample-manage/const'
import { ElPagination } from 'element-plus'
import type { TabsPaneContext } from 'element-plus'

defineOptions({
  name: 'Todo'
})

const router = useRouter()
const activeTab = ref<string>('process')
const useQueryTodoList = () => {
  const todoList = ref<ToDoListAPI.List>([])
  const { userInfo } = storeToRefs(useUserInfoStore())
  const todoLoading = ref(false)
  const fetchToDoList = async () => {
    todoLoading.value = true
    const [error, result] = await getToDoList({
      userId: userInfo.value.id
    })
    todoLoading.value = false
    if (!error && result?.datas) {
      todoList.value = result.datas
    }
  }
  onActivated(() => {
    fetchToDoList()
    fetchSampleList()
  })

  watch(
    () => userInfo.value.id,
    (val) => {
      if (val) {
        fetchToDoList()
      }
    }
  )

  const handleViewProduct = (row: ToDoListAPI.Row) => {
    if (row.type) {
      let query
      if (row.type === RouterTypeEnum.PRODUCT_LIST) {
        query = {
          productNumber: row.businessCode?.join(','),
          brandId: row.brandId,
          launchSeason: row.launchSeason,
          taskNode: row.taskNode,
          memberType: row.pendingMemberType
        }
      }
      if (row.type === RouterTypeEnum.PROOF) {
        query = {
          productNumber: row.businessCode?.join(',')
        }
      }
      if (row.type === RouterTypeEnum.INFRINGEMENT_SCREENING) {
        query = {
          infringementCode: row.businessCode?.join(',')
        }
      }
      router.push({ name: routerMap[row.type], query })
    }
  }

  return {
    todoList,
    todoLoading,
    handleViewProduct
  }
}
const pager = ref<Pager>({
  current: 1,
  size: 6,
  total: 0
})
const sampleList = ref<SampleListPageAPI.Row[]>()
const sampleLoading = ref(false)
const fetchSampleList = async () => {
  sampleLoading.value = true
  const [error, result] = await getSampleList({
    mine: true,
    status: [StatusEnum.DRAFT, StatusEnum.AGAIN, StatusEnum.DESIGN_REVIEW],
    ...pager.value
  })
  sampleLoading.value = false
  if (!error && result?.datas) {
    const { records } = result.datas
    sampleList.value = records
    pager.value.total = result.datas.pager.total
  }
}
const handleDetail = (row: SampleListPageAPI.Row) => {
  router.push({ name: 'SampleManageList', query: { code: row.code } })
}
const changTab = (tab: TabsPaneContext) => {
  if (tab.paneName == 'process') {
    pager.value.current = 1
    fetchSampleList()
  }
}
const { todoList, todoLoading, handleViewProduct } = useQueryTodoList()
onBeforeMount(() => {
  fetchSampleList()
})
</script>

<template>
  <ElCard v-loading="todoLoading" :class="activeTab == 'process' ? '' : 'no-footer-card'">
    <template #header>
      <div class="flex items-center">
        <Icon color="#409efc" icon="ep:calendar" />
        <span class="ml-1 font-bold">我的待办</span>
      </div>
    </template>
    <el-tabs v-model="activeTab" @tab-click="changTab" class="process-tabs">
      <el-tab-pane name="process">
        <template #label>
          流程<span v-if="pager.total > 0" style="color: red">（{{ pager.total }}）</span>
        </template>
        <VxeTable
          row-class-name="rowClass"
          header-row-class-name="headerRowClass"
          ref="tableRef"
          max-height="100%"
          class="w-full"
          size="small"
          :data="sampleList"
          :loading="sampleLoading"
        >
          <VxeColumn title="序号" type="seq" width="40" />
          <VxeColumn field="code" min-width="60" show-overflow title="编号" />
          <VxeColumn field="type" min-width="60" title="类型">
            <template #default="{ row }">
              {{ SampleMap[row.type] }}
            </template>
          </VxeColumn>
          <VxeColumn field="proofingSheet" title="打样单" min-width="40">
            <template #default="{ row }">
              <OssUpload
                :model-value="row.proofingSheet ? [row.proofingSheet] : []"
                disabled
                list-type="text"
              />
            </template>
          </VxeColumn>
          <VxeColumn title="状态" type="seq" min-width="60">
            <template #default="{ row }">
              {{ sampleStatusMap[row.status] }}
            </template>
          </VxeColumn>
          <VxeColumn width="60" title="操作" fixed="right">
            <template #default="{ row }">
              <ElButton size="small" text type="primary" @click="handleDetail(row)">
                详情
              </ElButton>
            </template>
          </VxeColumn>
        </VxeTable>
      </el-tab-pane>
      <el-tab-pane name="statistic">
        <template #label>
          统计<span v-if="todoList.length > 0" style="color: red">（{{ todoList.length }}）</span>
        </template>
        <ElTable
          :data="todoList"
          :max-height="260"
          :row-class-name="
            ({ rowIndex }) =>
              rowIndex % 2 === 0
                ? '!bg-blue-50 text-sky-500 font-bold cursor-pointer'
                : 'text-sky-500 font-bold cursor-pointer'
          "
          :show-header="false"
          class="w-full"
          size="small"
          @row-click="handleViewProduct"
        >
          <ElTableColumn align="center" width="50">
            <template #default>
              <div class="flex items-center justify-center ml-2.5 h-full">
                <Icon color="#409efc" icon="ep:opportunity" />
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="pending" show-overflow-tooltip />
        </ElTable>
      </el-tab-pane>
    </el-tabs>
    <template v-if="activeTab == 'process'" #footer>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="prev, next"
        @size-change="fetchSampleList"
        @current-change="fetchSampleList"
      />
    </template>
  </ElCard>
</template>

<style lang="less" scoped>
.el-card {
  position: relative;
}

:deep(.el-tabs) {
  height: 100%;

  .el-tab-pane {
    height: 100%;
  }

  .el-tabs__header {
    position: absolute;
    top: 13px;
    width: 100%;
    margin-top: -7px;
    border: none;

    .el-tabs__nav-scroll {
      margin-left: 46%;
    }
  }
}

.el-pagination {
  float: right;
  margin-top: 0;

  :deep(button) {
    width: var(--el-pagination-button-height-small);
    height: var(--el-pagination-button-height-small);
    min-width: auto;
    padding: 0 6px;
    margin: 0 5px;
    line-height: var(--el-pagination-button-height-small);
    color: #fff !important;
    text-align: center;
    background: var(--el-color-primary) !important;
    border-radius: 50%;
  }
}

:deep(.process-tabs) {
  .vxe-table--header-inner-wrapper {
    height: 30px !important;
  }

  th {
    .vxe-cell {
      height: 30px !important;
    }
  }

  .vxe-cell {
    padding: 0 2px !important;
  }
}

:deep(.el-card__body) {
  padding: 0;
}

.no-footer-card {
  > :deep(.el-card__body) {
    height: calc(30vh + 44px);
    min-height: 276px;
    padding: 0;
  }
}
</style>
