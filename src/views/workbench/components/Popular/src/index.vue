<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useLocalStorage } from '@vueuse/core'
import { allFuncList, FuncItem } from './const'
import { findRouteByName } from '@/utils/tree'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { storeToRefs } from 'pinia'
import { PageTypeEnum } from '@/utils/enum'

defineOptions({
  name: 'Popular'
})

const router = useRouter()

const { getRouters } = storeToRefs(usePermissionStoreWithOut())

// Filter and store the function list
const filterFuncList = (routers: AppCustomRouteRecordRaw[]) =>
  allFuncList.filter((e) => !e.route || findRouteByName(routers, e.route))

// Use useLocalStorage for persisting allFunctionList
const allFunctionList = useLocalStorage<FuncItem[]>(
  'allFuncList',
  filterFuncList(getRouters.value as AppCustomRouteRecordRaw[]),
  {
    serializer: {
      read: (v: string) => JSON.parse(v),
      write: (v: FuncItem[]) => JSON.stringify(v)
    }
  }
)

const handleUpdateRoute = () => {
  // Update routes if necessary
  const filteredList = filterFuncList(getRouters.value as AppCustomRouteRecordRaw[])
  if (!filteredList.length) {
    return
  }
  allFunctionList.value = filteredList.map((e) => ({
    ...e,
    checked:
      allFunctionList.value.find((item) => item.route === e.route || !item.route)?.checked ?? false
  }))
  if (allFunctionList.value.every((e) => !e.checked)) {
    allFunctionList.value = allFunctionList.value.map((e) => ({
      ...e,
      checked: e.route ? e.checked : true
    }))
  }
}

watch(() => getRouters.value, handleUpdateRoute, {
  immediate: true,
  deep: true
})

const cloneAllFunctionList = ref(JSON.parse(JSON.stringify(allFunctionList.value)))
const checkedList = computed(() => allFunctionList.value.filter((e) => e.checked))

const page = ref(1)
const pageSize = 6
const sortedPopularList = computed(() => {
  const sorted = [...checkedList.value].sort((a, b) => {
    if (a.index !== undefined && b.index !== undefined) return a.index - b.index
    if (a.index !== undefined) return -1
    if (b.index !== undefined) return 1
    return 0
  })

  // 创建一个足够大的数组来容纳所有项目
  const totalSlots = Math.max(
    sorted.length,
    Math.max(...sorted.filter((item) => item.index !== undefined).map((item) => item.index!)) + 1
  )
  const result: (FuncItem | null)[] = new Array(totalSlots).fill(null)

  // 填充固定位置的项目
  sorted.forEach((item) => {
    if (item.index !== undefined) {
      result[item.index] = item
    }
  })

  // 填充剩余项目
  let filledIndex = 0
  sorted.forEach((item) => {
    if (item.index === undefined) {
      while (filledIndex < totalSlots && result[filledIndex] !== null) {
        filledIndex++
      }
      if (filledIndex < totalSlots) {
        result[filledIndex] = item
        filledIndex++
      }
    }
  })

  return result.filter((item) => item !== null) as FuncItem[]
})

const popularList = computed(() => {
  const start = pageSize * (page.value - 1)
  const end = start + pageSize
  return sortedPopularList.value.slice(start, end)
})

const allFuncDialogVisible = ref(false)

const handleClick = (item: FuncItem) => {
  if (!item.route) {
    allFuncDialogVisible.value = true
  } else {
    router.push({ name: item.route, params: { pageType: PageTypeEnum.ADD } })
  }
}

const handleChangeFunc = () => {
  allFunctionList.value = cloneAllFunctionList.value
  allFuncDialogVisible.value = false
}
</script>

<template>
  <ElCard>
    <template #header>
      <div class="flex items-center">
        <Icon color="#409efc" icon="ep:guide" />
        <span class="ml-1 font-bold">常用功能</span>
      </div>
    </template>
    <ElRow class="h-full">
      <ElCol v-for="item in popularList" :key="item.name" :span="8" class="h-1/2">
        <div
          class="flex flex-col items-center justify-center cursor-pointer h-full"
          @click="item && handleClick(item)"
        >
          <ElAvatar :size="50" :src="item.src" class="!bg-transparent mt-1" fit="fill" />
          <TooltipClamp
            :max-lines="1"
            :text-content="item.name"
            autoresize
            class="mt-2 w-full text-center"
            style="
              font-size: var(--el-font-size-base);
              font-weight: 700;
              color: var(--el-text-color-regular);
            "
          >
            {{ item.name }}
          </TooltipClamp>
        </div>
      </ElCol>
    </ElRow>
    <Dialog v-model="allFuncDialogVisible" title="自定义快捷操作入口" top="5vh" width="700px">
      <VxeTable
        :checkbox-config="{
          checkField: 'checked',
          checkMethod: ({ row }) => !!row.route
        }"
        :data="cloneAllFunctionList"
        :max-height="600"
        :show-header-overflow="false"
        :show-overflow="false"
        align="center"
      >
        <VxeColumn type="checkbox" width="40" />
        <VxeColumn field="name" title="操作菜单" />
      </VxeTable>
      <template #footer>
        <ElButton type="primary" @click="handleChangeFunc">确定</ElButton>
        <ElButton
          @click="
            () => {
              allFuncDialogVisible = false
              cloneAllFunctionList = allFunctionList
            }
          "
        >
          取消
        </ElButton>
      </template>
    </Dialog>
    <template #footer>
      <ElPagination
        v-model:current-page="page"
        :hide-on-single-page="false"
        :page-size="pageSize"
        :total="sortedPopularList.length"
        class="justify-end"
        layout="prev, next"
        size="small"
      />
    </template>
  </ElCard>
</template>

<style lang="less" scoped>
:deep(.el-dialog__body) {
  max-height: calc(100vh - 10vh - 60px - 62px);
  overflow: auto;
}

:deep(.el-pagination > button) {
  margin: 0 5px;
  color: #fff;
  background-color: var(--el-color-primary);
  border-radius: 50%;
}
</style>
