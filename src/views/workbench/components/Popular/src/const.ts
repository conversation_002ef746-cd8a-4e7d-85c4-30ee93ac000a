export enum RouteEnums {
  /**
   * 产品开发进度
   */
  PDP = 'ProductDevProgress',
  /**
   * 侵权排查
   */
  II = 'InfringementInvestigation',
  /**
   * 新增产品
   */
  CP = 'CreateProduct',
  /**
   * 快速建档
   */
  QCP = 'QuickCreateProduct',
  /**
   * 产品清单
   */
  PL = 'ProductLibrary',
  /**
   * 选款公告
   */
  SR = 'SourceRequirementPage',
  /**
   * 新增公告
   */
  CSR = 'SourceRequirementAdd',
  /**
   * 推款产品管理
   */
  PLP = 'ProductListPage',
  /**
   * 颜色库
   */
  CL = 'ColorLibrary',
  /**
   * 创建颜色
   */
  CC = 'CreateColor',
  /**
   * 尺码库
   */
  SL = 'SizeLibrary',
  /**
   * 创建尺码
   */
  CS = 'CreateSize',
  /**
   * 材料分类
   */
  MC = 'MaterialCategory',
  /**
   * 材料清单管理
   */
  ML = 'MaterialLibrary',
  /**
   * 新增材料
   */
  CM = 'CreateMaterial',
  /**
   * 楦型库
   */
  LL = 'LastLibrary',
  /**
   * 新增楦型
   */
  CLA = 'CreateLast',
  /**
   * 跟底库
   */
  HL = 'HeelLibrary',
  /**
   * 新增跟底
   */
  CH = 'CreateHeel',
  /**
   * 模具清单
   */
  MDL = 'MoldLibrary',
  /**
   * 模具开制申请
   */
  CMP = 'CreateMoldProcess',
  /**
   * 型体库
   */
  MEL = 'ModelLibrary',
  /**
   * 新增型体
   */
  CME = 'CreateModel',
  /**
   * 品牌企划案管理
   */
  BPC = 'BrandPlanCollection',
  /**
   * 产品分类管理
   */
  PC = 'ProductCategory',
  /**
   * SKC清单管理
   */
  PSI = 'ProductSkcInfo',
  /**
   * 侵权初筛
   */
  IS = 'InitialScreening'
}

export interface FuncItem {
  src: string
  name: string
  route: string
  checked: boolean
  index?: number
}

export const allFuncList: FuncItem[] = [
  {
    src: '/assets/images/pdp.svg',
    name: '产品开发进度',
    route: RouteEnums.PDP,
    checked: true,
    index: 0
  },
  {
    src: '/assets/images/ii.svg',
    name: '侵权排查',
    route: RouteEnums.II,
    checked: true
  },
  {
    src: '/assets/images/cp.svg',
    name: '新增产品',
    route: RouteEnums.CP,
    checked: true
  },
  {
    src: '/assets/images/qcp.svg',
    name: '快速建档',
    route: RouteEnums.QCP,
    checked: true
  },
  {
    src: '/assets/images/pl.svg',
    name: '产品清单',
    route: RouteEnums.PL,
    checked: true
  },
  {
    src: '/assets/images/add.svg',
    name: '添加',
    route: '',
    checked: true,
    index: 5
  },
  {
    src: '/assets/images/sr.svg',
    name: '选款公告管理',
    route: RouteEnums.SR,
    checked: false
  },
  {
    src: '/assets/images/csr.svg',
    name: '新增公告',
    route: RouteEnums.CSR,
    checked: false
  },
  {
    src: '/assets/images/plp.svg',
    name: '推款产品管理',
    route: RouteEnums.PLP,
    checked: false
  },
  {
    src: '/assets/images/cl.svg',
    name: '颜色库',
    route: RouteEnums.CL,
    checked: false
  },
  {
    src: '/assets/images/cc.svg',
    name: '新增颜色',
    route: RouteEnums.CC,
    checked: false
  },
  {
    src: '/assets/images/sl.svg',
    name: '尺码库',
    route: RouteEnums.SL,
    checked: false
  },
  {
    src: '/assets/images/cs.svg',
    name: '新增尺码',
    route: RouteEnums.CS,
    checked: false
  },
  {
    src: '/assets/images/mc.svg',
    name: '材料分类管理',
    route: RouteEnums.MC,
    checked: false
  },
  {
    src: '/assets/images/ml.svg',
    name: '材料清单列表',
    route: RouteEnums.ML,
    checked: false
  },
  {
    src: '/assets/images/cm.svg',
    name: '新增材料',
    route: RouteEnums.CM,
    checked: false
  },
  {
    src: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    name: '楦型库',
    route: RouteEnums.LL,
    checked: false
  },
  {
    src: '/assets/images/cla.svg',
    name: '新增楦型',
    route: RouteEnums.CLA,
    checked: false
  },
  {
    src: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    name: '跟底库',
    route: RouteEnums.HL,
    checked: false
  },
  {
    src: '/assets/images/ch.svg',
    name: '新增跟底',
    route: RouteEnums.CH,
    checked: false
  },
  {
    src: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    name: '模具清单',
    route: RouteEnums.MDL,
    checked: false
  },
  {
    src: '/assets/images/cmp.svg',
    name: '模具开制申请',
    route: RouteEnums.CMP,
    checked: false
  },
  {
    src: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    name: '型体库',
    route: RouteEnums.MEL,
    checked: false
  },
  {
    src: '/assets/images/cme.svg',
    name: '新增型体',
    route: RouteEnums.CME,
    checked: false
  },
  {
    src: '/assets/images/bpc.svg',
    name: '品牌企划案管理',
    route: RouteEnums.BPC,
    checked: false
  },
  {
    src: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    name: '产品分类管理',
    route: RouteEnums.PC,
    checked: false
  },
  {
    src: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    name: 'SKC清单管理',
    route: RouteEnums.PSI,
    checked: false
  },
  {
    src: '/assets/images/is.svg',
    name: '侵权初筛',
    route: RouteEnums.IS,
    checked: false
  }
]
