import to from 'await-to-js'
import request from '@/config/fetch'

export enum RouterTypeEnum {
  PRODUCT_LIST = 'product',
  PROOF = 'proof',
  INFRINGEMENT_SCREENING = 'infringement_screening'
}

export enum RouterEnum {
  PRODUCT_LIST = 'ProductLibrary',
  PROOF = 'SampleList',
  INFRINGEMENT_SCREENING = 'InfringementInvestigation'
}

export const routerMap = {
  [RouterTypeEnum.PRODUCT_LIST]: RouterEnum.PRODUCT_LIST,
  [RouterTypeEnum.PROOF]: RouterEnum.PROOF,
  [RouterTypeEnum.INFRINGEMENT_SCREENING]: RouterEnum.INFRINGEMENT_SCREENING
}

export namespace ToDoListAPI {
  export interface Row {
    /**
     * 跳转页面
     */
    type?: RouterTypeEnum
    /**
     * 跳转页面携带参数
     */
    businessCode?: string[]
    /**
     * 当前节点人物类型
     */
    pendingMemberType?: string
    /**
     * 任务节点
     */
    taskNode?: string
    /**
     * brand
     */
    brandId?: number
    /**
     * 数量
     */
    count?: number
    /**
     * id
     */
    id?: number
    /**
     * 开款季
     */
    launchSeason?: string
    /**
     * 待办
     */
    pending?: string
    /**
     * 待办类型
     */
    pendingType?: string
    [property: string]: any
  }
  export type List = Row[]
  export type Response = ResponseData<List>
  export interface Request {
    userId?: number
  }
}
export function getToDoList(params: ToDoListAPI.Request) {
  return to<ToDoListAPI.Response>(
    request.get({
      url: '/pdm-base/workBench/pending/list',
      params
    })
  )
}

export namespace ProductListAPI {
  export interface Request {
    /**
     * 开款季
     */
    launchSeasonList?: string[]
    [property: string]: any
  }
  export interface Row {
    /**
     * 品牌
     */
    brand?: string
    /**
     * 上市季节
     */
    launchSeason?: string
    /**
     * 产品数量
     */
    quantity?: number
    [property: string]: any
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}
export function getProductList(data: ProductListAPI.Request) {
  return to<ProductListAPI.Response>(
    request.post({
      url: '/pdm-base/workBench/productListSummary',
      data
    })
  )
}

export namespace TaskListAPI {
  export interface Request {
    /**
     * 开款季
     */
    launchSeason?: string
    [property: string]: any
  }
  export interface ProductTaskUndoSummaryResp {
    /**
     * 品牌
     */
    brand?: number
    /**
     * 统计列表
     */
    summary?: ProductTaskUndoSummaryOfBrandResp[]
    [property: string]: any
  }
  export interface ProductTaskUndoSummaryOfBrandResp {
    /**
     * 品牌
     */
    brand?: string
    /**
     * 已完成数量
     */
    doneCount?: number
    /**
     * 任务节点
     */
    taskNode?: string
    /**
     * 总产品数量
     */
    totalCount?: number
    /**
     * 未完成数量
     */
    undoCount?: number
    /**
     * 未完成百分比
     */
    undoPercent?: string
    [property: string]: any
  }
  export type Row = ProductTaskUndoSummaryResp
  export type List = Row[]
  export type Response = ResponseData<List>
}
export function getTaskList(data: TaskListAPI.Request) {
  return to<TaskListAPI.Response>(
    request.post({
      url: '/pdm-base/workBench/queryProductUndoSummary',
      data
    })
  )
}

export function getLatestSeason() {
  return to<ResponseData<string>>(
    request.get({
      url: '/pdm-base/workBench/latestSeason'
    })
  )
}

export namespace VisitorTrendsAPI {
  export interface Data {
    [property: string]: string
  }
  export type Response = ResponseData<string>
}
export function getVisitorTrends() {
  return to<VisitorTrendsAPI.Response>(
    request.get({
      url: `/pdm-base/base/getMatomo`,
      params: {
        idSite: import.meta.env.VITE_API_MATOMO_ID,
        module: 'API',
        method: 'VisitsSummary.getVisits',
        period: 'day',
        date: 'last7',
        format: 'json',
        token_auth: import.meta.env.VITE_MATOMO_TOKEN_AUTH
      }
    })
  )
}
