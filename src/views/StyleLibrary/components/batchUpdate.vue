<script lang="ts" setup>
import { getBatchStyleApi, getBatchUpdateApi } from '@/api/trends'
import { BatchStyle } from '@/api/trends/types'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  editDatas: {
    type: Array<BatchStyle>,
    default: () => []
  },
  optionsDatas: {
    type: Object
  },
  updateType: {
    type: String,
    default: ''
  }
})
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refreshEdit'): void
}>()
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const closed = () => {
  emit('update:modelValue', false)
}
const tableData = ref<BatchStyle[]>([])
watch(
  () => props.editDatas,
  (val) => (tableData.value = val)
)
const loading = ref(false)
const handleSubmit = async () => {
  try {
    loading.value = true
    if (props.updateType === 'social') {
      await getBatchUpdateApi({ styles: tableData.value })
      closed()
      emit('refreshEdit')
    } else {
      await getBatchStyleApi({ styles: tableData.value })
      closed()
      emit('refreshEdit')
    }
  } finally {
    loading.value = false
  }
}
</script>
<template>
  <el-dialog
    :close-on-click-modal="false"
    :model-value="visible"
    center
    width="50%"
    @close="closed"
  >
    <vxe-table
      :cell-config="{ height: 100 }"
      :data="tableData"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
      border
      show-overflow
    >
      <vxe-column title="序号" type="seq" width="60" />
      <vxe-column fiele="pic" title="图片">
        <template #default="{ row }">
          <img
            :src="updateType === 'buyer' ? row.styleImgUrl : row.contentImgOssUrl"
            style="height: 80px; margin: 5px"
        /></template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="productTinyCategory" title="产品小类">
        <template #default="{ row }">
          {{
            optionsDatas?.productTinyCategoryDatas.find((i) => i.code === row.productTinyCategory)
              ?.cnDesc
          }}
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.productTinyCategory" filterable transfer>
            <vxe-option
              v-for="item in optionsDatas?.productTinyCategoryDatas"
              :key="item.code"
              :label="item.cnDesc"
              :value="item.code"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="colorScheme" title="色系">
        <template #default="{ row }">
          {{ optionsDatas?.colorShemaDatas.find((i) => i.code === row.colorScheme)?.cnDesc }}
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.colorScheme" filterable transfer>
            <vxe-option
              v-for="item in optionsDatas?.colorShemaDatas"
              :key="item.code"
              :label="item.cnDesc"
              :value="item.code"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="element" title="元素">
        <template #default="{ row }">
          {{ optionsDatas?.elementDatas.find((i) => i.code === row.element)?.cnDesc }}
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.element" filterable transfer>
            <vxe-option
              v-for="item in optionsDatas?.elementDatas"
              :key="item.code"
              :label="item.cnDesc"
              :value="item.code"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="heelType" title="跟型">
        <template #default="{ row }">
          {{ optionsDatas?.heelTypeDatas.find((i) => i.code === row.heelType)?.cnDesc }}
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.heelType" filterable transfer>
            <vxe-option
              v-for="item in optionsDatas?.heelTypeDatas"
              :key="item.code"
              :label="item.cnDesc"
              :value="item.code"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column :edit-render="{}" field="toeCap" title="鞋头">
        <template #default="{ row }">
          {{ optionsDatas?.topCapDatas.find((i) => i.code === row.toeCap)?.cnDesc }}
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.toeCap" filterable transfer>
            <vxe-option
              v-for="item in optionsDatas?.topCapDatas"
              :key="item.code"
              :label="item.cnDesc"
              :value="item.code"
            />
          </vxe-select>
        </template>
      </vxe-column>
    </vxe-table>
    <template #footer>
      <el-button @click="closed">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
