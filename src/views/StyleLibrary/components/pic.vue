<script setup lang="ts">
import { importStyleViewApi, importViewApi } from '@/api/trends'
import { getToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { uploadByPreUrlApi } from '@/api/system'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  type: {
    type: String
  }
})
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
  (e: 'getTableData', val): void
}>()
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const closed = () => {
  emit('update:modelValue', false)
  stylePic.value = []
  uploadImgfile.value = ''
}

const stylePic = ref([] as any)
const loading = ref(false)
const handleUploadFile = async (options) => {
  const { name: fileName, size: kbSize, uid } = options.file
  const params = {
    fileName,
    kbSize: Math.floor(kbSize / 1024),
    fileType: fileName.substring(fileName.lastIndexOf('.') + 1),
    configCode: '0001'
  }
  try {
    loading.value = true
    const { datas: res } = await uploadByPreUrlApi(params)
    const {
      originName: name,
      objectName: key,
      policy,
      accessid: OSSAccessKeyId,
      callback,
      signature,
      host: url
    } = res
    const form = Object.assign(
      {},
      {
        name,
        key,
        policy,
        OSSAccessKeyId,
        callback,
        signature
      },
      { file: options?.file }
    )
    uploadImgfile.value = options?.file
    const formData = new FormData()
    Object.keys(form).forEach((key) => formData.set(`${key}`, form[key]))
    const result = await axios({
      method: 'post',
      url,
      data: formData
    })
    result?.data?.datas && options.onSuccess({ datas: result.data.datas, uid })
  } catch (e) {
    ElMessage.error('上传失败')
  } finally {
    loading.value = false
  }
}
const uploadImgfile = ref([] as any)

const handleSearch = async () => {
  if (stylePic.value.length < 1) return ElMessage.error('请上传图片')
  else {
    try {
      loading.value = true
      const formData = new FormData()
      formData.append('img', uploadImgfile.value)
      if (props.type === 'buyer') {
        const { datas } = await importStyleViewApi(formData)
        emit('getTableData', datas)
      } else {
        const { datas } = await importViewApi(formData)
        emit('getTableData', datas)
      }
      closed()
    } finally {
      loading.value = false
    }
  }
}
const uploadHeaders = reactive({
  authorization: getToken()
})
const handleBeforeUpload = async (rawFile) => {
  if (rawFile.size > 100 * 1024 * 1024) {
    const msg = `文件大小不能超过 100 M`
    ElMessage.warning(msg)
    return false
  }
  const isJPG = ['image/jpg', 'image/jpeg', 'image/png'].includes(rawFile.type)
  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  return rawFile
}
const handleExceed = () => {
  ElMessage.warning(`至多选择1个文件!`)
}
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const handlePictureCardPreview = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url
  dialogVisible.value = true
}
const handleSuccess = (e, list) => {
  if (e) {
    const { datas, uid } = e
    const { objectName, url } = datas
    list[0] = { uid, url, objectName }
  }
}
const handleRemove = (file, list) => {
  // 删除新增的，uid匹配index
  const index = list?.findIndex((item) => item.uid === file.uid)
  index !== -1 && list?.splice(index, 1)
  uploadImgfile.value = ''
}
</script>
<template>
  <el-dialog
    :model-value="visible"
    @close="closed"
    center
    width="400"
    :close-on-click-modal="false"
  >
    <ElUpload
      v-model:file-list="stylePic"
      :headers="uploadHeaders"
      :limit="1"
      :on-exceed="handleExceed"
      :http-request="handleUploadFile"
      :on-success="(val) => handleSuccess(val, stylePic)"
      list-type="picture-card"
      show-overflow
      :before-upload="handleBeforeUpload"
      class="upload_files"
      accept=".jpg,.jpeg,.png"
    >
      <Icon icon="ep:plus" size="28" />

      <template #file="{ file }">
        <div>
          <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
              <Icon icon="ep:zoom-in" style="margin-right: -5px" />
            </span>
            <span class="el-upload-list__item-delete" @click="handleRemove(file, stylePic)">
              <Icon icon="ep:delete" />
            </span>
          </span>
        </div>
      </template>
    </ElUpload>

    <template #footer
      ><el-button :loading="loading" @click="closed">取消</el-button
      ><el-button @click="handleSearch" :loading="loading">搜索</el-button>
    </template>
  </el-dialog>
  <el-dialog v-model="dialogVisible">
    <ElImage :src="dialogImageUrl" hide-on-click-modal />
  </el-dialog>
</template>
<style lang="less" scoped>
.upload_files {
  :deep(.el-upload-list) {
    .is-success {
      width: 60px;
      height: auto;
      margin-right: 8px;
      overflow: hidden;
      border: 0;
      border-radius: 0;

      img,
      .el-upload-list__item-actions {
        height: 60px;
      }
    }

    .is-ready,
    .is-uploading {
      display: none;
    }

    > .el-upload {
      --el-upload-picture-card-size: 0px !important;
      display: block;
      background-color: transparent;
      border: none !important;
    }
  }
}
</style>
