<script setup lang="ts">
import { getToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import { uploadByPreUrlApi } from '@/api/system'
import { getStyleImgApi, getAddStyleApi } from '@/api/trends'
import { getQMSDict } from '@/utils'
import type { FormInstance } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  optionsDatas: {
    type: Object
  }
})
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()
const visible = computed({
  get: () => props.modelValue,
  set: () => {
    emit('update:modelValue', false)
  }
})
const ruleFormRef = ref<FormInstance>()
const closed = () => {
  emit('update:modelValue', false)
  ruleFormRef.value?.clearValidate()
  ruleFormRef.value?.resetFields()
  Object.assign(ruleForm, createForm())
}
const PRODUCT_ZH_CATEGORY = ref(getQMSDict('PRODUCT_ZH_CATEGORY'))

const rules = {
  productTinyCategory: [{ required: true, message: '必填' }],
  colorScheme: [{ required: true, message: '必填' }],
  element: [{ required: true, message: '必填' }],
  heelType: [{ required: true, message: '必填' }],
  toeCap: [{ required: true, message: '必填' }],
  brand: [{ required: true, message: '必填' }],
  productLargeCategory: [{ required: true, message: '必填' }],
  usdPrice: [{ required: true, message: '必填' }],
  mainPic: [{ required: true, message: '必填' }]
}
const createForm = () => ({
  productTinyCategory: '',
  productLargeCategory: '',
  colorScheme: '',
  element: '',
  heelType: '',
  toeCap: '',
  brand: '',
  usdPrice: undefined,
  mainPic: [] as any,
  styleImgUrl: ''
})
const ruleForm = reactive(createForm())
const loading = ref(false)
const uploadImgfile = ref('')
const handleUploadFile = async (options) => {
  const { name: fileName, size: kbSize, uid } = options.file
  const params = {
    fileName,
    kbSize: Math.floor(kbSize / 1024),
    fileType: fileName.substring(fileName.lastIndexOf('.') + 1),
    configCode: '0001'
  }
  try {
    // loading.value = true
    const { datas: res } = await uploadByPreUrlApi(params)
    const {
      originName: name,
      objectName: key,
      policy,
      accessid: OSSAccessKeyId,
      callback,
      signature,
      host: url
    } = res
    const form = Object.assign(
      {},
      {
        name,
        key,
        policy,
        OSSAccessKeyId,
        callback,
        signature
      },
      { file: options?.file }
    )
    uploadImgfile.value = options?.file
    const formData = new FormData()
    Object.keys(form).forEach((key) => formData.set(`${key}`, form[key]))
    const result = await axios({
      method: 'post',
      url,
      data: formData
    })
    result?.data?.datas && options.onSuccess({ datas: result.data.datas, uid })
  } catch (e) {
    ElMessage.error('上传失败')
  } finally {
    // loading.value = false
  }
}
const uploadHeaders = reactive({
  authorization: getToken()
})
const handleBeforeUpload = async (rawFile) => {
  const isJPG = ['image/jpg', 'image/jpeg', 'image/png'].includes(rawFile.type)
  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  if (rawFile.size > 100 * 1024 * 1024) {
    const msg = `文件大小不能超过 100 M`
    ElMessage.warning(msg)
    return false
  }
  return rawFile
}
const handleExceed = () => {
  ElMessage.warning(`至多选择1个文件!`)
}
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const handlePictureCardPreview = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url
  dialogVisible.value = true
}
const imgLoading = ref(false)
const handleSuccess = async (e, list) => {
  if (e) {
    try {
      loading.value = true
      imgLoading.value = true
      const { datas, uid } = e
      const { objectName, url } = datas
      list[0] = { uid, url, objectName }
      const formData = new FormData()
      formData.append('img', uploadImgfile.value)
      const { datas: tableList } = await getStyleImgApi(formData)
      const { colorScheme, element, heelType, productTinyCategory, toeCap, styleImgUrl } = tableList
      Object.assign(ruleForm, {
        colorScheme,
        element,
        heelType,
        productTinyCategory,
        toeCap,
        styleImgUrl
      })
    } finally {
      loading.value = false
      imgLoading.value = false
    }
  }
}
const handleRemove = (file, list) => {
  // 删除新增的，uid匹配index
  const index = list?.findIndex((item) => item.uid === file.uid)
  index !== -1 && list?.splice(index, 1)
  uploadImgfile.value = ''
}

const handleSubmit = async () => {
  await unref(ruleFormRef)?.validate(async (isValid) => {
    if (isValid) {
      try {
        loading.value = true
        const params = {} as any
        Object.assign(params, ruleForm)
        await getAddStyleApi(params)
        ElMessage.success('操作成功')
        emit('refresh')
        closed()
      } finally {
        loading.value = false
      }
    }
  })
}
</script>
<template>
  <el-dialog
    :model-value="visible"
    @close="closed"
    center
    width="600"
    :close-on-click-modal="false"
  >
    <el-form
      ref="ruleFormRef"
      style="max-width: 600px"
      :model="ruleForm"
      :rules="rules"
      label-width="auto"
      class="demo-ruleForm"
    >
      <div style="display: flex; justify-content: center">
        <div style="display: flex; justify-content: center; align-items: center">
          <el-form-item prop="mainPic">
            <ElUpload
              v-model:file-list="ruleForm.mainPic"
              :headers="uploadHeaders"
              :limit="1"
              :on-exceed="handleExceed"
              :http-request="handleUploadFile"
              :on-success="(val) => handleSuccess(val, ruleForm.mainPic)"
              list-type="picture-card"
              show-overflow
              :before-upload="handleBeforeUpload"
              class="upload_files"
              accept=".jpg,.jpeg,.png"
            >
              <Icon icon="ep:plus" size="28" />

              <template #file="{ file }">
                <div>
                  <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                  <span class="el-upload-list__item-actions">
                    <span
                      class="el-upload-list__item-preview"
                      @click="handlePictureCardPreview(file)"
                    >
                      <Icon icon="ep:zoom-in" style="margin-right: -5px" />
                    </span>
                    <span
                      class="el-upload-list__item-delete"
                      @click="handleRemove(file, ruleForm.mainPic)"
                    >
                      <Icon icon="ep:delete" />
                    </span>
                  </span>
                </div>
              </template>
            </ElUpload>
          </el-form-item>
        </div>
        <el-skeleton style="width: 240px" :loading="imgLoading" animated>
          <template #template>
            <el-skeleton :rows="8" />
          </template>
          <template #default>
            <div>
              <el-form-item label="产品小类" prop="productTinyCategory">
                <el-select v-model="ruleForm.productTinyCategory" filterable>
                  <el-option
                    v-for="item in optionsDatas?.productTinyCategoryDatas"
                    :value="item.code"
                    :label="item.cnDesc"
                    :key="item.code"
                /></el-select>
              </el-form-item>
              <el-form-item label="颜色" prop="colorScheme">
                <el-select v-model="ruleForm.colorScheme" filterable>
                  <el-option
                    v-for="item in optionsDatas?.colorShemaDatas"
                    :value="item.code"
                    :label="item.cnDesc"
                    :key="item.code"
                /></el-select>
              </el-form-item>
              <el-form-item label="元素" prop="element">
                <el-select v-model="ruleForm.element" filterable>
                  <el-option
                    v-for="item in optionsDatas?.elementDatas"
                    :value="item.code"
                    :label="item.cnDesc"
                    :key="item.code"
                /></el-select>
              </el-form-item>
              <el-form-item label="跟型" prop="heelType">
                <el-select v-model="ruleForm.heelType" filterable>
                  <el-option
                    v-for="item in optionsDatas?.heelTypeDatas"
                    :value="item.code"
                    :label="item.cnDesc"
                    :key="item.code"
                /></el-select>
              </el-form-item>
              <el-form-item label="鞋头" prop="toeCap">
                <el-select v-model="ruleForm.toeCap" filterable>
                  <el-option
                    v-for="item in optionsDatas?.topCapDatas"
                    :value="item.code"
                    :label="item.cnDesc"
                    :key="item.code"
                /></el-select>
              </el-form-item>
              <el-form-item label="品牌" prop="brand">
                <el-input v-model="ruleForm.brand" placeholder="请输入" maxlength="20"
              /></el-form-item>
              <el-form-item label="产品大类" prop="productLargeCategory">
                <el-select v-model="ruleForm.productLargeCategory" filterable>
                  <el-option
                    v-for="item in PRODUCT_ZH_CATEGORY"
                    :value="item.value"
                    :label="item.label"
                    :key="item.value"
                /></el-select>
              </el-form-item>
              <el-form-item label="价格（美元）" prop="usdPrice">
                <el-input-number
                  v-model="ruleForm.usdPrice"
                  placeholder="请输入"
                  :precision="2"
                  :step="0.1"
                  :max="999999.99"
                  :min="0"
                  controls-position="right"
              /></el-form-item>
            </div>
          </template>
        </el-skeleton>
      </div>
    </el-form>
    <template #footer>
      <el-button :loading="loading" @click="closed">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">确认</el-button>
    </template>
  </el-dialog>
</template>
