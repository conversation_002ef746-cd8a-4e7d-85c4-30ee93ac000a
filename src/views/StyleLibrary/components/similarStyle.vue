<script lang="ts" setup>
import { getqueryVectorIdsByBizIdApi, getSameSocialStyleApi, getStyleSameApi } from '@/api/trends'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  similarType: {
    type: String,
    default: ''
  },
  getImgDatas: {
    type: Object
  },
  optionsDatas: {
    type: Object
  }
})
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
}>()
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      getList()
    }
  }
)
const pager = reactive<pager>({
  current: 1,
  size: 10,
  total: 0
})
const handleUpdate = async (val) => {
  Object.assign(pager, val)
  if (!props.getImgDatas) return
  try {
    loading.value = true
    const { productTinyCategory, colorScheme, element } = props.getImgDatas
    const params = {} as any
    Object.assign(params, {
      productTinyCategory,
      colorScheme,
      element,
      page: pager.current,
      pageSize: pager.size
    })
    const { datas } = await getSameSocialStyleApi(params)
    tableData.value = datas.rows

    Object.assign(pager, { current: datas.currentPage, total: datas.total })
  } finally {
    loading.value = false
  }
}
const getList = async () => {
  if (!props.getImgDatas) return
  if (props.similarType === 'social') {
    try {
      loading.value = true
      const params = {} as any
      Object.assign(params, { bizId: props.getImgDatas.contentImgId, from: '01' })
      const { datas: mmtStyleVectorIds } = await getqueryVectorIdsByBizIdApi(params)
      if (mmtStyleVectorIds.length < 1) return ElMessage.warning('美迈相似款暂无数据')
      const { datas } = await getStyleSameApi({
        mmtStyleVectorIds
      })
      tableData.value = datas
    } finally {
      loading.value = false
    }
  } else {
    // 获取买手款式库的社媒相似款
    try {
      loading.value = true
      const { productTinyCategory, colorScheme, element } = props.getImgDatas
      const params = {} as any
      Object.assign(params, { productTinyCategory, colorScheme, element, page: 1, pageSize: 10 })
      const { datas } = await getSameSocialStyleApi(params)
      tableData.value = datas.rows
      Object.assign(pager, { current: datas.currentPage, total: datas.total })
    } finally {
      loading.value = false
    }
  }
}

const closed = () => {
  emit('update:modelValue', false)
  Object.assign(pager, {
    current: 1,
    size: 10,
    total: 0
  })
  radio.value = '01'
  tableData.value = []
}
const loading = ref(false)
const tableData = ref([] as any)
const radio = ref('01')
const handleRadio = async () => {
  if (!props.getImgDatas) return
  if (radio.value === '02') {
    // 获取美迈相似款
    try {
      loading.value = true
      const params = {} as any
      tableData.value = []
      Object.assign(params, { bizId: props.getImgDatas.id, from: '02' })
      const { datas: mmtStyleVectorIds } = await getqueryVectorIdsByBizIdApi(params)
      if (mmtStyleVectorIds.length < 1) return ElMessage.warning('美迈相似款暂无数据')
      const { datas } = await getStyleSameApi({
        mmtStyleVectorIds
      })
      tableData.value = datas
    } finally {
      loading.value = false
    }
  } else {
    try {
      loading.value = true
      tableData.value = []
      const { productTinyCategory, colorScheme, element } = props.getImgDatas
      const params = {} as any
      Object.assign(params, { productTinyCategory, colorScheme, element, page: 1, pageSize: 10 })
      const { datas } = await getSameSocialStyleApi(params)
      tableData.value = datas.rows
      Object.assign(pager, { current: datas.currentPage, total: datas.total })
    } finally {
      loading.value = false
    }
  }
}
</script>
<template>
  <el-dialog
    :close-on-click-modal="false"
    :model-value="visible"
    center
    width="50%"
    @close="closed"
  >
    <div v-if="similarType === 'social'">
      <el-table v-loading="loading" :data="tableData" border max-height="500" show-overflow-tooltip>
        <el-table-column align="center" label="Style" prop="style" />
        <el-table-column align="center" label="Color" prop="color" />
        <el-table-column align="center" label="图片" prop="imgUrl">
          <template #default="scope">
            <ElImage hide-on-click-modal :src="scope.row.imgUrl" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="销量" prop="saleNum" />
        <el-table-column align="center" label="库存" prop="inventory">
          <template #header>
            库存
            <el-tooltip class="box-item" effect="dark" placement="top">
              <template #content>
                平台仓库存+平台仓在途+自有仓库存+自有仓未发<br />+自有仓在途+第三方仓库存+第三方仓在途
              </template>
              <Icon class="ml-[5px]" icon="ep:info-filled" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-else>
      <el-radio-group v-model="radio" @change="handleRadio">
        <el-radio label="01">社媒</el-radio>
        <el-radio label="02">美迈</el-radio>
      </el-radio-group>
      <el-table v-loading="loading" :data="tableData" border max-height="500" show-overflow-tooltip>
        <el-table-column
          v-if="radio === '01'"
          align="center"
          label="产品小类"
          prop="productTinyCategory"
        >
          <template #default="{ row }">{{
            props.optionsDatas?.productTinyCategoryDatas?.find(
              (i) => i.code === row.productTinyCategory
            )?.cnDesc
          }}</template>
        </el-table-column>
        <el-table-column v-if="radio === '01'" align="center" label="色系" prop="colorScheme">
          <template #default="{ row }">{{
            props.optionsDatas?.colorShemaDatas?.find((i) => i.code === row.colorScheme)?.cnDesc
          }}</template>
        </el-table-column>
        <el-table-column v-if="radio === '02'" align="center" label="Style" prop="style" />
        <el-table-column v-if="radio === '02'" align="center" label="Color" prop="color" />
        <el-table-column align="center" label="图片" prop="imgUrl">
          <template #default="scope">
            <ElImage
              hide-on-click-modal
              :src="radio === '02' ? scope.row.imgUrl : scope.row.contentImgOssUrl"
            />
          </template>
        </el-table-column>
        <el-table-column v-if="radio === '02'" align="center" label="销量" prop="saleNum" />
        <el-table-column v-if="radio === '02'" align="center" label="库存" prop="inventory">
          <template #header>
            库存
            <el-tooltip class="box-item" effect="dark" placement="top">
              <template #content>
                平台仓库存+平台仓在途+自有仓库存+自有仓未发<br />+自有仓在途+第三方仓库存+第三方仓在途
              </template>
              <Icon class="ml-[5px]" icon="ep:info-filled" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column v-if="radio === '01'" align="center" label="元素" prop="element">
          <template #default="{ row }">{{
            props.optionsDatas?.elementDatas?.find((i) => i.code === row.element)?.cnDesc
          }}</template>
        </el-table-column>
        <el-table-column v-if="radio === '01'" align="center" label="跟型" prop="heelType">
          <template #default="{ row }">{{
            props.optionsDatas?.heelTypeDatas?.find((i) => i.code === row.heelType)?.cnDesc
          }}</template>
        </el-table-column>
        <el-table-column v-if="radio === '01'" align="center" label="鞋头" prop="toeCap">
          <template #default="{ row }">{{
            props.optionsDatas?.topCapDatas?.find((i) => i.code === row.toeCap)?.cnDesc
          }}</template>
        </el-table-column>
        <el-table-column v-if="radio === '01'" align="center" label="播放量" prop="viewsNum" />
        <el-table-column v-if="radio === '01'" align="center" label="点赞数" prop="likesNum" />
        <el-table-column v-if="radio === '01'" align="center" label="收藏数" prop="collectsNum" />
        <el-table-column v-if="radio === '01'" align="center" label="评论数" prop="commentsNum" />
      </el-table>
      <Paging v-if="radio === '01'" :pager="pager" @update="handleUpdate" />
    </div>

    <template #footer>
      <el-button type="primary" @click="closed">关闭</el-button>
    </template>
  </el-dialog>
</template>
