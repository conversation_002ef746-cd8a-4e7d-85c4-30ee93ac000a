<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { ElMessage, FormInstance } from 'element-plus'
import { getTypeApi } from '@/api/trendsStatistics'
import { getSocialTrendsApi } from '@/api/trends'
import StyleDetail from '../SocialTrends/components/StyleDetail.vue'
import Pic from './components/pic.vue'
import BatchUpdate from './components/batchUpdate.vue'
import { trackDialogEvent } from '@/utils/monitor'
import { cloneDeep } from 'lodash-es'

const elformRef = ref<FormInstance | null>(null)
const SM_PLATFORM = ref(getDictOptions('SM_PLATFORM'))
const ruleForm: any = reactive({
  pushTime: [],
  productTinyCategory: '',
  colorScheme: '',
  element: '',
  heelType: '',
  toeCap: '',
  platId: null,
  page: 1,
  pageSize: 10
})
const rules = {
  pushTime: [{ required: true, trigger: 'change', message: '必填' }]
}
const tableList = ref([] as any)
const pager = reactive<pager>({
  current: 1,
  size: 10,
  total: 0
})
const getDate = () => {
  const nowDate = new Date()
  const year = nowDate.getFullYear()
  const month = nowDate.getMonth() + 1
  const lastDay = new Date(year, month, 0).getDate()
  const newMonth = month < 10 ? '0' + month : month
  ruleForm.pushTime = [[year, newMonth, '01'].join('-'), [year, newMonth, lastDay].join('-')]
}
const handleUpdate = (val) => {
  Object.assign(pager, val)
  Object.assign(ruleForm, { page: val.current, pageSize: val.size })
  handleSearch()
}
const loading = ref(false)
const getList = () => {
  Object.assign(ruleForm, { page: 1, pageSize: 10 })
  handleSearch()
}
const handleSearch = async () => {
  await unref(elformRef)?.validate(async (isValid) => {
    if (isValid) {
      try {
        showPager.value = true
        loading.value = true
        const { pushTime } = ruleForm
        const [dateStart, dateEnd] = pushTime || []
        Object.assign(ruleForm, { dateStart, dateEnd })
        const { datas } = await getSocialTrendsApi(ruleForm)
        sessionStorage.setItem('SocialMediaLibrary', JSON.stringify(ruleForm))
        tableList.value = datas.rows
        Object.assign(pager, { current: datas.currentPage, total: datas.total })
      } finally {
        loading.value = false
      }
    }
  })
}
const handleReset = () => {
  elformRef.value?.resetFields()
  getDate()
  getList()
}
const productTinyCategoryList = ref([] as any)
const colorShemaList = ref([] as any)
const elementList = ref([] as any)
const heelTypeList = ref([] as any)
const topCapList = ref([] as any)
const optionsDatas = {}
const getOptions = async () => {
  const { datas: productTinyCategoryDatas } = await getTypeApi('PRODUCT_TINY_CATEGORY')
  const { datas: colorShemaDatas } = await getTypeApi('COLOR_SCHEME')
  const { datas: elementDatas } = await getTypeApi('ELEMENT ')
  const { datas: heelTypeDatas } = await getTypeApi('HEEL_TYPE')
  const { datas: topCapDatas } = await getTypeApi('TOE_CAP')
  productTinyCategoryList.value = productTinyCategoryDatas
  colorShemaList.value = colorShemaDatas
  elementList.value = elementDatas
  heelTypeList.value = heelTypeDatas
  topCapList.value = topCapDatas
  Object.assign(optionsDatas, {
    productTinyCategoryDatas,
    colorShemaDatas,
    elementDatas,
    heelTypeDatas,
    topCapDatas
  })
}

const PicVisible = ref(false)
const editVisible = ref(false)
const editDatas = ref([] as any)
const handleEdit = () => {
  const selectionList = tableList.value?.filter((i) => i.checked)
  if (!selectionList.length) return ElMessage.warning('至少勾选一条数据')
  editVisible.value = true
  editDatas.value = cloneDeep(selectionList)
  trackDialogEvent('社媒款式库修改')
}

onMounted(async () => {
  getDate()
  await getOptions()
  const storageDatas = sessionStorage.getItem('SocialMediaLibrary')
  storageDatas && Object.assign(ruleForm, JSON.parse(storageDatas))
  getList()
})
// 以图搜图
const showPager = ref(true)
const getTableData = (val) => {
  showPager.value = false
  tableList.value = val.rows
  Object.assign(pager, { current: val.currentPage, total: val.total })
}
const refreshEdit = () => {
  getList()
  tableList.value?.map((i) => Object.assign(i, { checked: false }))
}
</script>

<template>
  <ContentWrap>
    <ElRow>
      <ElCol :span="20">
        <ElForm ref="elformRef" :model="ruleForm" :rules="rules" label-width="100px">
          <ElRow>
            <ElCol :span="8">
              <ElFormItem label="选择时间段" prop="pushTime">
                <ElDatePicker
                  v-model="ruleForm.pushTime"
                  :clearable="false"
                  format="YYYY-MM-DD"
                  type="daterange"
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="平台" prop="platId">
                <ElSelect v-model="ruleForm.platId" clearable filterable
                  ><ElOption
                    v-for="item in SM_PLATFORM"
                    :key="Number(item.dictValue)"
                    :label="item.dictCnName"
                    :value="Number(item.dictValue)"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="20">
            <ElCol :span="6">
              <ElFormItem label="打标维度" prop="productTinyCategory">
                <ElSelect
                  v-model="ruleForm.productTinyCategory"
                  clearable
                  filterable
                  placeholder="产品小类"
                  ><ElOption
                    v-for="item in productTinyCategoryList"
                    :key="item.code"
                    :label="item.cnDesc"
                    :value="item.code"
                /></ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="4">
              <ElFormItem label-width="0px" prop="colorScheme">
                <ElSelect v-model="ruleForm.colorScheme" clearable filterable placeholder="色系"
                  ><ElOption
                    v-for="item in colorShemaList"
                    :key="item.code"
                    :label="item.cnDesc"
                    :value="item.code"
                /></ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="4">
              <ElFormItem label-width="0px" prop="collectFlag">
                <ElSelect
                  v-model="ruleForm.collectFlag"
                  clearable
                  filterable
                  placeholder="收藏状态"
                >
                  <ElOption label="已收藏" value="true" />
                  <ElOption label="未收藏" value="false" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <!-- <ElCol :span="4">
              <ElFormItem label-width="0px" prop="element">
                <ElSelect v-model="ruleForm.element" placeholder="元素" clearable filterable
                  ><ElOption
                    v-for="item in elementList"
                    :label="item.cnDesc"
                    :value="item.code"
                    :key="item.code"
                /></ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="4">
              <ElFormItem label-width="0px" prop="heelType">
                <ElSelect v-model="ruleForm.heelType" placeholder="跟型" clearable filterable
                  ><ElOption
                    v-for="item in heelTypeList"
                    :label="item.cnDesc"
                    :value="item.code"
                    :key="item.code"
                /></ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="4">
              <ElFormItem label-width="0px" prop="toeCap">
                <ElSelect v-model="ruleForm.toeCap" placeholder="鞋头" clearable filterable
                  ><ElOption
                    v-for="item in topCapList"
                    :label="item.cnDesc"
                    :value="item.code"
                    :key="item.code"
                /></ElSelect>
              </ElFormItem>
            </ElCol> -->
          </ElRow>
        </ElForm>
      </ElCol>
      <ElCol :span="4">
        <ElButton v-loading="loading" type="primary" @click="getList">
          <Icon class="mr-1" icon="ep:search" />
          查询
        </ElButton>
        <ElButton v-loading="loading" @click="handleReset">
          <Icon class="mr-1" icon="ep:refresh-right" />
          重置
        </ElButton>
      </ElCol>
    </ElRow>
    <ElButton @click="PicVisible = true">点击上传图片，以图搜图</ElButton>
    <ElRow class="mt-5 mb-5">
      <ElCol :span="16"
        ><ElButton v-hasPermi="['editSocialStyle']" plain type="primary" @click="handleEdit"
          ><Icon class="mr-1" icon="ep:edit" />修改维度数据</ElButton
        ></ElCol
      >
      <ElCol v-if="showPager" :span="8"
        ><Paging :pager="pager" class="mt-0 mr-2" @update="handleUpdate"
      /></ElCol>
    </ElRow>
    <div v-loading="loading" class="flex flex-wrap mb-5">
      <StyleDetail
        v-if="tableList.length > 0"
        :data="tableList"
        :isChecked="true"
        :isSocial="true"
        :type="'social'"
        @refresh="getList"
        @update="(val) => (tableList = val)"
      />
      <div v-else>暂无数据</div>
    </div>

    <Paging v-if="showPager" :pager="pager" @update="handleUpdate" />
    <BatchUpdate
      v-model="editVisible"
      :editDatas="editDatas"
      :optionsDatas="optionsDatas"
      :updateType="'social'"
      @refresh-edit="refreshEdit"
    />
    <Pic v-model="PicVisible" :type="'social'" @refresh="getList" @get-table-data="getTableData" />
  </ContentWrap>
</template>
