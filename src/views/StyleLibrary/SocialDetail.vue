<script setup lang="ts">
import { closeCurrentTag } from '@/utils/routerHelper'
import SimilarTable from '@/views/SocialTrends/components/SimilarTable.vue'
import StyleDetail from '@/views/SocialTrends/components/StyleDetail.vue'
import { useRouter, onBeforeRouteUpdate } from 'vue-router'
import { getSameSocialStyleApi, getqueryVectorIdsByBizIdApi, getStyleSameApi } from '@/api/trends'
import { ElMessage } from 'element-plus'

const { push, currentRoute } = useRouter()
const scrollToTop = () => {
  document.getElementById('social')?.scrollIntoView()
}
onMounted(() => {
  scrollToTop()
})
// 在路由更新之前触发scrollToTop方法
onBeforeRouteUpdate(scrollToTop)

const Rows = ref([{ comment: 1 }] as any)
const srcList = ref([] as any)
const similarDatas = ref([] as any)

// 获取详情
const getDatas = async () => {
  const itemObject = JSON.parse(currentRoute.value.query.datas as string)
  Rows.value = [itemObject]
  const { contentImgId } = itemObject

  // 获取社媒相似款
  getSocialTable(itemObject)
  // 获取美迈相似款
  getMmtTable(contentImgId)
}

const getSocialTable = async (itemObject) => {
  const { productTinyCategory, colorScheme, element } = itemObject
  const params = {} as any
  Object.assign(params, { productTinyCategory, colorScheme, element, page: 1, pageSize: 10 })
  const {
    datas: { rows: socialSimilarDatas }
  } = await getSameSocialStyleApi(params)
  srcList.value = socialSimilarDatas?.map((i) => i.contentImgOssUrl).splice(0, 4)
}
const getMmtTable = async (contentImgId) => {
  const { datas: mmtStyleVectorIds } = await getqueryVectorIdsByBizIdApi({
    bizId: contentImgId,
    from: '01'
  })
  if (mmtStyleVectorIds.length < 1) return ElMessage.warning('美迈相似款暂无数据')
  const { datas: mmtSimilarDatas } = await getStyleSameApi({
    mmtStyleVectorIds
  })
  similarDatas.value = mmtSimilarDatas
}

const goBack = () => {
  closeCurrentTag()
  push({
    name: 'SocialMediaLibrary'
  })
}

onMounted(() => {
  getDatas()
})
</script>
<template>
  <ContentWrap>
    <div class="word-style" id="social">社媒相似款</div>
    <div style="display: flex; align-items: center">
      <StyleDetail :data="Rows" :type="'detail'" :isSocial="true" />
      <ElImage
        v-for="(item, index) in srcList"
        :src="item"
        hide-on-click-modal
        :key="item"
        style="height: 240px; margin-left: 50px"
        :preview-src-list="srcList"
        :initial-index="index"
      />
    </div>
    <el-divider />
    <div class="word-style">美迈相似款</div>
    <SimilarTable :similarDatas="similarDatas" />

    <div style="display: flex; justify-content: center; margin-top: 20px">
      <el-button type="primary" @click="goBack">关闭</el-button>
    </div>
  </ContentWrap>
</template>
<style lang="less" scoped>
.word-style {
  margin-bottom: 6px;
  font-size: 24px;
  font-weight: bold;
}
</style>
