import to from 'await-to-js'
import { service } from '@/config/axios/service'
import { ViewListAPI } from '@/views/basic-library-manage/product-library/api/product-list'
import type { ProductSellingPointAddAPI } from '@/api/productFeatures/types'

export function getAllViews() {
  return to<ViewListAPI.Response>(
    service.get(`/pdm-base/viewConfiguration/allSellPointViewByLogin`)
  )
}
export namespace DetailApi {
  export interface Params {
    productNumber?: string
    id?: string
  }
  export type Request = Params
  export type Response = ResponseData<ProductSellingPointAddAPI.Params>
}
export function getDetail(params: DetailApi.Request) {
  return to<DetailApi.Response>(
    service.get(`/pdm-base/pdmProductSellPoint/getPdmProductSellPointDetail`, { params })
  )
}
