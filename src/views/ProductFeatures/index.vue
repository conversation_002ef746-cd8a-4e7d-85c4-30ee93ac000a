<script lang="tsx" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { ref } from 'vue'
import {
  ElButton,
  ElCollapseTransition,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElMessage,
  ElOption,
  ElPagination,
  ElSelect,
  FormInstance
} from 'element-plus'
import { type VxeGridInstance, VxeGridProps, VxeTableInstance } from 'vxe-table'
import {
  getProductCategoryList,
  Pager,
  ProductCategoryListAPI
} from '@/views/basic-library-manage/api/common'
import dayjs from 'dayjs'
import { isEqual } from 'lodash-es'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { Icon } from '@/components/Icon'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { SelectPlus } from '@/components/Business/SelectPlus'
import { Edit, Plus, RefreshLeft, Search } from '@element-plus/icons-vue'
import { ProductSellingPointPageAPI } from '@/api/productFeatures/types'
import { changeProductSellingPointStatus, productSellingPointPage } from '@/api/productFeatures'
import AddDialog from './components/AddDialog.vue'
import VersionDialog from './components/VersionDialog.vue'
import { StatusEnum, YesNoEnum } from '@/views/basic-library-manage/const'
import { ProductDataStatusEnums } from '@/enums'
import { Column } from '@/views/basic-library-manage/product-library/const'
import { ViewListAPI } from '@/views/basic-library-manage/product-library/api/product-list'
import CustomColumnsDialog from '@/views/basic-library-manage/product-library/components/CustomColumnsDialog.vue'
import { getAllViews } from './apis/list'

const props = defineProps<{
  isEmbed?: boolean
}>()

defineOptions({
  name: 'ProductFeature'
})

const useConst = () => {
  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '135'
    },
    {
      formLabelLength: '180'
    }
  ])

  const productCategoryList = ref<ProductCategoryListAPI.Data[]>([])
  const fetchProductCategoryList = async () => {
    const [error, result] = await getProductCategoryList()
    if (error === null && result?.datas) {
      productCategoryList.value = result?.datas
    }
  }
  fetchProductCategoryList()

  return {
    formLabelLength,
    productCategoryList
  }
}

const { formLabelLength, productCategoryList } = useConst()

const useQuery = () => {
  type FormMode = ProductSellingPointPageAPI.Params & { date?: [string, string] }
  const formRef = ref<FormInstance>()
  const tableRef = ref<VxeTableInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const tableData = ref<ProductSellingPointPageAPI.List[]>([])
  const queryLoading = ref(false)
  const defaultFormData: FormMode = {
    sellPointStatus: [StatusEnum.DRAFT, StatusEnum.START],
    productNumberList: [],
    productLaunchSeason: '',
    brandList: [],
    styleNumber: '',
    shoeName: '',
    toeStandard: '',
    launchSeason: '',
    productStyle: '',
    targetAudience: '',
    startTime: '',
    endTime: '',
    designPersonId: undefined,
    productCategory: '',
    designerId: undefined,
    combatTeam: '',
    styleWms: '',
    developmentDirection: '',
    productPositioning: '',
    date: [dayjs().add(-3, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
  }
  const formData = ref<FormMode>({
    ...defaultFormData
  })
  let lastFormData = {
    ...defaultFormData
  }
  const pager = ref<Pager>({
    current: 1,
    size: 10,
    total: 0
  })
  const queryParams = computed<ProductSellingPointPageAPI.Request>(() => {
    return {
      ...formData.value,
      ...pager.value,
      startTime: formData.value.date?.[0],
      endTime: formData.value.date?.[1],
      date: undefined
    }
  })

  const defaultTime: [Date, Date] = [
    dayjs('00:00:00', 'HH:mm:ss').toDate(),
    dayjs('23:59:59', 'HH:mm:ss').toDate()
  ]
  const router = useRouter()
  let controller: AbortController | null = null
  const handleQuery = async () => {
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery()
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await productSellingPointPage(queryParams.value, controller.signal)
    queryLoading.value = false
    if (error === null && result?.datas) {
      lastFormData = { ...formData.value }
      const { records } = result.datas
      tableData.value = records
      pager.value.total = result.datas.pager.total
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
    handleQuery()
  }

  onActivated(() => {
    handleQuery()
  })
  enum ExportTypeEnum {
    SELL_POINT = 'sellpoint-export',
    SKC_IMAGE = 'sellpoint-skcExport'
  }

  const exportList = [
    {
      label: '导出产品卖点数据',
      value: ExportTypeEnum.SELL_POINT
    },
    {
      label: '导出SKC图片数据',
      value: ExportTypeEnum.SKC_IMAGE
    }
  ]

  const { handleExport: exportFn, loading: exportLoading } = useOmsExport(false)
  const handleExport = (exportType: ExportTypeEnum) => {
    const isExportSkc = exportType === ExportTypeEnum.SKC_IMAGE
    const selected: ProductSellingPointPageAPI.List[] | undefined =
      tableRef.value?.getCheckboxRecords()
    let reqParam: string
    if (selected && selected.length > 0) {
      reqParam = JSON.stringify({
        idList: selected.map((item) => item.id),
        exportConfigViewId: isExportSkc ? undefined : currentView.value.id
      })
    } else {
      reqParam = JSON.stringify({
        ...queryParams.value,
        exportConfigViewId: isExportSkc ? undefined : currentView.value.id
      })
    }
    exportFn({
      exportType: exportType.toString(),
      reqParam
    })
  }

  const visible = ref(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef,
    offsetBottom: props.isEmbed ? 50 + 20 + 32 + 10 + 30 : 0
  })

  const formRules = ref({})

  return {
    formRef,
    formRules,
    tableRef,
    pagerRef,
    tableData,
    queryLoading,
    formData,
    pager,
    exportList,
    defaultTime,
    handleQuery,
    handleReset,
    handleExport,
    exportLoading,
    visible,
    setVisible,
    maxHeight
  }
}

const {
  formRef,
  formRules,
  tableRef,
  pagerRef,
  tableData,
  exportList,
  queryLoading,
  formData,
  pager,
  maxHeight,
  defaultTime,
  handleQuery,
  handleReset,
  handleExport,
  exportLoading,
  visible,
  setVisible
} = useQuery()

// 版本记录弹窗
const useVersionDialog = () => {
  const versionDialogVisible = ref(false)
  const setVersionDialogVisible = () => {
    versionDialogVisible.value = !unref(versionDialogVisible)
  }
  return {
    versionDialogVisible,
    setVersionDialogVisible
  }
}

const useOperation = () => {
  const currentRow = ref<ProductSellingPointPageAPI.List & { viewType?: string }>()

  // 新增产品卖点
  const addDialogVisible = ref(false)
  const handleOpenAddDialog = (row?: ProductSellingPointPageAPI.List & { viewType?: string }) => {
    router.push({
      name: 'CreateFeature'
    })
  }

  // 修改产品卖点
  const handleEdit = (hasCurrentRow?: boolean) => {
    const selectedRows: ProductSellingPointPageAPI.List[] =
      tableRef.value?.getCheckboxRecords() || []
    if (selectedRows.length === 0) {
      ElMessage.warning('请选择一条数据')
      return
    }
    if (selectedRows.length > 1) {
      ElMessage.warning('只能选择一条数据')
      return
    }
    const row = selectedRows[0]
    if (row.productDataStatus === ProductDataStatusEnums.Invalid) {
      ElMessage.warning('作废的产品不能修改产品卖点，请重新选择')
      return
    }
    router.push({
      name: 'EditFeature',
      query: {
        id: selectedRows[0].id,
        productNumber: selectedRows[0].productNumber
      }
    })
  }

  // 版本记录
  const { versionDialogVisible, setVersionDialogVisible } = useVersionDialog()
  const handleOpenVersionDialog = (row: ProductSellingPointPageAPI.List) => {
    currentRow.value = row
    setVersionDialogVisible()
  }
  const handleViewHistory = (id: number) => {
    router.push({
      name: 'HistoryFeature',
      query: {
        id
      }
    })
  }
  const handleViewDetail = (row: ProductSellingPointPageAPI.List) => {
    router.push({
      name: 'ViewFeature',
      query: {
        id: row.id,
        productNumber: row.productNumber
      }
    })
  }

  const router = useRouter()
  const handleDetail = (row: ProductSellingPointPageAPI.List) => {
    router.push({
      name: 'ViewProduct',
      query: {
        id: row.productId
      }
    })
  }

  const handleChangeStatus = async (
    sellPointStatus: string,
    row: ProductSellingPointPageAPI.List
  ) => {
    queryLoading.value = true
    const [error, result] = await changeProductSellingPointStatus({
      id: row.id,
      productNumber: row.productNumber,
      sellPointStatus
    })
    if (error === null && result) {
      ElMessage.success(result.msg)
      handleQuery()
      return
    }
    queryLoading.value = false
  }

  return {
    addDialogVisible,
    handleEdit,
    handleOpenAddDialog,
    handleChangeStatus,
    currentRow,
    handleViewDetail,
    handleDetail,
    handleViewHistory,
    versionDialogVisible,
    handleOpenVersionDialog
  }
}

const {
  addDialogVisible,
  currentRow,
  versionDialogVisible,
  handleOpenVersionDialog,
  handleChangeStatus,
  handleEdit,
  handleViewDetail,
  handleDetail,
  handleOpenAddDialog,
  handleViewHistory
} = useOperation()

const useCustomTable = () => {
  const tableRef = ref<VxeGridInstance>()
  const defaultColumns: Column<ProductSellingPointPageAPI.List>[] = [
    {
      type: 'checkbox',
      field: 'checkbox',
      width: 40
    },
    {
      type: 'seq',
      field: 'seq',
      title: '序号',
      width: 55
    },
    {
      title: '卖点编号',
      field: 'sellPointNumber',
      width: 160,
      cellRender: {
        name: 'Link',
        props: {
          clickFn: ({ row }: { row: ProductSellingPointPageAPI.List }) => {
            handleViewDetail(row)
          }
        }
      }
    },
    {
      title: '产品编号',
      field: 'productNumber',
      width: 150,
      cellRender: {
        name: 'Link',
        props: {
          clickFn: ({ row }: { row: ProductSellingPointPageAPI.List }) => {
            handleDetail(row)
          }
        }
      }
    },
    {
      title: '缩略图',
      field: 'thumbnail',
      width: 100,
      cellRender: {
        name: 'Image'
      }
    },
    {
      title: 'Style（WMS）',
      field: 'styleWms',
      width: 120
    },
    {
      title: '品牌',
      field: 'brandName',
      width: 120
    },
    {
      title: '商品上市季节',
      field: 'productLaunchSeason',
      width: 120
    },
    {
      title: '产品类目',
      field: 'productCategoryItemName',
      width: 120
    },
    {
      title: 'PRODUCT NAME',
      field: 'shoeName',
      width: 120
    },
    {
      title: '款色数',
      field: 'validSkcCount',
      width: 120
    },
    {
      title: '卖点优先级',
      field: 'sellPointPriority',
      width: 120,
      showOverflow: true
    },
    {
      title: '场景/穿搭',
      field: 'sceneDressingItemName',
      width: '120'
    },
    {
      title: '功能/特性',
      field: 'functionCharacteristicsItemName',
      showOverflow: true,
      width: '120'
    },
    {
      title: '趋势/元素',
      field: 'trendElementItemName',
      width: '120'
    },
    {
      title: '舒适度',
      field: 'comfortLevelItemName',
      width: '120'
    },
    {
      title: '其他卖点',
      field: 'sellPointOtherItemName',
      showOverflow: true,
      width: '120'
    },
    {
      title: '创建者',
      field: 'createByItemName',
      width: '120'
    },
    {
      title: '创建时间',
      field: 'createTime',
      width: '120'
    },
    {
      title: '操作',
      field: 'operation',
      width: '120',
      fixed: 'right',
      slots: {
        default: ({ row }: { row: ProductSellingPointPageAPI.List }) => (
          <ElButton text type="primary" onClick={() => handleOpenVersionDialog(row)}>
            版本记录
          </ElButton>
        )
      }
    }
  ]
  const columns = ref([...defaultColumns])
  const tableOptions = computed<VxeGridProps<ProductSellingPointPageAPI.List>>(() => ({
    minHeight: 100,
    columns: columns.value,
    maxHeight: maxHeight.value - 100,
    data: tableData.value,
    loading: queryLoading.value,
    showOverflow: 'tooltip',
    scrollX: {
      enabled: true,
      gt: 20
    },
    cellConfig: {
      height: 60
    },
    scrollY: {
      enabled: true,
      gt: 20
    }
  }))
  const columnSettingRef = ref<InstanceType<typeof CustomColumnsDialog>>()
  const viewList = ref<ViewListAPI.List>([])
  const currentView = ref<ViewListAPI.Row>(viewList.value[0])
  const customerDialogVisible = ref(false)

  watch(
    () => currentView.value,
    (val) => {
      const allColumns = columnSettingRef.value?.allColumns
      const currentViewColumns = Array.isArray(val.fieldAttr)
        ? val.fieldAttr.map((field, index) => {
            const column = defaultColumns.find((item) => item.field === field)
            if (column) {
              return column
            }

            const columnImgFlag = allColumns?.find((item) => {
              return item.attrField === field
            })?.imgFlag
            const isImgColumn = columnImgFlag === parseInt(YesNoEnum.Y, 10)
            const isFileColumn = +columnImgFlag === 2

            const defaultColumnConfig = defaultColumns.find((item) => item.field === field)
            const fieldTitle = val.fieldTitle?.[index] || field
            const minWidth = defaultColumnConfig?.minWidth || 100
            return {
              ...defaultColumnConfig,
              field: field,
              title: fieldTitle,
              minWidth,
              showOverflow: true,
              ...(isImgColumn && {
                cellRender: { name: 'Image' }
              }),
              ...(isFileColumn && {
                cellRender: { name: 'File' }
              })
            }
          })
        : []
      if (val.id) {
        currentViewColumns.unshift(defaultColumns[0])
      }
      columns.value = currentViewColumns
    }
  )

  const fetchAllViews = async (viewId?: number) => {
    const [error, result] = await getAllViews()
    if (error === null && result?.datas) {
      viewList.value = result.datas || []
      if (typeof viewId === 'number') {
        const updatedView = viewList.value.find((item) => item.id === viewId)
        if (updatedView) {
          currentView.value = updatedView
          return
        }
      }
      currentView.value = viewList.value[0]
    }
  }

  onMounted(() => {
    history.state?.productNumber &&
      (formData.value.productNumberList = [history.state?.productNumber])
    Promise.all([handleQuery()])
    fetchAllViews()
  })

  return {
    currentView,
    tableRef,
    tableOptions,
    columns,
    viewList,
    fetchAllViews,
    columnSettingRef,
    customerDialogVisible
  }
}

const {
  tableOptions,
  currentView,
  viewList,
  fetchAllViews,
  columnSettingRef,
  customerDialogVisible
} = useCustomTable()

defineExpose({
  tableRef
})
</script>

<template>
  <ContentWrap>
    <div class="max-h-[calc(100vh_-_200px)] overflow-x-hidden overflow-y-auto">
      <ElForm
        ref="formRef"
        :label-width="formLabelLength"
        :model="formData"
        :rules="formRules"
        @submit="
          (e) => {
            e.preventDefault()
          }
        "
      >
        <div class="grid grid-cols-3 items-start gap-x-4">
          <ElFormItem label="产品编号" prop="productNumberList">
            <SelectPlus
              v-model="formData.productNumberList"
              api-key="getProductNumberList"
              filterable
              multiple
              virtualized
            />
          </ElFormItem>
          <ElFormItem label="商品上市季节" prop="productLaunchSeason">
            <SelectPlus
              v-model="formData.productLaunchSeason"
              api-key="COMMON_MARKET_SEASON"
              cache
              filterable
            />
          </ElFormItem>
          <ElScrollbar class="w-full h-[32px]">
            <ElFormItem class="form-item-no-wrap" label="" label-width="0">
              <ElButton text @click="setVisible">
                {{ visible ? '收起' : '展开' }}
                <Icon
                  :class="visible ? 'rotate-90' : ''"
                  class="transform transition duration-400"
                  icon="ep:arrow-down"
                />
              </ElButton>
              <ElButton
                :icon="Search"
                :loading="queryLoading"
                class="w-16"
                native-type="submit"
                type="primary"
                @click="handleQuery"
              >
                查询
              </ElButton>
              <ElButton
                :icon="RefreshLeft"
                :loading="queryLoading"
                class="w-16"
                native-type="reset"
                @click="handleReset"
              >
                重置
              </ElButton>
              <ElDropdown class="ml-3" @command="handleExport">
                <ElButton :loading="exportLoading" class="w-16" type="primary">
                  <Icon class="mr-1" icon="ep:upload-filled" />
                  导出
                </ElButton>
                <template #dropdown>
                  <ElDropdownMenu>
                    <ElDropdownItem
                      v-for="item in exportList"
                      :key="item.value"
                      :command="item.value"
                    >
                      {{ item.label }}
                    </ElDropdownItem>
                  </ElDropdownMenu>
                </template>
              </ElDropdown>
            </ElFormItem>
          </ElScrollbar>
          <ElFormItem class="col-span-2" label="品牌" prop="brand">
            <SelectPlus
              v-model="formData.brandList"
              api-key="baseBrand"
              cache
              checkbox
              checkbox-button
              filterable
            />
          </ElFormItem>
          <ElCollapseTransition>
            <div v-show="visible" class="col-span-2 grid grid-cols-2 items-start gap-x-4 w-full">
              <ElFormItem label="Style编号" prop="styleNumber">
                <SelectPlus
                  v-model="formData.styleNumber"
                  api-key="getStyleNumberList"
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  placeholder="请选择"
                  virtualized
                />
              </ElFormItem>

              <ElFormItem label="PRODUCT NAME" prop="shoeName">
                <ElInput
                  v-model="formData.shoeName"
                  class="w-48"
                  clearable
                  maxlength="100"
                  placeholder="请输入"
                  show-word-limit
                />
              </ElFormItem>
              <ElFormItem label="楦头标准" prop="toeStandard">
                <SelectPlus
                  v-model="formData.toeStandard"
                  api-key="LAST_TYPE"
                  cache
                  clearable
                  filterable
                  placeholder="请选择楦头类别"
                />
              </ElFormItem>
              <ElFormItem label="开发季节" prop="launchSeason">
                <SelectPlus
                  v-model="formData.launchSeason"
                  api-key="COMMON_MARKET_SEASON"
                  cache
                  filterable
                />
              </ElFormItem>
              <ElFormItem label="产品风格" prop="productStyle">
                <SelectPlus
                  v-model="formData.productStyle"
                  api-key="PRODUCT_STYLE"
                  cache
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  placeholder="请选择"
                />
              </ElFormItem>
              <ElFormItem label="适用人群" prop="targetAudience">
                <SelectPlus
                  v-model="formData.targetAudience"
                  api-key="PRODUCT_PEOPLE"
                  clearable
                  filterable
                  placeholder="请选择适用人群"
                />
              </ElFormItem>
              <ElFormItem label="创建时间">
                <ElDatePicker
                  v-model="formData.date"
                  :default-time="defaultTime"
                  end-placeholder="结束时间"
                  placeholder="请选择"
                  start-placeholder="开始时间"
                  type="daterange"
                  unlink-panels
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
              <ElFormItem label="产品企划" prop="designPersonId">
                <CascadeSelector
                  v-model="formData.designPersonId"
                  :props="{ emitPath: false }"
                  api-key="allUsers"
                  cache
                  placeholder="请选择产品企划"
                />
              </ElFormItem>
              <ElFormItem label="产品类目" prop="productCategory">
                <ElCascader
                  ref="productCategoryRef"
                  v-model="formData.productCategory"
                  :options="productCategoryList"
                  :props="{
                    emitPath: false,
                    value: 'selectorKey',
                    label: 'selectorEnValue',
                    children: 'childList'
                  }"
                  clearable
                  filterable
                  placeholder="请选择"
                />
              </ElFormItem>
              <ElFormItem label="产品设计师" prop="designerId">
                <CascadeSelector
                  v-model="formData.designerId"
                  :props="{ emitPath: false }"
                  api-key="allUsers"
                  cache
                  placeholder="请选择产品设计师"
                />
              </ElFormItem>
              <ElFormItem label="CT归属" prop="combatTeam">
                <SelectPlus
                  v-model="formData.combatTeam"
                  api-key="COMBAT_TEAM"
                  clearable
                  filterable
                  placeholder="请选择CT归属"
                />
              </ElFormItem>
              <ElFormItem label="Style（WMS）" prop="styleWms">
                <SelectPlus
                  v-model="formData.styleWms"
                  api-key="getStyleWmsList"
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  placeholder="请选择"
                  virtualized
                />
              </ElFormItem>
              <ElFormItem label="数据状态" prop="sellPointStatus">
                <ElSelect
                  v-model="formData.sellPointStatus"
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  multiple
                  placeholder="请选择"
                >
                  <ElOption :value="StatusEnum.DRAFT" label="草稿" />
                  <ElOption :value="StatusEnum.START" label="启用" />
                </ElSelect>
              </ElFormItem>
              <ElFormItem label="产品系列" prop="developmentDirection">
                <SelectPlus
                  v-model="formData.developmentDirection"
                  api-key="PRODUCT_SERIES"
                  cache
                  clearable
                  filterable
                  placeholder="请选择产品系列"
                />
              </ElFormItem>
              <ElFormItem label="产品目标定级" prop="productPositioning">
                <SelectPlus
                  v-model="formData.productPositioning"
                  api-key="PRODUCT_POSITION"
                  cache
                  clearable
                  filterable
                  placeholder="请选择产品目标定级"
                />
              </ElFormItem>
            </div>
          </ElCollapseTransition>
        </div>
      </ElForm>
      <VxeToolbar>
        <template #buttons>
          <ElButton
            v-hasPermi="['product:addProductSellPoint']"
            :icon="Plus"
            type="primary"
            @click="handleOpenAddDialog()"
          >
            新增产品卖点
          </ElButton>
          <ElButton
            v-hasPermi="['product:editProductSellPoint']"
            :icon="Edit"
            type="primary"
            @click="handleEdit(false)"
          >
            修改产品卖点
          </ElButton>
        </template>
        <template #tools>
          <div class="flex flex-nowrap">
            <ElSelect v-model="currentView" class="min-w-36 mr-1" filterable value-key="name">
              <ElOption
                v-for="item in viewList"
                :key="item.name"
                :label="item.name"
                :value="item"
              />
            </ElSelect>
            <ElButton type="primary" @click="customerDialogVisible = true">
              <Icon icon="uil:setting" />
              <span class="text-[14px]">配置视图</span>
            </ElButton>
          </div>
        </template>
      </VxeToolbar>
      <VxeGrid ref="tableRef" v-bind="tableOptions" />
    </div>
    <ElPagination
      ref="pagerRef"
      v-model:current-page="pager.current"
      v-model:page-size="pager.size"
      :total="pager.total"
      background
      class="mt-4"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleQuery"
      @current-change="handleQuery"
    />
    <VersionDialog
      v-model="versionDialogVisible"
      :current-row="currentRow"
      @view-detail="handleViewHistory"
    />
    <AddDialog
      v-model="addDialogVisible"
      :current-row="currentRow"
      @edit="handleEdit(true)"
      @refresh="handleQuery"
    />
    <CustomColumnsDialog
      ref="columnSettingRef"
      v-model="customerDialogVisible"
      :view-list="viewList"
      column-type="sellPoint"
      @refresh="(viewId) => fetchAllViews(viewId)"
    />
  </ContentWrap>
</template>

<style lang="less" scoped>
:deep(.el-checkbox-button.is-checked .el-checkbox-button__inner) {
  color: var(--el-checkbox-button-checked-text-color);
  background-color: var(--el-checkbox-button-checked-bg-color);
  border-color: var(--el-checkbox-button-checked-border-color);
  box-shadow: -1px 0 0 0 var(--el-color-primary-light-7);
}
</style>
