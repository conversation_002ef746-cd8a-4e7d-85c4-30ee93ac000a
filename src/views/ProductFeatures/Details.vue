<script setup lang="ts">
import { productSellingPointAdd, productSellingPointUpdate } from '@/api/productFeatures'
import type {
  ProductSellingPointAddAPI,
  ProductSellingPointPageAPI,
  ProductSellingPointUpdateAPI
} from '@/api/productFeatures/types'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { OperationTypeEnums } from '@/enums'
import { formatArrayRules } from '@/utils'
import { getDetail } from '@/views/ProductFeatures/apis/list'
import { ContentWrap } from '@/components/ContentWrap'
defineOptions({
  name: 'FeatureDetail'
})
interface Props {
  modelValue: boolean
  operationType: OperationTypeEnums
  currentRow?: ProductSellingPointPageAPI.List
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})
const formData = ref<ProductSellingPointAddAPI.Params>({
  brandId: undefined,
  fileDTOList: [],
  saleSeason: ''
})
const formRef = ref<FormInstance>()

const rules = formatArrayRules([
  ['brandId', '品牌'],
  ['fileDTOList', '产品卖点文档'],
  ['saleSeason', '上市季节']
])
const loading = ref(false)
const submitLoading = ref(false)
const route = useRoute()
const handleCommit = () => {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      const { msg } =
        props.operationType === OperationTypeEnums.Add
          ? await productSellingPointAdd(formData.value).finally(() => {
              submitLoading.value = false
            })
          : await productSellingPointUpdate(
              formData.value as ProductSellingPointUpdateAPI.Params
            ).finally(() => {
              submitLoading.value = false
            })
      ElMessage.success(msg)
      useClosePage('ProductFeature')
    }
  })
}
watch(
  () => props.modelValue,
  async (modelValue) => {
    if (modelValue) {
      await fetchFeatureInfo()
    } else {
      formRef.value?.resetFields()
      formData.value.fileInstruction = ''
      formData.value.remark = ''
    }
  }
)
const isCreate = computed(() => {
  return props.operationType === OperationTypeEnums.Add
})
const id = computed(() => {
  return route.query.id || ''
})
const productNumber = computed(() => {
  return route.query.productNumber || ''
})
const fetchFeatureInfo = async () => {
  if (isCreate.value) {
    return false
  }
  loading.value = true
  const [error, result] = await getDetail({ productNumber: productNumber.value, id: id.value })
  if (error === null && result?.datas) {
    formData.value = result.datas
  }
}
const handleClose = () => {
  useClosePage('ProductFeature')
}

const emits = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'done'): void
}>()
</script>
<template>
  <ContentWrap class="info-wrapper">
    <div>
      <ElForm class="mt-2" :model="formData" ref="formRef" label-width="8em" :rules="rules">
        <ElFormItem label="品牌" prop="brandId">
          <SelectPlus
            :parent-scroll="false"
            class="!w-full"
            cache
            api-key="baseBrand"
            v-model="formData.brandId"
          />
        </ElFormItem>
        <ElFormItem label="上市季节" prop="saleSeason">
          <SelectPlus
            :parent-scroll="false"
            class="!w-full"
            cache
            api-key="COMMON_MARKET_SEASON"
            v-model="formData.saleSeason"
          />
        </ElFormItem>
        <ElFormItem label="产品卖点文档" prop="fileDTOList" class="file-update">
          <OssUpload
            v-model="formData.fileDTOList"
            listType="text"
            drag
            multiple
            :limit="20"
            :size-limit="1024 * 1024 * 100"
          />
        </ElFormItem>
        <ElFormItem label="文件说明">
          <ElInput
            v-model="formData.fileInstruction"
            placeholder="请输入名称"
            maxlength="200"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="备注">
          <ElInput
            type="textarea"
            v-model="formData.remark"
            placeholder="请输入名称"
            :autosize="{ minRows: 4, maxRows: 4 }"
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>
      </ElForm>
    </div>
  </ContentWrap>
  <ContentWrap class="mt-2">
    <div class="text-center">
      <ElButton @click="handleClose">关闭</ElButton>
      <ElButton :loading="submitLoading" type="primary" @click="handleCommit">确定</ElButton>
    </div>
  </ContentWrap>
</template>
<style lang="less" scoped>
.info-wrapper {
  max-height: calc(100vh - 250px);
  overflow: auto;
}
</style>
