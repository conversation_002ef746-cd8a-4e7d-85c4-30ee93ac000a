<script lang="ts" setup>
import { VersionListAPI } from '@/views/basic-library-manage/api/common'
import { ProductSellingPointPageAPI } from '@/api/productFeatures/types'
import { getProductSellingPointVersionList } from '@/api/productFeatures'

defineOptions({
  name: 'VersionDialog'
})

const props = defineProps<{
  modelValue: boolean
  currentRow?: ProductSellingPointPageAPI.List | null
}>()

const emit = defineEmits<{
  (e: 'view-detail', val: number): void
  (e: 'update:modelValue', val: boolean): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const tableData = ref<VersionListAPI.Data[]>([])
const queryLoading = ref(false)
const getVersionList = async () => {
  if (!props.currentRow) {
    return
  }
  if (!props.currentRow.id) {
    return
  }
  queryLoading.value = true
  const [error, result] = await getProductSellingPointVersionList(props.currentRow.id)
  queryLoading.value = false
  if (error === null && result?.datas) {
    tableData.value = result.datas
  }
}

watch(visible, () => {
  if (visible.value) {
    getVersionList()
  }
})

const handleClose = () => {
  visible.value = false
  tableData.value = []
}

const handleViewDetail = (row: VersionListAPI.Data) => {
  emit('view-detail', row.id)
  handleClose()
}
</script>

<template>
  <Dialog v-model="visible" :before-close="handleClose" title="版本记录">
    <VxeTable :data="tableData" :loading="queryLoading" :max-height="500">
      <VxeColumn field="versionCode" title="版本号" />
      <VxeColumn field="versionType" title="版本变更类型" />
      <VxeColumn field="operator" title="修改者" />
      <VxeColumn field="dataTime" title="修改时间" />
      <VxeColumn field="versionRemark" title="版本说明" />
      <VxeColumn title="修改明细">
        <template #default="{ row }">
          <el-button size="small" text type="primary" @click="handleViewDetail(row)">
            查看详情
          </el-button>
        </template>
      </VxeColumn>
    </VxeTable>
  </Dialog>
</template>

<style lang="less" scoped></style>
