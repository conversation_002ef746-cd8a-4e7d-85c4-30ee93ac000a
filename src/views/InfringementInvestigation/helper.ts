import { type Router } from 'vue-router'
import { formatArrayRules } from '@/utils'

/**
 * @description: 路由名称枚举
 */
export enum RouteNameEnums {
  /**
   * 侵权排查管理
   *
   */
  Index = 'InfringementInvestigation',
  /**
   * 侵权初筛
   */
  IS = 'InitialScreening',
  /**
   * 修改侵权初筛
   */
  ISE = 'InitialScreeningEdit',
  /**
   * 侵权排查初判
   */
  IPJ = 'InvestigationPreliminaryJudgment',
  /**
   * 侵权排查复判
   */
  IR = 'InvestigationReassessment',
  /**
   * 侵权排查详情
   */
  ID = 'InvestigationDetails',
  /**
   * 侵权排查历史详情
   */
  IHD = 'InvestigationHistoryDetails'
}

/**
 * 按钮权限枚举
 * */
export enum ButtonPermissionEnums {
  //列表
  ISE = 'InitialScreeningEdit',
  IPJ = 'InvestigationPreliminaryJudgment',
  IR = 'InvestigationReassessment',

  //详情
  ISEDetail = 'InitialScreeningEditDetail',
  IPJDetail = 'InvestigationPreliminaryJudgmentDetail',
  IRDetail = 'InvestigationReassessmentDetail'
}
/**
 * 暂存标识 1暂存 0正式
 */
export enum TempStorageFlagEnums {
  /**
   * 暂存
   */
  Temp = '1',
  /**
   * 正式
   */
  Formal = '0'
}
/**
 * active 枚举类型
 */
export enum ActiveNameEnums {
  /**
   * 关联产品信息
   */
  Association,
  /**
   * 排查初判
   */
  PreliminaryJudgment,
  /**
   * 排查复判
   */
  Reassessment,
  /**
   * 排查
   */
  AIFringement
}
/**
 * 侵权排查阶段 取值:初判、复判
 */
export enum InfringementStageEnums {
  /**
   * 初判
   */
  PreliminaryJudgment = '初判',
  /**
   * 复判
   */
  Reassessment = '复判'
}

export const getRouterParams = (router: () => Router) => {
  const { currentRoute } = router()
  const routerName = currentRoute.value?.name as RouteNameEnums
  const routerId = currentRoute.value.params?.routerId as unknown as number
  const infringementCode = currentRoute.value.query?.infringementCode as unknown as string
  return { routerName, routerId, infringementCode }
}
export const investigationRules = formatArrayRules([
  ['safeguardRightsStrength', '品牌维权力度'],
  ['infringementStage', '侵权排查阶段'],
  ['infringementRisk', '侵权风险判定'],
  ['searchDate', '检索日期'],
  ['innerControlUserId', '内控复核人']
])
export const infringeStatus = {
  create: 0,
  send: 1,
  finish: 2,
  error: 3
}
