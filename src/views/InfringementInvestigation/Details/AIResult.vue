<script setup lang="ts">
import { ref } from 'vue'
import type { VxeGridInstance, VxeGridProps } from 'vxe-table'
import { InfringementAIListApi } from '@/api/infringementInvestigation/types'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { ElButton, ElPagination } from 'element-plus'
import type { Pager } from '@/views/basic-library-manage/api/common'
import { Icon } from '@/components/Icon'
import { useOmsExport } from '@/hooks/autoImport/useOmsExport'
import { getPatentPage, infringementDetail } from '@/api/infringementInvestigation'
import AiDialog from '@/views/InfringementInvestigation/Details/AiDialog.vue'
import { useTagsViewStore } from '@/store/modules/tagsView'
const pagerRef = ref<InstanceType<typeof ElPagination>>()
const tableRef = ref<VxeGridInstance>()
const queryLoading = ref(false)
const tableData = ref<InfringementAIListApi.Data[]>()
defineOptions({
  name: 'AIResult'
})
const defaultColumns = [
  {
    type: 'seq',
    title: '序号',
    width: 60,
    fixed: 'left'
  },
  {
    title: '来源',
    width: 120,
    field: 'businessTypeName'
  },
  {
    title: '专利图片',
    width: 120,
    field: 'imageUrl',
    cellRender: {
      name: 'Image'
    }
  },
  {
    title: '专利号',
    minWidth: 150,
    field: 'patentNumber',
    cellRender: {
      name: 'Link',
      props: {
        clickFn: ({ row }: { row: InfringementAIListApi.Data }) => handleDetail(row)
      }
    }
  },
  {
    title: '专利标题',
    field: 'title',
    minWidth: 120
  },
  {
    title: '相似度',
    field: 'percent',
    width: 80
  },
  {
    title: '专利受理局',
    field: 'patentCountry',
    minWidth: 120
  },
  {
    title: '申请（专利权）人',
    field: 'currentAssignee',
    minWidth: 150
  },
  {
    title: '专利申请日',
    field: 'applicationDate',
    width: 100
  },
  {
    title: '专利公开日',
    field: 'publicationDate',
    width: 100
  }
]
const pager = ref<Pager>({
  current: 1,
  size: 10,
  total: 0
})
const route = useRoute()
const { push, currentRoute } = useRouter()
const dialogConfig = reactive<{
  visible: boolean
  orderId: string | undefined
  patentId: string | undefined
}>({
  visible: false,
  orderId: '',
  patentId: ''
})
const maxHeight = useTableHeight({
  tableRef,
  pagerRef,
  offsetBottom: 0
})
const id = route.query.id as unknown as string
const type = route.query.type as unknown as string
const routeName = route.query.name as unknown as string
const handleQuery = async () => {
  queryLoading.value = true
  const result = await getPatentPage({ id, type, ...unref(pager) })
  if (result?.datas) {
    const { records } = result.datas
    tableData.value = records || []
    pager.value.total = result.datas?.pager?.total || 0
  }
  queryLoading.value = false
}
const tableOptions = computed<VxeGridProps<InfringementAIListApi.Data>>(() => ({
  minHeight: 100,
  columns: defaultColumns,
  data: tableData.value,
  maxHeight: maxHeight.value - 100,
  showOverflow: 'tooltip',
  scrollX: {
    enabled: true,
    gt: 20
  },
  scrollY: {
    enabled: true,
    gt: 20
  },
  loading: queryLoading.value,
  cellConfig: {
    height: 110
  }
}))
const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
const handleExport = () => {
  let reqParam = JSON.stringify({ id, type })
  exportFn({
    exportType: 'patent-export',
    reqParam
  })
}
//专利号点击
const handleDetail = (row: InfringementAIListApi.Data) => {
  dialogConfig.visible = true
  dialogConfig.orderId = row.orderId
  dialogConfig.patentId = row.patentId
}
const closePage = async () => {
  const { datas } = await infringementDetail({ id })
  console.log(datas, routeName)
  const tagsViewStore = useTagsViewStore()
  tagsViewStore.delView(unref(currentRoute))
  push({
    name: routeName,
    params: { routerId: id },
    query: {
      infringementCode: datas.infringementCode
    }
  })
}
onMounted(handleQuery)
onActivated(handleQuery)
</script>

<template>
  <ContentWrap class="investigation-details">
    <ElRow align="middle"
      ><ElCol :span="12"> 相似度排查结果 </ElCol
      ><ElCol :span="3" :offset="9" style="text-align: right">
        <ElButton :loading="exportLoading" @click="handleExport" class="w-16" type="primary">
          <Icon class="mr-1" icon="ep:upload-filled" />
          导出
        </ElButton>
        <ElButton @click="closePage">关闭</ElButton>
      </ElCol></ElRow
    >
    <div class="table" style="width: 100%">
      <VxeGrid ref="tableRef" v-bind="tableOptions" />
    </div>
    <ElPagination
      ref="pagerRef"
      v-model:current-page="pager.current"
      v-model:page-size="pager.size"
      :total="pager.total"
      background
      class="mt-4"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleQuery"
      @current-change="handleQuery"
    />
  </ContentWrap>
  <AiDialog
    v-model:model-value="dialogConfig.visible"
    :order-id="dialogConfig.orderId"
    :patent-id="dialogConfig.patentId"
  />
</template>

<style scoped lang="less">
.table {
  margin-top: 10px;
}
</style>
