<script setup lang="ts">
import { ButtonPermissionEnums, RouteNameEnums } from '@/views/InfringementInvestigation/helper'
import { ElMessage } from 'element-plus'
import { InfringementPageAPI } from '@/api/infringementInvestigation/types'
import { InfringementEnum } from '@/views/InfringementInvestigation/const'
import { hasPermission } from '@/directives/permission/hasPermi'
const { push } = useRouter()
const props = defineProps<{
  row: InfringementPageAPI.List[]
  permissionButton: {
    ISE: RouteNameEnums | ButtonPermissionEnums
    IPJ: RouteNameEnums | ButtonPermissionEnums
    IR: RouteNameEnums | ButtonPermissionEnums
  }
}>()
const handleRouterPush = (routeName: RouteNameEnums) => {
  if (!props.row?.length || props.row.length > 1) {
    ElMessage.warning('请选择一条数据')
    return
  }
  const { statusCode, id, infringementCode, safeguardRightsStrengthRe } = props.row[0]
  if (statusCode === InfringementEnum.INFRINGEMENT_CANCEL_JUDGMENT) {
    ElMessage.warning('作废的数据不可操作')
    return
  }
  push({
    name: routeName,
    params: { routerId: id },
    query: {
      infringementCode: infringementCode,
      safeguardRightsStrengthRe: safeguardRightsStrengthRe
    }
  })
}

/**
 * 判断显示与否
 */
const judgeDisplay = computed(() => (type: RouteNameEnums | ButtonPermissionEnums) => {
  const hiddenISE: Array<string> = [
    InfringementEnum.INFRINGEMENT_CANCEL_JUDGMENT,
    InfringementEnum.INFRINGEMENT_FIRST_JUDGMENT_SUBMITTED,
    InfringementEnum.INFRINGEMENT_SECOND_JUDGMENT_SUBMITTED,
    InfringementEnum.INFRINGEMENT_FIRST_JUDGMENT,
    InfringementEnum.INFRINGEMENT_SECOND_JUDGMENT
  ]
  const hiddenIPJ: Array<string> = [
    InfringementEnum.INFRINGEMENT_CANCEL_JUDGMENT,
    InfringementEnum.INFRINGEMENT_SECOND_JUDGMENT_SUBMITTED,
    InfringementEnum.INFRINGEMENT_SECOND_JUDGMENT
  ]
  const hiddenIR: Array<string> = [InfringementEnum.INFRINGEMENT_CANCEL_JUDGMENT]
  if (!props.row || props.row.length <= 0) {
    return true
  }
  if (props.row.length > 1) {
    return false
  }
  const { statusCode = '' } = props.row?.[0]
  // 确保 statusCode 是一个有效的 InfringementEnum 类型
  const map = {
    //如果完成：侵权初判or侵权复判，则不显示操作 ||如果侵权排查处于【作废】状态，则不显示操作
    [ButtonPermissionEnums.ISE]: !hiddenISE.includes(statusCode),
    //作废、完成了复判
    [ButtonPermissionEnums.IPJ]: !hiddenIPJ.includes(statusCode),
    [ButtonPermissionEnums.IR]: !hiddenIR.includes(statusCode),
    [ButtonPermissionEnums.ISEDetail]: !hiddenISE.includes(statusCode),
    [ButtonPermissionEnums.IPJDetail]: !hiddenIPJ.includes(statusCode),
    [ButtonPermissionEnums.IRDetail]: !hiddenIR.includes(statusCode)
  }
  return map[type]
})
</script>

<template>
  <div>
    <ElButton
      type="primary"
      v-if="judgeDisplay(permissionButton.ISE) && hasPermission(permissionButton.ISE)"
      @click="handleRouterPush(RouteNameEnums.ISE)"
    >
      <Icon icon="ep:edit" />
      <span class="text-[14px]"> 修改侵权初筛</span>
    </ElButton>
    <ElButton
      type="primary"
      v-if="judgeDisplay(permissionButton.IPJ) && hasPermission(permissionButton.IPJ)"
      @click="handleRouterPush(RouteNameEnums.IPJ)"
    >
      <Icon icon="ep:edit" />
      <span class="text-[14px]"> 侵权初判</span>
    </ElButton>

    <ElButton
      type="primary"
      v-if="judgeDisplay(permissionButton.IR) && hasPermission(permissionButton.IR)"
      @click="handleRouterPush(RouteNameEnums.IR)"
    >
      <Icon icon="ep:edit" />
      <span class="text-[14px]"> 侵权复判</span>
    </ElButton>
  </div>
</template>

<style scoped lang="less"></style>
