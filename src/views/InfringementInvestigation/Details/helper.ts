import { InfringementAIListApi, InfringementDetailAPI } from '@/api/infringementInvestigation/types'

type DescriptionsItem = {
  label: string
  field: keyof InfringementDetailAPI.Data
  span?: number
}
type RelatedProductInfoItem = {
  label: string
  field: keyof InfringementDetailAPI.RefProductResp
}
/**
 * 设计师初步排查信息
 */
export const preliminaryInvestigationInfo: DescriptionsItem[] = [
  { field: 'referenceBrand', label: '参考品牌' },
  { field: 'productDescription', label: '产品描述' },
  { field: 'referenceBrandUrl', label: '参考品牌链接' },
  { field: 'sameShoesBrand', label: '同款鞋对应品牌' },
  { field: 'searchCategoryItemName', label: '检索分类', span: 2 },
  { field: 'riskScreening', label: '风险初筛' },
  { field: 'saleSeason', label: '开发季节' },
  { field: 'marketFlag', label: '产品是否上市' },
  { field: 'needHelpFlag', label: '设计师是否需协助' },
  { field: 'productNumber', label: '关联的产品编号' },
  { field: 'searchNeed', label: '检索需求' },
  { field: 'designerName', label: '设计师' },
  { field: 'screeningOperateTime', label: '创建时间' }
]

/**
 * 关联的产品信息
 */
export const relatedProductInfo: RelatedProductInfoItem[] = [
  { field: 'productNumber', label: '产品编号' },
  { field: 'launchSeasonItemName', label: '开发季节' },
  { field: 'brandItemName', label: '品牌' },
  { field: 'designerIdItemName', label: '设计师' },
  { field: 'productPositioningItemName', label: '产品定级' },
  { field: 'productCategoryItemName', label: '产品类目' },
  { field: 'targetAudienceItemName', label: '适用人群' },
  { field: 'meetingResultItemName', label: '选品会结果' },
  { field: 'dataStatusItemName', label: '状态' },
  { field: 'modifyByIdItemName', label: '操作人' },
  { field: 'modifyTime', label: '操作时间' }
]
/**
 * 侵权排查信息-初判
 */
export const infringeInvestPrelimJudge: DescriptionsItem[] = [
  { field: 'infringementStagePre', label: '侵权排查阶段' },
  { field: 'searchDatePre', label: '检索日期' },
  { field: 'safeguardRightsStrengthPre', label: '品牌维权力度' },
  { field: 'innerControlUserNamePre', label: '内控复核人' },
  { field: 'otherRiskCountry', label: '其他风险国家' },
  { field: 'suggestionInstructionPre', label: '规避建议/风险情况说明' },
  { field: 'sendErpTimePre', label: 'ERP下发时间' },
  { field: 'linkInfringementCodePre', label: '关联侵权排查需求' }
]
/**
 * 侵权排查信息-复判
 */
export const infringeInvestRejudge: DescriptionsItem[] = [
  { field: 'infringementStageRe', label: '侵权排查阶段' },
  { field: 'searchDateRe', label: '检索日期' },
  { field: 'safeguardRightsStrengthRe', label: '品牌维权力度' },
  { field: 'innerControlUserNameRe', label: '内控复核人' },
  { field: 'otherRiskCountry', label: '其他风险国家' },
  { field: 'sendErpTimeRe', label: 'ERP下发时间' },
  { field: 'suggestionInstructionRe', label: '规避建议/风险情况说明' },
  { field: 'linkInfringementCodeRe', label: '关联侵权排查需求' }
]
type DescriptionsAIItem = {
  label: string
  field: keyof InfringementAIListApi.Data
  span?: number
}
export const AIDescription: DescriptionsAIItem[] = [
  { field: 'patentUrl', label: '专利图片' },
  { field: 'patentNumber', label: '专利号' },
  { field: 'currentAssignee', label: '申请(专利)人' },
  { field: 'percent', label: '相似度' },
  { field: 'applicationDate', label: '专利申请日' },
  { field: 'publicationDate', label: '专利公开日' },
  { field: 'estimatedDate', label: '预估到期日' },
  { field: 'legalStatus', label: '专利法律状态' },
  { field: 'locTypeName', label: '洛迦诺分类（LOC）' },
  { field: 'allImageUrls', label: '附图', span: 24 }
]
