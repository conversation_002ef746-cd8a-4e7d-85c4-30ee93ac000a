<script setup lang="ts">
import { InfringementAIListApi } from '@/api/infringementInvestigation/types'
import { getPatentDetail } from '@/api/infringementInvestigation'
import { DescriptionsSchema } from '@/types/descriptions'
import { AIDescription } from '@/views/InfringementInvestigation/Details/helper'

defineOptions({
  name: 'AiDialog'
})
const props = defineProps<{
  modelValue: boolean
  orderId: string | undefined
  patentId: string | undefined
}>()
const detailsData = ref<InfringementAIListApi.Data>()
const loading = ref<boolean>(false)
watch(
  () => props.modelValue,
  () => {
    if (props.modelValue) {
      getDatail()
    }
  }
)
const getDatail = async () => {
  if (!props.patentId || !props.orderId) {
    return false
  }
  loading.value = true
  const [error, result] = await getPatentDetail({
    patentId: props.patentId,
    orderId: props.orderId
  })
  if (error === null && result?.datas) {
    detailsData.value = result?.datas
  }
  loading.value = false
}
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
}>()
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
</script>

<template>
  <Dialog v-model="visible" title="专利详情" top="5vh" :parent-scroll="false">
    <div v-loading="loading">
      <Descriptions
        label-width="8rem"
        :data="detailsData"
        :schema="AIDescription as DescriptionsSchema[]"
      >
        <template #patentUrl>
          <ElImage
            :preview-src-list="[detailsData?.patentUrl!]"
            :src="detailsData?.patentUrl"
            class="!h-[80px]"
            hide-on-click-modal
            loading="lazy"
          />
        </template>
        <template #allImageUrls>
          <div
            v-for="(item, index) in detailsData?.allImageUrls"
            :key="index"
            style="display: inline-block; margin-right: 10px"
          >
            <ElImage
              :preview-src-list="detailsData?.allImageUrls"
              :src="item"
              class="!h-[80px]"
              hide-on-click-modal
              loading="lazy"
            />
          </div>
        </template>
      </Descriptions>
    </div>
  </Dialog>
</template>

<style scoped lang="less"></style>
