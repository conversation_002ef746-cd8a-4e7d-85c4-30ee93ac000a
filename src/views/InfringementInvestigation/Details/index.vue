<script lang="ts" setup>
import { infringementDetail, infringementVersionDetail } from '@/api/infringementInvestigation'
import { InfringementDetailAPI } from '@/api/infringementInvestigation/types'
import SearchDetailList from '../Components/SearchDetailList.vue'
import {
  infringeInvestPrelimJudge,
  infringeInvestRejudge,
  preliminaryInvestigationInfo,
  relatedProductInfo
} from './helper'
import { ActiveNameEnums, ButtonPermissionEnums } from '../helper'
import { isUndefined, toNumber } from 'lodash-es'
import { InfringementRiskLevelEnums } from '@/enums'
import Operate from '@/views/InfringementInvestigation/Details/operate.vue'
const props = defineProps<{
  id?: number
}>()

const { currentRoute } = useRouter()
const versionId = toNumber(currentRoute.value.params.versionId)
const routerId = toNumber(currentRoute.value.params.routerId)
const detailsData = ref<InfringementDetailAPI.Data>({
  id: currentRoute.value.params.id
})
const activeNames = ref<ActiveNameEnums[]>([
  ActiveNameEnums.PreliminaryJudgment,
  ActiveNameEnums.AIFringement
])

const _infringementDetail = async () => {
  if (versionId) {
    const { datas } = await infringementVersionDetail(versionId)
    detailsData.value = datas
    return
  }
  const { datas } = await infringementDetail({ id: props.id || routerId })
  detailsData.value = datas
}
const handleFetch = () => {
  if (routerId || props.id || versionId) {
    _infringementDetail()
  }
}
const handleClose = () => {
  useClosePage('InfringementInvestigation', {
    state: {
      infringementCode: detailsData.value?.infringementCode
    }
  })
}

onMounted(handleFetch)
onActivated(handleFetch)
</script>

<template>
  <ContentWrap class="investigation-details">
    <ElCarousel v-if="isUndefined(props.id)" :autoplay="false" arrow="always" height="400px">
      <ElCarouselItem v-for="item in detailsData?.allImgList" :key="item.signatureUrl">
        <h2 class="text-center font-semibold">{{ item.fileType }}</h2>
        <ElImage
          :preview-src-list="[item.signatureUrl!]"
          :src="item.signatureUrl"
          hide-on-click-modal
          class="card-img w-full h-full"
          fit="contain"
          loading="lazy"
          preview-teleported
        />
      </ElCarouselItem>
    </ElCarousel>

    <ElDivider content-position="left">设计师初步排查信息</ElDivider>
    <Descriptions
      :data="detailsData"
      :schema="preliminaryInvestigationInfo"
      class="descriptions-details-layout__2"
    >
      <template #infringementRiskPre="{ row }: { row: InfringementDetailAPI.Data }">
        <span
          :class="[row.riskScreening === InfringementRiskLevelEnums.High ? 'text-red-500' : null]"
        >
          {{ row.riskScreening }}
        </span>
      </template>
    </Descriptions>

    <ElCollapse v-model="activeNames" accordion class="mt-5">
      <ElCollapseItem :name="ActiveNameEnums.Association" title="关联的产品信息">
        <Descriptions
          :data="detailsData?.refProductResp"
          :schema="relatedProductInfo"
          class="descriptions-details-layout__2"
        />
      </ElCollapseItem>
      <!--      <ElCollapseItem :name="ActiveNameEnums.AIFringement" title="智能侵权排查">-->
      <!--        <AIInfringement />-->
      <!--      </ElCollapseItem>-->
      <ElCollapseItem :name="ActiveNameEnums.PreliminaryJudgment" title="侵权排查信息-初判">
        <Descriptions
          :data="detailsData"
          :schema="infringeInvestPrelimJudge"
          class="descriptions-details-layout__2"
        >
          <template #infringementRiskPre="{ row }: { row: InfringementDetailAPI.Data }">
            <span
              :class="[
                row.infringementRiskPre === InfringementRiskLevelEnums.High ? 'text-red-500' : null
              ]"
            >
              {{ row.infringementRiskPre }}
            </span>
          </template>
        </Descriptions>
        <SearchDetailList
          :list="detailsData?.searchDetailPreList || []"
          :risk-list="detailsData?.infringementDetailPreList"
        />
      </ElCollapseItem>

      <ElCollapseItem :name="ActiveNameEnums.Reassessment" title="侵权排查信息-复判">
        <Descriptions
          :data="detailsData"
          :schema="infringeInvestRejudge"
          class="descriptions-details-layout__2"
        >
          <template #infringementRiskRe="{ row }: { row: InfringementDetailAPI.Data }">
            <span
              :class="[
                row.infringementRiskRe === InfringementRiskLevelEnums.High ? 'text-red-500' : null
              ]"
            >
              {{ row.infringementRiskRe }}
            </span>
          </template>
        </Descriptions>
        <SearchDetailList
          :list="detailsData?.searchDetailReList || []"
          :risk-list="detailsData?.infringementDetailReList"
        />
      </ElCollapseItem>
    </ElCollapse>
    <div class="button-container">
      <ElButton @click="handleClose" class="mr-3">返回</ElButton>
      <operate
        :row="[detailsData]"
        :permissionButton="{
          ISE: ButtonPermissionEnums.ISEDetail,
          IPJ: ButtonPermissionEnums.IPJDetail,
          IR: ButtonPermissionEnums.IRDetail
        }"
      />
    </div>
  </ContentWrap>
</template>

<style lang="less" scoped>
.investigation-details :deep(.el-collapse-item__header) {
  font-size: 14px;
}

.button-container {
  display: flex;
  width: 100%;
  margin: 20px;
  justify-content: center;
}
</style>
