<script setup lang="ts">
import * as echarts from 'echarts'
import { Echart } from '@/components/Echart'
import { getPatentDataScreen, reSearchPatentInfo } from '@/api/infringementInvestigation'
import { getRouterParams, infringeStatus } from '@/views/InfringementInvestigation/helper'
defineOptions({
  name: 'AIInfringement'
})
const echartRef = ref<InstanceType<typeof Echart>>()
const status = ref<number>(0)
const loading = ref<boolean>(false)
const similarContent = ref()
const errorMessage = ref()
const router = useRouter()
const { routerId, routerName } = getRouterParams(useRouter)
const queryContent = async () => {
  echartRef.value?.echartRef?.showLoading()
  const { code, datas } = await getPatentDataScreen({ id: routerId })
  if (!code) {
    return false
  }
  const config = {
    eightyPercentCount: {
      color: 'rgb(236,129,140)',
      name: '相似度100-80%',
      type: 0
    },
    fiftyPercentCount: {
      color: 'rgb(245,155, 38)',
      name: '相似度80-50%',
      type: 1
    },
    underFiftyPercentCount: {
      color: 'rgb(171,171,169)',
      name: '相似度50-0%',
      type: 2
    }
  }
  if (datas.status === infringeStatus.error) {
    errorMessage.value = datas.errorMessage
  }
  status.value = datas.status
  similarContent.value = Object.keys(config).map((key) => {
    const value = datas?.[key] || 0
    return {
      value,
      name: config[key]?.name,
      type: config[key].type,
      itemStyle: {
        color: config[key]?.color
      }
    }
  })
  echartRef.value?.echartRef?.hideLoading()
}
const AIInfringementOptions = computed<echarts.EChartsOption>(() => ({
  title: {
    show: true,
    text: '相似度',
    top: 'middle',
    left: 'center',
    textStyle: {
      color: 'black'
    }
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: false
        }
      },
      labelLine: {
        show: false
      },
      data: similarContent.value
    }
  ]
}))
const svg = `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `
const gotoDetail = (item: { value: number; name: string; type: string }) => {
  if (item.value <= 0) {
    return false
  }
  router.push({
    name: 'AIResult',
    query: {
      id: routerId,
      type: item.type,
      name: routerName
    }
  })
}
//重新排查
const reLoad = async () => {
  loading.value = true
  const { code } = await reSearchPatentInfo({ id: routerId })
  loading.value = false
  if (!code) {
    return false
  }
  status.value = infringeStatus.create
}
onBeforeMount(queryContent)
onActivated(queryContent)
</script>

<template>
  <el-row :gutter="24">
    <el-col :span="8">
      <Echart ref="echartRef" :height="250" :options="AIInfringementOptions" />
    </el-col>
    <el-col :span="14">
      <div
        v-loading="status == infringeStatus.create || status == infringeStatus.send"
        :element-loading-svg="svg"
        element-loading-text="智能侵权排查中，请稍后"
        class="custom-loading-svg"
        element-loading-custom-class="loading-class"
        element-loading-svg-view-box="-10, -10, 60, 60"
      >
        <div class="flex flex-ai">
          <div :class="`color-` + index" v-for="(item, index) in similarContent" :key="index">
            <p><span></span>{{ item.name }}</p>
            <p
              :class="[item.value !== 0 ? 'click' : '']"
              v-if="status == infringeStatus.finish || !status"
              @click="gotoDetail(item)"
              ><i :class="item.value == 0 ? 'zero' : ''">{{ item.value }}</i
              >个专利</p
            >
          </div>
        </div>
        <div class="ai-error" v-if="status == infringeStatus.error">
          <p>智能侵权排查异常，请点击重新排查</p>
          <p v-if="errorMessage" class="error">{{ errorMessage }}</p>
          <p><el-button type="primary" :loading="loading" @click="reLoad">重新排查</el-button></p>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<style scoped lang="less">
.el-loading-parent--relative {
  height: 100%;

  :deep(.el-loading-text) {
    @keyframes el-loading-text {
      0% {
        content: '.';
      }

      30% {
        content: '. . ';
      }

      60% {
        content: '. . . .';
      }

      90% {
        content: '. . . . .';
      }
    }

    &:after {
      font-size: 14px;
      content: '.';
      animation: el-loading-text 2s infinite linear;
    }
  }
}

.flex-ai {
  justify-content: space-between;

  i {
    font-size: 34px;
    font-style: normal;
    font-weight: bold;
  }

  > div {
    p {
      margin-top: 36px;

      &:first-child {
        font-size: 16px;

        span {
          display: inline-block;
          width: 20px;
          height: 20px;
          margin-right: 5px;
          vertical-align: middle;
        }
      }

      &:last-child {
        padding-left: 18px;
        margin-top: 29%;
        font-size: 14px;
        color: black;

        &.click {
          cursor: pointer;

          i {
            color: black;
            text-decoration: underline;
          }

          &:hover {
            color: var(--el-color-primary);

            i {
              color: var(--el-color-primary);
            }
          }
        }

        i {
          margin-right: 8px;
          font-weight: normal;
          color: #00000096;
        }
      }
    }
  }

  .color-0 {
    span {
      background-color: rgb(236, 129, 140);
    }
  }

  .color-1 {
    span {
      background-color: rgb(245, 155, 38);
    }
  }

  .color-2 {
    span {
      background-color: rgb(171, 171, 169);
    }
  }
}

.ai-error {
  padding: 25px 0 20px 0;
  text-align: center;

  .error {
    margin-bottom: 4px;
    color: red;
  }

  p {
    &:first-child {
      margin-bottom: 5px;
      font-size: 14px;
    }
  }
}
</style>
