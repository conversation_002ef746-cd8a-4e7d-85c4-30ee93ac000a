<script lang="ts" setup>
import {
  infringementDetail,
  infringementPreAdd,
  infringementPreDetail,
  infringementPreUpdate,
  infringementReAdd,
  infringementReDetail,
  infringementReUpdate
} from '@/api/infringementInvestigation'
import { ElMessage, type FormInstance, FormRules } from 'element-plus'
import {
  InfringementDetailAPI,
  InfringementJudgmentDetailQueryResp,
  InfringementPageAPI,
  InfringementPreAddAPI,
  InfringementSearchAddReq
} from '@/api/infringementInvestigation/types'
import {
  ActiveNameEnums,
  getRouterParams,
  InfringementStageEnums,
  investigationRules,
  RouteNameEnums,
  TempStorageFlagEnums
} from '../helper'
import { preliminaryInvestigationInfo, relatedProductInfo } from '../Details/helper'
import dayjs from 'dayjs'
import { Icon } from '@/components/Icon'
import InfringementInvestigationDialog from '@/views/basic-library-manage/components/InfringementInvestigationDialog.vue'
import { InfringementRiskEnums } from '@/enums'
import { getBanPlatformList } from '@/components/Business/SelectPlus/src/api'
// import AIInfringement from '@/views/InfringementInvestigation/Investigation/AIInfringement.vue'

defineOptions({
  name: 'InvestigationPreliminaryJudgment'
})

/** 是否是进行编辑操作 */
const isEdit = ref(false)
const detailsData = ref<InfringementDetailAPI.Data>()
const activeNames = ref<ActiveNameEnums[]>([
  ActiveNameEnums.PreliminaryJudgment,
  ActiveNameEnums.AIFringement
])
const activeLoading = ref(false)
const { routerName, routerId, infringementCode } = getRouterParams(useRouter)

const formData = reactive<InfringementPreAddAPI.Params>({
  searchDate: dayjs().format('YYYY-MM-DD'),
  infringementDetailList: [],
  searchAddReqList: []
})
const formRef = ref<FormInstance>()
const formRules = computed<FormRules<InfringementPreAddAPI.Params>>(() => ({
  ...investigationRules,
  riskCountry: [
    {
      required:
        formData.infringementRisk === InfringementRiskEnums.H ||
        formData.infringementRisk === InfringementRiskEnums.M,
      message: '请完善风险国家',
      trigger: 'change'
    }
  ]
}))

/**
 * 计算属性，用于检查搜索添加列表的有效性
 * @returns {boolean} 如果搜索添加列表不为空且每个项都有搜索方式和搜索记录说明，则返回true，否则返回false
 */
const searchAddListValid = computed(() => {
  return formData.searchAddReqList && formData.searchAddReqList.length > 0
    ? formData.searchAddReqList.every((item) => item.searchWay && item.searchRecordInstruction)
    : false
})

const riskValid = computed(() => {
  return formData.infringementDetailList?.every((row) => {
    if (
      row.infringementRisk === InfringementRiskEnums.H ||
      row.infringementRisk === InfringementRiskEnums.M
    ) {
      return !!row.riskCountry
    }
    return !!row.infringementRisk
  })
})

const banPlatformList = ref<string[]>([])
const fetchBanPlatformList = async () => {
  const { datas } = await getBanPlatformList()
  banPlatformList.value = datas
}
fetchBanPlatformList()

/**
 * 获取侵权详细信息。
 */
const fetchInfringementDetail = async () => {
  const { datas } = await infringementDetail({ id: routerId })
  detailsData.value = datas
  formData.infringementStage =
    routerName === RouteNameEnums.IPJ
      ? InfringementStageEnums.PreliminaryJudgment
      : InfringementStageEnums.Reassessment
}

/**
 * 获取初判或复审详情。
 * @param {boolean} isReassessment - 是否为复审阶段。
 */
const fetchDetailData = async (isReassessment: boolean) => {
  // 复审阶段是否没数据
  let reassessmentNotData = false
  const detailFunction = isReassessment ? infringementReDetail : infringementPreDetail
  let { datas } = await detailFunction({ code: infringementCode })

  // 如果是复审阶段且没有初判详情，则获取初判详情
  if (!datas && isReassessment) {
    datas = (await infringementPreDetail({ code: infringementCode })).datas || {}
    datas.infringementStage = InfringementStageEnums.Reassessment
    reassessmentNotData = true
  }

  if (datas && Object.keys(datas).length !== 0) {
    Object.assign(formData, datas)
    // 复审阶段没数据时为新增操作，否则为编辑操作
    isEdit.value = !reassessmentNotData
  }
}

/**
 * 处理侵权预添加或更新操作。
 * @param {boolean} isReassessment - 是否为复审阶段。
 */
const handleInfringementAction = async (isReassessment: boolean) => {
  try {
    activeLoading.value = true
    const action = isReassessment
      ? unref(isEdit)
        ? infringementReUpdate
        : infringementReAdd
      : unref(isEdit)
      ? infringementPreUpdate
      : infringementPreAdd

    const { msg } = await action(formData)
    ElMessage.success(msg)
    useClosePage(RouteNameEnums.Index, {
      state: {
        infringementCode
      }
    })
  } finally {
    activeLoading.value = false
  }
}

// 同一个国家不能出现不同的风险等级
const checkInfringementRiskConsistency = (items: InfringementJudgmentDetailQueryResp[]) => {
  const groupedByCountry = (items || []).reduce((acc, item) => {
    const { riskCountry, infringementRisk } = item
    if (!acc[riskCountry as string]) {
      acc[riskCountry as string] = new Set()
    }
    acc[riskCountry as string].add(infringementRisk)

    return acc
  }, {})
  return Object.values(groupedByCountry).every((risks) => risks.size === 1)
}

/**
 * 存储操作。
 * @param {TempStorageFlagEnums} type - 存储类型。
 */
const handleTemporaryStorage = (type: TempStorageFlagEnums) => {
  formRef.value?.validate((valid) => {
    if (valid) {
      if (!searchAddListValid.value) {
        ElMessage.warning('请完善检索详情')
        return
      }
      if (!riskValid.value) {
        ElMessage.warning('请完善风险判定')
        return
      }
      if (
        !checkInfringementRiskConsistency(
          formData.infringementDetailList as InfringementJudgmentDetailQueryResp[]
        )
      ) {
        ElMessage.warning('同一个国家不能出现不同的风险等级')
        return
      }
      formData.tempStorageFlag = type
      handleInfringementAction(routerName === RouteNameEnums.IR)
    }
  })
}

/**
 * 添加新的搜索请求列表项。
 */
const handleAddReqList = () => {
  formData.searchAddReqList = [...formData.searchAddReqList!, {}]
}
/**
 * 删除指定索引的搜索请求列表项。
 * @param {number} index - 要删除的项的索引。
 */
const handleDelReqList = (index: number) => {
  formData.searchAddReqList = formData.searchAddReqList?.filter((_, i) => i !== index)
}

const handleAddRiskCountry = () => {
  formData.infringementDetailList = [...formData.infringementDetailList!, {}]
}

const handleDelRiskCountry = (index: number) => {
  formData.infringementDetailList = formData.infringementDetailList?.filter((_, i) => i !== index)
}

const handleRiskChange = (val: string, row: InfringementJudgmentDetailQueryResp) => {
  if (val === InfringementRiskEnums.H || val === InfringementRiskEnums.M) {
    row.prohibitedPlatformList = banPlatformList.value
  } else {
    row.prohibitedPlatformList = []
  }
}

const copyrightInspectionVisible = ref(false)
const handleOpenInfringementDialog = () => {
  copyrightInspectionVisible.value = true
}
const handlePickCopyright = (val: InfringementPageAPI.List) => {
  formData.linkInfringementCode = val.infringementCode
}

/**
 * 组件挂载时执行的初始化操作。
 */
const handleFetch = () => {
  formData.infringementCode = infringementCode
  fetchInfringementDetail()
  fetchDetailData(routerName === RouteNameEnums.IR)
}
const handleClose = () => {
  useClosePage('InfringementInvestigation', {
    state: {
      infringementCode
    }
  })
}
onMounted(handleFetch)
onActivated(handleFetch)
</script>

<template>
  <InfringementInvestigationDialog
    v-model="copyrightInspectionVisible"
    embed-type="copyrightInspection"
    @submit="handlePickCopyright"
  />
  <ContentWrap class="investigation-details">
    <ElCarousel height="400px">
      <ElCarouselItem v-for="item in detailsData?.allImgList" :key="item.signatureUrl">
        <h2 class="text-center font-semibold">{{ item.fileType }}</h2>
        <ElImage
          :preview-src-list="[item.signatureUrl!]"
          :src="item.signatureUrl"
          hide-on-click-modal
          class="card-img w-full h-full"
          fit="contain"
          loading="lazy"
          preview-teleported
        />
      </ElCarouselItem>
    </ElCarousel>

    <ElDivider content-position="left">设计师初步排查信息</ElDivider>
    <Descriptions
      :data="detailsData"
      :schema="preliminaryInvestigationInfo"
      class="descriptions-details-layout__2"
    />

    <ElCollapse v-model="activeNames" accordion class="mt-5">
      <ElCollapseItem :name="ActiveNameEnums.Association" title="关联的产品信息">
        <Descriptions
          :data="detailsData?.refProductResp"
          :schema="relatedProductInfo"
          class="descriptions-details-layout__2"
        />
      </ElCollapseItem>
      <!--      <ElCollapseItem :name="ActiveNameEnums.AIFringement" title="智能侵权排查">-->
      <!--        <AIInfringement />-->
      <!--      </ElCollapseItem>-->
      <ElCollapseItem :name="ActiveNameEnums.PreliminaryJudgment" title="侵权排查信息">
        <ElForm
          ref="formRef"
          :model="formData"
          :rules="formRules"
          class="form-layout--2"
          inline
          label-width="12em"
        >
          <ElFormItem label="品牌维权力度" prop="safeguardRightsStrength">
            <SelectPlus
              v-model="formData.safeguardRightsStrength"
              api-key="RIGHTS_PROTECTION"
              cache
            />
          </ElFormItem>
          <ElFormItem label="侵权排查阶段" prop="infringementStage">
            <SelectPlus
              v-model="formData.infringementStage"
              api-key="INFRINGEMENT_PHASE"
              cache
              disabled
            />
          </ElFormItem>
          <ElFormItem label="其他风险国家">
            <ElInput
              v-model="formData.otherRiskCountry"
              :autosize="{ minRows: 4, maxRows: 4 }"
              maxlength="200"
              placeholder="请输入"
              show-word-limit
              type="textarea"
            />
          </ElFormItem>
          <ElFormItem label="关联侵权排查需求" prop="linkInfringementCode">
            <ElInput v-model="formData.linkInfringementCode" readonly>
              <template #suffix>
                <Icon
                  :size="26"
                  class="cursor-pointer"
                  color="#409EFF"
                  icon="mdi:search"
                  @click="handleOpenInfringementDialog"
                />
              </template>
            </ElInput>
          </ElFormItem>
          <ElFormItem label="检索日期" prop="searchDate">
            <ElDatePicker v-model="formData.searchDate" placeholder="请选择" />
          </ElFormItem>
          <ElFormItem label="内控复核人" prop="innerControlUserId">
            <CascadeSelector
              v-model="formData.innerControlUserId"
              :props="{ emitPath: false }"
              api-key="allUsers"
              cache
              collapse-tags
              collapse-tags-tooltip
            />
          </ElFormItem>
          <ElFormItem label="规避建议/风险情况说明">
            <ElInput
              v-model="formData.suggestionInstruction"
              :autosize="{ minRows: 4, maxRows: 4 }"
              maxlength="500"
              placeholder="请输入"
              show-word-limit
              type="textarea"
            />
          </ElFormItem>
        </ElForm>
        <ElDivider content-position="left">风险判定</ElDivider>
        <VxeToolbar>
          <template #buttons>
            <ElButton text type="primary" @click="handleAddRiskCountry">添加风险国家</ElButton>
          </template>
        </VxeToolbar>
        <VxeTable
          :cell-config="{ height: 80 }"
          :data="formData.infringementDetailList"
          :show-overflow="false"
        >
          <VxeColumn title="序号" type="seq" width="60" />
          <VxeColumn field="infringementRisk" title="侵权风险判定">
            <template #header> <span class="text-red-500">*</span>侵权风险判定 </template>
            <template #default="{ row }: { row: InfringementJudgmentDetailQueryResp }">
              <SelectPlus
                v-model="row.infringementRisk"
                api-key="INFRINGEMENT_RISK"
                cache
                @change="(val: string) => handleRiskChange(val, row)"
              />
            </template>
          </VxeColumn>
          <VxeColumn field="riskCountry" title="风险国家">
            <template #header> 风险国家 </template>
            <template #default="{ row }: { row: InfringementJudgmentDetailQueryResp }">
              <SelectPlus v-model="row.riskCountry" api-key="getRiskCountryList" cache filterable />
            </template>
          </VxeColumn>
          <VxeColumn field="prohibitedPlatformList" title="禁售平台">
            <template #default="{ row }: { row: InfringementJudgmentDetailQueryResp }">
              <ElSelect
                v-model="row.prohibitedPlatformList"
                collapse-tags
                collapse-tags-tooltip
                filterable
                multiple
              >
                <ElOption v-for="item in banPlatformList" :key="item" :label="item" :value="item" />
              </ElSelect>
            </template>
          </VxeColumn>
          <VxeColumn :show-overflow="false" title="操作">
            <template #default="{ rowIndex }">
              <ElButton text type="primary" @click="handleDelRiskCountry(rowIndex)">删除</ElButton>
            </template>
          </VxeColumn>
        </VxeTable>
        <ElDivider content-position="left">检索详情</ElDivider>
        <VxeToolbar>
          <template #buttons>
            <ElButton text type="primary" @click="handleAddReqList">添加检索详情</ElButton>
          </template>
        </VxeToolbar>
        <VxeTable
          :cell-config="{ height: undefined }"
          :data="formData.searchAddReqList"
          :show-overflow="false"
        >
          <VxeColumn title="序号" type="seq" width="60" />
          <VxeColumn field="searchWay" title="检索途径">
            <template #header>
              <span class="text-red-500">* </span>
              <span>检索途径</span>
            </template>
            <template #default="{ row }: { row: InfringementSearchAddReq }">
              <SelectPlus v-model="row.searchWay" api-key="INFRINGEMENT_SEARCH_WAY" cache />
            </template>
          </VxeColumn>
          <VxeColumn field="InfringementSearchAddReq" title="检索记录说明">
            <template #header>
              <span class="text-red-500">* </span>
              <span>检索记录说明</span>
            </template>
            <template #default="{ row }: { row: InfringementSearchAddReq }">
              <ElInput
                v-model="row.searchRecordInstruction"
                :autosize="{ minRows: 4, maxRows: 10 }"
                maxlength="2000"
                placeholder="请输入"
                type="textarea"
              />
            </template>
          </VxeColumn>
          <VxeColumn field="imgList" title="检索图片">
            <template #default="{ row }: { row: InfringementSearchAddReq }">
              <OssUpload
                v-model="row.imgList"
                :limit="20"
                :size-limit="1024 * 1024 * 100"
                accept="image/*"
                listType="text"
                multiple
                tableImage
              />
            </template>
          </VxeColumn>
          <VxeColumn field="fileList" title="相关附件">
            <template #default="{ row }: { row: InfringementSearchAddReq }">
              <OssUpload
                v-model="row.fileList"
                :limit="10"
                :size-limit="1024 * 1024 * 200"
                drag
                listType="text"
                multiple
              />
            </template>
          </VxeColumn>
          <VxeColumn :show-overflow="false" title="操作">
            <template #default="{ rowIndex }">
              <ElButton text type="primary" @click="handleDelReqList(rowIndex)">删除</ElButton>
            </template>
          </VxeColumn>
        </VxeTable>
      </ElCollapseItem>
    </ElCollapse>

    <FooterAction>
      <ElButton @click="handleClose">返回</ElButton>
      <ElButton
        :loading="activeLoading"
        type="primary"
        @click="handleTemporaryStorage(TempStorageFlagEnums.Formal)"
      >
        确定
      </ElButton>
    </FooterAction>
  </ContentWrap>
</template>

<style lang="less" scoped>
.investigation-details :deep(.el-collapse-item__header) {
  font-size: 14px;
}
</style>
