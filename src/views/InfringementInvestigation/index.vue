<script lang="ts" setup>
import { infringementPage, voidInfringement } from '@/api/infringementInvestigation'
import { InfringementPageAPI } from '@/api/infringementInvestigation/types'
import { ButtonPermissionEnums, RouteNameEnums } from './helper'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import VersionDialog from './Components/VersionDialog.vue'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import { ref } from 'vue'
import Operate from '@/views/InfringementInvestigation/Details/operate.vue'

defineOptions({
  name: 'InfringementInvestigation'
})

const props = withDefaults(
  defineProps<{
    isEmbed?: boolean
    embedType?: string
  }>(),
  {
    isEmbed: false
  }
)
const { push } = useRouter()
const { query } = useRoute()
const formData = reactive<InfringementPageAPI.Params & { date: [string, string] }>({
  relatedProduct: !props.isEmbed,
  brandIdList: [],
  date: ['', ''],
  createByIdList: [],
  modifyByIdList: [],
  emptyProductFlag: props.isEmbed && props.embedType === 'product' ? 1 : undefined,
  tempStorageFlag: props.isEmbed ? '0' : undefined,
  delFlag: props.isEmbed ? '0' : undefined
})
const {
  tableData,
  loading,
  searchUpdate,
  pager,
  pagerUpdate,
  refTable,
  refRegister,
  rules,
  pageParams
} = usePage({
  api: infringementPage,
  formData,
  dateFormat: [
    {
      modelValue: 'date',
      format: ['startDate', 'endDate']
    }
  ]
})

const { handleExport: exportFn, loading: exportLoading } = useOmsExport()
const handleExport = () => {
  const selected: InfringementPageAPI.List[] | undefined = refTable.value?.getCheckboxRecords()
  let reqParam: string
  if (selected && selected?.length > 0) {
    const idList = selected.map((item) => item.id)
    reqParam = JSON.stringify({ idList })
  } else {
    reqParam = JSON.stringify(pageParams.value)
  }
  exportFn({
    exportType: 'infringemnet-export',
    reqParam
  })
}

const maxHeight = useTableHeight({
  tableRef: refTable,
  offsetBottom: props.isEmbed ? 50 + 20 + 32 + 10 + 30 + 50 : 50
})

const handleRouterPush = (routeName: RouteNameEnums) => {
  push({
    name: routeName
  })
}

// 版本记录
const currentRow = ref<InfringementPageAPI.List>()
const versionDialogVisible = ref(false)
const handleOpenVersionDialog = (row: InfringementPageAPI.List) => {
  currentRow.value = row
  versionDialogVisible.value = true
}

const handleVoid = (row: InfringementPageAPI.List) => {
  ElMessageBox.prompt('请填写作废理由', '提示', {
    inputPattern: /^(?!\s*$).{1,500}$/,
    inputErrorMessage: '请填写作废理由，不超过500字符',
    beforeClose: async (action, instance, done) => {
      if (action === 'confirm') {
        instance.cancelButtonLoading = instance.confirmButtonLoading = true
        const { msg } = await voidInfringement({
          code: row.infringementCode,
          delReason: instance.inputValue
        }).finally(() => {
          instance.cancelButtonLoading = instance.confirmButtonLoading = false
        })
        ElMessage.success(msg)
        done()
        searchUpdate()
      } else {
        done()
      }
    }
  }).catch(() => {})
}

const onMountedReq = () => {
  const { infringementCode } = query
  if (infringementCode && typeof infringementCode === 'string') {
    formData.infringementCode = infringementCode
  } else {
    const params = history.state
    if (params?.infringementCode) {
      formData.infringementCode = params.infringementCode
    }
  }
  searchUpdate()
}
const getTableCheckRow = computed(() => refTable.value?.getCheckboxRecords())

onMounted(onMountedReq)
onActivated(onMountedReq)

const { formLabelLength } = useLocaleConfig([
  {
    formLabelLength: '100px'
  },
  {
    formLabelLength: '240px'
  }
])

defineExpose({
  refTable
})
</script>
<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_180px)] overflow-x-hidden overflow-y-auto">
        <SearchForm
          v-model="formData"
          :export="!isEmbed"
          :export-loading="exportLoading"
          :handle-export="handleExport"
          :label-width="formLabelLength"
          :loading="loading"
          :rules="rules"
          @update="searchUpdate"
        >
          <ElFormItem label="侵权记录编号">
            <ElInput
              v-model="formData.infringementCode"
              clearable
              placeholder="请输入"
              @change="searchUpdate()"
            />
          </ElFormItem>
          <ElFormItem label="产品编号">
            <SelectPlus
              v-model="formData.productCodeList"
              api-key="getProductNumberList"
              filterable
              multiple
              virtualized
              @blur="searchUpdate"
            />
          </ElFormItem>
          <ElFormItem :span="24" label="品牌">
            <SelectPlus
              v-model="formData.brandIdList"
              api-key="baseBrand"
              cache
              checkbox
              checkbox-button
              multiple
            />
          </ElFormItem>
          <ElFormItem label="创建时间">
            <ElDatePicker
              v-model="formData.date"
              end-placeholder="结束时间"
              placeholder="请选择"
              start-placeholder="开始时间"
              type="daterange"
              value-format="YYYY-MM-DD"
            />
          </ElFormItem>
          <ElFormItem label="开发季节" prop="saleSeasonList">
            <SelectPlus
              v-model="formData.saleSeasonList"
              api-key="COMMON_MARKET_SEASON"
              cache
              multiple
            />
          </ElFormItem>
          <ElFormItem label="设计师">
            <CascadeSelector
              v-model="formData.designerIdList"
              :props="{ multiple: true, emitPath: false }"
              api-key="allUsers"
              cache
              collapse-tags
              collapse-tags-tooltip
            />
          </ElFormItem>
          <ElFormItem label="创建者">
            <CascadeSelector
              v-model="formData.createByIdList"
              :props="{ multiple: true, emitPath: false }"
              api-key="allUsers"
              cache
              collapse-tags
              collapse-tags-tooltip
            />
          </ElFormItem>
          <ElFormItem label="修改者">
            <CascadeSelector
              v-model="formData.modifyByIdList"
              :props="{ multiple: true, emitPath: false }"
              api-key="allUsers"
              cache
              collapse-tags
              collapse-tags-tooltip
            />
          </ElFormItem>
          <ElFormItem label="检索分类">
            <SelectPlus
              v-model="formData.searchCategory"
              api-key="SEARCH_CATEGORY"
              cache
              multiple
            />
          </ElFormItem>
          <ElFormItem label="侵权风险判定">
            <SelectPlus v-model="formData.riskList" api-key="INFRINGEMENT_RISK" cache multiple />
          </ElFormItem>
          <ElFormItem label="品牌维权力度">
            <SelectPlus
              v-model="formData.strengthList"
              api-key="RIGHTS_PROTECTION"
              cache
              multiple
            />
          </ElFormItem>
        </SearchForm>
        <VxeToolbar v-if="!isEmbed">
          <template #buttons>
            <ElButton
              v-hasPermi="[RouteNameEnums.IS]"
              type="primary"
              class="mr-3"
              @click="handleRouterPush(RouteNameEnums.IS)"
            >
              <Icon icon="ep:plus" />
              <span class="text-[14px]"> 侵权初筛</span>
            </ElButton>
            <operate
              :row="getTableCheckRow"
              :permissionButton="{
                ISE: ButtonPermissionEnums.ISE,
                IPJ: ButtonPermissionEnums.IPJ,
                IR: ButtonPermissionEnums.IR
              }"
            />
          </template>
        </VxeToolbar>
        <div>
          <VTable
            :cell-config="{ height: 60 }"
            :data="tableData"
            :loading="loading"
            :max-height="maxHeight - 65"
            :pager="pager"
            @register="refRegister"
            @update="pagerUpdate"
          >
            <VxeColumn fixed="left" min-width="40px" type="checkbox" />
            <!--            <VxeColumn-->
            <!--              :visible="props.embedType !== 'productInfo'"-->
            <!--              fixed="left"-->
            <!--              min-width="60px"-->
            <!--              type="radio"-->
            <!--            />-->
            <VxeColumn min-width="60px" title="序号" fixed="left" type="seq" />
            <VxeColumn field="infringementCode" fixed="left" min-width="150px" title="侵权记录编号">
              <template #default="{ row }: { row: InfringementPageAPI.List }">
                <RouterLink
                  :to="{ name: RouteNameEnums.ID, params: { routerId: row.id } }"
                  class="text-blue-500"
                >
                  {{ row.infringementCode }}
                </RouterLink>
              </template>
            </VxeColumn>
            <VxeColumn img-base-file min-width="100px" fixed="left" title="缩略图">
              <template #default="{ row }: { row: InfringementPageAPI.List }">
                <ElImage
                  :preview-src-list="[row.thumbnail!]"
                  :src="row.thumbnail"
                  class="h-[40px] rounded shadow"
                  hide-on-click-modal
                  error="加载失败"
                  loading="lazy"
                  placeholder="加载中..."
                  preview-teleported
                />
              </template>
            </VxeColumn>
            <VxeColumn field="designerName" min-width="130px" title="产品设计师" />
            <VxeColumn field="searchCategoryItemName" min-width="130px" title="检索分类" />
            <VxeColumn field="searchNeed" min-width="100px" title="检索需求" />
            <VxeColumn field="referenceBrand" min-width="130px" title="参考品牌" />
            <VxeColumn field="referenceBrandUrl" link min-width="150px" title="参考品牌链接" />
            <VxeColumn field="sameShoesBrand" min-width="160px" title="同款鞋对应品牌" />
            <VxeColumn field="needHelpFlagItemName" min-width="170px" title="设计师是否需协助" />
            <VxeColumn field="createTime" min-width="160px" title="创建时间" />
            <VxeColumn
              field="safeguardRightsStrengthPre"
              min-width="190px"
              title="品牌维权力度-初判"
            />
            <VxeColumn field="infringementRiskPre" min-width="190px" title="侵权风险判定-初判" />
            <VxeColumn
              field="suggestionInstructionPre"
              min-width="230px"
              title="规避建议/风险情况说明-初判"
            />
            <VxeColumn field="innerControlUserNamePre" min-width="130px" title="内控复核人-初判" />
            <VxeColumn field="searchDatePre" min-width="160px" title="检索日期-初判" />
            <VxeColumn
              field="safeguardRightsStrengthRe"
              min-width="200px"
              title="品牌维权力度-复判"
            />
            <VxeColumn field="infringementRiskRe" min-width="200px" title="侵权风险判定-复判" />
            <VxeColumn
              field="suggestionInstructionRe"
              min-width="260px"
              title="规避建议/风险情况说明-复判"
            />
            <VxeColumn field="innerControlUserNameRe" min-width="130px" title="内控复核人-复判" />
            <VxeColumn field="searchDateRe" min-width="100px" title="检索日期-复判" />
            <VxeColumn field="productCode" min-width="150px" title="关联产品编号" />
            <VxeColumn field="brandName" min-width="70px" title="品牌" />
            <VxeColumn field="saleSeason" min-width="100px" title="开发季节" />
            <VxeColumn field="status" fixed="right" min-width="160px" title="状态" />
            <VxeColumn field="delReason" min-width="100px" title="作废原因" />
            <VxeColumn field="operator" fixed="right" min-width="80px" title="操作人" />
            <VxeColumn field="modifyTime" min-width="160px" title="操作时间" />
            <VxeColumn :show-overflow="false" fixed="right" min-width="160px" title="操作">
              <template #default="{ row }: { row: InfringementPageAPI.List }">
                <ElButton
                  v-if="
                    !isEmbed &&
                    !row.infringementRiskPre &&
                    !row.infringementRiskRe &&
                    row.status !== '作废'
                  "
                  size="small"
                  text
                  type="primary"
                  @click="handleVoid(row)"
                >
                  作废
                </ElButton>
                <ElButton
                  v-if="!isEmbed"
                  size="small"
                  text
                  type="primary"
                  @click="handleOpenVersionDialog(row)"
                >
                  版本记录
                </ElButton>
              </template>
            </VxeColumn>
          </VTable>
        </div>
      </div>
      <VersionDialog v-model="versionDialogVisible" :current-row="currentRow" />
    </div>
  </ContentWrap>
</template>
