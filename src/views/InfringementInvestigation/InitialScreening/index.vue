<script lang="ts" setup>
import { ElMessage, type FormInstance } from 'element-plus'
import { CommonYesNoEnums } from '@/enums'
import { formatArrayRules } from '@/utils'
import {
  infringementScreeningAdd,
  infringementScreeningDetail,
  infringementScreeningUpdate
} from '@/api/infringementInvestigation'
import type { InfringementScreeningAddAPI } from '@/api/infringementInvestigation/types'
import ProductInfoDialog from '@/views/basic-library-manage/components/ProductInfoDialog.vue'
import { RouteNameEnums, TempStorageFlagEnums } from '../helper'
import { ProductListPageAPI } from '@/views/basic-library-manage/product-library/api/product-list'
import { Icon } from '@/components/Icon'

defineOptions({
  name: 'InitialScreening'
})

const formRef = ref<FormInstance>()
const productInfoDialogVisible = ref(false)

const currentRoute = useRouter().currentRoute.value
const routeName = currentRoute.name as RouteNameEnums
const routerId = currentRoute.params.routerId as unknown as number
const loading = ref(false)
const activeLoading = ref(false)
const formData = reactive<InfringementScreeningAddAPI.Params>({
  designImgList: [],
  needHelpFlag: CommonYesNoEnums.No,
  marketFlag: CommonYesNoEnums.No
})

const rules = {
  ...formatArrayRules([
    ['brand', '品牌'],
    ['designImgList', '设计图'],
    ['stereoscopicImgList', '立体图'],
    ['saleSeason', '开发季节'],
    ['designerId', '设计师'],
    ['referenceBrand', '参考品牌'],
    ['needHelpFlag', '设计师是否需协助'],
    ['searchCategory', '检索分类']
  ]),
  detailImgList: [
    {
      required: true,
      message: `请至少上传2张，左右、前后、上下视图的图片详情`,
      trigger: ['change', 'blur']
    },
    {
      validator: (_rule: any, value: BaseFileDTO[], callback: any) => {
        if (value.length < 2) {
          callback(new Error('请至少上传2张，左右、前后、上下视图的图片详情'))
        }
        callback()
      },
      trigger: ['change', 'blur']
    }
  ]
}

const infringementScreeningAddParams = computed(() => {
  return { ...formData }
})

const _infringementScreeningActive = async () => {
  try {
    activeLoading.value = true
    const params = infringementScreeningAddParams.value
    const { msg } =
      routeName === RouteNameEnums.ISE
        ? await infringementScreeningUpdate(params)
        : await infringementScreeningAdd(params)
    ElMessage.success(msg)
    useClosePage(RouteNameEnums.Index, {
      state: {
        infringementCode: currentRoute.query?.infringementCode
      }
    })
  } finally {
    activeLoading.value = false
  }
}

const _infringementScreeningDetail = async () => {
  try {
    loading.value = true
    const { datas } = await infringementScreeningDetail({ id: routerId })
    Object.assign(formData, datas)
  } finally {
    loading.value = false
  }
}

const route = useRoute()

onMounted(() => {
  if (route.query && route.query.productInfo) {
    const row = JSON.parse(route.query.productInfo as string) as ProductListPageAPI.Row
    handleProductSelectDone(row)
  }
})

const handleOpenProductInfoDialog = () => {
  productInfoDialogVisible.value = true
}

const handleProductSelectDone = (row: ProductListPageAPI.Row) => {
  formData.styleCode = row.productNumber!
  formData.refProductImgList = row.referenceUrl
  formData.designImgList = row.designUrl
  formData.designerId = row.designerId
  formData.brand = row.brand
  formData.saleSeason = row.launchSeason
  formData.referenceBrand = row.referenceBrand
  formData.referenceBrandUrl = row.competitiveBrandLink
  productInfoDialogVisible.value = false
}

const handleSubmit = (type: TempStorageFlagEnums) => {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      formData.tempStorageFlag = type
      await _infringementScreeningActive()
    }
  })
}

const handleFetch = () => {
  if (routeName === RouteNameEnums.ISE) {
    _infringementScreeningDetail()
  }
}
const handleClose = () => {
  const infringementCode = currentRoute.query?.infringementCode
  useClosePage('InfringementInvestigation', {
    state: {
      infringementCode
    }
  })
}

onMounted(handleFetch)
onActivated(handleFetch)
</script>

<template>
  <ContentWrap v-loading="loading">
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      :scroll-into-view-options="{ behavior: 'smooth' }"
      class="form-layout--2"
      inline
      label-width="auto"
      scroll-to-error
    >
      <ElFormItem label="关联产品编号">
        <ElInput
          v-model="formData.styleCode"
          placeholder="请选择"
          readonly
          @click="handleOpenProductInfoDialog"
        >
          <template #suffix>
            <Icon :size="26" class="cursor-pointer" color="#409EFF" icon="mdi:search" />
          </template>
        </ElInput>
      </ElFormItem>
      <ElFormItem label="" />
      <ElFormItem label="设计图" prop="designImgList">
        <OssUpload
          v-model="formData.designImgList"
          :limit="20"
          :multiple="true"
          :size-limit="1024 * 1024 * 100"
          accept="image/*"
          drag
          listType="picture-card"
        />
      </ElFormItem>
      <ElFormItem label="参考款式图">
        <OssUpload
          v-model="formData.refProductImgList"
          :action="'/pdm-base/file/batchUpload'"
          :limit="20"
          :size-limit="1024 * 1024 * 100"
          accept="image/*"
          drag
          listType="picture-card"
          multiple
        />
      </ElFormItem>
      <ElFormItem label="立体图" prop="stereoscopicImgList">
        <OssUpload
          v-model="formData.stereoscopicImgList"
          :limit="20"
          :size-limit="1024 * 1024 * 100"
          accept="image/*"
          drag
          listType="picture-card"
          multiple
        />
      </ElFormItem>
      <ElFormItem label="图片详情（至少2张）" prop="detailImgList">
        <OssUpload
          v-model="formData.detailImgList"
          :limit="20"
          :size-limit="1024 * 1024 * 100"
          accept="image/*"
          drag
          listType="picture-card"
          multiple
        />
      </ElFormItem>
      <ElFormItem label="对应品牌鞋图">
        <OssUpload
          v-model="formData.brandShoesImgList"
          :limit="20"
          :size-limit="1024 * 1024 * 100"
          accept="image/*"
          drag
          listType="picture-card"
          multiple
        />
      </ElFormItem>
      <ElFormItem label="相关外观图片">
        <OssUpload
          v-model="formData.refOutlookImgList"
          :limit="20"
          :size-limit="1024 * 1024 * 100"
          accept="image/*"
          drag
          listType="picture-card"
          multiple
        />
      </ElFormItem>
      <ElFormItem label="设计师" prop="designerId">
        <CascadeSelector
          v-model="formData.designerId"
          :props="{ emitPath: false }"
          api-key="allUsers"
          cache
        />
      </ElFormItem>
      <ElFormItem label="品牌" prop="brand">
        <SelectPlus v-model="formData.brand" api-key="baseBrand" cache />
      </ElFormItem>
      <ElFormItem label="开发季节" prop="saleSeason">
        <SelectPlus v-model="formData.saleSeason" api-key="COMMON_MARKET_SEASON" cache />
      </ElFormItem>
      <ElFormItem label="参考品牌" prop="referenceBrand">
        <ElInput
          v-model="formData.referenceBrand"
          maxlength="100"
          placeholder="请输入"
          show-word-limit
        />
      </ElFormItem>
      <ElFormItem label="检索分类" prop="searchCategory">
        <SelectPlus v-model="formData.searchCategory" api-key="SEARCH_CATEGORY" cache filterable />
      </ElFormItem>
      <ElFormItem label="检索需求">
        <ElInput
          v-model="formData.searchNeed"
          maxlength="100"
          placeholder="请输入"
          show-word-limit
        />
      </ElFormItem>
      <ElFormItem label="产品描述">
        <ElInput
          v-model="formData.productDescription"
          maxlength="100"
          placeholder="请输入"
          show-word-limit
        />
      </ElFormItem>
      <ElFormItem label="参考品牌链接">
        <ElInput
          v-model="formData.referenceBrandUrl"
          maxlength="200"
          placeholder="请输入"
          show-word-limit
        />
      </ElFormItem>
      <ElFormItem label="风险初筛">
        <SelectPlus v-model="formData.riskScreening" api-key="INFRINGEMENT_RISK" cache />
      </ElFormItem>
      <ElFormItem label="同款鞋对应品牌">
        <ElInput
          v-model="formData.sameShoesBrand"
          maxlength="100"
          placeholder="请输入"
          show-word-limit
        />
      </ElFormItem>
      <ElFormItem label="设计师是否需协助" prop="needHelpFlag">
        <SelectPlus v-model="formData.needHelpFlag" api-key="COMMON_YES_NO" cache radio />
      </ElFormItem>
      <ElFormItem label="产品是否上市">
        <SelectPlus
          v-model="formData.marketFlag"
          api-key="COMMON_YES_NO"
          cache
          radio
          value-type="number"
        />
      </ElFormItem>
    </ElForm>
    <FooterAction>
      <ElButton @click="handleClose">返回</ElButton>
      <ElButton
        :loading="activeLoading"
        type="primary"
        @click="handleSubmit(TempStorageFlagEnums.Formal)"
      >
        确定
      </ElButton>
    </FooterAction>
    <ProductInfoDialog v-model="productInfoDialogVisible" @submit="handleProductSelectDone" />
  </ContentWrap>
</template>
