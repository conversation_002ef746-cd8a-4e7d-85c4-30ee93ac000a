/**
 * 侵权装填
 */
export enum InfringementEnum {
  INFRINGEMENT_FIRST_JUDGMENT_TEMPORARILY = 'INFRINGEMENT_FIRST_JUDGMENT_TEMPORARILY', //侵权排查-初判暂存
  INFRINGEMENT_FIRST_JUDGMENT_SUBMITTED = 'INFRINGEMENT_FIRST_JUDGMENT_SUBMITTED', //侵权排查-初判已提交
  INFRINGEMENT_SECOND_JUDGMENT_SUBMITTED = 'INFRINGEMENT_SECOND_JUDGMENT_SUBMITTED', //侵权排查-复判已提交
  INFRINGEMENT_SECOND_JUDGMENT_TEMPORARILY = 'INFRINGEMENT_SECOND_JUDGMENT_TEMPORARILY', //侵权排查-复判暂存
  INFRINGEMENT_INITIAL_JUDGMENT_SUBMITTED = 'INFRINGEMENT_INITIAL_JUDGMENT_SUBMITTED', //侵权排查-初筛已提交
  INFRINGEMENT_INITIAL_JUDGMENT_TEMPORARILY = 'INFRINGEMENT_INITIAL_JUDGMENT_TEMPORARILY', // 侵权排查-初筛暂存
  INFRINGEMENT_CANCEL_JUDGMENT = 'INFRINGEMENT_CANCEL_JUDGMENT', // 作废
  INFRINGEMENT_FIRST_JUDGMENT = 'INFRINGEMENT_FIRST_JUDGMENT', //初判
  INFRINGEMENT_SECOND_JUDGMENT = 'INFRINGEMENT_SECOND_JUDGMENT' //复判
}
