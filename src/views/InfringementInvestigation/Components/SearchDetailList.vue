<script lang="ts" setup>
import {
  InfringementJudgmentDetailQueryResp,
  InfringementSearchAddReq
} from '@/api/infringementInvestigation/types'

withDefaults(
  defineProps<{
    list: InfringementSearchAddReq[]
    riskList?: InfringementJudgmentDetailQueryResp[]
  }>(),
  {
    list: () => [],
    riskList: () => []
  }
)
</script>
<template>
  <ElDivider content-position="left">风险判定</ElDivider>
  <VxeTable :cell-config="{ height: 90 }" :data="riskList" show-overflow="tooltip">
    <VxeColumn title="序号" type="seq" width="60" />
    <VxeColumn field="infringementRisk" title="侵权风险判定" />
    <VxeColumn field="riskCountry" title="风险国家" />
    <VxeColumn
      class-name="ellipsis-cell"
      :cell-render="{ name: 'Ellipsis', props: { maxRow: 4 } }"
      field="prohibitedPlatformList"
      title="禁售平台"
    />
  </VxeTable>
  <ElDivider content-position="left">检索详情</ElDivider>
  <VxeTable :data="list" :show-overflow="false">
    <VxeColumn title="序号" type="seq" width="60" />
    <VxeColumn field="searchWay" title="检索途径" />
    <VxeColumn field="searchRecordInstruction" title="检索记录说明" />
    <VxeColumn field="imgList" title="检索图片">
      <template #default="{ row }: { row: InfringementSearchAddReq }">
        <ElImage
          v-for="item in row.imgList"
          :key="item.fileUrl"
          hide-on-click-modal
          :preview-src-list="[item.signatureUrl!]"
          :src="item.signatureUrl"
          class="h-[40px] w-[40px] rounded shadow mx-2 p-1 shadow-gray-400"
          fit="contain"
        />
      </template>
    </VxeColumn>
    <VxeColumn field="fileList" title="相关附件">
      <template #default="{ row }: { row: InfringementSearchAddReq }">
        <ElLink
          v-for="item in row.fileList"
          :key="item.fileUrl"
          :href="item.signatureUrl"
          :underline="false"
          class="mr-2"
          target="_blank"
        >
          {{ item.fileName }}
        </ElLink>
      </template>
    </VxeColumn>
  </VxeTable>
</template>
