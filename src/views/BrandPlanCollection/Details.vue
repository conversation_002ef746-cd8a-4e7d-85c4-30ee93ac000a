<script setup lang="ts">
import { proposalAdd, proposalUpdate } from '@/api/brandPlanCollection'
import { cloneDeep } from 'lodash-es'
import type {
  ProposalAddAPI,
  ProposalPageAPI,
  ProposalUpdateAPI
} from '@/api/brandPlanCollection/types'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { OperationTypeEnums } from '@/enums'
import { formatArrayRules } from '@/utils'

interface Props {
  modelValue: boolean
  operationType: OperationTypeEnums
  currentRow?: ProposalPageAPI.List
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})
const formData = reactive<ProposalAddAPI.Params>({
  brandId: undefined,
  fileDTOList: [],
  saleSeason: '',
  categoryIdList: [],
  totalNum: undefined,
  remark: ''
})
const formRef = ref<FormInstance>()

const rules = formatArrayRules([
  ['brandId', '品牌'],
  ['fileDTOList', '企划案文档'],
  ['categoryIdList', '产品类目'],
  ['saleSeason', '上市季节']
])

const dialogTitle = computed(() => {
  return props.operationType === OperationTypeEnums.Add ? '新增品牌企划案' : '修改品牌企划案'
})

const handleCommit = () => {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      const { msg } =
        props.operationType === OperationTypeEnums.Add
          ? await proposalAdd(formData)
          : await proposalUpdate(formData as ProposalUpdateAPI.Params)
      ElMessage.success(msg)
      emits('update:modelValue', false)
      emits('done')
    }
  })
}
watch(
  () => props.modelValue,
  async (modelValue) => {
    if (modelValue) {
      await nextTick()
      if (props.operationType === OperationTypeEnums.Edit) {
        Object.assign(formData, cloneDeep(props.currentRow))
      }
    } else {
      formRef.value?.resetFields()
      formData.remark = ''
      formData.totalNum = undefined
    }
  }
)

const emits = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'done'): void
}>()
</script>
<template>
  <Dialog
    :modelValue="modelValue"
    @update:model-value="emits('update:modelValue', $event)"
    width="900"
    :title="dialogTitle"
  >
    <ElForm class="mt-2" :model="formData" ref="formRef" label-width="8em" :rules="rules">
      <ElFormItem label="品牌" prop="brandId">
        <SelectPlus cache api-key="baseBrand" v-model="formData.brandId" />
      </ElFormItem>
      <ElFormItem label="上市季节" prop="saleSeason">
        <SelectPlus cache api-key="COMMON_MARKET_SEASON" v-model="formData.saleSeason" />
      </ElFormItem>
      <ElFormItem label="产品类目" prop="categoryIdList">
        <CascadeSelector
          class="w-full"
          api-key="productCategoryDrop"
          v-model="formData.categoryIdList"
          :props="{ emitPath: false, multiple: true }"
          collapse-tags
          collapse-tags-tooltip
        />
      </ElFormItem>
      <ElFormItem label="汇总款式数" prop="totalNum">
        <ElInputNumber
          class="min-w-48 number-input"
          v-model="formData.totalNum"
          placeholder="请输入"
          :min="0"
          :controls="false"
        />
      </ElFormItem>
      <ElFormItem label="企划案文档" prop="fileDTOList" class="file-update">
        <OssUpload
          v-model="formData.fileDTOList"
          listType="text"
          drag
          multiple
          :limit="20"
          :size-limit="1024 * 1024 * 100"
        />
      </ElFormItem>

      <ElFormItem label="备注" prop="remark">
        <ElInput class="w-48" type="textarea" v-model="formData.remark" placeholder="请输入名称" />
      </ElFormItem>
      <FooterAction>
        <div class="flex justify-end w-full">
          <ElButton @click="emits('update:modelValue', false)">取消</ElButton>
          <ElButton type="primary" @click="handleCommit">确定</ElButton>
        </div>
      </FooterAction>
    </ElForm>
  </Dialog>
</template>
<style lang="less" scoped>
.number-input {
  :deep(.el-input__wrapper) {
    padding-left: 12px;
  }

  :deep(.el-input__inner) {
    text-align: left;
  }
}

.file-update {
  :deep(.el-form-item__content) {
    .el-upload-list.el-upload-list--text {
      min-width: 300px;
    }

    .el-upload-dragger {
      justify-content: flex-start;
    }
  }
}
</style>
