<script lang="ts" setup>
import { proposalDelete, proposalPage } from '@/api/brandPlanCollection'
import type { ProposalPageAPI } from '@/api/brandPlanCollection/types'
import { OperationTypeEnums } from '@/enums'
import { ElButton, ElMessage, type FormInstance } from 'element-plus'
import { omit } from 'lodash-es'
import Details from '@/views/BrandPlanCollection/Details.vue'
import { Icon } from '@/components/Icon'

defineOptions({
  name: 'BrandPlanCollection'
})

type FormDataType = ProposalPageAPI.Params & { operatorIdLists?: number[][] }
type RowType = ProposalPageAPI.List
const visible = ref(false)
const formRef = ref<FormInstance>()
const operationType = ref<OperationTypeEnums>(OperationTypeEnums.Add)
const formData = reactive<FormDataType>({
  brandIdList: [],
  operatorIdLists: [],
  saleSeasonList: []
})

const pramsFormatMethod = (params: ProposalPageAPI.Request & { operatorIdLists?: number[][] }) => {
  return omit(
    {
      ...params,
      operatorIdList: params.operatorIdLists?.map((item) => item[item.length - 1])
    },
    ['operatorIdLists']
  )
}

const {
  tableData,
  loading,
  pager,
  radioChange,
  radioSelectedRow,
  requestData,
  pagerUpdate,
  searchUpdate,
  updateRequestData
} = usePage({
  api: proposalPage,
  pramsFormatMethod,
  formData,
  mountedRequest: true
})

const handleOperation = async (type: OperationTypeEnums, row?: ProposalPageAPI.List) => {
  if (type === OperationTypeEnums.Delete && row) {
    const { msg } = await proposalDelete({ id: row.id! })
    ElMessage.success(msg)
    requestData()
    return
  }
  if (type === OperationTypeEnums.Edit && !radioSelectedRow.value) {
    ElMessage.warning('请选择一条数据')
    return
  }
  operationType.value = type
  visible.value = true
}
const handleReset = () => {
  formRef.value?.resetFields()
  searchUpdate()
}
</script>
<template>
  <ContentWrap>
    <ElForm ref="formRef" :model="formData">
      <ElRow :gutter="20">
        <ElCol :span="6">
          <ElFormItem label="上市季节" prop="saleSeasonList">
            <SelectPlus v-model="formData.saleSeasonList" api-key="COMMON_MARKET_SEASON" multiple />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="品牌" prop="brandIdList">
            <SelectPlus v-model="formData.brandIdList" api-key="baseBrand" cache multiple />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="操作人" prop="operatorIdLists">
            <CascadeSelector
              v-model="formData.operatorIdLists"
              :props="{ multiple: true }"
              api-key="allUsers"
              cache
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElButton type="primary" :loaing="loading" @click="searchUpdate">
            <Icon v-show="!loading" class="mr-1" icon="ep:search" />
            查询
          </ElButton>
          <ElButton :loading="loading" class="w-16" native-type="reset" @click="handleReset">
            <Icon v-show="!loading" class="mr-1" icon="ep:refresh-right" />

            重置
          </ElButton>
        </ElCol>
      </ElRow>
      <div class="mb-[12px]">
        <ElButton
          v-hasPermi="['createProposal']"
          type="primary"
          @click="handleOperation(OperationTypeEnums.Add)"
        >
          <Icon icon="ep:plus" />
          <span> 新增企划案</span>
        </ElButton>
        <ElButton
          v-hasPermi="['editProposal']"
          type="primary"
          @click="handleOperation(OperationTypeEnums.Edit)"
        >
          <Icon icon="ep:edit" />
          <span> 修改企划案</span>
        </ElButton>
      </div>
    </ElForm>
    <div>
      <VTable
        :data="tableData"
        :loading="loading"
        :pager="pager"
        @update="pagerUpdate"
        @radio-change="radioChange"
      >
        <VxeColumn type="radio" width="60px" />
        <VxeColumn align="center" title="序号" type="seq" width="55" />
        <VxeColumn field="brandName" min-width="70px" title="品牌" />
        <VxeColumn field="saleSeason" min-width="100px" title="上市季节" />
        <VxeColumn
          field="categoryName"
          min-width="100px"
          title="产品类目"
          class-name="ellipsis-cell"
          :cell-render="{ name: 'Ellipsis', props: { minRow: 2, maxRow: 4 } }"
        />
        <VxeColumn field="totalNum" min-width="130px" title="汇总款式数" />
        <VxeColumn :show-overflow="false" field="fileDTOList" min-width="130px" title="企划案文档">
          <template #default="{ row }: { row: RowType }">
            <a
              class="text-[var(--el-color-primary)] hover:underline"
              v-for="item in row.fileDTOList"
              :key="item.fileUrl"
              :href="item.signatureUrl"
              target="_blank"
              type="primary"
            >
              {{ item.fileName }}
            </a>
          </template>
        </VxeColumn>
        <VxeColumn field="remark" min-width="70px" title="备注" />
        <VxeColumn :show-overflow="false" min-width="70px" title="操作">
          <template #default="{ row }: { row: RowType }">
            <el-popconfirm
              title="是否确认删除?"
              @confirm="handleOperation(OperationTypeEnums.Delete, row)"
            >
              <template #reference>
                <el-button text type="danger">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </VxeColumn>
        <VxeColumn field="modifyByName" min-width="80px" title="操作人" />
        <VxeColumn field="modifyTime" min-width="100px" title="操作时间" />
      </VTable>
    </div>
    <Details
      v-model="visible"
      :current-row="radioSelectedRow"
      :operation-type="operationType"
      @done="updateRequestData"
    />
  </ContentWrap>
</template>
