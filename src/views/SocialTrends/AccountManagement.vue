<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { reactive, ref } from 'vue'
import { AccountStatusChangeApi, getAccountApi } from '@/api/trends'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import Account from './components/Account.vue'
import { getDictOptions } from '@/hooks/autoImport/useDictAll'
import { trackDialogEvent } from '@/utils/monitor'

const elformRef = ref<FormInstance | null>(null)
const SM_PLATFORM = ref(getDictOptions('SM_PLATFORM'))
const SM_ACCOUNT_TYPE = ref(getDictOptions('SM_ACCOUNT_TYPE'))
const SM_FANS_SCALE = ref(getDictOptions('SM_FANS_SCALE'))
const SM_DATA_SOURCE = ref(getDictOptions('SM_DATA_SOURCE'))
const loading = ref(false)

const ruleForm: any = reactive({
  pushTime: [],
  platId: 1,
  account: '',
  accountType: '',
  accountSource: '',
  fanScale: '',
  page: 1,
  pageSize: 10
})
const rules = {
  platId: [{ required: true, trigger: 'change', message: '必填' }]
}
const tableList = ref([] as any)
const pager = reactive<pager>({
  current: 1,
  size: 10,
  total: 0
})
onMounted(() => {
  getList()
})
const getList = () => {
  Object.assign(ruleForm, { page: 1, pageSize: 10 })
  handleSearch()
}

const handleSearch = async () => {
  await unref(elformRef)?.validate(async (isValid) => {
    if (isValid) {
      try {
        loading.value = true
        const { pushTime } = ruleForm
        const [enabledDateStart, enabledDateEnd] = pushTime || []
        Object.assign(ruleForm, { enabledDateStart, enabledDateEnd })
        const { datas } = await getAccountApi(ruleForm)
        tableList.value = datas.rows
        Object.assign(pager, { current: datas.currentPage, total: datas.total })
      } finally {
        loading.value = false
      }
    }
  })
}
const handleReset = () => {
  elformRef.value?.resetFields()
  getList()
}
const handleUpdate = (val) => {
  Object.assign(pager, val)
  Object.assign(ruleForm, { page: val.current, pageSize: val.size })
  handleSearch()
}
const addVisible = ref(false)
const handleType = ref('')
const editDatas = ref([] as any)
const multipleSelection = ref([] as any)
const handleSelect = (val) => {
  multipleSelection.value = val
}

const handleAdd = async (type) => {
  handleType.value = type
  if (type === 'edit') {
    const selectionList = multipleSelection.value
    if (!Array.isArray(selectionList) || selectionList.length < 1)
      return ElMessage.warning('至少选择一条数据')
    editDatas.value = selectionList
  } else {
    editDatas.value = [{}]
  }
  addVisible.value = true
  trackDialogEvent('账号管理' + type)
}
const handleDisabled = async () => {
  const selectionList = multipleSelection.value
  if (!Array.isArray(selectionList) || selectionList.length < 1)
    return ElMessage.warning('至少选择一条数据')
  if (selectionList.some((item) => item.enabled !== selectionList[0].enabled)) {
    return ElMessage.warning('请选择同一个状态的数据')
  }

  ElMessageBox.confirm(
    `请确认${selectionList[0].enabled ? '禁用' : '启用'}数据`,
    `${selectionList[0].enabled ? '禁用' : '启用'}确认`,
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      const accountIds = selectionList.map((i) => i.id)
      const params = {} as any
      Object.assign(params, { accountIds, enabled: selectionList[0].enabled ? 0 : 1 })
      await AccountStatusChangeApi(params)
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      getList()
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      })
    })
}
</script>

<template>
  <ContentWrap>
    <ElForm
      ref="elformRef"
      :model="ruleForm"
      :rules="rules"
      label-width="100px"
      @submit="
        (e) => {
          e.preventDefault()
        }
      "
    >
      <ElRow>
        <ElCol :span="20">
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="账户名" prop="account">
                <ElInput v-model="ruleForm.account" clearable />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="生效时间" prop="pushTime">
                <ElDatePicker
                  v-model="ruleForm.pushTime"
                  :clearable="false"
                  format="YYYY-MM-DD"
                  type="daterange"
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="24">
              <ElFormItem label="平台" prop="platId">
                <ElRadioGroup v-model="ruleForm.platId">
                  <ElRadioButton
                    v-for="item in SM_PLATFORM"
                    :key="Number(item.dictValue)"
                    :label="item.dictCnName"
                    :value="Number(item.dictValue)"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="账号类型" prop="accountType">
                <ElRadioGroup v-model="ruleForm.accountType">
                  <ElRadioButton
                    v-for="item in SM_ACCOUNT_TYPE"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="粉丝量级" prop="fanScale">
                <ElRadioGroup v-model="ruleForm.fanScale">
                  <ElRadioButton
                    v-for="item in SM_FANS_SCALE"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="获取来源" prop="accountSource">
                <ElRadioGroup v-model="ruleForm.accountSource">
                  <ElRadioButton
                    v-for="item in SM_DATA_SOURCE"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCol>
        <ElCol :span="4">
          <ElButton v-loading="loading" type="primary" @click="getList" native-type="submit">
            <Icon class="mr-1" icon="ep:search" />
            查询
          </ElButton>
          <ElButton v-loading="loading" @click="handleReset" native-type="reset">
            <Icon class="mr-1" icon="ep:refresh-right" />
            重置
          </ElButton>
        </ElCol>
      </ElRow>
    </ElForm>
    <div class="mb-5">
      <ElButton v-hasPermi="['addAccount']" type="primary" @click="handleAdd('add')">
        <Icon class="mr-1" icon="ep:plus" />新增账户
      </ElButton>
      <ElButton v-hasPermi="['editAccount']" type="primary" @click="handleAdd('edit')">
        <Icon class="mr-1" icon="ep:edit" />修改账户
      </ElButton>
      <ElButton v-hasPermi="['disabledAccount']" type="primary" @click="handleDisabled">
        <Icon class="mr-1" icon="ep:lock" />启用/禁用
      </ElButton>
    </div>
    <el-table
      v-loading="loading"
      :data="tableList"
      border
      max-height="500"
      style="width: 100%"
      @selection-change="handleSelect"
    >
      <el-table-column type="selection" />
      <el-table-column align="center" label="序号" type="index" width="60" />
      <el-table-column align="center" label="账号名" prop="account" width="180" />
      <el-table-column align="center" label="平台" prop="platId" width="180">
        <template #default="{ row }">
          {{ SM_PLATFORM?.find((i) => Number(i.dictValue) === row.platId)?.dictCnName }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="主页链接" prop="homePage" width="180" />
      <el-table-column align="center" label="粉丝量级" prop="fanScale" width="180">
        <template #default="{ row }">
          {{ SM_FANS_SCALE?.find((i) => i.dictValue === row.fanScale)?.dictCnName }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="账户类型" prop="accountType" width="120">
        <template #default="{ row }">
          {{ SM_ACCOUNT_TYPE?.find((i) => i.dictValue === row.accountType)?.dictCnName }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="获取来源" prop="accountSource" width="120">
        <template #default="{ row }">
          {{ SM_DATA_SOURCE?.find((i) => i.dictValue === row.accountSource)?.dictCnName }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="状态" prop="enabled" width="120">
        <template #default="{ row }">{{ row.enabled ? '启用' : '禁用' }}</template>
      </el-table-column>
      <el-table-column align="center" label="禁用原因" prop="disableRemark" width="180" />
      <el-table-column align="center" label="生效时间" min-width="180" prop="enabledTime" />
    </el-table>
    <Paging :pager="pager" @update="handleUpdate" />
    <Account
      v-model="addVisible"
      :editDatas="editDatas"
      :handleType="handleType"
      @refresh="getList"
    />
  </ContentWrap>
</template>
<style lang="less" scoped>
:deep(.el-radio-button) {
  margin: 0 8px 4px 0 !important;

  .el-radio-button__inner {
    border: none;
  }
}

:deep(.el-radio-button:last-child),
:deep(.el-radio-button:first-child) {
  .el-radio-button__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }
}
</style>
