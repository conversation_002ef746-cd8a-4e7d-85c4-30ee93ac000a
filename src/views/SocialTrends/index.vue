<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { getSocialMediaApi } from '@/api/trendsStatistics'
import SpecialSearch from './components/SpecificSearch.vue'
import StyleDetail from './components/StyleDetail.vue'

const loading = ref(false)
const pager = reactive<pager>({
  current: 1,
  size: 10,
  total: 0
})
const handleUpdate = (val) => {
  Object.assign(pager, val)
  Object.assign(ruleForm.value, { page: val.current, pageSize: val.size })
  handleSearch(ruleForm.value)
}
const ruleForm = ref({} as any)
const tableList = ref([] as any)
const getList = (val) => {
  Object.assign(val, { page: 1, pageSize: 10 })
  handleSearch(val)
}
const handleSearch = async (val) => {
  try {
    loading.value = true
    ruleForm.value = val
    const { datas } = await getSocialMediaApi(val)
    tableList.value = datas.rows
    Object.assign(pager, { current: datas.currentPage, total: datas.total })
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <ContentWrap>
    <SpecialSearch @search="getList" />

    <ElRow>
      <ElCol :span="16"><div class="mb-5" style="font-size: 24px">热度TOP50</div></ElCol>
      <ElCol :span="8"><Paging :pager="pager" @update="handleUpdate" class="mt-0 mr-2" /></ElCol>
    </ElRow>
    <div class="flex flex-wrap mb-5" v-loading="loading">
      <StyleDetail
        v-if="tableList.length > 0"
        :data="tableList"
        :platId="ruleForm.platId"
        :heatIndex="ruleForm.heatIndex"
      />
      <div v-else>暂无数据</div>
    </div>
    <Paging :pager="pager" @update="handleUpdate" class="mt-0 mr-2" />
  </ContentWrap>
</template>
