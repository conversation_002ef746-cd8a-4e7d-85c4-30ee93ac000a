<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { reactive, ref } from 'vue'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import { getTagApi, TagStatusChangeApi } from '@/api/trends'
import { getDictOptions } from '@/hooks/autoImport/useDictAll'
import Tag from './components/Tag.vue'
import { trackDialogEvent } from '@/utils/monitor'

const elformRef = ref<FormInstance | null>(null)
const SM_PLATFORM = ref(getDictOptions('SM_PLATFORM'))
const SM_TAG_TYPE = ref(getDictOptions('SM_TAG_TYPE'))
const SM_DATA_SOURCE = ref(getDictOptions('SM_DATA_SOURCE'))
const ruleForm: any = reactive({
  pushTime: [],
  platId: 1,
  productTinyCategory: '',
  colorScheme: '',
  element: '',
  heelType: '',
  toeCap: '',
  heatIndex: 1,
  page: 1,
  pageSize: 10
})
const rules = {
  platId: [{ required: true, trigger: 'change', message: '必填' }]
}

const pager = reactive<pager>({
  current: 1,
  size: 10,
  total: 0
})
onMounted(() => {
  getList()
})

const tableList = ref([] as any)
const loading = ref(false)
const getList = () => {
  Object.assign(ruleForm, { page: 1, pageSize: 10 })
  handleSearch()
}
const handleSearch = async () => {
  await unref(elformRef)?.validate(async (isValid) => {
    if (isValid) {
      try {
        loading.value = true
        const { pushTime } = ruleForm
        const [enabledDateStart, enabledDateEnd] = pushTime || []
        Object.assign(ruleForm, { enabledDateStart, enabledDateEnd })
        const { datas } = await getTagApi(ruleForm)
        tableList.value = datas.rows
        Object.assign(pager, { current: datas.currentPage, total: datas.total })
      } finally {
        loading.value = false
      }
    }
  })
}
const handleReset = () => {
  elformRef.value?.resetFields()
  getList()
}
const addVisible = ref(false)
const handleType = ref('')
const editDatas = ref([] as any)
const multipleSelection = ref([] as any)
const handleSelect = (val) => {
  multipleSelection.value = val
}
const handleAdd = async (type) => {
  handleType.value = type
  if (type === 'edit') {
    const selectionList = multipleSelection.value
    if (!Array.isArray(selectionList) || selectionList.length < 1)
      return ElMessage.warning('至少选择一条数据')
    editDatas.value = selectionList
  } else {
    editDatas.value = [{}]
  }
  addVisible.value = true
  trackDialogEvent('标签管理' + type)
}
const handleDisabled = async () => {
  const selectionList = multipleSelection.value
  if (!Array.isArray(selectionList) || selectionList.length < 1)
    return ElMessage.warning('至少选择一条数据')
  if (selectionList.some((item) => item.enabled !== selectionList[0].enabled)) {
    return ElMessage.warning('请选择同一个状态的数据')
  }

  ElMessageBox.confirm(
    `请确认${selectionList[0].enabled ? '禁用' : '启用'}数据`,
    `${selectionList[0].enabled ? '禁用' : '启用'}确认`,
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      const tagIds = selectionList.map((i) => i.id)
      const params = {} as any
      Object.assign(params, { tagIds, enabled: selectionList[0].enabled ? 0 : 1 })
      await TagStatusChangeApi(params)
      getList()
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      })
    })
}

const handleUpdate = (val) => {
  Object.assign(pager, val)
  Object.assign(ruleForm, { page: val.current, pageSize: val.size })
  handleSearch()
}
</script>

<template>
  <ContentWrap>
    <ElForm
      ref="elformRef"
      :model="ruleForm"
      :rules="rules"
      label-width="100px"
      @submit="
        (e) => {
          e.preventDefault()
        }
      "
    >
      <ElRow>
        <ElCol :span="20">
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="标签" prop="tag">
                <ElInput v-model="ruleForm.tag" clearable />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="生效时间" prop="pushTime">
                <ElDatePicker
                  v-model="ruleForm.pushTime"
                  :clearable="false"
                  format="YYYY-MM-DD"
                  type="daterange"
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="24">
              <ElFormItem label="标签类型" prop="tagType">
                <ElRadioGroup v-model="ruleForm.tagType">
                  <ElRadioButton
                    v-for="item in SM_TAG_TYPE"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="平台" prop="platId">
                <ElRadioGroup v-model="ruleForm.platId">
                  <ElRadioButton
                    v-for="item in SM_PLATFORM"
                    :key="Number(item.dictValue)"
                    :label="item.dictCnName"
                    :value="Number(item.dictValue)"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="获取来源" prop="tagSource">
                <ElRadioGroup v-model="ruleForm.tagSource">
                  <ElRadioButton
                    v-for="item in SM_DATA_SOURCE"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="状态" prop="enabled">
                <ElRadioGroup v-model="ruleForm.enabled">
                  <ElRadioButton :label="0">禁用</ElRadioButton>
                  <ElRadioButton :label="1">启用</ElRadioButton>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElCol>
        <ElCol :span="4">
          <ElButton v-loading="loading" type="primary" @click="getList" native-type="submit">
            <Icon class="mr-1" icon="ep:search" />
            查询
          </ElButton>
          <ElButton v-loading="loading" @click="handleReset" native-type="reset">
            <Icon class="mr-1" icon="ep:refresh-right" />
            重置
          </ElButton>
        </ElCol>
      </ElRow>
    </ElForm>
    <div class="mb-5">
      <ElButton v-hasPermi="['addTag']" type="primary" @click="handleAdd('add')">
        <Icon class="mr-1" icon="ep:plus" />新增标签
      </ElButton>
      <ElButton v-hasPermi="['editTag']" type="primary" @click="handleAdd('edit')">
        <Icon class="mr-1" icon="ep:edit" />修改标签
      </ElButton>
      <ElButton v-hasPermi="['disabled']" type="primary" @click="handleDisabled">
        <Icon class="mr-1" icon="ep:lock" />启用/禁用
      </ElButton>
    </div>
    <el-table
      v-loading="loading"
      :data="tableList"
      border
      max-height="500"
      style="width: 100%"
      @selection-change="handleSelect"
    >
      <el-table-column type="selection" />
      <el-table-column align="center" label="序号" type="index" width="60" />
      <el-table-column align="center" label="标签" prop="tag" width="180" />
      <el-table-column align="center" label="平台" prop="platId" width="180">
        <template #default="{ row }">
          {{ SM_PLATFORM?.find((i) => Number(i.dictValue) === row.platId)?.dictCnName }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="标签类型" prop="tagType" width="180">
        <template #default="{ row }">
          {{ SM_TAG_TYPE?.find((i) => i.dictValue === row.tagType)?.dictCnName }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="获取来源" prop="tagSource" width="180">
        <template #default="{ row }">
          {{ SM_DATA_SOURCE?.find((i) => i.dictValue === row.tagSource)?.dictCnName }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="状态" prop="enabled" width="180">
        <template #default="{ row }">{{ row.enabled ? '启用' : '禁用' }}</template>
      </el-table-column>
      <el-table-column align="center" label="禁用原因" prop="disableRemark" width="180" />
      <el-table-column align="center" label="生效时间" min-width="180" prop="enabledTime" />
    </el-table>
    <Paging :pager="pager" @update="handleUpdate" />
    <Tag v-model="addVisible" :editDatas="editDatas" :handleType="handleType" @refresh="getList" />
  </ContentWrap>
</template>
<style lang="less" scoped>
:deep(.el-radio-button) {
  margin: 0 8px 4px 0 !important;

  .el-radio-button__inner {
    border: none;
  }
}

:deep(.el-radio-button:last-child),
:deep(.el-radio-button:first-child) {
  .el-radio-button__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }
}
</style>
