<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { ElRadioButton, ElRadioGroup, FormInstance } from 'element-plus'
import { reactive, ref } from 'vue'
import StyleDetail from './components/StyleDetail.vue'
import {
  getSocialKeyColor<PERSON>pi,
  getSocial<PERSON>ey<PERSON>lement<PERSON>pi,
  getTrendAccountApi,
  getType<PERSON>pi
} from '@/api/trendsStatistics'
import { getDictOptions } from '@/hooks/autoImport/useDictAll'

const SM_PLATFORM = ref(getDictOptions('SM_PLATFORM'))
const SM_ACCOUNT_TYPE = ref(getDictOptions('SM_ACCOUNT_TYPE'))
const SM_FANS_SCALE = ref(getDictOptions('SM_FANS_SCALE'))
const SM_HEAT_INDEX = ref(getDictOptions('SM_HEAT_INDEX'))
const colorShemaList = ref([] as any)
const elementList = ref([] as any)
const tagOptions = ref([] as any)
const getProductTinyCategory = async () => {
  const { datas: colorShemaDatas } = await getTypeApi('COLOR_SCHEME')
  const { datas: elementDatas } = await getTypeApi('ELEMENT ')
  colorShemaList.value = colorShemaDatas
  elementList.value = elementDatas
}
const elformRef = ref<FormInstance | null>(null)
const ruleForm: any = reactive({
  pushTime: [],
  platId: 1,
  productTinyCategory: '',
  colorScheme: '',
  element: '',
  heelType: '',
  toeCap: '',
  heatIndex: '01',
  page: 1,
  pageSize: 10,
  limit: 30
})

const rules = {
  pushTime: [{ required: true, trigger: 'change', message: '必填' }],
  platId: [{ required: true, trigger: 'change', message: '必填' }],
  heatIndex: [{ required: true, trigger: 'change', message: '必填' }]
}

const getDate = () => {
  const nowDate = new Date()
  const year = nowDate.getFullYear()
  const month = nowDate.getMonth() + 1
  const lastDay = new Date(year, month, 0).getDate()
  const newMonth = month < 10 ? '0' + month : month
  ruleForm.pushTime = [[year, newMonth, '01'].join('-'), [year, newMonth, lastDay].join('-')]
}

const loading = ref(false)
const tableList = ref([] as any)
const colorTableDatasList = ref([] as any)
const elementTableDatasList = ref([] as any)
const handleColor = async () => {
  try {
    loading.value = true
    const { colorScheme, heatIndex, pushTime, platId } = ruleForm
    const [dateStart, dateEnd] = pushTime || []
    const params = {} as any
    Object.assign(params, { dateStart, dateEnd, heatIndex, platId, colorScheme, limit: 30 })
    const { datas } = await getTrendAccountApi(params)
    colorTableDatasList.value = datas
  } finally {
    loading.value = false
  }
}
const handleElement = async () => {
  try {
    loading.value = true
    const { element, heatIndex, pushTime, platId } = ruleForm
    const [dateStart, dateEnd] = pushTime || []
    const params = {} as any
    Object.assign(params, { dateStart, dateEnd, heatIndex, platId, element, limit: 30 })
    const { datas } = await getTrendAccountApi(params)
    elementTableDatasList.value = datas
  } finally {
    loading.value = false
  }
}
const handleSearch = async () => {
  await unref(elformRef)?.validate(async (isValid) => {
    if (isValid) {
      try {
        loading.value = true
        const { pushTime } = ruleForm
        const [dateStart, dateEnd] = pushTime || []
        Object.assign(ruleForm, { dateStart, dateEnd, element: '', colorScheme: '' })
        const { datas } = await getTrendAccountApi(ruleForm)
        tableList.value = datas
        getColor()
        getElement()
      } finally {
        loading.value = false
      }
    }
  })
}
const reset = () => {
  elformRef.value?.resetFields()
  ruleForm.element = ''
  ruleForm.colorScheme = ''
  getDate()
  tagOptions.value = SM_HEAT_INDEX.value
  handleSearch()
}
const colorOptions = ref([] as any)
const getColor = async () => {
  await unref(elformRef)?.validate(async (isValid) => {
    if (isValid) {
      const { heatIndex, pushTime, platId } = ruleForm
      const [dateStart, dateEnd] = pushTime || []
      const params = {} as any
      Object.assign(params, { dateStart, dateEnd, heatIndex, platId })
      const { datas } = await getSocialKeyColorApi(params)
      const arr = [] as any
      for (let n = 0; n < datas.length; n++) {
        arr.push(colorShemaList.value.find((i) => i.code === datas[n]))
      }
      colorOptions.value = arr
      ruleForm.colorScheme = arr?.[0]?.code || ''
      handleColor()
    }
  })
}
const elementOptions = ref([] as any)
const getElement = async () => {
  await unref(elformRef)?.validate(async (isValid) => {
    if (isValid) {
      const { heatIndex, pushTime, platId } = ruleForm
      const [dateStart, dateEnd] = pushTime || []
      const params = {} as any
      Object.assign(params, { dateStart, dateEnd, heatIndex, platId })
      const { datas } = await getSocialKeyElementApi(params)
      const arr = [] as any
      for (let n = 0; n < datas.length; n++) {
        arr.push(elementList.value.find((i) => i.code === datas[n]))
      }
      elementOptions.value = arr
      ruleForm.element = arr?.[0]?.code || ''
      handleElement()
    }
  })
}

onMounted(async () => {
  getDate()
  tagOptions.value = SM_HEAT_INDEX.value
  getProductTinyCategory()
  handleSearch()
})

const handlePlat = (e: number) => {
  if (e === 2) {
    tagOptions.value = SM_HEAT_INDEX.value?.filter((item) => item.dictCnName !== '播放')
  } else {
    tagOptions.value = SM_HEAT_INDEX.value
  }
}
</script>

<template>
  <ContentWrap>
    <ElRow>
      <ElCol :span="20">
        <ElForm ref="elformRef" :model="ruleForm" :rules="rules" label-width="100px">
          <ElRow>
            <ElCol :span="8">
              <ElFormItem label="选择时间段" prop="pushTime">
                <ElDatePicker
                  v-model="ruleForm.pushTime"
                  :clearable="false"
                  format="YYYY-MM-DD"
                  type="daterange"
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="平台" prop="platId">
                <ElSelect v-model="ruleForm.platId" @change="handlePlat">
                  <ElOption
                    v-for="item in SM_PLATFORM"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="Number(item.dictValue)"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow>
            <ElCol :span="12">
              <ElFormItem label="账号类型" prop="accountType">
                <ElRadioGroup v-model="ruleForm.accountType">
                  <ElRadioButton
                    v-for="item in SM_ACCOUNT_TYPE"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="粉丝量级" prop="fanScale">
                <ElRadioGroup v-model="ruleForm.fanScale">
                  <ElRadioButton
                    v-for="item in SM_FANS_SCALE"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="热度指标" prop="heatIndex">
                <ElRadioGroup v-model="ruleForm.heatIndex">
                  <ElRadioButton
                    v-for="item in tagOptions"
                    :key="item.dictValue"
                    :label="item.dictCnName"
                    :value="item.dictValue"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
      </ElCol>
      <ElCol :span="4">
        <ElButton v-loading="loading" type="primary" @click="handleSearch">
          <Icon class="mr-1" icon="ep:search" />
          查询
        </ElButton>
        <ElButton v-loading="loading" @click="reset">
          <Icon class="mr-1" icon="ep:refresh-right" />
          重置
        </ElButton>
      </ElCol>
    </ElRow>
    <div v-loading="loading" class="mb-5 mt-5"
      ><ElRow>
        <ElCol :span="16"><div class="titleStyle">热度 TOP50</div></ElCol>
      </ElRow>
      <div class="flex flex-wrap mb-5 mt-2">
        <StyleDetail
          v-if="tableList.length > 0"
          :data="tableList"
          :heatIndex="ruleForm.heatIndex"
          :platId="ruleForm.platId"
          :type="'account'"
        />
        <div v-else>暂无数据</div>
      </div>
    </div>
    <div v-loading="loading" class="mb-5 mt-5">
      <ElRow>
        <ElCol :span="4"><div class="titleStyle">重点颜色 TOP30</div></ElCol>
        <ElCol :span="12">
          <ElFormItem prop="colorScheme">
            <ElRadioGroup v-model="ruleForm.colorScheme" @change="handleColor">
              <ElRadio
                v-for="item in colorOptions"
                :key="item.code"
                :label="item.cnDesc"
                :value="item.code"
              />
            </ElRadioGroup>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <div class="flex flex-wrap mb-5">
        <StyleDetail
          v-if="colorTableDatasList.length > 0"
          :data="colorTableDatasList"
          :heatIndex="ruleForm.heatIndex"
          :platId="ruleForm.platId"
          :type="'account'"
        />
        <div v-else>暂无数据</div>
      </div>
    </div>
    <div v-loading="loading" class="mb-5 mt-5">
      <ElRow>
        <ElCol :span="4"><div class="titleStyle">重点元素 TOP30</div></ElCol>
        <ElCol :span="12">
          <ElFormItem prop="element">
            <ElRadioGroup v-model="ruleForm.element" @change="handleElement">
              <ElRadio
                v-for="item in elementOptions"
                :key="item.code"
                :label="item.cnDesc"
                :value="item.code"
              />
            </ElRadioGroup>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <div class="flex flex-wrap mb-5">
        <StyleDetail
          v-if="elementTableDatasList.length > 0"
          :data="elementTableDatasList"
          :heatIndex="ruleForm.heatIndex"
          :platId="ruleForm.platId"
          :type="'account'"
        />
        <div v-else>暂无数据</div>
      </div>
    </div>
  </ContentWrap>
</template>
<style lang="less" scoped>
:deep(.el-radio-button) {
  margin: 0 8px 4px 0 !important;

  .el-radio-button__inner {
    border: none;
  }
}

:deep(.el-radio-button:last-child),
:deep(.el-radio-button:first-child) {
  .el-radio-button__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }
}

.titleStyle {
  font-size: 24px;
  font-weight: bold;
}
</style>
