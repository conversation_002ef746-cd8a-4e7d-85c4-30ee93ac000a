<script setup lang="ts">
import { closeCurrentTag } from '@/utils/routerHelper'
import TrendsReport from './components/TrendsReport.vue'
import SimilarTable from './components/SimilarTable.vue'
import StyleDetail from './components/StyleDetail.vue'
import { useRouter, onBeforeRouteUpdate } from 'vue-router'
import { getSameSocialStyleApi, getqueryVectorIdsByBizIdApi, getStyleSameApi } from '@/api/trends'
import { ElMessage } from 'element-plus'

const { push, currentRoute } = useRouter()
const scrollToTop = () => {
  document.getElementById('social')?.scrollIntoView()
}
onMounted(() => {
  scrollToTop()
})
// 在路由更新之前触发scrollToTop方法
onBeforeRouteUpdate(scrollToTop)

const Rows = ref([{ comment: 1 }] as any)
const srcList = ref([] as any)
const similarDatas = ref([] as any)
const transportDatas = ref({} as any)
// 获取详情
const getDatas = async () => {
  const itemObject = JSON.parse(currentRoute.value.query.datas as string)
  Rows.value = [itemObject]
  const { contentImgId } = itemObject
  // 获取趋势统计数据
  getTrendsReport(itemObject)
  // 获取社媒相似款
  getSocialTable(itemObject)
  // 获取美迈相似款
  getMmtTable(contentImgId)
}
const getTrendsReport = (itemObject) => {
  const { productTinyCategory, colorScheme, element } = itemObject
  const contentSourceType = currentRoute.value.query.type
    ? currentRoute.value.query.type === 'account'
      ? '01'
      : currentRoute.value.query.type === 'tag'
      ? '02'
      : ''
    : ''
  Object.assign(transportDatas.value, {
    productTinyCategory,
    colorScheme,
    element,
    platId: currentRoute.value.query.platId,
    heatIndex: currentRoute.value.query.heatIndex,
    contentSourceType
  })
}
const getSocialTable = async (itemObject) => {
  const { productTinyCategory, colorScheme, element } = itemObject
  const params = {} as any
  Object.assign(params, { productTinyCategory, colorScheme, element, page: 1, pageSize: 10 })
  const {
    datas: { rows: socialSimilarDatas }
  } = await getSameSocialStyleApi(params)
  srcList.value = socialSimilarDatas?.map((i) => i.contentImgOssUrl).splice(0, 3)
}
const getMmtTable = async (contentImgId) => {
  const { datas: mmtStyleVectorIds } = await getqueryVectorIdsByBizIdApi({
    bizId: contentImgId,
    from: '01'
  })
  if (mmtStyleVectorIds.length < 1) return ElMessage.warning('美迈相似款暂无数据')
  const { datas: mmtSimilarDatas } = await getStyleSameApi({
    mmtStyleVectorIds
  })
  similarDatas.value = mmtSimilarDatas
}

const goBack = () => {
  closeCurrentTag()
  push({
    name: 'SocialTrendsPage'
  })
}

onMounted(() => {
  getDatas()
})
</script>
<template>
  <ContentWrap>
    <div class="word-style" id="social">社媒相似款</div>
    <div style="display: flex; align-items: center">
      <StyleDetail :data="Rows" :type="'detail'" />
      <ElImage
        v-for="(item, index) in srcList"
        :src="item"
        hide-on-click-modal
        :key="item"
        style="height: 240px; margin-left: 50px"
        :preview-src-list="srcList"
        :initial-index="index"
      />
    </div>
    <el-divider />
    <div class="word-style">美迈相似款</div>
    <SimilarTable :similarDatas="similarDatas" />
    <el-divider />
    <div class="word-style">趋势统计数据</div>
    <TrendsReport :transportDatas="transportDatas" />
    <div style="display: flex; justify-content: center">
      <el-button type="primary" @click="goBack">关闭</el-button>
    </div>
  </ContentWrap>
</template>
<style lang="less" scoped>
.word-style {
  margin-bottom: 6px;
  font-size: 24px;
  font-weight: bold;
}
</style>
