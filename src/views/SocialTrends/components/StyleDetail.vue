<script lang="ts" setup>
import { computed } from 'vue'
import WordCloud from './WordCloud.vue'
import SimilarStyle from '@/views/StyleLibrary/components/similarStyle.vue'
import { updateCollectFlagApi } from '@/api/trends'
import { StyleCommentData } from '@/api/trends/types'
import { getQMSDict } from '@/utils'
import { getTypeApi } from '@/api/trendsStatistics'
import { ElMessage } from 'element-plus'

const { push } = useRouter()
const emit = defineEmits(['update', 'refresh'])
const productTinyCategoryList = ref([] as any)
const colorShemaList = ref([] as any)
const elementList = ref([] as any)
const heelTypeList = ref([] as any)
const topCapList = ref([] as any)
const PRODUCT_ZH_CATEGORY = ref(getQMSDict('PRODUCT_ZH_CATEGORY'))
const SM_PLATFORM = ref(getDictOptions('SM_PLATFORM'))
const optionsDatas = {}
const getOptions = async () => {
  const { datas: productTinyCategoryDatas = [] } = await getTypeApi('PRODUCT_TINY_CATEGORY')
  const { datas: colorShemaDatas } = await getTypeApi('COLOR_SCHEME')
  const { datas: elementDatas } = await getTypeApi('ELEMENT ')
  const { datas: heelTypeDatas } = await getTypeApi('HEEL_TYPE')
  const { datas: topCapDatas } = await getTypeApi('TOE_CAP')
  productTinyCategoryList.value = productTinyCategoryDatas
  colorShemaList.value = colorShemaDatas
  elementList.value = elementDatas
  heelTypeList.value = heelTypeDatas
  topCapList.value = topCapDatas
  Object.assign(optionsDatas, {
    productTinyCategoryDatas,
    colorShemaDatas,
    elementDatas,
    heelTypeDatas,
    topCapDatas
  })
}

const props = defineProps({
  data: {
    type: Array as any,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: true
  },
  type: {
    type: String
  },
  isChecked: {
    type: Boolean,
    default: false
  },
  isBuyer: {
    type: Boolean,
    default: false
  },
  isSocial: {
    type: Boolean,
    default: false
  },
  platId: {
    type: Number
  },
  heatIndex: {
    type: String
  }
})
const tableData = computed({
  get: () => props.data.map((i) => Object.assign(i, { checked: false })),
  set: (val) => {
    emit('update', val)
  }
})
onMounted(() => {
  getOptions()
})

const CommentVisible = ref(false)
const wordData = ref([] as StyleCommentData[])
// const handleClickComment = async (row) => {
//   const contentId = row.contentId
//   // 调用词云接口
//   const { datas } = await getStyleCommentWordSegment(contentId)
//   if (datas) {
//     wordData.value = datas
//     CommentVisible.value = true
//   } else {
//     ElMessage.warning('暂无数据')
//   }
// }
const normalSchema = reactive([
  {
    field: 'likesNum',
    label: '点赞'
  },
  {
    field: 'contentCrawledTime',
    label: '抓取时间'
  },
  {
    field: 'commentsNum',
    label: '评论'
  },
  {
    field: 'productTinyCategory',
    label: '产品小类'
  },
  {
    field: 'collectsNum',
    label: '收藏'
  },
  {
    field: 'colorScheme',
    label: '色系'
  },
  {
    field: 'viewsNum',
    label: '播放'
  },
  {
    field: 'element',
    label: '元素'
  },
  {
    field: 'transmitNum',
    label: '转发'
  },
  {
    field: 'heelType',
    label: '跟型'
  },
  {
    field: 'tags',
    label: 'tag'
  },
  {
    field: 'toeCap',
    label: '鞋头'
  }
])
const buyerShema = reactive([
  {
    field: 'createByName',
    label: '上传用户'
  },
  {
    field: 'productTinyCategory',
    label: '产品小类'
  },
  {
    field: 'brand',
    label: '品牌'
  },
  {
    field: 'colorScheme',
    label: '色系'
  },
  {
    field: 'productLargeCategory',
    label: '产品大类'
  },
  {
    field: 'element',
    label: '元素'
  },
  {
    field: 'usdPrice',
    label: '价格'
  },
  {
    field: 'heelType',
    label: '跟型'
  },
  {
    field: 'createTime',
    label: '上传时间'
  },
  {
    field: 'toeCap',
    label: '鞋头'
  }
])
const socialSchema = reactive([
  {
    field: 'likesNum',
    label: '点赞'
  },
  {
    field: 'contentCrawledTime',
    label: '抓取时间'
  },
  {
    field: 'commentsNum',
    label: '评论'
  },
  {
    field: 'tags',
    label: 'tag'
  },
  {
    field: 'collectsNum',
    label: '收藏'
  },

  {
    field: 'viewsNum',
    label: '播放'
  },
  {
    field: 'transmitNum',
    label: '转发'
  },
  {
    field: 'starAndDetail'
  },
  {
    field: 'contentUrl',
    label: '链接'
  }
])
const socialDetailSchema = reactive([
  {
    field: 'likesNum',
    label: '点赞'
  },
  {
    field: 'contentCrawledTime',
    label: '抓取时间'
  },
  {
    field: 'commentsNum',
    label: '评论'
  },
  {
    field: 'productTinyCategory',
    label: '产品小类'
  },
  {
    field: 'collectsNum',
    label: '收藏'
  },
  {
    field: 'colorScheme',
    label: '色系'
  },
  {
    field: 'viewsNum',
    label: '播放'
  },
  {
    field: 'element',
    label: '元素'
  },
  {
    field: 'transmitNum',
    label: '转发'
  },
  {
    field: 'heelType',
    label: '跟型'
  },
  {
    field: 'tags',
    label: 'tag'
  },
  {
    field: 'toeCap',
    label: '鞋头'
  },
  {
    field: 'contentUrl',
    label: '链接'
  },
  {
    field: 'star'
  }
])

const schema = props.isBuyer
  ? buyerShema
  : props.isSocial
  ? props.type === 'social'
    ? socialSchema
    : socialDetailSchema
  : normalSchema
const SimilarVisible = ref(false)
const getImgDatas = {}
const toDetail = async (item) => {
  if (props.type === 'buyer') {
    SimilarVisible.value = true
    Object.assign(getImgDatas, item)
  } else if (props.type && ['social', 'detail'].includes(props.type)) {
    return
  } else {
    push({
      name: 'TrendStatistics',
      query: {
        datas: JSON.stringify(item),
        platId: props.platId,
        type: props.type,
        heatIndex: props.heatIndex
      }
    })
  }
}

// 展示产品小类+色系
const handleShow = (row) => {
  const { productTinyCategory, colorScheme } = row
  const productTinyCategoryName =
    productTinyCategoryList.value?.find((i) => i.code === productTinyCategory)?.cnDesc || ''
  const colorSchemeName = colorShemaList.value?.find((i) => i.code === colorScheme)?.cnDesc || ''
  return productTinyCategoryName + ',' + colorSchemeName
}
const goDetail = (row) => {
  push({
    name: 'SocialDetail',
    query: {
      datas: JSON.stringify(row)
    }
  })
}

const handleStar = async (row) => {
  row.collectFlag = !row.collectFlag

  const { id, collectFlag } = row

  ElMessage.closeAll()
  await updateCollectFlagApi({ id, collectFlag })
  emit('refresh')

  if (row.collectFlag)
    ElMessage({
      message: '收藏成功',
      type: 'success',
      duration: 1000
    })
  else
    ElMessage({
      message: '已取消收藏',
      type: 'warning',
      duration: 1000
    })
}
</script>
<template>
  <div
    v-for="(item, index) in tableData"
    :key="index"
    :gutter="20"
    class="flex flex-wrap justify-start"
  >
    <ElCard
      :body-style="{ padding: '0px', width: '100%', height: '100%' }"
      :class="
        type === 'buyer' || type === 'social'
          ? 'w-[380px] m-2 h-[380px]'
          : type === 'detail' && isSocial
          ? 'w-[380px] m-2 h-[440px]'
          : 'w-[380px] m-2 h-[420px]'
      "
    >
      <div>
        <div class="flex justify-center border-b-2">
          <div v-if="type === 'social'" style="margin: 5px">
            <el-tag>{{ handleShow(item) }}</el-tag>
            <el-tag>
              {{ SM_PLATFORM?.find((i) => Number(i.dictValue) === item.platId)?.dictCnName }}
            </el-tag>
          </div>
          <ElImage
            :preview-src-list="
              type === 'social' || type === 'detail' ? [item.contentImgOssUrl] : []
            "
            :src="type === 'buyer' ? item.styleImgUrl : item.contentImgOssUrl"
            class="h-[180px] p-[6px] w-[220px]"
            fit="contain"
            lazy
            hide-on-click-modal
            @click="toDetail(item)"
          />
          <div v-if="isSocial" @click="handleStar(item)">
            <Icon v-if="!item.collectFlag" class="mt-[5px]" color="#0079fe" icon="ep:star" />
            <Icon v-else :size="25" class="mt-[3px]" color="#f59a23" icon="ep:star-filled" />
          </div>
          <div v-if="isChecked" class="h-4 flex flex-row-reverse ml-3" style="margin-right: 5px">
            <ElCheckbox v-model="item.checked" />
          </div>
        </div>
      </div>
      <div>
        <Descriptions :data="item" :schema="schema" class="my-descriptions">
          <template #createByName="{ row }">
            <el-tooltip :content="row.createByName" effect="dark" placement="top-start">
              <span>{{ row.createByName }}</span>
            </el-tooltip></template
          >
          <template #brand="{ row }">
            <el-tooltip :content="row.brand" effect="dark" placement="top-start">
              <span>{{ row.brand }}</span>
            </el-tooltip></template
          >
          <template #createTime="{ row }">
            <el-tooltip :content="row.createTime" effect="dark" placement="top-start">
              <span>{{ row.createTime }}</span>
            </el-tooltip></template
          >
          <template #contentCrawledTime="{ row }">
            <el-tooltip :content="row.contentCrawledTime" effect="dark" placement="top-start">
              <span>{{ row.contentCrawledTime }}</span>
            </el-tooltip></template
          >
          <template #tags="{ row }">
            <el-tooltip :content="row.tags?.join(',')" effect="dark" placement="top-start">
              <span>{{ row.tags?.join(',') }}</span>
            </el-tooltip></template
          >
          <template #productTinyCategory="{ row }">
            {{ productTinyCategoryList?.find((i) => i.code === row.productTinyCategory)?.cnDesc }}
          </template>
          <template #colorScheme="{ row }">
            {{ colorShemaList?.find((i) => i.code === row.colorScheme)?.cnDesc }}
          </template>
          <template #productLargeCategory="{ row }">
            {{ PRODUCT_ZH_CATEGORY?.find((i) => i.value === row.productLargeCategory)?.label }}
          </template>
          <template #element="{ row }">
            {{ elementList?.find((i) => i.code === row.element)?.cnDesc }}
          </template>
          <template #heelType="{ row }">
            {{ heelTypeList?.find((i) => i.code === row.heelType)?.cnDesc }}
          </template>
          <template #toeCap="{ row }">
            {{ topCapList?.find((i) => i.code === row.toeCap)?.cnDesc }}
          </template>
          <template #starAndDetail-label="{ row }">
            <div style="color: #409eff">{{ row.collectFlag ? '已收藏' : '未收藏' }}</div>
          </template>
          <template #starAndDetail="{ row }">
            <el-button size="small" text type="primary" @click="goDetail(row)">查看详情</el-button>
          </template>
          <template #star="{ row }">
            <div style="color: #409eff">{{ row.collectFlag ? '已收藏' : '未收藏' }}</div>
          </template>
          <template #contentUrl="{ row }">
            <el-link :href="row.contentUrl" target="_blank" type="primary">{{
              row.contentUrl
            }}</el-link>
          </template>
        </Descriptions>
      </div>
    </ElCard>
  </div>
  <WordCloud v-model="CommentVisible" :wordData="wordData" />
  <SimilarStyle
    v-model="SimilarVisible"
    :getImgDatas="getImgDatas"
    :optionsDatas="optionsDatas"
    :similarType="type"
  />
</template>
<style lang="less" scoped>
:deep(.el-descriptions__content) {
  max-width: 100px !important;
  padding: 5px 10px !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.el-descriptions__label) {
  padding: 5px 10px !important;
}
</style>
