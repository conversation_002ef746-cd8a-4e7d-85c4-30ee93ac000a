<script lang="ts" setup>
import { Echart } from '@/components/Echart'
import { ref } from 'vue'
import { omit } from 'lodash-es'
import { ElForm, FormInstance } from 'element-plus'
import { useValidator } from '@/hooks/web/useValidator'
import {
  getColorSchemeApi,
  getElementApi,
  getPieColorSchemeApi,
  getPieElementApi,
  getProductTinyCategoryApi,
  getTagTopNApi,
  getTypeApi
} from '@/api/trendsStatistics'
import {
  ColorSchemeOptions,
  ElementOptions,
  PieColorSchemeOptions,
  PieElementOptions,
  ProductTinyCategoryOptions,
  TagTopNOptions
} from './index'

const { required } = useValidator()

const props = defineProps({
  transportDatas: {
    type: Object
  }
})

const ruleFormRef = ref<FormInstance>()
const loading = ref(false)
const ruleForm: any = reactive({
  pushTime: [],
  heatIndex: ''
})

const rules = {
  pushTime: [required()],
  heatIndex: [required()]
}
// 获取颜色元素
const colorShemaList = ref([] as any)
const elementList = ref([] as any)
const getOptions = async () => {
  const { datas: colorShemaDatas } = await getTypeApi('COLOR_SCHEME')
  const { datas: elementDatas } = await getTypeApi('ELEMENT')
  colorShemaList.value = colorShemaDatas
  elementList.value = elementDatas
}
const search = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    try {
      if (!valid) return
      loading.value = true
      const params = {}
      const { pushTime } = ruleForm
      if (!props.transportDatas) return
      const { productTinyCategory, colorScheme, element, platId, contentSourceType } =
        props.transportDatas
      const [dateStart, dateEnd] = pushTime || []
      Object.assign(params, omit(ruleForm, 'pushTime'), {
        dateStart,
        dateEnd,
        productTinyCategory,
        colorScheme,
        element,
        platId,
        contentSourceType
      })
      await getOptions()
      // 调用所有接口
      getProductTinyCategoryList(params)
      getColorSchemeList(params)
      getElementList(params)
      getTagTopNList(params)
      getPieElementList(params)
      gePieColorSchemeList(params)
    } finally {
      loading.value = false
    }
  })
}

watch(
  () => props.transportDatas,
  (newVal) => {
    if (newVal) {
      //这里是数据更新变化后需要执行的动作
      Object.assign(ruleForm, { heatIndex: props.transportDatas?.heatIndex })
      search()
    }
  },
  { immediate: true, deep: true }
)
const reset = async () => {
  ruleFormRef.value?.resetFields()
  getDate()
  search()
}

const getDate = () => {
  const nowDate = new Date()
  const year = nowDate.getFullYear()
  const month = nowDate.getMonth() + 1
  const lastDay = new Date(year, month, 0).getDate()
  const newMonth = month < 10 ? '0' + month : month
  ruleForm.pushTime = [[year, newMonth, '01'].join('-'), [year, newMonth, lastDay].join('-')]
}
//处理柱状返回数据
const getProductTinyCategoryList = async (params) => {
  const { datas } = await getProductTinyCategoryApi(params)
  const monthList: string[] = []
  const totalList: number[] = []
  datas &&
    datas.map((v) => {
      monthList.push(v.month)
      totalList.push(v.count)
    })
  Object.assign(ProductTinyCategoryOptions, {
    xAxis: [
      {
        type: 'category',
        data: monthList,
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    series: [
      {
        name: '数量',
        type: 'bar',
        barWidth: '60%',
        data: totalList
      }
    ]
  })
}
const getColorSchemeList = async (params) => {
  const { datas } = await getColorSchemeApi(params)
  const monthList: string[] = []
  const totalList: number[] = []
  datas &&
    datas.map((v) => {
      monthList.push(v.month)
      totalList.push(v.count)
    })
  Object.assign(ColorSchemeOptions, {
    xAxis: [
      {
        type: 'category',
        data: monthList,
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    series: [
      {
        name: '数量',
        type: 'bar',
        barWidth: '60%',
        data: totalList
      }
    ]
  })
}
const getElementList = async (params) => {
  const { datas } = await getElementApi(params)
  const monthList: string[] = []
  const totalList: number[] = []
  datas &&
    datas.map((v) => {
      monthList.push(v.month)
      totalList.push(v.count)
    })
  Object.assign(ElementOptions, {
    xAxis: [
      {
        type: 'category',
        data: monthList,
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    series: [
      {
        name: '数量',
        type: 'bar',
        barWidth: '60%',
        data: totalList
      }
    ]
  })
}
const getTagTopNList = async (params) => {
  const { datas } = await getTagTopNApi(params)
  const monthList: string[] = []
  const totalList: number[] = []
  datas &&
    datas.map((v) => {
      monthList.push(v.tag)
      totalList.push(v.count)
    })
  Object.assign(TagTopNOptions, {
    xAxis: [
      {
        type: 'category',
        data: monthList,
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    series: [
      {
        name: '数量',
        type: 'bar',
        barWidth: '60%',
        data: totalList
      }
    ]
  })
}
// 处理饼状返回数据
const getPieElementList = async (params) => {
  const { datas } = await getPieElementApi(params)
  const vendorData = [] as any
  datas &&
    datas.map((item) => {
      if (!item) return null
      const value = item.count
      const name = elementList.value?.find((i) => i.code === item.info).cnDesc
      vendorData.push(Object.assign({ value, name }))
    })
  Object.assign(PieElementOptions, {
    series: [
      {
        name: '数量',
        type: 'pie',
        radius: '55%',
        center: ['60%', '60%'],
        data: vendorData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  })
}
// 处理玫瑰图返回数据
const gePieColorSchemeList = async (params) => {
  const { datas } = await getPieColorSchemeApi(params)
  const vendorData = [] as any
  datas &&
    datas.map((item) => {
      if (!item) return null
      const value = item.count
      const name = colorShemaList.value?.find((i) => i.code === item.info).cnDesc
      vendorData.push(Object.assign({ value, name }))
    })
  Object.assign(PieColorSchemeOptions, {
    series: [
      {
        name: '数量',
        type: 'pie',
        radius: '55%',
        center: ['60%', '60%'],
        data: vendorData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  })
}

onMounted(() => {
  getDate()
})
</script>

<template>
  <ElRow>
    <Elcol :span="20" style="width: 800px">
      <ElForm
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        center
        label-width="100px"
        style="margin-right: 20px"
      >
        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="推款时间" prop="pushTime">
              <ElDatePicker
                v-model="ruleForm.pushTime"
                :clearable="false"
                format="YYYY-MM-DD"
                type="daterange"
                value-format="YYYY-MM-DD"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="热度指标" prop="heatIndex">
              <SelectPlus v-model="ruleForm.heatIndex" api-key="SM_HEAT_INDEX" />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </Elcol>
    <ElCol :span="4">
      <ElButton :loading="loading" type="primary" @click="search">
        <Icon class="mr-1" icon="ep:search" />
        查询
      </ElButton>
      <ElButton @click="reset">
        <Icon class="mr-1" icon="ep:refresh-right" />
        重置
      </ElButton>
    </ElCol>
  </ElRow>

  <ElRow :gutter="40" class="mb-10" justify="space-around">
    <ElCol :span="12">
      <ElCard> <Echart :height="300" :options="ProductTinyCategoryOptions" /> </ElCard
    ></ElCol>
    <ElCol :span="12">
      <ElCard>
        <Echart :height="300" :options="ColorSchemeOptions" />
      </ElCard>
    </ElCol>
  </ElRow>
  <ElRow :gutter="40" class="mb-10" justify="space-around">
    <ElCol :span="12">
      <ElCard>
        <Echart :height="300" :options="ElementOptions" />
      </ElCard>
    </ElCol>
    <ElCol :span="12">
      <ElCard>
        <Echart :height="300" :options="TagTopNOptions" />
      </ElCard>
    </ElCol>
  </ElRow>
  <ElRow :gutter="40" class="mb-10" justify="space-around">
    <ElCol :span="12">
      <ElCard>
        <Echart :height="300" :options="PieElementOptions" />
      </ElCard>
    </ElCol>
    <ElCol :span="12">
      <ElCard>
        <Echart :height="300" :options="PieColorSchemeOptions" />
      </ElCard>
    </ElCol>
  </ElRow>
</template>
