<script setup lang="ts">
import { computed } from 'vue'
import { VxeTablePropTypes } from 'vxe-table'
import { propTypes } from '@/utils/propTypes'
import { TagInsertApi, TagUpdateApi } from '@/api/trends'
import { getDictOptions } from '@/hooks/autoImport/useDictAll'

const props = defineProps({
  modelValue: propTypes.bool.def(false),
  editDatas: propTypes.array.def([]) as any,
  handleType: propTypes.string.def('')
})
const SM_PLATFORM = ref(getDictOptions('SM_PLATFORM'))
const SM_TAG_TYPE = ref(getDictOptions('SM_TAG_TYPE'))
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const xTableData = computed(() => props.editDatas)

const validRules = ref<VxeTablePropTypes.EditRules>({
  tag: [{ required: true, message: '请输入标签' }],
  platId: [{ required: true, message: '请选择平台' }],
  tagType: [{ required: true, message: '请选择标签类型' }]
})

const closed = () => {
  emit('update:modelValue', false)
  emit('refresh')
}

const xTable = ref()

const getIndex = (row) => {
  const $table = xTable.value
  let currentIndex = $table.getRowIndex(row)
  if (currentIndex !== 0) return true
  else return false
}

const handleAdd = async () => {
  const $table = xTable.value
  if ($table) {
    const record = {}
    await $table.insertAt(record, -1)
  }
}
const handleDelete = (row) => {
  const $table = xTable.value
  if ($table) {
    $table.remove(row)
  }
}
const loading = ref(false)
const onSubmit = async () => {
  const $table = xTable.value
  if ($table) {
    const errMap = await $table.validate(true)
    if (errMap) {
    } else {
      const tags = xTable.value?.getTableData().tableData
      if (props.handleType === 'add') {
        try {
          loading.value = true
          await TagInsertApi({ tags })
          closed()
        } finally {
          loading.value = false
        }
      } else {
        try {
          loading.value = true
          await TagUpdateApi({ tags })
          closed()
        } finally {
          loading.value = false
        }
      }
    }
  }
}
</script>
<template>
  <vxe-modal
    v-model="visible"
    title="新增/修改标签"
    width="60%"
    :esc-closable="false"
    showFooter
    destroy-on-close
  >
    <vxe-table
      border
      show-overflow
      ref="xTable"
      :data="xTableData"
      height="300"
      :edit-rules="validRules"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
    >
      <vxe-column type="seq" width="60" />
      <vxe-column
        field="tag"
        title="标签"
        :edit-render="{}"
        :title-prefix="{ message: '标签无需带#', icon: 'vxe-icon-info-circle-fill' }"
      >
        <template #edit="{ row }">
          <vxe-input
            v-model="row.tag"
            type="text"
            placeholder="请输入"
            maxlength="300"
            :disabled="handleType === 'edit'"
          />
        </template>
      </vxe-column>
      <vxe-column field="platId" title="平台" :edit-render="{}">
        <template #default="{ row }">
          <span>{{
            SM_PLATFORM?.find((val) => Number(val.dictValue) === row.platId)?.dictCnName
          }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.platId" transfer :disabled="handleType === 'edit'" filterable>
            <vxe-option
              v-for="item in SM_PLATFORM"
              :key="item.dictValue"
              :value="Number(item.dictValue)"
              :label="item.dictCnName"
            />
          </vxe-select>
        </template>
      </vxe-column>

      <vxe-column field="tagType" title="标签类型" :edit-render="{}">
        <template #default="{ row }">
          <span>{{ SM_TAG_TYPE?.find((val) => val.dictValue === row.tagType)?.dictCnName }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.tagType" transfer filterable>
            <vxe-option
              v-for="item in SM_TAG_TYPE"
              :key="item.dictValue"
              :value="item.dictValue"
              :label="item.dictCnName"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column title="操作" width="100" v-if="handleType === 'add'">
        <template #default="{ row }">
          <vxe-button type="text" icon="vxe-icon-square-plus" @click="handleAdd()" />
          <vxe-button
            type="text"
            v-if="getIndex(row)"
            icon="vxe-icon-square-minus"
            @click="handleDelete(row)"
          />
        </template>
      </vxe-column>
    </vxe-table>

    <template #footer>
      <el-button @click="closed">取消</el-button>
      <el-button type="primary" @click="onSubmit">确认</el-button>
    </template>
  </vxe-modal>
</template>
