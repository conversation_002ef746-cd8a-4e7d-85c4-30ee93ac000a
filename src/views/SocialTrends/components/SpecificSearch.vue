<script lang="ts" setup>
import { ElRadioButton, ElRadioGroup, FormInstance } from 'element-plus'
import { getTypeApi } from '@/api/trendsStatistics'
import { getDictOptions } from '@/hooks/autoImport/useDictAll'
import {
  ColorCategoryListAPI,
  getColorCategoryList
} from '@/views/basic-library-manage/color-library/api/color-list'

const emit = defineEmits(['search'])
const elformRef = ref<FormInstance | null>(null)
const ruleForm: any = reactive({
  pushTime: [],
  platId: 1,
  productTinyCategory: '',
  colorScheme: '',
  element: '',
  heelType: '',
  toeCap: '',
  heatIndex: '01',
  page: 1,
  pageSize: 10
})
const colorCategoryList = ref<ColorCategoryListAPI.Data[]>([])
const useFetchColorCategoryList = async () => {
  const [error, result] = await getColorCategoryList()
  if (error === null && result?.datas) {
    colorCategoryList.value = result?.datas
  }
}
const pantoneCategoryList = computed(() => {
  return colorCategoryList.value
    .filter((e) => e.value.PANTONE)
    .map((e) => ({ ...e, value: e.value.PANTONE }))
})
const rules = {
  pushTime: [{ required: true, trigger: 'change', message: '必填' }],
  platId: [{ required: true, trigger: 'change', message: '必填' }],
  heatIndex: [{ required: true, trigger: 'change', message: '必填' }]
}

const getDate = () => {
  const nowDate = new Date()
  const year = nowDate.getFullYear()
  const month = nowDate.getMonth() + 1
  const lastDay = new Date(year, month, 0).getDate()
  const newMonth = month < 10 ? '0' + month : month
  ruleForm.pushTime = [[year, newMonth, '01'].join('-'), [year, newMonth, lastDay].join('-')]
}

const handleSearch = async () => {
  await unref(elformRef)?.validate(async (isValid) => {
    if (isValid) {
      const { pushTime } = ruleForm
      const [dateStart, dateEnd] = pushTime || []
      Object.assign(ruleForm, { dateStart, dateEnd })
      emit('search', ruleForm)
    }
  })
}
const reset = async () => {
  elformRef.value?.resetFields()
  getDate()
  tagOptions.value = SM_HEAT_INDEX.value
  handleSearch()
}
const SM_PLATFORM = ref(getDictOptions('SM_PLATFORM'))
const PRODUCT_POPULAT_ELEMENTS = ref(getDictOptions('PRODUCT_POPULAT_ELEMENTS'))
const HEEL_PATTERN = ref(getDictOptions('HEEL_PATTERN'))
const HEEL_HEAD = ref(getDictOptions('HEEL_HEAD'))
const SM_HEAT_INDEX = ref(getDictOptions('SM_HEAT_INDEX'))
const productTinyCategoryList = ref([] as any)
const getProductTinyCategory = async () => {
  const { datas } = await getTypeApi('PRODUCT_TINY_CATEGORY')
  productTinyCategoryList.value = datas
}
onMounted(() => {
  getDate()
  tagOptions.value = SM_HEAT_INDEX.value
  getProductTinyCategory()
  handleSearch()
  useFetchColorCategoryList()
})
const tagOptions = ref([] as any)
const handlePlat = (e: number) => {
  if (e === 2) {
    tagOptions.value = SM_HEAT_INDEX.value?.filter((item) => item.dictCnName !== '播放')
  } else {
    tagOptions.value = SM_HEAT_INDEX.value
  }
}
</script>

<template>
  <ElRow>
    <ElCol :span="20">
      <ElForm ref="elformRef" :model="ruleForm" :rules="rules" label-width="100px">
        <ElRow>
          <ElCol :span="8">
            <ElFormItem label="选择时间段" prop="pushTime">
              <ElDatePicker
                v-model="ruleForm.pushTime"
                :clearable="false"
                format="YYYY-MM-DD"
                type="daterange"
                value-format="YYYY-MM-DD"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="平台" prop="platId">
              <ElSelect v-model="ruleForm.platId" @change="handlePlat">
                <ElOption
                  v-for="item in SM_PLATFORM"
                  :key="item.dictValue"
                  :label="item.dictCnName"
                  :value="Number(item.dictValue)"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="20">
          <ElCol :span="6">
            <ElFormItem label="打标维度" prop="productTinyCategory">
              <ElSelect
                v-model="ruleForm.productTinyCategory"
                clearable
                filterable
                placeholder="产品小类"
              >
                <ElOption
                  v-for="item in productTinyCategoryList"
                  :key="item.code"
                  :label="item.cnDesc"
                  :value="item.code"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="4">
            <ElFormItem label-width="0px" prop="colorScheme">
              <ElSelect v-model="ruleForm.colorScheme" clearable filterable placeholder="色系">
                <ElOption
                  v-for="item in pantoneCategoryList"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="4">
            <ElFormItem label-width="0px" prop="element">
              <ElSelect v-model="ruleForm.element" clearable filterable placeholder="元素">
                <ElOption
                  v-for="item in PRODUCT_POPULAT_ELEMENTS"
                  :key="item.dictValue"
                  :label="item.dictCnName"
                  :value="item.dictValue"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="4">
            <ElFormItem label-width="0px" prop="heelType">
              <ElSelect v-model="ruleForm.heelType" clearable filterable placeholder="跟型">
                <ElOption
                  v-for="item in HEEL_PATTERN"
                  :key="item.dictValue"
                  :label="item.dictCnName"
                  :value="item.dictValue"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="4">
            <ElFormItem label-width="0px" prop="toeCap">
              <ElSelect v-model="ruleForm.toeCap" clearable filterable placeholder="鞋头">
                <ElOption
                  v-for="item in HEEL_HEAD"
                  :key="item.dictValue"
                  :label="item.dictCnName"
                  :value="item.dictValue"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow>
          <ElCol :span="24">
            <ElFormItem label="热度指标" prop="heatIndex">
              <ElRadioGroup v-model="ruleForm.heatIndex">
                <ElRadioButton
                  v-for="item in tagOptions"
                  :key="item.dictCnName"
                  :label="item.dictCnName"
                  :value="item.dictValue"
                />
              </ElRadioGroup>
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </ElCol>
    <ElCol :span="4">
      <ElButton type="primary" @click="handleSearch">
        <Icon class="mr-1" icon="ep:search" />
        查询
      </ElButton>
      <ElButton @click="reset">
        <Icon class="mr-1" icon="ep:refresh-right" />
        重置
      </ElButton>
    </ElCol>
  </ElRow>
</template>
<style lang="less" scoped>
:deep(.el-radio-button) {
  margin: 0 8px 4px 0 !important;

  .el-radio-button__inner {
    border: none;
  }
}

:deep(.el-radio-button:last-child),
:deep(.el-radio-button:first-child) {
  .el-radio-button__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }
}
</style>
