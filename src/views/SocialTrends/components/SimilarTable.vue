<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'

const props = defineProps({
  similarDatas: propTypes.array.def([]) as any
})
const tableData = computed(() => props.similarDatas)
</script>
<template>
  <el-table :data="tableData" border max-height="400" style="width: 100%">
    <el-table-column align="center" label="序号" type="index" width="60" />
    <el-table-column align="center" label="Style" prop="style" width="240" />
    <el-table-column align="center" label="Color" prop="color" width="240" />
    <el-table-column align="center" label="图片" min-width="240" prop="imgUrl">
      <template #default="{ row }">
        <ElImage
          :preview-src-list="[row.imgUrl]"
          :preview-teleported="true"
          hide-on-click-modal
          :src="row.imgUrl"
          lazy
          style="width: 100px; height: 100px"
        />
      </template>
    </el-table-column>
    <el-table-column align="center" label="近30天全渠道销量" prop="saleNum" width="240" />
    <el-table-column align="center" label="库存" prop="inventory" width="240">
      <template #header>
        库存
        <el-tooltip class="box-item" effect="dark" placement="top">
          <template #content>
            平台仓库存+平台仓在途+自有仓库存+自有仓未发<br />+自有仓在途+第三方仓库存+第三方仓在途
          </template>
          <Icon class="ml-[5px]" icon="ep:info-filled" />
        </el-tooltip>
      </template>
    </el-table-column>
  </el-table>
</template>
