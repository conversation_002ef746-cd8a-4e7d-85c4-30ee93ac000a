<script setup lang="ts">
import { Echart } from '@/components/Echart'
import type { EChartsOption } from 'echarts'

const props = defineProps({
  modelValue: {
    type: Boolean
  },
  wordData: {
    type: Array as any,
    default: () => []
  }
})
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
}>()
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const closed = () => {
  emit('update:modelValue', false)
}

const maskImage = new Image()
// 图片在iconfont上找，图片的黑色区域表示被填充
maskImage.src =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAScAAAC3CAYAAABQS5i2AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsIAAA7CARUoSoAAAAjiSURBVHhe7d17qGVVHQdwKwkxM5OykLKyEHtSFBEiESFFYWUUERkUERFlEf0R0R9FRERERESECUWEgUjYg0izN/RCCgM1Ky1TM2tMLC3Nnr/vnH3oOLPvzL13zj1nnb0+H/iy1jnckTvOPb+79tprr3UUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAe+t+QwuLHlY5p/LMyomV2ytXVL5Sua0CsFLHVz5eubvy35H8s3JB5QkVgJV4euW3lbGidGD+Xflk5YQKwJ55buXOylghOlT2VV5bAVi6jJj+UhkrPtvNFyuZpwJYilyWbfdS7nD5feWMCsARu6QyVmh2m0yYn1cB2LXXV8YKzDLyqcrRFYAdyfzQnytjhWVZuaxybAVg286vjBWUZScLN02UA9tyWiXrlMaKyV7k6sqjKgCHdFFlrIjsZa6vKFDAlk6vjBWPVUSBArb02cpY4VhVflExBwXcx0mVrEMaKxqrzE8recAYYL/3VsaKxTpyeeWBFaBz96/k8ZKxQrGuZGIe6NzZlbECse58tAJ0LLsGjBWHFvLGCtChTD7/ozJWGFpIJumfVwE6s5cP+C4rec7vlArQkTyAO1YQWkuewzumMpcR38mzLjA1WfC4yufojjQXVjJJ/ruF9zKq+lDF0gOYkDdUFj/8m5ysjbJHFEzEVytjH/RNzQcqdMKhmtOVUUYOLpjShm/3Vh5TuXX/KyYtK4eZphx8ObWdKDPv9NZZl6lTnKZr8c7XlDgnrxOK03TdPrRT89hKDgGFbmWLkXwQNvnSKBu8jU0sb3oyl6ZA0ZUs/Ht/ZfEJ/qwT+knlXZVN2yjtTZXFD/WUcnclDzTD5D2ncrhtRfKByCLBTSlSWWH9t8rY32UKyS8ODw4zaS+opPCMfQDGksuKd1RaXhR4ZuUPlbHvf2r5YAUm51mVnRSmxeTYo+dXWpKCmcc9NumxlWXky5Vclmcu6uJK5tt+XvlYxcEKbJxcni1jh8jsl5TJ83VLoUzBHPsee8gfR95LMtJ9cQU2xiWVsR/m3SR7Jn2kso75qKdVlvl3mWLy75N5RWjeqypjP8RHmkxAZw7kkZW9lnmlr1XGvg85OL+u2N2ApuUHdHFbjr1Idnj8fCWXWstc7Hpq5d2Vni/fjiTZqQGa9c7K2A/uXmVf5YLKayopLtuVopavf3XlE5UcSjn235ft5zsVNkCPuxJkxfdNlRP3v1qPPFpy85DbKndUIt9bktXpucOUwuQyZPkeWpn/P4dmvLky9htV+km2BD69Ak1xaSRJ1ra9vUKjerusyxFE5hxY9O3K6yq5xI5cSmchZ7ac+X7lmgrsufMrY79Fpe/MH0caO6nmPRXWoKeRUx7ryAridU6Es5nOrXxh1mVVeipOWW/0rVkXduRPlcdX7tr/ipXoaSfMFw0t7FSWdmStGSvUU3E6a2hhN142tKxIL5d1mWfKybGwW3+t5FgqizdXpJeR07OHFnYre0XlecwPV7baeSKr+63oXxLFCbYvBSp7yc+LVHaeyGNGee4xu45mR4rkRxX7m7MtUzuWW9pIVpnfecB7i7FG6gj0MueUfXxyAi6s2gsr35h12YkeilPmAPIbrqc7k7TjqspTZ112oocPbEZMChPr8pSKA0B3oYcP7SlDC+vy8qFlB3ooTo4FYt2c/LILPRSnRw8trMtpleNmXbbrAUM7Za+oWOfEumXu6YRKHh7O1swcRg936y6s5GABaEV2OfhuJRsfpr22wgF6KE45pvqlsy406ZbKvFh9s3JDpXs9FKfvVdzKZZPcWEmxys6cKVYZaXVHcYL2XVnJKvOvV35YubcyeT0Upx9Uzph1YeP9vTIfVaVgTXa+ysgJNlsuAS+tZG41l4CTGVUpTjAdGVVlNHVJ5UuVbJC3sXpYhHnP0MLUZbO7cyqfq+yrXDy83sgN8HooThv92wN2KQXplZWMorI53nmVjfq891Cc7PlM77JjZ3brzA6dG/OsaQ/F6dahhd7lMa4rKnnWr3k9FKebhhaYjaKyEj1t03ooTtcNLTBzcuWiStOf/x6K0zVDC/xflte8ZdZtUw/rnCKXdjadg/vKzaLHDW1zetjPKbKXzjNmXWBwTCWHf2ShcnN6uKyLPF8HHOxtlaNn3bb0UpwydAUOlqPVz5p129JLcXrS0AIHO3dom9JLcXLaL2zt7EpztaCX4pR1HcC4HLzQ3NVFD8UpD0DmuhrY2plD24weitNJQwts7YlD24xeRk7AoZ06tM3ooTgdP7TA1pqblzVyAqK5hZg9FKcujtGBqemhOP1raIGt3TW0zeihON02tMDWcnJLU3ooTjnK2egJDs3IaQ3+U/nVrAtsobkrjB6KU/xsaIFxucJoSi/F6fKhBcb9cmib0UtxytHMzU34QUOuHNpm9FKccurvp2dd4ACZb7pq1m1HL8Up3le5cdYFFnymkhtHTenl9JW50yuXVU7Z/wrIRPiTK83drevl9JW5/APkt0Qu806sPKLSW4GGuZzp+JLKDftfNab3D+axlYymkuxnk20jMqrKGXdJk6dSwC7lptCPK7l7fWmluUnwRUYNh5aN6pKcKz9vH17JqCtbm6Zd7KeFFmQO6dpK1vhdUUlRSn9jnpZQnJZvsVDN2+MW2ocMbZK9ppJ5f/F92K6bK7lES64e2oyKNnr5jOLUrlxyZi+qFLW0eZ3ClX6KV05rTdLPew+u7OTP5E6tIrgZMtrJnebfDLl+oX9dJXOok6M4ESlgmV+bt/Mil0KWfopYCl7afE1Gf+kfKvnzHF5u0txauaWSEVD6Ny28N09zt/r3muLEXklxSiFLoUqbQjd/vVj00n/Q0M9oLkVwPqqbF8z5KHDeRr4u8rX5mlXLaCaXTXmaPxsaZvSS1+nfUcn7t1f2De1WYQuKE1MyL2axWMjGzIvhgVJUDpw0vmfIXIoPAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAjTvqqP8B25QZBcFmvn8AAAAASUVORK5CYII='
const keyWords = ref([] as any)
watch(
  () => props.wordData,
  (val) => {
    keyWords.value = val.map((item) => {
      return Object.assign({ name: item.word, value: item.qty })
    })
  }
)

const max = Math.max(...keyWords.value.map((item) => item.value))

const MonthOptions: EChartsOption = reactive({
  backgroundColor: '#fff',
  tooltip: {
    show: true
  },
  series: [
    {
      type: 'wordCloud',
      // 单词间隔的大小
      gridSize: 0,
      // 字体大小范围
      sizeRange: [24, 60],
      // 文字旋转角度范围
      rotationRange: [0, 0],
      // 自定义词云图形状
      maskImage: maskImage,
      textStyle: {
        color: function (e) {
          var a = [
            Math.round(Math.random() * 255),
            Math.round(Math.random() * 255),
            Math.round(Math.random() * 255),
            e.data.value / max < 0.3 ? 0.3 : e.data.value
          ]
          return 'rgba(' + a.join(',') + ')'
        }
      },
      //设置显示区域的位置以及大小
      left: 'center',
      top: 'center',
      width: '100%',
      height: '100%',
      // 源数据
      data: keyWords,
      // 鼠标放上样式
      emphasis: {
        textStyle: {
          fontWeight: 'bold',
          color: '#ff0000'
        }
      }
    }
  ]
})

maskImage.onload = () => {
  Object.assign(MonthOptions, {
    series: [
      {
        type: 'wordCloud',
        // 单词间隔的大小
        gridSize: 0,
        // 字体大小范围
        sizeRange: [24, 60],
        // 文字旋转角度范围
        rotationRange: [0, 0],
        // 自定义词云图形状
        maskImage: maskImage,
        textStyle: {
          color: function (e) {
            var a = [
              Math.round(Math.random() * 255),
              Math.round(Math.random() * 255),
              Math.round(Math.random() * 255),
              e.data.value / max < 0.3 ? 0.3 : e.data.value
            ]
            return 'rgba(' + a.join(',') + ')'
          }
        },
        //设置显示区域的位置以及大小
        left: 'center',
        top: 'center',
        width: '100%',
        height: '100%',
        // 源数据
        data: keyWords,
        // 鼠标放上样式
        emphasis: {
          textStyle: {
            fontWeight: 'bold',
            color: '#ff0000'
          }
        }
      }
    ]
  })
}
</script>
<template>
  <el-dialog
    :model-value="visible"
    @close="closed"
    center
    width="700"
    :close-on-click-modal="false"
  >
    <Echart :options="MonthOptions" :width="650" />
    <template #footer>
      <el-button type="primary" @click="closed">关闭</el-button>
    </template>
  </el-dialog>
</template>
