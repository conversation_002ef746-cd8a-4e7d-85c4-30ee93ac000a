<script setup lang="ts">
import { computed } from 'vue'
import { VxeTablePropTypes } from 'vxe-table'
import { propTypes } from '@/utils/propTypes'
import { getDictOptions } from '@/hooks/autoImport/useDictAll'
import { AccountInsertApi, AccountUpdateApi } from '@/api/trends'

const props = defineProps({
  modelValue: propTypes.bool.def(false),
  editDatas: propTypes.array.def([]) as any,
  handleType: propTypes.string.def('')
})

const SM_PLATFORM = ref(getDictOptions('SM_PLATFORM'))
const SM_ACCOUNT_TYPE = ref(getDictOptions('SM_ACCOUNT_TYPE'))
const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
  (e: 'refresh'): void
}>()
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const xTableData = computed(() => props.editDatas)
const validRules = ref<VxeTablePropTypes.EditRules>({
  account: [{ required: true, message: '请输入账户名' }],
  platId: [{ required: true, message: '请选择平台' }],
  homePage: [{ required: true, message: '请输入主页链接' }],
  accountType: [{ required: true, message: '请选择账户类型' }]
})

const closed = () => {
  emit('update:modelValue', false)
  emit('refresh')
}

const xTable = ref()

const getIndex = (row) => {
  const $table = xTable.value
  let currentIndex = $table.getRowIndex(row)
  if (currentIndex !== 0) return true
  else return false
}

const handleAdd = async () => {
  const $table = unref(xTable)
  const record = {}
  const table = await $table?.insertAt(record, -1)
  await $table?.setEditCell(table?.row, '')
}
const handleDelete = (row) => {
  const $table = xTable.value
  if ($table) {
    $table.remove(row)
  }
}
const loading = ref(false)
const onSubmit = async () => {
  const $table = xTable.value
  if ($table) {
    const errMap = await $table.validate(true)
    if (errMap) {
    } else {
      const accounts = xTable.value?.getTableData().tableData

      if (props.handleType === 'add') {
        try {
          loading.value = true
          await AccountInsertApi({ accounts })
          closed()
        } finally {
          loading.value = false
        }
      } else {
        try {
          loading.value = true
          await AccountUpdateApi({ accounts })
          closed()
        } finally {
          loading.value = false
        }
      }
    }
  }
}
</script>
<template>
  <vxe-modal
    v-model="visible"
    title="新增/修改账号信息"
    width="60%"
    :esc-closable="false"
    showFooter
    destroy-on-close
  >
    <vxe-table
      border
      show-overflow
      ref="xTable"
      :data="xTableData"
      height="300"
      :edit-rules="validRules"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
    >
      <vxe-column type="seq" width="60" />
      <vxe-column field="account" title="账户名" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input
            v-model="row.account"
            type="text"
            placeholder="请输入"
            maxlength="300"
            :disabled="handleType === 'edit'"
          />
        </template>
      </vxe-column>
      <vxe-column field="platId" title="平台" :edit-render="{}">
        <template #default="{ row }">
          <span>{{
            SM_PLATFORM?.find((val) => Number(val.dictValue) === row.platId)?.dictCnName
          }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.platId" transfer :disabled="handleType === 'edit'" filterable>
            <vxe-option
              v-for="item in SM_PLATFORM"
              :key="item.dictValue"
              :value="Number(item.dictValue)"
              :label="item.dictCnName"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column field="homePage" title="主页链接" :edit-render="{}">
        <template #edit="{ row }">
          <vxe-input
            v-model="row.homePage"
            type="text"
            placeholder="请输入"
            maxlength="300"
            :disabled="handleType === 'edit'"
          />
        </template>
      </vxe-column>
      <vxe-column field="accountType" title="账户类型" :edit-render="{}">
        <template #default="{ row }">
          <span>{{
            SM_ACCOUNT_TYPE?.find((val) => val.dictValue === row.accountType)?.dictCnName
          }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.accountType" transfer filterable>
            <vxe-option
              v-for="item in SM_ACCOUNT_TYPE"
              :key="item.dictValue"
              :value="item.dictValue"
              :label="item.dictCnName"
            />
          </vxe-select>
        </template>
      </vxe-column>
      <vxe-column title="操作" width="100" v-if="handleType === 'add'">
        <template #default="{ row }">
          <vxe-button type="text" icon="vxe-icon-square-plus" @click="handleAdd()" />
          <vxe-button
            type="text"
            v-if="getIndex(row)"
            icon="vxe-icon-square-minus"
            @click="handleDelete(row)"
          />
        </template>
      </vxe-column>
    </vxe-table>

    <template #footer>
      <el-button @click="closed" :loading="loading">取消</el-button>
      <el-button type="primary" @click="onSubmit" :loading="loading">确认</el-button>
    </template>
  </vxe-modal>
</template>
