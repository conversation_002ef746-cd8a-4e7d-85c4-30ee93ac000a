import type { EChartsOption } from 'echarts'
const ProductTinyCategoryOptions: EChartsOption = reactive({
  title: {
    text: '产品小类热度变化统计',
    left: 'left'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisTick: {
        alignWithLabel: true
      }
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: '数量',
      type: 'bar',
      barWidth: '60%',
      data: []
    }
  ]
})
const ColorSchemeOptions: EChartsOption = reactive({
  title: {
    text: '色系热度变化统计',
    left: 'left'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisTick: {
        alignWithLabel: true
      }
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: '数量',
      type: 'bar',
      barWidth: '60%',
      data: []
    }
  ]
})
const ElementOptions: EChartsOption = reactive({
  title: {
    text: '元素热度变化统计',
    left: 'left'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisTick: {
        alignWithLabel: true
      }
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: '数量',
      type: 'bar',
      barWidth: '60%',
      data: []
    }
  ]
})
const TagTopNOptions: EChartsOption = reactive({
  title: {
    text: 'TOP10标签统计',
    left: 'left'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisTick: {
        alignWithLabel: true
      }
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: '数量',
      type: 'bar',
      barWidth: '60%',
      data: []
    }
  ]
})
const PieElementOptions: EChartsOption = reactive({
  title: {
    text: '产品小类元素热度统计',
    left: 'left'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c}'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    top: '8%'
  },
  series: [
    {
      name: '数量',
      type: 'pie',
      radius: '55%',
      center: ['60%', '60%'],
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})
const PieColorSchemeOptions: EChartsOption = reactive({
  title: {
    text: '产品小类颜色热度统计',
    left: 'left'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    top: '8%'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c}'
  },
  series: [
    {
      name: '数量',
      type: 'pie',
      radius: '55%',
      center: ['60%', '60%'],

      itemStyle: {
        borderRadius: 8
      },
      data: []
    }
  ]
})

export {
  ProductTinyCategoryOptions,
  ColorSchemeOptions,
  ElementOptions,
  TagTopNOptions,
  PieElementOptions,
  PieColorSchemeOptions
}
