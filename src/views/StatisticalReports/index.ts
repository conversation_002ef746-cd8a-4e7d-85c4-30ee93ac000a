import type { EChartsOption } from 'echarts'
const MonthOptions: EChartsOption = reactive({
  title: {
    text: '按月份统计推款数量',
    left: 'left'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisTick: {
        alignWithLabel: true
      }
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: '推款数量',
      type: 'bar',
      barWidth: '60%',
      data: []
    }
  ]
})
const CategoryOptions: EChartsOption = reactive({
  title: {
    text: '按产品大类统计推款数量',
    left: 'left'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisTick: {
        alignWithLabel: true
      }
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: '推款数量',
      type: 'bar',
      barWidth: '60%',
      data: []
    }
  ]
})
const vendorTotalOptions: EChartsOption = reactive({
  title: {
    text: '供应商统计推款数量',
    left: 'left'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c}'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    top: '8%'
  },
  series: [
    {
      name: '推款数量',
      type: 'pie',
      radius: '55%',
      center: ['60%', '60%'],
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})

const brandTotalOptions: EChartsOption = reactive({
  title: {
    text: '按归属品牌统计推款数量',
    left: 'left'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c}'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    top: '8%'
  },
  series: [
    {
      name: '推款数量',
      type: 'pie',
      radius: '55%',
      center: ['60%', '60%'],
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})
const unSelectedOptions: EChartsOption = reactive({
  title: {
    text: '推款未选中原因统计',
    left: 'left'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c}'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    top: '8%'
  },
  series: [
    {
      name: '未选中原因',
      type: 'pie',
      radius: '55%',
      center: ['50%', '60%'],
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})
const unSelectedVendorOptions: EChartsOption = reactive({
  title: {
    text: '推款未选中原因统计',
    left: 'left'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c}'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    top: '8%'
  },
  series: [
    {
      name: '未选中原因',
      type: 'pie',
      radius: '55%',
      center: ['50%', '60%'],
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})
const vendorCategoryOptions: EChartsOption = reactive({
  title: {
    text: '按供应商统计产品大类内推款数量',
    left: 'left'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  xAxis: [
    {
      type: 'category',
      axisTick: { show: false },
      axisLabel: {
        formatter: function (value) {
          let ret = '' //拼接加\n返回的类目项
          const maxLength = 5 //每项显示文字个数
          const valLength = value.length //X轴类目项的文字个数
          const rowN = Math.ceil(valLength / maxLength) //类目项需要换行的行数
          if (rowN > 1) {
            //如果类目项的文字大于5,
            for (let i = 0; i < rowN; i++) {
              let temp = '' //每次截取的字符串
              const start = i * maxLength //开始截取的位置
              const end = start + maxLength //结束截取的位置
              //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
              temp = value.substring(start, end) + '\n'
              ret += temp //凭借最终的字符串
            }
            return ret
          } else {
            return value
          }
        }
      },
      data: []
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: '男鞋',
      type: 'bar',
      barGap: 0,
      label: {
        show: true,
        position: 'insideBottom',
        distance: 10,
        align: 'left',
        verticalAlign: 'middle',
        rotate: 90,
        formatter: '{c}  {name|{a}}',
        fontSize: 14,
        rich: {
          name: {}
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    },
    {
      name: '女鞋',
      type: 'bar',
      label: {
        show: true,
        position: 'insideBottom',
        distance: 10,
        align: 'left',
        verticalAlign: 'middle',
        rotate: 90,
        formatter: '{c}  {name|{a}}',
        fontSize: 14,
        rich: {
          name: {}
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    },
    {
      name: '童鞋',
      type: 'bar',
      label: {
        show: true,
        position: 'insideBottom',
        distance: 10,
        align: 'left',
        verticalAlign: 'middle',
        rotate: 90,
        formatter: '{c}  {name|{a}}',
        fontSize: 14,
        rich: {
          name: {}
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    },
    {
      name: '运动鞋',
      type: 'bar',
      label: {
        show: true,
        position: 'insideBottom',
        distance: 10,
        align: 'left',
        verticalAlign: 'middle',
        rotate: 90,
        formatter: '{c}  {name|{a}}',
        fontSize: 14,
        rich: {
          name: {}
        }
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    }
  ]
})
const vendorAgingOptions: EChartsOption = reactive({
  title: {
    text: '供应商推款流程平均时效统计',
    left: 'left'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {
    left: 'left',
    top: '8%'
  },
  grid: {
    left: '3%',
    right: '4%',
    top: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'value'
  },
  yAxis: {
    type: 'category',
    data: []
  },
  series: [
    {
      name: '初选中',
      type: 'bar',
      stack: 'total',
      label: {
        show: true
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    },
    {
      name: '待寄送样品',
      type: 'bar',
      stack: 'total',
      label: {
        show: true
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    },
    {
      name: '待补充信息',
      type: 'bar',
      stack: 'total',
      label: {
        show: true
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    },
    {
      name: '待接收样品',
      type: 'bar',
      stack: 'total',
      label: {
        show: true
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    },
    {
      name: '评审中',
      type: 'bar',
      stack: 'total',
      label: {
        show: true
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    }
  ]
})
const vendorSelectedOptions: EChartsOption = reactive({
  title: {
    text: '供应商推款选中数据',
    left: 'left',
    subtext: '点击供应商柱状图可查看未选中原因分析',
    subtextStyle: {
      color: 'red'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  legend: {
    top: '10%',
    data: ['未选中', '已选中', '选中率']
  },
  grid: {
    top: '20%'
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisPointer: {
        type: 'shadow'
      },
      axisLabel: {
        formatter: function (value) {
          let ret = '' //拼接加\n返回的类目项
          const maxLength = 5 //每项显示文字个数
          const valLength = value.length //X轴类目项的文字个数
          const rowN = Math.ceil(valLength / maxLength) //类目项需要换行的行数
          if (rowN > 1) {
            //如果类目项的文字大于5,
            for (let i = 0; i < rowN; i++) {
              let temp = '' //每次截取的字符串
              const start = i * maxLength //开始截取的位置
              const end = start + maxLength //结束截取的位置
              //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
              temp = value.substring(start, end) + '\n'
              ret += temp //凭借最终的字符串
            }
            return ret
          } else {
            return value
          }
        }
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '数量'
    },
    {
      type: 'value',
      name: '比率'
    }
  ],
  series: [
    {
      name: '未选中',
      type: 'bar',
      data: []
    },
    {
      name: '已选中',
      type: 'bar',
      data: []
    },
    {
      name: '选中率',
      type: 'line',
      yAxisIndex: 1,
      data: []
    }
  ]
})
export {
  MonthOptions,
  CategoryOptions,
  vendorTotalOptions,
  brandTotalOptions,
  unSelectedOptions,
  unSelectedVendorOptions,
  vendorCategoryOptions,
  vendorAgingOptions,
  vendorSelectedOptions
}
