<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { Search } from '@/components/Search'
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { reactive, ref } from 'vue'
import { omit } from 'lodash-es'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { getAgingApi, exportAging } from '@/api/report'
import { ElMessage } from 'element-plus'

const { register, tableObject, methods } = useTable({
  getListApi: getAgingApi,
  processedParameter: {
    timeField: {
      pushTime: ['createTimeStart', 'createTimeEnd']
    }
  }
})

const getDate = () => {
  const nowDate = new Date()
  const date = {
    year: nowDate.getFullYear()
  }
  searchRef.value.setValues({ pushTime: [date.year + '-01-01', date.year + '-12-31'] })
}

const searchRef = ref()
const { setSearchParams, getList } = methods
const pager = reactive<pager>({
  current: 1,
  size: 10,
  total: 0
})
onMounted(() => {
  getDate()
  getList()
})

const crudSchemas = reactive<CrudSchema[]>([
  {
    type: 'seq',
    label: '序号',
    width: 50
  },
  {
    field: 'productName',
    label: '产品名称',
    width: 100,
    search: {
      show: true,
      index: 4
    }
  },
  {
    field: 'productZhCategoryDict_zh',
    label: '产品大类',
    width: 100,
    search: {
      field: 'productZhCategoryDict',
      show: true,
      component: 'Select',
      index: 5,
      value: [],
      dictName: 'PRODUCT_ZH_CATEGORY',
      componentProps: {
        filterable: true,
        multiple: true,
        collapseTags: true
      }
    }
  },
  {
    label: '供应商名称',
    minWidth: 100,
    field: 'vendorName',
    search: {
      show: true,
      index: 3
    }
  },
  {
    label: '区域',
    minWidth: 100,
    field: 'vendorAreaName',
    search: {
      field: 'vendorAreaCode',
      show: true,
      component: 'Select',
      index: 2,
      dictName: 'PDM_AREA',
      componentProps: {
        filterable: true
      }
    }
  },
  {
    field: 'publishSeasonDict',
    label: '季节',
    minWidth: 80,
    colProps: { span: 8 },
    search: {
      field: 'publishSeasonDict',
      show: true,
      component: 'Select',
      index: 6,
      dictName: 'DEVELOP_SEASON',
      componentProps: {
        filterable: true,
        multiple: true,
        collapseTags: true
      }
    }
  },
  {
    field: 'brandName',
    label: '所属品牌',
    width: 100
  },
  {
    field: 'primaryTime',
    label: '初选中（天）',
    'min-width': 80
  },
  {
    field: 'deliveryTime',
    label: '待寄送样品（天）',
    'min-width': 100
  },
  {
    field: 'replenishTime',
    label: '待补充信息（天）',
    minWidth: 100
  },
  {
    field: 'acceptTime',
    label: '待接收样品（天）',
    'min-width': 100
  },
  {
    field: 'reviewTime',
    label: '评审中（天）',
    width: 80
  },
  {
    field: 'stateDict_zh',
    label: '推款状态',
    minWidth: 100,
    search: {
      label: '推款状态',
      field: 'stateDict',
      show: true,
      component: 'Select',
      dictName: 'PUSH_STYLE_STATE',
      index: 7,
      componentProps: {
        filterable: true,
        multiple: true,
        collapseTags: true
      }
    }
  },
  {
    field: 'totalTime',
    label: '总用时（天）',
    'min-width': 120
  },

  {
    field: 'vendorHandleTime',
    label: '供应商处理平均时效（天）',
    minWidth: 100
  },
  {
    field: 'productCenterHandleTime',
    label: '产品中心审批平均时效（天）',
    minWidth: 100
  },
  {
    field: 'pushTime',
    label: '推款时间',
    minWidth: 100,
    search: {
      label: '推款时间',
      show: true,
      index: 1,
      component: 'DatePicker',
      componentProps: {
        type: 'daterange',
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD'
      }
    }
  }
])
watch(
  () => tableObject.total,
  (total) => {
    pager.total = total
  },
  {
    immediate: true,
    deep: true
  }
)

const { allSchemas } = useCrudSchemas(crudSchemas)

const handleSearch = async () => {
  const formData = (await searchRef?.value?.getFormData()) || {}
  const { pushTime } = formData
  if (!pushTime) return ElMessage.warning('请填写推款时间')
  const params = Object.assign({}, formData, pager)

  return setSearchParams(params)
}
const handleReset = () => {
  getDate()
  handleSearch()
}

const loading = ref(false)

const handleExport = async () => {
  const formData = (await searchRef?.value?.getFormData()) || {}
  const { pushTime } = formData
  if (!pushTime) return ElMessage.warning('请填写推款时间')
  const [createTimeStart, createTimeEnd] = pushTime || []
  const exportData = Object.assign({}, pager, omit(formData, 'pushTime'), {
    createTimeStart,
    createTimeEnd
  })
  try {
    loading.value = true
    await exportAging(exportData)
    ElMessage.success('导出成功，请前往导出页面下载')
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <ContentWrap>
    <Search
      ref="searchRef"
      :colNum="3"
      :schema="allSchemas.searchSchema"
      :loading="tableObject.loading"
      @search="handleSearch"
      @reset="handleReset"
      :show-export="true"
      @handle-export="handleExport"
      :export-permission="['productAging:export']"
    />
    <Table
      v-model:pageSize="tableObject.size"
      v-model:currentPage="tableObject.current"
      :pagination="{ total: tableObject.total }"
      :columns="allSchemas.tableColumns"
      :data="tableObject.tableList"
      :loading="tableObject.loading"
      @register="register"
    />
  </ContentWrap>
</template>
<style lang="less" scoped>
:deep(.el-checkbox-button) {
  margin: 0 8px 4px 0 !important;

  .el-checkbox-button__inner {
    border: none;
  }
}

:deep(.el-checkbox-button:last-child),
:deep(.el-checkbox-button:first-child) {
  .el-checkbox-button__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }
}
</style>
