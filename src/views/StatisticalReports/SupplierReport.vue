<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { Echart } from '@/components/Echart'
import { ref } from 'vue'
import { omit } from 'lodash-es'
import { ElDialog, ElForm, ElInput, ElMessage, FormInstance } from 'element-plus'
import { useValidator } from '@/hooks/web/useValidator'
import { getQMSDict } from '@/utils'
import {
  getBrandListApi,
  getCategoryListApi,
  getMonthListApi,
  getUnSelectedListApi,
  getUnSelectedStateListApi,
  getVendorAgingListApi,
  getVendorCategoryListApi,
  getVendorListApi,
  getVendorSelectedListApi
} from '@/api/report'
import {
  brandTotalOptions,
  CategoryOptions,
  MonthOptions,
  unSelectedOptions,
  unSelectedVendorOptions,
  vendorAgingOptions,
  vendorCategoryOptions,
  vendorSelectedOptions,
  vendorTotalOptions
} from './index'

const { required } = useValidator()

const ruleFormRef = ref<FormInstance>()
const loading = ref(false)
const modelValue = ref(false)
const ruleForm: any = reactive({
  pushTime: [],
  vendorAreaCode: '',
  vendorName: ''
})
const rules = {
  pushTime: [required()]
}
const search = async () => {
  ruleFormRef.value?.validate(async (valid) => {
    try {
      if (!valid) return
      loading.value = true
      const params = {}
      const { pushTime } = ruleForm
      const [createTimeStart, createTimeEnd] = pushTime || []
      Object.assign(params, omit(ruleForm, 'pushTime'), {
        createTimeStart,
        createTimeEnd
      })
      // 调用所有接口
      getMonthList(params)
      getCategoryList(params)
      getVendorTotalList(params)
      getBrandList(params)
      handleUnSelectedVendor(params, false)
      getVendorCategoryList(params)
      getVendorAgingList(params)
      getVendorSelectedList(params)
    } finally {
      loading.value = false
    }
  })
}
const reset = async () => {
  ruleFormRef.value?.clearValidate()
  ruleFormRef.value?.resetFields()
  getDate()
  search()
}
const getDate = () => {
  const nowDate = new Date()
  const date = {
    year: nowDate.getFullYear()
  }
  ruleForm.pushTime = [date.year + '-01-01', date.year + '-12-31']
}
//处理柱状返回数据
const getMonthList = async (params) => {
  const { datas } = await getMonthListApi(params)
  const monthList: string[] = []
  const totalList: number[] = []
  datas &&
    datas.map((v) => {
      monthList.push(v.month)
      totalList.push(v.total)
    })
  Object.assign(MonthOptions, {
    xAxis: [
      {
        type: 'category',
        data: monthList,
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    series: [
      {
        name: '推款数量',
        type: 'bar',
        barWidth: '60%',
        data: totalList
      }
    ]
  })
}
const PRODUCT_ZH_CATEGORY = ref(getQMSDict('PRODUCT_ZH_CATEGORY'))
const getCategoryList = async (params) => {
  const { datas } = await getCategoryListApi(params)
  const categoryList = [] as any
  const totalList: number[] = []
  datas &&
    datas.map((v) => {
      const CategoryI18 = PRODUCT_ZH_CATEGORY.value?.find(
        (i) => i.value === v.productZhCategoryDict
      )
      CategoryI18 && categoryList.push(CategoryI18.label)
      totalList.push(v.total)
    })
  Object.assign(CategoryOptions, {
    xAxis: [
      {
        type: 'category',
        data: categoryList,
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    series: [
      {
        name: '推款数量',
        type: 'bar',
        barWidth: '60%',
        data: totalList
      }
    ]
  })
}

// 处理饼状返回数据
const getVendorTotalList = async (params) => {
  const { datas } = await getVendorListApi(params)
  const vendorData = [] as any
  datas &&
    datas.map((item) => {
      const value = item.total
      const name = item.vendorName
      vendorData.push(Object.assign({ value, name }))
    })
  Object.assign(vendorTotalOptions, {
    series: [
      {
        name: '推款数量',
        type: 'pie',
        radius: '55%',
        center: ['60%', '60%'],
        data: vendorData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  })
}
const getBrandList = async (params) => {
  const { datas } = await getBrandListApi(params)
  const brandData = [] as any
  datas &&
    datas.map((item) => {
      const value = item.total
      const name = item.brandName || '无归属品牌（未选中）'
      brandData.push(Object.assign({ value, name }))
    })
  Object.assign(brandTotalOptions, {
    series: [
      {
        name: '推款数量',
        type: 'pie',
        radius: '55%',
        center: ['60%', '60%'],
        data: brandData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  })
}

const handleUnSelectedVendor = async (params, isDown) => {
  const unSelectedVendorData = [] as any
  const series = [
    {
      name: '未选中原因',
      type: 'pie',
      radius: '55%',
      center: ['50%', '60%'],
      data: unSelectedVendorData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]

  if (isDown) {
    const { datas } = await getUnSelectedListApi(params)
    if (datas) {
      datas.map((item) => {
        const value = item.total
        const name = item.unSelectReasonDict_zh
        unSelectedVendorData.push(Object.assign({ value, name }))
      })

      if (datas.length > 0) {
        modelValue.value = true
      } else {
        ElMessage.info('当前供应商不存在未选中原因的统计数据')
      }
      Object.assign(unSelectedVendorOptions, { series })
    } else {
      modelValue.value = false
    }
  } else {
    const { datas } = await getUnSelectedStateListApi(params)
    if (datas) {
      datas.map((item) => {
        const value = item.total
        const name = item.unSelectReasonDict_zh
        unSelectedVendorData.push(Object.assign({ value, name }))
      })
      Object.assign(unSelectedOptions, { series })
    } else {
      modelValue.value = false
    }
  }
}

// 处理供应商统计大类
const getVendorCategoryList = async (params) => {
  const { datas } = await getVendorCategoryListApi(params)
  const vendorCategoryNameList = ref([] as any)
  const CategoryArr1 = ref([] as any)
  const CategoryArr2 = ref([] as any)
  const CategoryArr3 = ref([] as any)
  const CategoryArr4 = ref([] as any)
  if (datas) {
    datas.map((item) => {
      vendorCategoryNameList.value.push(item.vendorName)
      item.category.map((i) => {
        if (i.productZhCategoryDict === '1') {
          CategoryArr1.value.push(i.total)
        }
        if (i.productZhCategoryDict === '2') {
          CategoryArr2.value.push(i.total)
        }
        if (i.productZhCategoryDict === '3') {
          CategoryArr3.value.push(i.total)
        }
        if (i.productZhCategoryDict === '4') {
          CategoryArr4.value.push(i.total)
        }
      })
    })
  }
  Object.assign(vendorCategoryOptions, {
    xAxis: [
      {
        type: 'category',
        axisTick: { show: false },
        axisLabel: {
          formatter: function (value) {
            var ret = '' //拼接加\n返回的类目项
            var maxLength = 5 //每项显示文字个数
            var valLength = value.length //X轴类目项的文字个数
            var rowN = Math.ceil(valLength / maxLength) //类目项需要换行的行数
            if (rowN > 1) {
              //如果类目项的文字大于5,
              for (var i = 0; i < rowN; i++) {
                var temp = '' //每次截取的字符串
                var start = i * maxLength //开始截取的位置
                var end = start + maxLength //结束截取的位置
                //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                temp = value.substring(start, end) + '\n'
                ret += temp //凭借最终的字符串
              }
              return ret
            } else {
              return value
            }
          }
        },
        data: vendorCategoryNameList
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: '男鞋',
        type: 'bar',
        label: {
          show: true,
          position: 'insideBottom',
          distance: 10,
          align: 'left',
          verticalAlign: 'middle',
          rotate: 90,
          formatter: '{c}  {name|{a}}',
          fontSize: 14,
          rich: {
            name: {}
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: CategoryArr1
      },
      {
        name: '女鞋',
        type: 'bar',
        label: {
          show: true,
          position: 'insideBottom',
          distance: 10,
          align: 'left',
          verticalAlign: 'middle',
          rotate: 90,
          formatter: '{c}  {name|{a}}',
          fontSize: 14,
          rich: {
            name: {}
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: CategoryArr2
      },
      {
        name: '童鞋',
        type: 'bar',
        label: {
          show: true,
          position: 'insideBottom',
          distance: 10,
          align: 'left',
          verticalAlign: 'middle',
          rotate: 90,
          formatter: '{c}  {name|{a}}',
          fontSize: 14,
          rich: {
            name: {}
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: CategoryArr3
      },
      {
        name: '运动鞋',
        type: 'bar',
        label: {
          show: true,
          position: 'insideBottom',
          distance: 10,
          align: 'left',
          verticalAlign: 'middle',
          rotate: 90,
          formatter: '{c}  {name|{a}}',
          fontSize: 14,
          rich: {
            name: {}
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: CategoryArr4
      }
    ]
  })
}

// 处理时效
const getVendorAgingList = async (params) => {
  const { datas } = await getVendorAgingListApi(params)
  const vendorNameList = ref([] as any)
  const AgingArr1 = ref([] as any)
  const AgingArr2 = ref([] as any)
  const AgingArr3 = ref([] as any)
  const AgingArr4 = ref([] as any)
  const AgingArr5 = ref([] as any)
  const avarageArr = ref([] as any)
  if (datas) {
    datas.map((item) => {
      const {
        vendorName,
        primaryTime,
        deliveryTime,
        replenishTime,
        acceptTime,
        reviewTime,
        totalTime
      } = item
      if (totalTime !== '0.0') {
        vendorNameList.value.push(vendorName)
        AgingArr1.value.push(primaryTime)
        AgingArr2.value.push(deliveryTime)
        AgingArr3.value.push(replenishTime)
        AgingArr4.value.push(acceptTime)
        AgingArr5.value.push(reviewTime)
        avarageArr.value.push({ totalTime, vendorName })
      }
    })
  }
  Object.assign(vendorAgingOptions, {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (data) {
        var avarageSum = avarageArr.value.find((i) => i.vendorName === data[0].name)?.totalTime
        var text = data[0].name + '<br/>'
        for (var i = 0; i < data.length; i++) {
          text += data[i].marker + data[i].seriesName + ': ' + data[i].value + '<br/>'
        }
        return (text += '总平均用时：' + avarageSum)
      }
    },
    yAxis: {
      type: 'category',
      data: vendorNameList.value
    },
    series: [
      {
        name: '初选中',
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        emphasis: {
          focus: 'series'
        },
        data: AgingArr1.value
      },
      {
        name: '待寄送样品',
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        emphasis: {
          focus: 'series'
        },
        data: AgingArr2.value
      },
      {
        name: '待补充信息',
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        emphasis: {
          focus: 'series'
        },
        data: AgingArr3.value
      },
      {
        name: '待接收样品',
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        emphasis: {
          focus: 'series'
        },
        data: AgingArr4.value
      },
      {
        name: '评审中',
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        emphasis: {
          focus: 'series'
        },
        data: AgingArr5.value
      }
    ]
  })
}

// 处理供应商推款选中数据

const getVendorSelectedList = async (params) => {
  const { datas } = await getVendorSelectedListApi(params)
  const vendorNameList = ref([] as any)
  const unSelectedSum = ref([] as any)
  const selectedSum = ref([] as any)
  const selectedRateNum = ref([] as any)
  datas &&
    datas.map((item) => {
      const { vendorName, unSelectedTotal, selectedTotal, selectedRate } = item
      if (
        unSelectedTotal !== 0 ||
        selectedTotal !== 0 ||
        (selectedRate !== '0.0%' && selectedRate)
      ) {
        vendorNameList.value.push(vendorName)
        unSelectedSum.value.push(unSelectedTotal)
        selectedSum.value.push(selectedTotal)
        selectedRateNum.value.push(parseFloat(selectedRate))
      }
    })
  Object.assign(vendorSelectedOptions, {
    xAxis: [
      {
        type: 'category',
        data: vendorNameList.value,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量'
      },
      {
        type: 'value',
        name: '比率'
      }
    ],
    series: [
      {
        name: '未选中',
        type: 'bar',
        data: unSelectedSum.value
      },
      {
        name: '已选中',
        type: 'bar',
        data: selectedSum.value
      },
      {
        name: '选中率',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value + '%'
          }
        },
        data: selectedRateNum.value
      }
    ]
  })
}

// 下钻查看未选中原因
const getSonData = async (e) => {
  if (!e) return
  // 调用查询接口
  const params = {}
  const { pushTime, vendorAreaCode } = ruleForm
  const [createTimeStart, createTimeEnd] = pushTime || []
  Object.assign(params, { vendorName: e.name, vendorAreaCode, createTimeStart, createTimeEnd })
  handleUnSelectedVendor(params, true)
}

onMounted(async () => {
  getDate()
  search()
})
</script>

<template>
  <ContentWrap>
    <div class="flex justify-content-center">
      <ElForm
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        center
        label-width="100px"
        style="margin-right: 20px"
      >
        <ElRow>
          <ElCol :span="8">
            <ElFormItem label="推款时间" prop="pushTime">
              <ElDatePicker
                v-model="ruleForm.pushTime"
                :clearable="false"
                format="YYYY-MM-DD"
                type="daterange"
                value-format="YYYY-MM-DD"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="区域" prop="vendorAreaCode">
              <Selection v-model="ruleForm.vendorAreaCode" api-key="AREA_CODE" filterable />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="供应商名称" prop="vendorName">
              <ElInput v-model="ruleForm.vendorName" filterable />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
      <ElButton :loading="loading" type="primary" @click="search">
        <Icon class="mr-1" icon="ep:search" />
        查询
      </ElButton>
      <ElButton @click="reset">
        <Icon class="mr-1" icon="ep:refresh-right" />
        重置
      </ElButton>
    </div>
    <ElRow :gutter="40" class="mb-10">
      <ElCol :span="12">
        <ElCard>
          <Echart :options="MonthOptions" />
        </ElCard>
      </ElCol>
      <ElCol :span="12">
        <ElCard>
          <Echart :options="CategoryOptions" />
        </ElCard>
      </ElCol>
    </ElRow>
    <ElRow :gutter="40" class="mb-10">
      <ElCol :span="12">
        <ElCard>
          <Echart :options="vendorTotalOptions" />
        </ElCard>
      </ElCol>
      <ElCol :span="12">
        <ElCard>
          <Echart :options="vendorCategoryOptions" />
        </ElCard>
      </ElCol>
    </ElRow>
    <ElRow :gutter="40" class="mb-10">
      <ElCol :span="12">
        <ElCard>
          <Echart :options="brandTotalOptions" />
        </ElCard>
      </ElCol>
      <ElCol :span="12">
        <ElCard>
          <Echart :options="vendorAgingOptions" />
        </ElCard>
      </ElCol>
    </ElRow>

    <ElRow :gutter="40" class="mb-10">
      <ElCol :span="12">
        <ElCard>
          <ElRow justify="center">
            <ElCol :span="1">
              <el-tooltip class="box-item" effect="dark" placement="top">
                <template #content>
                  已选中即推款状态为【已选中】的数量<br />
                  未选中即推款状态【初选未选中】和【评审未选中】之和<br />
                  选中率为【已选中】数量/（【初选未选中】+【评审未选中】+【已选中】
                </template>
                <Icon class="ml-[5px]" icon="ep:question-filled" />
              </el-tooltip>
            </ElCol>
            <ElCol :span="23">
              <Echart :is-click="true" :options="vendorSelectedOptions" @son-data="getSonData" />
            </ElCol>
          </ElRow>
        </ElCard>
      </ElCol>
      <ElCol :span="12">
        <ElCard>
          <Echart :options="unSelectedOptions" />
        </ElCard>
      </ElCol>
    </ElRow>

    <ElDialog
      :close-on-click-modal="false"
      :model-value="modelValue"
      center
      width="60%"
      @close="modelValue = false"
    >
      <Echart :options="unSelectedVendorOptions" :width="800" />
    </ElDialog>
  </ContentWrap>
</template>
