<script lang="ts" setup>
import { ref } from 'vue'
import {
  getWMSCategoryList,
  Pager,
  WMSCategoryListAPI
} from '@/views/basic-library-manage/api/common'
import { SelectPlus } from '@/components/Business/SelectPlus'
import { VxeTableInstance } from 'vxe-table'
import {
  type CascaderProps,
  ElButton,
  ElCollapseTransition,
  ElPagination,
  ElRow,
  FormInstance,
  type FormRules
} from 'element-plus'
import { isEqual, omit } from 'lodash-es'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { ProductDevProgressListApi } from '@/views/product-calendar/product-dev-progress/api'
import { useLocaleConfig } from '@/hooks/autoImport/useLocaleConfig'
import {
  BsrRankingListApi,
  getBsrCategoryList,
  getBsrRankingList,
  getBsrVersionList
} from '@/views/BsrRanking/api'
import BsrRankingDetailDialog from '@/views/BsrRanking/BsrRankingDetailDialog.vue'
import { watchDebounced } from '@vueuse/core'
import { RefreshLeft, Search, UploadFilled } from '@element-plus/icons-vue'
import { StationEnums } from '@/enums'
import { scrollProp } from '@/plugins/vxeTable'
import { Icon } from '@/components/Icon'
import { trackSiteSearch } from '@/utils/monitor'

defineOptions({
  name: 'BsrRankingList'
})
const useConst = () => {
  const { formLabelLength } = useLocaleConfig([
    {
      formLabelLength: '120'
    },
    {
      formLabelLength: '200'
    }
  ])

  const cascaderProps: CascaderProps = {
    label: 'selectorEnValue',
    value: 'selectorKey',
    children: 'childList',
    expandTrigger: 'hover' as const,
    emitPath: false,
    multiple: true
  }

  return {
    formLabelLength,
    cascaderProps
  }
}

const { formLabelLength, cascaderProps } = useConst()

const useOperation = () => {
  const tableRef = ref<VxeTableInstance>()
  const selectedRows = ref<ProductDevProgressListApi.List>([])
  const handleSelectChange = () => {
    selectedRows.value = tableRef.value?.getCheckboxRecords() as ProductDevProgressListApi.List
  }

  return {
    tableRef,
    selectedRows,
    handleSelectChange
  }
}

const { tableRef, selectedRows, handleSelectChange } = useOperation()

const useQuery = () => {
  type FormModel = BsrRankingListApi.Request & { productCategoryID?: number }
  const formRef = ref<FormInstance>()
  const pagerRef = ref<InstanceType<typeof ElPagination>>()
  const formRules = ref<FormRules<FormModel>>({
    station: [
      {
        required: true,
        message: '请选择站点'
      }
    ]
  })
  const defaultFormData: FormModel = {
    amazonCategoryIds: [],
    asin: '',
    brand: '',
    current: undefined,
    ids: [],
    productCategoryName: '',
    rankVersionList: [],
    rankVersionTimeList: [],
    size: undefined,
    sortInfos: [],
    station: StationEnums.US,
    productCategoryID: undefined
  }
  const lastFormData = ref({
    ...defaultFormData
  })
  const formData = ref({
    ...defaultFormData
  })

  const tableData = ref<BsrRankingListApi.Response[]>([])
  const pager = ref<Pager>({
    current: 1,
    size: 50,
    total: 0
  })
  const queryLoading = ref(false)
  const queryParams = computed<BsrRankingListApi.Request>(() => {
    return {
      ...omit(formData.value, 'productCategoryID'),
      ...pager.value
    }
  })
  let controller: AbortController | null = null
  const handleQuery = async (sortInfos?: BsrRankingListApi.SortInfo[]) => {
    trackSiteSearch('search', '亚马逊榜单搜索')
    const valid = await formRef.value?.validate()
    if (!valid) {
      return
    }
    if (controller) {
      controller.abort()
      controller = null
      setTimeout(() => {
        handleQuery(sortInfos)
      })
      return
    }
    queryLoading.value = true
    controller = new AbortController()
    if (!isEqual(lastFormData.value, formData.value)) {
      pager.value.current = 1
    }
    const [error, result] = await getBsrRankingList(
      {
        ...queryParams.value,
        sortInfos: Array.isArray(sortInfos) ? sortInfos : undefined
      },
      controller.signal
    )
    queryLoading.value = false
    selectedRows.value = []
    if (error === null && result?.datas) {
      lastFormData.value = { ...formData.value }
      const { records } = result.datas
      tableData.value = records || []
      await nextTick()
      tableRef.value?.loadData([]).then(() => {
        tableRef.value?.loadData(tableData.value)
      })
      pager.value.total = result.datas?.pager?.total || 0
    }
  }

  const handleReset = () => {
    formData.value = {
      ...defaultFormData
    }
  }

  const visible = ref<boolean>(false)
  const setVisible = () => {
    visible.value = !unref(visible)
  }

  const maxHeight = useTableHeight({
    tableRef,
    pagerRef
  })

  return {
    formRef,
    pagerRef,
    formRules,
    formData,
    lastFormData,
    tableData,
    pager,
    queryLoading,
    queryParams,
    handleQuery,
    handleReset,
    visible,
    setVisible,
    maxHeight
  }
}

const {
  formRef,
  pagerRef,
  formRules,
  formData,
  tableData,
  pager,
  queryLoading,
  handleQuery,
  handleReset,
  maxHeight,
  setVisible,
  visible
} = useQuery()

onActivated(async () => {
  await queryAmazonCategory()
  await handleQuery()
  formRef.value?.clearValidate()
})
type RowType = BsrRankingListApi.Response
const amazonCategoryTree = ref<RowType[]>([])

async function queryAmazonCategory() {
  if (!formData.value.station) return
  const [err, data] = await getBsrCategoryList(formData.value.station)
  if (err) return
  if (!Array.isArray(data.datas)) {
    amazonCategoryTree.value = []
    return
  }
  amazonCategoryTree.value = data?.datas
}

onMounted(queryAmazonCategory)

const versionOptions = ref<{ label: string; value: string }[]>([])

async function queryVersionList() {
  const [err, data] = await getBsrVersionList()
  if (err) return
  if (!Array.isArray(data.datas)) {
    versionOptions.value = []
    return
  }
  versionOptions.value = data?.datas.map((str) => {
    return {
      label: str,
      value: str
    }
  })
}

const wmsCategoryList = ref<WMSCategoryListAPI.Data[]>([])
const fetchCategoryList = async () => {
  const [error, result] = await getWMSCategoryList()
  if (error === null && result?.datas) {
    wmsCategoryList.value = result.datas
  }
}

Promise.all([fetchCategoryList(), queryVersionList()])
const currentRow = ref<RowType>()
const dialogVisible = ref(false)

function openDialog(row: RowType) {
  currentRow.value = row
  dialogVisible.value = true
}

const { handleExport: exportFn, loading: exportLoading } = useOmsExport()

watch(
  () => formData.value.productCategoryID,
  (newId) => {
    if (!newId) {
      formData.value.productCategoryName = ''
      return
    }
    formData.value.productCategoryName = findCategoryPath(wmsCategoryList.value, newId)
  }
)

function findCategoryPath(categories: WMSCategoryListAPI.Data[], targetId: number) {
  function dfs(node: WMSCategoryListAPI.Data, path: string[]) {
    if (node.id === targetId) {
      return [...path, node.categoryName]
    }
    if (node.sonCategory && node.sonCategory.length > 0) {
      for (let child of node.sonCategory) {
        const result = dfs(child, [...path, node.categoryName!])
        if (result) {
          return result
        }
      }
    }
    return null
  }

  for (let category of categories) {
    const path = dfs(category, [])
    if (path) {
      return path.join('/')
    }
  }
  return ''
}

async function handleExport() {
  const checked: RowType[] | undefined = tableRef.value?.getCheckboxRecords()

  const reqParamObj =
    Array.isArray(checked) && checked.length > 0
      ? {
          ids: checked.map((item) => {
            return item.id
          })
        }
      : {
          ...formData.value
        }

  await exportFn({
    exportType: 'bsrRank-export',
    reqParam: JSON.stringify(reqParamObj)
  })
}

onMounted(() => {
  watchDebounced(
    formData,
    async () => {
      await handleQuery()
    },
    { deep: true, debounce: 400 }
  )
})

async function handleSortChange(params) {
  pager.value.current = 1
  const sortInfos: BsrRankingListApi.SortInfo[] = params.sortList.map((item, index) => {
    return {
      param: item.field,
      rules: item.order,
      priority: index
    }
  })
  await handleQuery(sortInfos)
}

// 这个页面0为true,1为false
function format01(value: number | undefined, falseLabel = '否') {
  if (value === 0) {
    return '是'
  }
  return falseLabel
}
function formatMoney(money: number | undefined, unit: string | undefined) {
  return `${money} ${unit}`
}
</script>

<template>
  <ContentWrap>
    <div>
      <div class="max-h-[calc(100vh_-_250px)] overflow-x-hidden overflow-y-auto">
        <ElForm
          ref="formRef"
          :label-width="formLabelLength"
          :model="formData"
          :rules="formRules"
          @submit="
            (e) => {
              e.preventDefault()
            }
          "
        >
          <ElRow :gutter="20">
            <ElCol :span="8">
              <ElFormItem label="站点" prop="station">
                <SelectPlus
                  v-model="formData.station"
                  :configuration="{
                    value: 'dictValue',
                    label: 'dictEnName'
                  }"
                  :parent-scroll="false"
                  api-key="station"
                  class="!w-full"
                  filterable
                  virtualized
                  @change="queryAmazonCategory"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8"
              ><ElFormItem label="榜单版本" prop="rankVersionList">
                <ElSelect
                  v-model="formData.rankVersionList"
                  class="!w-full"
                  clearable
                  multiple
                  placeholder="请选择"
                >
                  <ElOption v-for="item in versionOptions" :key="item.value" :value="item.value" />
                </ElSelect> </ElFormItem
            ></ElCol>
            <ElCol :span="8">
              <ElFormItem class="form-item-no-wrap" label="" label-width="0">
                <ElButton text @click="setVisible">
                  {{ visible ? '收起' : '展开' }}
                  <Icon
                    :class="visible ? 'rotate-90' : ''"
                    class="transform transition duration-400"
                    icon="ant-design:down-outlined"
                  />
                </ElButton>
                <ElButton
                  :icon="Search"
                  :loading="queryLoading"
                  class="w-16"
                  native-type="submit"
                  type="primary"
                  @click="handleQuery()"
                >
                  查询
                </ElButton>
                <ElButton
                  :icon="RefreshLeft"
                  :loading="queryLoading"
                  class="w-16"
                  native-type="reset"
                  @click="handleReset"
                >
                  重置
                </ElButton>
                <ElButton
                  :icon="UploadFilled"
                  :loading="exportLoading"
                  class="w-16"
                  type="primary"
                  @click="handleExport"
                >
                  导出
                </ElButton>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="亚马逊类目" prop="amazonCategoryIds">
                <ElCascader
                  v-model="formData.amazonCategoryIds"
                  :disabled="!formData.station"
                  :max-collapse-tags="1"
                  :options="amazonCategoryTree"
                  :props="cascaderProps"
                  class="!w-full"
                  clearable
                  collapse-tags
                  filterable
                  separator="-"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="MMT类目" prop="productCategoryID">
                <ElCascader
                  v-model="formData.productCategoryID"
                  :options="wmsCategoryList"
                  :props="{
                    children: 'sonCategory',
                    label: 'categoryName',
                    value: 'id',
                    emitPath: false,
                    multiple: false
                  }"
                  :separator="'/'"
                  class="!w-full overflow-auto"
                  clearable
                  filterable
                  placeholder="请选择商品三级分类名称"
                /> </ElFormItem
            ></ElCol>

            <ElCollapseTransition>
              <ElCol :span="16">
                <div v-show="visible" class="flex flex-wrap w-full">
                  <ElCol :span="12"
                    ><ElFormItem label="品牌" prop="brand">
                      <ElInput
                        v-model="formData.brand"
                        clearable
                        placeholder="请输入"
                      /> </ElFormItem
                  ></ElCol>
                  <ElCol :span="12"
                    ><ElFormItem label="ASIN" prop="asin">
                      <ElInput
                        v-model="formData.asin"
                        clearable
                        placeholder="请输入"
                      /> </ElFormItem
                  ></ElCol>
                  <ElCol :span="12">
                    <ElFormItem label="人群" prop="targetAudiences">
                      <SelectPlus
                        v-model="formData.targetAudiences"
                        api-key="PLATFORM_PRODUCT_PEOPLE"
                        cache
                        clearable
                        collapse-tags
                        collapse-tags-tooltip
                        multiple
                      /> </ElFormItem
                  ></ElCol>
                  <ElCol :span="12">
                    <ElFormItem label="性别" prop="targetSexs">
                      <SelectPlus
                        v-model="formData.targetSexs"
                        :configuration="{
                          key: 'dictValue',
                          label: 'dictEnName',
                          value: 'dictValue'
                        }"
                        api-key="PLATFORM_CATEGORY_SEX"
                        cache
                        clearable
                        collapse-tags
                        collapse-tags-tooltip
                        multiple
                      /> </ElFormItem
                  ></ElCol>
                  <ElCol :span="12">
                    <ElFormItem label="风格" prop="categoryStyles">
                      <SelectPlus
                        v-model="formData.categoryStyles"
                        api-key="PLATFORM_PRODUCT_STYLE"
                        cache
                        clearable
                        collapse-tags
                        collapse-tags-tooltip
                        multiple
                      /> </ElFormItem
                  ></ElCol>
                  <ElCol :span="12">
                    <ElFormItem label="大类" prop="mainCategorys">
                      <SelectPlus
                        v-model="formData.mainCategorys"
                        api-key="PRODUCT_STYLE"
                        cache
                        clearable
                        collapse-tags
                        collapse-tags-tooltip
                        multiple
                      /> </ElFormItem
                  ></ElCol>
                  <ElCol :span="12">
                    <ElFormItem label="季节" prop="applicableSeasons">
                      <SelectPlus
                        v-model="formData.applicableSeasons"
                        :parent-scroll="false"
                        api-key="PLATFORM_CATEGORY_SEASON"
                        class="!w-full"
                        filterable
                      /> </ElFormItem
                  ></ElCol>
                  <ElCol :span="12">
                    <ElFormItem label="类目名称" prop="classificationNames">
                      <SelectPlus
                        v-model="formData.classificationNames"
                        api-key="PRODUCT_STYLE"
                        cache
                        clearable
                        collapse-tags
                        collapse-tags-tooltip
                        multiple
                        placeholder="请输入"
                      /> </ElFormItem
                  ></ElCol>
                </div>
              </ElCol>
            </ElCollapseTransition>
          </ElRow>
        </ElForm>
        <VxeTable
          ref="tableRef"
          :cellConfig="{ height: 110 }"
          :data="tableData"
          :loading="queryLoading"
          :maxHeight="maxHeight - 100"
          :minHeight="100"
          :sort-config="{
            multiple: true
          }"
          showOverflow="tooltip"
          v-bind="{ ...scrollProp }"
          @sort-change="handleSortChange"
          @checkbox-change="handleSelectChange"
          @checkbox-all="handleSelectChange"
        >
          <VxeColumn type="checkbox" width="40" />
          <VxeColumn field="station" title="站点" width="120" />
          <!-- rankVersionInfo展示, rankVersion排序 -->
          <VxeColumn field="rankVersion" sortable title="榜单版本" width="120">
            <template #default="{ row }: { row: RowType }">
              {{ row.rankVersionInfo }}
            </template>
          </VxeColumn>
          <VxeColumn field="ranking" sortable title="排名" width="120" />
          <VxeColumn
            :cell-render="{ name: 'Image' }"
            field="primaryImageUrl"
            title="主图"
            width="100"
          />
          <VxeColumn field="productName" title="产品名称" width="100" />
          <VxeColumn field="asin" sortable title="ASIN" width="100" />
          <VxeColumn field="productCategory" title="产品类型" width="100" />
          <VxeColumn field="parentAsin" sortable title="ParentAsin" width="125" />
          <VxeColumn field="amazonCategoryName" title="所在类目" width="100" />
          <VxeColumn field="productCategoryName" title="MMT类目" width="100" />
          <VxeColumn field="targetAudience" title="人群" width="100" />
          <VxeColumn field="targetSex" title="性别" width="100" />
          <VxeColumn field="categoryStyle" title="风格" width="100" />
          <VxeColumn field="mainCategory" title="大类" width="100" />
          <VxeColumn field="classificationName" title="类目名称" width="100" />
          <VxeColumn field="applicableSeason" title="季节" width="100" />
          <VxeColumn field="amazonUrl" title="URL" width="100">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              <ElLink :href="row.amazonUrl" target="_blank" type="primary">
                {{ row.amazonUrl }}
              </ElLink>
            </template>
          </VxeColumn>
          <VxeColumn
            field="estimatedMonthlyListingSales"
            sortable
            title="预计Listing月销量"
            width="150"
          />
          <VxeColumn field="estimatedMonthlySalesRevenue" sortable title="预计月销售额" width="100">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              <span v-if="row.estimatedMonthlyListingSales">
                {{ row.estimatedMonthlyListingSales }}
              </span>
              <span v-if="row.estimatedMonthlyListingSales && row.monetaryUnit">
                {{ row.monetaryUnit }}
              </span>
            </template>
          </VxeColumn>
          <VxeColumn field="brand" title="品牌" width="100" />
          <VxeColumn field="listingTime" sortable title="上架时间" width="150" />
          <VxeColumn field="listingDays" sortable title="上架天数" width="100" />
          <VxeColumn
            field="recentSevenDayStockTensionFlag"
            title="近7日是否出现库存紧张"
            width="175"
          >
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              {{ format01(row.recentSevenDayStockTensionFlag) }}
            </template>
          </VxeColumn>
          <VxeColumn
            field="recentThirtyDayStockTensionFlag"
            title="近30日是否出现库存紧张"
            width="175"
          >
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              {{ format01(row.recentThirtyDayStockTensionFlag) }}
            </template>
          </VxeColumn>
          <VxeColumn field="recentSevenDayStockoutFlag" title="近7日是否出现断货" width="150">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              {{ format01(row.recentSevenDayStockoutFlag) }}
            </template>
          </VxeColumn>
          <VxeColumn field="recentThirtyDayStockoutFlag" title="近30日是否出现断货" width="150">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              {{ format01(row.recentThirtyDayStockoutFlag) }}
            </template>
          </VxeColumn>
          <VxeColumn field="reviesNum" sortable title="评价数量" width="100" />
          <VxeColumn field="ratingStar" sortable title="评分星级" width="100" />
          <VxeColumn field="bbxSellerAttribute" title="BBX卖家属性" width="100" />
          <VxeColumn field="storeName" title="店铺" width="100" />
          <VxeColumn field="nationalRegion" title="国籍/地区" width="100" />
          <VxeColumn field="currentSalesPrice" sortable title="目前销售价" width="100">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              {{ formatMoney(row.crossedPrice, row.monetaryUnit) }}
            </template>
          </VxeColumn>
          <VxeColumn field="coupon" title="Coupon" width="100" />
          <VxeColumn field="preferentialPrice" sortable title="优惠" width="100">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              {{ formatMoney(row.preferentialPrice, row.monetaryUnit) }}
            </template>
          </VxeColumn>
          <VxeColumn field="actualPrice" sortable title="实际价格" width="100">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              {{ formatMoney(row.actualPrice, row.monetaryUnit) }}
            </template>
          </VxeColumn>
          <VxeColumn field="crossedPrice" sortable title="划线价" width="100">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              {{ formatMoney(row.crossedPrice, row.monetaryUnit) }}
            </template>
          </VxeColumn>
          <VxeColumn field="grossProfit" sortable title="单个产品毛利" width="100">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              <span v-if="row.grossProfit">
                {{ row.grossProfit }}
              </span>
              <span v-if="row.grossProfit && row.monetaryUnit">
                {{ row.monetaryUnit }}
              </span>
            </template>
          </VxeColumn>
          <VxeColumn field="grossProfitRate" sortable title="单个产品毛利率(%)" width="150" />
          <VxeColumn field="counterQuantity" sortable title="单个产品跟卖数量" width="125" />
          <VxeColumn field="variantsQuantity" sortable title="单个产品变体数量" width="125" />
          <VxeColumn
            field="marketShare"
            sortable
            title="月度同品牌销量占比(市场份额%)"
            width="225"
          />
          <VxeColumn field="salesRatio" sortable title="月度流量圈销量占比(%)" width="100" />
          <VxeColumn field="fbaCost" sortable title="FBA费用" width="100">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              <span v-if="row.fbaCost">
                {{ row.fbaCost }}
              </span>
              <span v-if="row.fbaCost && row.monetaryUnit">
                {{ row.monetaryUnit }}
              </span>
            </template>
          </VxeColumn>
          <VxeColumn field="categoryRanking" sortable title="大类排名" width="100" />
          <VxeColumn field="averageCategoryRanking" title="平均大类排名" width="100" />
          <VxeColumn field="categoryRankingAndName" title="小类排名及名称" width="150" />
          <VxeColumn field="rankingChange" title="排名变化" width="100" />
          <VxeColumn field="rankingChangeRatio" title="排名变化率" width="100" />
          <VxeColumn field="primaryImageVideo" title="主图视频" width="100">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              {{ format01(row.primaryImageVideo, '无') }}
            </template>
          </VxeColumn>
          <VxeColumn field="aAdd" title="A+" width="100">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              {{ format01(row.aAdd, '无') }}
            </template>
          </VxeColumn>
          <VxeColumn field="climatePledgeFriendlyFlag" title="是否气候承诺友好标" width="150">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              {{ format01(row.climatePledgeFriendlyFlag, '无') }}
            </template>
          </VxeColumn>
          <VxeColumn field="doBrandFlagshipStoreFlag" title="是否做品牌旗舰店" width="125">
            <template #default="{ row }: { row: BsrRankingListApi.Response }">
              {{ format01(row.doBrandFlagshipStoreFlag) }}
            </template>
          </VxeColumn>
          <VxeColumn field="logisticsMode" title="物流方式" width="100" />
          <VxeColumn field="weight" title="重量(lb)" width="100" />
          <VxeColumn field="volume" title="体积（in³）" width="100" />
          <VxeColumn field="sizeStandard" title="尺寸标准" width="100" />
          <VxeColumn field="sizeInfo" title="尺寸（in）" width="150" />
          <VxeColumn field="fivePointDesc" title="五点描述" width="100" />

          <VxeColumn fixed="right" title="操作" width="180">
            <template #default="{ row }: { row: RowType }">
              <ElButton link type="primary" @click="openDialog(row)">历史销量与单价</ElButton>
            </template>
          </VxeColumn>
        </VxeTable>
      </div>
      <ElPagination
        ref="pagerRef"
        v-model:current-page="pager.current"
        v-model:page-size="pager.size"
        :total="pager.total"
        background
        class="mt-4"
        layout="total, sizes, prev, pager, next, jumper"
        @click="handleQuery()"
      />
    </div>
    <BsrRankingDetailDialog v-model="dialogVisible" :current-row="currentRow" />
  </ContentWrap>
</template>

<style lang="less" scoped>
:deep(.el-form-item) {
  align-items: center;
}
</style>
