<script lang="ts" setup>
import { BsrRanking<PERSON>istApi, getBsrHistory } from '@/views/BsrRanking/api'

defineOptions({
  name: 'BsrRankingDetailDialog'
})

const props = defineProps<{
  modelValue: boolean
  currentRow: BsrRankingListApi.Response | undefined
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', val: boolean): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const queryLoading = ref(false)
const queryBsrHistory = async () => {
  const row = props.currentRow
  if (!row) {
    return
  }
  queryLoading.value = true

  const [error, result] = await getBsrHistory({
    asin: row.asin,
    station: row.station
  })
  queryLoading.value = false
  if (error === null && result?.datas) {
    tableData.value = result.datas
  }
}

watch(
  visible,
  async () => {
    if (visible.value) {
      await queryBsrHistory()
    }
  },
  { immediate: true }
)

const handleClose = () => {
  visible.value = false
  tableData.value = []
}

type TableRow = BsrRankingListApi.Response & { rankVersion?: string }
const tableData = ref<TableRow[]>([])

const transformedData = ref<Record<string, string>[]>([])
const dynamicColumns = ref<string[]>([])
const isEmpty = computed(() => {
  if (!Array.isArray(tableData.value)) {
    return true
  }
  return tableData.value.length === 0
})

const mapTranslate = {
  monthlyListingSales: 'Listing月销量',
  monthlyListingSalesVolume: '月销售额',
  monthlyAverageUnitPrice: '平均单价'
}

watch(
  tableData,
  (newVal) => {
    transformedData.value = transformTableData(newVal)
    dynamicColumns.value = newVal.map((item) => item.rankVersion!)
  },
  { deep: true }
)

function transformTableData(data: TableRow[]) {
  const objKeys = Object.keys(mapTranslate)
  return objKeys.map((key) => {
    const row = { rankVersion: mapTranslate[key] }
    data.forEach((item) => {
      if (item.rankVersion) {
        if (key === 'monthlyAverageUnitPrice') {
          row[item.rankVersion] = `${item[key]} ${item.monetaryUnit || ''}`
          return
        }
        row[item.rankVersion] = item[key]
      }
    })
    return row
  })
}
</script>

<template>
  <Dialog v-model="visible" :before-close="handleClose" :parent-scroll="false" title="">
    <template #title>
      <div class="flex w-full items-center">
        <div class="flex items-center gap-4">
          <ElImage
            hide-on-click-modal
            :preview-src-list="[currentRow?.primaryImageUrl!]"
            :src="currentRow?.primaryImageUrl"
            class="w-[40px] h-[35px]"
            lazy
          />
          <span class="text-gray-500"> 查看历史销量与单价 </span>
        </div>
        <div class="mr-auto ml-32">ASIN：B074HD3Z5H</div>
      </div>
    </template>
    <template v-if="isEmpty">
      <el-table />
    </template>
    <template v-else>
      <el-table :data="transformedData" border class="w-full">
        <el-table-column label="历史月份" prop="rankVersion" width="180" />
        <el-table-column
          v-for="(column, index) in dynamicColumns"
          :key="index"
          :label="column"
          :prop="column"
        />
      </el-table>
    </template>
    <template #footer>
      <ElButton @click="handleClose">关闭</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
:deep(tr .el-table__cell:first-of-type) {
  background: #ccc;
}

:deep(.el-table__header thead) {
  color: var(--el-text-color-regular);

  .el-table__cell {
    font-weight: 500;
  }
}
</style>
