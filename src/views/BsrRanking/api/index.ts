import to from 'await-to-js'
import { service } from '@/config/fetch/service'

export namespace BsrRankingListApi {
  export type Request = Partial<
    {
      /**
       * 亚马逊分类id集合
       */
      amazonCategoryIds: number[]
      /**
       * 季节
       */
      applicableSeasons?: string[]
      asin?: string
      /**
       * 品牌
       */
      brand?: string
      /**
       * 风格
       */
      categoryStyles?: string[]
      /**
       * 类目名称
       */
      classificationNames?: string[]
      /**
       * 亚马逊榜单id集合
       */
      ids?: number[]
      /**
       * 大类
       */
      mainCategorys?: string[]
      /**
       * wms商品名称
       */
      productCategoryName?: string
      /**
       * 榜单版本
       */
      rankVersionList?: string[]
      /**
       * 榜单版本时间
       */
      rankVersionTimeList?: string[]
      /**
       * 排序信息
       */
      sortInfos?: SortInfo[]
      /**
       * 站点信息
       */
      station: string
      /**
       * 二期新增
       * 人群
       */
      targetAudiences?: string[]
      /**
       * 性别
       */
      targetSexs?: string[]
    } & PageParams
  >

  export interface SortInfo {
    param: string
    priority: number
    rules: 'asc' | 'desc' | '' | null
  }

  export type Response = Partial<{
    /**
     * A+ 0-有,1-无
     */
    aAdd?: number
    aAddDesc?: string
    /**
     * 实际
     */
    actualPrice?: number
    /**
     * 所在类目id，亚马逊类目id
     */
    amazonCategoryId?: number
    /**
     * 所在类目-亚马逊类目
     */
    amazonCategoryName?: string
    /**
     * 榜单对应产品在亚马逊的地址信息
     */
    amazonUrl?: string
    /**
     * ASIN
     */
    asin?: string
    /**
     * 平均大类排名
     */
    averageCategoryRanking?: number
    /**
     * BBX卖家属性
     */
    bbxSellerAttribute?: string
    /**
     * 品牌信息
     */
    brand?: string
    /**
     * 大类排名
     */
    categoryRanking?: number
    /**
     * 小类排名及名称
     */
    categoryRankingAndName?: string
    climatePledgeFriendly?: string
    /**
     * 是否气候承诺友好标 0-有,1-无
     */
    climatePledgeFriendlyFlag?: number
    /**
     * 跟卖数量
     */
    counterQuantity?: number
    /**
     * coupon
     */
    coupon?: number
    couponDesc?: string
    /**
     * 划线
     */
    crossedPrice?: number
    /**
     * 目前销售价
     */
    currentSalesPrice?: number
    doBrandFlagshipStore?: string
    /**
     * 是是否做品牌旗舰店 0-是,1-否
     */
    doBrandFlagshipStoreFlag?: number
    /**
     * 预计Listing月销量
     */
    estimatedMonthlyListingSales?: number
    /**
     * 预计月销售额
     */
    estimatedMonthlySalesRevenue?: number
    /**
     * fba费用
     */
    fbaCost?: number
    /**
     * 五点描述
     */
    fivePointDesc?: string
    /**
     * 毛利
     */
    grossProfit?: number
    /**
     * 毛利率
     */
    grossProfitRate?: number
    id?: number
    /**
     * 上架天数
     */
    listingDays?: number
    /**
     * 上架时间
     */
    listingTime?: string
    /**
     * 物流方式
     */
    logisticsMode?: string
    /**
     * 月度同品牌销量占比(市场份额%)
     */
    marketShare?: number
    /**
     * 货币单位
     */
    monetaryUnit?: string
    /**
     * 国籍/地区
     */
    nationalRegion?: string
    /**
     * ParentAsin
     */
    parentAsin?: string
    /**
     * 平台类型 amazon-亚马逊
     */
    platformType?: string
    /**
     * 优惠
     */
    preferentialPrice?: number
    /**
     * 主图
     */
    primaryImageUrl?: string
    /**
     * 主图视频 0-有,1-无
     */
    primaryImageVideo?: number
    primaryImageVideoDesc?: string
    /**
     * 产品类型
     */
    productCategory?: string
    /**
     * MMT类目,wms商品名称
     */
    productCategoryName?: string
    /**
     * 产品名称
     */
    productName?: string
    /**
     * 排名
     */
    ranking?: number
    /**
     * 排名变化
     */
    rankingChange?: number
    /**
     * 排名变化率
     */
    rankingChangeRatio?: number
    /**
     * 榜单版本,榜单年月
     */
    rankVersionInfo?: string
    /**
     * 榜单版本,榜单年月
     */
    rankVersionTime?: string
    /**
     * 评分星级
     */
    ratingStar?: number
    recentSevenDayStockout?: string
    /**
     * 近7日是否出现断货,0-是,1-否
     */
    recentSevenDayStockoutFlag?: number
    recentSevenDayStockTension?: string
    /**
     * 近7日是否出现库存紧张,0-是,1-否
     */
    recentSevenDayStockTensionFlag?: number
    recentThirtyDayStockout?: string
    /**
     * 近30日是否出现断货,0-是,1-否
     */
    recentThirtyDayStockoutFlag?: number
    recentThirtyDayStockTension?: string
    /**
     * 近30日是否出现库存紧张,0-是,1-否
     */
    recentThirtyDayStockTensionFlag?: number
    /**
     * 评价数量
     */
    reviesNum?: number
    /**
     * 月度流量圈销量占比(%)
     */
    salesRatio?: number
    /**
     * 尺寸标准
     */
    sizeInfo?: string
    /**
     * 尺寸标准
     */
    sizeStandard?: string
    /**
     * 站点
     */
    station?: string
    /**
     * 店铺名称
     */
    storeName?: string
    /**
     * 变体数量
     */
    variantsQuantity?: number
    /**
     * 体积
     */
    volume?: number
    /**
     * 重量(lb)
     */
    weight?: number
  }>
}

export function getBsrRankingList(req: BsrRankingListApi.Request, signal?: AbortSignal) {
  return to<PagedResponseData<BsrRankingListApi.Response>>(
    service.post('/pdm-base/amazon/rank/queryBsrRankPage', req, { signal })
  )
}

export namespace BsrRankingCategoryApi {
  export type Response = Partial<{
    /**
     * categoryAllName
     */
    categoryAllName?: string
    /**
     * 子节点集合
     */
    childList?: BsrRankingCategoryApi.Response[]
    /**
     * 父类id父类id
     */
    parentKey?: number
    /**
     * 亚马逊分类英文名称
     */
    selectorEnValue?: string
    /**
     * 亚马逊分类id
     */
    selectorKey?: number
    /**
     * 站点
     */
    station?: string
  }>
}

export function getBsrCategoryList(station: string, signal?: AbortSignal) {
  return to<ResponseData<BsrRankingCategoryApi.Response[]>>(
    service.get('/pdm-base/amazon/rank/queryAmazonCategoryBox', { params: { station }, signal })
  )
}

export function getBsrVersionList(signal?: AbortSignal) {
  return to<ResponseData<string[]>>(
    service.get('/pdm-base/amazon/rank/queryRankVersionBox', { signal })
  )
}

export namespace BsrRankingHistoryApi {
  export type Request = Partial<{
    station: string
    asin: string
  }>
  export type Response = {
    /**
     * 货币单位
     */
    monetaryUnit?: string
    /**
     * 月平均单价
     */
    monthlyAverageUnitPrice?: number
    /**
     * Listing月销量
     */
    monthlyListingSales?: number
    /**
     * Listing月销量额,保留两位小数
     */
    monthlyListingSalesVolume?: number
    /**
     * 历史月份（yyyy-MM）
     */
    rankVersion?: string
    rankVersionTime?: string
  }
}

export function getBsrHistory(req: BsrRankingHistoryApi.Request, signal?: AbortSignal) {
  return to<ResponseData<BsrRankingHistoryApi.Response[]>>(
    service.post('/pdm-base/amazon/rank/queryHisSaleUnitPrice', req, { signal })
  )
}
