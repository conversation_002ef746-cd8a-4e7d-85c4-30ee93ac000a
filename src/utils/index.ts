import { every, isArray, isEmpty, isNil, isObject, some } from 'lodash-es'
import type { FormRules } from 'element-plus'
/*
  获取字典内容
*/
import { useDictStoreWithOut } from '@/store/modules/dict'
import JSZip from 'jszip'
import { downloadFile } from '@/api/common'
import dayjs from 'dayjs'
import { saveAs } from 'file-saver'

export type FormatArrayRulesVerify = [key: string, name: string][]

/**
 * @param str 需要转下划线的驼峰字符串
 * @returns 字符串下划线
 */
export const humpToUnderline = (str: string): string => {
  return str.replace(/([A-Z])/g, '_$1').toLowerCase()
}

// 模板字符串类型：将下划线转为小驼峰
export type ToCamelCase<S extends string> = S extends `${infer Head}_${infer Tail}` // 判断是否有下划线
  ? `${Lowercase<Head>}${Capitalize<ToCamelCase<Tail>>}` // 递归处理下划线后的部分
  : Lowercase<S> // 如果没有下划线，直接转小写

/**
 * @param str 需要转小驼峰的下划线字符串
 * @returns 字符串小驼峰
 */
export function toCamelCase<S extends string>(str: S): ToCamelCase<S> {
  return str
    .toLowerCase()
    .replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()) as ToCamelCase<S>
}

/**
 * @param str 需要转驼峰的下划线字符串
 * @returns 字符串驼峰
 */
export const underlineToHump = (str: string): string => {
  if (!str) return ''
  return str.replace(/\-(\w)/g, (_, letter: string) => {
    return letter.toUpperCase()
  })
}

export const setCssVar = (prop: string, val: any, dom = document.documentElement) => {
  dom.style.setProperty(prop, val)
}

/**
 * 查找数组对象的某个下标
 * @param {Array} ary 查找的数组
 * @param {Function} fn 判断的方法
 */
// eslint-disable-next-line
export const findIndex = <T = Recordable>(ary: Array<T>, fn: Fn): number => {
  if (ary.findIndex) {
    return ary.findIndex(fn)
  }
  let index = -1
  ary.some((item: T, i: number, ary: Array<T>) => {
    const ret: T = fn(item, i, ary)
    if (ret) {
      index = i
      return ret
    }
  })
  return index
}

const dictStore = useDictStoreWithOut()
export function getQMSDict(typeDictName, labelDict = 'value', labelDict_zh = 'label') {
  if (!typeDictName) return []
  return (
    dictStore.getDictionariesOpt(dictStore.getDictObj[typeDictName])?.map((v) => {
      return { [labelDict]: v.value, [labelDict_zh]: v.label }
    }) || []
  )
}

export function getQMSDictLabel(typeDictName, value) {
  return getQMSDict(typeDictName)?.find((item) => item.value === value)?.label
}

/**
 * 格式化数组规则
 * @param verify 验证数组
 * @returns 表单规则
 */
export const formatArrayRules = (verify?: FormatArrayRulesVerify): FormRules => {
  return (
    verify?.reduce((acc, [key, name]) => {
      const prefix = name.includes('请') ? '' : '请完善'
      acc[key] = { required: true, message: `${prefix}${name}`, trigger: ['change', 'blur'] }
      return acc
    }, {}) || {}
  )
}

/**
 * 重置对象的值。
 * @template T - 对象的类型。
 * @param obj - 要重置的对象。
 * @returns 重置后的对象。
 */
export const resetObject = <T extends Record<string, unknown>>(obj: T): T => {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key]
      if (isObject(value) && !Array.isArray(value)) {
        resetObject(value as Record<string, unknown>)
      } else if (Array.isArray(value)) {
        ;(obj[key] as Array<unknown>) = []
      } else {
        switch (typeof value) {
          case 'string':
            ;(obj[key] as string) = ''
            break
          case 'boolean':
            ;(obj[key] as boolean) = false
            break
          default:
            ;(obj[key] as undefined) = undefined
            break
        }
      }
    }
  }
  return obj
}

// 对尺码值数组进行排序
export function sort(data: string[]) {
  if (!data || !Array.isArray(data)) {
    return []
  }
  function parseValue(value: string) {
    // Remove parentheses and any text inside
    value = value.replace(/\s*\(.*?\)\s*/g, '')

    // Handle ranges like "46.5-52.3"
    if (value.includes('-')) {
      const [start, end] = value.split('-').map((v) => parseFloat(v))
      return (start + end) / 2 // Take the average for sorting
    }

    // Handle ranges with letters like "M13.5-15/L"
    if (value.includes('/')) {
      const [range] = value.split('/')
      return parseFloat(range.split('-')[0].replace(/[^\d.]/g, ''))
    }

    // Handle sizes like "11.5W"
    if (/\d+\.?\d*W/.test(value)) {
      return parseFloat(value)
    }

    // Handle normal sizes like "49.5 (EUR)"
    return parseFloat(value.replace(/[^\d.]/g, ''))
  }

  // Sort the array using the parsed values
  return data.sort((a, b) => {
    const aValue = parseValue(a)
    const bValue = parseValue(b)
    return aValue - bValue
  })
}

export const hasValue = (val: unknown) => {
  // 处理基本类型
  if (!isObject(val)) {
    return !isNil(val) && val !== ''
  }

  // 处理 null, undefined, 或空值
  if (isEmpty(val)) {
    return false
  }

  if (isArray(val)) {
    return val.length > 0 && every(val, hasValue)
  }

  // 处理对象
  return some(val, (value) => {
    return hasValue(value)
  })
}

/**
 * 根据url获取文件名
 */
export const getFileNameFromUrl = (url: string) => {
  const index = url.lastIndexOf('/')
  // 去除query
  return url.substring(index + 1).split('?')[0]
}

export const downloadZip = async (fileUrls: string[]) => {
  const zip = new JSZip()
  for (const [_, url] of fileUrls.entries()) {
    try {
      const response = await downloadFile(url)
      const blob = response.data
      let filename: string
      if (response.headers['content-disposition']) {
        filename = decodeURI(
          response.headers['content-disposition'].match(/filename=(.*)/)?.[1] || ''
        )
      } else {
        filename = getFileNameFromUrl(url) || dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
      zip.file(filename, blob) // 将 Blob 添加到 ZIP 文件中
    } catch (error) {
      console.error(`Error downloading ${url}:`, error)
    }
  }
  // 生成 ZIP 文件并触发下载
  const zipBlob = await zip.generateAsync({ type: 'blob' })
  saveAs(zipBlob, 'labels.zip')
}

export function getValuesByKey<T extends Record<string, any>>(
  dataList: T[],
  options?: {
    targetKey?: keyof T // 要匹配的字段
    targetValues?: any | any[] // 匹配的值（单个值或数组）
    returnKey?: keyof T // 返回的字段
    childrenKey?: keyof T // 递归的子节点字段
  }
): string {
  const results: string[] = []

  const {
    targetKey = 'selectorKey', // 默认匹配字段
    targetValues,
    returnKey = 'selectorValue', // 默认返回字段
    childrenKey = 'childList' // 默认子节点字段
  } = options || {}
  if (!targetValues || targetValues.length === 0) {
    return ''
  }
  // 如果 targetValues 是单个值，将其转换为数组以统一处理
  const targetValuesArray = Array.isArray(targetValues) ? targetValues : [targetValues]

  // Helper function to process a single data object
  function processData(item: T, parentPath = '') {
    const currentValue = item[returnKey]
    const currentPath = parentPath ? `${parentPath}/${String(currentValue)}` : String(currentValue)

    // 检查当前项是否匹配
    if (targetValuesArray.includes(item[targetKey])) {
      results.push(currentPath)
    }

    // 递归处理子节点
    const children = item[childrenKey] as unknown as T[] | undefined
    if (Array.isArray(children)) {
      children.forEach((child) => processData(child, currentPath))
    }
  }

  // 遍历数据列表
  dataList.forEach((data) => processData(data))

  return results.join(' | ')
}
