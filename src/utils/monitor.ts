// 上报一个事件，例如点击事件，播放事件等，在主动上报中比较常用。
export const trackEvent = (
  category: string,
  action: string,
  name?: string,
  value?: number
): void => {
  if (window._paq) {
    window._paq.push(['trackEvent', category, action, name, value])
  } else {
    console.warn('can not found window._paq')
  }
}

// 二次封装，专门上报弹窗的动作，例如 action 参数可以填写 show, close
export const trackDialogEvent = (action: string, name?: string, value?: number): void => {
  if (window._paq) {
    window._paq.push(['trackEvent', 'Dialog', action, name, value])
  } else {
    console.warn('can not found window._paq')
  }
}

// 上报错误
export const trackErrorEvent = (action: string, name?: string, value?: number): void => {
  if (window._paq) {
    window._paq.push(['trackEvent', 'Error', action, name, value])
  } else {
    console.warn('can not found window._paq')
  }
}

// 上报搜索动作
export const trackSiteSearch = (
  keyword: string,
  category?: string,
  resultsCount?: string
): void => {
  if (window._paq) {
    window._paq.push(['trackSiteSearch', keyword, category, resultsCount])
  } else {
    console.warn('can not found window._paq')
  }
}
