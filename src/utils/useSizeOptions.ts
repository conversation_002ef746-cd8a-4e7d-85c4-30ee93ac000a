import { SizeListAPI } from '@/components/Business/SelectPlus/src/api/types'
import { StatusEnum } from '@/views/basic-library-manage/const'
import { sizeList } from '@/components/Business/SelectPlus/src/api'

export interface TreeNode {
  id?: number
  label: string
  disabled?: boolean
  standard?: boolean
  children?: TreeNode[]
}

/**
 * 将 Data 数组转换为树状结构
 * @param dataList 原始数据数组
 * @param sizeType
 * @returns 树形结构数据
 */
function convertToTree(dataList: SizeListAPI.Data[], sizeType?: string): TreeNode[] {
  return dataList
    .filter((data) => data.sizeType === sizeType || !sizeType)
    .map((data) => ({
      id: data.id,
      label: data.name || 'Unnamed',
      children:
        data.sizeValueList?.map((row) => ({
          id: row.id,
          standard: row.standard,
          label: row.sizeValue || 'Unknown Size',
          disabled: row.status !== StatusEnum.START,
          children: []
        })) || []
    }))
}

export function useSizeOptions(sizeType?: string) {
  const sizeOptions = ref<TreeNode[]>([])
  const fetchSizeList = async () => {
    const { datas } = await sizeList()
    if (datas) {
      sizeOptions.value = convertToTree(datas, sizeType)
    }
  }
  return {
    sizeOptions,
    fetchSizeList
  }
}
