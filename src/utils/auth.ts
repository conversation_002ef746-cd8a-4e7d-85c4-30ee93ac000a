import Cookies from 'js-cookie'
import { useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache()

export const JWT_TOKEN_KEY = 'jwt_token'

export function getToken() {
  return wsCache.get(JWT_TOKEN_KEY) || Cookies.get(JWT_TOKEN_KEY)
}

export function setToken(token) {
  wsCache.set(JWT_TOKEN_KEY, token)
  return Cookies.set(JWT_TOKEN_KEY, token)
}

export function removeToken() {
  wsCache.delete(JWT_TOKEN_KEY)
  Cookies.remove(JWT_TOKEN_KEY)
}
