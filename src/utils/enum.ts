import { ignoreAutoI18n } from '@higgins-mmt/vite-plugin-i18n-transformer/utils'

export enum BannerSource {
  MAIN = 'mainPic_url',
  REFER = 'referPics_url',
  BOTTOM = 'bottomPic_url',
  COLOR = 'colorPics_url',
  FRONT = 'frontPic_url',
  OTHER = 'otherPics_url',
  SIDE = 'sidePics_url'
}

export const bannerSourceToLabel = {
  [BannerSource.MAIN]: ignoreAutoI18n('主图'),
  [BannerSource.REFER]: ignoreAutoI18n('参考图片'),
  [BannerSource.BOTTOM]: ignoreAutoI18n('大底图'),
  [BannerSource.COLOR]: ignoreAutoI18n('配色图'),
  [BannerSource.FRONT]: ignoreAutoI18n('正面图'),
  [BannerSource.SIDE]: ignoreAutoI18n('背面图'),
  [BannerSource.OTHER]: ignoreAutoI18n('其他图片')
}

export enum PageTypeEnum {
  /**
   * 新增
   */
  ADD = 'add',
  /**
   * 编辑
   */
  EDIT = 'edit',
  /**
   * 查看
   */
  VIEW = 'view',
  /**
   * 分配
   */
  ALLOCATE = 'allocate',
  /**
   * 重置
   */
  RESET = 'reset',
  /**
   * 初选
   */
  FIRST = 'first',
  /**
   * 评审
   */
  COMMENT = 'comment'
}

export type CurrentType =
  | PageTypeEnum.VIEW
  | PageTypeEnum.EDIT
  | PageTypeEnum.ADD
  | PageTypeEnum.RESET
  | PageTypeEnum.ALLOCATE
  | PageTypeEnum.FIRST
  | PageTypeEnum.COMMENT

/**
 * @description 跳转页面pageType
 */
export const pageTypeToLabel = {
  [PageTypeEnum.ADD]: ignoreAutoI18n('新增'),
  [PageTypeEnum.EDIT]: ignoreAutoI18n('修改'),
  [PageTypeEnum.VIEW]: ignoreAutoI18n('查看'),
  [PageTypeEnum.ALLOCATE]: ignoreAutoI18n('分配'),
  [PageTypeEnum.RESET]: ignoreAutoI18n('重启'),
  [PageTypeEnum.COMMENT]: ignoreAutoI18n('评审'),
  [PageTypeEnum.FIRST]: ignoreAutoI18n('初选')
}

export enum STATUSENUM {
  /**
   * 接收样品操作
   */
  SAMPLE = 'sample',
  /**
   * 变更状态操作
   */
  STATUS = 'status'
}
