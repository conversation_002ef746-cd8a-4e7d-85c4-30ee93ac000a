import router from './router'
// import { useAppStoreWithOut } from '@/store/modules/app'
import { useUserStoreWithOut } from './store/modules/user'
// import { useCache } from '@/hooks/web/useCache'
import type { RouteRecordRaw } from 'vue-router'
import { useI18nTitle, useTitle } from '@/hooks/web/useTitle'
import { useNProgress } from '@/hooks/web/useNProgress'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { usePageLoading } from '@/hooks/web/usePageLoading'
import { JWT_TOKEN_KEY, setToken } from '@/utils/auth'
import { cloneDeep } from 'lodash-es'
import { useDictStoreWithOut } from '@/store/modules/dict'
import { getDictAllApi, getDictApi, userMenus } from '@/api/common'
import { useAppStore } from '@/store/modules/app'
import { useDictAllStoreWithOut } from '@/store/modules/dictAll'
import { findRouteByName } from '@/utils/tree'
import { LocaleEnum, useLocaleStoreWithOut } from '@/store/modules/locale'
import { storeToRefs } from 'pinia'
import { useLocale } from '@/hooks/web/useLocale'
import { io, Socket } from 'socket.io-client'
import { ElNotification, NotificationHandle } from 'element-plus'

const appStore = useAppStore()

const dictStore = useDictStoreWithOut()

const dictAllStore = useDictAllStoreWithOut()

const permissionStore = usePermissionStoreWithOut()

// const appStore = useAppStoreWithOut()

const userStore = useUserStoreWithOut()

// const { wsCache } = useCache()
const { changeLocale } = useLocale()
const { getCurrentLocale } = storeToRefs(useLocaleStoreWithOut())

const { start, done } = useNProgress()

const { loadStart, loadDone } = usePageLoading()

interface UpdatePayload {
  name?: string
  task?: {
    pipelineName?: string
    pipelineTags?: string
  }
}

interface ServerToClientEvents {
  updated: (e: UpdatePayload) => void
}

let notifyIns: NotificationHandle | null = null

function notify() {
  if (notifyIns) {
    return
  }
  notifyIns = ElNotification({
    title: 'PDM',
    type: 'warning',
    duration: 0,
    message: () => {
      return (
        <div>
          检测到新版本，点击此处更新
          <div>如多次出现，请按Ctrl+F5(FN)刷新页面</div>
        </div>
      )
    },
    position: 'bottom-right',
    customClass: 'cursor-pointer',
    onClick: () => {
      notifyIns?.close()
      notifyIns = null
      window.location.reload()
    }
  })
}

let socket: Socket<ServerToClientEvents, {}>
const checkVersionFn = async () => {
  if (import.meta.env.DEV) {
    return
  }
  if (socket) {
    return
  }
  const { version } = await (await fetch(`/version.json`)).json()
  if (version !== __APP_VERSION__) {
    notify()
  }
  socket = io(import.meta.env.VITE_VERSION_URL, {
    transports: ['websocket']
  })
  socket.on('updated', (e) => {
    const isUat = import.meta.env.MODE === 'uat'
    const isPro = import.meta.env.MODE === 'pro'
    if (e.name?.includes('pdm')) {
      // 测试环境
      notify()
    }
    if (isUat || isPro) {
      const { task } = e
      if (task?.pipelineName?.toLowerCase()?.includes('pdm')) {
        if (isUat && task?.pipelineTags === 'UAT') {
          notify()
        }
        if (isPro && task?.pipelineTags === 'sz-pro') {
          notify()
        }
      }
    }
  })
}
router.beforeEach(async (to, from, next) => {
  start()
  loadStart()

  const { name } = to
  await changeLocale(getCurrentLocale.value.lang || LocaleEnum.ZH_CN)
  checkVersionFn()
  const resetWhiteNameList = ['privacy']

  if (name && resetWhiteNameList.includes(name as string)) {
    next()
  } else {
    const jwtToken = to.query[JWT_TOKEN_KEY] as string
    if (jwtToken) {
      setToken(jwtToken)
      const query = cloneDeep(to.query)
      Reflect.deleteProperty(query, JWT_TOKEN_KEY)
      next({
        path: to.path,
        query: { ...query },
        replace: true
      })
      return
    }
    // get user info
    if (!userStore.userId) {
      const result = await userStore.getClientUserInfo()
      if (!result) {
        next()
        return
      }
    }

    if (!dictStore.getIsSetDict || !dictAllStore.getIsSetDict) {
      const [{ datas: res }, { datas }] = await Promise.all([getDictApi(), getDictAllApi()])
      if (res) {
        dictStore.setDictObj(res)
        dictStore.setIsSetDict(true)
      }
      if (datas && datas.length) {
        dictAllStore.setDictObj(datas)
        dictAllStore.setIsSetDict(true)
      }
    }

    if (permissionStore.getIsAddRouters) {
      const route = findRouteByName(
        permissionStore.getRouters as AppCustomRouteRecordRaw[],
        to.name as string
      )
      if (route?.id) {
        await permissionStore.setResList(route.id!)
      }
      next()
      return
    }

    const { menus = [] } = await userMenus()
    await permissionStore.generateRoutes('admin', menus as AppCustomRouteRecordRaw[])

    permissionStore.getAddRouters.forEach((route) => {
      router.addRoute(route as unknown as RouteRecordRaw)
    })
    const redirectPath = from.query.redirect || to.path
    const redirect = decodeURIComponent(redirectPath as string)
    const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect }
    permissionStore.setIsAddRouters(true)
    next(nextData)
  }
})

router.afterEach((to) => {
  permissionStore.setPermissions((to?.meta?.permissions || []) as (string | void)[])
  const title = useI18nTitle(to.meta)
  useTitle(title)
  appStore.setFooter(true)
  done() // 结束Progress
  loadDone()
})
