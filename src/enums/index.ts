/**
 * 操作枚举类型
 */
export enum OperationTypeEnums {
  /**
   * 添加
   */
  Add = 'ADD',
  /**
   * 修改
   */
  Edit = 'EDIT',
  /**
   * 删除
   */
  Delete = 'DELETE'
}

/**
 * 基础库-是否
 */
export enum CommonYesNoEnums {
  /**
   * 否
   */
  No = '0',
  /**
   * 是
   */
  Yes = '1'
}
export const commonYesNoMap = {
  [CommonYesNoEnums.Yes]: '是',
  [CommonYesNoEnums.No]: '否'
}

/**
 * 基础库-通过/不通过
 */
export enum CommonPassFailEnums {
  /**
   * 通过
   */
  PASS = '1',
  FAIL = '0'
}

/**
 * 产品数据状态
 */
export enum ProductDataStatusEnums {
  /**
   * 草稿
   */
  Draft = 'draft',
  /**
   * 生效
   */
  Effective = 'effective',
  /**
   * 作废
   */
  Invalid = 'invalid'
}

/**
 * 侵权风险等级
 */
export enum InfringementRiskLevelEnums {
  /**
   * 低风险
   */
  Low = '低风险',
  /**
   * 中风险
   */
  Medium = '中风险',
  /**
   * 高风险
   */
  High = '高风险'
}

/**
 * 弹窗类型
 */
export enum DialogTypeEnums {
  /**
   * 新增
   */
  ADD,
  /**
   * 编辑
   */
  EDIT,
  /**
   * 查看
   */
  VIEW
}

export enum InfringementRiskEnums {
  H = 'hr',
  M = 'mr',
  L = 'lr'
}

/**
 * 尺码类型枚举
 */
export enum SizeCodeTypeEnums {
  /**
   * 产品类
   */
  PRODUCT = 'PRODUCT',
  /**
   * 模具类
   */
  MOLD = 'MOLD'
}

/**
 * 卖点枚举
 */
export enum SellPointEnums {
  /**
   * /
   */
  SLASH = 'SLASH'
}

/**
 * 站点枚举
 */
export enum StationEnums {
  /**
   * us
   */
  US = 'US'
}
