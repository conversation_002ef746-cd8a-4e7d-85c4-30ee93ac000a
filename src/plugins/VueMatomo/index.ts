import type { App } from 'vue'
import VueMatomo from 'vue-matomo'

export const setupMatomo = (app: App, router) => {
  app.use(VueMatomo, {
    host: import.meta.env.VITE_API_MATOMO, // 自己的matomo服务器地址，根据tracking code中填写
    siteId: import.meta.env.VITE_API_MATOMO_ID, // siteId值，根据tracking code中填写
    router: router, // 根据router自动注册
    requireConsent: false, // 是否需要在发送追踪信息之前请求许可，默认false
    enableLinkTracking: true,
    trackInitialView: false, // 是否追踪初始页面，默认true
    trackerFileName: 'matomo', // 最终的追踪js文件名，默认'matomo'
    debug: false
  })
}
