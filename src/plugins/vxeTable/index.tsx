import VxeUI, { VxeTooltip } from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import VxeUITable, { VxeUI as VxeTable } from 'vxe-table'
import 'vxe-table/lib/style.css'
import type { App } from 'vue'
import './renderer'

export const setupVuxTable = async (app: App) => {
  const { useI18n } = await import('../../hooks/web/useI18n')
  VxeTable.component(VxeTooltip)
  VxeTable.setConfig({
    i18n: (key, args) => {
      const { t } = useI18n()
      return t(key, args)
    },
    zIndex: 3000,
    size: 'small',
    table: {
      loading: false,
      //    升级之后会有默认minHeight
      minHeight: '0',
      size: 'small',
      autoResize: true,
      border: true,
      validConfig: {
        autoClear: false
      },
      rowConfig: {
        isHover: true
      },
      columnConfig: {
        resizable: true
      },
      cellConfig: {
        height: undefined
      },
      headerCellConfig: {
        height: 40
      },
      showOverflow: false,
      align: 'center',
      showHeaderOverflow: true
    }
  })

  app.use(VxeUI).use(VxeUITable)
}

export const scrollProp = {
  scrollX: {
    enabled: true,
    gt: 0
  },
  scrollY: {
    enabled: true,
    gt: 0
  }
}
