import { VxeGlobalRendererHandles, VxeUI } from 'vxe-table'
import { ElTooltip } from 'element-plus'
import XEUtils from 'xe-utils'

interface Props {
  separator?: string
  maxRow?: number
  format?: () => string[]
}

interface RenderOptions extends VxeGlobalRendererHandles.RenderOptions {
  props?: Props
}

const defaultFormat = (property: unknown) => property

VxeUI.renderer.add('Ellipsis', {
  renderTableDefault(
    renderOpts: RenderOptions,
    params: VxeGlobalRendererHandles.RenderTableDefaultParams
  ) {
    const { props } = renderOpts
    const { row, column } = params
    const property = XEUtils.get(row, column.field)
    const maxRow = props?.maxRow || 5

    const format = props?.format || defaultFormat
    let formatValue: string[] = []
    if (property) {
      if (typeof property === 'string') {
        const separator = props?.separator || '，'
        formatValue = property.split(separator)
      }
      if (Array.isArray(property)) {
        formatValue = format(property) as string[]
      }
    }
    return [
      <ElTooltip show-after={200} effect="dark" placement="top">
        {{
          content: () => formatValue.map((e) => <div key={e}>{e}</div>),
          default: () => (
            <div class={'ellipsis-tooltip-wrapper'}>
              {formatValue.slice(0, maxRow).map((e, i) => (
                <div key={e} class="overflow-ellipsis whitespace-nowrap overflow-hidden">
                  {e}
                  {i === maxRow - 1 && formatValue.length > maxRow ? <span>...</span> : null}
                </div>
              ))}
            </div>
          )
        }}
      </ElTooltip>
    ]
  }
})
