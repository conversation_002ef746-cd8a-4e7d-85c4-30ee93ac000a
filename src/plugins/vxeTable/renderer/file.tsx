import { VxeGlobalRendererHandles, VxeUI } from 'vxe-table'
import { UploadValue } from '@/components/Upload/types'
import XEUtils from 'xe-utils'
import OssUpload from '@/components/Upload/OssUpload.vue'
import { getFileNameFromUrl } from '@/utils'

interface RenderOptions extends VxeGlobalRendererHandles.RenderOptions {
  props?: {
    filename?: string
  }
}

VxeUI.renderer.add('File', {
  renderTableDefault(
    renderOpts: RenderOptions,
    params: VxeGlobalRendererHandles.RenderTableDefaultParams
  ) {
    const { row, column } = params
    const { filename } = renderOpts.props || {}
    const fileList: UploadValue[] = []
    const property: string | string[] | UploadValue | UploadValue[] = XEUtils.get(row, column.field)
    if (typeof property === 'string') {
      const fileName = filename || getFileNameFromUrl(property)
      fileList.push({
        fileName,
        signatureUrl: property
      })
    } else if (Array.isArray(property)) {
      property.forEach((item: string | UploadValue) => {
        if (typeof item === 'string') {
          const fileName = filename || getFileNameFromUrl(item)
          fileList.push({
            fileName,
            signatureUrl: item
          })
          return
        }
        if (item.signatureUrl) {
          item.fileName = item.fileName || filename || getFileNameFromUrl(item.signatureUrl)
          fileList.push(item)
        }
      })
    } else if (property?.signatureUrl) {
      property.fileName = property.fileName || filename || getFileNameFromUrl(property.signatureUrl)
      fileList.push(property)
    }

    return [
      fileList.length ? (
        <OssUpload modelValue={fileList} listType="text" disabled></OssUpload>
      ) : (
        <span></span>
      )
    ]
  }
})
