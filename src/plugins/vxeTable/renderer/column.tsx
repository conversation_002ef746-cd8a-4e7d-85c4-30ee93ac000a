import { VxeGlobalRendererHandles, VxeUI } from 'vxe-table'
import { ImageColumn } from './rendercolumns/index'

VxeUI.renderer.add('LinkColumn', {
  renderTableDefault(
    renderOpts: VxeGlobalRendererHandles.RenderOptions,
    params: VxeGlobalRendererHandles.RenderTableDefaultParams
  ) {
    const { row, column } = params
    const { events, props } = renderOpts

    return [
      <span onClick={() => events?.click(params)} class="text-blue-400 cursor-pointer">
        {(events?.formatter && events?.formatter(params)) || row[props?.renderName || column.field]}
      </span>
    ]
  }
})

const renderImageColumn = (renderOpts, params) => {
  return <ImageColumn params={params} renderOpts={renderOpts} />
}
VxeUI.renderer.add('ImageColumn', {
  renderTableDefault: renderImageColumn
})
