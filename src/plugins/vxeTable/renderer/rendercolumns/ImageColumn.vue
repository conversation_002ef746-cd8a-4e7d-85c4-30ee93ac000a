<script lang="tsx">
import { ElImage } from 'element-plus'
import { PropType } from 'vue'
import { VxeGlobalRendererHandles } from 'vxe-table'

export default defineComponent({
  name: 'ImageColumn',
  props: {
    renderOpts: {
      type: Object as PropType<VxeGlobalRendererHandles.RenderDefaultOptions>,
      default: () => {}
    },
    params: {
      type: Object as PropType<VxeGlobalRendererHandles.RenderHeaderParams>,
      default: () => {}
    }
  },
  setup(props) {
    const renderContent = () => {
      const { props: columnProps } = props.renderOpts
      const url = columnProps?.url(props.params) || ''
      return url ? (
        <ElImage
          class="w-[40px] h-[35px]"
          hide-on-click-modal
          src={url}
          lazy
          preview-src-list={[url]}
        ></ElImage>
      ) : null
    }
    return () => <div class="flex justify-center">{renderContent()}</div>
  }
})
</script>
