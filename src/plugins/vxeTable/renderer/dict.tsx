import { VxeGlobalRendererHandles, VxeUI } from 'vxe-table'
import XEUtils from 'xe-utils'

interface Props {
  dictMap?: Record<string, string>
  class?: string
  separator?: string
}

interface RenderOptions extends VxeGlobalRendererHandles.RenderOptions {
  props?: Props
}

VxeUI.renderer.add('Dict', {
  renderTableDefault(
    renderOpts: RenderOptions,
    params: VxeGlobalRendererHandles.RenderTableDefaultParams
  ) {
    const { row, column } = params
    const { props } = renderOpts
    const property = XEUtils.get(row, column.field)
    if (!property) {
      return [<span class={props?.class}></span>]
    }
    if (Array.isArray(property)) {
      return [
        <span class={props?.class}>
          {property.map((item) => props?.dictMap?.[item]).join(props?.separator || ';')}
        </span>
      ]
    }
    return [<span class={props?.class}>{props?.dictMap?.[property]}</span>]
  }
})
