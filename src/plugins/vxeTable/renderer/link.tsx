import { VxeGlobalRendererHandles, VxeUI } from 'vxe-table'
import { ElButton } from 'element-plus'
import XEUtils from 'xe-utils'
import { Icon } from '@/components/Icon'

interface Props {
  clickFn?: (params: VxeGlobalRendererHandles.RenderTableDefaultParams) => void
  copyFn?: (params: VxeGlobalRendererHandles.RenderTableDefaultParams) => void
}

interface RenderOptions extends VxeGlobalRendererHandles.RenderOptions {
  props?: Props
}
VxeUI.renderer.add('Link', {
  renderTableDefault(
    renderOpts: RenderOptions,
    params: VxeGlobalRendererHandles.RenderTableDefaultParams
  ) {
    const { props } = renderOpts
    const { row, column } = params
    const property = XEUtils.get(row, column.field)
    return (
      <div style="display: flex;" class="table-render">
        <ElButton
          style="max-width:88%"
          link
          type="primary"
          onClick={() => props?.clickFn?.(params)}
        >
          <span style="white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">
            {property}
          </span>
        </ElButton>
        {property ? (
          <ElButton style="margin-left:1px" link>
            <Icon
              class="mr-1"
              size="12"
              icon="ep:copy-document"
              onClick={() => {
                navigator.clipboard?.writeText(row[column?.field])
                ElMessage({
                  message: '复制成功',
                  type: 'success',
                  plain: true
                })
              }}
            />
          </ElButton>
        ) : null}
      </div>
    )
  }
})
