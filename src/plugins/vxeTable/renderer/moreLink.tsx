import { VxeGlobalRendererHandles, VxeUI } from 'vxe-table'
import { ElButton } from 'element-plus'
import XEUtils from 'xe-utils'

interface Props {
  clickFn?: (code: string, params: VxeGlobalRendererHandles.RenderTableDefaultParams) => void
}

interface RenderOptions extends VxeGlobalRendererHandles.RenderOptions {
  props?: Props
}
VxeUI.renderer.add('moreLink', {
  renderTableDefault(
    renderOpts: RenderOptions,
    params: VxeGlobalRendererHandles.RenderTableDefaultParams
  ) {
    const { props } = renderOpts
    const { row, column } = params
    const property = XEUtils.get(row, column.field)
    const propertyArray = property.split(/[\s,，]+/).filter(Boolean)
    console.log(propertyArray)
    return (
      <div>
        {propertyArray.map((item: string, index: number) => {
          return (
            <span>
              <ElButton
                style="max-width:88%;margin-right:4px;"
                link
                type="primary"
                onClick={() => props?.clickFn?.(item, row)}
              >
                <span style="white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">
                  {item}
                </span>
              </ElButton>
              {index < propertyArray.length - 1 ? ',' : ''}
            </span>
          )
        })}
      </div>
    )
  }
})
