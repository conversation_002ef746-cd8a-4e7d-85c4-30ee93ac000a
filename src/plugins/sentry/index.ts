import * as Sentry from '@sentry/vue'
import type { App } from 'vue'

const isDev = import.meta.env.DEV // 检查当前环境是否为开发环境
/**
 * 初始化Sentry
 * @param app Vue应用实例
 * 使用Sentry来初始化错误追踪，通过配置不同的参数来实现错误追踪和用户会话回放等功能
 */
export const setupSentry = (app: App) => {
  if (isDev) return // 如果是开发或uat环境，跳过 Sentry 初始化
  Sentry.init({
    app,
    logErrors: false,
    // DSN (Data Source Name): 数据源链接，用于指定错误和性能数据的上传地址
    dsn: import.meta.env.VITE_SENTRY_DSN,

    // 集成插件：
    // Sentry.browserTracingIntegration(): 启用浏览器端的自动追踪功能，用于监控应用的性能（如页面加载时间、接口响应时间等）。
    // Sentry.replayIntegration(): 启用会话重放功能，可以记录用户的操作过程（如点击、滚动等），在错误发生时回放用户的交互过程，有助于更直观地分析问题。
    integrations: [
      Sentry.browserTracingIntegration(),
      Sentry.replayIntegration(),
      Sentry.breadcrumbsIntegration({
        dom: false
      })
    ],

    // 错误追踪的采样率配置:
    // tracesSampleRate: 控制性能追踪的采样率。设为 1.0 表示捕获所有事务（100%）。在生产环境中，你可以根据需求调整这个值，例如 0.1 表示仅捕获 10% 的事务数据。
    tracesSampleRate: 1.0, // 捕获100%的交易

    // // 分布式追踪的目标:
    // // tracePropagationTargets: 定义了在进行分布式追踪时哪些 URL 需要启用追踪。这对于你需要监控特定 API 的性能非常有用。可以使用字符串或正则表达式来匹配目标 URL。
    // tracePropagationTargets: ['localhost', /^https:\/\/sentry.test.wangoon.cn\.io\/api/],

    // 会话重放的配置:
    // replaysSessionSampleRate: 控制会话重放的采样率，0.1 表示采样 10% 的用户会话。你可以在开发环境中将此值设为 1.0（100%），以便更全面地收集数据，然后在生产环境中将其降低。
    replaysSessionSampleRate: 0.1, // 将采样率设置为10%。在开发过程中，您可能希望将其更改为100%，然后在生产环境中以较低的采样率进行采样。

    // 错误时的会话重放采样率:
    // replaysOnErrorSampleRate: 设置为 1.0，表示当发生错误时，确保 100% 采样当前会话的操作记录。即使总体会话采样率较低，一旦有错误发生，该会话的数据都会被完整记录。
    replaysOnErrorSampleRate: 1.0 // 如果您尚未对整个会话进行采样，则在对发生错误的会话进行采样时将采样率更改为100%
  })
}
