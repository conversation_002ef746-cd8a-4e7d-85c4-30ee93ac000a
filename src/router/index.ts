import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import type { App } from 'vue'
import { Layout } from '@/utils/routerHelper'
import { LocaleEnum } from '@/store/modules/locale'
import { ignoreAutoI18n } from '@higgins-mmt/vite-plugin-i18n-transformer/dist/utils'
// import { ignoreAutoI18n } from '@higgins-mmt/vite-plugin-i18n-transformer'
// import { SystemToolRouterMap } from './SystemTool/index'
// import { internalTransactionRouterMap } from './InternalTransaction/index'

export const constantRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    redirect: '/home',
    name: 'Root',
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/home',
    component: Layout,
    name: 'home',
    meta: {
      i18n: {
        [LocaleEnum.ZH_CN]: ignoreAutoI18n('工作台'),
        [LocaleEnum.EN_US]: 'Workbench'
      },
      breadcrumb: false
    },
    children: [
      {
        path: '',
        component: () => import('@/views/workbench/index.vue'),
        name: 'Home',
        meta: {
          i18n: {
            [LocaleEnum.ZH_CN]: ignoreAutoI18n('工作台'),
            [LocaleEnum.EN_US]: 'Workbench'
          },
          affix: true,
          icon: 'cil:house'
        }
      }
    ]
  },
  {
    path: '/redirect',
    component: Layout,
    name: 'RedirectIndex',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFind',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  }
]

export const asyncRouterMap: AppRouteRecordRaw[] = []

const router = createRouter({
  history: createWebHistory(),
  strict: true,
  routes: [],
  scrollBehavior: () => ({ left: 0, top: 0 })
})

export const resetRouter = (): void => {
  const resetWhiteNameList = ['Redirect', 'Login', 'NoFind', 'Root', 'privacy']
  router.getRoutes().forEach((route) => {
    const { name } = route
    if (name && !resetWhiteNameList.includes(name as string)) {
      router.hasRoute(name) && router.removeRoute(name)
    }
  })
}

export const setupRouter = (app: App<Element>) => {
  constantRouterMap.forEach((e) => {
    router.addRoute(e as RouteRecordRaw)
  })
  app.use(router)
}

export default router
