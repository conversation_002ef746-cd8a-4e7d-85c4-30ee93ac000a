const config: {
  base_url: {
    base: string
    dev: string
    pro: string
    test: string
  }
  result_code: number | string
  default_headers: AxiosHeaders
  request_timeout: number
  status_success: number
  redirection_code: ['302', '401']
} = {
  /**
   * api请求基础路径
   */
  base_url: {
    // 开发环境接口前缀
    base: '',

    // 打包开发环境接口前缀
    dev: '',

    // 打包生产环境接口前缀
    pro: '',

    // 打包测试环境接口前缀
    test: ''
    // mock接口前缀
  },
  /**
   * 成功状态
   */
  status_success: 200,
  /**
   * 重定向codes
   */
  redirection_code: ['302', '401'],
  /**
   * 接口成功返回状态码
   */
  result_code: '1',

  /**
   * 接口请求超时时间
   */
  request_timeout: 600000,

  /**
   * 默认接口请求类型
   * 可选值：application/x-www-form-urlencoded multipart/form-data
   */
  default_headers: 'application/json'
}

export { config }
