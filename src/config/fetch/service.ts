import axios, { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import qs from 'qs'
import { config } from './config'
import { ElMessage } from 'element-plus'
import { getToken, removeToken } from '@/utils/auth'
import { useLocaleStoreWithOut } from '@/store/modules/locale'

declare module 'axios' {
  interface AxiosResponse<T = any, D = any> {
    datas: T
  }
}

const { result_code, redirection_code, status_success } = config

// 创建axios实例
const service: AxiosInstance = axios.create({
  timeout: config.request_timeout,
  validateStatus() {
    return true
  },
  paramsSerializer(params) {
    return qs.stringify(params, {
      arrayFormat: 'comma'
    })
  }
})

// request拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    if (config.url && config.url.startsWith('/smt-web')) {
      config.baseURL = import.meta.env.VITE_TRENDS_URL
    }

    if (config.headers) {
      config.headers['x-referer'] = location.href
      if (getToken()) {
        config.headers['Authorization'] = getToken()
      }
      const localeStore = useLocaleStoreWithOut()
      const locale = localeStore.getCurrentLocale
      config.headers['x-lang'] = locale.lang
    }
    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error)
  }
)

// response 拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    if (redirection_code.includes(response.data.responseCode)) {
      removeToken()
      location.replace(response.data.data.redirectPath)
      return
    }
    if (response.status === status_success) {
      if (response.config.responseType === 'blob') return response

      if (response.data.code === result_code || response.data.responseCode === result_code) {
        if (!response.data.msg) response.data.msg = '操作成功'
        return response.data
      }
      if (
        (typeof response.data.isSuccess === 'boolean' && response.data.isSuccess === false) ||
        (typeof response.data.success === 'boolean' && response.data.success === false)
      ) {
        ElMessage.error(response.data.msg || response.data.message || response.data.responseDesc)
        return Promise.reject(response.data)
      }
      return response.data
    } else {
      ElMessage.error(response.data.msg || response.data.message || response.data.responseDesc)
      return Promise.reject(response.data)
    }
  },
  (error: AxiosError) => {
    if (error.code === AxiosError.ERR_CANCELED) {
      return
    }
    console.log('err' + error) // for debug
    ElMessage.error(error.message)
    return Promise.reject(error)
  }
)
export { service }
