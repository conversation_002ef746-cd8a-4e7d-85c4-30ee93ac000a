import { service } from './service'

import { config } from './config'
import { AxiosRequestConfig, Method } from 'axios'
import to from 'await-to-js'

const { default_headers } = config

const request = (option: Partial<AxiosRequestConfig> & { headersType: string }) => {
  const { url, method, params, data, headersType, responseType, signal } = option
  return service({
    ...option,
    url: url,
    method,
    params,
    data,
    responseType: responseType,
    headers: {
      'Content-Type': headersType || default_headers
    },
    signal
  })
}
export default {
  get: <T = any>(option: any) => {
    return request({ method: 'get', ...option }) as unknown as T
  },
  post: <T = any>(option: any) => {
    return request({ method: 'post', ...option }) as unknown as T
  },
  delete: <T = any>(option: any) => {
    return request({ method: 'delete', ...option }) as unknown as T
  },
  put: <T = any>(option: any) => {
    return request({ method: 'put', ...option }) as unknown as T
  }
}

// 抽取通用请求逻辑
async function formatRequest<T>(
  method: Method,
  config: AxiosRequestConfig,
  headers?: AxiosRequestConfig['headers']
): Promise<T | undefined> {
  const [error, response] = await to<T>(
    service.request({
      method,
      ...config,
      headers: {
        ...headers,
        ...config.headers
      }
    })
  )

  if (!error && response) {
    return response
  }
}

// GET 请求方法
export function get<T>(config: AxiosRequestConfig) {
  return formatRequest<T>('GET', config)
}

// POST JSON 请求方法
export function postJSON<T>(config: AxiosRequestConfig) {
  return formatRequest<T>('POST', config, {
    'Content-Type': 'application/json'
  })
}

export function postFormData<T>(config: AxiosRequestConfig) {
  return formatRequest<T>('POST', config, {
    'Content-Type': 'multipart/form-data'
  })
}

export function postForm<T>(config: AxiosRequestConfig) {
  return formatRequest<T>('POST', config, {
    'Content-Type': 'application/x-www-form-urlencoded'
  })
}
