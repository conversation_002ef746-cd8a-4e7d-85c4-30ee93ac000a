export interface BasicDataPageResponseData {
  current?: number
  size?: number
  typeName?: string
  id?: number | string
  typeCode?: string
  remark?: string
  [key: string]: any
}

export interface BasicDataPageRequestData {
  current: number
  size: number
  typeName?: string
  typeCode?: string
}

export interface BasicDataItemResponseData {
  current?: number
  size?: number
  dictName?: string
  dictValue?: string
  id?: number
  [key: string]: any
}

export interface BasicDataItemRequestData {
  current: number
  size: number
  dictName?: string
  dictValue?: string
}

export interface UploadRequestData {
  configCode?: string
  fileName?: string
  fileType?: string
  mbSize?: number
}

export interface UploadResponseData {
  accessid?: string
  callback?: string
  configCode?: string
  expire?: string
  host?: string
  objectName?: string
  originName?: string
  policy?: string
  signature?: string
  success_action_status?: number
}

export interface UploadRequestCallbackData {
  name: string
  key?: string
  policy?: string
  OSSAccessKeyId?: string
  success_action_status?: number
  callback?: string
  signature?: string
  file?: any
}

/**
 * Result«PageBean«ErrorCodePageDTO»»
 */
export interface ErrorCodePageResponseData {
  /**
   * 返回标记：成功标记=0，其余都是失败
   */
  code?: string
  datas?: PageBeanErrorCodePageDTO
  /**
   * 是否成功
   */
  isSuccess?: boolean
  /**
   * 返回信息
   */
  msg?: string
}

/**
 * PageBean«ErrorCodePageDTO»
 */
export interface PageBeanErrorCodePageDTO {
  empty?: boolean
  pager?: pager
  records?: ErrorCodePageDTO[]
}

/**
 * ErrorCodePageDTO
 */
export interface ErrorCodePageDTO {
  /**
   * 错误编码
   */
  errorCode?: string
  /**
   * 错误消息
   */
  errorMsg?: string
  /**
   * 主键编号
   */
  id?: number
  /**
   * 一级模块编号
   */
  module1Id?: string
  /**
   * 一级模块名称
   */
  module1Name?: string
  /**
   * 二级模块编号
   */
  module2Id?: string
  /**
   * 二级模块名称
   */
  module2Name?: string
  /**
   * 三级模块编号
   */
  module3Id?: string
  /**
   * 三级模块名称
   */
  module3Name?: string
  /**
   * 解决方案,富文本
   */
  solution?: string
}

export interface ErrorCodeViewDTO {
  /**
   * 错误编码
   */
  code?: string
}

/**
 * Result«ErrorCodePageDTO»
 */
export interface ErrorCodeViewResponse {
  /**
   * 返回标记：成功标记=0，其余都是失败
   */
  code?: string
  datas?: ErrorCodePageDTO
  /**
   * 是否成功
   */
  isSuccess?: boolean
  /**
   * 返回信息
   */
  msg?: string
}

/**
 * ErrorCodeUpdateDTO
 */
export interface ErrorCodeUpdateDTO {
  /**
   * 错误消息, 模糊查询
   */
  errorMsg?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 解决方案,富文本
   */
  solution?: string
}

/**
 * Result«ListModuleDTO»
 */
export interface ModuleResponse {
  /**
   * 返回标记：成功标记=0，其余都是失败
   */
  code?: string
  datas?: ListModuleDTO
  /**
   * 是否成功
   */
  isSuccess?: boolean
  /**
   * 返回信息
   */
  msg?: string
}

/**
 * ListModuleDTO
 */
export interface ListModuleDTO {
  /**
   * 一级模块
   */
  module1?: ModuleDTO[]
  /**
   * 二级模块
   */
  module2?: ModuleDTO[]
  /**
   * 三级模块
   */
  module3?: ModuleDTO[]
}

/**
 * ModuleDTO
 */
export interface ModuleDTO {
  /**
   * 模块编号
   */
  moduleId?: string
  /**
   * 模块名称
   */
  moduleName?: string
}

export interface moduleListDTO {
  module1Id?: string | void
  module2Id?: string | void
}
