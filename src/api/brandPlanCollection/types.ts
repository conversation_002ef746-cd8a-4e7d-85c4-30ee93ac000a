/**
 * 分页查询企划案
 */
export namespace ProposalPageAPI {
  export interface Params {
    /**
     * 品牌ID列表
     */
    brandIdList?: number[]
    /**
     * 操作人列表
     */
    operatorIdList?: number[]
    /**
     * 销售季节列表
     */
    saleSeasonList?: string[]
  }
  export interface List {
    /**
     * 品牌ID
     */
    brandId?: number
    /**
     * 品牌名称
     */
    brandName?: string
    categoryId?: string
    /**
     * 产品类目ID列表
     */
    categoryIdList?: number[]
    /**
     * 产品类目名称
     */
    categoryName?: string
    /**
     * 创建人
     */
    createById?: number
    createByName?: string
    /**
     * 创建时间
     */
    createTime?: Date
    delFlag?: number
    /**
     * 企划案文档
     */
    fileDTOList?: BaseFileDTO[]
    id?: number
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作人
     */
    modifyByName?: string
    /**
     * 操作时间
     */
    modifyTime?: Date
    /**
     * 备注
     */
    remark?: string
    /**
     * 销售季节
     */
    saleSeason?: string
    /**
     * 汇总款式数
     */
    totalNum?: number
  }
  export type Request = Params & PageParams
  export type Response = PagedResponseData<List>
}

/**
 * 新增企划案
 */
export namespace ProposalAddAPI {
  export interface Params {
    /**
     * 品牌ID
     */
    brandId?: number
    /**
     * 品牌名称
     */
    brandName?: string
    /**
     * 产品类目ID列表
     */
    categoryIdList?: number[]
    errorList?: string[]
    /**
     * 文件列表
     */
    fileDTOList?: BaseFileDTO[]
    /**
     * 备注
     */
    remark?: string
    /**
     * 上市季节
     */
    saleSeason?: string
    /**
     * 汇总款式数
     */
    totalNum?: number
  }
  export type Request = Params
  export type Response = BasicResponseData
}

/**
 * 修改产品卖点
 */
export namespace ProposalUpdateAPI {
  export interface Params {
    /**
     * 品牌ID
     */
    brandId?: number
    /**
     * 品牌名称
     */
    brandName?: string
    /**
     * 产品类目ID列表
     */
    categoryIdList?: number[]
    errorList?: string[]
    /**
     * 文件列表
     */
    fileDTOList?: BaseFileDTO[]
    /**
     * 数据ID
     */
    id?: number
    /**
     * 备注
     */
    remark?: string
    /**
     * 上市季节
     */
    saleSeason?: string
    /**
     * 汇总款式数
     */
    totalNum?: number
  }
  export type Request = Params
  export type Response = BasicResponseData
}

/**
 * 删除企划案
 */
export namespace ProposalDeleteAPI {
  export interface Params {
    id: number
  }
  export type Request = Params
  export type Response = BasicResponseData
}
