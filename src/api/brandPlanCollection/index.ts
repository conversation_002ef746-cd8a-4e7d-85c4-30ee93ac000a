import request from '@/config/fetch'
import type { ProposalPageAPI, ProposalAddAPI, ProposalUpdateAPI, ProposalDeleteAPI } from './types'

/**
 * 分页查询企划案
 */
export const proposalPage = (data: ProposalPageAPI.Request): Promise<ProposalPageAPI.Response> => {
  return request.post({ url: '/pdm-base/proposal/page', data })
}

/**
 * 新增企划案
 */
export const proposalAdd = (data: ProposalAddAPI.Request): Promise<ProposalAddAPI.Response> => {
  return request.post({ url: '/pdm-base/proposal/add', data })
}
/**
 * 修改产品卖点
 */
export const proposalUpdate = (
  data: ProposalUpdateAPI.Request
): Promise<ProposalUpdateAPI.Response> => {
  return request.post({ url: '/pdm-base/proposal/update', data })
}
/**
 * 删除企划案
 */
export const proposalDelete = (
  params: ProposalDeleteAPI.Request
): Promise<ProposalDeleteAPI.Response> => {
  return request.get({ url: '/pdm-base/proposal/delete', params })
}
