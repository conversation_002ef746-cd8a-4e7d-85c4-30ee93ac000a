/**
 * 退出登录
 */
export type UserLogoutType = {
  loginPage: string
}
/**
 * EmployeeDTO
 */
export interface EmployeeDTO {
  departmentId?: string
  departments?: EmployeeDepartmentDTO[]
  directManagerId?: string
  emailAddress?: string
  employeeId?: string
  employeeNumber?: string
  gender?: string
  nameCn?: string
  nameEn?: string
  userId?: string
  [property: string]: any
}

/**
 * EmployeeDepartmentDTO
 */
export interface EmployeeDepartmentDTO {
  departmentId?: string
  departmentName?: string
  [property: string]: any
}

export interface VendorListRequest {
  useStatus?: number | string
}

export interface VendorListResponse {
  /**
   * 返回标记：成功标记=0，其余都是失败
   */
  code?: string
  datas?: VendorPageDTO[]
  /**
   * 是否成功
   */
  isSuccess?: boolean
  /**
   * 返回信息
   */
  msg?: string
}
export interface VendorPageDTO {
  vendorId?: number
  vendorName?: string
  [propName: string]: any
}
export interface StyleListResponse {
  /**
   * 返回标记：成功标记=0，其余都是失败
   */
  code?: string
  datas?: StylePageDTO[]
  /**
   * 是否成功
   */
  isSuccess?: boolean
  /**
   * 返回信息
   */
  msg?: string
}

export interface StylePageDTO {
  id?: number
  styleName?: string
  [propName: string]: any
}

export interface DictListRequest {
  dictType?: string
}

export interface DictListResponse {
  dictValue?: string
  dictName?: string
  remark?: string
  orderNo?: number
}

export interface FactoryPageDTO {
  /**
   * 工厂id或者供应商id,根据 outgoingSource 联动
   */
  factoryId?: string
  /**
   * 工厂名称或供应商名称,根据 outgoingSource 联动
   */
  factoryName?: string
  /**
   * 外发工厂数据源类型：vendor:供应商、factory:通用配置外发工厂
   */
  outgoingSource?: string
}

export interface BuPageDTO {
  id?: number
  buName?: string
}

export interface PositionPageDTO {
  id?: number
  name?: string
}

export interface WarehousePageDTO {
  warehouseName?: string
  id?: number
}

export interface treeInEffectDTO {
  id?: number
  level?: number
  parentId?: number
  name?: string
  children?: treeInEffectDTO[]
}

/**
 * VendorFilterDTO
 */
export interface VendorFilterDTO {
  /**
   * 启用状态（1：启用，2：未启用）
   */
  useStatus?: number
}

/**
 * VendorPageDTO
 */
export interface VendorPageDTO {
  vendorId?: number
  vendorName?: string
}

/**
 * BaseStyleDetailDTO
 */
export interface BaseStyleDetailDTO {
  brandId?: number
  brandName?: string
  buId?: number
  buName?: string
  categoryIdList?: number[]
  categoryNameList?: string[]
  styleId?: number
  styleName?: string
}

/**
 * Result«List«Vendor»»
 */
export interface ResultListVendor {
  /**
   * 返回标记：成功标记=0，其余都是失败
   */
  code?: string
  /**
   * 数据
   */
  datas?: VendorDTO[]
  /**
   * 是否成功
   */
  isSuccess?: boolean
  /**
   * 返回信息
   */
  msg?: string
}
export interface VendorDTO {
  areaCode?: string
  areaName?: string
  purchasePersonnel?: string
  useStatus?: number
  vendorFull?: string
  vendorId?: VendorId
  vendorName?: string
  id?: number
}
export interface VendorId {
  id?: number
}

export interface BrandDTO {
  brandName?: string
  id?: number
  [propName: string]: any
}

/**
 * ExportTaskFilterDTO
 */
export interface ExportRequest {
  /**
   * 正排序字段
   */
  ascOrderBy?: string
  /**
   * 是否查询总数
   */
  count?: boolean
  /**
   * 创建人
   */
  createById?: number
  /**
   * 创建时间
   */
  createTimeEnd?: Date
  /**
   * 创建时间
   */
  createTimeStart?: Date
  /**
   * 页码
   */
  current?: number
  /**
   * 倒排序字段
   */
  descOrderBy?: string
  /**
   * 主键
   */
  idList?: number[]
  /**
   * 请求requestId
   */
  requestId?: string
  /**
   * 页面尺寸
   */
  size?: number
  /**
   * 多个字段排序配置。
   */
  sortList?: PageSort[]
  /**
   * state
   */
  state?: number
  [property: string]: any
}

/**
 * PageSort
 */
export interface PageSort {
  /**
   * 是否正排序？true是，false否
   */
  ascField?: boolean
  /**
   * 排序的字段
   */
  field?: string
  [property: string]: any
}

/**
 * ExportTaskDTO
 */
export interface ExportTaskDTO {
  /**
   * 创建人userId
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 数据状态（0：正常，1：删除）
   */
  delFlag?: boolean
  /**
   * 错误消息
   */
  errorMsg?: string
  /**
   * 失败次数
   */
  hasRetryCount?: number
  /**
   * id
   */
  id?: number
  /**
   * 修改人userId
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 模板名称
   */
  moduleName?: string
  /**
   * 是否需要飞书通知创建人: 0不通知， 1通知
   */
  needFeishuNotice?: number
  /**
   * 下载URL
   */
  ossUrl?: string
  /**
   * 请求唯一字符串
   */
  requestId?: string
  /**
   * 计划状态：(待执行-0、执行中-1、执行成功-2、执行失败-9)
   */
  state?: number
  /**
   * 计划状态：(待执行-0、执行中-1、执行成功-2、执行失败-9)
   */
  stateI18?: string
  [property: string]: any
}

export interface UserMenusResponse {
  menus: Menu[]
  /**
   * 用户id
   */
  userId: number
}

export interface Menu {
  appId: number
  children: Menu[]
  code: string
  component: string
  hidden: number
  icon: string
  id: number
  level: number
  meta: { [key: string]: any }
  name: string
  parentId: number
  path: string
  redirect: string
  requireAuth: number
  sort: number
}

export namespace GetDictAllAPI {
  export interface Data {
    /**
     * DictItemValueResp
     */
    dictItem?: string
    dictValueList?: DictValueResp[]
    id?: number
  }
  /**
   * DictValueResp
   */
  export interface DictValueResp {
    /**
     * 展示值-中文
     */
    dictCnName?: string
    /**
     * 展示值-英文
     */
    dictEnName?: string
    /**
     * 传值
     */
    dictValue?: string
    id?: number
  }
  export type Response = ResponseData<Data[]>
}

export namespace GetOssSignAPI {
  export interface Params {
    /**
     * 唯一配置id
     */
    configCode?: string
    /**
     * 文件名
     */
    fileName: string
    /**
     * 文件类型
     */
    fileType?: string
    /**
     * 文件大小，单位KB
     */
    kbSize?: number
  }
  export interface Data {
    accessid?: string
    /**
     * 上传回调信息
     */
    callback?: string
    configCode?: string
    /**
     * sign有效期
     */
    expire?: string
    /**
     * 前端请求上传的host
     */
    host?: string
    objectName?: string
    originName?: string
    /**
     * 加密后的策略
     */
    policy?: string
    /**
     * 校验签名
     */
    signature?: string
  }
  export type Request = Params
  export type Response = ResponseData<Data>
}

export namespace CurrentUserAPI {
  export interface UserCacheDto {
    /**
     * 头像
     */
    avatar?: string
    /**
     * 所属城市
     */
    city?: string
    /**
     * 所属部门
     */
    depts?: DeptCacheDto[]
    /**
     * 邮箱
     */
    email?: string
    /**
     * 工号
     */
    employeeNo?: string
    /**
     * 员工类型：1：正式员工；2：实习生；3：外包；4：劳务；5：顾问
     */
    employeeType?: number
    /**
     * 全称
     */
    fullName?: string
    /**
     * 性别(0:保密；1，男；2，女)
     */
    gender?: number
    /**
     * id
     */
    id?: number
    /**
     * 职位
     */
    jobTitle?: string
    /**
     * 直属领导id
     */
    leaderUserId?: number
    /**
     * 直属领导uid
     */
    leaderUserUid?: string
    /**
     * 手机号
     */
    mobile?: string
    /**
     * 手机号国家码
     */
    mobileCountryCode?: string
    /**
     * 名称
     */
    name?: string
    /**
     * 用户来源：FEISHU-飞书；MANUAL-手动添加
     */
    source?: string
    /**
     * 员工状态：0：正常；1：未加入；2：冻结；3：主动退出；4：离职
     */
    status?: number
    /**
     * 唯一标识（取飞书union_id）
     */
    uid?: string
  }

  export interface DeptCacheDto {
    /**
     * id
     */
    id?: number
    /**
     * 部门leader id
     */
    leaderUserId?: number
    /**
     * 部门leader uid
     */
    leaderUserUid?: string
    /**
     * 部门名称
     */
    name?: string
    /**
     * 排序
     */
    order?: number
    /**
     * 父部门id
     */
    parentId?: number
    /**
     * 父部门uid
     */
    parentUid?: string
    /**
     * 来源：FEISHU-飞书；MANUAL-手动添加
     */
    source?: string
    /**
     * 部门类型：REAL:实体部门；DUMMY-虚拟部门
     */
    type?: string
    /**
     * 唯一标识
     */
    uid?: string
  }

  export type Response = ResponseData<UserCacheDto>
}

export namespace UserResAPI {
  export interface Data {
    menuId: number // 菜单id
    res: Res[] // 资源数组
    skipAuth: boolean // 是否跳过授权
    userId: number // 用户id
  }

  export interface Res {
    code: string // 菜单资源code
    name: string // 菜单资源名称
  }
  export type Response = Data // 响应数据类型
}

export namespace OMSExportAPI {
  export interface Params {
    appName?: string
    exportType: string
    reqParam?: string
  }
  export type Response = BasicResponseData
  export type Request = Params
}
