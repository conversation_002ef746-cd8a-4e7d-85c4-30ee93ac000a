import fetch from '@/config/fetch'
import {
  BrandDTO,
  CurrentUserAPI,
  DictListRequest,
  DictListResponse,
  EmployeeDTO,
  ExportRequest,
  ExportTaskDTO,
  GetDictAllAPI,
  GetOssSignAPI,
  OMSExportAPI,
  UserLogoutType,
  UserMenusResponse,
  UserResAPI,
  VendorPageDTO
} from './type'
import type { UserDataType } from '@/types/user'
import to from 'await-to-js'
import { service } from '@/config/fetch/service'
import { ColumnListAPI } from '@/views/basic-library-manage/product-library/api/customColumns'
// 获取所有字典
export const getDictApi = (): Promise<{ datas: TempIResponse }> => {
  return fetch.get({ url: '/pdm/dict/all' })
}

export const getDictByCodeApi = (params: { dictCode: string }): Promise<{ data: [] }> => {
  return fetch.get({ url: `/pdm-base/common/queryOptions/${params.dictCode}` })
}
export const queryCascade = (data: { type: string; value?: string }): Promise<{ data: [] }> => {
  return fetch.post({ url: `/pdm-base/common/queryCascade`, data })
}
// 获取已登录的用户信息
export const getClientUser = (): Promise<ResponseDataType<UserDataType>> => {
  return fetch.post({
    url: '/pdm/common/sso/client/user'
  })
}

// 退出登录
export const clientLogout = (): Promise<ResponseDataType<UserLogoutType>> => {
  return fetch.post({ url: '/pdm/common/sso/client/logout' })
}

/**
 * @description 获取供管用户列表
 */
export const getSupplierManagerListApi = <T = EmployeeDTO>(): Promise<T> => {
  return fetch.get({ url: '/pdm/user/getSupplierMangerUser' })
}

// 产品分类
export const getCategaryApi = async () => {
  return fetch.get({ url: '/pdm/basic/categoryTree' })
}

// 供应商下拉
export const VendorListApi = <T = VendorPageDTO>(data?: any): Promise<T> => {
  return fetch.post({ url: '/pdm/basic/vendorList', data })
}

// 供应商区域下拉列表， 供应商可选
export const getListVendorAreaApi = <T = any>(params?: any): Promise<T> => {
  return fetch.get({ url: '/pdm/basic/listVendorArea', params })
}

// 品牌下拉列表，品牌 ID可选
export const getListBrandApi = <T = BrandDTO>(data?: any): Promise<T> => {
  return fetch.get({ url: '/pdm/basic/listBrand', data })
}

export const getDictItemMapApi = (params: DictListRequest): Promise<DictListResponse[]> => {
  return fetch.get({ url: '/pdm/basic/getDictItemListByTypeCode', params })
}

/**
 * @description 产品中心用户列表
 */
export const getProductCenterListApi = <T = EmployeeDTO>(): Promise<T> => {
  return fetch.get({ url: '/pdm/user/getproductCenterUser' })
}

// 公共导出服务
export const exportPageApi = (data: ExportRequest): Promise<ExportTaskDTO[]> => {
  return fetch.post({ url: '/pdm/export/page', data })
}

export const userMenus = (): Promise<UserMenusResponse> => {
  return fetch.get({ url: '/pdm/common/pms/user/menus' })
}

// 获取所有字典
export const getDictAllApi = (): Promise<GetDictAllAPI.Response> => {
  return fetch.get({ url: '/pdm-base/dict/value/all' })
}
//获取二级
export const getDictLeafOpen = (data: {
  typeCode: string
  itemValue: string
}): Promise<{ data: any[] }> => {
  if (!data.typeCode || !data.itemValue) {
    return Promise.resolve({ data: [] })
  }
  return fetch.get({ url: `/pdm-base/dict/value/leafOpen`, params: data })
}

// 获取oss签名
export const getOssSign = (data: GetOssSignAPI.Request): Promise<GetOssSignAPI.Response> => {
  return fetch.post({ url: '/pdm-base/base/getOssSign', data })
}

// 获取当前用户信息
export const getUserInfo = () => {
  return to<CurrentUserAPI.Response>(fetch.get({ url: '/pdm-base/base/currentUser' }))
}

export const userRes = (menuId: number): Promise<UserResAPI.Response> => {
  return fetch.get({ url: `/pdm-base/common/pms/user/menu/res/${menuId}` })
}

// OMS导出
export const omsExport = (data: OMSExportAPI.Request) => {
  return to<OMSExportAPI.Response>(fetch.post({ url: '/pdm-export/execute-export/execute', data }))
}

export const pdmExport = (data: OMSExportAPI.Request) => {
  return to<OMSExportAPI.Response>(fetch.post({ url: '/pdm-export/execute-export/execute', data }))
}

export function refreshDict() {
  return to<BasicResponseData>(service.get('/pdm-base/dict/value/refreshCache'))
}

export function downloadFile(url: string) {
  return service.get(url, { responseType: 'blob' })
}

export function getAllFieldsByType(type: number) {
  return to<ColumnListAPI.Response>(
    service.get(`/pdm-base/viewConfiguration/getConfigDetail/${type}`)
  )
}
