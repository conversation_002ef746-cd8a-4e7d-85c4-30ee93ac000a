export interface SocialMediaRequest {
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 时间段止
   */
  dateEnd: string
  /**
   * 时间段起
   */
  dateStart: string
  /**
   * 元素
   */
  element: string
  /**
   * 热度指标，01-点赞；02-播放；03-收藏；04-评论；05-转发
   */
  heatIndex: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 页数
   */
  page: number
  /**
   * 分页大小
   */
  pageSize: number
  /**
   * 所属平台id
   */
  platId: number
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 鞋头
   */
  toeCap: string
  [property: string]: any
}

export interface SocialMediaResponse {
  code: string
  datas: SocialMediaDatas
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface SocialMediaDatas {
  /**
   * 当前页
   */
  currentPage: number
  /**
   * 是否还有数据
   */
  hasNext: boolean
  /**
   * 数据
   */
  rows: SocialMediaRow[]
  /**
   * 总条数
   */
  total: number
  /**
   * 总页数
   */
  totalPage: number
  [property: string]: any
}

export interface SocialMediaRow {
  /**
   * 归属日期
   */
  belongedDate: string
  /**
   * 收藏数
   */
  collectsNum: number
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 评论数
   */
  commentsNum: number
  /**
   * 内容爬取时间
   */
  contentCrawledTime: string
  /**
   * 社媒内容id
   */
  contentId: number
  /**
   * 社媒图片id
   */
  contentImgId: number
  /**
   * 图片url地址
   */
  contentImgOssUrl: string
  /**
   * 内容发布账号
   */
  contentPublishAccount: string
  /**
   * 内容发布时间
   */
  contentPublishTime: string
  /**
   * 内容来源账号，当content_source_type=01时必填
   */
  contentSourceAccount: string
  /**
   * 内容来源标签，当content_source_type=02时必填
   */
  contentSourceTag: string
  /**
   * 内容来源类型，01-关注账号，02-关注标签，03-探索，04-推荐
   */
  contentSourceType: string
  /**
   * 元素
   */
  element: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 社媒款式id
   */
  id: number
  /**
   * 点赞数
   */
  likesNum: number
  /**
   * 平台id
   */
  platId: number
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 鞋头
   */
  toeCap: string
  /**
   * 转发数
   */
  transmitNum: number
  /**
   * 播放量
   */
  viewsNum: number
  [property: string]: any
}

export interface SocialKeyColorRequest {
  /**
   * 时间段止
   */
  dateEnd: string
  /**
   * 时间段起
   */
  dateStart: string
  /**
   * 热度指标，01-点赞；02-播放；03-收藏；04-评论；05-转发
   */
  heatIndex: string
  /**
   * 平台id
   */
  platId: number
  [property: string]: any
}

export interface SocialKeyColorResponse {
  code: string
  /**
   * 色系code
   */
  datas: string[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface SocialKeyElementRequest {
  /**
   * 时间段止
   */
  dateEnd: string
  /**
   * 时间段起
   */
  dateStart: string
  /**
   * 热度指标，01-点赞；02-播放；03-收藏；04-评论；05-转发
   */
  heatIndex: string
  /**
   * 平台id
   */
  platId: number
  [property: string]: any
}

export interface SocialKeyElementResponse {
  code: string
  /**
   * 元素code
   */
  datas: string[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface TrendAccountRequest {
  /**
   * 账号类型，01-网红；02-品牌；99-其他
   */
  accountType: string
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 时间段止，yyyy-MM-dd
   */
  dateEnd: string
  /**
   * 时间段起，yyyy-MM-dd
   */
  dateStart: string
  /**
   * 元素
   */
  element: string
  /**
   * 粉丝量级，01,10k-50k; 02,50k-100k; 03,100k-500k; 04,500k-1000k; 05, 1000k以上
   */
  fanScale: string
  /**
   * 热度指标，01-点赞；02-播放；03-收藏；04-评论；05-转发
   */
  heatIndex: string
  /**
   * 限制条数，也就是topN的N
   */
  limit: string
  /**
   * 所属平台id
   */
  platId: string
  [property: string]: any
}

export interface TrendAccountResponse {
  code: string
  datas: TrendAccountData[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface TrendAccountData {
  /**
   * 归属日期
   */
  belongedDate: string
  /**
   * 收藏数
   */
  collectsNum: number
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 评论数
   */
  commentsNum: number
  /**
   * 内容爬取时间
   */
  contentCrawledTime: string
  /**
   * 社媒内容id
   */
  contentId: number
  /**
   * 社媒图片id
   */
  contentImgId: number
  /**
   * 图片url地址
   */
  contentImgOssUrl: string
  /**
   * 内容发布账号
   */
  contentPublishAccount: string
  /**
   * 内容发布时间
   */
  contentPublishTime: string
  /**
   * 内容来源账号，当content_source_type=01时必填
   */
  contentSourceAccount: string
  /**
   * 内容来源标签，当content_source_type=02时必填
   */
  contentSourceTag: string
  /**
   * 内容来源类型，01-关注账号，02-关注标签，03-探索，04-推荐
   */
  contentSourceType: string
  /**
   * 元素
   */
  element: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 社媒款式id
   */
  id: number
  /**
   * 点赞数
   */
  likesNum: number
  /**
   * 平台id
   */
  platId: number
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 鞋头
   */
  toeCap: string
  /**
   * 转发数
   */
  transmitNum: number
  /**
   * 播放量
   */
  viewsNum: number
  [property: string]: any
}

export interface TrendTagRequest {
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 时间段止，yyyy-MM-dd
   */
  dateEnd: string
  /**
   * 时间段起，yyyy-MM-dd
   */
  dateStart: string
  /**
   * 元素
   */
  element: string
  /**
   * 热度指标，01-点赞；02-播放；03-收藏；04-评论；05-转发
   */
  heatIndex: string
  /**
   * 限制条数，也就是topN的N
   */
  limit: string
  /**
   * 所属平台id
   */
  platId: string
  /**
   * 账号类型，01-产品；02-节日；03-行业；04-品牌；99-其他
   */
  tagType: string
  [property: string]: any
}

export interface TrendTagResponse {
  code: string
  datas: TrendTagData[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface TrendTagData {
  /**
   * 归属日期
   */
  belongedDate: string
  /**
   * 收藏数
   */
  collectsNum: number
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 评论数
   */
  commentsNum: number
  /**
   * 内容爬取时间
   */
  contentCrawledTime: string
  /**
   * 社媒内容id
   */
  contentId: number
  /**
   * 社媒图片id
   */
  contentImgId: number
  /**
   * 图片url地址
   */
  contentImgOssUrl: string
  /**
   * 内容发布账号
   */
  contentPublishAccount: string
  /**
   * 内容发布时间
   */
  contentPublishTime: string
  /**
   * 内容来源账号，当content_source_type=01时必填
   */
  contentSourceAccount: string
  /**
   * 内容来源标签，当content_source_type=02时必填
   */
  contentSourceTag: string
  /**
   * 内容来源类型，01-关注账号，02-关注标签，03-探索，04-推荐
   */
  contentSourceType: string
  /**
   * 元素
   */
  element: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 社媒款式id
   */
  id: number
  /**
   * 点赞数
   */
  likesNum: number
  /**
   * 平台id
   */
  platId: number
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 鞋头
   */
  toeCap: string
  /**
   * 转发数
   */
  transmitNum: number
  /**
   * 播放量
   */
  viewsNum: number
  [property: string]: any
}

export interface ChangeStaticsRequest {
  /**
   * 时间段止，yyyy-MM-dd
   */
  dateEnd: string
  /**
   * 时间段起，yyyy-MM-dd
   */
  dateStart: string
  /**
   * 热度指标，01-点赞；02-播放；03-收藏；04-评论；05-转发
   */
  heatIndex: string
  /**
   * 产品小类
   */
  productTinyCategory: string
  [property: string]: any
}

export interface ProductTinyCategoryResponse {
  code: string
  datas: ProductTinyCategoryData[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface ProductTinyCategoryData {
  /**
   * 数量
   */
  count: number
  /**
   * 月份
   */
  month: string
  [property: string]: any
}

export interface ColorSchemeResponse {
  code: string
  datas: ColorSchemeData[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface ColorSchemeData {
  /**
   * 数量
   */
  count: number
  /**
   * 月份
   */
  month: string
  [property: string]: any
}

export interface ElementResponse {
  code: string
  datas: ElementData[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface ElementData {
  /**
   * 数量
   */
  count: number
  /**
   * 月份
   */
  month: string
  [property: string]: any
}

export interface TagTopNResponse {
  code: string
  datas: TagTopNData[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface TagTopNData {
  count: number
  tag: string
  [property: string]: any
}

export interface PieElementResponse {
  code: string
  datas: PieElementData[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface PieElementData {
  /**
   * 数量
   */
  count: number
  /**
   * 元素
   */
  element: string
  [property: string]: any
}

export interface PieColorSchemeResponse {
  code: string
  datas: PieColorSchemeData[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface PieColorSchemeData {
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 数量
   */
  count: number
  [property: string]: any
}

export interface TypeResponse {
  code: string
  datas: TypeData[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface TypeData {
  cnDesc: string
  code: string
  enDesc: string
  [property: string]: any
}
