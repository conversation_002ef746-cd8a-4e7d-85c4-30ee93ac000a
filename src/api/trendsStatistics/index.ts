import request from '@/config/fetch'
import {
  SocialMediaRequest,
  SocialMediaResponse,
  SocialKeyColorRequest,
  SocialKeyColorResponse,
  SocialKeyElementRequest,
  SocialKeyElementResponse,
  TrendAccountRequest,
  TrendAccountResponse,
  TrendTagRequest,
  TrendTagResponse,
  ChangeStaticsRequest,
  ProductTinyCategoryResponse,
  ColorSchemeResponse,
  ElementResponse,
  TagTopNResponse,
  PieElementResponse,
  PieColorSchemeResponse,
  TypeResponse
} from './types'

// 社媒热度趋势分页查询
export const getSocialMediaApi = (data: SocialMediaRequest): Promise<SocialMediaResponse> => {
  return request.post({
    url: '/smt-web/summary/trend/socialMedia',
    data
  })
}

// 重点颜色查询
export const getSocialKeyColorApi = (
  data: SocialKeyColorRequest
): Promise<SocialKeyColorResponse> => {
  return request.post({
    url: '/smt-web/summary/key/color',
    data
  })
}

// 重点元素查询
export const getSocialKeyElementApi = (
  data: SocialKeyElementRequest
): Promise<SocialKeyElementResponse> => {
  return request.post({
    url: '/smt-web/summary/key/element',
    data
  })
}

// 账号热度topN
export const getTrendAccountApi = (data: TrendAccountRequest): Promise<TrendAccountResponse> => {
  return request.post({
    url: '/smt-web/summary/trend/account/topn',
    data
  })
}

// 标签热度topN
export const getTrendTagApi = (data: TrendTagRequest): Promise<TrendTagResponse> => {
  return request.post({
    url: '/smt-web/summary/trend/tag/topn',
    data
  })
}

// 产品小类热度变化统计
export const getProductTinyCategoryApi = (
  data: ChangeStaticsRequest
): Promise<ProductTinyCategoryResponse> => {
  return request.post({
    url: '/smt-web/summary/heat/productTinyCategory',
    data
  })
}

// 色系热度变化统计
export const getColorSchemeApi = (data: ChangeStaticsRequest): Promise<ColorSchemeResponse> => {
  return request.post({
    url: '/smt-web/summary/heat/colorScheme',
    data
  })
}

// 元素热度变化统计
export const getElementApi = (data: ChangeStaticsRequest): Promise<ElementResponse> => {
  return request.post({
    url: '/smt-web/summary/heat/element',
    data
  })
}

// Top10标签统计
export const getTagTopNApi = (data: ChangeStaticsRequest): Promise<TagTopNResponse> => {
  return request.post({
    url: '/smt-web/summary/tag/topn',
    data
  })
}

// 产品小类元素热度统计
export const getPieElementApi = (data: ChangeStaticsRequest): Promise<PieElementResponse> => {
  return request.post({
    url: '/smt-web/summary/heat/pie/element',
    data
  })
}

// 产品小类颜色热度统计
export const getPieColorSchemeApi = (
  data: ChangeStaticsRequest
): Promise<PieColorSchemeResponse> => {
  return request.post({
    url: '/smt-web/summary/heat/pie/colorScheme',
    data
  })
}

// 获取产品小类
export const getTypeApi = (type: string): Promise<TypeResponse> => {
  return request.get({ url: `/smt-web/dict/list/?type=${type}` })
}
