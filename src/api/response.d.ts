/**
 * 基础分页信息接口。
 * 包含分页所需的基本属性。
 */
interface BasicPage {
  /** 当前页码 */
  current: number
  /** 每页显示的记录数 */
  size: number
  /** 总记录数 */
  total: number
  /** 总页数 */
  pages?: number
}

/**
 * 分页请求参数的接口。
 * 用于指定分页请求的当前页和页大小。
 */
interface PageParams {
  /** 当前页码 */
  current: number
  /** 每页显示的记录数 */
  size: number
}

/**
 * 带有分页信息的列表数据的接口。
 * @template ListItemType 列表项的类型
 * @template ToTalDataType 合计数据的类型，默认为 unknown
 */
interface ListPageData<ListItemType, ToTalDataType = unknown> extends BasicPage {
  /** 列表项数据 */
  records: ListItemType[]
  /** 分页数据 */
  pager: BasicPage
  /** 合计数据 */
  totalData: ToTalDataType
}

/**
 * 基本的响应数据结构的接口。
 */
interface BasicResponseData {
  /** 返回标记：成功标记=0，其余都是失败 */
  code: '0' | string
  /** 响应描述 */
  msg: string
  /** 表示请求是否成功 */
  isSuccess: boolean
}

/**
 * 通用的响应数据类型。
 * 结合基本响应数据和泛型参数 T 指定的数据类型。
 * @template T 响应中的数据类型
 */
type ResponseData<T> = BasicResponseData & {
  datas: T
}

/**
 * 分页响应数据类型。
 * 结合基本响应数据和分页列表数据。
 * @template ListItemType 分页列表项的类型
 * @template ToTalDataType 合计数据的类型，默认为 unknown
 */
type PagedResponseData<ListItemType, ToTalDataType = unknown> = BasicResponseData & {
  datas: ListPageData<ListItemType, ToTalDataType>
}
type NewListResponseData<T> = {
  /** 数据列表 */
  records: T[]
} & NewBasicPage
type NewPageResponseData<T> = NewBasicResponseData & {
  data: NewListResponseData<T>
}
type NewResponseData<T> = NewBasicResponseData & {
  data: T
}

interface NewBasicPage {
  currPage: number
  pageSize: number
  totalCount: number
  totalPage?: number
  total?: number
}

interface NewBasicResponseData {
  /** 返回标记：成功标记=0001，其余都是失败 */
  responseCode: string
  /** 响应描述 */
  responseDesc: string
  /** 表示请求是否成功 */
  success: boolean
}
