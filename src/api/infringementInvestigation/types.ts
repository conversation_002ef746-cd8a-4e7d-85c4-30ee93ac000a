/**
 * 检索详情
 */
export interface InfringementSearchQueryResp {
  /**
   * 相关附件
   */
  fileList?: BaseFileDTO[]
  /**
   * 检索途径
   */
  imgList?: BaseFileDTO[]
  /**
   * 检索记录说明
   */
  searchRecordInstruction?: string
  /**
   * 检索途径
   */
  searchWay?: string
}

/**
 * 风险判断
 */
export interface InfringementJudgmentDetailQueryResp {
  /**
   * 侵权风险判定
   */
  infringementRisk?: string
  /**
   * 判定时间
   */
  judgeTime?: string
  /**
   * 禁售平台
   */
  prohibitedPlatformList?: string[]
  /**
   * 风险国家
   */
  riskCountry?: string
}
/**
 * 检索详情
 */
export interface InfringementSearchAddReq {
  errorParamList?: string[]
  /**
   * 相关附件列表
   */
  fileList?: BaseFileDTO[]
  /**
   * 检索图片列表
   */
  imgList?: BaseFileDTO[]
  /**
   * 侵权记录编号
   */
  infringementCode?: string
  /**
   * 检索阶段 1初判 2复判
   */
  investigationPhase?: number
  /**
   * 检索记录说明
   */
  searchRecordInstruction?: string
  /**
   * 检索途径
   */
  searchWay?: string
  /**
   * 暂存标识 1暂存 0正式
   */
  tempStorageFlag?: number
}
/**
 * 侵权排查分页查询
 */
export namespace InfringementPageAPI {
  export interface Params {
    relatedProduct?: boolean
    createByIdList?: number[]
    modifyByIdList?: number[]
    /**
     * 检索分类
     */
    searchCategory?: string
    /**
     * 侵权记录编号
     */
    infringementCode?: string
    /**
     * 过滤暂存状态
     */
    tempStorageFlag?: string
    /**
     * 过滤作废
     */
    delFlag?: string
    emptyProductFlag?: number
    /**
     * 品牌
     */
    brandIdList?: number[]
    /**
     * 设计师
     */
    designerIdList?: number[]
    /**
     * 创建时间-结束时间 yyyy-MM-dd 23:59:59
     */
    endDate?: string
    /**
     * 产品编号
     */
    productCodeList?: string[]
    /**
     * 侵权风险判定
     */
    riskList?: string[]
    /**
     * 上市季节
     */
    saleSeasonList?: string[]
    /**
     * 创建时间-开始时间 yyyy-MM-dd 00:00:00
     */
    startDate?: string
    /**
     * 品牌维权力度
     */
    strengthList?: string[]
  }
  export interface List {
    /**
     * 品牌
     */
    brandName?: string
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: Date
    delFlag?: number
    /**
     * 产品设计师
     */
    designerName?: string
    /**
     * 数据ID
     */
    id?: number
    /**
     * 侵权记录编号
     */
    infringementCode?: string
    /**
     * 侵权风险判定-初判
     */
    infringementRiskPre?: string
    /**
     * 侵权风险判定-复判
     */
    infringementRiskRe?: string
    /**
     * 内控复核人名称-初判
     */
    innerControlUserNamePre?: string
    /**
     * 内控复核人名称-复判
     */
    innerControlUserNameRe?: string
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: Date
    /**
     * 设计师是否需要协助 1是 0否
     */
    needHelpFlag?: string
    /**
     * 操作时间
     */
    operateTime?: string
    /**
     * 操作人
     */
    operator?: string
    /**
     * 关联产品编号
     */
    productCode?: string
    /**
     * 参考品牌
     */
    referenceBrand?: string
    /**
     * 参考品牌链接
     */
    referenceBrandUrl?: string
    /**
     * 风险国家-初判
     */
    riskCountryPre?: string
    /**
     * 风险国家-复判
     */
    riskCountryRe?: string
    /**
     * 品牌维权力度-初判
     */
    safeguardRightsStrengthPre?: string
    /**
     * 品牌维权力度-复判
     */
    safeguardRightsStrengthRe?: string
    /**
     * 上市季节
     */
    saleSeason?: string
    /**
     * 同款鞋对应品牌
     */
    sameShoesBrand?: string
    /**
     * 检索日期-初判
     */
    searchDatePre?: string
    /**
     * 检索日期-复判
     */
    searchDateRe?: string
    /**
     * 状态
     */
    status?: string
    statusCode?: string
    /**
     * 规避建议/风险情况说明-初判
     */
    suggestionInstructionPre?: string
    /**
     * 规避建议/风险情况说明-复判
     */
    suggestionInstructionRe?: string
    /**
     * 暂存标识 1暂存 0正式
     */
    tempStorageFlag?: string
    /**
     * 缩略图
     */
    thumbnail?: string
  }
  export type Request = Params & PageParams
  export type Response = PagedResponseData<List>
}
/**
 * 侵权排查作废
 */
export namespace InfringementVoidAPI {
  export interface Params {
    code?: string
    delReason?: string
  }
  export type Response = BasicResponseData
}
/**
 * 添加侵权初判
 */
export namespace InfringementPreAddAPI {
  export interface Params {
    riskCountryRe?: string[]
    riskCountryPre?: string[]
    /**
     * 其他风险国家
     */
    otherRiskCountry?: string
    /**
     * 关联侵权排查编号
     */
    linkInfringementCode?: string
    /**
     * 设计师名称
     */
    designerName?: string
    errorParamList?: string[]
    /**
     * 侵权记录编号
     */
    infringementCode?: string
    /**
     * 侵权风险判定
     */
    infringementRisk?: string
    /**
     * 侵权排查阶段 取值:初判、复判
     */
    infringementStage?: string
    /**
     * 内控复核人ID
     */
    innerControlUserId?: number
    /**
     * 内控复核人名称
     */
    innerControlUserName?: string
    /**
     * 风险国家
     */
    riskCountry?: string[]
    /**
     * 品牌维权力度
     */
    safeguardRightsStrength?: string
    /**
     * 检索详情列表
     */
    searchAddReqList?: InfringementSearchAddReq[]
    /**
     * 侵权风险判定列表
     */
    infringementDetailList?: InfringementJudgmentDetailQueryResp[]
    /**
     * 检索日期
     */
    searchDate?: string
    /**
     * 规避建议/风险情况说明
     */
    suggestionInstruction?: string
    /**
     * 暂存标识 1暂存 0正式
     */
    tempStorageFlag?: string
  }
  export type Request = Params
  export type Response = BasicResponseData
}
/**
 * 添加侵权复判
 */
export namespace InfringementReAddAPI {
  export interface Params {
    riskCountryRe?: string[]
    riskCountryPre?: string[]
    /**
     * 关联侵权排查编号
     */
    linkInfringementCode?: string
    /**
     * 设计师名称
     */
    designerName?: string
    errorParamList?: string[]
    /**
     * 侵权记录编号
     */
    infringementCode?: string
    /**
     * 侵权风险判定
     */
    infringementRisk?: string
    /*
     * 侵权排查阶段 取值:初判、复判
     */
    infringementStage?: string
    /**
     * 内控复核人ID
     */
    innerControlUserId?: number
    /**
     * 内控复核人名称
     */
    innerControlUserName?: string
    /**
     * 其他风险国家
     */
    otherRiskCountry?: string
    /**
     * 风险国家
     */
    riskCountry?: string[]
    /**
     * 品牌维权力度
     */
    safeguardRightsStrength?: string
    /**
     * 检索详情列表
     */
    searchAddReqList?: InfringementSearchAddReq[]
    /**
     * 检索日期
     */
    searchDate?: string
    /**
     * 规避建议/风险情况说明
     */
    suggestionInstruction?: string
    /**
     * 暂存标识 1暂存 0正式
     */
    tempStorageFlag?: string
  }
  export type Request = Params
  export type Response = BasicResponseData
}
/**
 * 侵权排查初筛
 */
export namespace InfringementScreeningAddAPI {
  export interface Params {
    /**
     * 品牌
     */
    brand?: number
    /**
     * 检索需求
     */
    searchNeed?: string
    /**
     * 检索分类
     */
    searchCategory?: string
    /**
     * 对应品牌鞋图
     */
    brandShoesImgList?: BaseFileDTO[]
    /**
     * 设计师ID
     */
    designerId?: number
    /**
     * 设计师名称
     */
    designerName?: string
    /**
     * 设计图
     */
    designImgList?: BaseFileDTO[]
    /**
     * 图片详情
     */
    detailImgList?: BaseFileDTO[]
    errorParamList?: string[]
    /**
     * 产品是否上市 1是 0否
     */
    marketFlag?: string
    /**
     * 设计师是否需要协助 1是 0否
     */
    needHelpFlag?: string
    /**
     * 产品描述
     */
    productDescription?: string
    /**
     * 参考品牌
     */
    referenceBrand?: string
    /**
     * 参考品牌链接
     */
    referenceBrandUrl?: string
    /**
     * 相关外观图片
     */
    refOutlookImgList?: BaseFileDTO[]
    /**
     * 参考款式图
     */
    refProductImgList?: BaseFileDTO[]
    /**
     * 风险初筛
     */
    riskScreening?: string
    /**
     * 上市季节
     */
    saleSeason?: string
    /**
     * 同款鞋对应品牌
     */
    sameShoesBrand?: string
    /**
     * 立体图
     */
    stereoscopicImgList?: BaseFileDTO[]
    /**
     * 关联产品编号
     */
    styleCode?: string
    /**
     * 暂存标识 1暂存 0正式
     */
    tempStorageFlag?: string
  }
  export type Request = Params
  export type Response = BasicResponseData
}
/**
 * 修改侵权初筛
 */
export namespace InfringementScreeningUpdateAPI {
  export interface Params {
    /**
     * 对应品牌鞋图
     */
    brandShoesImgList?: BaseFileDTO[]
    /**
     * 设计师ID
     */
    designerId?: number
    /**
     * 设计师名称
     */
    designerName?: string
    /**
     * 设计图
     */
    designImgList?: BaseFileDTO[]
    /**
     * 图片详情
     */
    detailImgList?: BaseFileDTO[]
    errorParamList?: string[]
    /**
     * 数据ID
     */
    id?: number
    /**
     * 产品是否上市 1是 0否
     */
    marketFlag?: string
    /**
     * 设计师是否需要协助 1是 0否
     */
    needHelpFlag?: string
    /**
     * 产品描述
     */
    productDescription?: string
    /**
     * 参考品牌
     */
    referenceBrand?: string
    /**
     * 参考品牌链接
     */
    referenceBrandUrl?: string
    /**
     * 相关外观图片
     */
    refOutlookImgList?: BaseFileDTO[]
    /**
     * 参考款式图
     */
    refProductImgList?: BaseFileDTO[]
    /**
     * 风险初筛
     */
    riskScreening?: string
    /**
     * 上市季节
     */
    saleSeason?: string
    /**
     * 同款鞋对应品牌
     */
    sameShoesBrand?: string
    /**
     * 立体图
     */
    stereoscopicImgList?: BaseFileDTO[]
    /**
     * 关联产品编号
     */
    styleCode?: string
    /**
     * 暂存标识 1暂存 0正式
     */
    tempStorageFlag?: string
  }

  export type Request = Params
  export type Response = BasicResponseData
}
/**
 * 侵权排查详情
 */
export namespace InfringementDetailAPI {
  export interface Params {
    id: number
  }
  export interface Data {
    otherRiskCountry?: string
    /**
     * 初判-下发erp时间
     */
    sendErpTimePre?: string
    /**
     * 复判-下发erp时间
     */
    sendErpTimeRe?: string
    /**
     * 品牌
     */
    brand?: string
    /**
     * 关联侵权排查编号-初判
     */
    linkInfringementCodePre?: string
    /**
     * 关联侵权排查编号-复判
     */
    linkInfringementCodeRe?: string
    /**
     * 关联的产品编号
     */
    productNumber?: string
    /**
     * 检索需求
     */
    searchNeed?: string
    /**
     * 检索分类
     */
    searchCategoryItemName?: string
    /**
     * 轮播图列表
     */
    allImgList?: BaseFileDTO[]
    /**
     * 设计师
     */
    designerName?: string
    /**
     * 主键ID
     */
    id?: number
    /**
     * 初判-侵权风险判定
     */
    infringementRiskPre?: string
    /**
     * 复判-侵权风险判定
     */
    infringementRiskRe?: string
    /**
     * 初判-侵权排查阶段
     */
    infringementStagePre?: string
    /**
     * 复判-侵权排查阶段
     */
    infringementStageRe?: string
    /**
     * 初判-内控复核人
     */
    innerControlUserNamePre?: string
    /**
     * 复判-内控复核人
     */
    innerControlUserNameRe?: string
    /**
     * 产品是否上市
     */
    marketFlag?: string
    /**
     * 设计师是否需要协助
     */
    needHelpFlag?: string
    /**
     * 产品描述
     */
    productDescription?: string
    /**
     * 参考品牌
     */
    referenceBrand?: string
    /**
     * 参考品牌链接
     */
    referenceBrandUrl?: string
    refProductResp?: RefProductResp
    /**
     * 初判-风险国家
     */
    riskCountryPre?: string
    /**
     * 复判-风险国家
     */
    riskCountryRe?: string
    /**
     * 风险初筛
     */
    riskScreening?: string
    /**
     * 初判-品牌维权力度
     */
    safeguardRightsStrengthPre?: string
    /**
     * 复判-品牌维权力度
     */
    safeguardRightsStrengthRe?: string
    /**
     * 上市季节
     */
    saleSeason?: string
    /**
     * 同款鞋对应品牌
     */
    sameShoesBrand?: string
    /**
     * 初步排查操作时间
     */
    screeningOperateTime?: string
    /**
     * 初判-检索日期
     */
    searchDatePre?: string
    /**
     * 复判-检索日期
     */
    searchDateRe?: string
    /**
     * 风险国家
     */
    infringementDetailPreList?: InfringementJudgmentDetailQueryResp[]
    infringementDetailReList?: InfringementJudgmentDetailQueryResp[]
    /**
     * 初判-检索详情
     */
    searchDetailPreList?: InfringementSearchQueryResp[]
    /**
     * 复判-检索详情
     */
    searchDetailReList?: InfringementSearchQueryResp[]
    /**
     * 初判-规避建议/风险情况说明
     */
    suggestionInstructionPre?: string
    /**
     * 复判-规避建议/风险情况说明
     */
    suggestionInstructionRe?: string
  }
  /**
   * RefProductResp
   */
  export interface RefProductResp {
    /**
     * 品牌
     */
    brandItemName?: string
    /**
     * 设计师
     */
    designer?: string
    designerIdItemName?: string
    /**
     * 选品会结果
     */
    meetingResult?: string
    meetingResultItemName?: string
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 操作人
     */
    modifyByIdItemName?: string
    /**
     * 产品分类
     */
    productCategoryName?: string
    productCategoryItemName?: string
    /**
     * 产品配色
     */
    productColor?: string
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 产品定位
     */
    productPositioning?: string
    productPositioningItemName?: string
    /**
     * 上市季节
     */
    launchSeasonItemName?: string
    saleSeason?: string
    /**
     * 尺码段
     */
    size?: string
    /**
     * 状态
     */
    status?: string
    dataStatusItemName?: string
    /**
     * 适用人群
     */
    targetAudience?: string
    targetAudienceItemName?: string
    thumbnailFileDTO?: BaseFileDTO
  }

  export type Request = Params
  export type Response = ResponseData<Data>
}

export namespace InfringementScreeningDetailAPI {
  export interface Params {
    id: number
  }
  export interface Data {
    /**
     * 对应品牌鞋图
     */
    brandShoesImgList?: BaseFileDTO[]
    /**
     * 设计师ID
     */
    designerId?: number
    /**
     * 设计师名称
     */
    designerName?: string
    /**
     * 设计图
     */
    designImgList?: BaseFileDTO[]
    /**
     * 图片详情
     */
    detailImgList?: BaseFileDTO[]
    errorParamList?: string[]
    /**
     * 数据ID
     */
    id?: number
    /**
     * 产品是否上市 1是 0否
     */
    marketFlag?: string
    /**
     * 设计师是否需要协助 1是 0否
     */
    needHelpFlag?: string
    /**
     * 产品描述
     */
    productDescription?: string
    /**
     * 参考品牌
     */
    referenceBrand?: string
    /**
     * 参考品牌链接
     */
    referenceBrandUrl?: string
    /**
     * 相关外观图片
     */
    refOutlookImgList?: BaseFileDTO[]
    /**
     * 参考款式图
     */
    refProductImgList?: BaseFileDTO[]
    /**
     * 风险初筛
     */
    riskScreening?: string
    /**
     * 上市季节
     */
    saleSeason?: string
    /**
     * 同款鞋对应品牌
     */
    sameShoesBrand?: string
    /**
     * 立体图
     */
    stereoscopicImgList?: BaseFileDTO[]
    /**
     * 关联产品编号
     */
    styleCode?: string
    /**
     * 暂存标识 1暂存 0正式
     */
    tempStorageFlag?: string
  }
  export type Request = Params
  export type Response = ResponseData<Data>
}
/**
 * 侵权初判详情
 */
export namespace InfringementPreDetailAPI {
  export interface Params {
    code: string
  }
  export interface Data {
    /**
     * 设计师名称
     */
    designerName?: string
    errorParamList?: string[]
    /**
     * 数据ID
     */
    id?: number
    /**
     * 侵权记录编号
     */
    infringementCode?: string
    /**
     * 侵权风险判定
     */
    infringementRisk?: string
    /**
     * 侵权排查阶段 取值:初判、复判
     */
    infringementStage?: string
    /**
     * 内控复核人ID
     */
    innerControlUserId?: number
    /**
     * 内控复核人名称
     */
    innerControlUserName?: string
    /**
     * 其他风险国家
     */
    otherRiskCountry?: string
    /**
     * 风险国家
     */
    riskCountry?: string[]
    /**
     * 品牌维权力度
     */
    safeguardRightsStrength?: string
    /**
     * 检索详情列表
     */
    searchAddReqList?: InfringementSearchAddReq[]
    /**
     * 检索日期
     */
    searchDate?: string
    /**
     * 规避建议/风险情况说明
     */
    suggestionInstruction?: string
    /**
     * 暂存标识 1暂存 0正式
     */
    tempStorageFlag?: string
  }
  export type Request = Params
  export type Response = ResponseData<Data>
}
/**
 * 修改侵权初判
 */
export namespace InfringementPreUpdateAPI {
  export interface Params {
    riskCountryRe?: string[]
    riskCountryPre?: string[]
    /**
     * 其他风险国家
     */
    otherRiskCountry?: string
    /**
     * 关联侵权排查编号
     */
    linkInfringementCode?: string
    /**
     * 设计师名称
     */
    designerName?: string
    errorParamList?: string[]
    /**
     * 数据ID
     */
    id?: number
    /**
     * 侵权记录编号
     */
    infringementCode?: string
    /**
     * 侵权风险判定
     */
    infringementRisk?: string
    /**
     * 侵权排查阶段 取值:初判、复判
     */
    infringementStage?: string
    /**
     * 内控复核人ID
     */
    innerControlUserId?: number
    /**
     * 内控复核人名称
     */
    innerControlUserName?: string
    /**
     * 风险国家
     */
    riskCountry?: string[]
    /**
     * 品牌维权力度
     */
    safeguardRightsStrength?: string
    /**
     * 检索详情列表
     */
    searchAddReqList?: InfringementSearchAddReq[]
    /**
     * 检索日期
     */
    searchDate?: string
    /**
     * 规避建议/风险情况说明
     */
    suggestionInstruction?: string
    /**
     * 暂存标识 1暂存 0正式
     */
    tempStorageFlag?: string
  }
  export type Request = Params
  export type Response = BasicResponseData
}
/**
 * 侵权复判 - 修改
 */
export namespace InfringementReUpdateAPI {
  export interface Params {
    riskCountryRe?: string[]
    riskCountryPre?: string[]
    /**
     * 其他风险国家
     */
    otherRiskCountry?: string
    /**
     * 关联侵权排查编号
     */
    linkInfringementCode?: string
    /**
     * 设计师名称
     */
    designerName?: string
    errorParamList?: string[]
    /**
     * 数据ID
     */
    id?: number
    /**
     * 侵权记录编号
     */
    infringementCode?: string
    /**
     * 侵权风险判定
     */
    infringementRisk?: string
    /**
     * 侵权排查阶段 取值:初判、复判
     */
    infringementStage?: string
    /**
     * 内控复核人ID
     */
    innerControlUserId?: number
    /**
     * 内控复核人名称
     */
    innerControlUserName?: string
    /**
     * 风险国家
     */
    riskCountry?: string[]
    /**
     * 品牌维权力度
     */
    safeguardRightsStrength?: string
    /**
     * 检索详情列表
     */
    searchAddReqList?: InfringementSearchAddReq[]
    /**
     * 检索日期
     */
    searchDate?: string
    /**
     * 规避建议/风险情况说明
     */
    suggestionInstruction?: string
    /**
     * 暂存标识 1暂存 0正式
     */
    tempStorageFlag?: string
  }
  export type Request = Params
  export type Response = BasicResponseData
}
/**
 * 侵权复判详情
 */
export namespace InfringementReDetailAPI {
  export type Request = InfringementPreDetailAPI.Request
  export type Response = InfringementPreDetailAPI.Response
}

export namespace InfringementAIApi {
  export type Response = ResponseData<Data>
  export type Request = Params
  export interface Params {
    id?: string
  }

  export interface Data {
    total?: string
    eightyPercentCount?: number
    fiftyPercentCount?: number
    underFiftyPercentCount?: number
    status?: number
    statusName?: string
    errorMessage?: string
  }
}
export namespace InfringementAIListApi {
  export type Response = PagedResponseData<Data>
  export type Request = Params & PageParams
  export interface Params {
    id?: string
  }
  export interface Data {
    businessType?: string
    businessTypeName?: string
    imageName?: string
    imageUrl?: string
    patentFile: BaseFileDTO[]
    patentId?: string
    patentNumber?: string
    title?: string
    score?: number
    locTypeName?: string
    similarity?: number
    percent?: string
    patentCountry?: string
    currentAssignee?: string
    originalAssignee?: string
    apdt?: number
    applicationDate?: string
    pbdt?: number
    publicationDate?: string
    estimatedDate?: string
    legalStatus?: string
    orderId?: string
    locType?: string
    patentUrl?: string
    allImageUrls?: string[]
  }
}
