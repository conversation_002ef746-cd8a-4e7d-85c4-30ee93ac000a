import request from '@/config/fetch'
import {
  InfringementAIListApi,
  InfringementDetailAPI,
  InfringementPageAPI,
  InfringementPreAddAPI,
  InfringementPreDetailAPI,
  InfringementPreUpdateAPI,
  InfringementReAddAPI,
  InfringementReDetailAPI,
  InfringementReUpdateAPI,
  InfringementScreeningAddAPI,
  InfringementScreeningDetailAPI,
  InfringementScreeningUpdateAPI
} from './types'
import { InfringementAIApi, InfringementVoidAPI } from './types'
import to from 'await-to-js'
import { VersionListAPI } from '@/views/basic-library-manage/api/common'
import { service } from '@/config/fetch/service'
/**
 * 侵权排查分页查询
 */
export const infringementPage = (
  data: InfringementPageAPI.Request
): Promise<InfringementPageAPI.Response> => {
  return request.post({ url: '/pdm-base/infringement/page', data })
}

/**
 * 侵权排查作废
 */
export const voidInfringement = (
  data: InfringementVoidAPI.Params
): Promise<InfringementVoidAPI.Response> => {
  return request.post({ url: '/pdm-base/infringement/discard', data })
}

/**
 * 侵权初判 - 添加
 */
export const infringementPreAdd = (
  data: InfringementPreAddAPI.Request
): Promise<InfringementPreAddAPI.Response> => {
  return request.post({ url: '/pdm-base/infringement/pre/add', data })
}
/**
 * 侵权初判 - 修改
 */
export const infringementPreUpdate = (
  data: InfringementPreUpdateAPI.Request
): Promise<InfringementPreUpdateAPI.Response> => {
  return request.post({ url: '/pdm-base/infringement/pre/update', data })
}
/**
 * 侵权复判 - 添加
 */
export const infringementReAdd = (
  data: InfringementReAddAPI.Request
): Promise<InfringementReAddAPI.Response> => {
  return request.post({ url: '/pdm-base/infringement/re/add', data })
}

/**
 * 侵权复判 - 修改
 */
export const infringementReUpdate = (
  data: InfringementReUpdateAPI.Request
): Promise<InfringementReUpdateAPI.Response> => {
  return request.post({ url: '/pdm-base/infringement/re/update', data })
}

/**
 * 侵权排查初筛
 */
export const infringementScreeningAdd = (
  data: InfringementScreeningAddAPI.Request
): Promise<InfringementScreeningAddAPI.Response> => {
  return request.post({ url: '/pdm-base/infringement/screening/add', data })
}

/**
 * 侵权排查复筛
 */
export const infringementScreeningUpdate = (
  data: InfringementScreeningUpdateAPI.Request
): Promise<InfringementScreeningUpdateAPI.Response> => {
  return request.post({ url: '/pdm-base/infringement/screening/update', data })
}
/**
 * 侵权排查详情
 */
export const infringementDetail = (
  params: InfringementDetailAPI.Request
): Promise<InfringementDetailAPI.Response> => {
  return request.get({ url: '/pdm-base/infringement/detail', params })
}

/**
 * 侵权排查版本记录
 */
export const infringementVersionDetail = (id: number): Promise<InfringementDetailAPI.Response> => {
  return request.get({ url: `/pdm-base/infringement/versionDetail/${id}` })
}

/**
 * 侵权初筛详情
 */
export const infringementScreeningDetail = (
  params: InfringementScreeningDetailAPI.Request
): Promise<InfringementScreeningDetailAPI.Response> => {
  return request.get({ url: '/pdm-base/infringement/screening/detail', params })
}

/**
 * 侵权初判详情
 */
export const infringementPreDetail = (
  params: InfringementPreDetailAPI.Request
): Promise<InfringementPreDetailAPI.Response> => {
  return request.get({ url: '/pdm-base/infringement/pre/detail', params })
}
/**
 * 侵权复判详情
 */
export const infringementReDetail = (
  params: InfringementReDetailAPI.Request
): Promise<InfringementReDetailAPI.Response> => {
  return request.get({ url: '/pdm-base/infringement/re/detail', params })
}

/**
 * 版本记录
 */
export function getInfringementVersionList(id: number) {
  return to<VersionListAPI.Response>(
    service.get(`/pdm-base/version/history?businessId=${id}&dataType=infringement_screening`)
  )
}

/*
 * 相似专利信息统计
 * */
export const getPatentDataScreen = (
  data: InfringementAIApi.Request
): Promise<InfringementAIApi.Response> => {
  return request.post({ url: '/pdm-base/patent/patentDataScreening', data })
}
/**
 * 相似专利信息分页查询
 * **/
export const getPatentPage = (
  data: InfringementAIListApi.Request
): Promise<InfringementAIListApi.Response> => {
  return request.post({ url: '/pdm-base/patent/page', data })
}
/**
 * 相似专利信息详情
 * **/
export function getPatentDetail(row: { orderId: string; patentId: string }) {
  return to<ResponseData<InfringementAIListApi.Data>>(
    service.get(`/pdm-base/patent/patentDetail/${row.orderId}/${row.patentId}`)
  )
}

/**
 * 重新查询侵权专利相关信息
 */
export const reSearchPatentInfo = (
  data: InfringementAIApi.Request
): Promise<InfringementAIApi.Response> => {
  return request.post({ url: '/pdm-base/patent/reSearchPatentInfo', data })
}
