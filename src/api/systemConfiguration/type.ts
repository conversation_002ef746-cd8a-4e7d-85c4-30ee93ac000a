import to from 'await-to-js'
import { service } from '@/config/fetch/service'
import { Attribute } from '@/components/PlmBase/type'
export namespace TypeApi {
  // 约束
  export interface PlmBaseConstraintResp {
    constraintType?: string
    constraintValue?: string
    createById?: number
    createByIdName?: string
    createTime?: string
    delFlag?: number
    id?: number
    modifyById?: number
    modifyByIdName?: string
    modifyTime?: string
    seq?: number
    [property: string]: any
  }
  //
  export interface instanceRequest {
    constraintAttributeId?: string
    constraintType?: string
    constraintAttribute?: string
  }
  export interface TypeTreeResp {
    /**
     * 父节点id
     */
    parentId?: number
    /**
     * 子类型
     */
    typeChild?: TypeTreeResp[]
    /**
     * 类型id
     */
    typeId?: number
    /**
     * 类型显示名称
     */
    typeShowName?: string
    [property: string]: any
  }
  export interface addTypeRequest {
    /**
     * 类型父节点
     */
    categoryId?: number
    /**
     * id
     */
    id?: number
    /**
     * 内部名称
     */
    name?: string
    /**
     * 说明
     */
    remark?: string
    /**
     * 显示名称
     */
    shoeName?: string
    [property: string]: any
  }
}
//根据约束类型查询约束接口
export function getConstraintByType(constraintType: string) {
  return to<NewResponseData<TypeApi.PlmBaseConstraintResp>>(
    service.get(`/pdm-base/plmConstraint/getConstraint/${constraintType}`)
  )
}

export function getConstraint(data: TypeApi.instanceRequest) {
  return to<NewResponseData<Attribute>>(
    service.post(`/pdm-base/plmConstraint/getConstraintInstance`, data)
  )
}
export function saveConstraint(data: Attribute) {
  return to<NewResponseData<Attribute>>(
    service.post(`/pdm-base/plmConstraint/saveConstraintInstance`, data)
  )
}
export function updateConstraintInstance(data: {
  relationId?: string
  constraintAttribute?: string
  constrain: Attribute
}) {
  return to<NewResponseData<Attribute>>(
    service.post(`/pdm-base/propertyRelation/saveOrUpdateConstrain`, data)
  )
}
export function getRelationDetail(id: string) {
  return to<NewResponseData<Attribute[]>>(
    service.post(`/pdm-base/propertyRelation/relationDetail/${id}`)
  )
}
export function deletePropertyRelation(data: { relationId: string; businessId: string }) {
  return to<NewResponseData<Attribute[]>>(service.post(`/pdm-base/propertyRelation/delete`, data))
}
export function saveProperty(data) {
  return to<NewResponseData<Attribute[]>>(
    service.post(`/pdm-base/propertyRelation/saveProperty`, data)
  )
}
export function typeTree() {
  return to<NewResponseData<TypeApi.TypeTreeResp>>(service.get(`/pdm-base//plm/type/tree`))
}
export function saveOrUpdateType(data: TypeApi.addTypeRequest) {
  return to<NewResponseData<TypeApi.TypeTreeResp>>(
    service.post(`/pdm-base/plm/type/saveOrUpdate`, data)
  )
}
///plm/type/delete/{id}
export function deleteType(id: string) {
  return to<NewResponseData<TypeApi.TypeTreeResp>>(service.post(`/pdm-base/plm/type/delete/${id}`))
}
export function deleteTypeRelation(data: { relationId: string; businessId: string }) {
  return to<NewResponseData<TypeApi.TypeTreeResp>>(
    service.post(`/pdm-base/plm/type/deleteProperty`, data)
  )
}
export function getTypeRelationDetail(id: string) {
  return to<NewResponseData<Attribute[]>>(service.post(`/pdm-base/plm/type/relationDetail/${id}`))
}
///plm/type/saveProperty
export function saveTypeProperty(data) {
  return to<NewResponseData<Attribute[]>>(service.post(`/pdm-base/plm/type/saveProperty`, data))
}
export namespace LayoutProperty {
  // 布局属性
  export interface PlmBasePageResp {
    createByIdName?: string
    createTime?: string
    id?: number
    modifyByIdName?: string
    modifyTime?: string
    pageAttributes?: string
    pageLayoutCode?: number
    pageLayoutName?: string
    pageTypeCategoryId?: number
    pageTypeCategoryName?: string
    seq?: number
    [property: string]: any
  }
}
//布局上的属性
export function getPageAttributeList(params: { typeCategoryId: string }) {
  return to<NewResponseData<Attribute[]>>(
    service.get(`/pdm-base/plmPage/getPageAttributeList`, { params })
  )
}
export function getPage(data: { pageLayoutCode: string; pageTypeCategoryId: string }) {
  return to<NewResponseData<LayoutProperty.PlmBasePageResp>>(
    service.post(`/pdm-base/plmPage/getPage`, data)
  )
}
export function updatePageAttribute(data: Attribute) {
  return to<NewBasicResponseData>(service.post(`/pdm-base/plmPage/updatePage`, data))
}

export function getPageCategoryList(params: { typeCategoryId: string }) {
  return to<NewResponseData<Attribute[]>>(
    service.get(`/pdm-base/plmPage/getPageCategoryList`, { params })
  )
}
export function savePageAttribute(data: Attribute) {
  return to<NewResponseData<Attribute[]>>(service.post(`/pdm-base/plmPage/savePage`, data))
}
export function getAllConstraints() {
  return to<NewResponseData<Attribute[]>>(service.get(`/pdm-base/plmConstraint/getAllConstraint`))
}
export function updateConstraint(data: Attribute) {
  return to<NewBasicResponseData>(service.post(`/pdm-base/plmConstraint/updateConstraint`, data))
}
