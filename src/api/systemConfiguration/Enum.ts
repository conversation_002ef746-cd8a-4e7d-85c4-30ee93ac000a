import to from 'await-to-js'
import { service } from '@/config/fetch/service'
export namespace EnumApi {
  export interface Row {
    [property: string]: any
    createById?: number
    createByIdName?: string
    createTime?: string
    delFlag?: number
    enumeratedClassificationId?: string
    enumeratedCode?: string
    enumeratedDataType?: string
    enumeratedDesc?: string
    enumeratedValue?: string
    id?: number
    modifyById?: number
    modifyByIdName?: string
    modifyTime?: string
    status?: string
    statusCode?: string
  }
  export interface Params {
    createById?: number
    createByIdName?: string
    createTime?: string
    delFlag?: number
    enumeratedClassificationId: string
    enumeratedCode?: string
    enumeratedDataType?: string
    enumeratedDesc?: string
    enumeratedValue?: string
    id?: number
    modifyById?: number
    modifyByIdName?: string
    modifyTime?: string
    status?: string
    statusCode?: string
    [property: string]: any
  }
  // 高级查询条件
  export interface AdvancedCondition {
    /**
     * 字段名
     */
    field?: string
    /**
     * eq，like等
     * 操作符
     */
    operator?: string
    /**
     * 查询值
     */
    value?: string
    [property: string]: any
  }
  //分页查询内容
  export interface PageSearch {
    /**
     * 高级查询条件
     */
    conditionList?: AdvancedCondition[]
    /**
     * 枚举分类id
     */
    enumeratedClassificationId: string
    /**
     * 枚举名称
     */
    enumeratedDesc?: string
    /**
     * 枚举内部值
     */
    enumeratedValue?: string
    [property: string]: any
  }
  export type pageResponse = NewPageResponseData<Row>
  export type pageRequest = PageSearch & PageParams
  export interface deleteRequest {
    id: string
  }
}
export namespace EnumTypeApi {
  // 操作参数
  export interface Row {
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: string
    delFlag?: number
    /**
     * 枚举分类CODE
     */
    enumeratedClassificationCode?: string
    /**
     * 枚举分类描述
     */
    enumeratedClassificationDesc?: string
    id?: string
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: string
    parentId?: string
    [property: string]: any
  }
  // 分页查询
  export interface deleteRequest {
    id: string
  }
}
// 定义添加数据项的接口
export interface AddSourceDataItem {
  id?: number | string
  code?: string
  name?: string
  label?: string
  categoryCode?: string
  categoryCnName?: string
  parentId?: number | string
  parent_id?: number | string
  pid?: number | string
  childList?: AddSourceDataItem[]
  [key: string]: any
}

/**-------------------------------------枚举值分类----------------------------------*/
//查询枚举值分类接口
export function listClassification() {
  return to<NewResponseData<EnumTypeApi.Row>>(
    service.get('/pdm-base/pdmEnumeratedClassification/listClassification')
  )
}
//保存枚举值分类
export function saveClassification(data: EnumTypeApi.Row) {
  return to<NewBasicResponseData>(
    service.post('/pdm-base/pdmEnumeratedClassification/saveClassification', data)
  )
}
//修改枚举值分类接口
export function updateClassification(data: EnumTypeApi.Row) {
  return to<NewBasicResponseData>(
    service.post('/pdm-base/pdmEnumeratedClassification/updateClassification', data)
  )
}
//删除枚举值分类接口
export function deleteClassification(data: EnumTypeApi.deleteRequest) {
  return to<NewBasicResponseData>(
    service.post('/pdm-base/pdmEnumeratedClassification/deleteClassification', data)
  )
}

/**-------------------------------------枚举值----------------------------------*/
//查询枚举值接口
export function pageClassificationItem(data: EnumApi.pageRequest) {
  return to<EnumApi.pageResponse>(
    service.post('/pdm-base/pdmEnumeratedClassification/pageClassificationItem', data)
  )
}
//保存枚举值
export function saveClassificationItem(data: EnumApi.Row) {
  return to<NewBasicResponseData>(
    service.post('/pdm-base/pdmEnumeratedClassification/saveClassificationItem', data)
  )
}
//修改枚举值接口
export function updateClassificationItem(data: EnumApi.Row) {
  return to<NewBasicResponseData>(
    service.post('/pdm-base/pdmEnumeratedClassification/updateClassificationItem', data)
  )
}
//删除枚举值接口
export function deleteClassificationItem(data: EnumApi.deleteRequest) {
  return to<NewBasicResponseData>(
    service.post('/pdm-base/pdmEnumeratedClassification/deleteClassificationItem', data)
  )
}
//搜索枚举值接口
export function queryItemByClassificationName(params: { source: string }) {
  return to<NewResponseData<EnumApi.Row[]>>(
    service.get(
      `/pdm-base/pdmEnumeratedClassification/queryItemByClassificationCode/${params.source}`
    )
  )
}
