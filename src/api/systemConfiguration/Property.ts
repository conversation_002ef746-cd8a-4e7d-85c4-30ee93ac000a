import to from 'await-to-js'
import { service } from '@/config/fetch/service'
export namespace PropertyApi {
  export interface Row {
    /**
     * 分类id
     */
    categoryId?: string
    /**
     * 分类名称
     */
    categoryName?: string
    /**
     * 创建人
     */
    createById?: string
    /**
     * 创建人
     */
    createByName?: string
    /**
     * 创建时间
     */
    createTime?: string
    /**
     * 属性描述
     */
    description?: string
    id?: number
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作人
     */
    modifyByName?: string
    /**
     * 操作时间
     */
    modifyTime?: string
    /**
     * 属性名称
     */
    name?: string
    /**
     * 状态code
     */
    status?: string
    /**
     * 状态值
     */
    statusItemName?: string
    /**
     * 数据类型
     */
    type?: string
    /**
     * 属性内部值
     */
    value?: string
    [property: string]: any
  }
  export interface categoryRow {
    selectorKey: string
    selectorValue: number
    id: string
    label: string
    [property: string]: any
  }
  export interface Params {
    /**
     * 分类目录id
     */
    categoryId?: string
    /**
     * 属性名称
     */
    name?: string
    /**
     * 属性内部值
     */
    value?: string
    description?: string
    /**
     * 状态
     */
    status?: string
    [property: string]: any
  }
  export type pageResponse = NewPageResponseData<Row>
  export type pageRequest = Params & PageParams
  export interface deleteRequest {
    id: string
  }
}
//分页查询属性
export function propertyPage(data: PropertyApi.pageRequest) {
  return to<NewResponseData<PropertyApi.Row>>(service.post('/pdm-base/plm/property/page', data))
}
//不分页属性
export function propertyList(data: PropertyApi.Params) {
  return to<NewResponseData<PropertyApi.Row[]>>(service.post('/pdm-base/plm/property/list', data))
}
//删除属性分类目录
export function deleteProperty(id: string) {
  return to<NewBasicResponseData>(service.post(`/pdm-base/plm/property/delete/${id}`))
}
//删除属性分类目录
export function deleteCategory(id: string) {
  return to<NewBasicResponseData>(service.post(`/pdm-base/plm/property/category/${id}`))
}
//属性分类下拉框数据
export function categoryData() {
  return to<NewResponseData<PropertyApi.categoryRow[]>>(
    service.post(`/pdm-base/plm/property/categoryData`)
  )
}
//修改保存属性分类
export function saveOrUpdateProperty(data: PropertyApi.Row) {
  return to<NewResponseData<PropertyApi.Row>>(
    service.post(`/pdm-base/plm/property/saveOrUpdate`, data)
  )
}
//修改保存
export function saveOrUpdateCategory(data: PropertyApi.Row) {
  return to<NewResponseData<PropertyApi.categoryRow>>(
    service.post(`/pdm-base/plm/property/category`, data)
  )
}
