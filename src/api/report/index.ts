import request from '@/config/fetch'
import {
  Request,
  MonthResponse,
  CategoryResponse,
  VendorResponse,
  VendorCategoryResponse,
  BrandResponse,
  VendorAgingResponse,
  VendorSelectedResponse,
  UnSelectedResponse,
  PushStyleStatisticalFormFilterDTO,
  ResultPageBeanPushStyleStatisticalFormAgingItemDTO,
  Result
} from './types'
// 月份查询
export const getMonthListApi = (data: Request): Promise<MonthResponse> => {
  return request.post({
    url: '/pdm/reportForm/month',
    data
  })
}
// 产品大类
export const getCategoryListApi = (data: Request): Promise<CategoryResponse> => {
  return request.post({
    url: '/pdm/reportForm/category',
    data
  })
}
// 供应商统计
export const getVendorListApi = (data: Request): Promise<VendorResponse> => {
  return request.post({
    url: '/pdm/reportForm/vendor',
    data
  })
}
// 供应商统计产品大类
export const getVendorCategoryListApi = (data: Request): Promise<VendorCategoryResponse> => {
  return request.post({
    url: '/pdm/reportForm/vendorCategory',
    data
  })
}
// 归属品牌统计
export const getBrandListApi = (data: Request): Promise<BrandResponse> => {
  return request.post({
    url: '/pdm/reportForm/brand',
    data
  })
}
// 推款流程平均时效
export const getVendorAgingListApi = (data: Request): Promise<VendorAgingResponse> => {
  return request.post({
    url: '/pdm/reportForm/vendorAging',
    data
  })
}
// 供应商推款选中数据
export const getVendorSelectedListApi = (data: Request): Promise<VendorSelectedResponse> => {
  return request.post({
    url: '/pdm/reportForm/vendorSelected',
    data
  })
}
// 供应商推款未选中原因
export const getUnSelectedListApi = (data: Request): Promise<UnSelectedResponse> => {
  return request.post({
    url: '/pdm/reportForm/unSelected',
    data
  })
}
export const getUnSelectedStateListApi = (data: Request): Promise<UnSelectedResponse> => {
  return request.post({
    url: '/pdm/reportForm/unSelectedState',
    data
  })
}

// 推款产品时效数据
export const getAgingApi = (
  data: PushStyleStatisticalFormFilterDTO
): Promise<ResultPageBeanPushStyleStatisticalFormAgingItemDTO> => {
  return request.post({
    url: '/pdm/reportForm/pageAging',
    data
  })
}
// 推款产品导出
export const exportAging = (data: PushStyleStatisticalFormFilterDTO): Promise<Result> => {
  return request.post({
    url: '/pdm/reportForm/export',
    data
  })
}
