export interface Request {
  createTimeEnd?: string
  createTimeStart?: string
  vendorAreaCode?: string
  vendorName?: string
  [property: string]: any
}

export interface MonthResponse {
  /**
   * 月份
   */
  month?: Date
  /**
   * 推款数量
   */
  total?: number
  [property: string]: any
}

export interface CategoryResponse {
  /**
   * 产品大类名字
   */
  productZhCategoryDict?: string
  /**
   * 推款数量
   */
  total?: number
  [property: string]: any
}

export interface VendorResponse {
  /**
   * 推款数量
   */
  total: number
  vendorName: string
  [property: string]: any
}

export interface VendorCategoryResponse {
  catogorys?: Catogory[]
  /**
   * 供应商名称
   */
  vendor_name?: string
  [property: string]: any
}

export interface Catogory {
  /**
   * 种类名称
   */
  productZhCategoryDict?: string
  /**
   * 数量
   */
  total?: number
  [property: string]: any
}

export interface BrandResponse {
  /**
   * 品牌名称
   */
  brandName?: string
  total?: number
  [property: string]: any
}

/**
 * VendorAgingResponse
 */
export interface VendorAgingResponse {
  /**
   * 返回标记：成功标记=0，其余都是失败
   */
  code?: string
  /**
   * 数据
   */
  datas?: PushStyleStatisticalFormVendorAgingDTO[]
  /**
   * 是否成功
   */
  isSuccess?: boolean
  /**
   * 返回信息
   */
  msg?: string
  [property: string]: any
}

/**
 * PushStyleStatisticalFormVendorAgingDTO
 */
export interface PushStyleStatisticalFormVendorAgingDTO {
  /**
   * 待接收样品（天）
   */
  acceptTime?: number
  /**
   * 待寄送样品（天）
   */
  deliveryTime?: number
  /**
   * 初选中（天）
   */
  primaryTime?: number
  /**
   * 待补充信息（天）
   */
  replenishTime?: number
  /**
   * 评审中（天）
   */
  reviewTime?: number
  /**
   * 供应商名称
   */
  vendorName?: string
  [property: string]: any
}

export interface VendorSelectedResponse {
  selectedRate: string
  selectedTotal?: number
  unSelectedTotal?: number
  vendorName?: string
  [property: string]: any
}

export interface UnSelectedResponse {
  total?: number
  unSelectReasonDict?: string
  [property: string]: any
}

/**
 * PushStyleStatisticalFormFilterDTO
 */
export interface PushStyleStatisticalFormFilterDTO {
  /**
   * 正排序字段
   */
  ascOrderBy?: string
  /**
   * 是否查询总数
   */
  count?: boolean
  /**
   * 推款时间-结束
   */
  createTimeEnd?: Date
  /**
   * 推款时间-开始
   */
  createTimeStart?: Date
  /**
   * 页码
   */
  current?: number
  /**
   * 倒排序字段
   */
  descOrderBy?: string
  /**
   * 产品名称
   */
  productName?: string[]
  /**
   * 产品大类名称：PRODUCT_ZH_CATEGORY
   */
  productZhCategoryDict?: string[]
  /**
   * 上市季节:DEVELOP_SEASON
   */
  publishSeasonDict?: string[]
  /**
   * 页面尺寸
   */
  size?: number
  /**
   * 多个字段排序配置。
   */
  sortList?: PageSort[]
  /**
   * 状态：PUSH_STYLE_STATE
   */
  stateDict?: string[]
  /**
   * 供应商区域code
   */
  vendorAreaCode?: string
  /**
   * 供应商名称
   */
  vendorName?: string[]
  [property: string]: any
}

/**
 * PageSort
 */
export interface PageSort {
  /**
   * 是否正排序？true是，false否
   */
  ascField?: boolean
  /**
   * 排序的字段
   */
  field?: string
  [property: string]: any
}

/**
 * Result«PageBean«PushStyleStatisticalFormAgingItemDTO»»
 */
export interface ResultPageBeanPushStyleStatisticalFormAgingItemDTO {
  /**
   * 返回标记：成功标记=0，其余都是失败
   */
  code?: string
  datas?: PageBeanPushStyleStatisticalFormAgingItemDTO
  /**
   * 是否成功
   */
  isSuccess?: boolean
  /**
   * 返回信息
   */
  msg?: string
  [property: string]: any
}

/**
 * PageBean«PushStyleStatisticalFormAgingItemDTO»
 */
export interface PageBeanPushStyleStatisticalFormAgingItemDTO {
  empty?: boolean
  pager?: Pager
  records?: PushStyleStatisticalFormAgingItemDTO[]
  [property: string]: any
}

/**
 * Pager
 */
export interface Pager {
  current?: number
  pages?: number
  size?: number
  total?: number
  [property: string]: any
}

/**
 * PushStyleStatisticalFormAgingItemDTO
 */
export interface PushStyleStatisticalFormAgingItemDTO {
  /**
   * 待接收样品（天）
   */
  acceptTime?: number
  /**
   * 品牌名称
   */
  brandName?: string
  /**
   * 待寄送样品（天）
   */
  deliveryTime?: number
  /**
   * 初选中（天）
   */
  primaryTime?: number
  /**
   * 产品中心审批平均时效（天）
   */
  productCenterHandleTime?: number
  /**
   * 产品名称
   */
  productName?: string
  /**
   * 产品大类名称：PRODUCT_ZH_CATEGORY
   */
  productZhCategoryDict?: string
  /**
   * 上市季节:DEVELOP_SEASON
   */
  publishSeasonDict?: string
  /**
   * 推款时间
   */
  pushTime?: Date
  /**
   * 待补充信息（天）
   */
  replenishTime?: number
  /**
   * 评审中（天）
   */
  reviewTime?: number
  /**
   * 状态：PUSH_STYLE_STATE
   */
  stateDict?: string
  /**
   * 总用时
   */
  totalTime?: number
  /**
   * 供应商区域名称
   */
  vendorAreaName?: string
  /**
   * 供应商处理平均时效（天）
   */
  vendorHandleTime?: number
  /**
   * 供应商名称
   */
  vendorName?: string
  [property: string]: any
}
/**
 * Result
 */
export interface Result {
  /**
   * 返回标记：成功标记=0，其余都是失败
   */
  code?: string
  /**
   * 数据
   */
  datas?: { [key: string]: any }
  /**
   * 是否成功
   */
  isSuccess?: boolean
  /**
   * 返回信息
   */
  msg?: string
  [property: string]: any
}
