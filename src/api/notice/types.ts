export interface PushStyleAggregateStateDTO {
  /**
   * 6,已选中
   */
  hasSelected?: number
  /**
   * 4,评审中
   */
  judging?: number
  /**
   * 1,初选中
   */
  primarySelect?: number
  /**
   * 7,未选中
   */
  unSelected?: number
  /**
   * 3,待接收样品
   */
  waitAccept?: number
  /**
   * 5,待补充信息
   */
  waitInformation?: number
  /**
   * 2,待寄送样品
   */
  waitSend?: number
  [property: string]: any
}
/**
 * PushStyleAggregateReturnDTO
 */
export interface ReturnRequest {
  /**
   * 推款id
   */
  id?: number
  /**
   * 退回原因
   */
  returnReasonDict?: string
  /**
   * 退回说明
   */
  returnReasonRemark?: string
  [property: string]: any
}

export interface ResumeRequest {
  list?: Array<ResumeItemDTO>
}

/**
 * AcceptItemDTO
 */
export interface AcceptItemDTO {
  /**
   * 样品接收时间
   */
  acceptTime?: Date
  /**
   * 推款id
   */
  id?: number
  /**
   * 样品数量
   */
  sampleCount?: number
  [property: string]: any
}

/**
 * ResumeItemDTO
 */
export interface ResumeItemDTO {
  /**
   * 推款id
   */
  id?: number
  /**
   * 评审中状态
   */
  resumeJudge?: boolean
  /**
   * 初选中状态
   */
  resumePrimary?: boolean
  [property: string]: any
}

/**
 * PushStyleAggregateInsertDTO
 */
export interface PushAddRequest {
  /**
   * 样品接收时间
   */
  acceptTime?: Date
  attachment?: PushStyleAttachmentInsertDTO
  /**
   * 大底开模周期
   */
  bottomCycle?: number
  /**
   * 品牌id
   */
  brandId?: number
  /**
   * 品牌名称
   */
  brandName?: string
  /**
   * 编号
   */
  code?: string
  /**
   * 公司名称
   */
  companyName?: string
  /**
   * 联系地址
   */
  contactAddress?: string
  /**
   * 邮箱
   */
  contactEmail?: string
  /**
   * 联系人
   */
  contactName?: string
  /**
   * 联系电话
   */
  contactPhone?: string
  /**
   * 是否有底楦：YES_NO
   */
  containBottomDict?: string
  /**
   * 是否有底楦：YES_NO
   */
  containBottomDict_zh?: string
  /**
   * 现有配色
   */
  containColor?: string
  /**
   * 包含半码：YES_NO
   */
  containHalfDict?: string
  /**
   * 包含半码：YES_NO
   */
  containHalfDict_zh?: string
  /**
   * 包含税：YES_NO
   */
  containTaxDict?: string
  /**
   * 包含税：YES_NO
   */
  containTaxDict_zh?: string
  /**
   * 成本-闭区间
   */
  costEnd?: number
  /**
   * 成本-开区间
   */
  costStart?: number
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 数据状态（0：正常, 1：删除）
   */
  delFlag?: number
  /**
   * 已售卖品牌
   */
  hasSellBrand?: string
  /**
   * 已售卖国家：TODO
   */
  hasSellCountryDict?: string
  /**
   * 已售卖国家：TODO
   */
  hasSellCountryDict_zh?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 生命周期
   */
  lifeCycle?: number
  /**
   * 主要材质
   */
  mainMaterial?: string
  /**
   * 子表-推款产品清单-型体关联表
   */
  model?: string[]
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * MOQ
   */
  moq?: number
  /**
   * 模具是否量产：YES_NO
   */
  mouldHasProductDict?: string
  /**
   * 模具是否量产：YES_NO
   */
  mouldHasProductDict_zh?: string
  /**
   * 模具类型：MOULD_TYPE
   */
  mouldTypeDict?: string
  /**
   * 模具类型：MOULD_TYPE
   */
  mouldTypeDict_zh?: string
  /**
   * 产品分类
   */
  productClassify?: string
  /**
   * 产品名称
   */
  productName?: string
  /**
   * 产品大类：PRODUCT_ZH_CATEGORY
   */
  productZhCategoryDict?: string
  /**
   * 产品大类：PRODUCT_ZH_CATEGORY
   */
  productZhCategoryDict_zh?: string
  /**
   * 公告id
   */
  publicNoticeId?: number
  /**
   * 样品寄送地址id
   */
  pushStyleAddressId?: number
  remark?: PushStyleRemarkInsertDTO
  /**
   * 退回原因
   */
  returnReasonDict?: string
  /**
   * 样品数量
   */
  sampleCount?: number
  /**
   * 楦型：SHOES_TREE
   */
  shoesTreeDict?: string
  /**
   * 楦型：SHOES_TREE
   */
  shoesTreeDict_zh?: string
  /**
   * 尺码-闭
   */
  sizeEnd?: number
  /**
   * 尺码-开
   */
  sizeStart?: number
  /**
   * 推款信息来源：SOURCE_FROM
   */
  sourceFromDict?: string
  /**
   * 推款信息来源：SOURCE_FROM
   */
  sourceFromDict_zh?: string
  /**
   * 状态：PUSH_STYLE_STATE
   */
  stateDict?: string
  /**
   * 状态：PUSH_STYLE_STATE
   */
  stateDict_zh?: string
  /**
   * 子表-推款产品清单-风格关联表
   */
  styleBody?: string[]
  /**
   * 未选中原因：UN_SELECT_REASON
   */
  unSelectReasonDict?: string
  /**
   * 未选中原因：UN_SELECT_REASON
   */
  unSelectReasonDict_zh?: string
  /**
   * 供应商区域code
   */
  vendorAreaCode?: string
  /**
   * 供应商区域名称
   */
  vendorAreaName?: string
  /**
   * 供应商id
   */
  vendorId?: number
  /**
   * 供应商名称
   */
  vendorName?: string
  [property: string]: any
}

/**
 * PushStyleAttachmentInsertDTO
 */
export interface PushStyleAttachmentInsertDTO {
  /**
   * 大底图
   */
  bottomPic?: string[]
  /**
   * 配色图
   */
  colorPics?: string[]
  /**
   * 正面图
   */
  frontPic?: string[]
  /**
   * 款式主图
   */
  mainPic?: string[]
  /**
   * 其他图片
   */
  otherPics?: string[]
  /**
   * 参考文件
   */
  referFiles?: string[]
  /**
   * 侧面图
   */
  sidePics?: string[]
  [property: string]: any
}

/**
 * PushStyleRemarkInsertDTO
 */
export interface PushStyleRemarkInsertDTO {
  /**
   * 产品买点
   */
  productPoint?: string
  /**
   * 推款说明
   */
  pushRemark?: string
  /**
   * 推款主键id
   */
  pushStyleId?: number
  /**
   * 特殊功能说明
   */
  specialRemark?: string
  [property: string]: any
}
export interface PublicNoticeProductTypeDTO {
  /**
   * 一级id
   */
  categoryCode?: string
  /**
   * 一级name
   */
  categoryName?: string
  /**
   * id集合
   */
  codeArray?: string
  /**
   * 创建人userId
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 数据状态（0：正常，1：删除）
   */
  delFlag?: boolean
  /**
   * 主键
   */
  id?: number
  /**
   * 修改人userId
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 名称集合
   */
  nameArray?: string
  /**
   * 公告id
   */
  publicNoticeId?: number
  [property: string]: any
}
export interface ProductTypeDTO {
  /**
   * 名称集合
   */
  productTypeNameArray?: string[]
  /**
   * 公告id
   */
  productTypeLastId?: number
  productTypeIdArray?: number[]

  [property: string]: any
}
/**
 * PublicNoticeCloseDTO
 */
export interface PublicNoticeCloseDTO {
  /**
   * 关闭原因
   */
  closeReasonDict?: string
  /**
   * 主键
   */
  ids?: number[]
  [property: string]: any
}
/**
 * PublicNoticeAggregateGrantDTO
 */
export interface PublicNoticeGrantRequest {
  /**
   * 区域
   */
  area?: string[]
  /**
   * 主键
   */
  id?: number
  /**
   * 对接供管
   */
  noticeManager?: PublicNoticeManagerInsertDTO[]
  /**
   * 供应商
   */
  vendor?: PublicNoticeVendorInsertDTO[]
  [property: string]: any
}

/**
 * PublicNoticeManagerInsertDTO
 */
export interface PublicNoticeManagerInsertDTO {
  /**
   * 用户id
   */
  userId?: number
  /**
   * 用户名称
   */
  userName?: string
  [property: string]: any
}

/**
 * PublicNoticeVendorInsertDTO
 */
export interface PublicNoticeVendorInsertDTO {
  /**
   * 供应商id
   */
  vendorId?: number
  /**
   * 供应商名称
   */
  vendorName?: string
  [property: string]: any
}
/**
 * PublicNoticeFilterDTO
 */
export interface NoticePageRequest {
  /**
   * 年龄段-闭
   */
  ageEnd?: number
  /**
   * 年龄段-开
   */
  ageStart?: number
  /**
   * 正排序字段
   */
  ascOrderBy?: string
  /**
   * 品牌id
   */
  brandId?: number
  /**
   * 品牌名称
   */
  brandName?: string
  /**
   * 关闭原因:SEEKING_CLOSE_REASON
   */
  closeReasonDict?: string
  /**
   * 公告编号
   */
  code?: string
  /**
   * 颜色要求
   */
  colorRequire?: string
  /**
   * 成本-闭区间
   */
  costEnd?: number
  /**
   * 成本-开区间
   */
  costStart?: number
  /**
   * 是否查询总数
   */
  count?: boolean
  /**
   * 创建人userId
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 页码
   */
  current?: number
  /**
   * 数据状态（0：正常，1：删除）
   */
  delFlag?: boolean
  /**
   * 倒排序字段
   */
  descOrderBy?: string
  /**
   * 对接供管
   */
  dockManagerJson?: string
  /**
   * 截止时间
   */
  endTime?: Date
  /**
   * 首单-闭区间
   */
  firstPairEnd?: number
  /**
   * 首单-开区间
   */
  firstPairStart?: number
  /**
   * 跟进说明
   */
  followRemark?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 主要材质
   */
  mainMaterial?: string
  /**
   * 主图
   */
  mainPic?: string
  /**
   * 修改人userId
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 公告分类：SEEKING_CATEGORY
   */
  noticeCategoryDict?: string
  /**
   * 公告说明
   */
  noticeDescription?: string
  /**
   * 公告主题
   */
  noticeTopic?: string
  /**
   * 流行元素
   */
  popularElement?: string
  /**
   * 产品类别
   */
  productTypeJson?: string
  /**
   * 推款数量-闭
   */
  pushNumberEnd?: number
  /**
   * 推款数量-开
   */
  pushNumberStart?: number
  /**
   * 参考文件
   */
  referFiles?: string
  /**
   * 参考图片
   */
  referPics?: string
  /**
   * 公告发布日
   */
  releaseTime?: Date
  /**
   * 场景
   */
  scene?: string
  /**
   * 页面尺寸
   */
  size?: number
  /**
   * 尺码-闭
   */
  sizeEnd?: number
  /**
   * 尺码-开
   */
  sizeStart?: number
  /**
   * 多个字段排序配置。
   */
  sortList?: PageSort[]
  /**
   * 状态：NOTICE_STATE
   */
  stateDict?: string
  /**
   * 定向供应商
   */
  vendorJson?: string
}

/**
 * PageSort
 */
export interface PageSort {
  /**
   * 是否正排序？true是，false否
   */
  ascField?: boolean
  /**
   * 排序的字段
   */
  field?: string
}

/**
 * Result«PageBean«PublicNoticeAggregateDTO»»
 */
export interface PublicNoticeAggregateDTO {
  /**
   * 返回标记：成功标记=0，其余都是失败
   */
  code?: string
  datas?: PageBeanPublicNoticeAggregateDTO
  /**
   * 是否成功
   */
  isSuccess?: boolean
  /**
   * 返回信息
   */
  msg?: string
  [property: string]: any
}

/**
 * PageBean«PublicNoticeAggregateDTO»
 */
export interface PageBeanPublicNoticeAggregateDTO {
  empty?: boolean
  pager?: pager
  records?: RecordElement[]
  [property: string]: any
}

/**
 * PublicNoticeAggregateDTO
 */
export interface RecordElement {
  /**
   * 年龄段-闭
   */
  ageEnd?: number
  /**
   * 年龄段-开
   */
  ageStart?: number
  /**
   * 区域
   */
  area?: string[]
  /**
   * 区域
   */
  area_zh?: string[]
  /**
   * 品牌id
   */
  brandId?: number
  /**
   * 品牌名称
   */
  brandName?: string
  /**
   * 关闭原因:SEEKING_CLOSE_REASON
   */
  closeReasonDict?: string
  /**
   * 关闭原因:SEEKING_CLOSE_REASON
   */
  closeReasonDict_zh?: string
  /**
   * 公告编号
   */
  code?: string
  /**
   * 颜色要求
   */
  colorRequire?: string
  /**
   * 成本-闭区间
   */
  costEnd?: number
  /**
   * 成本-开区间
   */
  costStart?: number
  /**
   * 创建人userId
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 开发季节
   */
  developSeason?: string[]
  /**
   * 开发季节
   */
  developSeason_zh?: string[]
  /**
   * 截止时间
   */
  endTime?: Date
  /**
   * 首单-闭区间
   */
  firstPairEnd?: number
  /**
   * 首单-开区间
   */
  firstPairStart?: number
  /**
   * 跟进说明
   */
  followRemark?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 主要材质
   */
  mainMaterial?: string
  mainPic?: number[]
  mainPic_url?: OssUploadRecordDTO[]
  /**
   * 型体
   */
  model?: string[]
  /**
   * 型体
   */
  model_zh?: string[]
  /**
   * 修改人userId
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 公告分类：SEEKING_CATEGORY
   */
  noticeCategoryDict?: string
  /**
   * 公告分类：SEEKING_CATEGORY
   */
  noticeCategoryDict_zh?: string
  /**
   * 公告说明
   */
  noticeDescription?: string
  /**
   * 公告主题
   */
  noticeTopic?: string
  /**
   * 流行元素
   */
  popularElement?: string
  /**
   * 定位
   */
  position?: string[]
  /**
   * 定位
   */
  position_zh?: string[]
  pushNumber?: number
  /**
   * 推款数量-闭
   */
  pushNumberEnd?: number
  /**
   * 推款数量-开
   */
  pushNumberStart?: number
  referFiles?: number[]
  referFiles_url?: OssUploadRecordDTO[]
  referPics?: number[]
  referPics_url?: OssUploadRecordDTO[]
  /**
   * 公告发布日
   */
  releaseTime?: Date
  /**
   * 场景
   */
  scene?: string
  /**
   * 公告阶段(寻源阶段）
   */
  seekingStage?: string[]
  /**
   * 公告阶段(寻源阶段）
   */
  seekingStage_zh?: string[]
  /**
   * 楦型
   */
  shoesTree?: string[]
  /**
   * 楦型
   */
  shoesTree_zh?: string[]
  /**
   * 尺码-闭
   */
  sizeEnd?: number
  /**
   * 尺码-开
   */
  sizeStart?: number
  /**
   * 状态：NOTICE_STATE
   */
  stateDict?: string
  /**
   * 状态：NOTICE_STATE
   */
  stateDict_zh?: string
  /**
   * 风格
   */
  styleBody?: string[]
  /**
   * 风格
   */
  styleBody_zh?: string[]
  [property: string]: any
}

export interface PushAggregateRequest {
  /**
   * 样品接收时间
   */
  acceptTime?: Date
  /**
   * 正排序字段
   */
  ascOrderBy?: string
  /**
   * 大底开模周期
   */
  bottomCycle?: number
  /**
   * 品牌id
   */
  brandId?: number
  /**
   * 品牌名称
   */
  brandName?: string
  /**
   * 编号
   */
  code?: string
  /**
   * 公司名称
   */
  companyName?: string
  /**
   * 联系地址
   */
  contactAddress?: string
  /**
   * 邮箱
   */
  contactEmail?: string
  /**
   * 联系人
   */
  contactName?: string
  /**
   * 联系电话
   */
  contactPhone?: string
  /**
   * 是否有底楦：YES_NO
   */
  containBottomDict?: string
  /**
   * 现有配色
   */
  containColor?: string
  /**
   * 包含半码：YES_NO
   */
  containHalfDict?: string
  /**
   * 包含税：YES_NO
   */
  containTaxDict?: string
  /**
   * 成本-闭区间
   */
  costEnd?: number
  /**
   * 成本-开区间
   */
  costStart?: number
  /**
   * 是否查询总数
   */
  count?: boolean
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 页码
   */
  current?: number
  /**
   * 数据状态（0：正常, 1：删除）
   */
  delFlag?: number
  /**
   * 倒排序字段
   */
  descOrderBy?: string
  /**
   * 已售卖品牌
   */
  hasSellBrand?: string
  /**
   * 已售卖国家：TODO
   */
  hasSellCountryDict?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 生命周期
   */
  lifeCycle?: number
  /**
   * 主要材质
   */
  mainMaterial?: string
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * MOQ
   */
  moq?: number
  /**
   * 模具是否量产：YES_NO
   */
  mouldHasProductDict?: string
  /**
   * 模具类型：MOULD_TYPE
   */
  mouldTypeDict?: string
  /**
   * 产品分类
   */
  productClassify?: string
  /**
   * 产品名称
   */
  productName?: string
  /**
   * 产品大类：PRODUCT_ZH_CATEGORY
   */
  productZhCategoryDict?: string
  /**
   * 公告id
   */
  publicNoticeId?: number
  /**
   * 样品寄送地址id
   */
  pushStyleAddressId?: number
  /**
   * 退回原因
   */
  returnReasonDict?: string
  /**
   * 样品数量
   */
  sampleCount?: number
  /**
   * 楦型：SHOES_TREE
   */
  shoesTreeDict?: string
  /**
   * 页面尺寸
   */
  size?: number
  /**
   * 尺码-闭
   */
  sizeEnd?: number
  /**
   * 尺码-开
   */
  sizeStart?: number
  /**
   * 多个字段排序配置。
   */
  sortList?: PageSort[]
  /**
   * 推款信息来源：SOURCE_FROM
   */
  sourceFromDict?: string
  /**
   * 状态：PUSH_STYLE_STATE
   */
  stateDict?: string
  /**
   * 未选中原因：UN_SELECT_REASON
   */
  unSelectReasonDict?: string
  /**
   * 供应商区域code
   */
  vendorAreaCode?: string
  /**
   * 供应商区域名称
   */
  vendorAreaName?: string
  /**
   * 供应商id
   */
  vendorId?: number
  /**
   * 供应商名称
   */
  vendorName?: string
  [property: string]: any
}

/**
 * PushAggregateResponse
 */
export interface PushAggregateResponse {
  /**
   * 样品接收时间
   */
  acceptTime?: Date
  attachment?: PushStyleAttachmentDTO
  /**
   * 大底开模周期
   */
  bottomCycle?: number
  /**
   * 品牌id
   */
  brandId?: number
  /**
   * 品牌名称
   */
  brandName?: string
  /**
   * 编号
   */
  code?: string
  /**
   * 公司名称
   */
  companyName?: string
  /**
   * 联系地址
   */
  contactAddress?: string
  /**
   * 邮箱
   */
  contactEmail?: string
  /**
   * 联系人
   */
  contactName?: string
  /**
   * 联系电话
   */
  contactPhone?: string
  /**
   * 是否有底楦：YES_NO
   */
  containBottomDict?: string
  /**
   * 是否有底楦：YES_NO
   */
  containBottomDict_zh?: string
  /**
   * 现有配色
   */
  containColor?: string
  /**
   * 包含半码：YES_NO
   */
  containHalfDict?: string
  /**
   * 包含半码：YES_NO
   */
  containHalfDict_zh?: string
  /**
   * 包含税：YES_NO
   */
  containTaxDict?: string
  /**
   * 包含税：YES_NO
   */
  containTaxDict_zh?: string
  /**
   * 成本-闭区间
   */
  costEnd?: number
  /**
   * 成本-开区间
   */
  costStart?: number
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 数据状态（0：正常, 1：删除）
   */
  delFlag?: number
  /**
   * 子项-推款产品清单-产品设计师关联表
   */
  designer?: PushStyleDesignerDTO[]
  /**
   * 已售卖品牌
   */
  hasSellBrand?: string
  /**
   * 已售卖国家：TODO
   */
  hasSellCountryDict?: string
  /**
   * 已售卖国家：TODO
   */
  hasSellCountryDict_zh?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 生命周期
   */
  lifeCycle?: number
  /**
   * 主要材质
   */
  mainMaterial?: string
  /**
   * 子项-推款产品清单-型体关联表
   */
  model?: PushStyleModelDTO[]
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * MOQ
   */
  moq?: number
  /**
   * 模具是否量产：YES_NO
   */
  mouldHasProductDict?: string
  /**
   * 模具是否量产：YES_NO
   */
  mouldHasProductDict_zh?: string
  /**
   * 模具类型：MOULD_TYPE
   */
  mouldTypeDict?: string
  /**
   * 模具类型：MOULD_TYPE
   */
  mouldTypeDict_zh?: string
  /**
   * 子项-推款产品清单-企划人员关联表
   */
  planer?: PushStylePlanerDTO[]
  /**
   * 产品分类
   */
  productClassify?: string
  /**
   * 产品名称
   */
  productName?: string
  /**
   * 产品大类：PRODUCT_ZH_CATEGORY
   */
  productZhCategoryDict?: string
  /**
   * 产品大类：PRODUCT_ZH_CATEGORY
   */
  productZhCategoryDict_zh?: string
  /**
   * 公告id
   */
  publicNoticeId?: number
  /**
   * 样品寄送地址id
   */
  pushStyleAddressId?: number
  /**
   * 子项-推款产品清单-备注信息表
   */
  remark?: PushStyleRemarkDTO[]
  /**
   * 退回原因
   */
  returnReasonDict?: string
  /**
   * 样品数量
   */
  sampleCount?: number
  /**
   * 楦型：SHOES_TREE
   */
  shoesTreeDict?: string
  /**
   * 楦型：SHOES_TREE
   */
  shoesTreeDict_zh?: string
  /**
   * 尺码-闭
   */
  sizeEnd?: number
  /**
   * 尺码-开
   */
  sizeStart?: number
  /**
   * 推款信息来源：SOURCE_FROM
   */
  sourceFromDict?: string
  /**
   * 推款信息来源：SOURCE_FROM
   */
  sourceFromDict_zh?: string
  /**
   * 状态：PUSH_STYLE_STATE
   */
  stateDict?: string
  /**
   * 状态：PUSH_STYLE_STATE
   */
  stateDict_zh?: string
  /**
   * 子项-推款产品清单-风格关联表
   */
  styleBody?: PushStyleBodyDTO[]
  /**
   * 未选中原因：UN_SELECT_REASON
   */
  unSelectReasonDict?: string
  /**
   * 未选中原因：UN_SELECT_REASON
   */
  unSelectReasonDict_zh?: string
  /**
   * 供应商区域code
   */
  vendorAreaCode?: string
  /**
   * 供应商区域名称
   */
  vendorAreaName?: string
  /**
   * 供应商id
   */
  vendorId?: number
  /**
   * 供应商名称
   */
  vendorName?: string
  [property: string]: any
}

/**
 * PushStyleAttachmentDTO
 */
export interface PushStyleAttachmentDTO {
  bottomPic?: number[]
  bottomPic_url?: OssUploadRecordDTO[]
  colorPics?: number[]
  colorPics_url?: OssUploadRecordDTO[]
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 数据状态（0：正常，1：删除）
   */
  delFlag?: number
  frontPic?: number[]
  frontPic_url?: OssUploadRecordDTO[]
  /**
   * 主键
   */
  id?: number
  mainPic?: number[]
  mainPic_url?: OssUploadRecordDTO[]
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  otherPics?: number[]
  otherPics_url?: OssUploadRecordDTO[]
  /**
   * 推款主键id
   */
  pushStyleId?: number
  referFiles?: number[]
  referFiles_url?: OssUploadRecordDTO[]
  sidePics?: number[]
  sidePics_url?: OssUploadRecordDTO[]
  [property: string]: any
}

/**
 * OssUploadRecordDTO
 */
export interface OssUploadRecordDTO {
  /**
   * 文件contentType
   */
  contentType?: string
  /**
   * 文件类型
   */
  fileType?: string
  /**
   * 文件大小，单位KB
   */
  kbSize?: number
  /**
   * 存到oss服务器文件名
   */
  objectName?: string
  /**
   * 原始文件名
   */
  originFileName?: string
  /**
   * 文件，限时URL
   */
  url?: string
  [property: string]: any
}

/**
 * PushStyleDesignerDTO
 */
export interface PushStyleDesignerDTO {
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 数据状态（0：正常，1：删除）
   */
  delFlag?: number
  /**
   * 主键
   */
  id?: number
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 推款主键id
   */
  pushStyleId?: number
  /**
   * 人员id
   */
  userId?: number
  /**
   * 人员名称
   */
  userName?: string
  [property: string]: any
}

/**
 * PushStyleModelDTO
 */
export interface PushStyleModelDTO {
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 数据状态（0：正常，1：删除）
   */
  delFlag?: number
  /**
   * 主键
   */
  id?: number
  /**
   * 型体：MODEL
   */
  modelDict?: string
  /**
   * 型体：MODEL
   */
  modelDict_zh?: string
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 推款主键id
   */
  pushStyleId?: number
  /**
   * 退回说明
   */
  returnReasonRemark?: string
  [property: string]: any
}

/**
 * PushStylePlanerDTO
 */
export interface PushStylePlanerDTO {
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 数据状态（0：正常，1：删除）
   */
  delFlag?: number
  /**
   * 主键
   */
  id?: number
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 推款主键id
   */
  pushStyleId?: number
  /**
   * 企划人员id
   */
  userId?: number
  /**
   * 企划人员名称
   */
  userName?: string
  [property: string]: any
}

/**
 * PushStyleRemarkDTO
 */
export interface PushStyleRemarkDTO {
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 数据状态（0：正常，1：删除）
   */
  delFlag?: number
  /**
   * 主键
   */
  id?: number
  /**
   * 评审意见
   */
  judgeRemark?: string
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 初选说明
   */
  primaryRemark?: string
  /**
   * 产品买点
   */
  productPoint?: string
  /**
   * 推款说明
   */
  pushRemark?: string
  /**
   * 推款主键id
   */
  pushStyleId?: number
  /**
   * 退回说明
   */
  returnReasonRemark?: string
  /**
   * 销售市场
   */
  sellMarket?: string
  /**
   * 特殊功能说明
   */
  speicalRemark?: string
  /**
   * 未选中说明
   */
  unSelectDescription?: string
  [property: string]: any
}

/**
 * PushStyleBodyDTO
 */
export interface PushStyleBodyDTO {
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 数据状态（0：正常，1：删除）
   */
  delFlag?: number
  /**
   * 主键
   */
  id?: number
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 推款主键id
   */
  pushStyleId?: number
  /**
   * 退回说明
   */
  returnReasonRemark?: string
  /**
   * 风格：STYLE_BODY
   */
  styleBodyDict?: string
  /**
   * 风格：STYLE_BODY
   */
  styleBodyDict_zh?: string
  [property: string]: any
}

/**
 * PushStyleAggregatePrimaryDTO
 */
export interface FirstPrimaryRequest {
  /**
   * 初选对象
   */
  list?: PrimaryItemDTO[]
  [property: string]: any
}

/**
 * PrimaryItemDTO
 */
export interface PrimaryItemDTO {
  /**
   * 品牌id
   */
  brandId?: number
  /**
   * 品牌名称
   */
  brandName?: string
  /**
   * 产品设计师
   */
  designer?: PushStyleDesignerInsertDTO[]
  /**
   * 推款id
   */
  id?: number
  /**
   * 产品企划
   */
  planer?: PushStylePlanerInsertDTO[]
  /**
   * 初选说明
   */
  primaryRemark?: string
  /**
   * 初选结果
   */
  primaryResult?: boolean
  /**
   * 样品寄送地id
   */
  pushStyleAddressId?: number
  /**
   * 未选中说明
   */
  unSelectDescription?: string
  /**
   * 未选中原因
   */
  unSelectReasonDict?: string
  /**
   * 供应商id:非合作供应商时存 0
   */
  vendorId?: number
  /**
   * 供应商名称
   */
  vendorName?: string
  [property: string]: any
}

/**
 * PushStyleDesignerInsertDTO
 */
export interface PushStyleDesignerInsertDTO {
  /**
   * 人员id
   */
  userId?: number
  /**
   * 人员名称
   */
  userName?: string
  [property: string]: any
}

/**
 * PushStylePlanerInsertDTO
 */
export interface PushStylePlanerInsertDTO {
  /**
   * 企划人员id
   */
  userId?: number
  /**
   * 企划人员名称
   */
  userName?: string
  [property: string]: any
}

/**
 * PushStyleAggregateJudgeRejectDTO
 */
export interface JudgeRejectRequest {
  /**
   * 推款id
   */
  id?: number
  /**
   * 未选中说明
   */
  unSelectDescription?: string
  /**
   * 未选中原因
   */
  unSelectReasonDict?: string
  [property: string]: any
}

/**
 * PushStyleAggregateJudgePassDTO
 */
export interface JudgePassRequest {
  /**
   * 归属品牌
   */
  brandId?: number
  /**
   * 归属品牌
   */
  brandName?: string
  /**
   * 产品设计师
   */
  designer?: PushStyleDesignerInsertDTO[]
  /**
   * 推款id
   */
  id?: number
  /**
   * 评审说明
   */
  judgeRemark?: string
  /**
   * 产品企划
   */
  planer?: PushStylePlanerInsertDTO[]
  /**
   * 产品类别id集合
   */
  productTypeIdArray?: string[]
  /**
   * 产品类别最后一级
   */
  productTypeLastId?: string
  /**
   * 产品类别名称集合
   */
  productTypeNameArray?: string[]
  /**
   * 销售市场
   */
  sellMarket?: string
  /**
   * 供应商id:非合作供应商时存 0
   */
  vendorId?: number
  /**
   * 供应商名称
   */
  vendorName?: string
  [property: string]: any
}

/**
 * Result«List«ConfigAcceptAddressCityDictDTO»»
 */
export interface ResultListConfigAcceptAddressCityDictDTO {
  /**
   * 返回标记：成功标记=0，其余都是失败
   */
  code?: string
  /**
   * 数据
   */
  datas?: ConfigAcceptAddressCityDictDTO[]
  /**
   * 是否成功
   */
  isSuccess?: boolean
  /**
   * 返回信息
   */
  msg?: string
  [property: string]: any
}

/**
 * ConfigAcceptAddressCityDictDTO
 */
export interface ConfigAcceptAddressCityDictDTO {
  /**
   * 样品寄送地：ACCEPT_CITY
   */
  cityDict?: string
  /**
   * 样品寄送地：ACCEPT_CITY
   */
  cityDict_zh?: string
  [property: string]: any
}

/**
 * PushStyleProductInfoModifyDTO
 */
export interface PushStyleProductInfoModifyDTO {
  attachment?: PushStyleAttachmentUpdateDTO
  /**
   * 大底开模周期
   */
  bottomCycle?: number
  /**
   * 是否有底楦：YES_NO
   */
  containBottomDict?: string
  /**
   * 现有配色
   */
  containColor?: string
  /**
   * 包含半码：YES_NO
   */
  containHalfDict?: string
  /**
   * 包含税：YES_NO
   */
  containTaxDict?: string
  /**
   * 成本-闭区间
   */
  costEnd?: number
  /**
   * 成本-开区间
   */
  costStart?: number
  /**
   * 已售卖品牌
   */
  hasSellBrand?: string
  /**
   * 已售卖地区：EXPORT_AREA
   */
  hasSellCountryDict?: string[]
  /**
   * push_style主键
   */
  id?: number
  /**
   * 修改项目
   */
  items?: PushStyleProductInfoModifyRecordInsertDTO[]
  /**
   * 生产周期
   */
  lifeCycle?: number
  /**
   * 主要材质
   */
  mainMaterial?: string
  /**
   * 子表-推款产品清单-型体关联表
   */
  model?: string[]
  /**
   * MOQ
   */
  moq?: number
  /**
   * 模具是否量产：YES_NO
   */
  mouldHasProductDict?: string
  /**
   * 模具类型：MOULD_TYPE
   */
  mouldTypeDict?: string
  /**
   * 产品分类
   */
  productClassify?: string
  /**
   * 产品名称
   */
  productName?: string
  /**
   * 产品大类：PRODUCT_ZH_CATEGORY
   */
  productZhCategoryDict?: string
  remark?: PushStyleRemarkUpdateDTO
  /**
   * 楦型：SHOES_TREE
   */
  shoesTreeDict?: string
  /**
   * 尺码-闭
   */
  sizeEnd?: string
  /**
   * 尺码-开
   */
  sizeStart?: string
  /**
   * 推款信息来源：SOURCE_FROM
   */
  sourceFromDict?: string
  /**
   * 子表-推款产品清单-风格关联表
   */
  styleBody?: string[]
  [property: string]: any
}

/**
 * PushStyleAttachmentUpdateDTO
 */
export interface PushStyleAttachmentUpdateDTO {
  /**
   * 大底图
   */
  bottomPic?: string[]
  /**
   * 配色图
   */
  colorPics?: string[]
  /**
   * 正面图
   */
  frontPic?: string[]
  /**
   * 主键
   */
  id?: number
  /**
   * 款式主图
   */
  mainPic?: string[]
  /**
   * 其他图片
   */
  otherPics?: string[]
  /**
   * 推款主键id
   */
  pushStyleId?: number
  /**
   * 参考文件
   */
  referFiles?: string[]
  /**
   * 侧面图
   */
  sidePics?: string[]
  [property: string]: any
}

/**
 * PushStyleProductInfoModifyRecordInsertDTO
 */
export interface PushStyleProductInfoModifyRecordInsertDTO {
  /**
   * 修改后
   */
  afterValue?: string
  /**
   * 修改前
   */
  beforeValue?: string
  /**
   * 修改人名称
   */
  modifyByName?: string
  /**
   * 修改项目（属性名）
   */
  name?: string
  /**
   * 推款主键id
   */
  pushStyleId?: number
  [property: string]: any
}

/**
 * PushStyleRemarkUpdateDTO
 */
export interface PushStyleRemarkUpdateDTO {
  /**
   * 主键
   */
  id?: number
  /**
   * 评审意见
   */
  judgeRemark?: string
  /**
   * 初选说明
   */
  primaryRemark?: string
  /**
   * 产品买点
   */
  productPoint?: string
  /**
   * 推款说明
   */
  pushRemark?: string
  /**
   * 推款主键id
   */
  pushStyleId?: number
  /**
   * 退回说明
   */
  returnReasonRemark?: string
  /**
   * 销售市场
   */
  sellMarket?: string
  /**
   * 特殊功能说明
   */
  specialRemark?: string
  /**
   * 未选中说明
   */
  unSelectDescription?: string
  [property: string]: any
}
