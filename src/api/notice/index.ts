import request from '@/config/fetch'
import type {
  NoticePageRequest,
  PublicNoticeAggregateDTO,
  PublicNoticeGrantRequest,
  PublicNoticeCloseDTO,
  RecordElement,
  PushAggregateRequest,
  PushAggregateResponse,
  PushAddRequest,
  FirstPrimaryRequest,
  JudgeRejectRequest,
  JudgePassRequest,
  ResumeRequest,
  ReturnRequest,
  ResultListConfigAcceptAddressCityDictDTO,
  PushStyleProductInfoModifyDTO,
  PushStyleAggregateStateDTO
} from './types'

export const getNoticeListApi = (data: NoticePageRequest): Promise<PublicNoticeAggregateDTO> => {
  return request.post({
    url: '/pdm/publicNotice/pageAggregate',
    data
  })
}

// 保存草稿
export const draftApi = (data: PublicNoticeAggregateDTO): Promise<IResponse> => {
  return request.post({
    url: '/pdm/publicNotice/draft',
    data
  })
}

// 保存并发布
export const saveApi = (data: PublicNoticeAggregateDTO): Promise<IResponse> => {
  return request.post({
    url: '/pdm/publicNotice/save',
    data
  })
}

// 修改
export const updateApi = (data: PublicNoticeAggregateDTO): Promise<IResponse> => {
  return request.post({
    url: '/pdm/publicNotice/update',
    data
  })
}

// 发布公告
export const releaseApi = (data: { ids?: number[] }): Promise<IResponse> => {
  return request.post({
    url: '/pdm/publicNotice/release',
    data
  })
}

export const getApi = (params: { id?: number }): Promise<ResponseDataType<RecordElement>> => {
  return request.get({
    url: '/pdm/publicNotice/get',
    params
  })
}

// 分配公告
export const grantApi = (data: PublicNoticeGrantRequest): Promise<IResponse> => {
  return request.post({
    url: '/pdm/publicNotice/grant',
    data
  })
}

// 关闭公告
export const closeApi = (data: PublicNoticeCloseDTO): Promise<IResponse> => {
  return request.post({
    url: '/pdm/publicNotice/close',
    data
  })
}

// 重启公告
export const reopenApi = (data: PublicNoticeAggregateDTO): Promise<IResponse> => {
  return request.post({
    url: '/pdm/publicNotice/reopen',
    data
  })
}

// 推款产品清单分页
export const getPushListApi = (data: PushAggregateRequest): Promise<PushAggregateResponse> => {
  return request.post({
    url: '/pdm/pushStyle/pageAggregate',
    data
  })
}

// 关闭推款清单
export const deleteApi = (data: { ids?: Array<number> }): Promise<IResponse> => {
  return request.post({
    url: '/pdm/pushStyle/delete',
    data
  })
}

// 新增推款产品
export const InsertApi = (data: PushAddRequest): Promise<IResponse> => {
  return request.post({
    url: '/pdm/pushStyle/insert',
    data
  })
}

// 推款产品详情
export const getProductApi = (params: {
  id?: number
}): Promise<ResponseDataType<PushAggregateResponse>> => {
  return request.get({
    url: '/pdm/pushStyle/get',
    params
  })
}

// 初选
export const primaryApi = (data: FirstPrimaryRequest): Promise<IResponse> => {
  return request.post({
    url: '/pdm/pushStyle/primary',
    data
  })
}

// 评审通过
export const commentPassApi = (data: JudgePassRequest): Promise<IResponse> => {
  return request.post({
    url: '/pdm/pushStyle/judgePass',
    data
  })
}

// 评审不通过
export const commentRejectApi = (data: JudgeRejectRequest): Promise<IResponse> => {
  return request.post({
    url: '/pdm/pushStyle/judgeReject',
    data
  })
}

/**
 * 接收样品
 */
type AcceptRequest = Pick<PushAggregateResponse, 'acceptTime' | 'id' | 'sampleCount'>[]
export const acceptApi = (data: { list: AcceptRequest }): Promise<IResponse> => {
  return request.post({
    url: '/pdm/pushStyle/accept',
    data
  })
}

/**
 * @param  resumeJudge 评审中true
 * @param  resumePrimary 初选中true
 * @returns
 */
export const resumeApi = (data: ResumeRequest): Promise<IResponse> => {
  return request.post({
    url: '/pdm/pushStyle/resume',
    data
  })
}

/**
 * @description 评审操作退回供应商
 */
export const returnApi = (data: ReturnRequest): Promise<IResponse> => {
  return request.post({
    url: '/pdm/pushStyle/returnStyle',
    data
  })
}

/**
 * @description 推款导出
 */
export const getPushExportApi = (data: PushAggregateRequest): Promise<IResponse> => {
  return request.post({
    url: '/pdm/pushStyle/export',
    data
  })
}

// 根据品牌获取样品信息
export const getBrandAddressApi = (
  data: number
): Promise<ResultListConfigAcceptAddressCityDictDTO> => {
  return request.post({
    url: '/pdm/config/address/acceptCity',
    data
  })
}

// 获取修改日志
export const modifyRecordApi = (id: number): Promise<ErrorTip> => {
  return request.get({ url: `/pdm/modifyRecord/list/${id}` })
}

// 修改校验
export const modifyCheckApi = (data: PushStyleProductInfoModifyDTO): Promise<IResponse> => {
  return request.post({
    url: '/pdm/modifyRecord/save/check',
    data
  })
}

// 修改提交
export const modifySubmitApi = (data: PushStyleProductInfoModifyDTO): Promise<IResponse> => {
  return request.post({
    url: '/pdm/modifyRecord/save/submit',
    data
  })
}

/**
 * @description 推款状态数量
 */
export const pageAggregateCountApi = (
  data: PushAggregateRequest
): Promise<PushStyleAggregateStateDTO> => {
  return request.post({
    url: '/pdm/pushStyle/pageAggregateCount',
    data
  })
}
