import { ProductTaskNodeEnum } from '@/views/basic-library-manage/product-library/const'

/**
 * 分页查询
 */
export namespace ProductSkcInfoPageAPI {
  export interface Params {
    id?: string
    /**
     * 品牌
     */
    brand?: string[]
    /**
     * 选品渠道
     */
    chooseChannel?: string[]
    /**
     * 颜色编码
     * 产品配色
     */
    colorCode?: string[]
    /**
     * 设计师
     */
    designerId?: string[]
    /**
     * 开发渠道
     */
    developmentChannel?: string[]
    /**
     * 产品系列
     */
    developmentDirection?: string[]
    /**
     * 产品阶段
     */
    developStage?: string[]
    /**
     * 创建时间结束时间
     */
    endDate?: string
    /**
     * 过滤skcCode为空，1为过滤，0为不过滤
     */
    filterSkcCode?: number
    idList?: string[]
    /**
     * 上市季节
     */
    launchSeason?: string[]
    /**
     * 主渠道标识
     */
    mainChannelMark?: string[]
    /**
     * 产品类目,最低等级id
     */
    productCategory?: string[]
    /**
     * 产品id
     */
    productIdList?: string[]
    /**
     * PRODUCT NAME
     */
    productName?: string
    /**
     * 产品编号
     */
    productNumber?: string[]
    /**
     * 产品定级
     */
    productPositioning?: string[]
    /**
     * skc编码列表
     */
    productSkcIdList?: string[]
    /**
     * 产品尺码段
     */
    sizeRangeId?: string[]
    /**
     * skc编码
     */
    skcCode?: string
    /**
     * 状态
     */
    skcStatus?: string[]
    /**
     * 创建时间开始时间
     */
    startDate?: string
    /**
     * style编码
     */
    styleNumber?: string
    /**
     * style WMS
     */
    styleWms?: string[]
    ableUpdate?: boolean
  }

  export interface List {
    skcColorId?: number
    colorSchemeDraftUrl?: BaseFileDTO
    /**
     * 品牌  itemName
     */
    brand?: string
    brandItemName?: string
    /**
     * 开发类型
     */
    developmentTypeItemName?: string
    /**
     * 颜色编码
     */
    colorCode?: string
    /**
     * 产品配色
     */
    colorName?: string
    /**
     * 配色类型 ItemName
     */
    colorType?: string
    /**
     * 确认样评审结果 （ItemName）
     */
    confirmSampleReviewResults?: string
    /**
     * 创建时间
     */
    createTime?: string
    /**
     * 数据状态
     */
    dataStatus?: string
    /**
     * 数据状态 （ItemName）
     */
    dataStatusItemName?: string
    /**
     * 产品设计师的id （ItemName）
     */
    designerId?: number
    /**
     * 产品设计师的id （ItemName）
     */
    designerIdItemName?: string
    /**
     * 企划创建人 （ItemName）
     */
    designPersonId?: number
    /**
     * 产品阶段 （ItemName）
     */
    developStage?: string
    /**
     * 数据下发失败说明
     */
    errorDataMsg?: string
    id?: number
    /**
     * 上市季 itemName
     */
    launchSeason?: string
    launchSeasonItemName?: string
    /**
     * 主要面料
     */
    mainFabric?: string
    mainFabricItemName?: string
    /**
     * 操作人  （ItemName）
     */
    modifyById?: number
    /**
     * 操作时间  （ItemName）
     */
    modifyTime?: string
    /**
     * 是否已下单 （ItemName）
     */
    orderPlaced?: string
    /**
     * 初样结论(ItemName)
     */
    preliminaryConclusion?: string
    /**
     * 产品类目 itemName
     */
    productCategory?: number
    productId?: number
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 齐色样样品图片
     */
    sampleUrl?: BaseFileDTO[]
    /**
     * 确认样样品图片
     */
    confirmSampleUrl?: BaseFileDTO[]
    /**
     * 选中尺码 （ItemName）
     */
    selectedSize?: number[]
    selectedSizeItemName?: string[]
    /**
     * 选品会结论 （ItemName）
     */
    selectionMeetingResult?: string
    /**
     * 数据下发时间
     */
    sendTime?: string
    /**
     * 是否已下发WMS （ItemName）
     */
    sendWms?: string
    /**
     * 产品尺码段 （ItemName）
     */
    sizeRangeId?: number[]
    sizeRangeIdItemName?: string[]
    /**
     * SKC编号
     */
    skcCode?: string
    /**
     * 阶段完成说明
     */
    stageCompDesc?: string
    /**
     * 颜色缩略图
     */
    thumbnail?: BaseFileDTO
    /**
     * 齐色样结论 （ItemName）
     */
    uniformColorConclusion?: string
    /**
     * 不植关税率(%) （ItemName）
     */
    unVelvetTariff?: number
    /**
     * 是否植绒 （ItemName）
     */
    velvetApplied?: string
    /**
     * 植绒要求 ItemName
     */
    velvetRequirements?: string
    /**
     * 植绒/植皮关税率(%)
     */
    velvetTariff?: number
    /**
     * 变更原因
     */
    invalidReason?: string
  }
  export type Request = Params & PageParams
  export type Response = PagedResponseData<List>
}
/**
 * 详情
 */
export namespace ProductSkcInfoDetailAPI {
  export interface ProductSkuInfoDetailResp {
    /**
     * 箱规
     */
    boxNumberText?: string
    /**
     * 颜色id
     */
    colorId?: number
    /**
     * 状态
     */
    dataStatus?: string
    dataStatusItemName?: string
    /**
     * 数据下发失败说明
     */
    errorDataMsg?: string
    /**
     * 是否已下单
     */
    orderPlaced?: string
    /**
     * 外箱高cm
     */
    outerBoxHeight?: number
    /**
     * 外箱长cm
     */
    outerBoxLong?: number
    /**
     * 外箱毛重kg
     */
    outerBoxRoughWeight?: number
    /**
     * 外箱宽cm
     */
    outerBoxWidth?: number
    /**
     * 包装标准id
     */
    packageStandardId?: number
    /**
     * 数据下发时间
     */
    sendTime?: string
    /**
     * 是否已下发WMS
     */
    sendWms?: string
    /**
     * 鞋盒高cm
     */
    shoeBoxHeight?: number
    /**
     * 鞋盒长cm
     */
    shoeBoxLong?: number
    /**
     * 鞋盒毛重kg
     */
    shoeBoxRoughWeight?: number
    /**
     * 鞋盒宽cm
     */
    shoeBoxWidth?: number
    /**
     * sizeId
     */
    sizeId?: number
    /**
     * size名称
     */
    sizeName?: string
    /**
     * 分段尺码
     */
    sizeSection?: string
    /**
     * sku编码
     */
    skuCode?: string
    /**
     * 状态
     */
    skuStatus?: string
    styleId?: number
    /**
     * style名称
     */
    styleName?: string
    /**
     * 供应商id
     */
    vendorId?: number
    /**
     * 供应商名称
     */
    vendorName?: string
    [property: string]: any
  }
  export interface Data {
    /**
     * sku信息
     */
    skuList?: ProductSkuInfoDetailResp[]
    /**
     * 齐色样外观意见
     */
    appearanceOpinion?: string
    /**
     * 确认样外观意见
     */
    confirmAppearanceOpinion?: string
    /**
     * 品牌
     */
    brand?: string
    /**
     * 渠道销量汇总
     */
    channelSales?: SkcChannelSales[]
    /**
     * 渠道销售标识
     */
    channelSalesMark?: string
    /**
     * 颜色编码
     */
    colorCode?: string
    /**
     * 产品配色
     */
    colorCodeItemName?: string
    /**
     * 配色图稿
     */
    colorSchemeDraftUrl?: BaseFileDTO[]
    /**
     * 配色类型
     */
    colorType?: string
    /**
     * 确认样完成日期
     */
    confirmSampleReviewDate?: Date
    /**
     * 确认样打样次数
     */
    confirmSampleReviewNum?: number
    /**
     * 确认样评审意见
     */
    confirmSampleReviewOpinions?: string
    /**
     * 确认样评审结果
     */
    confirmSampleReviewResults?: string
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: Date
    /**
     * 状态
     */
    dataStatus?: string
    delFlag?: number
    /**
     * 跟进开发人员
     */
    developmentPersonnel?: string
    /**
     * 产品阶段
     */
    developStage?: string
    /**
     * 齐色样完成日期
     */
    dyeSampleCompletionDate?: Date
    /**
     * 齐色样打样次数
     */
    dyeSampleMakingCount?: number
    /**
     * 齐色样评审意见
     */
    dyeSampleReviewOpinion?: string
    /**
     * 数据下发失败说明
     */
    errorDataMsg?: string
    /**
     * 订单量汇总
     */
    estimatedOrderVolume?: number
    /**
     * 预估上架时间
     */
    estimatedShelfTime?: Date
    /**
     * 齐色样试穿意见
     */
    fittingOpinion?: string
    /**
     * 确认样试穿意见
     */
    confirmFittingOpinion?: string
    id?: number
    /**
     * 上市季节
     */
    launchSeason?: string
    /**
     * 里材料编码
     */
    liningMaterialCode?: number[]
    /**
     * 里绒情况
     */
    liningSituation?: number[]
    /**
     * 主要面料
     */
    mainFabric?: string
    /**
     * skc材料信息
     */
    materialInfoList?: SkuMaterialInfo[]
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: Date
    /**
     * 是否已下单
     */
    orderPlaced?: string
    /**
     * 垫材料(填充)编码
     */
    paddingMaterialFillingCode?: number[]
    /**
     * 垫材料(面)编码
     */
    paddingMaterialSurfaceCode?: number[]
    /**
     * 初样结论(通过 不通过)
     */
    preliminaryConclusion?: string
    /**
     * 产品类目
     */
    productCategory?: number
    /**
     * 产品id
     */
    productId?: number
    productInfoDetailResp?: ProductInfoDetailResp
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 打样信息记录
     */
    sampleMakingInfo?: string
    /**
     * 齐色样样品图片
     */
    sampleUrl?: BaseFileDTO[]
    /**
     * 确认样样品图片
     */
    confirmSampleUrl?: BaseFileDTO[]
    /**
     * 1是  0否 是否选中
     */
    selectable?: number
    /**
     * 选品渠道
     */
    selectionChannel?: string
    /**
     * 选品会结果
     */
    selectionMeetingResult?: string
    /**
     * 选品会评审意见
     */
    selectionMeetingReviewOpinion?: string
    /**
     * 数据下发时间
     */
    sendTime?: Date
    /**
     * 是否已下发WMS
     */
    sendWms?: string
    /**
     * SKC编号
     */
    skcCode?: string
    /**
     * 状态
     */
    skcStatus?: string
    /**
     * 底材料（中底）编码
     */
    soleMaterialMidsoleCode?: string
    /**
     * 底材料（大底）编码
     */
    soleMaterialOutsoleCode?: number[]
    /**
     * 阶段完成说明
     */
    stageCompDesc?: string
    /**
     * 款式编码
     */
    styleCode?: string
    /**
     * 齐色样技术评估意见
     */
    technicalAssessment?: string
    /**
     * 确认样技术评估意见
     */
    confirmTechnicalAssessment?: string
    /**
     * 跟进技术人员
     */
    technicalPersonnel?: string
    thumbnail?: BaseFileDTO
    /**
     * 齐色样结论(通过 不通过
     */
    uniformColorConclusion?: string
    /**
     * 齐色样处理结果
     */
    uniformColorSampleProcessingResult?: string
    /**
     * 不植关税率(%)
     */
    unVelvetTariff?: number
    /**
     * 是否植绒
     */
    velvetApplied?: string
    /**
     * 植绒备注
     */
    velvetRemark?: string
    /**
     * 植绒要求
     */
    velvetRequirements?: string
    /**
     * 植绒/植皮关税率(%)
     */
    velvetTariff?: number
    /**
     * 标准码
     */
    sizeStandValue?: string
    [property: string]: any
  }
  /**
   * 产品基础信息库
   */
  export interface ProductInfoDetailResp {
    taskTimeRecord?: {
      [key in ProductTaskNodeEnum]?: {
        taskNode?: ProductTaskNodeEnum
        createTime?: string
        createById?: number
        createByIdItemName?: string
      }
    }
    /**
     * 1是  0否  是否过踝/过膝
     */
    ankleCoverage?: string
    /**
     * 齐色样外观意见
     */
    appearanceOpinions?: string
    /**
     * 适用季节
     */
    applicableSeason?: string
    /**
     * arch type
     */
    archType?: string
    /**
     * 供应商
     */
    assignedFactory?: string
    /**
     * 产品鞋跟
     */
    associatedHeelInfo?: string
    /**
     * 关联跟信息
     */
    associatedHeelInfo2?: string
    /**
     * 产品鞋垫
     */
    associatedInsoleInfo?: string
    /**
     * 关联垫信息
     */
    associatedInsoleInfo2?: string
    /**
     * 产品楦型
     */
    associatedLastType?: string
    /**
     * 关联楦型
     */
    associatedLastType2?: string
    /**
     * 产品大底
     */
    associatedSoleInfo?: string
    /**
     * 关联底信息
     */
    associatedSoleInfo2?: string
    /**
     * 基本计量单位
     */
    basicUnitOfMeasure?: string
    /**
     * 批量工艺
     */
    batchProcess?: string
    /**
     * 批量工艺图链接
     */
    batchProcessImageLink?: string
    /**
     * 靴筒高(mm）
     */
    bootLegHeightMm?: number
    /**
     * 品牌
     */
    brand?: string
    /**
     * 品牌维权力度
     */
    brandEnforcementEffort?: string
    /**
     * 品牌维权力度
     */
    brandEnforcementEffort2?: string
    /**
     * 装箱数（箱规）
     */
    cartonQuantity?: number
    /**
     * 儿童年龄段
     */
    childrenAvgGroup?: string[]
    /**
     * 儿童人群
     */
    childrenCrowd?: string
    /**
     * 选品渠道
     */
    chooseChannel?: string[]
    /**
     * 穿脱方式
     */
    closureMethod?: string
    /**
     * 初样颜色英文名称
     */
    colorEnName?: string
    /**
     * 初样颜色名称
     */
    colorName?: string
    /**
     * 配色类型
     */
    colorType?: string
    /**
     * 竞品链接
     */
    competitiveBrandLink?: string
    /**
     * 技转全套报告
     */
    completeTechnologyTransferReport?: string
    /**
     * 确认样操作人Id
     */
    confirmationSampleCreateBy?: number
    /**
     * 确认样操作日期
     */
    confirmationSampleCreateTime?: Date
    /**
     * 确认样打样日期
     */
    confirmationSampleDate?: Date
    /**
     * 确认样打样单
     */
    confirmationSampleOrderUrl?: BaseFileDTO[]
    /**
     * 确认样外发日期
     */
    confirmDispatchDate?: Date
    /**
     * 1是  0否 是否需要侵权排查
     */
    copyright?: string
    /**
     * 关联侵权排查
     */
    copyrightInspection?: string
    /**
     * 同款鞋对应品牌
     */
    correspondingBrand?: string
    /**
     * 对应品牌鞋图
     */
    correspondingBrandImage?: string
    /**
     * 成本区间最小值（$）
     */
    costRangeMax?: number
    /**
     * 成本区间最小值（$）
     */
    costRangeMin?: number
    /**
     * 创建人
     */
    createById?: number
    /**
     * 创建时间
     */
    createTime?: Date
    /**
     * 数据状态
     */
    dataStatus?: string
    delFlag?: number
    /**
     * 设计师是否需协助
     */
    designerAssistanceRequired?: string
    /**
     * 产品设计师的id
     */
    designerId?: number
    /**
     * 企划创建人
     */
    designPersonId?: number
    /**
     * 设计图
     */
    designUrl?: BaseFileDTO[]
    /**
     * 开发渠道
     */
    developmentChannel?: string
    /**
     * 跟进开发人员Id
     */
    developmentContactPersonId?: number
    developmentContactPersonIdItemName?: string
    /**
     * 产品系列
     */
    developmentDirection?: string
    /**
     * 开发策略
     */
    developmentStrategy?: string
    /**
     * 开发类型
     */
    developmentType?: string
    /**
     * 产品阶段
     */
    developStage?: string
    /**
     * 1是  0否 是否S2C
     */
    dtc?: string
    /**
     * 数据下发失败说明
     */
    errorDataMsg?: string
    /**
     * 预估售价（$）
     */
    estimatedPrice?: number
    /**
     * 汇率
     */
    exchangeRate?: number
    /**
     * 期望上架日期
     */
    expectedLaunchDate?: Date
    /**
     * 工厂报价（¥）含税
     */
    factoryQuotation?: number
    /**
     * 1是  0否 是否折叠
     */
    foldable?: string
    /**
     * 垫脚工艺
     */
    footbedTechnique?: string
    /**
     * 功能系列(A+参考）
     */
    functionSeriesAPlusReference?: string
    /**
     * 功能系列(视频参考）
     */
    functionSeriesVideoReference?: string
    /**
     * 1是  0否有功能吊牌挂牌
     */
    hasFeatureTag?: string
    /**
     * 跟高
     */
    heelHeight?: string
    /**
     * 跟高（mm）
     */
    heelHeightMm?: number
    /**
     * 跟型
     */
    heelType?: string
    id?: number
    /**
     * 图片详情
     */
    imageDetails?: string
    /**
     * 侵权风险初判
     */
    infringementRiskAssessment?: string
    /**
     * 侵权风险初判
     */
    infringementRiskAssessment2?: string
    /**
     * 产品颜色
     */
    initialSampleColorId?: number
    /**
     * 初样完成日期
     */
    initialSampleCompletionDate?: Date
    /**
     * 下发打样日期
     */
    initialSampleIssueDate?: Date
    /**
     * 初样评审意见
     */
    initialSampleReviewOpinions?: string
    /**
     * 初样评审结果
     */
    initialSampleReviewResult?: string
    /**
     * 初样打样次数
     */
    initialSampleTimes?: number
    /**
     * 内控复核人
     */
    internalControlReviewer?: string
    /**
     * 内控复核人
     */
    internalControlReviewer2?: string
    /**
     * 内控知识产权复核检索记录
     */
    ipReviewRecord?: string
    /**
     * 内控知识产权复核检索记录
     */
    ipReviewRecord2?: string
    /**
     * 是否新开模
     */
    isNewMold?: string
    /**
     * 产品是否上市
     */
    isProductLaunched?: string
    /**
     * 楦底类型
     */
    lastBottomType?: string
    /**
     * 楦型标准
     */
    lastsStandard?: string
    /**
     * 上市季
     */
    launchSeason?: string
    /**
     * 内里标签
     */
    liningLabel?: string
    /**
     * 主渠道标识
     */
    mainChannelMark?: string[]
    /**
     * 主面料
     */
    mainMaterial?: string
    /**
     * 1是 0否 强制性警示声明
     */
    mandatoryWarningStatement?: string
    /**
     * 齐色样打样单
     */
    matchingSampleUrl?: BaseFileDTO[]
    /**
     * 材料送检说明
     */
    materialInspectionInstructions?: string
    /**
     * 选品会结果
     */
    meetingResult?: string
    /**
     * 操作人
     */
    modifyById?: number
    /**
     * 操作时间
     */
    modifyTime?: Date
    /**
     * 颜色（新增）
     */
    newColor?: string
    /**
     * 配色类型（新增）
     */
    newColorType?: string
    /**
     * 主面料（新增）
     */
    newMainMaterial?: string
    /**
     * 开口类型（单鞋必填）
     */
    openingType?: string
    /**
     * 是否已下单
     */
    orderPlaced?: string
    /**
     * 齐色样外发打样日期(设计师)
     */
    outsourcingIssueDate?: Date
    /**
     * 包装代码
     */
    packagingCode?: string
    /**
     * 包装类型
     */
    packagingType?: string
    /**
     * 预估上架日期
     */
    predictLaunchDate?: Date
    /**
     * 产品类目,最低等级id
     */
    productCategory?: number
    /**
     * 产品描述
     */
    productDescription?: string
    /**
     * 产品简介【50字内】
     */
    productIntroShort?: string
    /**
     * 制作工艺
     */
    productionTechnique?: string
    /**
     * 1是 0否 是否上市
     */
    productLaunched2?: string
    /**
     * 产品编号（自动生成）
     */
    productNumber?: string
    /**
     * 产品定位
     */
    productPositioning?: string
    /**
     * 产品风格
     */
    productStyle?: string
    /**
     * 参考品牌
     */
    referenceBrand?: string
    /**
     * 参考品牌
     */
    referenceBrand2?: string
    /**
     * 参考品牌链接
     */
    referenceBrandLink?: string
    /**
     * 对标页面呈现参考
     */
    referencePageDisplay?: string
    /**
     * 参考产品编号
     */
    referenceProductCode?: string
    /**
     * 参考款式图
     */
    referenceUrl?: BaseFileDTO[]
    /**
     * 区域
     */
    region?: string
    /**
     * 相关外观图片
     */
    relatedAppearanceImages?: string
    /**
     * 备注
     */
    remarks?: string
    /**
     * 吊牌价
     */
    retailPrice?: number
    /**
     * 复核日期
     */
    reviewDate?: Date
    /**
     * 评审日期
     */
    reviewDate2?: Date
    /**
     * 复核日期
     */
    reviewDate3?: Date
    /**
     * 评审意见
     */
    reviewOpinions?: string
    /**
     * 规避建议/风险情况说明
     */
    riskAvoidanceSuggestions?: string
    /**
     * 规避建议/风险情况说明
     */
    riskAvoidanceSuggestions2?: string
    /**
     * 风险国家
     */
    riskCountries?: string
    /**
     * 风险国家
     */
    riskCountries2?: string
    /**
     * 风险初筛
     */
    riskScreening?: string
    /**
     * 是否含金属鞋头
     */
    safetyFeatures?: string
    /**
     * 齐色打样日期
     */
    sameColorSampleDate?: Date
    /**
     * 齐色样样品图片
     */
    sampleImages?: string
    /**
     * 打样信息记录
     */
    sampleInformationRecord?: string
    /**
     * 初样打样单
     */
    samplePrintUrl?: BaseFileDTO[]
    /**
     * 场景
     */
    scene?: string
    /**
     * 场景图1-主场景(场景+服装)
     */
    sceneImage1?: string
    /**
     * 场景图2-辅场景
     */
    sceneImage2?: string
    /**
     * 场景图3-辅场景
     */
    sceneImage3?: string
    /**
     * 选中颜色
     */
    selectedColor?: string[]
    /**
     * 选中尺码
     */
    selectedSize?: number[]
    /**
     * 选品会评审意见
     */
    selectionMeetingReviewOpinion?: string
    /**
     * 卖点1【25字内】
     */
    sellingPoint1?: string
    /**
     * 卖点2【25字内】
     */
    sellingPoint2?: string
    /**
     * 卖点3【25字内】
     */
    sellingPoint3?: string
    /**
     * 数据下发时间
     */
    sendTime?: Date
    /**
     * 是否已下发WMS
     */
    sendWms?: string
    /**
     * 系列/重点款
     */
    seriesKeyStyles?: string
    /**
     * PRODUCT NAME
     */
    shoeName?: string
    /**
     * 鞋底工艺
     */
    shoeSoleTechnique?: string
    /**
     * 单色MOQ
     */
    singleColorMoq?: number
    /**
     * 单款MOQ
     */
    singleStyleMoq?: number
    /**
     * 尺码段id表
     */
    sizeRangeId?: number[]
    /**
     * sku信息
     */
    skcInfoDetailResp?: Data[]
    /**
     * 特殊包装方式
     */
    specialPackagingMethod?: string
    /**
     * 特殊检测需求及说明
     */
    specialTestingRequirements?: string
    /**
     * 立体图
     */
    stereogramImage?: string
    /**
     * 款式定位
     */
    stylePositioning?: string
    /**
     * 款式结构
     */
    styleStructure?: string
    /**
     * Style（WMS）
     */
    styleWms?: string
    /**
     * 建议主推色
     */
    suggestedMainColor?: string
    /**
     * 适用人群
     */
    targetAudience?: string
    /**
     * 任务节点
     */
    taskNode?: string
    /**
     * 跟进技术人员Id
     */
    technicalContactPersonId?: number
    technicalContactPersonIdItemName?: string
    /**
     * 图稿技术评估
     */
    technicalEvaluationOpinions?: string
    /**
     * 技术评估意见
     */
    technicalEvaluationOpinions2?: string
    /**
     * 产前技术评估报告
     */
    technologyAssessmentReport?: BaseFileDTO[]
    /**
     * 技术内审记录
     */
    techReviewRecordUrl?: BaseFileDTO[]
    thumbnail?: BaseFileDTO
    /**
     * 头型
     */
    toeShape?: string
    /**
     * 楦头标准
     */
    toeStandard?: string
    /**
     * 流行元素
     */
    trendyElements?: string
    /**
     * 试穿意见
     */
    trialWearOpinions?: string
    /**
     * 试穿报告
     */
    tryOnReportUrl?: BaseFileDTO[]
    /**
     * 不含税美金报价
     */
    usdQuotationExcludingTax?: number
    /**
     * 使用电池/附带电池
     */
    usesBattery?: string
    /**
     * VOC原款号
     */
    vocOriginalStyleNumber?: string
    /**
     * 防水级别
     */
    waterproofLevel?: string
  }

  /**
   * SkcChannelSales
   */
  export interface SkcChannelSales {
    /**
     * 渠道名称
     */
    salesChannel?: string
    /**
     * 销量量
     */
    salesNum?: number
  }

  /**
   * SkuMaterialInfo
   */
  export interface SkuMaterialInfo {
    /**
     * 面料的名称  如  1 ，2 ，3， 4
     */
    indexName?: string
    /**
     * 面料
     */
    material?: number
    materialItemName?: string
    /**
     * 面料占比%
     */
    materialRatio?: string
    /**
     * 纹路
     */
    texture?: string
    /**
     * 厚度(mm)
     */
    thickness?: string
    /**
     * 宽幅(cm)：
     */
    widthInCm?: string
    /**
     * 克重
     */
    wight?: string
  }

  export type Response = ResponseData<Data>
}
/**
 * 下发wms或者更新sku 状态
 */
export namespace ProductSkcInfoUpdateStatusAPI {
  export interface Params {
    /**
     * 需要更新sku状态的集合
     */
    reqs?: UpdateProductSkcInfoReq[]
    /**
     * 下发wms时 设置为true 并且设置 updateSkcInfos的id集合
     */
    sendWms?: boolean
  }
  export interface UpdateProductSkcInfoReq {
    /**
     * 状态
     */
    dataStatus?: string
    id?: number
  }
  export type Request = Params
  export type Response = BasicResponseData
}

export namespace ProductSkcInfoImageInfoAPI {
  export interface Params {
    /*skcId*/
    skcId?: string
    /*配色图稿*/
    colorSchemeDraft?: BaseFileDTO[]
    /*样品图片*/
    sample?: BaseFileDTO[]
    /* 初样样品图*/
    initialProofResultImg?: BaseFileDTO[]
    /* 齐色样样品图*/
    colorProofResultImg?: BaseFileDTO[]
    /* 确认样样品图*/
    confirmProofResultImg?: BaseFileDTO[]
    /* 白底图*/
    erpSkcImg?: BaseFileDTO[]
  }
  export type Response = ResponseData<Params>
}
