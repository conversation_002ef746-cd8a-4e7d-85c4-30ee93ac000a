import request from '@/config/fetch'
import {
  ProductSkcInfoPageAPI,
  ProductSkcInfoDetailAPI,
  ProductSkcInfoUpdateStatusAPI,
  ProductSkcInfoImageInfoAPI
} from './types'
import to from 'await-to-js'
import { service } from '@/config/axios/service'

/**
 * 分页查询
 */
export const productSkcInfoPage = (
  params: ProductSkcInfoPageAPI.Request,
  signal?: AbortSignal
): Promise<ProductSkcInfoPageAPI.Response> => {
  return request.get({ url: '/pdm-base/productSkcInfo/page', params, signal })
}
/**
 * 分页查询
 */
export const productSkcInfoDetail = (id: string): Promise<ProductSkcInfoDetailAPI.Response> => {
  return request.get({ url: `/pdm-base/productSkcInfo/detail/${id}` })
}
/**修改**/
export const editSkcImg = (data: ProductSkcInfoDetailAPI.Data) => {
  return request.post({ url: '/pdm-base/productSkcInfo/updateSkcImg', data })
}
/**
 * 下发wms或者更新sku 状态
 */
export const productSkcInfoUpdateStatus = (
  data: ProductSkcInfoUpdateStatusAPI.Request
): Promise<ProductSkcInfoUpdateStatusAPI.Response> => {
  return request.post({ url: `/pdm-base/productSkcInfo/updateStatus`, data })
}

export namespace SkcViewListAPI {
  export interface Row {
    /**
     * 字段属性(string)
     * 字段属性(string)集合
     */
    fieldAttr?: string[]
    fieldTitle?: string
    /**
     * 名称
     */
    name?: string
    /**
     * 类型(0 就是产品库)
     */
    type?: number
    id?: number
  }
  export type List = Row[]
  export type Response = ResponseData<List>
}

/**
 * skc列表的所有视图
 */
export function getAllSkcViews() {
  return to<SkcViewListAPI.Response>(service.get(`/pdm-base/viewConfiguration/allSkcViewByLogin`))
}

/**
 * 导出skc图片
 */
export function exportSkcImage(req: ProductSkcInfoPageAPI.Request) {
  return to<SkcViewListAPI.Response>(service.post(`/pdm-base/productSkcInfo/exportSkcImg`, req))
}

/**
 * 获取图片
 **/
export function getSkcImgData(id: string | number) {
  return to<ProductSkcInfoImageInfoAPI.Response>(
    service.get(`/pdm-base/productSkcInfo/imgData/${id}`)
  )
}
