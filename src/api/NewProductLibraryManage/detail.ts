import to from 'await-to-js'
import { service } from '@/config/fetch/service'
import { AttributeData } from '@/components/PlmBase/type'
import request from '@/config/fetch'
export namespace TableDetailApi {
  export interface VersionResp {
    /**
     * 业务ID
     */
    businessId?: number
    createById?: number
    /**
     * 创建人名称
     */
    createByName?: string
    createTime?: string
    /**
     * 页面属性配置（JSON格式）
     * 使用@TableField注解指定JSON类型处理器
     */
    dataDetail?: { [key: string]: any }
    dataStatus?: string
    /**
     * 数据类型(如小商品、skc、sku等)
     */
    dataType?: string
    id?: number
    modifyById?: number
    /**
     * 修改人名称
     */
    modifyByName?: string
    modifyTime?: string
    seq?: number
    /**
     * 版本号
     */
    versionCode?: string
    /**
     * 版本说明
     */
    versionRemark?: string
    [property: string]: any
  }
}
export function getVersionByBusinessId(data: { businessId: string; typeId: string }) {
  return to<NewResponseData<TableDetailApi.VersionResp[]>>(
    service.post(`/pdm-base/common/getVersionByBusinessId`, data)
  )
}
export function productSave(data) {
  return to<NewResponseData<TableDetailApi.VersionResp>>(
    service.post(`/pdm-base/plm/product/save`, data)
  )
}
export function productUpdate(data) {
  return to<NewResponseData<TableDetailApi.VersionResp>>(
    service.post(`/pdm-base/plm/product/update`, data)
  )
}
///multiProductBase/detail
export function getMultiProductBaseDetail(data) {
  return to<NewResponseData<AttributeData>>(service.post(`/pdm-base/multiProductBase/detail`, data))
}
///multiProductBase/update
export function multiProductBaseUpdate(data) {
  return to<NewResponseData<AttributeData>>(service.post(`/pdm-base/multiProductBase/update`, data))
}
export const getProductSkcDetails = (params: {
  productCode: string
}): Promise<NewResponseData<AttributeData>> => {
  return request.get({ url: '/pdm-base/multiProductSkc/productSkcDetails', params })
}
export const updateProductStatus = (data) => {
  return to<NewResponseData<AttributeData>>(
    service.post(`/pdm-base/plm/product/updateStatus`, data)
  )
}
export const updateSkcStatus = (data) => {
  return to<NewResponseData<AttributeData>>(
    service.post(`/pdm-base/multiProductSkc/updateSkcStatus`, data)
  )
}
export const updateSkuStatus = (data) => {
  return to<NewResponseData<AttributeData>>(
    service.post(`/pdm-base/productSkuInfo/effectiveOrInvalid`, data)
  )
}
export const copyColorByProduct = (data: { productCode: string; copyProductCode: string }) => {
  return to<NewResponseData<AttributeData[]>>(
    service.post(`/pdm-base/plm/product/copyColorByProduct`, data)
  )
}
export const convertProducts = (data) => {
  return to<NewResponseData<AttributeData>>(
    service.post(`/pdm-base//plm/product/convertProducts`, data)
  )
}
// 详情接口-通过编码查询尺码段相关信息
export function getSizeAndStandardValueByCode(params: { code: string }) {
  return request.get({ url: '/pdm-base/common/getSizeAndStandardValueByCode', params })
}
// 修改产品配色
export function updateStyleColor(data) {
  return to<NewResponseData<AttributeData>>(
    service.post(`/pdm-base/plm/product/updateStyleColor`, data)
  )
}
export function skuDetailsBySkcCode(params) {
  return request.get({ url: '/pdm-base/productSkuInfo/skuDetailsBySkcCode', params })
}
export function getHeadDetail(id: string) {
  return request.get({ url: `/pdm-base/plm/product/productDetail/${id}` })
}
export function showVersionDiffByIdList(
  data
): Promise<[Error, undefined] | [null, NewResponseData<AttributeData>]> {
  return to<NewResponseData<AttributeData>>(
    service.post(`/pdm-base/common/showVersionDiffByIdList`, data)
  )
}
export function productCopy(data) {
  return to<NewResponseData<AttributeData>>(service.post(`/pdm-base/plm/product/copy`, data))
}
export function getCopySkcDetail(productCode: string) {
  return request.get({ url: `/pdm-base/multiProductSkc/copy`, params: { productCode } })
}
export function importPackageExcel(data) {
  return to<NewResponseData<AttributeData>>(
    service.post(`/pdm-base/productSkuInfo/importPackageExcel`, data)
  )
}
export function importPackage(data) {
  return to<NewResponseData<AttributeData>>(
    service.post(`/pdm-base/productSkuInfo/importPackage`, data)
  )
}
export function importMiniProductStyle(data) {
  return to<NewResponseData<AttributeData>>(
    service.post(`/pdm-base/common/importMiniProductStyle`, data)
  )
}
export function getVersionDetail(data) {
  return to<NewResponseData<AttributeData>>(service.post(`/pdm-base/common/getVersionDetail`, data))
}
