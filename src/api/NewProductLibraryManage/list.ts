import { service } from '@/config/fetch/service'
import to from 'await-to-js'
import type { Attribute } from '@/views/NewBasicLibraryManage/components/Help/useHook'
import { AttributeData } from '@/components/PlmBase/type'

export namespace TableListApi {
  export interface QueryViewConfigurationDetailResp {
    createById?: number
    createTime?: string
    delFlag?: number
    /**
     * 字段属性(string)
     * 字段属性(string)集合
     */
    fieldAttr?: string[]
    fieldTitle?: string[]
    id: string
    modifyById?: number
    modifyTime?: string
    /**
     * 名称
     */
    name?: string
    plmFieldAttr?: JSONObject[]
    /**
     * 类型(0 就是产品库)
     */
    type?: number
    [property: string]: any
  }
  export interface JSONObject {
    key?: { [key: string]: any }
    [property: string]: any
  }
  /**
   * SaveViewConfigurationReq
   */
  export interface saveAttributeReq {
    createById?: number
    createTime?: string
    delFlag?: number
    /**
     * 字段属性(string)
     * 字段属性(string)集合
     */
    fieldAttr?: string[]
    id?: string
    modifyById?: number
    modifyTime?: string
    /**
     * 名称
     */
    name?: string
    relationDetailResps?: Attribute[]
    /**
     * 类型(0 就是产品库)
     */
    type?: number
    [property: string]: any
  }
  export interface condition {
    categoryName: string
    value: string
    searchType: string
    dataType: string
  }
  export interface searchRes {
    current: number
    size: number
    typeId: string
    type: string
    conditionList?: condition[]
  }
}
//当前登录人的所有配置
export function allViewByLogin(type: string) {
  return to<NewResponseData<TableListApi.QueryViewConfigurationDetailResp[]>>(
    service.get(`/pdm-base/common/allViewByLogin/${type}`)
  )
}
export function saveCommonViewConfiguration(data: TableListApi.saveAttributeReq) {
  return to<NewBasicResponseData>(
    service.post(`/pdm-base/common/saveCommonViewConfiguration`, data)
  )
}
export function getMultiProductBase(data: TableListApi.searchRes) {
  return to<NewPageResponseData<AttributeData[]>>(
    service.post(`/pdm-base/multiProductBase/page`, data)
  )
}
