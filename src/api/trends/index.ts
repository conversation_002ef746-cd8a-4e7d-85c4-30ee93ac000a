import request from '@/config/fetch'
import {
  SocialTrendsRequest,
  SocialTrendsResponse,
  BatchUpdateSocialRequest,
  StyleCommentResponse,
  StyleSameResponse,
  StyleSameRequest,
  VectorIdsByBizIdRequest,
  VectorIdsByBizIdResponse,
  StyleLibRequest,
  StyleLibResponse,
  BatchStyleLibRequest,
  StyleImgResponse,
  AddStyleRequest,
  SameSocialRequest,
  SameSocialResponse,
  AccountRequest,
  AccountResponse,
  AccountStatusRequest,
  AccountUpdateRequest,
  AccountInsertRequest,
  TagRequest,
  TagResponse,
  TagStatusRequest,
  TagUpdateRequest,
  TagInsertRequest,
  UpdateCollectFlagRequest
} from './types'

// 社媒款式库分页
export const getSocialTrendsApi = (data: SocialTrendsRequest): Promise<SocialTrendsResponse> => {
  return request.post({
    url: '/smt-web/styleLib/social/page',
    data
  })
}

// 批量修改社媒款式库维度数据
export const getBatchUpdateApi = (data: BatchUpdateSocialRequest): Promise<IResponse> => {
  return request.post({
    url: '/smt-web/styleLib/social/batchUpdate',
    data
  })
}

// 获取款式对应的评论词云
export const getStyleCommentWordSegment = (contentId: number): Promise<StyleCommentResponse> => {
  return request.get({ url: `/smt-web/styleLib/social/commentWordSegment/${contentId}` })
}

// 社媒款式库以图搜图
export const importViewApi = (data): Promise<SocialTrendsResponse> => {
  return request.post({ url: '/smt-web/styleLib/social/searchByImg', data })
}

// 买手款式库分页
export const getStyleLibApi = (data: StyleLibRequest): Promise<StyleLibResponse> => {
  return request.post({
    url: '/smt-web/styleLib/buyer/page',
    data
  })
}

// 批量修改买手款式库维度数据
export const getBatchStyleApi = (data: BatchStyleLibRequest): Promise<IResponse> => {
  return request.post({
    url: '/smt-web/styleLib/buyer/batchUpdate',
    data
  })
}

// 买手款式库以图搜图
export const importStyleViewApi = (data): Promise<StyleLibResponse> => {
  return request.post({ url: '/smt-web/styleLib/buyer/searchByImg', data })
}

// 根据上传的图片获取款式维度信息
export const getStyleImgApi = (data): Promise<StyleImgResponse> => {
  return request.post({ url: '/smt-web/styleLib/buyer/upload', data })
}

// 新增买手款式
export const getAddStyleApi = (data: AddStyleRequest): Promise<IResponse> => {
  return request.post({
    url: '/smt-web/styleLib/buyer/add',
    data
  })
}

// 美迈类似款查询
export const getStyleSameApi = (data: StyleSameRequest): Promise<StyleSameResponse> => {
  return request.post({
    url: '/smt-web/styleLib/mmt/query/same',
    data
  })
}

// 根据业务id获取美迈相似款的向量Id
export const getqueryVectorIdsByBizIdApi = (
  data: VectorIdsByBizIdRequest
): Promise<VectorIdsByBizIdResponse> => {
  return request.post({
    url: '/smt-web/styleLib/mmt/queryVectorIdsByBizId',
    data
  })
}

// 社媒相似款分页查询
export const getSameSocialStyleApi = (data: SameSocialRequest): Promise<SameSocialResponse> => {
  return request.post({
    url: '/smt-web/styleLib/social/query/same',
    data
  })
}

// 账号分页查询
export const getAccountApi = (data: AccountRequest): Promise<AccountResponse> => {
  return request.post({
    url: '/smt-web/account/page',
    data
  })
}

// 批量禁用、启用账号
export const AccountStatusChangeApi = (data: AccountStatusRequest): Promise<IResponse> => {
  return request.post({
    url: '/smt-web/account/statusChange',
    data
  })
}

// 批量修改账号信息
export const AccountUpdateApi = (data: AccountUpdateRequest): Promise<IResponse> => {
  return request.post({
    url: '/smt-web/account/batchUpdate',
    data
  })
}

// 批量新增社媒账号
export const AccountInsertApi = (data: AccountInsertRequest): Promise<IResponse> => {
  return request.post({
    url: '/smt-web/account/batchInsert',
    data
  })
}

// 标签分页查询
export const getTagApi = (data: TagRequest): Promise<TagResponse> => {
  return request.post({
    url: '/smt-web/tag/page',
    data
  })
}
// 批量禁用、启用标签
export const TagStatusChangeApi = (data: TagStatusRequest): Promise<IResponse> => {
  return request.post({
    url: '/smt-web/tag/statusChange',
    data
  })
}
// 批量修改标签
export const TagUpdateApi = (data: TagUpdateRequest): Promise<IResponse> => {
  return request.post({
    url: '/smt-web/tag/batchUpdate',
    data
  })
}
// 批量新增标签
export const TagInsertApi = (data: TagInsertRequest): Promise<IResponse> => {
  return request.post({
    url: '/smt-web/tag/batchInsert',
    data
  })
}

// 更改帖子是否被收藏
export const updateCollectFlagApi = (data: UpdateCollectFlagRequest): Promise<IResponse> => {
  return request.post({
    url: '/smt-web/styleLib/social/updateCollectFlag',
    data
  })
}
