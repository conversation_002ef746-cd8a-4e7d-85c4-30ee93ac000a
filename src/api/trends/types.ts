export interface SocialTrendsRequest {
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 时间段止，yyyy-MM-dd
   */
  dateEnd: string
  /**
   * 时间段起，yyyy-MM-dd
   */
  dateStart: string
  /**
   * 元素
   */
  element: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 页数
   */
  page: number
  /**
   * 分页大小
   */
  pageSize: number
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 鞋头
   */
  toeCap: string
  [property: string]: any
}

export interface SocialTrendsResponse {
  code: string
  datas: SocialTrendsDatas
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface SocialTrendsDatas {
  /**
   * 当前页
   */
  currentPage: number
  /**
   * 是否还有数据
   */
  hasNext: boolean
  /**
   * 数据
   */
  rows: SocialTrendsRow[]
  /**
   * 总条数
   */
  total: number
  /**
   * 总页数
   */
  totalPage: number
  [property: string]: any
}

export interface SocialTrendsRow {
  /**
   * 归属日期
   */
  belongedDate: string
  /**
   * 收藏数
   */
  collectsNum: number
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 评论数
   */
  commentsNum: number
  /**
   * 内容爬取时间
   */
  contentCrawledTime: string
  /**
   * 社媒内容id
   */
  contentId: number
  /**
   * 社媒图片id
   */
  contentImgId: number
  /**
   * 图片url地址
   */
  contentImgOssUrl: string
  /**
   * 内容发布账号
   */
  contentPublishAccount: string
  /**
   * 内容发布时间
   */
  contentPublishTime: string
  /**
   * 内容来源账号，当content_source_type=01时必填
   */
  contentSourceAccount: string
  /**
   * 内容来源标签，当content_source_type=02时必填
   */
  contentSourceTag: string
  /**
   * 内容来源类型，01-关注账号，02-关注标签，03-探索，04-推荐
   */
  contentSourceType: string
  /**
   * 元素
   */
  element: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 社媒款式id
   */
  id: number
  /**
   * 点赞数
   */
  likesNum: number
  /**
   * 平台id
   */
  platId: number
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 鞋头
   */
  toeCap: string
  /**
   * 转发数
   */
  transmitNum: number
  /**
   * 播放量
   */
  viewsNum: number
  [property: string]: any
}

export interface BatchUpdateSocialRequest {
  styles: BatchUpdateSocialStyle[]
  [property: string]: any
}

export interface BatchUpdateSocialStyle {
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 元素
   */
  element: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 社媒款式id
   */
  id: string
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 鞋头
   */
  toeCap: string
  [property: string]: any
}

export interface StyleCommentResponse {
  code: string
  datas: StyleCommentData[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface StyleCommentData {
  /**
   * 出现次数
   */
  qty: number
  /**
   * 分词
   */
  word: string
  [property: string]: any
}

export interface StyleSameResponse {
  code: string
  datas: StyleSameData[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface StyleSameData {
  /**
   * color
   */
  color: string
  /**
   * 图片链接
   */
  imgUrl: string
  /**
   * 库存，暂为空
   */
  inventory?: number | null
  /**
   * 销量
   */
  saleNum?: number | null
  /**
   * style
   */
  style: string
  [property: string]: any
}

export interface StyleSameRequest {
  /**
   * style图片id列表
   */
  mmtStyleVectorIds: string[]
  [property: string]: any
}

export interface VectorIdsByBizIdRequest {
  /**
   * 业务id，from=01时为社媒款式对应的图片id；from=02时为买手款式id
   */
  bizId: number
  /**
   * 业务id来源，01-社媒款式，02-买手款式
   */
  from: string
  [property: string]: any
}

export interface VectorIdsByBizIdResponse {
  code: string
  /**
   * 美迈款式id列表
   */
  datas: string[]
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface StyleLibRequest {
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 时间段止，yyyy-MM-dd
   */
  dateEnd: string
  /**
   * 时间段起，yyyy-MM-dd
   */
  dateStart: string
  /**
   * 元素
   */
  element: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 页数
   */
  page: number
  /**
   * 分页大小
   */
  pageSize: number
  /**
   * 产品大类
   */
  productLargeCategory: string
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 鞋头
   */
  toeCap: string
  [property: string]: any
}

export interface StyleLibResponse {
  code: string
  datas: StyleLibDatas
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface StyleLibDatas {
  /**
   * 当前页
   */
  currentPage: number
  /**
   * 是否还有数据
   */
  hasNext: boolean
  /**
   * 数据
   */
  rows: StyleLibRow[]
  /**
   * 总条数
   */
  total: number
  /**
   * 总页数
   */
  totalPage: number
  [property: string]: any
}

export interface StyleLibRow {
  /**
   * 品牌
   */
  brand: string
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 上传用户
   */
  createByName: string
  /**
   * 上传时间
   */
  createTime: string
  /**
   * 元素
   */
  element: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 买手款式id
   */
  id: number
  /**
   * 产品大类
   */
  productLargeCategory: string
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 款式图片地址
   */
  styleImgUrl: string
  /**
   * 鞋头
   */
  toeCap: string
  /**
   * 价格，美元
   */
  usdPrice: number
  [property: string]: any
}

export interface BatchStyleLibRequest {
  styles: BatchStyle[]
  [property: string]: any
}

export interface BatchStyle {
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 元素
   */
  element: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 社媒款式id
   */
  id: string
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 鞋头
   */
  toeCap: string
  [property: string]: any
}

export interface StyleImgResponse {
  code: string
  datas: StyleImgDatas
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface StyleImgDatas {
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 元素
   */
  element: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 款式图片地址
   */
  styleImgUrl: string
  /**
   * 鞋头
   */
  toeCap: string
  [property: string]: any
}

export interface AddStyleRequest {
  /**
   * 品牌
   */
  brand: string
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 元素
   */
  element: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 产品大类
   */
  productLargeCategory: string
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 款式图片地址
   */
  styleImgUrl: string
  /**
   * 鞋头
   */
  toeCap: string
  /**
   * 价格，美元
   */
  usdPrice: number
  [property: string]: any
}

export interface SameSocialRequest {
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 元素
   */
  element: string
  /**
   * 页数
   */
  page: number
  /**
   * 分页大小
   */
  pageSize: number
  /**
   * 产品小类
   */
  productTinyCategory: string
  [property: string]: any
}

export interface SameSocialResponse {
  code: string
  datas: SameSocialDatas
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface SameSocialDatas {
  /**
   * 当前页
   */
  currentPage: number
  /**
   * 是否还有数据
   */
  hasNext: boolean
  /**
   * 数据
   */
  rows: SameSocialRow[]
  /**
   * 总条数
   */
  total: number
  /**
   * 总页数
   */
  totalPage: number
  [property: string]: any
}

export interface SameSocialRow {
  /**
   * 归属日期
   */
  belongedDate: string
  /**
   * 收藏数
   */
  collectsNum: number
  /**
   * 色系
   */
  colorScheme: string
  /**
   * 评论数
   */
  commentsNum: number
  /**
   * 内容爬取时间
   */
  contentCrawledTime: string
  /**
   * 社媒内容id
   */
  contentId: number
  /**
   * 社媒图片id
   */
  contentImgId: number
  /**
   * 图片url地址
   */
  contentImgOssUrl: string
  /**
   * 内容发布账号
   */
  contentPublishAccount: string
  /**
   * 内容发布时间
   */
  contentPublishTime: string
  /**
   * 内容来源账号，当content_source_type=01时必填
   */
  contentSourceAccount: string
  /**
   * 内容来源标签，当content_source_type=02时必填
   */
  contentSourceTag: string
  /**
   * 内容来源类型，01-关注账号，02-关注标签，03-探索，04-推荐
   */
  contentSourceType: string
  /**
   * 元素
   */
  element: string
  /**
   * 跟型
   */
  heelType: string
  /**
   * 社媒款式id
   */
  id: number
  /**
   * 点赞数
   */
  likesNum: number
  /**
   * 平台id
   */
  platId: number
  /**
   * 产品小类
   */
  productTinyCategory: string
  /**
   * 鞋头
   */
  toeCap: string
  /**
   * 转发数
   */
  transmitNum: number
  /**
   * 播放量
   */
  viewsNum: number
  [property: string]: any
}

export interface AccountRequest {
  /**
   * 账户名
   */
  account: string
  /**
   * 账号来源，01-手动维护；02-系统抓取
   */
  accountSource: string
  /**
   * 账号类型，01-网红；02-品牌；99-其他
   */
  accountType: string
  /**
   * 生效时间止，格式：yyyy-MM-dd
   */
  enabledDateEnd: string
  /**
   * 生效时间起，格式：yyyy-MM-dd
   */
  enabledDateStart: string
  /**
   * 粉丝量级，01,10k-50k; 02,50k-100k; 03,100k-500k; 04,500k-1000k; 05, 1000k以上
   */
  fanScale: string
  /**
   * 页数
   */
  page: number
  /**
   * 分页大小
   */
  pageSize: number
  /**
   * 所属社交平台id
   */
  platId: number
  [property: string]: any
}

export interface AccountResponse {
  code: string
  datas: AccountDatas
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface AccountDatas {
  /**
   * 当前页
   */
  currentPage: number
  /**
   * 是否还有数据
   */
  hasNext: boolean
  /**
   * 数据
   */
  rows: AccountRow[]
  /**
   * 总条数
   */
  total: number
  /**
   * 总页数
   */
  totalPage: number
  [property: string]: any
}

export interface AccountRow {
  /**
   * 账户名，社交平台内唯一
   */
  account: string
  /**
   * 账号来源
   */
  accountSource: string
  /**
   * 账号类型
   */
  accountType: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 生效时间
   */
  enabledTime: string
  /**
   * 粉丝量级
   */
  fanScale: string
  /**
   * 主页地址
   */
  homePage: string
  /**
   * 账号id
   */
  id: number
  /**
   * 所属社交平台id
   */
  platId: number
  [property: string]: any
}

export interface AccountStatusRequest {
  /**
   * 账户id列表
   */
  accountIds: number[]
  /**
   * 是否启用，0，禁用；1，启用
   */
  enabled: number
  [property: string]: any
}

export interface AccountUpdateRequest {
  accounts: AccountUpdate[]
  [property: string]: any
}

export interface AccountUpdate {
  /**
   * 账号类型，01-网红；02-品牌；99-其他
   */
  accountType: string
  /**
   * 账户id
   */
  id: number
  [property: string]: any
}

export interface AccountInsertRequest {
  accounts: AccountInsert[]
  [property: string]: any
}

export interface AccountInsert {
  /**
   * 账户名，社交平台内唯一
   */
  account: string
  /**
   * 账号类型，01-网红；02-品牌；99-其他
   */
  accountType: string
  /**
   * 主页地址
   */
  homePage: string
  /**
   * 所属社交平台id
   */
  platId: number
  [property: string]: any
}

export interface TagRequest {
  /**
   * 是否启用，0-否；1-是
   */
  enabled: number
  /**
   * 生效时间止
   */
  enabledDateEnd: string
  /**
   * 生效时间起
   */
  enabledDateStart: string
  /**
   * 页数
   */
  page: number
  /**
   * 分页大小
   */
  pageSize: number
  /**
   * 所属平台id
   */
  platId: number
  /**
   * 标签
   */
  tag: string
  /**
   * 标签来源，01-系统抓取；02-手动维护
   */
  tagSource: string
  /**
   * 标签类型，01-产品；02-节日；03-行业；04-品牌；99-其他
   */
  tagType: string
  [property: string]: any
}

export interface TagResponse {
  code: string
  datas: TagDatas
  isSuccess: boolean
  msg: string
  [property: string]: any
}

export interface TagDatas {
  /**
   * 当前页
   */
  currentPage: number
  /**
   * 是否还有数据
   */
  hasNext: boolean
  /**
   * 数据
   */
  rows: TagRow[]
  /**
   * 总条数
   */
  total: number
  /**
   * 总页数
   */
  totalPage: number
  [property: string]: any
}

export interface TagRow {
  /**
   * 是否启用，0-否；1-是
   */
  enabled: string
  /**
   * 生效时间
   */
  enabledTime: string
  /**
   * 标签id
   */
  id: number
  /**
   * 所属平台id
   */
  platId: number
  /**
   * 标签
   */
  tag: string
  /**
   * 标签来源，01-系统抓取；02-手动维护
   */
  tagSource: string
  /**
   * 标签类型，01-产品；02-节日；03-行业；04-品牌；99-其他
   */
  tagType: string
  [property: string]: any
}

export interface TagStatusRequest {
  /**
   * 是否启用，0，禁用；1，启用
   */
  enabled: number
  /**
   * 标签id列表
   */
  tagIds: number[]
  [property: string]: any
}

export interface TagUpdateRequest {
  tags: TagUpdate[]
  [property: string]: any
}

export interface TagUpdate {
  /**
   * 标签id
   */
  id: string
  /**
   * 标签类型，01-产品；02-节日；03-行业；04-品牌；99-其他
   */
  tagType: string
  [property: string]: any
}

export interface TagInsertRequest {
  tags: TagInsert[]
  [property: string]: any
}

export interface TagInsert {
  /**
   * 所属平台id
   */
  platId: number
  /**
   * 标签
   */
  tag: string
  /**
   * 标签类型，01-产品；02-节日；03-行业；04-品牌；99-其他
   */
  tagType: string
  [property: string]: any
}

export interface UpdateCollectFlagRequest {
  /**
   * 收藏帖子状态
   */
  collectFlag: string
  /**
   * 款式id
   */
  id: number
  [property: string]: any
}
