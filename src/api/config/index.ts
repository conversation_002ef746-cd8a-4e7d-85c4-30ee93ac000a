import request from '@/config/fetch'
import type {
  ConfigAcceptAddressDTO,
  AddressSaveRequest,
  ConfigAcceptAddressFilterDTO,
  ConfigVendorManagerDTO,
  ConfigVendorManagerUpdateDTO,
  ConfigVendorManagerFilterDTO,
  ResultListEmployeeDTO
} from './types'

export const getAddressListApi = (
  data: ConfigAcceptAddressFilterDTO
): Promise<ResponseDataType<Array<ConfigAcceptAddressDTO>>> => {
  return request.post({
    url: '/pdm/config/address/list',
    data
  })
}

export const batchSaveAddressApi = (data: Array<AddressSaveRequest>): Promise<IResponse> => {
  return request.post({
    url: '/pdm/config/address/batchSave',
    data
  })
}

export const getVendorManagerListApi = (
  data = {}
): Promise<ResponseDataType<Array<ConfigVendorManagerDTO>>> => {
  return request.post({
    url: '/pdm/config/vendorManager/list',
    data
  })
}
export const batchSaveVendorApi = (
  data: Array<ConfigVendorManagerUpdateDTO>
): Promise<IResponse> => {
  return request.post({
    url: '/pdm/config/vendorManager/batchSave',
    data
  })
}

//  根据品牌获取对接供管下拉
export const getSupplierMangerUserApi = (
  data: ConfigVendorManagerFilterDTO
): Promise<ResultListEmployeeDTO> => {
  return request.post({
    url: '/pdm/config/vendorManager/getSupplierMangerUser',
    data
  })
}
