/**
 * ConfigAcceptAddressDTO
 */
export interface ConfigAcceptAddressDTO {
  /**
   * 收件地址
   */
  acceptAddress?: string
  /**
   * 样品寄送地：ACCEPT_CITY
   */
  cityDict?: string
  /**
   * 样品寄送地：ACCEPT_CITY
   */
  cityDict_zh?: string
  /**
   * 联系电话
   */
  concatPhone?: string
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 删除标识符
   */
  delFlag?: boolean
  /**
   * 主键
   */
  id?: number
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 状态：CONFIG_STATE
   */
  stateDict?: string
  /**
   * 状态：CONFIG_STATE
   */
  stateDict_zh?: string
  /**
   * 用户id
   */
  userId?: string
  [property: string]: any
}

/**
 * ConfigAcceptAddressUpdateDTO
 */
export interface AddressSaveRequest {
  /**
   * 收件地址
   */
  acceptAddress?: string
  /**
   * 品牌id
   */
  brandId?: number
  /**
   * 品牌名称
   */
  brandName?: string
  /**
   * 样品寄送地：ACCEPT_CITY
   */
  cityDict?: string
  /**
   * 联系电话
   */
  concatPhone?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 状态：CONFIG_STATE
   */
  stateDict?: string
  /**
   * 用户id
   */
  userId?: string
  [property: string]: any
}

/**
 * ConfigAcceptAddressFilterDTO
 */
export interface ConfigAcceptAddressFilterDTO {
  /**
   * 收件地址
   */
  acceptAddress?: string
  /**
   * 正排序字段
   */
  ascOrderBy?: string
  /**
   * 样品寄送地：ACCEPT_CITY
   */
  cityDict?: string
  /**
   * 联系电话
   */
  concatPhone?: string
  /**
   * 是否查询总数
   */
  count?: boolean
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 页码
   */
  current?: number
  /**
   * 删除标识符
   */
  delFlag?: boolean
  /**
   * 倒排序字段
   */
  descOrderBy?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 页面尺寸
   */
  size?: number

  /**
   * 状态：CONFIG_STATE
   */
  stateDict?: string
  /**
   * 用户id
   */
  userId?: string
  [property: string]: any
}

/**
 * ConfigVendorManagerDTO
 */
export interface ConfigVendorManagerDTO {
  /**
   * 地区:PDM_AREA
   */
  areaDict?: string
  /**
   * 地区:PDM_AREA
   */
  areaDict_zh?: string
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 删除标识符
   */
  delFlag?: boolean
  /**
   * 主键
   */
  id?: number
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 状态：CONFIG_STATE
   */
  stateDict?: string
  /**
   * 状态：CONFIG_STATE
   */
  stateDict_zh?: string
  /**
   * 用户id
   */
  userIds?: string
  [property: string]: any
}

/**
 * ConfigVendorManagerUpdateDTO
 */
export interface ConfigVendorManagerUpdateDTO {
  /**
   * 地区:PDM_AREA
   */
  areaDict?: string
  /**
   * 品牌id
   */
  brandId?: number
  /**
   * 品牌名称
   */
  brandName?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 状态：CONFIG_STATE
   */
  stateDict?: string
  /**
   * 用户id
   */
  userIds?: string[]
  [property: string]: any
}

/**
 * ConfigVendorManagerFilterDTO
 */
export interface ConfigVendorManagerFilterDTO {
  /**
   * 地区:PDM_AREA
   */
  areaDict?: string
  /**
   * 正排序字段
   */
  ascOrderBy?: string
  /**
   * 品牌id
   */
  brandId?: number
  /**
   * 是否查询总数
   */
  count?: boolean
  /**
   * 创建人id
   */
  createById?: number
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 页码
   */
  current?: number
  /**
   * 删除标识符
   */
  delFlag?: boolean
  /**
   * 倒排序字段
   */
  descOrderBy?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 修改人id
   */
  modifyById?: number
  /**
   * 修改时间
   */
  modifyTime?: Date
  /**
   * 页面尺寸
   */
  size?: number
  /**
   * 多个字段排序配置。
   */
  sortList?: PageSort[]
  /**
   * 状态：CONFIG_STATE
   */
  stateDict?: string
  /**
   * 用户id
   */
  userIds?: string
  [property: string]: any
}

/**
 * PageSort
 */
export interface PageSort {
  /**
   * 是否正排序？true是，false否
   */
  ascField?: boolean
  /**
   * 排序的字段
   */
  field?: string
  [property: string]: any
}

/**
 * Result«List«EmployeeDTO»»
 */
export interface ResultListEmployeeDTO {
  /**
   * 返回标记：成功标记=0，其余都是失败
   */
  code?: string
  /**
   * 数据
   */
  datas?: EmployeeDTO[]
  /**
   * 是否成功
   */
  isSuccess?: boolean
  /**
   * 返回信息
   */
  msg?: string
  [property: string]: any
}

/**
 * EmployeeDTO
 */
export interface EmployeeDTO {
  departmentId?: string
  departments?: EmployeeDepartmentDTO[]
  directManagerId?: string
  emailAddress?: string
  employeeId?: string
  employeeNumber?: string
  gender?: string
  nameCn?: string
  nameEn?: string
  userId?: string
  [property: string]: any
}

/**
 * EmployeeDepartmentDTO
 */
export interface EmployeeDepartmentDTO {
  departmentId?: string
  departmentName?: string
  [property: string]: any
}
