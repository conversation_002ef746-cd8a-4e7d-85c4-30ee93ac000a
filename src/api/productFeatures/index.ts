import request from '@/config/fetch'
import {
  ProductSellingPointAddAPI,
  ProductSellingPointDeleteAPI,
  ProductSellingPointDetailAPI,
  ProductSellingPointPageAPI,
  ProductSellingPointUpdateAPI,
  ProductSellingPointUpdateStatus
} from './types'
import to from 'await-to-js'
import { service } from '@/config/fetch/service'
import { VersionListAPI } from '@/views/basic-library-manage/api/common'

/**
 * 分页查询产品卖点
 */
export const productSellingPointPage = (
  data: ProductSellingPointPageAPI.Request,
  signal: AbortSignal
) => {
  return to<ProductSellingPointPageAPI.Response>(
    request.post({
      url: '/pdm-base/pdmProductSellPoint/getPdmProductSellPointPageList',
      data,
      signal
    })
  )
}

/**
 * 新增产品卖点
 */
export const productSellingPointAdd = (
  data: ProductSellingPointAddAPI.Request
): Promise<ProductSellingPointAddAPI.Response> => {
  return request.post({ url: '/pdm-base/product/selling/point/add', data })
}
/**
 * 修改产品卖点
 */
export const productSellingPointUpdate = (data: ProductSellingPointUpdateAPI.Request) => {
  return to<ProductSellingPointUpdateAPI.Response>(
    service.post('/pdm-base/pdmProductSellPoint/saveSellPoint', data)
  )
}
/***
 暂存产品卖点
 **/
export const productSellingPointSave = (data: ProductSellingPointUpdateAPI.Request) => {
  return to<ProductSellingPointUpdateAPI.Response>(
    service.post('/pdm-base/pdmProductSellPoint/temporarilyStoreSellPoint', data)
  )
}
/**
 * 删除产品卖点
 */
export const productSellingPointDelete = (
  params: ProductSellingPointDeleteAPI.Request
): Promise<ProductSellingPointDeleteAPI.Response> => {
  return request.get({ url: '/pdm-base/product/selling/point/delete', params })
}

/**
 * 查询产品卖点详情
 */
export function getProductSellPointDetail(data: ProductSellingPointDetailAPI.Request) {
  return to<ProductSellingPointDetailAPI.Response>(
    service.post('/pdm-base/pdmProductSellPoint/getPdmProductSellPointDetail', data)
  )
}

/**
 * 查看历史产品卖点详情
 * @param id
 */
export function getProductSellPointHistoryDetail(id: string) {
  return to<ProductSellingPointDetailAPI.Response>(
    service.get(`/pdm-base/pdmProductSellPoint/versionSellPointDetail/${id}`)
  )
}

export function getProductSellingPointVersionList(id?: number) {
  return to<VersionListAPI.Response>(
    service.get(`/pdm-base/version/history?businessId=${id}&dataType=selling_point`)
  )
}

export function changeProductSellingPointStatus(data: ProductSellingPointUpdateStatus.Request) {
  return to<ProductSellingPointUpdateStatus.Response>(
    service.post(`/pdm-base/pdmProductSellPoint/changeStatus`, data)
  )
}
