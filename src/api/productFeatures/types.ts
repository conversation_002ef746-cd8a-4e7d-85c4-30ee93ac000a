/*
 * 分页查询产品卖点
 */
import { StatusEnum } from '@/views/basic-library-manage/const'

export namespace ProductSellingPointPageAPI {
  export interface Params {
    /**
     * 品牌
     */
    brandList?: number[]
    /**
     * CT归属
     */
    combatTeam?: string
    /**
     * 产品设计师的id
     */
    designerId?: number
    /**
     * 产品企划人员id
     */
    designPersonId?: number
    /**
     * 产品系列
     */
    developmentDirection?: string
    /**
     * 创建时间结束时间
     */
    endTime?: string
    /**
     * 开发季节
     */
    launchSeason?: string
    /**
     * 产品类目,最低等级id
     */
    productCategory?: string
    /**
     * 商品上市季节
     */
    productLaunchSeason?: string
    /**
     * 产品编号
     */
    productNumberList?: string[]
    /**
     * 产品目标定级
     */
    productPositioning?: string
    /**
     * 产品风格
     */
    productStyle?: string
    /**
     * 状态
     */
    sellPointStatus?: string[]
    /**
     * product name
     */
    shoeName?: string
    /**
     * 创建时间开始时间
     */
    startTime?: string
    /**
     * style编号
     */
    styleNumber?: string
    /**
     * style WMS
     */
    styleWms?: string
    /**
     * 使用人群
     */
    targetAudience?: string
    /**
     * 楦头标准
     */
    toeStandard?: string
  }
  export interface List {
    /**
     * 品牌名称
     */
    brandName?: string
    /**
     * 舒适性度
     */
    comfortLevel?: string
    /**
     * 创建人
     */
    createBy?: string
    /**
     * 创建时间
     */
    createTime?: string
    id?: number
    /**
     * 产品类目,最低等级id
     */
    productCategoryItemName?: string
    /**
     * 产品id
     */
    productId?: number
    /**
     * 产品上市季节
     */
    productLaunchSeason?: string
    /**
     * 产品编号
     */
    productNumber?: string
    /**
     * 产品状态
     */
    productDataStatus?: string
    /**
     * 产品风格
     */
    productStyle?: string
    /**
     * 功能特性Alpha参考
     */
    sceneAlphaPresentReference?: string
    /**
     * 场景穿搭
     */
    sceneDressing?: string
    /**
     * 卖点特性
     */
    sellPointCharacteristic?: string
    /**
     * 卖点编号
     */
    sellPointNumber?: string
    /**
     * 其他卖点
     */
    sellPointOther?: string
    /**
     * 卖点优先级
     */
    sellPointPriority?: string
    /**
     * 状态
     */
    sellPointStatus?: string
    /**
     * PRODUCT NAME
     */
    shoeName?: string
    /**
     * Style（WMS）
     */
    styleWms?: string
    /**
     * 缩略图
     */
    thumbnail?: BaseFileDTO
    /**
     * 趋势元素
     */
    trendElement?: string
    /**
     * 有效SKC数量
     */
    validSkcCount?: number
  }
  export type Request = Params & PageParams
  export type Response = PagedResponseData<List>
}
/**
 * 新增产品卖点
 */
export namespace ProductSellingPointAddAPI {
  export interface Params {
    /**
     * 品牌ID
     */
    brandId?: number
    /**
     * 品牌名称
     */
    brandName?: string
    errorList?: string[]
    /**
     * 文件列表
     */
    fileDTOList?: BaseFileDTO[]
    /**
     * 文件说明
     */
    fileInstruction?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 上市季节
     */
    saleSeason?: string
  }
  export type Request = Params
  export type Response = BasicResponseData
}
/**
 * 修改产品卖点
 */
export namespace ProductSellingPointUpdateAPI {
  export interface Params {
    id: number
    /**
     * 品牌ID
     */
    brandId?: number
    /**
     * 品牌名称
     */
    brandName?: string
    errorList?: string[]
    /**
     * 文件列表
     */
    fileDTOList?: BaseFileDTO[]
    /**
     * 文件说明
     */
    fileInstruction?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 上市季节
     */
    saleSeason?: string
  }
  export type Request = Partial<ProductSellingPointDetailAPI.QueryPdmSellPointDetailResp>
  export type Response = BasicResponseData
}
/**
 * 删除
 */
export namespace ProductSellingPointDeleteAPI {
  export interface Params {
    id: number
  }
  export type Request = Params
  export type Response = BasicResponseData
}

/**
 * 查询产品卖点详情
 */
export namespace ProductSellingPointDetailAPI {
  export interface Params {
    id?: number
    productNumber?: string
  }
  export type Request = Params
  export interface QueryPdmSellPointDetailResp {
    id?: number
    /**
     * 产品信息
     */
    pdmSellPointProductInfoDTO?: QueryPdmSellPointProductDTO
    productNumber?: string
    /**
     * 卖点基本信息
     */
    sellPointBaseInfoDTO: QuerySellPointBaseInfoDTO
    /**
     * 卖点舒适性
     */
    sellPointComfortInfoDTO: QuerySellPointComfortDTO
    /**
     * 卖点其他
     */
    sellPointOtherInfoDTO: QuerySellPointOtherDTO
    /**
     * 卖点产品技术参数
     */
    sellPointProductTechnicalParameterDTO: QuerySellPointProductTechnicalParameterDTO
    /**
     * 卖点场景
     */
    sellPointSceneInfoDTO: QuerySellPointSceneDTO
    /**
     * 卖点趋势
     */
    sellPointTrendInfoDTO: QuerySellPointTrendDTO
    /**
     * 功能/特性
     */
    sellPointFunctionDTO: QuerySellPointFunctionDTO
  }

  /**
   * 产品信息
   *
   * QueryPdmSellPointProductDTO
   */
  export interface QueryPdmSellPointProductDTO {
    /**
     * 适用季节
     */
    applicableSeason?: string
    /**
     * 大货供应商
     */
    assignedFactory?: string
    brandItemName?: string
    /**
     * 选品渠道
     */
    chooseChannel?: string
    /**
     * CT归属
     */
    combatTeam?: string
    /**
     * 产品设计师
     */
    designerIdItemName?: string
    /**
     * 产品企划
     */
    designPersonIdItemName?: string
    designUrl?: BaseFileDTO[]
    /**
     * 开发渠道
     */
    developmentChannel?: string
    /**
     * 开发策略
     */
    developmentStrategy?: string
    /**
     * 开发类型
     */
    developmentType?: string
    /**
     * 楦型标准
     */
    lastsStandard?: string
    launchSeason?: string
    /**
     * 主渠道标识
     */
    mainChannelMark?: string
    productActualPosition?: string
    productCategoryItemName?: string
    /**
     * 商品上市季节
     */
    productLaunchSeason?: string
    productNumber?: string
    productPositioning?: string
    productType?: string
    /**
     * 吊牌价
     */
    retailPrice?: number
    /**
     * 款式定位
     */
    stylePositioning?: string
    styleWms?: string
    targetAudience?: string
    /**
     * 楦头标准
     */
    toeStandard?: string
    /**
     * VOC原款款号
     */
    vocOriginalStyleNumber?: string
  }

  /**
   * 卖点基本信息
   *
   * QuerySellPointBaseInfoDTO
   */
  export interface QuerySellPointBaseInfoDTO {
    /**
     * 对标竞品
     */
    benchmarking?: string
    /**
     * 竞品款式图
     */
    benchmarkingStyleImage?: BaseFileDTO[]
    /**
     * 产品风格
     */
    productStyle?: string
    sellPointNumber?: string
    /**
     * 系列名
     * **/
    sellPointSeries?: string
    /**
     * 产品摘要
     */
    productSummary?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 卖点优势
     */
    sellPointAdvantage?: string
    /**
     * 卖点特性
     */
    sellPointCharacteristic?: string
    /**
     * 卖点证据
     */
    sellPointEvidence?: string
    /**
     * 卖点兴趣
     */
    sellPointInterest?: string
    /**
     * 卖点优先级
     */
    sellPointPriority?: string
    /**
     *
     */
    sellPointStatus?: StatusEnum
  }

  /**
   * 卖点舒适性
   *
   * QuerySellPointComfortDTO
   */
  export interface QuerySellPointComfortDTO {
    /**
     * 舒适度A+呈现
     */
    comfortAlphaPresentReference?: string
    /**
     * 舒适度站外呈现
     */
    comfortExternalPresentReference?: string
    /**
     * 舒适度
     */
    comfortLevel?: string
    /**
     * 舒适度说明
     */
    comfortLevelDesc?: string
    comfortLevelFile?: BaseFileDTO[]
    comfortLevelImage?: BaseFileDTO[]
    comfortLevelVideo?: BaseFileDTO[]
  }

  /**
   * 卖点其他
   *
   * QuerySellPointOtherDTO
   */
  export interface QuerySellPointOtherDTO {
    /**
     * 其他卖点A+呈现参考
     */
    otherAlphaPresentReference?: string
    /**
     * 其他卖点站外呈现参考
     */
    otherExternalPresentReference?: string
    /**
     * 其他卖点
     */
    sellPointOther?: string
    /**
     * 其他卖点说明
     */
    sellPointOtherDesc?: string
    sellPointOtherFile?: BaseFileDTO[]
    sellPointOtherImage?: BaseFileDTO[]
    sellPointOtherVideo?: BaseFileDTO[]
  }

  /**
   * 卖点产品技术参数
   *
   * QuerySellPointProductTechnicalParameterDTO
   */
  export interface QuerySellPointProductTechnicalParameterDTO {
    /**
     * 筒围
     */
    cylinderCircumference?: number
    /**
     * 筒高
     */
    cylinderHeight?: number
    /**
     * 跟高
     */
    heelHeight?: string
    /**
     * 检测报告
     */
    testReport?: BaseFileDTO[]
  }

  /**
   * 卖点场景
   *
   * QuerySellPointSceneDTO
   */
  export interface QuerySellPointSceneDTO {
    /**
     * 场景Alpha参考
     */
    sceneAlphaPresentReference?: string
    /**
     * 场景穿搭
     */
    sceneDressing?: string
    /**
     * 场景穿搭描述
     */
    sceneDressingDesc?: string
    /**
     * 站外呈现参考
     */
    sceneExternalPresentReference?: string
    /**
     * 功能特性文件
     */
    sceneFunctionFile?: BaseFileDTO[]
    /**
     * 功能特性图片
     */
    sceneFunctionImage?: BaseFileDTO[]
    /**
     * 功能特性视频
     */
    sceneFunctionVideo?: BaseFileDTO[]
  }

  export interface QuerySellPointFunctionDTO {
    functionCharacteristics?: string
    functionCharacteristicsAlphaPresentReference?: string
    functionCharacteristicsDesc?: string
    functionCharacteristicsExternalPresentReference?: string
    /**
     * 功能特性文件
     */
    sellPointFunctionFile?: BaseFileDTO[]
    /**
     * 功能特性图片
     */
    sellPointFunctionImage?: BaseFileDTO[]
    /**
     * 功能特性视频
     */
    sellPointFunctionVideo?: BaseFileDTO[]
  }

  /**
   * 卖点趋势
   *
   * QuerySellPointTrendDTO
   */
  export interface QuerySellPointTrendDTO {
    /**
     * 趋势元素A+呈现
     */
    trendAlphaPresentReference?: string
    /**
     * 趋势元素
     */
    trendElement?: string
    /**
     * 趋势元素说明
     */
    trendElementDesc?: string
    /**
     * 趋势元素文件
     */
    trendElementFile?: BaseFileDTO[]
    /**
     * 趋势元素图片
     */
    trendElementImage?: BaseFileDTO[]
    /**
     * 趋势元素视频
     */
    trendElementVideo?: BaseFileDTO[]
    /**
     * 趋势元素站外呈现
     */
    trendExternalPresentReference?: string
  }

  export type Response = ResponseData<QueryPdmSellPointDetailResp>
}

/**
 * 修改产品卖点状态
 */
export namespace ProductSellingPointUpdateStatus {
  export interface Request {
    /**
     * 状态
     */
    sellPointStatus?: string
    /**
     * 卖点id
     */
    id?: number
    productNumber?: string
  }

  export type Response = BasicResponseData
}
