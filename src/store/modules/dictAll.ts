import { defineStore } from 'pinia'
import { GetDictAllAPI } from '@/api/common/type'
import { store } from '@/store'
import { LocaleEnum, useLocaleStore } from '@/store/modules/locale'
import { DictKey } from '@/hooks/autoImport/useDictAll'

export interface DictState {
  isSetDict: boolean
  dictObj: GetDictAllAPI.Data[]
}

const localeStore = useLocaleStore()

export const useAllDictStore = defineStore('dictAll', {
  state: (): DictState => ({
    isSetDict: false,
    dictObj: []
  }),
  getters: {
    getDictObj(): GetDictAllAPI.Data[] {
      return this.dictObj
    },
    getIsSetDict(): boolean {
      return this.isSetDict
    }
  },
  actions: {
    setDictObj(dictObj: GetDictAllAPI.Data[]) {
      this.dictObj = dictObj
    },
    setIsSetDict(isSetDict: boolean) {
      this.isSetDict = isSetDict
    },
    dictToOptions(dictType: DictKey) {
      const find = this.getDictObj.find((item) => item.dictItem === dictType)
      if (!find || !Array.isArray(find.dictValueList)) {
        return []
      }
      const locale = localeStore.currentLocale.lang
      return find.dictValueList.map((item) => {
        return {
          value: item.dictValue as string,
          label: LocaleEnum.ZH_CN === locale ? item.dictCnName : item.dictEnName
        }
      })
    },
    valueToLabel(dictType: DictKey, dictValue?: string) {
      const findDict = this.getDictObj.find((item) => item.dictItem === dictType)
      if (!findDict || !Array.isArray(findDict.dictValueList)) {
        return dictValue || ''
      }
      const locale = localeStore.currentLocale.lang
      const findKey = findDict.dictValueList.find((item) => {
        return item.dictValue === dictValue
      })
      if (!findKey) {
        return dictValue || ''
      }
      return LocaleEnum.ZH_CN === locale ? findKey.dictCnName : findKey.dictEnName
    }
  }
})

export const useDictAllStoreWithOut = () => {
  return useAllDictStore(store)
}
