import { defineStore } from 'pinia'
import { clientLogout, getClientUser } from '@/api/common'
import type { UserDataType } from '@/types/user'
import { store } from '..'
import { useCache } from '@/hooks/web/useCache'
import { removeToken } from '@/utils/auth'
import { useTagsViewStore } from './tagsView'
import { resetRouter } from '@/router'

const { wsCache } = useCache()
const tagsViewStore = useTagsViewStore()

export const useUserStore = defineStore('user', {
  state: (): UserDataType => {
    return {
      userId: '',
      name: '',
      avatarUrl: '',
      email: ''
    }
  },
  getters: {
    getUserName(): string {
      return this.name
    },
    getUserAvatarUrl(): string {
      return this.avatarUrl
    },
    getUserId(): string {
      return this.userId
    }
  },
  actions: {
    removeUserInfo() {
      this.$state = {
        userId: '',
        name: '',
        avatarUrl: '',
        email: ''
      }
    },
    async getClientUserInfo() {
      const { datas: res } = await getClientUser()
      // 区分重定向和获取到用户信息
      if (res && res.userId) {
        this.$state = res
        // window._paq.push(['setUserId', res.userId])
        res.name && window._paq?.push(['setUserId', res.name]) // 埋点-名字
        return true
      }
      return false
    },
    async logout() {
      const { datas: res } = await clientLogout()
      if (res.loginPage) {
        this.removeUserInfo()
        wsCache.clear()
        removeToken()
        tagsViewStore.delAllViews()
        resetRouter()
        location.replace(res.loginPage)
      }
    }
  }
})
export const useUserStoreWithOut = () => {
  return useUserStore(store)
}
