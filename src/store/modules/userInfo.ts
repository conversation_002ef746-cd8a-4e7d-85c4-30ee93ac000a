import { defineStore } from 'pinia'
import { CurrentUserAPI } from '@/api/common/type'
import { getUserInfo } from '@/api/common'

export const useUserInfoStore = defineStore('userInfoStore', {
  state: (): CurrentUserAPI.UserCacheDto => ({}),
  getters: {
    userInfo: (state): CurrentUserAPI.UserCacheDto => {
      if (!state['id']) {
        getUserInfo().then(([error, result]) => {
          if (error === null && result?.datas) {
            Object.keys(result.datas).forEach((key) => {
              state[key] = result.datas[key]
            })
          }
        })
        return {}
      }
      return state
    }
  }
})
