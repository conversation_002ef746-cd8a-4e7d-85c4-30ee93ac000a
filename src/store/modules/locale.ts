import { defineStore } from 'pinia'
import { store } from '@/store'
import elZhCn from 'element-plus/es/locale/lang/zh-cn'
import elEn from 'element-plus/es/locale/lang/en'
import vxeZhCN from 'vxe-table/lib/locale/lang/zh-CN'
import vxeEnUS from 'vxe-table/lib/locale/lang/en-US'
import { useCache } from '@/hooks/web/useCache'
import { LocaleDropdownType } from '@/types/localeDropdown'

const { wsCache } = useCache()

export enum LocaleEnum {
  ZH_CN = 'zh-CN',
  EN_US = 'en-US'
}

const elLocaleMap = new Map([
  [LocaleEnum.ZH_CN, elZhCn],
  [LocaleEnum.EN_US, elEn]
])

export const vxeLocaleMap = new Map([
  [LocaleEnum.ZH_CN, vxeZhCN],
  [LocaleEnum.EN_US, vxeEnUS]
])

interface LocaleState {
  currentLocale: LocaleDropdownType
  localeMap: LocaleDropdownType[]
}

export const useLocaleStore = defineStore('locales', {
  state: (): LocaleState => {
    return {
      currentLocale: {
        lang: wsCache.get('lang') || LocaleEnum.ZH_CN,
        elLocale: elLocaleMap.get(wsCache.get('lang') || LocaleEnum.ZH_CN)
      },
      // 多语言
      localeMap: [
        {
          lang: LocaleEnum.ZH_CN,
          name: '简体中文'
        },
        {
          lang: LocaleEnum.EN_US,
          name: 'English'
        }
      ]
    }
  },
  getters: {
    getCurrentLocale(): LocaleDropdownType {
      return this.currentLocale
    },
    getLocaleMap(): LocaleDropdownType[] {
      return this.localeMap
    }
  },
  actions: {
    setCurrentLocale(localeMap: LocaleDropdownType) {
      // this.locale = Object.assign(this.locale, localeMap)
      this.currentLocale.lang = localeMap?.lang
      this.currentLocale.elLocale = elLocaleMap.get(localeMap?.lang)
      wsCache.set('lang', localeMap?.lang)
    }
  }
})

export const useLocaleStoreWithOut = () => {
  return useLocaleStore(store)
}
