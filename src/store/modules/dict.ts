import { defineStore } from 'pinia'
import { store } from '../index'
import { useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()

export interface DictState {
  isSetDict: boolean
  dictObj: Recordable
}

export interface DictStateArray {
  label: string
  value: string | number
}
export const useDictStore = defineStore('dict', {
  state: (): DictState => ({
    isSetDict: false,
    dictObj: {}
  }),
  getters: {
    getDictObj(): Recordable {
      return this.dictObj
    },
    getIsSetDict(): boolean {
      return this.isSetDict
    }
  },
  actions: {
    setDictObj(dictObj: Recordable) {
      this.dictObj = dictObj
    },
    setIsSetDict(isSetDict: boolean) {
      this.isSetDict = isSetDict
    },
    getDictionariesOpt(arr: any): DictStateArray[] {
      if (!arr) return []
      const LangObj = {
        'zh-CN': 'zh-CN',
        en: 'en-US'
      }
      const langKey = LangObj[wsCache.get('lang')]
      const Arr: DictStateArray[] = []
      arr.map((v) => {
        if (v.dictName) Arr.push({ label: JSON.parse(v.dictName)[langKey], value: v.dictValue })
      })
      return Arr
    }
  }
})

export const useDictStoreWithOut = () => {
  return useDictStore(store)
}
