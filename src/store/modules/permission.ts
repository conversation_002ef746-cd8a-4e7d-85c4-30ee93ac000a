import { defineStore } from 'pinia'
import { asyncRouterMap, constantRouterMap } from '@/router'
import { flatMultiLevelRoutes, generateRoutesFn1, generateRoutesFn2 } from '@/utils/routerHelper'
import { store } from '@/store'
import { cloneDeep } from 'lodash-es'
import { UserResAPI } from '@/api/common/type'
import { userRes } from '@/api/common'

export interface PermissionState {
  routers: AppRouteRecordRaw[]
  addRouters: AppRouteRecordRaw[]
  isAddRouters: boolean
  menuTabRouters: AppRouteRecordRaw[]
  permissions: (string | void)[]
  resList: UserResAPI.Res[]
  skipAuth: boolean
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    routers: [],
    addRouters: [],
    isAddRouters: false,
    menuTabRouters: [],
    permissions: [],
    resList: [],
    skipAuth: false
  }),
  getters: {
    getRouters(): AppRouteRecordRaw[] {
      return this.routers
    },
    getAddRouters(): AppRouteRecordRaw[] {
      return flatMultiLevelRoutes(cloneDeep(this.addRouters))
    },
    getIsAddRouters(): boolean {
      return this.isAddRouters
    },
    getMenuTabRouters(): AppRouteRecordRaw[] {
      return this.menuTabRouters
    },
    getPermissons(): (string | void)[] {
      return this.permissions
    },
    getResList(): UserResAPI.Res[] {
      return this.resList
    },
    getSkipAuth(): boolean {
      return this.skipAuth
    }
  },
  actions: {
    generateRoutes(
      type: 'admin' | 'test' | 'none',
      routers?: AppCustomRouteRecordRaw[] | string[]
    ): Promise<unknown> {
      return new Promise<void>((resolve) => {
        let routerMap: AppRouteRecordRaw[] = []
        if (type === 'admin') {
          // 模拟后端过滤菜单
          routerMap = generateRoutesFn2(routers as AppCustomRouteRecordRaw[])
        } else if (type === 'test') {
          // 模拟前端过滤菜单
          routerMap = generateRoutesFn1(cloneDeep(asyncRouterMap), routers as string[])
        } else {
          // 直接读取静态路由表
          routerMap = cloneDeep(asyncRouterMap)
        }
        // 动态路由，404一定要放到最后面
        this.addRouters = routerMap.concat([
          {
            path: '/:path(.*)*',
            redirect: '/404',
            name: '404Page',
            meta: {
              hidden: true,
              breadcrumb: false
            }
          }
        ])
        // 渲染菜单的所有路由
        this.routers = cloneDeep(constantRouterMap).concat(routerMap)
        resolve()
      })
    },
    setIsAddRouters(state: boolean): void {
      this.isAddRouters = state
    },
    setMenuTabRouters(routers: AppRouteRecordRaw[]): void {
      this.menuTabRouters = routers
    },
    setPermissions(list: Array<string | void>): void {
      this.permissions = list
    },
    setResList(id: number): Promise<UserResAPI.Res[]> {
      return new Promise(async (resolve) => {
        const { res, skipAuth } = await userRes(id)
        this.resList = res
        this.skipAuth = skipAuth
        return resolve(res)
      })
    }
  }
})

export const usePermissionStoreWithOut = () => {
  return usePermissionStore(store)
}
