// 导入全局的svg图标
import '@/plugins/svgIcon'

// 初始化多语言
import { setupI18n } from '@/hooks/web/useI18n'

// 引入状态管理
import { setupStore } from '@/store'

// 引入element-plus
import { setupElementPlus } from '@/plugins/elementPlus'
import { Cascader } from '@/components/Cascader'

// 引入Vxe-table
import { setupVuxTable } from '@/plugins/vxeTable'
import { setupMatomo } from '@/plugins/VueMatomo'
import { setupSentry } from '@/plugins/sentry'

// 引入全局样式
import '@/styles/index.less'

// 引入动画
import '@/plugins/animate.css'

// 路由
import router, { setupRouter } from './router'

// 权限
import { setupPermission } from './directives'

import { createApp } from 'vue'

import App from './App.vue'

import './permission'

// 创建实例
const setupAll = async () => {
  const app = createApp(App)

  setupI18n(app)

  setupStore(app)

  setupElementPlus(app)

  setupRouter(app)

  setupPermission(app)

  await setupVuxTable(app)

  setupSentry(app)

  setupMatomo(app, router)

  app.component('ElCascader', Cascader).mount('#app')
}

setupAll()
