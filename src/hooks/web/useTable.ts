import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, nextTick, reactive, ref, unref, watch } from 'vue'
import { cloneDeep, get } from 'lodash-es'
import type { TableProps } from '@/components/Table/src/types'
import { useI18n } from '@/hooks/web/useI18n'
import { VXETable } from 'vxe-table'
import { TableExpose } from '@/components/Table'
// import { TableSetPropsType } from '@/types/table'

const { t } = useI18n()

interface UseTableConfig<T = any> {
  getListApi: (option: any) => Promise<T>
  delListApi?: (option: any) => Promise<IResponse>
  disListApi?: (option: any) => Promise<IResponse>
  // 接口返回数据格式配置
  response?: {
    list?: string
    total?: string
  }
  // 接口传参(除搜索参数以外的其他必填参数)
  params?: {
    [propName: string]: any
  }
  // 不分页
  noPaging?: boolean
  props?: TableProps
  processedParameter?: {
    timeField?: {
      [propName: string]: string[]
    }
    inputRange?: {
      [propName: string]: string[]
    }
    lastCategoryId?: string[]
  }
}

interface TableObject<T = any> {
  size: number
  current: number
  total: number
  tableList: T[]
  params: any
  loading: boolean
  currentRow: Nullable<T>
}

export const useTable = <T = any>(config?: UseTableConfig<T>) => {
  const tableObject = reactive<TableObject<T>>({
    // 页数
    size: 10,
    // 当前页
    current: 1,
    // 总条数
    total: 10,
    // 表格数据
    tableList: [],
    // AxiosConfig 配置
    params: {},
    // 加载中
    loading: true,
    // 当前行的数据
    currentRow: null
  })

  const noPaging = config?.noPaging || false
  const paramsObj = computed(() => {
    return noPaging
      ? {
          ...config?.params,
          ...tableObject.params
        }
      : {
          ...config?.params,
          ...tableObject.params,
          size: tableObject.size,
          current: tableObject.current
        }
  })

  watch(
    () => tableObject.current,
    () => {
      methods.getList()
    }
  )

  watch(
    () => tableObject.size,
    () => {
      // 当前页不为1时，修改页数后会导致多次调用getList方法
      if (tableObject.current === 1) {
        methods.getList()
      } else {
        tableObject.current = 1
        methods.getList()
      }
    }
  )

  // Table实例
  const tableRef = ref<typeof VXETable & TableExpose>()

  // ElTable实例
  const elTableRef = ref<ComponentRef<typeof VXETable>>()

  const register = (ref: typeof VXETable & TableExpose, elRef: ComponentRef<typeof VXETable>) => {
    tableRef.value = ref
    elTableRef.value = unref(elRef)
  }

  const getTable = async () => {
    await nextTick()
    const table = unref(tableRef)
    if (!table) {
      console.error('The table is not registered. Please use the register method to register')
    }
    return table
  }
  const handleParams = (data) => {
    const newData = cloneDeep(data)
    // 时间范围查询参数，原数据格式: [开始时间，结束时间]
    if (config?.processedParameter?.timeField) {
      const keys = Object.keys(config?.processedParameter?.timeField)

      const values = Object.values(config?.processedParameter?.timeField)

      keys.forEach((v, idx) => {
        const [start, end] = values[idx]
        ;[newData[start], newData[end]] = newData[v] || ['', '']
        if (values[idx].indexOf(v) === -1) {
          Reflect.deleteProperty(newData, v)
        }
      })
    }
    if (config?.processedParameter?.inputRange) {
      const keys = Object.keys(config?.processedParameter?.inputRange)
      const values = Object.values(config?.processedParameter?.inputRange)
      keys.forEach((v, idx) => {
        const [start, end] = values[idx]
        ;[newData[start], newData[end]] = newData[v] || ['', '']
        if (values[idx].indexOf(v) === -1) {
          Reflect.deleteProperty(newData, v)
        }
      })
    }
    if (config?.processedParameter?.lastCategoryId) {
      const keys = config?.processedParameter?.lastCategoryId
      // 取最后一个id
      keys.forEach((v) => {
        newData[v] = newData[v] && newData[v].map((ele) => ele[ele.length - 1])
      })
    }
    return newData
  }
  const delData = async (ids: string[] | number[], callback?: any) => {
    const res = await (config?.delListApi && config?.delListApi(ids))
    if (res) {
      ElMessage.success(t('common.delSuccess'))
      // 计算出临界点
      const pageNum =
        tableObject.total % tableObject.size === ids.length || tableObject.size === 1
          ? tableObject.current > 1
            ? tableObject.current - 1
            : tableObject.current
          : tableObject.current

      tableObject.current = pageNum
      methods.getList()
      callback && callback()
    }
  }
  const disData = async (ids: string[] | number[]) => {
    const res = await (config?.disListApi && config?.disListApi(ids))
    if (res) {
      ElMessage.success('操作成功')
      // 计算出临界点
      const pageNum =
        tableObject.total % tableObject.size === ids.length || tableObject.size === 1
          ? tableObject.current > 1
            ? tableObject.current - 1
            : tableObject.current
          : tableObject.current

      tableObject.current = pageNum
      methods.getList()
    }
  }
  const clearSelections = async () => {
    const table = await getTable()
    table?.selectionChange([])
  }
  const methods = {
    getList: async (params: object | void) => {
      tableObject.loading = true
      const res = await config?.getListApi({ ...unref(paramsObj), ...params }).finally(() => {
        tableObject.loading = false
      })

      if (res) {
        if (noPaging) {
          tableObject.tableList =
            get(res || {}, (config?.response?.list as string) || 'datas.records') || []
          tableObject.total =
            get(res || {}, (config?.response?.total as string) || 'datas.pager.total') || 0
        } else {
          tableObject.tableList =
            get(res || {}, (config?.response?.list as string) || 'datas.records') || []
          tableObject.total =
            get(res || {}, (config?.response?.total as string) || 'datas.pager.total') || 0
        }
      }

      await clearSelections()
    },
    updateList: (list) => {
      tableObject.tableList = [...list, ...tableObject.tableList]
    },
    setProps: async (props: TableProps = {}) => {
      const table = await getTable()
      table?.setProps(props)
    },
    getSelected: async () => {
      const table = await getTable()
      return table?.selected.row || {}
    },
    getSelections: async () => {
      const table = await getTable()
      return (table?.selections.records || []) as T[]
    },

    // 与Search组件结合
    setSearchParams: (data: Recordable, isGetList = true) => {
      // 重新处理data
      if (config?.processedParameter) {
        data = handleParams(data)
      }
      tableObject.current = 1
      tableObject.params = Object.assign(
        tableObject.params,
        noPaging
          ? {
              ...data
            }
          : {
              size: tableObject.size,
              current: tableObject.current,
              ...data
            }
      )
      isGetList && methods.getList()
    },
    // 删除数据
    delList: async (ids: number[] | any, multiple: boolean, message = true, callback?: any) => {
      const tableRef = await getTable()
      if (multiple) {
        if (!tableRef?.selections.records?.length) {
          ElMessage.warning(t('common.delNoData'))
          return
        }
      } else {
        if (!tableObject.currentRow) {
          ElMessage.warning(t('common.delNoData'))
          return
        }
      }
      if (message) {
        const msg = multiple
          ? `您已选择了「${tableRef?.selections.records?.length}」条记录操作删除，请确认`
          : t('common.delMessage')
        ElMessageBox.confirm(msg, t('common.delWarning'), {
          confirmButtonText: t('common.delOk'),
          cancelButtonText: t('common.delCancel'),
          type: 'warning'
        }).then(async () => {
          await delData(ids, callback)
        })
      } else {
        await delData(ids)
      }
    },
    getSearchParams: (data) => {
      if (config?.processedParameter) {
        data = handleParams(data)
      }

      return Object.assign(tableObject.params, data)
    },
    // 启用禁用方法
    disableList: async (ids: number[] | any, multiple: boolean, message = true) => {
      const tableRef = await getTable()
      if (multiple) {
        if (!tableRef?.selections.records?.length) {
          return ElMessage.warning('请选择需要操作的数据')
        }
      } else {
        if (!tableObject.currentRow) {
          return ElMessage.warning('请选择需要操作的数据')
        }
      }
      if (message) {
        const msg = multiple
          ? `您已选择了「${tableRef?.selections.records?.length}」条记录操作启用/禁用，请确认`
          : '该操作将启用/禁用该数据，请确认'
        ElMessageBox.confirm(msg, t('common.delWarning'), {
          confirmButtonText: t('common.delOk'),
          cancelButtonText: t('common.delCancel'),
          type: 'warning'
        }).then(async () => {
          await disData(ids)
        })
      } else {
        await disData(ids)
      }
    }
  }

  config?.props && methods.setProps(config.props)

  return {
    register,
    elTableRef,
    tableObject,
    methods
  }
}
