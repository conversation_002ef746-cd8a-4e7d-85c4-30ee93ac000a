interface Options {
  label: string | number
  value: string
}

export interface Result {
  map: Record<string, string | number>
  list: Options[]
}

/**
 * 生成常量的函数
 * @param {Record<string, string | number>[] | Record<string, string | number>} staticData 数据源，类型为数组或对象
 * @param {Options} [options]
 * @return {Result}
 * 一个包含枚举和映射的对象，包含list/map和enums两个属性，list属性是一个包含label和value的对象数组，map属性是一个键值对集合，键是value值，值是label值
 */
export const useConstants = (
  staticData: Record<string, string | number | undefined>[] | Record<string, string | number>,
  options: Options = { label: 'label', value: 'value' }
): Result => {
  if (!staticData) {
    return { map: {}, list: [] }
  }

  if (Array.isArray(staticData)) {
    // 根据list生成map, 以及枚举
    return staticData.reduce(
      (result, item) => {
        if (item[options.value]) {
          result.map[item[options.value]!] = item[options.label] || ''
        }
        return result
      },
      { map: {}, list: [] } as Result
    )
  }

  if (typeof staticData === 'object') {
    // 根据map生成下拉框数据, 以及枚举
    return Object.keys(staticData).reduce(
      (result, key) => {
        result.list.push({
          label: staticData[key],
          value: key
        })
        return result
      },
      { map: {}, list: [] } as Result
    )
  }

  return { map: {}, list: [] }
}
