import { reactive } from 'vue'
import { eachTree, filter, treeMap } from '@/utils/tree'
import { findIndex } from '@/utils'
import { useI18n } from '@/hooks/web/useI18n'
// import type { AxiosPromise } from 'axios'
import { FormSchema } from '@/types/form'
import { TableColumn } from '@/types/table'
import { DescriptionsSchema } from '@/types/descriptions'
import { useDictStoreWithOut } from '@/store/modules/dict'
import { isArray } from '@/utils/is'

const dictStore = useDictStoreWithOut()

export type CrudSchema = Omit<TableColumn, 'children'> & {
  search?: CrudSearchParams
  table?: CrudTableParams
  form?: CrudFormParams
  detail?: CrudDescriptionsParams
  children?: CrudSchema[]
  export?: CrudExportParams
}

type CrudExportParams = {
  // 是否显示在导出列表
  show?: boolean
}

type CrudSearchParams = {
  // 是否显示在查询项
  show?: boolean
  // 搜索项排序
  index?: number
  // 字典名称，会去取全局的字典
  dictName?: string
  field?: string
  // 接口
  api?: () => Promise<any>
} & Omit<FormSchema, 'field'>

type CrudTableParams = {
  // 是否显示表头
  show?: boolean
} & Omit<FormSchema, 'field'>

type CrudFormParams = {
  // 字典名称，会去取全局的字典
  dictName?: string
  // 接口
  api?: () => Promise<any>
  // 是否显示表单项
  show?: boolean
  field?: string
} & Omit<FormSchema, 'field'>

type CrudDescriptionsParams = {
  // 是否显示表单项
  show?: boolean
} & Omit<DescriptionsSchema, 'field'>

const { t } = useI18n()

interface AllSchemas {
  searchSchema: FormSchema[]
  tableColumns: TableColumn[]
  formSchema: FormSchema[]
  detailSchema: DescriptionsSchema[]
  exportSchema: object
  monitorSchema: object
  queryFormSchema: object | void
}

// 过滤所有结构
export const useCrudSchemas = (
  crudSchema: CrudSchema[],
  queryForm: object | void
): {
  allSchemas: AllSchemas
} => {
  // 所有结构数据
  const allSchemas = reactive<AllSchemas>({
    searchSchema: [],
    tableColumns: [],
    formSchema: [],
    detailSchema: [],
    exportSchema: {},
    monitorSchema: {},
    queryFormSchema: queryForm
  })

  const searchSchema = filterSearchSchema(crudSchema, allSchemas)
  allSchemas.searchSchema = searchSchema || []

  const tableColumns = filterTableSchema(crudSchema)
  allSchemas.tableColumns = tableColumns || []

  const formSchema = filterFormSchema(crudSchema, allSchemas)
  allSchemas.formSchema = formSchema

  const detailSchema = filterDescriptionsSchema(crudSchema)
  allSchemas.detailSchema = detailSchema

  const exportSchema = filterExportSchema(crudSchema)
  allSchemas.exportSchema = exportSchema

  const monitorSchema = filterMonitorSchema(crudSchema, allSchemas)
  allSchemas.monitorSchema = monitorSchema
  return {
    allSchemas
  }
}

// 过滤 Search 结构
const filterSearchSchema = (crudSchema: CrudSchema[], allSchemas: AllSchemas): FormSchema[] => {
  const searchSchema: FormSchema[] = []

  // 获取字典列表队列
  const searchRequestTask: Array<() => Promise<void>> = []

  eachTree(crudSchema, (schemaItem: CrudSchema) => {
    // 判断是否显示
    if (schemaItem?.search?.show) {
      const searchSchemaItem = {
        // 默认为 input
        component: schemaItem.search.component || 'Input',
        componentProps: {},
        ...schemaItem.search,
        field: schemaItem.search?.field || schemaItem.field,
        label: schemaItem.search?.label || schemaItem.label,
        index: schemaItem.search?.index
      }
      Object.assign(searchSchemaItem.componentProps, { style: { width: '100%' } })
      if (schemaItem.colProps) {
        searchSchemaItem.colProps = schemaItem.colProps
      }

      if (searchSchemaItem.dictName) {
        // 如果有 dictName 则证明是从字典中获取数据
        const dictArr = dictStore.getDictionariesOpt(
          dictStore.getDictObj[searchSchemaItem.dictName]
        )

        searchSchemaItem.componentProps!.options = filterOptions(dictArr)
      } else if (searchSchemaItem.api) {
        searchRequestTask.push(async () => {
          if (!searchSchemaItem.api) return
          const { datas: res } = await searchSchemaItem.api(searchSchemaItem.apiParams)
          if (res) {
            const index = findIndex(allSchemas.searchSchema, (v: FormSchema) => {
              return v.field === searchSchemaItem.field
            })
            if (index !== -1) {
              if (res.every((item) => Object.prototype.toString.call(item) == '[object String]')) {
                const list = res.map((item) => {
                  return { label: item, value: item }
                })

                // 说明是普通数组返回
                allSchemas.searchSchema[index]!.componentProps!.options = list
              } else {
                allSchemas.searchSchema[index]!.componentProps!.options = filterOptions(
                  res,
                  searchSchemaItem.componentProps.optionsAlias?.labelField
                )
              }
            }
          }
        })
      }

      // 删除不必要的字段
      delete searchSchemaItem.show
      delete searchSchemaItem.dictName
      searchSchema.push(searchSchemaItem)
    }
  })

  for (const task of searchRequestTask) {
    task()
  }

  searchSchema.sort((a, b) => (a.index || 0) - (b.index || 0))

  return searchSchema
}

// 过滤 table 结构
const filterTableSchema = (crudSchema: CrudSchema[]): TableColumn[] => {
  const tableColumns = treeMap<CrudSchema>(crudSchema, {
    conversion: (schema: CrudSchema) => {
      if (schema?.table?.show !== false) {
        return {
          ...schema.table,
          ...schema
        }
      }
    }
  })

  // 第一次过滤会有 undefined 所以需要二次过滤
  return filter<TableColumn>(tableColumns as TableColumn[], (data) => {
    if (data.children === void 0) {
      delete data.children
    }
    const vxeTableType = ['seq', 'expand', 'checkbox', 'radio']
    return vxeTableType.indexOf(data.type) !== -1 || !!data.field
  })
}

// 过滤 form 结构
const filterFormSchema = (crudSchema: CrudSchema[], allSchemas: AllSchemas): FormSchema[] => {
  const formSchema: FormSchema[] = []

  // 获取字典列表队列
  const formRequestTask: Array<() => Promise<void>> = []

  eachTree(crudSchema, (schemaItem: CrudSchema) => {
    // 判断是否显示
    if (schemaItem?.form?.show === true) {
      const formSchemaItem = {
        // 默认为 input
        component: schemaItem?.form?.component || 'Input',
        componentProps: {},
        field: schemaItem.field,
        label: schemaItem.label,
        ...schemaItem.form
      }
      Object.assign(formSchemaItem.componentProps, { style: { width: '100%' } })
      if (formSchemaItem.dictName) {
        // 如果有 dictName 则证明是从字典中获取数据
        const dictArr = dictStore.getDictionariesOpt(dictStore.getDictObj[formSchemaItem.dictName])
        formSchemaItem.componentProps!.options = filterOptions(dictArr)
      } else if (formSchemaItem.api) {
        formRequestTask.push(async () => {
          if (!formSchemaItem.api) return
          const { datas: res } = await formSchemaItem.api(formSchemaItem.apiParams)
          if (res) {
            const index = findIndex(allSchemas.formSchema, (v: FormSchema) => {
              return v.field === formSchemaItem.field
            })
            if (index !== -1) {
              if (
                res.length &&
                res.every((item) => Object.prototype.toString.call(item) == '[object String]')
              ) {
                const list = res.map((item) => {
                  return { label: item, value: item }
                })
                // 说明是普通数组返回
                allSchemas.formSchema[index]!.componentProps!.options = list
              } else {
                allSchemas.formSchema[index]!.componentProps!.options = filterOptions(
                  res,
                  formSchemaItem.componentProps.optionsAlias?.labelField
                )
              }
            }
          }
        })
      }

      // 删除不必要的字段
      delete formSchemaItem.show
      delete formSchemaItem.dictName

      formSchema.push(formSchemaItem)
    }
  })

  for (const task of formRequestTask) {
    task()
  }
  return formSchema
}

// 过滤 descriptions 结构
const filterDescriptionsSchema = (crudSchema: CrudSchema[]): DescriptionsSchema[] => {
  const descriptionsSchema: any[] = []

  eachTree(crudSchema, (schemaItem: CrudSchema) => {
    // 判断是否显示
    if (schemaItem?.detail?.show !== false) {
      const descriptionsSchemaItem = {
        ...schemaItem.detail,
        field: schemaItem.field,
        label: schemaItem.detail?.label || schemaItem.label
      }

      // 删除不必要的字段
      delete descriptionsSchemaItem.show

      descriptionsSchema.push(descriptionsSchemaItem)
    }
  })

  return descriptionsSchema
}

// 过滤export结构
const filterExportSchema = (crudSchema: CrudSchema[]): object => {
  const exportSchema = {}
  crudSchema.forEach((schema) => {
    if (schema?.export?.show) {
      if (schema.type === 'seq') {
        exportSchema['index'] = schema.label || '#'
      } else {
        exportSchema[schema.field] = schema.label
      }
    }
  })
  return exportSchema
}

// 给options添加国际化
const filterOptions = (options: Recordable, labelField?: string) => {
  return (
    options.length &&
    options.map((v: Recordable) => {
      if (labelField) {
        v['labelField'] = t(v.labelField)
      } else {
        v['label'] = t(v.label)
      }
      return v
    })
  )
}

const getTreeName = (list, id, key, name) => {
  for (let i = 0; i < list.length; i++) {
    const a = list[i]
    if (a.id + '' === id + '') {
      return a[name]
    } else {
      if (a[key] && a[key].length > 0) {
        const res = getTreeName(a[key], id, key, name)
        if (res) {
          return res
        }
      }
    }
  }
}

const filterMonitorSchema = (crudSchema: CrudSchema[], allSchemas: AllSchemas): object => {
  const monitorSchema = {}
  // console.log('queryFormSchema', allSchemas?.queryFormSchema)
  const searchSchema = filterSearchSchema(crudSchema, allSchemas)
  // console.log('searchSchema: ', searchSchema)
  allSchemas?.queryFormSchema &&
    Object.keys(allSchemas?.queryFormSchema).map((item) => {
      const schemaItem: FormSchema = searchSchema?.find((e) => e.field === item) || {}
      // 存在值的数组或者非空字符串/数字
      if (
        (isArray(allSchemas?.queryFormSchema[item]) &&
          !!allSchemas?.queryFormSchema[item].length) ||
        (allSchemas?.queryFormSchema[item] && !isArray(allSchemas?.queryFormSchema[item]))
      ) {
        if (schemaItem?.component === 'Select') {
          const options: any = schemaItem?.componentProps?.options
          const optionsAlias = schemaItem?.componentProps?.optionsAlias
          const labelField = optionsAlias?.labelField || 'label'
          const valueField = optionsAlias?.valueField || 'value'
          if (schemaItem?.componentProps?.multiple as boolean) {
            monitorSchema[schemaItem.label as string] = allSchemas?.queryFormSchema[item].map(
              (e) => options?.find((optionItem) => optionItem[valueField] === e)[labelField]
            )
          } else {
            monitorSchema[schemaItem.label as string] = options?.find(
              (optionItem) => optionItem[valueField] === allSchemas?.queryFormSchema[item]
            )[labelField]
          }
        } else if (schemaItem?.component === 'Cascader') {
          const options: any = schemaItem?.componentProps?.options
          // queryFormSchema的categoryList 二维数组
          let categoryName: string[] = []
          const categoryNameList: Array<string>[] = []
          allSchemas?.queryFormSchema[item].map((e) => {
            categoryName = []
            e.map((ee) =>
              categoryName.push(
                getTreeName(
                  options,
                  ee,
                  schemaItem?.componentProps?.props?.children,
                  schemaItem?.componentProps?.props?.label
                ) as string
              )
            )
            categoryNameList.push(categoryName)
          })
          monitorSchema[schemaItem.label as string] = categoryNameList
        } else {
          monitorSchema[schemaItem.label as string] = allSchemas?.queryFormSchema[item]
        }
      }
    })
  return monitorSchema
}
