import { ref, watch } from 'vue'
import { isString } from '@/utils/is'
import { useAppStoreWithOut } from '@/store/modules/app'
import { RouteMeta } from 'vue-router'
import { LocaleEnum, useLocaleStoreWithOut } from '@/store/modules/locale'
import { storeToRefs } from 'pinia'

const appStore = useAppStoreWithOut()

export const useTitle = (newTitle: string) => {
  const title = ref(newTitle ? `${appStore.getTitle} - ${newTitle}` : appStore.getTitle)

  watch(
    title,
    (n, o) => {
      if (isString(n) && n !== o && document) {
        document.title = n
      }
    },
    { immediate: true }
  )

  return title
}

export const useI18nTitle = (meta: RouteMeta) => {
  const { getCurrentLocale } = storeToRefs(useLocaleStoreWithOut())
  const { lang } = getCurrentLocale.value
  const { i18n } = meta
  return i18n?.[lang] || i18n?.[LocaleEnum.ZH_CN] || meta.title || 'Please Set Title'
}
