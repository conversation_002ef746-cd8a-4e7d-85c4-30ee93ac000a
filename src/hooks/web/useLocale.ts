import { i18n, setHtmlPageLang } from '@/hooks/web/useI18n'
import { useLocaleStoreWithOut, vxeLocaleMap } from '@/store/modules/locale'

const setI18nLanguage = (locale: LocaleType) => {
  const localeStore = useLocaleStoreWithOut()

  if (i18n.mode === 'legacy') {
    i18n.global.locale = locale
  } else {
    ;(i18n.global.locale as any).value = locale
  }
  localeStore.setCurrentLocale({
    lang: locale
  })
  setHtmlPageLang(locale)
}

export const useLocale = () => {
  const localeMessage = ref<Record<string, any>>({})
  // Switching the language will change the locale of useI18n
  // And submit to configuration modification
  const changeLocale = async (locale: LocaleType) => {
    const globalI18n = i18n.global
    const langConfig = import.meta.glob<{
      default: Record<string, string>
    }>(['../../locales/**.json'])
    const { default: staticMsg } = await langConfig[`../../locales/static-${locale}.json`]()
    if (localeMessage.value && localeMessage.value[locale]) {
      const msg = localeMessage.value[locale]
      globalI18n.setLocaleMessage(locale, {
        ...msg,
        ...staticMsg
      })
      setI18nLanguage(locale)
      return
    }
    // const { default: msg } = await langConfig[`../../locales/${locale.lang}.json`]()
    const vxeMsg = vxeLocaleMap.get(locale)
    const response = await fetch(`/assets/locales/${locale}.json?t=${Date.now()}`)
    const msg = await response.json()
    const message = {
      ...msg,
      ...staticMsg,
      ...vxeMsg
    }
    globalI18n.setLocaleMessage(locale, message)
    setI18nLanguage(locale)
    localeMessage.value = {
      [locale]: {
        ...msg,
        ...vxeMsg
      }
    }
  }

  return {
    changeLocale
  }
}
