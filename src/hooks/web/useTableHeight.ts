import type { VxeGridInstance, VxeTableInstance } from 'vxe-table'
import type { ElPagination, TableInstance } from 'element-plus'
import type { ComponentPublicInstance, Ref } from 'vue'

interface TableHeightParams {
  offsetBottom?: number
  tableRef?: Ref<
    VxeTableInstance | VxeGridInstance | TableInstance | ComponentPublicInstance | undefined
  >
  pagerRef?: Ref<InstanceType<typeof ElPagination> | undefined>
  minHeight?: number
}

export const getMaxHeight = (params?: TableHeightParams) => {
  let maxHeight = params?.minHeight ?? 500
  const tableRef = params?.tableRef?.value
  if (!tableRef) {
    return maxHeight
  }

  const offsetBottom = params?.offsetBottom ?? 40
  const pagerRef = params?.pagerRef?.value

  // 获取浏览器页面高度
  const winHeight = window.innerHeight
  const minHeight = params?.minHeight ?? 300

  // 获取表格距离顶部的距离
  const tableOffsetTop = tableRef.$el?.getBoundingClientRect().top || 0

  // 获取分页器的高度
  const pagerHeight = pagerRef?.$el.getBoundingClientRect().height || 0
  // 获取表格的最大高度
  const height = winHeight - tableOffsetTop - pagerHeight - offsetBottom
  maxHeight = height > minHeight ? height : minHeight
  return maxHeight
}

/**
 * @description 根据页面大小设置表格最大高度
 */
export const useTableHeight = (params?: TableHeightParams): Ref<number> => {
  const maxHeight = ref(params?.minHeight ?? 500)

  const setMaxHeight = () => {
    maxHeight.value = getMaxHeight(params)
  }

  watch(
    () => params?.tableRef?.value,
    (newVal) => {
      if (newVal) {
        setMaxHeight()
      }
    }
  )

  onMounted(async () => {
    await nextTick()
    maxHeight.value = getMaxHeight(params)
    window.addEventListener('resize', setMaxHeight)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('resize', setMaxHeight)
  })

  return maxHeight
}
