import { useTagsViewStore } from '@/store/modules/tagsView'
import type { RouteRecordName } from 'vue-router'
import router from '@/router'

export const useClosePage = (toName?: RouteRecordName, options?: Record<string, any>) => {
  const { currentRoute, push } = router
  const tagsViewStore = useTagsViewStore()
  const toLastView = () => {
    const visitedViews = tagsViewStore.getVisitedViews
    const latestView = visitedViews.slice(-1)[0]
    if (latestView) {
      push(latestView)
    } else {
      push({ path: '/' })
    }
  }
  const routerName = toName as string
  tagsViewStore.delView(unref(currentRoute))
  if (routerName) {
    push({ name: routerName, ...options })
  } else {
    toLastView()
  }
}
