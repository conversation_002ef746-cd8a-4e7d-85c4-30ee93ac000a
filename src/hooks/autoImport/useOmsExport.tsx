import { omsExport, pdmExport } from '@/api/common'
import { OMSExportAPI } from '@/api/common/type'
import { ElButton, ElMessageBox } from 'element-plus'
import router from '@/router'

const handleJump = () => {
  const { href } = router.resolve({
    path: '/export-file'
  })
  window.open(href, '_blank')
}

export const useExportMsg = async () => {
  await ElMessageBox({
    type: 'success',
    title: '导出成功',
    showCancelButton: false,
    showConfirmButton: true,
    message: () => {
      return (
        <div class={'flex items-center'}>
          <span>导出成功，请到</span>
          <ElButton
            type={'primary'}
            class={'!p-0'}
            link
            onClick={() => {
              handleJump()
            }}
          >
            下载中心
          </ElButton>
          <span>查看</span>
        </div>
      )
    }
  })
}

export const useOmsExport = (useOms = true) => {
  const loading = ref(false)

  const handleExport = async (data: OMSExportAPI.Request) => {
    loading.value = true
    const exportApi = useOms ? omsExport : pdmExport
    const [error, result] = await exportApi({
      ...data,
      appName: 'pdm'
    })
    loading.value = false
    if (error === null && result) {
      await useExportMsg()
    }
  }

  return {
    loading,
    handleExport
  }
}
