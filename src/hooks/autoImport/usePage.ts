import type { VxeTableEvents, VxeTableInstance, VxeTablePropTypes } from 'vxe-table'
import { cloneDeep, isEqual, isFunction, isObject, omit } from 'lodash-es'
import { ComputedRef, Ref, ShallowRef } from 'vue'
import { DateFormat, FooterField, SplitConversion } from '@/hooks/types'
import { isString } from 'xe-utils'
import { formatArrayRules, type FormatArrayRulesVerify } from '@/utils'
import type { FormRules } from 'element-plus'

interface UsePageOptions<List, Params extends PageParams, Total> {
  /** API 函数，用于执行分页请求 */
  api?: (params: Params) => Promise<PagedResponseData<List, Total>>
  /** 表单数据，用于构建请求参数 */
  formData?: Omit<Params, 'current' | 'size'>
  /** 日期格式处理配置 */
  dateFormat?: DateFormat<Params>[]
  /** 需要转换成数组的字符串的配置 */
  splitConversion?: SplitConversion<Params>[]
  /** 尾行合计字段的配置 */
  footerField?: FooterField<List, Total>[]
  /** 是否启用 checkbox 记忆功能 */
  checkBokRemember?: boolean
  /** 是否在组件 mounted 时调用 API */
  mountedRequest?: boolean
  /** FormArray 规则验证 */
  formArrayRules?: FormatArrayRulesVerify
  /** 导出详情映射键 */
  exportDetailsMapKey?: [string, keyof List]

  /** 数据格式化方法，用于处理 API 返回的列表数据 */
  dataFormatMethod?(list: List[]): List[]

  /** 合计数据处理方法，用于处理 API 返回的合计数据 */
  tableDataTotalMethod?(total: Total): Total

  /** 请求参数格式化方法，用于调整发送到 API 的参数 */
  pramsFormatMethod?(params: Params): Params

  /** 接口响应后执行的方法 */
  responseAfterMethod?(): void | Promise<void>
}

interface UsePageReturnType<List, Params extends PageParams, Total> {
  /** 表格加载状态 */
  loading: Ref<boolean>
  /** 表格实例的引用 */
  refTable: Ref<VxeTableInstance | undefined>
  /** 查询表单数据 */
  queryFormData: ShallowRef<Omit<Params, 'current' | 'size'> | undefined>
  /** 分页器状态 */
  pager: BasicPage
  /** 经过处理的页面参数 */
  pageParams: ComputedRef<Params>
  /** 表格数据 */
  tableData: Ref<List[]>
  /** 选中的复选框列表 */
  checkBoxSelectedList: Ref<List[]>
  /** 选中的单选行数据 */
  radioSelectedRow: Ref<List | undefined>
  /** 合计数据 */
  tableDataTotal: ShallowRef<Total | undefined>
  /** 分页更新函数 */
  pagerUpdate: (val: BasicPage) => void
  /** 搜索更新函数 */
  searchUpdate: () => void
  /** 请求数据的函数 */
  requestData: () => Promise<void>
  /** 更新请求数据的函数 */
  updateRequestData: () => void
  /** 复选框变化时的处理函数 */
  tableCheckBoxSelectChange: VxeTableEvents.CheckboxChange<List>
  /** 单选框变化时的处理函数 */
  radioChange: VxeTableEvents.RadioChange<List>
  /** 表格合计行的处理方法 */
  tableFooterMethod: VxeTablePropTypes.FooterMethod
  /** 注册表格实例的引用 */
  refRegister: (val: Ref<{ vxeTableRef: VxeTableInstance }>) => void
  /** 导出参数 */
  exportParams: ComputedRef<Omit<Params, 'current' | 'size'>>
  /** 导出详情参数 */
  exportDetailsParams: ComputedRef<Omit<Params, 'current' | 'size'>>
  /** 表单校验规则 */
  rules: FormRules | undefined
}

/**
 * 在Vue组件中管理页面数据的自定义钩子。
 *
 * @template List - 表格数据项的类型。
 * @template Params - 页面参数的类型。
 * @template Total - 表格数据总数的类型。
 * @param {UsePageOptions<List, Params, Total>} [options] - 配置钩子的选项。
 * @returns {UsePageReturnType<List, Params, Total>} - 包含钩子的响应式变量和函数的返回对象。
 */
export function usePage<List, Params extends PageParams, Total>(
  options?: UsePageOptions<List, Params, Total>
): UsePageReturnType<List, Params, Total> {
  const {
    api,
    formData,
    dataFormatMethod,
    tableDataTotalMethod,
    pramsFormatMethod,
    responseAfterMethod,
    dateFormat,
    splitConversion,
    footerField,
    checkBokRemember,
    exportDetailsMapKey = [],
    mountedRequest = false
  } = options || {}

  // 声明响应式变量
  const loading = ref<boolean>(false)
  const refTable = ref<VxeTableInstance>()
  // https://github.com/vuejs/core/issues/2136
  const tableData = ref<List[]>([]) as Ref<List[]>
  const checkBoxSelectedList = ref<List[]>([]) as Ref<List[]>
  const radioSelectedRow = ref<List>()
  const tableDataTotal = shallowRef<Total>()
  const queryFormData = shallowRef(cloneDeep(formData))
  const pager: BasicPage = reactive({
    current: 1,
    size: 20,
    total: 0,
    pages: 0
  })

  // 计算用于分页查询的参数。
  const pageParams = computed<Params>(() => {
    // 解构基本的分页参数。
    const { current, size } = pager

    // 基本的分页参数。
    const baseParams = { current, size }

    // 处理需要split的参数
    const splitParams =
      splitConversion?.reduce((acc, cur) => {
        const { key, symbol } = cur
        const requestData = unref(queryFormData) as Record<string, unknown>
        const value = requestData?.[key]
        if (value && isString(value)) {
          acc[key] = value.trim().split(symbol)
        }
        return acc
      }, {} as Record<string, unknown>) || {}

    // 如果没有定义特殊的日期格式，则直接返回组合后的查询参数。
    if (!dateFormat?.length) {
      const params = { ...baseParams, ...unref(queryFormData) } as Params
      return isFunction(pramsFormatMethod)
        ? // 如果存在参数格式化函数，应用它。
          pramsFormatMethod(params)
        : // 否则，直接返回组合后的参数。
          { ...params, ...splitParams }
    }

    // 格式化日期参数。
    const dateParams = dateFormat.map(({ format, modelValue }) => {
      const [startDateStr, endDateStr] = format
      const [start = '', end = ''] = queryFormData.value?.[modelValue] || []
      return { [startDateStr]: start, [endDateStr]: end }
    })

    // 移除已经处理过的日期参数。
    const otherParams = omit(
      unref(queryFormData as object),
      dateFormat.map(({ modelValue }) => modelValue)
    )

    // 组合所有的查询参数。
    const combinedParams = {
      ...baseParams,
      ...otherParams,
      ...splitParams,
      ...Object.assign({}, ...dateParams)
    }

    // 如果存在参数格式化函数，应用它。
    return isFunction(pramsFormatMethod)
      ? pramsFormatMethod(combinedParams as Params)
      : combinedParams
  })

  const exportParams = computed(() => {
    return omit(pageParams.value || null, ['current', 'size']) as Omit<Params, 'current' | 'size'>
  })

  const exportDetailsParams = computed(() => {
    const pageParamsValue = unref(pageParams)

    if (exportDetailsMapKey?.length) {
      const [paramsKey, fieldKey] = exportDetailsMapKey
      pageParamsValue[paramsKey] = checkBoxSelectedList.value.map((item) => item[fieldKey])
    }
    return omit(pageParamsValue || null, ['current', 'size']) as Omit<Params, 'current' | 'size'>
  })

  // 传入tableRef
  const refRegister = (ref: Ref<{ vxeTableRef: VxeTableInstance<List> }>) => {
    refTable.value = unref(ref).vxeTableRef
  }
  const requestData = async () => {
    if (!api) return
    try {
      loading.value = true
      const { datas } = await api(pageParams.value)
      const { records, pager: responsePager, totalData } = datas
      // 传入dataFormatMethod 对返回数据进行处理
      if (dataFormatMethod) {
        const formattedData = dataFormatMethod(records)
        if (Array.isArray(formattedData)) {
          tableData.value = formattedData
        }
      } else {
        tableData.value = records
      }

      if (tableDataTotalMethod && totalData !== undefined) {
        tableDataTotal.value = tableDataTotalMethod(totalData)
      } else {
        tableDataTotal.value = totalData
      }
      // 赋值分页参数
      Object.assign(pager, responsePager)

      // 如果传入refTable并且复选框有值则记忆勾选
      if (refTable.value && checkBokRemember) {
        tableData.value.forEach((item) => {
          if (checkBoxSelectedList.value.find((list) => isEqualObj(list, item))) {
            nextTick(() => {
              refTable.value?.setCheckboxRow(item, true)
            })
          }
        })
      }

      // 调用接口后执行
      await nextTick()
      if (responseAfterMethod) responseAfterMethod()
    } finally {
      radioSelectedRow.value = undefined
      loading.value = false
    }
  }

  // 对比整个对象
  const isEqualObj = (value: List, other: List): boolean => {
    // value 和 other 是否都是对象
    if (isObject(value) && isObject(other)) {
      return isEqual(
        omit(toRaw(value), [VXE_TABLE_ROW_KEY]),
        omit(toRaw(other), [VXE_TABLE_ROW_KEY])
      )
    }
    // 如果 value 或 other 不是对象
    return false
  }

  // 合计方法
  const tableFooterMethod: VxeTablePropTypes.FooterMethod = ({ columns }) => {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return '总计'
        }
        const footerCur = footerField?.find((item) => item.field === column.field)
        if (footerCur) {
          return tableDataTotal.value?.[footerCur.total] as string
        }
        return '-'
      })
    ]
  }
  // 复选方法
  const tableCheckBoxSelectChange: VxeTableEvents.CheckboxChange<List> = ({ checked, row }) => {
    if (row) {
      if (checked) {
        checkBoxSelectedList.value.push(row)
      } else {
        const start = checkBoxSelectedList.value.findIndex((item) => isEqualObj(item, row))
        checkBoxSelectedList.value.splice(start, 1)
      }
    } else {
      const selectList = cloneDeep(checkBoxSelectedList.value)
      checkBoxSelectedList.value = tableData.value?.reduce((acc, cur) => {
        const index = acc.findIndex((item) => isEqualObj(item, cur))
        if (checked) {
          index === -1 && acc.push(cur)
        } else {
          acc.splice(index, 1)
        }
        return acc
      }, selectList)
    }
  }

  // 单选方法
  const radioChange: VxeTableEvents.RadioChange<List> = ({ row }) => {
    radioSelectedRow.value = row
  }
  // 分页更新
  const pagerUpdate = (val: BasicPage) => {
    Object.assign(pager, val)
    requestData()
  }
  // 搜索方法
  const searchUpdate = (data?: Omit<Params, 'current' | 'size'>) => {
    if (data && isObject(formData)) {
      Object.assign(formData, data)
    }
    queryFormData.value = cloneDeep(formData)
    checkBoxSelectedList.value = []
    pager.current = 1
    requestData()
  }
  const updateRequestData = () => {
    queryFormData.value = cloneDeep(formData)
    requestData()
  }

  const rules = formatArrayRules(options?.formArrayRules)

  onMounted(() => {
    if (mountedRequest) {
      requestData()
    }
  })
  return {
    loading,
    refTable,
    queryFormData,
    pager,
    pageParams,
    tableData,
    checkBoxSelectedList,
    radioSelectedRow,
    tableDataTotal,
    exportParams,
    exportDetailsParams,
    rules,
    pagerUpdate,
    searchUpdate,
    requestData,
    tableCheckBoxSelectChange,
    updateRequestData,
    radioChange,
    tableFooterMethod,
    refRegister
  }
}
