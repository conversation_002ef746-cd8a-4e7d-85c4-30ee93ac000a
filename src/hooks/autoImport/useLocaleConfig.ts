import { LocaleEnum, useLocaleStore } from '@/store/modules/locale'
import { storeToRefs } from 'pinia'

const localeConfigList = [LocaleEnum.ZH_CN, LocaleEnum.EN_US]

// useLocalConfig 函数类型化
export const useLocaleConfig = <T extends readonly unknown[]>(configList: T): T[number] => {
  const { currentLocale } = storeToRefs(useLocaleStore())

  // 获取当前语言的索引
  const index = localeConfigList.findIndex((item) => item === currentLocale.value.lang)

  // 返回对应的配置
  return configList[index]!
}
