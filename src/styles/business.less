.form-layout--2 .el-form-item {
  width: 50%;
  margin-right: 0 !important;
  padding-right: 32px;
}

.descriptions-details-layout__2 .el-descriptions__body {
  .el-descriptions__label {
    width: 10em;
  }

  .el-descriptions__content {
    word-break: break-all; // 让内容超出列宽时自动换行显示
    word-wrap: break-word;
  }
}

.el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
  outline: none;
}

.form-item-no-wrap {
  > .el-form-item__content {
    flex-wrap: nowrap;
  }
}

.el-form-item {
  .el-cascader,
  .el-select,
  .el-input,
  .el-autocomplete {
    width: 240px;
    max-width: 100%;

    .el-select__wrapper {
      width: 100%;
    }
  }
}

.vxe-table {
  .el-cascader,
  .el-select,
  .el-autocomplete {
    width: 240px;
    max-width: 100%;

    .el-select__wrapper {
      width: 100%;
    }
  }

  .el-scrollbar {
    width: 100%;
  }
}

.vxe-table--render-default {
  .vxe-body--row {
    .vxe-body--column {
      &.col--ellipsis {
        &:not(.col--active) {
          & > .vxe-cell {
            text-overflow: clip;
            white-space: break-spaces;

            &.c--tooltip {
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
}

// 用于表头的必填项
.header-required .vxe-cell--title::before {
  content: '*';
  color: red;
  margin-right: 5px;
}

// 开启collapse-tags-tooltip的el-select,disable设为true, hover时可正常触发tooltip
.el-select__wrapper.is-disabled:has(.el-select__selection.is-near) {
  pointer-events: auto;
}

// 通过全局样式设置elMessageBox打开的组件
.send-to-wms-dialog,
.style-partner-code-dialog {
  .el-message-box__container,
  .el-message-box__message {
    // el-table宽度
    width: 100%;
  }
}

// 避免vxe-cell的固定height影响ellipsis-tooltip的padding
//.ellipsis-cell {
//  .vxe-cell:has(> .vxe-cell--wrapper > .ellipsis-tooltip-wrapper) {
//    height: initial !important;
//  }
//}
.table-render {
  .el-button > span {
    width: 100%;
  }
}
