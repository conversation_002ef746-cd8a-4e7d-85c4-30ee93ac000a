:root {
  --dark-bg-color: #293146;

  /* left menu start */
  --left-menu-border-color: '#eee';

  --left-menu-max-width: 230px;

  --left-menu-min-width: 64px;

  /* --left-menu-bg-color: #001529; */
  --left-menu-bg-color: #ffffff;

  /* --left-menu-bg-light-color: #0f2438; */
  --left-menu-bg-color: #ffffff;

  /* --left-menu-bg-active-color: var(--el-color-primary); */
  --left-menu-bg-active-color: RGBA(64, 158, 255, 0.1);

  /* --left-menu-text-color: #bfcbd9; */

  --left-menu-text-color: #333;

  /* --left-menu-text-active-color: #fff; */
  --left-menu-text-active-color: var(--el-color-primary);

  /* --left-menu-collapse-bg-active-color: var(--el-color-primary); */
  --left-menu-collapse-bg-active-color: RGBA(64, 158, 255, 0.1);

  /* left menu end */

  /* logo start */
  --logo-height: 50px;

  /* --logo-title-text-color: #fff; */

  --logo-title-text-color: 'inherit';

  /* --logo-border-color: 'inherit'; */
  --logo-border-color: #eee;
  /* logo end */

  /* header start */
  --top-header-bg-color: '#fff';

  --top-header-text-color: 'inherit';

  --top-header-hover-color: #f6f6f6;

  --top-tool-height: var(--logo-height);

  --top-tool-p-x: 0;

  --top-tool-border-color: #eee;

  --tags-view-height: 35px;

  --tags-view-border-color: #eee;
  /* header start */

  /* tab menu start */
  --tab-menu-max-width: 80px;

  --tab-menu-min-width: 30px;

  --tab-menu-collapse-height: 36px;

  --tab-menu-border-color: #eee;
  /* tab menu end */

  --app-content-padding: 10px;

  --app-content-bg-color: #f5f7f9;

  --app-footer-height: 30px;

  --transition-time-02: 0.2s;
}
