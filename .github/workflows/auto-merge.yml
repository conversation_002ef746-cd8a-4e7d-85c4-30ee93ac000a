name: Automerge

on:
  pull_request:
    types:
      - labeled
      - unlabeled
      - synchronize
      - opened
      - edited
      - ready_for_review
      - reopened
      - unlocked
  pull_request_review:
    types:
      - submitted
  status: {}

jobs:
  # 合并发布版本的 pr 到 master
  auto-merge:
    runs-on: ubuntu-latest
    steps:
      - name: Automerge
        uses: 'pascalgn/automerge-action@v0.14.3'
        env:
          GITHUB_TOKEN: '${{ secrets.TOKEN }}'
          MERGE_LABELS: ''
          MERGE_FILTER_AUTHOR: 'kailong321200875'

  push-to-gh-pages:
    needs: [auto-merge]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup Pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      - name: use Node.js 16
        uses: actions/setup-node@v2.1.2
        with:
          node-version: '16.x'

      - name: Set SSH Environment
        env:
          DOCS_DEPLOY_KEY: ${{ secrets.ACTIONS_DEPLOY_KEY }}
        run: |
          mkdir -p ~/.ssh/
          echo "$ACTIONS_DEPLOY_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan github.com > ~/.ssh/known_hosts
          chmod 700 ~/.ssh && chmod 600 ~/.ssh/*
          git config --local user.email "<EMAIL>"
          git config --local user.name "kailong321200875"

      # 发布到 github
      - name: Build Github
        run: |
          pnpm install --no-frozen-lockfile
          pnpm run build:pro

      - name: Deploy Github
        uses: peaceiris/actions-gh-pages@v3
        with:
          deploy_key: ${{secrets.ACTIONS_DEPLOY_KEY}}
          publish_branch: gh-pages
          publish_dir: ./dist-pro
          force_orphan: true
          cname: element-plus-admin.cn

  push-to-gh-pages-gitee:
    needs: [auto-merge]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup Pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      - name: use Node.js 16
        uses: actions/setup-node@v2.1.2
        with:
          node-version: '16.x'

      - name: Set SSH Environment
        env:
          DOCS_DEPLOY_KEY: ${{ secrets.ACTIONS_DEPLOY_KEY }}
        run: |
          mkdir -p ~/.ssh/
          echo "$ACTIONS_DEPLOY_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan github.com > ~/.ssh/known_hosts
          chmod 700 ~/.ssh && chmod 600 ~/.ssh/*
          git config --local user.email "<EMAIL>"
          git config --local user.name "kailong321200875"

      - name: Build Gitee
        run: |
          pnpm install --no-frozen-lockfile
          pnpm run build:gitee

      # 发布到 gitee
      - name: Deploy Gitee
        uses: peaceiris/actions-gh-pages@v3
        with:
          deploy_key: ${{secrets.ACTIONS_DEPLOY_KEY}}
          publish_branch: gh-pages-gitee
          publish_dir: ./dist-pro
          force_orphan: true

      - name: Sync Github Repos To Gitee # 名字随便起
        uses: Yikun/hub-mirror-action@v1.1 # 使用Yikun/hub-mirror-action
        with:
          src: github/kailong321200875 # 源端账户名(github)
          dst: gitee/kailong110120130 # 目的端账户名(gitee)
          dst_key: ${{ secrets.ACTIONS_DEPLOY_KEY }} # SSH密钥对中的私钥
          dst_token: ${{ secrets.GITEE_TOKEN }} # Gitee账户的私人令牌
          account_type: user # 账户类型
          clone_style: 'https' # 使用https方式进行clone，也可以使用ssh
          debug: true # 启用后会显示所有执行命令
          force_update: true # 启用后，强制同步，即强制覆盖目的端仓库
          static_list: 'vue-element-plus-admin' # 静态同步列表，在此填写需要同步的仓库名称，可填写多个
          timeout: '600s' # git超时设置，超时后会自动重试git操作
