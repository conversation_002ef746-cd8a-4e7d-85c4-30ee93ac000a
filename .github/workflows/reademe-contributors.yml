# on:
#   push:
#     branches:
#       - master

# jobs:
#   contrib-readme-en-job:
#     runs-on: ubuntu-latest
#     name: A job to automate contrib in readme
#     steps:
#       - name: Contribute List
#         uses: akhilmhdh/contributors-readme-action@v2.3.4
#         env:
#           GITHUB_TOKEN: ${{ secrets.CONTRIBUTORS_TOKEN }}

#   contrib-readme-job:
#     runs-on: ubuntu-latest
#     name: A job to automate contrib in readme.zh-CN
#     steps:
#       - name: Contribute List
#         uses: akhilmhdh/contributors-readme-action@v2.3.4
#         with:
#           readme_path: README.zh-CN.md
#         env:
#           GITHUB_TOKEN: ${{ secrets.CONTRIBUTORS_TOKEN }}
