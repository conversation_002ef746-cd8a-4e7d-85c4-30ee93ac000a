{
  "compilerOptions": {
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "Bundler",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "allowJs": true,
    "forceConsistentCasingInFileNames": true,
    "allowSyntheticDefaultImports": true,
    "strictFunctionTypes": false,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "experimentalDecorators": true,
    "noImplicitAny": false,
    "skipLibCheck": true,
    "paths": {
      "@/*": ["src/*"]
    },
    "types": [
      "vite/client",
      "element-plus/global",
      "vite-plugin-svg-icons/client",
    ],
    "typeRoots": ["./node_modules/@types/", "./types", "./node_modules"]
  },
  "include": ["src/**/*", "types/**/*.d.ts", "mock/**/*.ts"],
  "exclude": ["dist", "node_modules", ".nvmrc"],
}
