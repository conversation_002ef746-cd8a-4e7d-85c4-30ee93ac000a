{"$schema": "https://github.com/JetBrains/web-types/blob/master/schema/web-types.json", "name": "pdm-web-type", "version": "1.0.0", "framework": "vue", "contributions": {"html": {"types-syntax": "typescript", "description-markup": "markdown", "attributes": [{"name": "v-has<PERSON>ermi", "doc-url": "https://github.com/JetBrains/web-types/blob/master/packages/vue/<EMAIL>", "description": "权限指令, 接受 string 或 string 数组", "required": true, "value": {"kind": "expression", "type": ["string", "string[]"]}}]}}}