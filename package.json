{"name": "vue-element-plus-admin", "version": "1.8.1", "description": "一套基于vue3、element-plus、typesScript、vite3的后台集成方案。", "author": "MMT FE", "private": false, "scripts": {"i": "pnpm install", "dev": "vite --mode dev", "uat": "vite --mode uat", "pro": "vite --mode pro", "ts:check": "vue-tsc --noEmit", "build:pro": "vite build --mode pro", "build:visualizer": "set VITE_VISUALIZER=true&& vite build --mode pro", "build:uat": "vite build --mode uat", "build:dev": "vite build --mode dev", "serve:pro": "vite preview --mode pro", "serve:dev": "vite preview --mode dev", "npm:check": "npx npm-check-updates", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,vue,html,md}\"", "lint:style": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "prepare": "husky install", "p": "plop"}, "dependencies": {"@iconify/iconify": "^3.1.1", "@sentry/vue": "^8.54.0", "@tinymce/tinymce-vue": "^6.1.0", "@vueuse/core": "^9.13.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@zxcvbn-ts/core": "^2.2.1", "animate.css": "^4.1.1", "await-to-js": "^3.0.0", "axios": "^1.7.9", "dayjs": "^1.11.13", "dom-to-image": "^2.6.0", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "element-plus": "2.9.8", "file-saver": "^2.0.5", "fuse.js": "^6.6.2", "js-cookie": "^3.0.5", "jsondiffpatch": "^0.5.0", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "pinia": "^2.3.1", "qs": "^6.14.0", "resize-detector": "^0.3.0", "socket.io-client": "^4.8.1", "sortablejs": "^1.15.6", "swiper": "^10.3.1", "tinymce": "^7.6.1", "vue": "^3.5.13", "vue-cropper": "^1.1.4", "vue-i18n": "9.2.2", "vue-matomo": "^4.2.0", "vue-router": "^4.5.0", "vue-types": "^5.1.3", "vuedraggable": "^4.1.0", "vxe-pc-ui": "^4.4.20", "vxe-table": "^4.11.28", "web-storage-cache": "^1.1.1", "xe-utils": "^3.7.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@higgins-mmt/vite-plugin-i18n-transformer": "^1.2.0", "@iconify/json": "^2.2.305", "@intlify/unplugin-vue-i18n": "^1.6.0", "@purge-icons/generated": "^0.9.0", "@sentry/vite-plugin": "^3.1.2", "@types/dom-to-image": "^2.6.7", "@types/file-saver": "^2.0.7", "@types/lodash-es": "^4.17.12", "@types/node": "^18.19.75", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.18", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "autoprefixer": "^10.4.20", "consola": "^2.15.3", "cssnano": "^7.0.6", "eslint": "^8.57.1", "eslint-config-prettier": "^8.10.0", "eslint-define-config": "^1.24.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.32.0", "husky": "^8.0.3", "less": "^4.2.2", "lint-staged": "^13.3.0", "plop": "^3.1.2", "postcss": "^8.5.2", "postcss-html": "^1.8.0", "postcss-import": "^16.1.0", "postcss-less": "^6.0.0", "prettier": "^2.8.8", "rimraf": "^3.0.2", "rollup-plugin-visualizer": "^5.14.0", "sass-embedded": "^1.89.0", "stylelint": "^14.16.1", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^9.0.0", "stylelint-config-standard": "^28.0.0", "stylelint-order": "^5.0.0", "tailwindcss": "^3.4.17", "typescript": "5.6.3", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.22.12", "vite": "^6.1.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.2", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-style-import": "2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.2.0"}, "engines": {"node": ">= 18.18.0"}, "license": "MIT", "web-types": "./web-types.json"}