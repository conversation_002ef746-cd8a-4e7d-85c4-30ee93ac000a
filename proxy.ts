import { ProxyOptions } from 'vite'

export function createProxy(envUrl: Record<string, string>): Record<string, string | ProxyOptions> {
  return {
    '/socket-version': {
      target: envUrl.VITE_API_VERSION,
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/version/, '')
    },
    '/pdm': {
      target: envUrl.VITE_PDM_URL,
      changeOrigin: true
    },
    '/pdm-export': {
      target: envUrl.VITE_PDM_URL,
      changeOrigin: true
    },
    '/oms': {
      target: envUrl.VITE_OMS_URL,
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/oms/, '')
    }
  }
}
