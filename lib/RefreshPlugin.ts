import fs from 'fs/promises'
import path from 'path'
import { Plugin } from 'vite'

const writeVersion = async (versionFile: string, content: string) => {
  await fs.writeFile(versionFile, content)
}

interface Options {
  version: number
}

export default (options: Options): Plugin => {
  let configPath: string
  let isBuild = false
  return {
    name: 'refresh-plugin',
    configResolved(resolvedConfig) {
      // 保存配置文件的路径
      configPath = resolvedConfig.publicDir
      // 保存是否是构建过程
      isBuild = resolvedConfig.command === 'build'
    },

    async buildStart() {
      if (!isBuild) return
      // 生成版本信息文件路径
      const file = path.join(configPath, 'version.json') // 使用 path.join 连接路径
      // 采用编译的当前时间作为每个版本的标识
      const content = JSON.stringify({ version: options.version })
      await fs.mkdir(configPath, { recursive: true })
      await writeVersion(file, content)
    }
  }
}
