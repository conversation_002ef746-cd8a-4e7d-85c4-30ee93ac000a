import { sentryVitePlugin } from '@sentry/vite-plugin'

/**
 * 集成 Sentry Vite 插件，以便在开发环境之外上传 Source Maps
 */
export const SentryVite = (env: Record<string, string>, isBuild: boolean) => {
  if (!isBuild) return // 如果是开发环境或者uat，则跳过 Sentry Vite 插件的初始化
  // 配置 Sentry Vite 插件，自动上传 Source Maps 文件
  return sentryVitePlugin({
    debug: false,
    url: env.VITE_SENTRY_URL,
    org: env.VITE_SENTRY_ORG, // 组织
    project: env.VITE_SENTRY_PROJECT, // 项目
    release: {
      // 版本
      name: 'PDM-0.0.1',
      uploadLegacySourcemaps: {
        paths: ['dist']
      }
    },
    sourcemaps: {
      assets: 'dist/assets/**',
      ignore: ['node_modules'],
      filesToDeleteAfterUpload: 'dist/**/*.map'
    },
    authToken: env.VITE_SENTRY_TOKEN // 秘钥
  })
}
