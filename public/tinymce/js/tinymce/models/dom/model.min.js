/**
 * TinyMCE version 7.3.0 (2024-08-07)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.ModelManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(o=n=e,(r=String).prototype.isPrototypeOf(o)||(null===(s=n.constructor)||void 0===s?void 0:s.name)===r.name)?"string":t;var o,n,r,s})(t)===e,o=e=>t=>typeof t===e,n=e=>t=>e===t,r=t("string"),s=t("object"),l=t("array"),a=n(null),c=o("boolean"),i=n(void 0),m=e=>!(e=>null==e)(e),d=o("function"),u=o("number"),f=()=>{},g=e=>()=>e,h=e=>e,p=(e,t)=>e===t;function b(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const w=e=>t=>!e(t),v=e=>e(),y=g(!1),x=g(!0);class C{constructor(e,t){this.tag=e,this.value=t}static some(e){return new C(!0,e)}static none(){return C.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?C.some(e(this.value)):C.none()}bind(e){return this.tag?e(this.value):C.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:C.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return m(e)?C.some(e):C.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}C.singletonNone=new C(!1);const T=Array.prototype.slice,S=Array.prototype.indexOf,R=Array.prototype.push,D=(e,t)=>{return o=e,n=t,S.call(o,n)>-1;var o,n},O=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},k=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},E=(e,t)=>{const o=e.length,n=new Array(o);for(let r=0;r<o;r++){const o=e[r];n[r]=t(o,r)}return n},N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},B=(e,t)=>{const o=[],n=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?o:n).push(s)}return{pass:o,fail:n}},_=(e,t)=>{const o=[];for(let n=0,r=e.length;n<r;n++){const r=e[n];t(r,n)&&o.push(r)}return o},z=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),A=(e,t,o)=>(N(e,((e,n)=>{o=t(o,e,n)})),o),L=(e,t)=>((e,t,o)=>{for(let n=0,r=e.length;n<r;n++){const r=e[n];if(t(r,n))return C.some(r);if(o(r,n))break}return C.none()})(e,t,y),W=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return C.some(o);return C.none()},M=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);R.apply(t,e[o])}return t},j=(e,t)=>M(E(e,t)),P=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},I=(e,t)=>{const o={};for(let n=0,r=e.length;n<r;n++){const r=e[n];o[String(r)]=t(r,n)}return o},F=(e,t)=>t>=0&&t<e.length?C.some(e[t]):C.none(),H=e=>F(e,0),$=e=>F(e,e.length-1),V=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return C.none()},q=Object.keys,U=Object.hasOwnProperty,G=(e,t)=>{const o=q(e);for(let n=0,r=o.length;n<r;n++){const r=o[n];t(e[r],r)}},K=(e,t)=>Y(e,((e,o)=>({k:o,v:t(e,o)}))),Y=(e,t)=>{const o={};return G(e,((e,n)=>{const r=t(e,n);o[r.k]=r.v})),o},J=(e,t)=>{const o=[];return G(e,((e,n)=>{o.push(t(e,n))})),o},Q=e=>J(e,h),X=(e,t)=>U.call(e,t),Z="undefined"!=typeof window?window:Function("return this;")(),ee=(e,t)=>((e,t)=>{let o=null!=t?t:Z;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),te=Object.getPrototypeOf,oe=e=>{const t=ee("ownerDocument.defaultView",e);return s(e)&&((e=>((e,t)=>{const o=((e,t)=>ee(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(te(e).constructor.name))},ne=e=>e.dom.nodeName.toLowerCase(),re=e=>e.dom.nodeType,se=e=>t=>re(t)===e,le=e=>8===re(e)||"#comment"===ne(e),ae=e=>ce(e)&&oe(e.dom),ce=se(1),ie=se(3),me=se(9),de=se(11),ue=e=>t=>ce(t)&&ne(t)===e,fe=(e,t,o)=>{if(!(r(o)||c(o)||u(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},ge=(e,t,o)=>{fe(e.dom,t,o)},he=(e,t)=>{const o=e.dom;G(t,((e,t)=>{fe(o,t,e)}))},pe=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},be=(e,t)=>C.from(pe(e,t)),we=(e,t)=>{e.dom.removeAttribute(t)},ve=e=>A(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),ye=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},xe={fromHtml:(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return ye(o.childNodes[0])},fromTag:(e,t)=>{const o=(t||document).createElement(e);return ye(o)},fromText:(e,t)=>{const o=(t||document).createTextNode(e);return ye(o)},fromDom:ye,fromPoint:(e,t,o)=>C.from(e.dom.elementFromPoint(t,o)).map(ye)},Ce=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Te=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Se=(e,t)=>{const o=void 0===t?document:t.dom;return Te(o)?C.none():C.from(o.querySelector(e)).map(xe.fromDom)},Re=(e,t)=>e.dom===t.dom,De=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},Oe=Ce,ke=e=>xe.fromDom(e.dom.ownerDocument),Ee=e=>me(e)?e:ke(e),Ne=e=>C.from(e.dom.parentNode).map(xe.fromDom),Be=e=>C.from(e.dom.parentElement).map(xe.fromDom),_e=(e,t)=>{const o=d(t)?t:y;let n=e.dom;const r=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=xe.fromDom(e);if(r.push(t),!0===o(t))break;n=e}return r},ze=e=>C.from(e.dom.previousSibling).map(xe.fromDom),Ae=e=>C.from(e.dom.nextSibling).map(xe.fromDom),Le=e=>E(e.dom.childNodes,xe.fromDom),We=(e,t)=>{const o=e.dom.childNodes;return C.from(o[t]).map(xe.fromDom)},Me=(e,t)=>{Ne(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},je=(e,t)=>{Ae(e).fold((()=>{Ne(e).each((e=>{Ie(e,t)}))}),(e=>{Me(e,t)}))},Pe=(e,t)=>{const o=(e=>We(e,0))(e);o.fold((()=>{Ie(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Ie=(e,t)=>{e.dom.appendChild(t.dom)},Fe=(e,t)=>{Me(e,t),Ie(t,e)},He=(e,t)=>{N(t,((o,n)=>{const r=0===n?e:t[n-1];je(r,o)}))},$e=(e,t)=>{N(t,(t=>{Ie(e,t)}))},Ve=e=>{e.dom.textContent="",N(Le(e),(e=>{qe(e)}))},qe=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Ue=e=>{const t=Le(e);t.length>0&&He(e,t),qe(e)},Ge=(e,t)=>xe.fromDom(e.dom.cloneNode(t)),Ke=e=>Ge(e,!1),Ye=e=>Ge(e,!0),Je=(e,t)=>{const o=xe.fromTag(t),n=ve(e);return he(o,n),o},Qe=["tfoot","thead","tbody","colgroup"],Xe=(e,t,o)=>({element:e,rowspan:t,colspan:o}),Ze=(e,t,o)=>({element:e,cells:t,section:o}),et=(e,t,o)=>({element:e,isNew:t,isLocked:o}),tt=(e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}),ot=e=>xe.fromDom(e.dom.host),nt=e=>{const t=ie(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return(e=>{const t=(e=>xe.fromDom(e.dom.getRootNode()))(e);return de(o=t)&&m(o.dom.host)?C.some(t):C.none();var o})(xe.fromDom(t)).fold((()=>o.body.contains(t)),(n=nt,r=ot,e=>n(r(e))));var n,r},rt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return xe.fromDom(t)},st=(e,t)=>{let o=[];return N(Le(e),(e=>{t(e)&&(o=o.concat([e])),o=o.concat(st(e,t))})),o},lt=(e,t,o)=>((e,o,n)=>_(_e(e,n),(e=>Ce(e,t))))(e,0,o),at=(e,t)=>((e,o)=>_(Le(e),(e=>Ce(e,t))))(e),ct=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Te(o)?[]:E(o.querySelectorAll(e),xe.fromDom)})(t,e);var it=(e,t,o,n,r)=>e(o,n)?C.some(o):d(r)&&r(o)?C.none():t(o,n,r);const mt=(e,t,o)=>{let n=e.dom;const r=d(o)?o:y;for(;n.parentNode;){n=n.parentNode;const e=xe.fromDom(n);if(t(e))return C.some(e);if(r(e))break}return C.none()},dt=(e,t,o)=>it(((e,t)=>t(e)),mt,e,t,o),ut=(e,t,o)=>mt(e,(e=>Ce(e,t)),o),ft=(e,t)=>((e,o)=>L(e.dom.childNodes,(e=>{return o=xe.fromDom(e),Ce(o,t);var o})).map(xe.fromDom))(e),gt=(e,t)=>Se(t,e),ht=(e,t,o)=>it(((e,t)=>Ce(e,t)),ut,e,t,o),pt=(e,t,o=p)=>e.exists((e=>o(e,t))),bt=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},wt=(e,t)=>e?C.some(t):C.none(),vt=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,yt=(e,t,o=0,n)=>{const r=e.indexOf(t,o);return-1!==r&&(!!i(n)||r+t.length<=n)},xt=(e,t)=>vt(e,t,0),Ct=(e,t)=>vt(e,t,e.length-t.length),Tt=(e=>t=>t.replace(e,""))(/^\s+|\s+$/g),St=e=>e.length>0,Rt=e=>void 0!==e.style&&d(e.style.getPropertyValue),Dt=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Rt(e)&&e.style.setProperty(t,o)},Ot=(e,t,o)=>{const n=e.dom;Dt(n,t,o)},kt=(e,t)=>{const o=e.dom;G(t,((e,t)=>{Dt(o,t,e)}))},Et=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||nt(e)?n:Nt(o,t)},Nt=(e,t)=>Rt(e)?e.style.getPropertyValue(t):"",Bt=(e,t)=>{const o=e.dom,n=Nt(o,t);return C.from(n).filter((e=>e.length>0))},_t=(e,t)=>{((e,t)=>{Rt(e)&&e.style.removeProperty(t)})(e.dom,t),pt(be(e,"style").map(Tt),"")&&we(e,"style")},zt=(e,t,o=0)=>be(e,t).map((e=>parseInt(e,10))).getOr(o),At=(e,t)=>zt(e,t,1),Lt=e=>ue("col")(e)?zt(e,"span",1)>1:At(e,"colspan")>1,Wt=(e,t)=>parseInt(Et(e,t),10),Mt=g(10),jt=g(10),Pt=(e,t)=>It(e,t,x),It=(e,t,o)=>j(Le(e),(e=>Ce(e,t)?o(e)?[e]:[]:It(e,t,o))),Ft=(e,t)=>((e,t,o=y)=>o(t)?C.none():D(e,ne(t))?C.some(t):ut(t,e.join(","),(e=>Ce(e,"table")||o(e))))(["td","th"],e,t),Ht=e=>Pt(e,"th,td"),$t=e=>Ce(e,"colgroup")?at(e,"col"):j(Ut(e),(e=>at(e,"col"))),Vt=(e,t)=>ht(e,"table",t),qt=e=>Pt(e,"tr"),Ut=e=>Vt(e).fold(g([]),(e=>at(e,"colgroup"))),Gt=(e,t)=>E(e,(e=>{if("colgroup"===ne(e)){const t=E($t(e),(e=>{const t=zt(e,"span",1);return Xe(e,1,t)}));return Ze(e,t,"colgroup")}{const o=E(Ht(e),(e=>{const t=zt(e,"rowspan",1),o=zt(e,"colspan",1);return Xe(e,t,o)}));return Ze(e,o,t(e))}})),Kt=e=>Ne(e).map((e=>{const t=ne(e);return(e=>D(Qe,e))(t)?t:"tbody"})).getOr("tbody"),Yt=e=>{const t=qt(e),o=[...Ut(e),...t];return Gt(o,Kt)},Jt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},Qt=()=>Xt(0,0),Xt=(e,t)=>({major:e,minor:t}),Zt={nu:Xt,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?Qt():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return Xt(n(1),n(2))})(e,o)},unknown:Qt},eo=(e,t)=>{const o=String(t).toLowerCase();return L(e,(e=>e.search(o)))},to=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,oo=e=>t=>yt(t,e),no=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>yt(e,"edge/")&&yt(e,"chrome")&&yt(e,"safari")&&yt(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,to],search:e=>yt(e,"chrome")&&!yt(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>yt(e,"msie")||yt(e,"trident")},{name:"Opera",versionRegexes:[to,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:oo("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:oo("firefox")},{name:"Safari",versionRegexes:[to,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(yt(e,"safari")||yt(e,"mobile/"))&&yt(e,"applewebkit")}],ro=[{name:"Windows",search:oo("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>yt(e,"iphone")||yt(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:oo("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:oo("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:oo("linux"),versionRegexes:[]},{name:"Solaris",search:oo("sunos"),versionRegexes:[]},{name:"FreeBSD",search:oo("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:oo("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],so={browsers:g(no),oses:g(ro)},lo="Edge",ao="Chromium",co="Opera",io="Firefox",mo="Safari",uo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(lo),isChromium:n(ao),isIE:n("IE"),isOpera:n(co),isFirefox:n(io),isSafari:n(mo)}},fo=()=>uo({current:void 0,version:Zt.unknown()}),go=uo,ho=(g(lo),g(ao),g("IE"),g(co),g(io),g(mo),"Windows"),po="Android",bo="Linux",wo="macOS",vo="Solaris",yo="FreeBSD",xo="ChromeOS",Co=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(ho),isiOS:n("iOS"),isAndroid:n(po),isMacOS:n(wo),isLinux:n(bo),isSolaris:n(vo),isFreeBSD:n(yo),isChromeOS:n(xo)}},To=()=>Co({current:void 0,version:Zt.unknown()}),So=Co,Ro=(g(ho),g("iOS"),g(po),g(bo),g(wo),g(vo),g(yo),g(xo),e=>window.matchMedia(e).matches);let Do=Jt((()=>((e,t,o)=>{const n=so.browsers(),r=so.oses(),s=t.bind((e=>((e,t)=>V(t.brands,(t=>{const o=t.brand.toLowerCase();return L(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Zt.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>eo(e,t).map((e=>{const o=Zt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(fo,go),l=((e,t)=>eo(e,t).map((e=>{const o=Zt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(r,e).fold(To,So),a=((e,t,o,n)=>{const r=e.isiOS()&&!0===/ipad/i.test(o),s=e.isiOS()&&!r,l=e.isiOS()||e.isAndroid(),a=l||n("(pointer:coarse)"),c=r||!s&&l&&n("(min-device-width:768px)"),i=s||l&&!c,m=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),d=!i&&!c&&!m;return{isiPad:g(r),isiPhone:g(s),isTablet:g(c),isPhone:g(i),isTouch:g(a),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:g(m),isDesktop:g(d)}})(l,s,e,o);return{browser:s,os:l,deviceType:a}})(window.navigator.userAgent,C.from(window.navigator.userAgentData),Ro)));const Oo=()=>Do(),ko=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Et(o,e);return parseFloat(t)||0}return n},n=(e,t)=>A(t,((t,o)=>{const n=Et(e,o),r=void 0===n?0:parseInt(n,10);return isNaN(r)?t:t+r}),0);return{set:(t,o)=>{if(!u(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Rt(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const r=n(e,o);return t>r?t-r:0}}},Eo=(e,t,o)=>((e,t)=>(e=>{const t=parseFloat(e);return isNaN(t)?C.none():C.some(t)})(e).getOr(t))(Et(e,t),o),No=ko("width",(e=>e.dom.offsetWidth)),Bo=e=>No.get(e),_o=e=>No.getOuter(e),zo=e=>((e,t)=>{const o=e.dom,n=o.getBoundingClientRect().width||o.offsetWidth;return"border-box"===t?n:((e,t,o,n)=>t-Eo(e,`padding-${o}`,0)-Eo(e,`padding-${n}`,0)-Eo(e,`border-${o}-width`,0)-Eo(e,`border-${n}-width`,0))(e,n,"left","right")})(e,"content-box"),Ao=(e,t,o)=>{const n=e.cells,r=n.slice(0,t),s=n.slice(t),l=r.concat(o).concat(s);return Mo(e,l)},Lo=(e,t,o)=>Ao(e,t,[o]),Wo=(e,t,o)=>{e.cells[t]=o},Mo=(e,t)=>tt(e.element,t,e.section,e.isNew),jo=(e,t)=>e.cells[t],Po=(e,t)=>jo(e,t).element,Io=e=>e.cells.length,Fo=e=>{const t=B(e,(e=>"colgroup"===e.section));return{rows:t.fail,cols:t.pass}},Ho=(e,t,o)=>{const n=E(e.cells,o);return tt(t(e.element),n,e.section,!0)},$o="data-snooker-locked-cols",Vo=e=>be(e,$o).bind((e=>C.from(e.match(/\d+/g)))).map((e=>I(e,x))),qo=e=>{const t=A(Fo(e).rows,((e,t)=>(N(t.cells,((t,o)=>{t.isLocked&&(e[o]=!0)})),e)),{}),o=J(t,((e,t)=>parseInt(t,10)));return((e,t)=>{const o=T.call(e,0);return o.sort(void 0),o})(o)},Uo=(e,t)=>e+","+t,Go=(e,t)=>{const o=j(e.all,(e=>e.cells));return _(o,t)},Ko=e=>{const t={},o=[],n=H(e).map((e=>e.element)).bind(Vt).bind(Vo).getOr({});let r=0,s=0,l=0;const{pass:a,fail:c}=B(e,(e=>"colgroup"===e.section));N(c,(e=>{const a=[];N(e.cells,(e=>{let o=0;for(;void 0!==t[Uo(l,o)];)o++;const r=((e,t)=>X(e,t)&&void 0!==e[t]&&null!==e[t])(n,o.toString()),c=((e,t,o,n,r,s)=>({element:e,rowspan:t,colspan:o,row:n,column:r,isLocked:s}))(e.element,e.rowspan,e.colspan,l,o,r);for(let n=0;n<e.colspan;n++)for(let r=0;r<e.rowspan;r++){const e=o+n,a=Uo(l+r,e);t[a]=c,s=Math.max(s,e+1)}a.push(c)})),r++,o.push(Ze(e.element,a,e.section)),l++}));const{columns:i,colgroups:m}=$(a).map((e=>{const t=(e=>{const t={};let o=0;return N(e.cells,(e=>{const n=e.colspan;k(n,(r=>{const s=o+r;t[s]=((e,t,o)=>({element:e,colspan:t,column:o}))(e.element,n,s)})),o+=n})),t})(e),o=((e,t)=>({element:e,columns:t}))(e.element,Q(t));return{colgroups:[o],columns:t}})).getOrThunk((()=>({colgroups:[],columns:{}}))),d=((e,t)=>({rows:e,columns:t}))(r,s);return{grid:d,access:t,all:o,columns:i,colgroups:m}},Yo=e=>{const t=Yt(e);return Ko(t)},Jo=Ko,Qo=(e,t,o)=>C.from(e.access[Uo(t,o)]),Xo=(e,t,o)=>{const n=Go(e,(e=>o(t,e.element)));return n.length>0?C.some(n[0]):C.none()},Zo=Go,en=e=>j(e.all,(e=>e.cells)),tn=e=>Q(e.columns),on=e=>q(e.columns).length>0,nn=(e,t)=>C.from(e.columns[t]),rn=(e,t=x)=>{const o=e.grid,n=k(o.columns,h),r=k(o.rows,h);return E(n,(o=>sn((()=>j(r,(t=>Qo(e,t,o).filter((e=>e.column===o)).toArray()))),(e=>1===e.colspan&&t(e.element)),(()=>Qo(e,0,o)))))},sn=(e,t,o)=>{const n=e();return L(n,t).orThunk((()=>C.from(n[0]).orThunk(o))).map((e=>e.element))},ln=e=>{const t=e.grid,o=k(t.rows,h),n=k(t.columns,h);return E(o,(t=>sn((()=>j(n,(o=>Qo(e,t,o).filter((e=>e.row===t)).fold(g([]),(e=>[e]))))),(e=>1===e.rowspan),(()=>Qo(e,t,0)))))},an=(e,t)=>o=>"rtl"===cn(o)?t:e,cn=e=>"rtl"===Et(e,"direction")?"rtl":"ltr",mn=ko("height",(e=>{const t=e.dom;return nt(e)?t.getBoundingClientRect().height:t.offsetHeight})),dn=e=>mn.get(e),un=e=>mn.getOuter(e),fn=(e,t)=>({left:e,top:t,translate:(o,n)=>fn(e+o,t+n)}),gn=fn,hn=(e,t)=>void 0!==e?e:void 0!==t?t:0,pn=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,r=t.documentElement;if(o===e.dom)return gn(o.offsetLeft,o.offsetTop);const s=hn(null==n?void 0:n.pageYOffset,r.scrollTop),l=hn(null==n?void 0:n.pageXOffset,r.scrollLeft),a=hn(r.clientTop,o.clientTop),c=hn(r.clientLeft,o.clientLeft);return bn(e).translate(l-c,s-a)},bn=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?gn(o.offsetLeft,o.offsetTop):nt(e)?(e=>{const t=e.getBoundingClientRect();return gn(t.left,t.top)})(t):gn(0,0)},wn=(e,t)=>({row:e,y:t}),vn=(e,t)=>({col:e,x:t}),yn=e=>pn(e).left+_o(e),xn=e=>pn(e).left,Cn=(e,t)=>vn(e,xn(t)),Tn=(e,t)=>vn(e,yn(t)),Sn=e=>pn(e).top,Rn=(e,t)=>wn(e,Sn(t)),Dn=(e,t)=>wn(e,Sn(t)+un(t)),On=(e,t,o)=>{if(0===o.length)return[];const n=E(o.slice(1),((t,o)=>t.map((t=>e(o,t))))),r=o[o.length-1].map((e=>t(o.length-1,e)));return n.concat([r])},kn={delta:h,positions:e=>On(Rn,Dn,e),edge:Sn},En=an({delta:h,edge:xn,positions:e=>On(Cn,Tn,e)},{delta:e=>-e,edge:yn,positions:e=>On(Tn,Cn,e)}),Nn={delta:(e,t)=>En(t).delta(e,t),positions:(e,t)=>En(t).positions(e,t),edge:e=>En(e).edge(e)},Bn={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},_n=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),zn=/(\d+(\.\d+)?)%/,An=/(\d+(\.\d+)?)px|em/,Ln=ue("col"),Wn=ue("tr"),Mn=(e,t,o)=>{const n=Be(e).getOrThunk((()=>rt(ke(e))));return t(e)/o(n)*100},jn=(e,t)=>{Ot(e,"width",t+"px")},Pn=(e,t)=>{Ot(e,"width",t+"%")},In=(e,t)=>{Ot(e,"height",t+"px")},Fn=e=>{const t=(e=>{return Eo(t=e,"height",t.dom.offsetHeight)+"px";var t})(e);return t?((e,t,o,n)=>{const r=parseFloat(e);return Ct(e,"%")&&"table"!==ne(t)?((e,t,o,n)=>{const r=Vt(e).map((e=>{const n=o(e);return Math.floor(t/100*n)})).getOr(t);return n(e,r),r})(t,r,o,n):r})(t,e,dn,In):dn(e)},Hn=(e,t)=>Bt(e,t).orThunk((()=>be(e,t).map((e=>e+"px")))),$n=e=>Hn(e,"width"),Vn=e=>Mn(e,Bo,zo),qn=e=>{return Ln(e)?Bo(e):Eo(t=e,"width",t.dom.offsetWidth);var t},Un=e=>Wn(e)?dn(e):((e,t,o)=>o(e)/At(e,"rowspan"))(e,0,Fn),Gn=(e,t,o)=>{Ot(e,"width",t+o)},Kn=e=>Mn(e,Bo,zo)+"%",Yn=g(zn),Jn=ue("col"),Qn=e=>$n(e).getOrThunk((()=>qn(e)+"px")),Xn=e=>{return(t=e,Hn(t,"height")).getOrThunk((()=>Un(e)+"px"));var t},Zn=(e,t,o,n,r,s)=>e.filter(n).fold((()=>s(((e,t)=>{if(t<0||t>=e.length-1)return C.none();const o=e[t].fold((()=>{const o=(e=>{const t=T.call(e,0);return t.reverse(),t})(e.slice(0,t));return V(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>C.some({value:e,delta:0}))),n=e[t+1].fold((()=>{const o=e.slice(t+1);return V(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>C.some({value:e,delta:1})));return o.bind((e=>n.map((t=>{const o=t.delta+e.delta;return Math.abs(t.value-e.value)/o}))))})(o,t))),(e=>r(e))),er=(e,t,o,n)=>{const r=rn(e),s=on(e)?(e=>E(tn(e),(e=>C.from(e.element))))(e):r,l=[C.some(Nn.edge(t))].concat(E(Nn.positions(r,t),(e=>e.map((e=>e.x))))),a=w(Lt);return E(s,((e,t)=>Zn(e,t,l,a,(e=>{if((e=>{const t=Oo().browser,o=t.isChromium()||t.isFirefox();return!Jn(e)||o})(e))return o(e);{const e=null!=(s=r[t])?h(s):C.none();return Zn(e,t,l,a,(e=>n(C.some(Bo(e)))),n)}var s}),n)))},tr=e=>e.map((e=>e+"px")).getOr(""),or=(e,t,o)=>er(e,t,qn,(e=>e.getOrThunk(o.minCellWidth))),nr=(e,t,o,n)=>{const r=ln(e),s=E(e.all,(e=>C.some(e.element))),l=[C.some(kn.edge(t))].concat(E(kn.positions(r,t),(e=>e.map((e=>e.y)))));return E(s,((e,t)=>Zn(e,t,l,x,o,n)))},rr=(e,t)=>()=>nt(e)?t(e):parseFloat(Bt(e,"width").getOr("0")),sr=e=>{const t=rr(e,(e=>parseFloat(Kn(e)))),o=rr(e,Bo);return{width:t,pixelWidth:o,getWidths:(t,o)=>((e,t,o)=>er(e,t,Vn,(e=>e.fold((()=>o.minCellWidth()),(e=>e/o.pixelWidth()*100)))))(t,e,o),getCellDelta:e=>e/o()*100,singleColumnWidth:(e,t)=>[100-e],minCellWidth:()=>Mt()/o()*100,setElementWidth:Pn,adjustTableWidth:o=>{const n=t();Pn(e,n+o/100*n)},isRelative:!0,label:"percent"}},lr=e=>{const t=rr(e,Bo);return{width:t,pixelWidth:t,getWidths:(t,o)=>or(t,e,o),getCellDelta:h,singleColumnWidth:(e,t)=>[Math.max(Mt(),e+t)-e],minCellWidth:Mt,setElementWidth:jn,adjustTableWidth:o=>{const n=t()+o;jn(e,n)},isRelative:!1,label:"pixel"}},ar=e=>$n(e).fold((()=>(e=>{const t=rr(e,Bo),o=g(0);return{width:t,pixelWidth:t,getWidths:(t,o)=>or(t,e,o),getCellDelta:o,singleColumnWidth:g([0]),minCellWidth:o,setElementWidth:f,adjustTableWidth:f,isRelative:!0,label:"none"}})(e)),(t=>((e,t)=>null!==Yn().exec(t)?sr(e):lr(e))(e,t))),cr=lr,ir=sr,mr=(e,t,o)=>{const n=e[o].element,r=xe.fromTag("td");Ie(r,xe.fromTag("br")),(t?Ie:Pe)(n,r)},dr=((e,t)=>{const o=t=>e(t)?C.from(t.dom.nodeValue):C.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(ie),ur=e=>dr.get(e),fr=e=>dr.getOption(e),gr=(e,t)=>dr.set(e,t),hr=e=>"img"===ne(e)?1:fr(e).fold((()=>Le(e).length),(e=>e.length)),pr=["img","br"],br=e=>fr(e).filter((e=>0!==e.trim().length||e.indexOf("\xa0")>-1)).isSome()||D(pr,ne(e))||(e=>ae(e)&&"false"===pe(e,"contenteditable"))(e),wr=e=>((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const r=xe.fromDom(e.childNodes[n]);if(t(r))return C.some(r);const s=o(e.childNodes[n]);if(s.isSome())return s}return C.none()};return o(e.dom)})(e,br),vr=e=>yr(e,br),yr=(e,t)=>{const o=e=>{const n=Le(e);for(let e=n.length-1;e>=0;e--){const r=n[e];if(t(r))return C.some(r);const s=o(r);if(s.isSome())return s}return C.none()};return o(e)},xr={scope:["row","col"]},Cr=e=>()=>{const t=xe.fromTag("td",e.dom);return Ie(t,xe.fromTag("br",e.dom)),t},Tr=e=>()=>xe.fromTag("col",e.dom),Sr=e=>()=>xe.fromTag("colgroup",e.dom),Rr=e=>()=>xe.fromTag("tr",e.dom),Dr=(e,t,o)=>{const n=((e,t)=>{const o=Je(e,t),n=Le(Ye(e));return $e(o,n),o})(e,t);return G(o,((e,t)=>{null===e?we(n,t):ge(n,t,e)})),n},Or=e=>e,kr=(e,t,o)=>{const n=(e,t)=>{((e,t)=>{const o=e.dom,n=t.dom;Rt(o)&&Rt(n)&&(n.style.cssText=o.style.cssText)})(e.element,t),_t(t,"height"),1!==e.colspan&&_t(t,"width")};return{col:o=>{const r=xe.fromTag(ne(o.element),t.dom);return n(o,r),e(o.element,r),r},colgroup:Sr(t),row:Rr(t),cell:r=>{const s=xe.fromTag(ne(r.element),t.dom),l=o.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),a=l.length>0?((e,t,o)=>wr(e).map((n=>{const r=o.join(","),s=lt(n,r,(t=>Re(t,e)));return z(s,((e,t)=>{const o=Ke(t);return Ie(e,o),o}),t)})).getOr(t))(r.element,s,l):s;return Ie(a,xe.fromTag("br")),n(r,s),((e,t)=>{G(xr,((o,n)=>be(e,n).filter((e=>D(o,e))).each((e=>ge(t,n,e)))))})(r.element,s),e(r.element,s),s},replace:Dr,colGap:Tr(t),gap:Cr(t)}},Er=e=>({col:Tr(e),colgroup:Sr(e),row:Rr(e),cell:Cr(e),replace:Or,colGap:Tr(e),gap:Cr(e)}),Nr=e=>t=>t.options.get(e),Br="100%",_r=e=>{var t;const o=e.dom,n=null!==(t=o.getParent(e.selection.getStart(),o.isBlock))&&void 0!==t?t:e.getBody();return zo(xe.fromDom(n))+"px"},zr=e=>C.from(e.options.get("table_clone_elements")),Ar=Nr("table_header_type"),Lr=Nr("table_column_resizing"),Wr=e=>"preservetable"===Lr(e),Mr=e=>"resizetable"===Lr(e),jr=Nr("table_sizing_mode"),Pr=e=>"relative"===jr(e),Ir=e=>"fixed"===jr(e),Fr=e=>"responsive"===jr(e),Hr=Nr("table_resize_bars"),$r=Nr("table_style_by_css"),Vr=Nr("table_merge_content_on_paste"),qr=e=>{const t=e.options,o=t.get("table_default_attributes");return t.isSet("table_default_attributes")?o:((e,t)=>Fr(e)||$r(e)?t:Ir(e)?{...t,width:_r(e)}:{...t,width:Br})(e,o)},Ur=Nr("table_use_colgroups"),Gr=e=>ht(e,"[contenteditable]"),Kr=(e,t=!1)=>nt(e)?e.dom.isContentEditable:Gr(e).fold(g(t),(e=>"true"===Yr(e))),Yr=e=>e.dom.contentEditable,Jr=e=>xe.fromDom(e.getBody()),Qr=e=>t=>Re(t,Jr(e)),Xr=e=>{we(e,"data-mce-style");const t=e=>we(e,"data-mce-style");N(Ht(e),t),N($t(e),t),N(qt(e),t)},Zr=e=>xe.fromDom(e.selection.getStart()),es=e=>e.getBoundingClientRect().width,ts=e=>e.getBoundingClientRect().height,os=e=>(t,o)=>{const n=t.dom.getStyle(o,e)||t.dom.getAttrib(o,e);return C.from(n).filter(St)},ns=os("width"),rs=os("height"),ss=e=>dt(e,ue("table")).exists(Kr),ls=(e,t)=>{const o=t.column,n=t.column+t.colspan-1,r=t.row,s=t.row+t.rowspan-1;return o<=e.finishCol&&n>=e.startCol&&r<=e.finishRow&&s>=e.startRow},as=(e,t)=>t.column>=e.startCol&&t.column+t.colspan-1<=e.finishCol&&t.row>=e.startRow&&t.row+t.rowspan-1<=e.finishRow,cs=(e,t,o)=>{const n=Xo(e,t,Re),r=Xo(e,o,Re);return n.bind((e=>r.map((t=>{return o=e,n=t,{startRow:Math.min(o.row,n.row),startCol:Math.min(o.column,n.column),finishRow:Math.max(o.row+o.rowspan-1,n.row+n.rowspan-1),finishCol:Math.max(o.column+o.colspan-1,n.column+n.colspan-1)};var o,n}))))},is=(e,t,o)=>cs(e,t,o).map((t=>{const o=Zo(e,b(ls,t));return E(o,(e=>e.element))})),ms=(e,t)=>Xo(e,t,((e,t)=>De(t,e))).map((e=>e.element)),ds=(e,t,o)=>{const n=fs(e);return is(n,t,o)},us=(e,t,o,n,r)=>{const s=fs(e),l=Re(e,o)?C.some(t):ms(s,t),a=Re(e,r)?C.some(n):ms(s,n);return l.bind((e=>a.bind((t=>is(s,e,t)))))},fs=Yo;var gs=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],hs=()=>({up:g({selector:ut,closest:ht,predicate:mt,all:_e}),down:g({selector:ct,predicate:st}),styles:g({get:Et,getRaw:Bt,set:Ot,remove:_t}),attrs:g({get:pe,set:ge,remove:we,copyTo:(e,t)=>{const o=ve(e);he(t,o)}}),insert:g({before:Me,after:je,afterAll:He,append:Ie,appendAll:$e,prepend:Pe,wrap:Fe}),remove:g({unwrap:Ue,remove:qe}),create:g({nu:xe.fromTag,clone:e=>xe.fromDom(e.dom.cloneNode(!1)),text:xe.fromText}),query:g({comparePosition:(e,t)=>e.dom.compareDocumentPosition(t.dom),prevSibling:ze,nextSibling:Ae}),property:g({children:Le,name:ne,parent:Ne,document:e=>Ee(e).dom,isText:ie,isComment:le,isElement:ce,isSpecial:e=>{const t=ne(e);return D(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],t)},getLanguage:e=>ce(e)?be(e,"lang"):C.none(),getText:ur,setText:gr,isBoundary:e=>!!ce(e)&&("body"===ne(e)||D(gs,ne(e))),isEmptyTag:e=>!!ce(e)&&D(["br","img","hr","input"],ne(e)),isNonEditable:e=>ce(e)&&"false"===pe(e,"contenteditable")}),eq:Re,is:Oe});const ps=(e,t,o,n)=>{const r=t(e,o);return z(n,((o,n)=>{const r=t(e,n);return bs(e,o,r)}),r)},bs=(e,t,o)=>t.bind((t=>o.filter(b(e.eq,t)))),ws=hs(),vs=(e,t)=>((e,t,o)=>o.length>0?((e,t,o,n)=>n(e,t,o[0],o.slice(1)))(e,t,o,ps):C.none())(ws,((t,o)=>e(o)),t),ys=e=>ut(e,"table"),xs=(e,t,o)=>{const n=e=>t=>void 0!==o&&o(t)||Re(t,e);return Re(e,t)?C.some({boxes:C.some([e]),start:e,finish:t}):ys(e).bind((r=>ys(t).bind((s=>{if(Re(r,s))return C.some({boxes:ds(r,e,t),start:e,finish:t});if(De(r,s)){const o=lt(t,"td,th",n(r)),l=o.length>0?o[o.length-1]:t;return C.some({boxes:us(r,e,r,t,s),start:e,finish:l})}if(De(s,r)){const o=lt(e,"td,th",n(s)),l=o.length>0?o[o.length-1]:e;return C.some({boxes:us(s,e,r,t,s),start:e,finish:l})}return((e,t,o)=>((e,t,o,n=y)=>{const r=[t].concat(e.up().all(t)),s=[o].concat(e.up().all(o)),l=e=>W(e,n).fold((()=>e),(t=>e.slice(0,t+1))),a=l(r),c=l(s),i=L(a,(t=>O(c,((e,t)=>b(e.eq,t))(e,t))));return{firstpath:a,secondpath:c,shared:i}})(ws,e,t,void 0))(e,t).shared.bind((l=>ht(l,"table",o).bind((o=>{const l=lt(t,"td,th",n(o)),a=l.length>0?l[l.length-1]:t,c=lt(e,"td,th",n(o)),i=c.length>0?c[c.length-1]:e;return C.some({boxes:us(o,e,r,t,s),start:i,finish:a})}))))}))))},Cs=(e,t)=>{const o=ct(e,t);return o.length>0?C.some(o):C.none()},Ts=(e,t,o)=>gt(e,t).bind((t=>gt(e,o).bind((e=>vs(ys,[t,e]).map((o=>({first:t,last:e,table:o}))))))),Ss=(e,t,o,n,r)=>((e,t)=>L(e,(e=>Ce(e,t))))(e,r).bind((e=>((e,t,o)=>Vt(e).bind((n=>((e,t,o,n)=>Xo(e,t,Re).bind((t=>{const r=o>0?t.row+t.rowspan-1:t.row,s=n>0?t.column+t.colspan-1:t.column;return Qo(e,r+o,s+n).map((e=>e.element))})))(fs(n),e,t,o))))(e,t,o).bind((e=>((e,t)=>ut(e,"table").bind((o=>gt(o,t).bind((t=>xs(t,e).bind((e=>e.boxes.map((t=>({boxes:t,start:e.start,finish:e.finish}))))))))))(e,n))))),Rs=(e,t)=>Cs(e,t),Ds=(e,t,o)=>Ts(e,t,o).bind((t=>{const o=t=>Re(e,t),n="thead,tfoot,tbody,table",r=ut(t.first,n,o),s=ut(t.last,n,o);return r.bind((e=>s.bind((o=>Re(e,o)?((e,t,o)=>((e,t,o)=>cs(e,t,o).bind((t=>((e,t)=>{let o=!0;const n=b(as,t);for(let r=t.startRow;r<=t.finishRow;r++)for(let s=t.startCol;s<=t.finishCol;s++)o=o&&Qo(e,r,s).exists(n);return o?C.some(t):C.none()})(e,t))))(fs(e),t,o))(t.table,t.first,t.last):C.none()))))})),Os=h,ks=e=>{const t=(e,t)=>be(e,t).exists((e=>parseInt(e,10)>1));return e.length>0&&P(e,(e=>t(e,"rowspan")||t(e,"colspan")))?C.some(e):C.none()},Es=(e,t,o)=>t.length<=1?C.none():Ds(e,o.firstSelectedSelector,o.lastSelectedSelector).map((e=>({bounds:e,cells:t}))),Ns="data-mce-selected",Bs="data-mce-first-selected",_s="data-mce-last-selected",zs="["+Ns+"]",As={selected:Ns,selectedSelector:"td["+Ns+"],th["+Ns+"]",firstSelected:Bs,firstSelectedSelector:"td["+Bs+"],th["+Bs+"]",lastSelected:_s,lastSelectedSelector:"td["+_s+"],th["+_s+"]"},Ls=(e,t,o)=>({element:o,mergable:Es(t,e,As),unmergable:ks(e),selection:Os(e)}),Ws=e=>(t,o)=>{const n=ne(t),r="col"===n||"colgroup"===n?Vt(s=t).bind((e=>Rs(e,As.firstSelectedSelector))).fold(g(s),(e=>e[0])):t;var s;return ht(r,e,o)},Ms=Ws("th,td,caption"),js=Ws("th,td"),Ps=e=>{return t=e.model.table.getSelectedCells(),E(t,xe.fromDom);var t},Is=(e,t)=>{e.on("BeforeGetContent",(t=>{const o=o=>{t.preventDefault(),(e=>Vt(e[0]).map((e=>{const t=((e,t)=>{const o=e=>Ce(e.element,t),n=Ye(e),r=Yt(n),s=ar(e),l=Jo(r),a=((e,t)=>{const o=e.grid.columns;let n=e.grid.rows,r=o,s=0,l=0;const a=[],c=[];return G(e.access,(e=>{if(a.push(e),t(e)){c.push(e);const t=e.row,o=t+e.rowspan-1,a=e.column,i=a+e.colspan-1;t<n?n=t:o>s&&(s=o),a<r?r=a:i>l&&(l=i)}})),((e,t,o,n,r,s)=>({minRow:e,minCol:t,maxRow:o,maxCol:n,allCells:r,selectedCells:s}))(n,r,s,l,a,c)})(l,o),c="th:not("+t+"),td:not("+t+")",i=It(n,"th,td",(e=>Ce(e,c)));N(i,qe),((e,t,o,n)=>{const r=_(e,(e=>"colgroup"!==e.section)),s=t.grid.columns,l=t.grid.rows;for(let e=0;e<l;e++){let l=!1;for(let a=0;a<s;a++)e<o.minRow||e>o.maxRow||a<o.minCol||a>o.maxCol||(Qo(t,e,a).filter(n).isNone()?mr(r,l,e):l=!0)}})(r,l,a,o);const m=((e,t,o,n)=>{if(0===n.minCol&&t.grid.columns===n.maxCol+1)return 0;const r=or(t,e,o),s=A(r,((e,t)=>e+t),0),l=A(r.slice(n.minCol,n.maxCol+1),((e,t)=>e+t),0),a=l/s*o.pixelWidth()-o.pixelWidth();return o.getCellDelta(a)})(e,Yo(e),s,a);return((e,t,o,n)=>{G(o.columns,(e=>{(e.column<t.minCol||e.column>t.maxCol)&&qe(e.element)}));const r=_(Pt(e,"tr"),(e=>0===e.dom.childElementCount));N(r,qe),t.minCol!==t.maxCol&&t.minRow!==t.maxRow||N(Pt(e,"th,td"),(e=>{we(e,"rowspan"),we(e,"colspan")})),we(e,$o),we(e,"data-snooker-col-series"),ar(e).adjustTableWidth(n)})(n,a,l,m),n})(e,zs);return Xr(t),[t]})))(o).each((o=>{t.content="text"===t.format?(e=>E(e,(e=>e.dom.innerText)).join(""))(o):((e,t)=>E(t,(t=>e.selection.serializer.serialize(t.dom,{}))).join(""))(e,o)}))};if(!0===t.selection){const t=(e=>_(Ps(e),(e=>Ce(e,As.selectedSelector))))(e);t.length>=1&&o(t)}})),e.on("BeforeSetContent",(o=>{if(!0===o.selection&&!0===o.paste){const n=Ps(e);H(n).each((n=>{Vt(n).each((r=>{const s=_(((e,t)=>{const o=document.createElement("div");return o.innerHTML=e,Le(xe.fromDom(o))})(o.content),(e=>"meta"!==ne(e))),l=ue("table");if(Vr(e)&&1===s.length&&l(s[0])){o.preventDefault();const l=xe.fromDom(e.getDoc()),a=Er(l),c=((e,t,o)=>({element:e,clipboard:t,generators:o}))(n,s[0],a);t.pasteCells(r,c).each((()=>{e.focus()}))}}))}))}}))},Fs=(e,t)=>({element:e,offset:t}),Hs=(e,t,o)=>e.property().isText(t)&&0===e.property().getText(t).trim().length||e.property().isComment(t)?o(t).bind((t=>Hs(e,t,o).orThunk((()=>C.some(t))))):C.none(),$s=(e,t)=>e.property().isText(t)?e.property().getText(t).length:e.property().children(t).length,Vs=(e,t)=>{const o=Hs(e,t,e.query().prevSibling).getOr(t);if(e.property().isText(o))return Fs(o,$s(e,o));const n=e.property().children(o);return n.length>0?Vs(e,n[n.length-1]):Fs(o,$s(e,o))},qs=Vs,Us=hs(),Gs=(e,t)=>{if(!Lt(e)){const o=(e=>$n(e).bind((e=>{return t=e,o=["fixed","relative","empty"],C.from(_n.exec(t)).bind((e=>{const t=Number(e[1]),n=e[2];return((e,t)=>O(t,(t=>O(Bn[t],(t=>e===t)))))(n,o)?C.some({value:t,unit:n}):C.none()}));var t,o})))(e);o.each((o=>{const n=o.value/2;Gn(e,n,o.unit),Gn(t,n,o.unit)}))}},Ks=e=>E(e,g(0)),Ys=(e,t,o,n,r)=>r(e.slice(0,t)).concat(n).concat(r(e.slice(o))),Js=e=>(t,o,n,r)=>{if(e(n)){const e=Math.max(r,t[o]-Math.abs(n)),s=Math.abs(e-t[o]);return n>=0?s:-s}return n},Qs=Js((e=>e<0)),Xs=Js(x),Zs=()=>{const e=(e,t,o,n)=>{const r=(100+o)/100,s=Math.max(n,(e[t]+o)/r);return E(e,((e,o)=>(o===t?s:e/r)-e))},t=(t,o,n,r,s,l)=>l?e(t,o,r,s):((e,t,o,n,r)=>{const s=Qs(e,t,n,r);return Ys(e,t,o+1,[s,0],Ks)})(t,o,n,r,s);return{resizeTable:(e,t)=>e(t),clampTableDelta:Qs,calcLeftEdgeDeltas:t,calcMiddleDeltas:(e,o,n,r,s,l,a)=>t(e,n,r,s,l,a),calcRightEdgeDeltas:(t,o,n,r,s,l)=>{if(l)return e(t,n,r,s);{const e=Qs(t,n,r,s);return Ks(t.slice(0,n)).concat([e])}},calcRedestributedWidths:(e,t,o,n)=>{if(n){const n=(t+o)/t,r=E(e,(e=>e/n));return{delta:100*n-100,newSizes:r}}return{delta:o,newSizes:e}}}},el=()=>{const e=(e,t,o,n,r)=>{const s=Xs(e,n>=0?o:t,n,r);return Ys(e,t,o+1,[s,-s],Ks)};return{resizeTable:(e,t,o)=>{o&&e(t)},clampTableDelta:(e,t,o,n,r)=>{if(r){if(o>=0)return o;{const t=A(e,((e,t)=>e+t-n),0);return Math.max(-t,o)}}return Qs(e,t,o,n)},calcLeftEdgeDeltas:e,calcMiddleDeltas:(t,o,n,r,s,l)=>e(t,n,r,s,l),calcRightEdgeDeltas:(e,t,o,n,r,s)=>{if(s)return Ks(e);{const t=n/e.length;return E(e,g(t))}},calcRedestributedWidths:(e,t,o,n)=>({delta:0,newSizes:e})}},tl=e=>Yo(e).grid,ol=ue("th"),nl=e=>P(e,(e=>ol(e.element))),rl=(e,t)=>e&&t?"sectionCells":e?"section":"cells",sl=e=>{const t="thead"===e.section,o=pt(ll(e.cells),"th");return"tfoot"===e.section?{type:"footer"}:t||o?{type:"header",subType:rl(t,o)}:{type:"body"}},ll=e=>{const t=_(e,(e=>ol(e.element)));return 0===t.length?C.some("td"):t.length===e.length?C.some("th"):C.none()},al=(e,t,o)=>et(o(e.element,t),!0,e.isLocked),cl=(e,t)=>e.section!==t?tt(e.element,e.cells,t,e.isNew):e,il=()=>({transformRow:cl,transformCell:(e,t,o)=>{const n=o(e.element,t),r="td"!==ne(n)?((e,t)=>{const o=Je(e,"td");je(e,o);const n=Le(e);return $e(o,n),qe(e),o})(n):n;return et(r,e.isNew,e.isLocked)}}),ml=()=>({transformRow:cl,transformCell:al}),dl=()=>({transformRow:(e,t)=>cl(e,"thead"===t?"tbody":t),transformCell:al}),ul=il,fl=ml,gl=dl,hl=()=>({transformRow:h,transformCell:al}),pl=(e,t,o,n)=>{o===n?we(e,t):ge(e,t,o)},bl=(e,t,o)=>{$(at(e,t)).fold((()=>Pe(e,o)),(e=>je(e,o)))},wl=(e,t)=>{const o=[],n=[],r=e=>E(e,(e=>{e.isNew&&o.push(e.element);const t=e.element;return Ve(t),N(e.cells,(e=>{e.isNew&&n.push(e.element),pl(e.element,"colspan",e.colspan,1),pl(e.element,"rowspan",e.rowspan,1),Ie(t,e.element)})),t})),s=e=>j(e,(e=>E(e.cells,(e=>(pl(e.element,"span",e.colspan,1),e.element))))),l=(t,o)=>{const n=((e,t)=>{const o=ft(e,t).getOrThunk((()=>{const o=xe.fromTag(t,ke(e).dom);return"thead"===t?bl(e,"caption,colgroup",o):"colgroup"===t?bl(e,"caption",o):Ie(e,o),o}));return Ve(o),o})(e,o),l=("colgroup"===o?s:r)(t);$e(n,l)},a=(t,o)=>{t.length>0?l(t,o):(t=>{ft(e,t).each(qe)})(o)},c=[],i=[],m=[],d=[];return N(t,(e=>{switch(e.section){case"thead":c.push(e);break;case"tbody":i.push(e);break;case"tfoot":m.push(e);break;case"colgroup":d.push(e)}})),a(d,"colgroup"),a(c,"thead"),a(i,"tbody"),a(m,"tfoot"),{newRows:o,newCells:n}},vl=(e,t)=>{if(0===e.length)return 0;const o=e[0];return W(e,(e=>!t(o.element,e.element))).getOr(e.length)},yl=(e,t)=>{const o=E(e,(e=>E(e.cells,y)));return E(e,((n,r)=>{const s=j(n.cells,((n,s)=>{if(!1===o[r][s]){const m=((e,t,o,n)=>{const r=((e,t)=>e[t])(e,t),s="colgroup"===r.section,l=vl(r.cells.slice(o),n),a=s?1:vl(((e,t)=>E(e,(e=>jo(e,t))))(e.slice(t),o),n);return{colspan:l,rowspan:a}})(e,r,s,t);return((e,t,n,r)=>{for(let s=e;s<e+n;s++)for(let e=t;e<t+r;e++)o[s][e]=!0})(r,s,m.rowspan,m.colspan),[(l=n.element,a=m.rowspan,c=m.colspan,i=n.isNew,{element:l,rowspan:a,colspan:c,isNew:i})]}return[];var l,a,c,i}));return((e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}))(n.element,s,n.section,n.isNew)}))},xl=(e,t,o)=>{const n=[];N(e.colgroups,(r=>{const s=[];for(let n=0;n<e.grid.columns;n++){const r=nn(e,n).map((e=>et(e.element,o,!1))).getOrThunk((()=>et(t.colGap(),!0,!1)));s.push(r)}n.push(tt(r.element,s,"colgroup",o))}));for(let r=0;r<e.grid.rows;r++){const s=[];for(let n=0;n<e.grid.columns;n++){const l=Qo(e,r,n).map((e=>et(e.element,o,e.isLocked))).getOrThunk((()=>et(t.gap(),!0,!1)));s.push(l)}const l=e.all[r],a=tt(l.element,s,l.section,o);n.push(a)}return n},Cl=e=>yl(e,Re),Tl=(e,t)=>V(e.all,(e=>L(e.cells,(e=>Re(t,e.element))))),Sl=(e,t,o)=>{const n=E(t.selection,(t=>Ft(t).bind((t=>Tl(e,t))).filter(o))),r=bt(n);return wt(r.length>0,r)},Rl=(e,t,o,n,r)=>(s,l,a,c)=>{const i=Yo(s),m=C.from(null==c?void 0:c.section).getOrThunk(hl);return t(i,l).map((t=>{const o=((e,t)=>xl(e,t,!1))(i,a),n=e(o,t,Re,r(a),m),s=qo(n.grid);return{info:t,grid:Cl(n.grid),cursor:n.cursor,lockedColumns:s}})).bind((e=>{const t=wl(s,e.grid),r=C.from(null==c?void 0:c.sizing).getOrThunk((()=>ar(s))),l=C.from(null==c?void 0:c.resize).getOrThunk(el);return o(s,e.grid,e.info,{sizing:r,resize:l,section:m}),n(s),we(s,$o),e.lockedColumns.length>0&&ge(s,$o,e.lockedColumns.join(",")),C.some({cursor:e.cursor,newRows:t.newRows,newCells:t.newCells})}))},Dl=(e,t)=>Sl(e,t,x).map((e=>({cells:e,generators:t.generators,clipboard:t.clipboard}))),Ol=(e,t)=>Sl(e,t,x),kl=(e,t)=>Sl(e,t,(e=>!e.isLocked)),El=(e,t)=>P(t,(t=>((e,t)=>Tl(e,t).exists((e=>!e.isLocked)))(e,t))),Nl=(e,t,o,n)=>{const r=Fo(e).rows;let s=!0;for(let e=0;e<r.length;e++)for(let l=0;l<Io(r[0]);l++){const a=r[e],c=jo(a,l),i=o(c.element,t);i&&!s?Wo(a,l,et(n(),!0,c.isLocked)):i&&(s=!1)}return e},Bl=e=>{const t=t=>t(e),o=g(e),n=()=>r,r={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:x,isError:y,map:t=>zl.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>C.some(e)};return r},_l=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:y,isError:x,map:t,mapError:t=>zl.error(t(e)),bind:t,exists:y,forall:x,getOr:h,or:h,getOrThunk:v,orThunk:v,getOrDie:(n=String(e),()=>{throw new Error(n)}),each:f,toOptional:C.none};var n;return o},zl={value:Bl,error:_l,fromOption:(e,t)=>e.fold((()=>_l(t)),Bl)},Al=(e,t)=>({rowDelta:0,colDelta:Io(e[0])-Io(t[0])}),Ll=(e,t)=>({rowDelta:e.length-t.length,colDelta:0}),Wl=(e,t,o,n)=>{const r="colgroup"===t.section?o.col:o.cell;return k(e,(e=>et(r(),!0,n(e))))},Ml=(e,t,o,n)=>{const r=e[e.length-1];return e.concat(k(t,(()=>{const e="colgroup"===r.section?o.colgroup:o.row,t=Ho(r,e,h),s=Wl(t.cells.length,t,o,(e=>X(n,e.toString())));return Mo(t,s)})))},jl=(e,t,o,n)=>E(e,(e=>{const r=Wl(t,e,o,y);return Ao(e,n,r)})),Pl=(e,t,o)=>{const n=t.colDelta<0?jl:h,r=t.rowDelta<0?Ml:h,s=qo(e),l=Io(e[0]),a=O(s,(e=>e===l-1)),c=n(e,Math.abs(t.colDelta),o,a?l-1:l),i=qo(c);return r(c,Math.abs(t.rowDelta),o,I(i,x))},Il=(e,t,o,n)=>{const r=b(n,jo(e[t],o).element),s=e[t];return e.length>1&&Io(s)>1&&(o>0&&r(Po(s,o-1))||o<s.cells.length-1&&r(Po(s,o+1))||t>0&&r(Po(e[t-1],o))||t<e.length-1&&r(Po(e[t+1],o)))},Fl=(e,t,o)=>_(o,(o=>o>=e.column&&o<=Io(t[0])+e.column)),Hl=(e,t,o,n,r)=>{((e,t,o,n)=>{t>0&&t<e[0].cells.length&&N(e,(e=>{const r=e.cells[t-1];let s=0;const l=n();for(;e.cells.length>t+s&&o(r.element,e.cells[t+s].element);)Wo(e,t+s,et(l,!0,e.cells[t+s].isLocked)),s++}))})(t,e,r,n.cell);const s=Ll(o,t),l=Pl(o,s,n),a=Ll(t,l),c=Pl(t,a,n);return E(c,((t,o)=>Ao(t,e,l[o].cells)))},$l=(e,t,o,n,r)=>{((e,t,o,n)=>{const r=Fo(e).rows;if(t>0&&t<r.length){const e=((e,t)=>A(e,((e,o)=>O(e,(e=>t(e.element,o.element)))?e:e.concat([o])),[]))(r[t-1].cells,o);N(e,(e=>{let s=C.none();for(let l=t;l<r.length;l++)for(let t=0;t<Io(r[0]);t++){const a=r[l],c=jo(a,t);o(c.element,e.element)&&(s.isNone()&&(s=C.some(n())),s.each((e=>{Wo(a,t,et(e,!0,c.isLocked))})))}}))}})(t,e,r,n.cell);const s=qo(t),l=Al(t,o),a={...l,colDelta:l.colDelta-s.length},c=Pl(t,a,n),{cols:i,rows:m}=Fo(c),d=qo(c),u=Al(o,t),f={...u,colDelta:u.colDelta+d.length},g=(p=n,b=d,E(o,(e=>A(b,((t,o)=>{const n=Wl(1,e,p,x)[0];return Lo(t,o,n)}),e)))),h=Pl(g,f,n);var p,b;return[...i,...m.slice(0,e),...h,...m.slice(e,m.length)]},Vl=(e,t,o,n,r)=>{const{rows:s,cols:l}=Fo(e),a=s.slice(0,t),c=s.slice(t);return[...l,...a,((e,t,o,n)=>Ho(e,(e=>n(e,o)),t))(s[o],((e,o)=>t>0&&t<s.length&&n(Po(s[t-1],o),Po(s[t],o))?jo(s[t],o):et(r(e.element,n),!0,e.isLocked)),n,r),...c]},ql=(e,t,o,n,r)=>E(e,(e=>{const s=t>0&&t<Io(e)&&n(Po(e,t-1),Po(e,t)),l=((e,t,o,n,r,s,l)=>{if("colgroup"!==o&&n)return jo(e,t);{const t=jo(e,r);return et(l(t.element,s),!0,!1)}})(e,t,e.section,s,o,n,r);return Lo(e,t,l)})),Ul=(e,t,o,n)=>((e,t,o,n)=>void 0!==Po(e[t],o)&&t>0&&n(Po(e[t-1],o),Po(e[t],o)))(e,t,o,n)||((e,t,o)=>t>0&&o(Po(e,t-1),Po(e,t)))(e[t],o,n),Gl=(e,t,o,n)=>{const r=e=>(e=>"row"===e?(e=>At(e,"rowspan")>1)(t):Lt(t))(e)?`${e}group`:e;return e?ol(t)?r(o):null:n&&ol(t)?r("row"===o?"col":"row"):null},Kl=(e,t,o)=>et(o(e.element,t),!0,e.isLocked),Yl=(e,t,o,n,r,s,l)=>E(e,((e,a)=>((e,c)=>{const i=e.cells,m=E(i,((e,c)=>{if((e=>O(t,(t=>o(e.element,t.element))))(e)){const t=l(e,a,c)?r(e,o,n):e;return s(t,a,c).each((e=>{var o,n;o=t.element,n={scope:C.from(e)},G(n,((e,t)=>{e.fold((()=>{we(o,t)}),(e=>{fe(o.dom,t,e)}))}))})),t}return e}));return tt(e.element,m,e.section,e.isNew)})(e))),Jl=(e,t,o)=>j(e,((n,r)=>Ul(e,r,t,o)?[]:[jo(n,t)])),Ql=(e,t,o,n,r)=>{const s=Fo(e).rows,l=j(t,(e=>Jl(s,e,n))),a=E(s,(e=>nl(e.cells))),c=((e,t)=>P(t,h)&&nl(e)?x:(e,o,n)=>!("th"===ne(e.element)&&t[o]))(l,a),i=((e,t)=>(o,n)=>C.some(Gl(e,o.element,"row",t[n])))(o,a);return Yl(e,l,n,r,Kl,i,c)},Xl=(e,t,o,n)=>{const r=Fo(e).rows,s=E(t,(e=>jo(r[e.row],e.column)));return Yl(e,s,o,n,Kl,C.none,x)},Zl=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return N(e,((n,r)=>{const s=q(n);if(1!==s.length)throw new Error("one and only one name per case");const a=s[0],c=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(c))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==c.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+c.length+" ("+c+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,o)},match:e=>{const n=q(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!P(t,(e=>D(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o},ea={...Zl([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}])},ta=(e,t,o)=>{const n=((e,t)=>on(e)?((e,t)=>{const o=tn(e);return E(o,((e,o)=>({element:e.element,width:t[o],colspan:e.colspan})))})(e,t):((e,t)=>{const o=en(e);return E(o,(e=>{const o=((e,t,o)=>{let n=0;for(let r=e;r<t;r++)n+=void 0!==o[r]?o[r]:0;return n})(e.column,e.column+e.colspan,t);return{element:e.element,width:o,colspan:e.colspan}}))})(e,t))(e,t);N(n,(e=>{o.setElementWidth(e.element,e.width)}))},oa=(e,t,o,n,r)=>{const s=Yo(e),l=r.getCellDelta(t),a=r.getWidths(s,r),c=o===s.grid.columns-1,i=n.clampTableDelta(a,o,l,r.minCellWidth(),c),m=((e,t,o,n,r)=>{const s=e.slice(0),l=((e,t)=>0===e.length?ea.none():1===e.length?ea.only(0):0===t?ea.left(0,1):t===e.length-1?ea.right(t-1,t):t>0&&t<e.length-1?ea.middle(t-1,t,t+1):ea.none())(e,t),a=g(E(s,g(0)));return l.fold(a,(e=>n.singleColumnWidth(s[e],o)),((e,t)=>r.calcLeftEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)),((e,t,l)=>r.calcMiddleDeltas(s,e,t,l,o,n.minCellWidth(),n.isRelative)),((e,t)=>r.calcRightEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)))})(a,o,i,r,n),d=E(m,((e,t)=>e+a[t]));ta(s,d,r),n.resizeTable(r.adjustTableWidth,i,c)},na=(e,t,o)=>{const n=Yo(e),r=((e,t)=>nr(e,t,Un,(e=>e.getOrThunk(jt))))(n,e),s=E(r,((e,n)=>o===n?Math.max(t+e,jt()):e)),l=((e,t)=>E(e.all,((e,o)=>({element:e.element,height:t[o]}))))(n,s);N(l,(e=>{In(e.element,e.height)})),N(en(n),(e=>{(e=>{_t(e,"height")})(e.element)}));const a=z(s,((e,t)=>e+t),0);In(e,a)},ra=e=>A(e,((e,t)=>O(e,(e=>e.column===t.column))?e:e.concat([t])),[]).sort(((e,t)=>e.column-t.column)),sa=ue("col"),la=ue("colgroup"),aa=e=>"tr"===ne(e)||la(e),ca=e=>({element:e,colspan:zt(e,"colspan",1),rowspan:zt(e,"rowspan",1)}),ia=e=>be(e,"scope").map((e=>e.substr(0,3))),ma=(e,t=ca)=>{const o=o=>{if(aa(o))return la((r={element:o}).element)?e.colgroup(r):e.row(r);{const r=o,s=(t=>sa(t.element)?e.col(t):e.cell(t))(t(r));return n=C.some({item:r,replacement:s}),s}var r};let n=C.none();return{getOrInit:(e,t)=>n.fold((()=>o(e)),(n=>t(e,n.item)?n.replacement:o(e)))}},da=e=>t=>{const o=[],n=n=>{const r="td"===e?{scope:null}:{},s=t.replace(n,e,r);return o.push({item:n,sub:s}),s};return{replaceOrInit:(e,t)=>{if(aa(e)||sa(e))return e;{const r=e;return((e,t)=>L(o,(o=>t(o.item,e))))(r,t).fold((()=>n(r)),(o=>t(e,o.item)?o.sub:n(r)))}}}},ua=e=>({unmerge:t=>{const o=ia(t);return o.each((e=>ge(t,"scope",e))),()=>{const n=e.cell({element:t,colspan:1,rowspan:1});return _t(n,"width"),_t(t,"width"),o.each((e=>ge(n,"scope",e))),n}},merge:e=>(_t(e[0],"width"),(()=>{const t=bt(E(e,ia));if(0===t.length)return C.none();{const e=t[0],o=["row","col"];return O(t,(t=>t!==e&&D(o,t)))?C.none():C.from(e)}})().fold((()=>we(e[0],"scope")),(t=>ge(e[0],"scope",t+"group"))),g(e[0]))}),fa=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],ga=hs(),ha=e=>((e,t)=>{const o=e.property().name(t);return D(fa,o)})(ga,e),pa=e=>((e,t)=>{const o=e.property().name(t);return D(["ol","ul"],o)})(ga,e),ba=e=>{const t=ue("br"),o=e=>vr(e).bind((o=>{const n=Ae(o).map((e=>!!ha(e)||!!((e,t)=>D(["br","img","hr","input"],e.property().name(t)))(ga,e)&&"img"!==ne(e))).getOr(!1);return Ne(o).map((r=>{return!0===n||("li"===ne(s=r)||mt(s,pa).isSome())||t(o)||ha(r)&&!Re(e,r)?[]:[xe.fromTag("br")];var s}))})).getOr([]),n=(()=>{const n=j(e,(e=>{const n=Le(e);return(e=>P(e,(e=>t(e)||ie(e)&&0===ur(e).trim().length)))(n)?[]:n.concat(o(e))}));return 0===n.length?[xe.fromTag("br")]:n})();Ve(e[0]),$e(e[0],n)},wa=e=>Kr(e,!0),va=e=>{0===Ht(e).length&&qe(e)},ya=(e,t)=>({grid:e,cursor:t}),xa=(e,t,o)=>{const n=((e,t,o)=>{var n,r;const s=Fo(e).rows;return C.from(null===(r=null===(n=s[t])||void 0===n?void 0:n.cells[o])||void 0===r?void 0:r.element).filter(wa).orThunk((()=>(e=>V(e,(e=>V(e.cells,(e=>{const t=e.element;return wt(wa(t),t)})))))(s)))})(e,t,o);return ya(e,n)},Ca=e=>A(e,((e,t)=>O(e,(e=>e.row===t.row))?e:e.concat([t])),[]).sort(((e,t)=>e.row-t.row)),Ta=(e,t)=>(o,n,r,s,l)=>{const a=Ca(n),c=E(a,(e=>e.row)),i=((e,t,o,n,r,s,l)=>{const{cols:a,rows:c}=Fo(e),i=c[t[0]],m=j(t,(e=>((e,t,o)=>{const n=e[t];return j(n.cells,((n,r)=>Ul(e,t,r,o)?[]:[n]))})(c,e,r))),d=E(i.cells,((e,t)=>nl(Jl(c,t,r)))),u=[...c];N(t,(e=>{u[e]=l.transformRow(c[e],o)}));const f=[...a,...u],g=((e,t)=>P(t,h)&&nl(e.cells)?x:(e,o,n)=>!("th"===ne(e.element)&&t[n]))(i,d),p=((e,t)=>(o,n,r)=>C.some(Gl(e,o.element,"col",t[r])))(n,d);return Yl(f,m,r,s,l.transformCell,p,g)})(o,c,e,t,r,s.replaceOrInit,l);return xa(i,n[0].row,n[0].column)},Sa=Ta("thead",!0),Ra=Ta("tbody",!1),Da=Ta("tfoot",!1),Oa=(e,t,o)=>{const n=((e,t)=>Gt(e,(()=>t)))(e,o.section),r=Jo(n);return xl(r,t,!0)},ka=(e,t,o,n)=>((e,t,o,n)=>{const r=Jo(t),s=n.getWidths(r,n);ta(r,s,n)})(0,t,0,n.sizing),Ea=(e,t,o,n)=>((e,t,o,n,r)=>{const s=Jo(t),l=n.getWidths(s,n),a=n.pixelWidth(),{newSizes:c,delta:i}=r.calcRedestributedWidths(l,a,o.pixelDelta,n.isRelative);ta(s,c,n),n.adjustTableWidth(i)})(0,t,o,n.sizing,n.resize),Na=(e,t)=>O(t,(e=>0===e.column&&e.isLocked)),Ba=(e,t)=>O(t,(t=>t.column+t.colspan>=e.grid.columns&&t.isLocked)),_a=(e,t)=>{const o=rn(e),n=ra(t);return A(n,((e,t)=>e+o[t.column].map(_o).getOr(0)),0)},za=e=>(t,o)=>Ol(t,o).filter((o=>!(e?Na:Ba)(t,o))).map((e=>({details:e,pixelDelta:_a(t,e)}))),Aa=e=>(t,o)=>Dl(t,o).filter((o=>!(e?Na:Ba)(t,o.cells))),La=da("th"),Wa=da("td"),Ma=Rl(((e,t,o,n)=>{const r=t[0].row,s=Ca(t),l=z(s,((e,t)=>({grid:Vl(e.grid,r,t.row+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return xa(l,r,t[0].column)}),Ol,f,f,ma),ja=Rl(((e,t,o,n)=>{const r=Ca(t),s=r[r.length-1],l=s.row+s.rowspan,a=z(r,((e,t)=>Vl(e,l,t.row,o,n.getOrInit)),e);return xa(a,l,t[0].column)}),Ol,f,f,ma),Pa=Rl(((e,t,o,n)=>{const r=t.details,s=ra(r),l=s[0].column,a=z(s,((e,t)=>({grid:ql(e.grid,l,t.column+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return xa(a,r[0].row,l)}),za(!0),Ea,f,ma),Ia=Rl(((e,t,o,n)=>{const r=t.details,s=r[r.length-1],l=s.column+s.colspan,a=ra(r),c=z(a,((e,t)=>ql(e,l,t.column,o,n.getOrInit)),e);return xa(c,r[0].row,l)}),za(!1),Ea,f,ma),Fa=Rl(((e,t,o,n)=>{const r=ra(t.details),s=((e,t)=>j(e,(e=>{const o=e.cells,n=z(t,((e,t)=>t>=0&&t<e.length?e.slice(0,t).concat(e.slice(t+1)):e),o);return n.length>0?[tt(e.element,n,e.section,e.isNew)]:[]})))(e,E(r,(e=>e.column))),l=s.length>0?s[0].cells.length-1:0;return xa(s,r[0].row,Math.min(r[0].column,l))}),((e,t)=>kl(e,t).map((t=>({details:t,pixelDelta:-_a(e,t)})))),Ea,va,ma),Ha=Rl(((e,t,o,n)=>{const r=Ca(t),s=((e,t,o)=>{const{rows:n,cols:r}=Fo(e);return[...r,...n.slice(0,t),...n.slice(o+1)]})(e,r[0].row,r[r.length-1].row),l=Math.max(Fo(s).rows.length-1,0);return xa(s,Math.min(t[0].row,l),t[0].column)}),Ol,f,va,ma),$a=Rl(((e,t,o,n)=>{const r=ra(t),s=E(r,(e=>e.column)),l=Ql(e,s,!0,o,n.replaceOrInit);return xa(l,t[0].row,t[0].column)}),kl,f,f,La),Va=Rl(((e,t,o,n)=>{const r=ra(t),s=E(r,(e=>e.column)),l=Ql(e,s,!1,o,n.replaceOrInit);return xa(l,t[0].row,t[0].column)}),kl,f,f,Wa),qa=Rl(Sa,kl,f,f,La),Ua=Rl(Ra,kl,f,f,Wa),Ga=Rl(Da,kl,f,f,Wa),Ka=Rl(((e,t,o,n)=>{const r=Xl(e,t,o,n.replaceOrInit);return xa(r,t[0].row,t[0].column)}),kl,f,f,La),Ya=Rl(((e,t,o,n)=>{const r=Xl(e,t,o,n.replaceOrInit);return xa(r,t[0].row,t[0].column)}),kl,f,f,Wa),Ja=Rl(((e,t,o,n)=>{const r=t.cells;ba(r);const s=((e,t,o,n)=>{const r=Fo(e).rows;if(0===r.length)return e;for(let e=t.startRow;e<=t.finishRow;e++)for(let o=t.startCol;o<=t.finishCol;o++){const t=r[e],s=jo(t,o).isLocked;Wo(t,o,et(n(),!1,s))}return e})(e,t.bounds,0,n.merge(r));return ya(s,C.from(r[0]))}),((e,t)=>((e,t)=>t.mergable)(0,t).filter((t=>El(e,t.cells)))),ka,f,ua),Qa=Rl(((e,t,o,n)=>{const r=z(t,((e,t)=>Nl(e,t,o,n.unmerge(t))),e);return ya(r,C.from(t[0]))}),((e,t)=>((e,t)=>t.unmergable)(0,t).filter((t=>El(e,t)))),ka,f,ua),Xa=Rl(((e,t,o,n)=>{const r=((e,t)=>{const o=Yo(e);return xl(o,t,!0)})(t.clipboard,t.generators);var s,l;return((e,t,o,n,r)=>{const s=qo(t),l=((e,t,o)=>{const n=Io(t[0]),r=Fo(t).cols.length+e.row,s=k(n-e.column,(t=>t+e.column));return{row:r,column:L(s,(e=>P(o,(t=>t!==e)))).getOr(n-1)}})(e,t,s),a=Fo(o).rows,c=Fl(l,a,s),i=((e,t,o)=>{if(e.row>=t.length||e.column>Io(t[0]))return zl.error("invalid start address out of table bounds, row: "+e.row+", column: "+e.column);const n=t.slice(e.row),r=n[0].cells.slice(e.column),s=Io(o[0]),l=o.length;return zl.value({rowDelta:n.length-l,colDelta:r.length-s})})(l,t,a);return i.map((e=>{const o={...e,colDelta:e.colDelta-c.length},s=Pl(t,o,n),i=qo(s),m=Fl(l,a,i);return((e,t,o,n,r,s)=>{const l=e.row,a=e.column,c=l+o.length,i=a+Io(o[0])+s.length,m=I(s,x);for(let e=l;e<c;e++){let s=0;for(let c=a;c<i;c++){if(m[c]){s++;continue}Il(t,e,c,r)&&Nl(t,Po(t[e],c),r,n.cell);const i=c-a-s,d=jo(o[e-l],i),u=d.element,f=n.replace(u);Wo(t[e],c,et(f,!0,d.isLocked))}}return t})(l,s,a,n,r,m)}))})((s=t.row,l=t.column,{row:s,column:l}),e,r,t.generators,o).fold((()=>ya(e,C.some(t.element))),(e=>xa(e,t.row,t.column)))}),((e,t)=>Ft(t.element).bind((o=>Tl(e,o).map((e=>({...e,generators:t.generators,clipboard:t.clipboard})))))),ka,f,ma),Za=Rl(((e,t,o,n)=>{const r=Fo(e).rows,s=t.cells[0].column,l=r[t.cells[0].row],a=Oa(t.clipboard,t.generators,l),c=Hl(s,e,a,t.generators,o);return xa(c,t.cells[0].row,t.cells[0].column)}),Aa(!0),f,f,ma),ec=Rl(((e,t,o,n)=>{const r=Fo(e).rows,s=t.cells[t.cells.length-1].column+t.cells[t.cells.length-1].colspan,l=r[t.cells[0].row],a=Oa(t.clipboard,t.generators,l),c=Hl(s,e,a,t.generators,o);return xa(c,t.cells[0].row,s)}),Aa(!1),f,f,ma),tc=Rl(((e,t,o,n)=>{const r=Fo(e).rows,s=t.cells[0].row,l=r[s],a=Oa(t.clipboard,t.generators,l),c=$l(s,e,a,t.generators,o);return xa(c,t.cells[0].row,t.cells[0].column)}),Dl,f,f,ma),oc=Rl(((e,t,o,n)=>{const r=Fo(e).rows,s=t.cells[t.cells.length-1].row+t.cells[t.cells.length-1].rowspan,l=r[t.cells[0].row],a=Oa(t.clipboard,t.generators,l),c=$l(s,e,a,t.generators,o);return xa(c,s,t.cells[0].column)}),Dl,f,f,ma),nc=(e,t)=>{const o=Yo(e);return Ol(o,t).bind((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=M(E(o.all,(e=>_(e.cells,(e=>e.column>=n&&e.column<r)))));return ll(s)})).getOr("")},rc=(e,t)=>{const o=Yo(e);return Ol(o,t).bind(ll).getOr("")},sc=(e,t)=>{const o=Yo(e);return Ol(o,t).bind((e=>{const t=e[e.length-1],n=e[0].row,r=t.row+t.rowspan;return(e=>{const t=E(e,(e=>sl(e).type)),o=D(t,"header"),n=D(t,"footer");if(o||n){const e=D(t,"body");return!o||e||n?o||e||!n?C.none():C.some("footer"):C.some("header")}return C.some("body")})(o.all.slice(n,r))})).getOr("")},lc=(e,t)=>e.dispatch("NewRow",{node:t}),ac=(e,t)=>e.dispatch("NewCell",{node:t}),cc=(e,t,o)=>{e.dispatch("TableModified",{...o,table:t})},ic={structure:!1,style:!0},mc={structure:!0,style:!1},dc={structure:!0,style:!0},uc=(e,t)=>Pr(e)?ir(t):Ir(e)?cr(t):ar(t),fc=(e,t,o)=>{const n=e=>"table"===ne(Jr(e)),r=zr(e),s=Mr(e)?f:Gs,l=t=>{switch(Ar(e)){case"section":return ul();case"sectionCells":return fl();case"cells":return gl();default:return((e,t)=>{var o;switch((o=Yo(e),V(o.all,(e=>{const t=sl(e);return"header"===t.type?C.from(t.subType):C.none()}))).getOr(t)){case"section":return il();case"sectionCells":return ml();case"cells":return dl()}})(t,"section")}},a=(n,s,a,c)=>(i,m,d=!1)=>{Xr(i);const u=xe.fromDom(e.getDoc()),f=kr(a,u,r),g={sizing:uc(e,i),resize:Mr(e)?Zs():el(),section:l(i)};return s(i)?n(i,m,f,g).bind((n=>{t.refresh(i.dom),N(n.newRows,(t=>{lc(e,t.dom)})),N(n.newCells,(t=>{ac(e,t.dom)}));const r=((t,n)=>n.cursor.fold((()=>{const n=Ht(t);return H(n).filter(nt).map((n=>{o.clearSelectedCells(t.dom);const r=e.dom.createRng();return r.selectNode(n.dom),e.selection.setRng(r),ge(n,"data-mce-selected","1"),r}))}),(n=>{const r=qs(Us,n),s=e.dom.createRng();return s.setStart(r.element.dom,r.offset),s.setEnd(r.element.dom,r.offset),e.selection.setRng(s),o.clearSelectedCells(t.dom),C.some(s)})))(i,n);return nt(i)&&(Xr(i),d||cc(e,i.dom,c)),r.map((e=>({rng:e,effect:c})))})):C.none()},c=a(Ha,(t=>!n(e)||tl(t).rows>1),f,mc),i=a(Fa,(t=>!n(e)||tl(t).columns>1),f,mc);return{deleteRow:c,deleteColumn:i,insertRowsBefore:a(Ma,x,f,mc),insertRowsAfter:a(ja,x,f,mc),insertColumnsBefore:a(Pa,x,s,mc),insertColumnsAfter:a(Ia,x,s,mc),mergeCells:a(Ja,x,f,mc),unmergeCells:a(Qa,x,f,mc),pasteColsBefore:a(Za,x,f,mc),pasteColsAfter:a(ec,x,f,mc),pasteRowsBefore:a(tc,x,f,mc),pasteRowsAfter:a(oc,x,f,mc),pasteCells:a(Xa,x,f,dc),makeCellsHeader:a(Ka,x,f,mc),unmakeCellsHeader:a(Ya,x,f,mc),makeColumnsHeader:a($a,x,f,mc),unmakeColumnsHeader:a(Va,x,f,mc),makeRowsHeader:a(qa,x,f,mc),makeRowsBody:a(Ua,x,f,mc),makeRowsFooter:a(Ga,x,f,mc),getTableRowType:sc,getTableCellType:rc,getTableColType:nc}},gc=(e,t,o)=>{const n=zt(e,t,1);1===o||n<=1?we(e,t):ge(e,t,Math.min(o,n))},hc=(e,t)=>o=>{const n=o.column+o.colspan-1,r=o.column;return n>=e&&r<t},pc=Zl([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),bc=(e,t,o)=>{const n=o.substring(0,o.length-e.length),r=parseFloat(n);return n===r.toString()?t(r):pc.invalid(o)},wc={...pc,from:e=>Ct(e,"%")?bc("%",pc.percent,e):Ct(e,"px")?bc("px",pc.pixels,e):pc.invalid(e)},vc=(e,t,o)=>{const n=wc.from(o),r=P(e,(e=>"0px"===e))?((e,t)=>{const o=e.fold((()=>g("")),(e=>g(e/t+"px")),(()=>g(100/t+"%")));return k(t,o)})(n,e.length):((e,t,o)=>e.fold((()=>t),(e=>((e,t,o)=>{const n=o/t;return E(e,(e=>wc.from(e).fold((()=>e),(e=>e*n+"px"),(e=>e/100*o+"px"))))})(t,o,e)),(e=>((e,t)=>E(e,(e=>wc.from(e).fold((()=>e),(e=>e/t*100+"%"),(e=>e+"%")))))(t,o))))(n,e,t);return Cc(r)},yc=(e,t)=>0===e.length?t:z(e,((e,t)=>wc.from(t).fold(g(0),h,h)+e),0),xc=(e,t)=>wc.from(e).fold(g(e),(e=>e+t+"px"),(e=>e+t+"%")),Cc=e=>{if(0===e.length)return e;const t=z(e,((e,t)=>{const o=wc.from(t).fold((()=>({value:t,remainder:0})),(e=>((e,t)=>{const o=Math.floor(e);return{value:o+"px",remainder:e-o}})(e)),(e=>({value:e+"%",remainder:0})));return{output:[o.value].concat(e.output),remainder:e.remainder+o.remainder}}),{output:[],remainder:0}),o=t.output;return o.slice(0,o.length-1).concat([xc(o[o.length-1],Math.round(t.remainder))])},Tc=wc.from,Sc=(e,t,o)=>{const n=Yo(e),r=n.all,s=en(n),l=tn(n);t.each((t=>{const o=Tc(t).fold(g("px"),g("px"),g("%")),r=Bo(e),a=((e,t)=>er(e,t,Qn,tr))(n,e),c=vc(a,r,t);on(n)?((e,t,o)=>{N(t,((t,n)=>{const r=yc([e[n]],Mt());Ot(t.element,"width",r+o)}))})(c,l,o):((e,t,o)=>{N(t,(t=>{const n=e.slice(t.column,t.colspan+t.column),r=yc(n,Mt());Ot(t.element,"width",r+o)}))})(c,s,o),Ot(e,"width",t)})),o.each((t=>{const o=dn(e),l=((e,t)=>nr(e,t,Xn,tr))(n,e);((e,t,o)=>{N(o,(e=>{_t(e.element,"height")})),N(t,((t,o)=>{Ot(t.element,"height",e[o])}))})(vc(l,o,t),r,s),Ot(e,"height",t)}))},Rc=e=>$n(e).exists((e=>zn.test(e))),Dc=e=>$n(e).exists((e=>An.test(e))),Oc=e=>$n(e).isNone(),kc=e=>{we(e,"width"),we(e,"height")},Ec=e=>{const t=Kn(e);Sc(e,C.some(t),C.none()),kc(e)},Nc=e=>{const t=(e=>Bo(e)+"px")(e);Sc(e,C.some(t),C.none()),kc(e)},Bc=e=>{_t(e,"width");const t=$t(e),o=t.length>0?t:Ht(e);N(o,(e=>{_t(e,"width"),kc(e)})),kc(e)},_c={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},colGroups:!1},zc=(e,t,o,n)=>k(e,(e=>((e,t,o,n)=>{const r=xe.fromTag("tr");for(let s=0;s<e;s++){const e=xe.fromTag(n<t||s<o?"th":"td");s<o&&ge(e,"scope","row"),n<t&&ge(e,"scope","col"),Ie(e,xe.fromTag("br")),Ie(r,e)}return r})(t,o,n,e))),Ac=(e,t)=>{e.selection.select(t.dom,!0),e.selection.collapse(!0)},Lc=(e,t,o,n,s)=>{const l=(e=>{const t=e.options,o=t.get("table_default_styles");return t.isSet("table_default_styles")?o:((e,t)=>Fr(e)||!$r(e)?t:Ir(e)?{...t,width:_r(e)}:{...t,width:Br})(e,o)})(e),a={styles:l,attributes:qr(e),colGroups:Ur(e)};return e.undoManager.ignore((()=>{const r=((e,t,o,n,r,s=_c)=>{const l=xe.fromTag("table"),a="cells"!==r;kt(l,s.styles),he(l,s.attributes),s.colGroups&&Ie(l,(e=>{const t=xe.fromTag("colgroup");return k(e,(()=>Ie(t,xe.fromTag("col")))),t})(t));const c=Math.min(e,o);if(a&&o>0){const e=xe.fromTag("thead");Ie(l,e);const s=zc(o,t,"sectionCells"===r?c:0,n);$e(e,s)}const i=xe.fromTag("tbody");Ie(l,i);const m=zc(a?e-c:e,t,a?0:o,n);return $e(i,m),l})(o,t,s,n,Ar(e),a);ge(r,"data-mce-id","__mce");const l=(e=>{const t=xe.fromTag("div"),o=xe.fromDom(e.dom.cloneNode(!0));return Ie(t,o),(e=>e.dom.innerHTML)(t)})(r);e.insertContent(l),e.addVisual()})),gt(Jr(e),'table[data-mce-id="__mce"]').map((t=>(Ir(e)?Nc(t):Fr(e)?Bc(t):(Pr(e)||(e=>r(e)&&-1!==e.indexOf("%"))(l.width))&&Ec(t),Xr(t),we(t,"data-mce-id"),((e,t)=>{N(ct(t,"tr"),(t=>{lc(e,t.dom),N(ct(t,"th,td"),(t=>{ac(e,t.dom)}))}))})(e,t),((e,t)=>{gt(t,"td,th").each(b(Ac,e))})(e,t),t.dom))).getOrNull()};var Wc=tinymce.util.Tools.resolve("tinymce.FakeClipboard");const Mc="x-tinymce/dom-table-",jc=Mc+"rows",Pc=Mc+"columns",Ic=e=>{const t=Wc.FakeClipboardItem(e);Wc.write([t])},Fc=e=>{var t;const o=null!==(t=Wc.read())&&void 0!==t?t:[];return V(o,(t=>C.from(t.getType(e))))},Hc=e=>{Fc(e).isSome()&&Wc.clear()},$c=e=>{e.fold(qc,(e=>Ic({[jc]:e})))},Vc=()=>Fc(jc),qc=()=>Hc(jc),Uc=e=>{e.fold(Kc,(e=>Ic({[Pc]:e})))},Gc=()=>Fc(Pc),Kc=()=>Hc(Pc),Yc=e=>Ms(Zr(e),Qr(e)).filter(ss),Jc=(e,t)=>{const o=Qr(e),n=e=>Vt(e,o),l=t=>(e=>js(Zr(e),Qr(e)).filter(ss))(e).bind((e=>n(e).map((o=>t(o,e))))),a=t=>{e.focus()},c=(t,o=!1)=>l(((n,r)=>{const s=Ls(Ps(e),n,r);t(n,s,o).each(a)})),i=()=>l(((t,o)=>((e,t,o)=>{const n=Yo(e);return Ol(n,t).bind((e=>{const t=xl(n,o,!1),r=Fo(t).rows.slice(e[0].row,e[e.length-1].row+e[e.length-1].rowspan),s=j(r,(e=>{const t=_(e.cells,(e=>!e.isLocked));return t.length>0?[{...e,cells:t}]:[]})),l=Cl(s);return wt(l.length>0,l)})).map((e=>E(e,(e=>{const t=Ke(e.element);return N(e.cells,(e=>{const o=Ye(e.element);pl(o,"colspan",e.colspan,1),pl(o,"rowspan",e.rowspan,1),Ie(t,o)})),t}))))})(t,Ls(Ps(e),t,o),kr(f,xe.fromDom(e.getDoc()),C.none())))),m=()=>l(((t,o)=>((e,t)=>{const o=Yo(e);return kl(o,t).map((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=((e,t,o)=>{if(on(e)){const n=_(tn(e),hc(t,o)),r=E(n,(e=>{const n=Ye(e.element);return gc(n,"span",o-t),n})),s=xe.fromTag("colgroup");return $e(s,r),[s]}return[]})(o,n,r),l=((e,t,o)=>E(e.all,(e=>{const n=_(e.cells,hc(t,o)),r=E(n,(e=>{const n=Ye(e.element);return gc(n,"colspan",o-t),n})),s=xe.fromTag("tr");return $e(s,r),s})))(o,n,r);return[...s,...l]}))})(t,Ls(Ps(e),t,o)))),d=(t,o)=>o().each((o=>{const n=E(o,(e=>Ye(e)));l(((o,r)=>{const s=Er(xe.fromDom(e.getDoc())),l=((e,t,o,n)=>({selection:Os(e),clipboard:o,generators:n}))(Ps(e),0,n,s);t(o,l).each(a)}))})),g=e=>(t,o)=>((e,t)=>X(e,t)?C.from(e[t]):C.none())(o,"type").each((t=>{c(e(t),o.no_events)}));G({mceTableSplitCells:()=>c(t.unmergeCells),mceTableMergeCells:()=>c(t.mergeCells),mceTableInsertRowBefore:()=>c(t.insertRowsBefore),mceTableInsertRowAfter:()=>c(t.insertRowsAfter),mceTableInsertColBefore:()=>c(t.insertColumnsBefore),mceTableInsertColAfter:()=>c(t.insertColumnsAfter),mceTableDeleteCol:()=>c(t.deleteColumn),mceTableDeleteRow:()=>c(t.deleteRow),mceTableCutCol:()=>m().each((e=>{Uc(e),c(t.deleteColumn)})),mceTableCutRow:()=>i().each((e=>{$c(e),c(t.deleteRow)})),mceTableCopyCol:()=>m().each((e=>Uc(e))),mceTableCopyRow:()=>i().each((e=>$c(e))),mceTablePasteColBefore:()=>d(t.pasteColsBefore,Gc),mceTablePasteColAfter:()=>d(t.pasteColsAfter,Gc),mceTablePasteRowBefore:()=>d(t.pasteRowsBefore,Vc),mceTablePasteRowAfter:()=>d(t.pasteRowsAfter,Vc),mceTableDelete:()=>Yc(e).each((t=>{Vt(t,o).filter(w(o)).each((t=>{const o=xe.fromText("");if(je(t,o),qe(t),e.dom.isEmpty(e.getBody()))e.setContent(""),e.selection.setCursorLocation();else{const t=e.dom.createRng();t.setStart(o.dom,0),t.setEnd(o.dom,0),e.selection.setRng(t),e.nodeChanged()}}))})),mceTableCellToggleClass:(t,o)=>{l((t=>{const n=Ps(e),r=P(n,(t=>e.formatter.match("tablecellclass",{value:o},t.dom))),s=r?e.formatter.remove:e.formatter.apply;N(n,(e=>s("tablecellclass",{value:o},e.dom))),cc(e,t.dom,ic)}))},mceTableToggleClass:(t,o)=>{l((t=>{e.formatter.toggle("tableclass",{value:o},t.dom),cc(e,t.dom,ic)}))},mceTableToggleCaption:()=>{Yc(e).each((t=>{Vt(t,o).each((o=>{ft(o,"caption").fold((()=>{const t=xe.fromTag("caption");Ie(t,xe.fromText("Caption")),((e,t,o)=>{We(e,0).fold((()=>{Ie(e,t)}),(e=>{Me(e,t)}))})(o,t),e.selection.setCursorLocation(t.dom,0)}),(n=>{ue("caption")(t)&&Se("td",o).each((t=>e.selection.setCursorLocation(t.dom,0))),qe(n)})),cc(e,o.dom,mc)}))}))},mceTableSizingMode:(t,n)=>(t=>Yc(e).each((n=>{Fr(e)||Ir(e)||Pr(e)||Vt(n,o).each((o=>{"relative"!==t||Rc(o)?"fixed"!==t||Dc(o)?"responsive"!==t||Oc(o)||Bc(o):Nc(o):Ec(o),Xr(o),cc(e,o.dom,mc)}))})))(n),mceTableCellType:g((e=>"th"===e?t.makeCellsHeader:t.unmakeCellsHeader)),mceTableColType:g((e=>"th"===e?t.makeColumnsHeader:t.unmakeColumnsHeader)),mceTableRowType:g((e=>{switch(e){case"header":return t.makeRowsHeader;case"footer":return t.makeRowsFooter;default:return t.makeRowsBody}}))},((t,o)=>e.addCommand(o,t))),e.addCommand("mceInsertTable",((t,o)=>{((e,t,o,n={})=>{const r=e=>u(e)&&e>0;if(r(t)&&r(o)){const r=n.headerRows||0,s=n.headerColumns||0;return Lc(e,o,t,s,r)}console.error("Invalid values for mceInsertTable - rows and columns values are required to insert a table.")})(e,o.rows,o.columns,o.options)})),e.addCommand("mceTableApplyCellStyle",((t,o)=>{const l=e=>"tablecell"+e.toLowerCase().replace("-","");if(!s(o))return;const a=_(Ps(e),ss);if(0===a.length)return;const c=((e,t)=>{const o={};return((e,t,o,n)=>{G(e,((e,r)=>{(t(e,r)?o:n)(e,r)}))})(e,t,(e=>(t,o)=>{e[o]=t})(o),f),o})(o,((t,o)=>e.formatter.has(l(o))&&r(t)));(e=>{for(const t in e)if(U.call(e,t))return!1;return!0})(c)||(G(c,((t,o)=>{const n=l(o);N(a,(o=>{""===t?e.formatter.remove(n,{value:null},o.dom,!0):e.formatter.apply(n,{value:t},o.dom)}))})),n(a[0]).each((t=>cc(e,t.dom,ic))))}))},Qc=Zl([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Xc={before:Qc.before,on:Qc.on,after:Qc.after,cata:(e,t,o,n)=>e.fold(t,o,n),getStart:e=>e.fold(h,h,h)},Zc=(e,t)=>({selection:e,kill:t}),ei=(e,t)=>{const o=e.document.createRange();return o.selectNode(t.dom),o},ti=(e,t)=>{const o=e.document.createRange();return oi(o,t),o},oi=(e,t)=>e.selectNodeContents(t.dom),ni=(e,t,o)=>{const n=e.document.createRange();var r;return r=n,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},ri=(e,t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},si=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),li=Zl([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),ai=(e,t,o)=>t(xe.fromDom(o.startContainer),o.startOffset,xe.fromDom(o.endContainer),o.endOffset),ci=(e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:g(e),rtl:C.none}),relative:(t,o)=>({ltr:Jt((()=>ni(e,t,o))),rtl:Jt((()=>C.some(ni(e,o,t))))}),exact:(t,o,n,r)=>({ltr:Jt((()=>ri(e,t,o,n,r))),rtl:Jt((()=>C.some(ri(e,n,r,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>li.rtl(xe.fromDom(e.endContainer),e.endOffset,xe.fromDom(e.startContainer),e.startOffset))).getOrThunk((()=>ai(0,li.ltr,o))):ai(0,li.ltr,o)})(0,o)},ii=(e,t)=>ci(e,t).match({ltr:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},rtl:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(n.dom,r),s.setEnd(t.dom,o),s}});li.ltr,li.rtl;const mi=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),di=(e,t,o,n)=>({start:Xc.on(e,t),finish:Xc.on(o,n)}),ui=(e,t)=>{const o=ii(e,t);return mi(xe.fromDom(o.startContainer),o.startOffset,xe.fromDom(o.endContainer),o.endOffset)},fi=di,gi=(e,t,o,n,r)=>Re(o,n)?C.none():xs(o,n,t).bind((t=>{const n=t.boxes.getOr([]);return n.length>1?(r(e,n,t.start,t.finish),C.some(Zc(C.some(fi(o,0,o,hr(o))),!0))):C.none()})),hi=(e,t)=>({item:e,mode:t}),pi=(e,t,o,n=bi)=>e.property().parent(t).map((e=>hi(e,n))),bi=(e,t,o,n=wi)=>o.sibling(e,t).map((e=>hi(e,n))),wi=(e,t,o,n=wi)=>{const r=e.property().children(t);return o.first(r).map((e=>hi(e,n)))},vi=[{current:pi,next:bi,fallback:C.none()},{current:bi,next:wi,fallback:C.some(pi)},{current:wi,next:wi,fallback:C.some(bi)}],yi=(e,t,o,n,r=vi)=>L(r,(e=>e.current===o)).bind((o=>o.current(e,t,n,o.next).orThunk((()=>o.fallback.bind((o=>yi(e,t,o,n))))))),xi=(e,t,o,n,r,s)=>yi(e,t,n,r).bind((t=>s(t.item)?C.none():o(t.item)?C.some(t.item):xi(e,t.item,o,t.mode,r,s))),Ci=e=>t=>0===e.property().children(t).length,Ti=(e,t,o,n)=>xi(e,t,o,bi,{sibling:(e,t)=>e.query().prevSibling(t),first:e=>e.length>0?C.some(e[e.length-1]):C.none()},n),Si=(e,t,o,n)=>xi(e,t,o,bi,{sibling:(e,t)=>e.query().nextSibling(t),first:e=>e.length>0?C.some(e[0]):C.none()},n),Ri=hs(),Di=(e,t)=>((e,t,o)=>Ti(e,t,Ci(e),o))(Ri,e,t),Oi=(e,t)=>((e,t,o)=>Si(e,t,Ci(e),o))(Ri,e,t),ki=Zl([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),Ei=e=>ht(e,"tr"),Ni={...ki,verify:(e,t,o,n,r,s,l)=>ht(n,"td,th",l).bind((o=>ht(t,"td,th",l).map((t=>Re(o,t)?Re(n,o)&&hr(o)===r?s(t):ki.none("in same cell"):vs(Ei,[o,t]).fold((()=>((e,t,o)=>{const n=e.getRect(t),r=e.getRect(o);return r.right>n.left&&r.left<n.right})(e,t,o)?ki.success():s(t)),(e=>s(t))))))).getOr(ki.none("default")),cata:(e,t,o,n,r)=>e.fold(t,o,n,r)},Bi=ue("br"),_i=(e,t,o)=>t(e,o).bind((e=>ie(e)&&0===ur(e).trim().length?_i(e,t,o):C.some(e))),zi=(e,t,o,n)=>((e,t)=>We(e,t).filter(Bi).orThunk((()=>We(e,t-1).filter(Bi))))(t,o).bind((t=>n.traverse(t).fold((()=>_i(t,n.gather,e).map(n.relative)),(e=>(e=>Ne(e).bind((t=>{const o=Le(t);return((e,t)=>W(e,b(Re,t)))(o,e).map((n=>((e,t,o,n)=>({parent:e,children:t,element:o,index:n}))(t,o,e,n)))})))(e).map((e=>Xc.on(e.parent,e.index))))))),Ai=(e,t)=>({left:e.left,top:e.top+t,right:e.right,bottom:e.bottom+t}),Li=(e,t)=>({left:e.left,top:e.top-t,right:e.right,bottom:e.bottom-t}),Wi=(e,t,o)=>({left:e.left+t,top:e.top+o,right:e.right+t,bottom:e.bottom+o}),Mi=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom}),ji=(e,t)=>C.some(e.getRect(t)),Pi=(e,t,o)=>ce(t)?ji(e,t).map(Mi):ie(t)?((e,t,o)=>o>=0&&o<hr(t)?e.getRangedRect(t,o,t,o+1):o>0?e.getRangedRect(t,o-1,t,o):C.none())(e,t,o).map(Mi):C.none(),Ii=(e,t)=>ce(t)?ji(e,t).map(Mi):ie(t)?e.getRangedRect(t,0,t,hr(t)).map(Mi):C.none(),Fi=Zl([{none:[]},{retry:["caret"]}]),Hi=(e,t,o)=>dt(t,ha).fold(y,(t=>Ii(e,t).exists((e=>((e,t)=>e.left<t.left||Math.abs(t.right-e.left)<1||e.left>t.right)(o,e))))),$i={point:e=>e.bottom,adjuster:(e,t,o,n,r)=>{const s=Ai(r,5);return Math.abs(o.bottom-n.bottom)<1||o.top>r.bottom?Fi.retry(s):o.top===r.bottom?Fi.retry(Ai(r,1)):Hi(e,t,r)?Fi.retry(Wi(s,5,0)):Fi.none()},move:Ai,gather:Oi},Vi=(e,t,o,n,r)=>0===r?C.some(n):((e,t,o)=>e.elementFromPoint(t,o).filter((e=>"table"===ne(e))).isSome())(e,n.left,t.point(n))?((e,t,o,n,r)=>Vi(e,t,o,t.move(n,5),r))(e,t,o,n,r-1):e.situsFromPoint(n.left,t.point(n)).bind((s=>s.start.fold(C.none,(s=>Ii(e,s).bind((l=>t.adjuster(e,s,l,o,n).fold(C.none,(n=>Vi(e,t,o,n,r-1))))).orThunk((()=>C.some(n)))),C.none))),qi=(e,t,o)=>{const n=e.move(o,5),r=Vi(t,e,o,n,100).getOr(n);return((e,t,o)=>e.point(t)>o.getInnerHeight()?C.some(e.point(t)-o.getInnerHeight()):e.point(t)<0?C.some(-e.point(t)):C.none())(e,r,t).fold((()=>t.situsFromPoint(r.left,e.point(r))),(o=>(t.scrollBy(0,o),t.situsFromPoint(r.left,e.point(r)-o))))},Ui={tryUp:b(qi,{point:e=>e.top,adjuster:(e,t,o,n,r)=>{const s=Li(r,5);return Math.abs(o.top-n.top)<1||o.bottom<r.top?Fi.retry(s):o.bottom===r.top?Fi.retry(Li(r,1)):Hi(e,t,r)?Fi.retry(Wi(s,5,0)):Fi.none()},move:Li,gather:Di}),tryDown:b(qi,$i),getJumpSize:g(5)},Gi=(e,t,o)=>e.getSelection().bind((n=>((e,t,o,n)=>{const r=Bi(t)?((e,t,o)=>o.traverse(t).orThunk((()=>_i(t,o.gather,e))).map(o.relative))(e,t,n):zi(e,t,o,n);return r.map((e=>({start:e,finish:e})))})(t,n.finish,n.foffset,o).fold((()=>C.some(Fs(n.finish,n.foffset))),(r=>{const s=e.fromSitus(r);return l=Ni.verify(e,n.finish,n.foffset,s.finish,s.foffset,o.failure,t),Ni.cata(l,(e=>C.none()),(()=>C.none()),(e=>C.some(Fs(e,0))),(e=>C.some(Fs(e,hr(e)))));var l})))),Ki=(e,t,o,n,r,s)=>0===s?C.none():Qi(e,t,o,n,r).bind((l=>{const a=e.fromSitus(l),c=Ni.verify(e,o,n,a.finish,a.foffset,r.failure,t);return Ni.cata(c,(()=>C.none()),(()=>C.some(l)),(l=>Re(o,l)&&0===n?Yi(e,o,n,Li,r):Ki(e,t,l,0,r,s-1)),(l=>Re(o,l)&&n===hr(l)?Yi(e,o,n,Ai,r):Ki(e,t,l,hr(l),r,s-1)))})),Yi=(e,t,o,n,r)=>Pi(e,t,o).bind((t=>Ji(e,r,n(t,Ui.getJumpSize())))),Ji=(e,t,o)=>{const n=Oo().browser;return n.isChromium()||n.isSafari()||n.isFirefox()?t.retry(e,o):C.none()},Qi=(e,t,o,n,r)=>Pi(e,o,n).bind((t=>Ji(e,r,t))),Xi=(e,t,o,n,r)=>ht(n,"td,th",t).bind((n=>ht(n,"table",t).bind((s=>((e,t)=>mt(e,(e=>Ne(e).exists((e=>Re(e,t)))),void 0).isSome())(r,s)?((e,t,o)=>Gi(e,t,o).bind((n=>Ki(e,t,n.element,n.offset,o,20).map(e.fromSitus))))(e,t,o).bind((e=>ht(e.finish,"td,th",t).map((t=>({start:n,finish:t,range:e}))))):C.none())))),Zi=(e,t,o,n,r,s)=>s(n,t).orThunk((()=>Xi(e,t,o,n,r).map((e=>{const t=e.range;return Zc(C.some(fi(t.start,t.soffset,t.finish,t.foffset)),!0)})))),em=(e,t)=>ht(e,"tr",t).bind((e=>ht(e,"table",t).bind((o=>{const n=ct(o,"tr");return Re(e,n[0])?((e,t,o)=>Ti(Ri,e,(e=>vr(e).isSome()),o))(o,0,t).map((e=>{const t=hr(e);return Zc(C.some(fi(e,t,e,t)),!0)})):C.none()})))),tm=(e,t)=>ht(e,"tr",t).bind((e=>ht(e,"table",t).bind((o=>{const n=ct(o,"tr");return Re(e,n[n.length-1])?((e,t,o)=>Si(Ri,e,(e=>wr(e).isSome()),o))(o,0,t).map((e=>Zc(C.some(fi(e,0,e,0)),!0))):C.none()})))),om=(e,t,o,n,r,s,l)=>Xi(e,o,n,r,s).bind((e=>gi(t,o,e.start,e.finish,l))),nm=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},rm=()=>{const e=(e=>{const t=nm(C.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(C.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(C.some(e))}}})(f);return{...e,on:t=>e.get().each(t)}},sm=(e,t)=>ht(e,"td,th",t),lm=e=>Be(e).exists(Kr),am={traverse:Ae,gather:Oi,relative:Xc.before,retry:Ui.tryDown,failure:Ni.failedDown},cm={traverse:ze,gather:Di,relative:Xc.before,retry:Ui.tryUp,failure:Ni.failedUp},im=e=>t=>t===e,mm=im(38),dm=im(40),um=e=>e>=37&&e<=40,fm={isBackward:im(37),isForward:im(39)},gm={isBackward:im(39),isForward:im(37)},hm=Zl([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),pm={domRange:hm.domRange,relative:hm.relative,exact:hm.exact,exactFromRange:e=>hm.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>xe.fromDom(e.startContainer),relative:(e,t)=>Xc.getStart(e),exact:(e,t,o,n)=>e}))(e);return xe.fromDom(Ee(t).dom.defaultView)},range:mi},bm=(e,t)=>{const o=ne(e);return"input"===o?Xc.after(e):D(["br","img"],o)?0===t?Xc.before(e):Xc.after(e):Xc.on(e,t)},wm=e=>C.from(e.getSelection()),vm=(e,t)=>{wm(e).each((e=>{e.removeAllRanges(),e.addRange(t)}))},ym=(e,t,o,n,r)=>{const s=ri(e,t,o,n,r);vm(e,s)},xm=(e,t)=>ci(e,t).match({ltr:(t,o,n,r)=>{ym(e,t,o,n,r)},rtl:(t,o,n,r)=>{wm(e).each((s=>{if(s.setBaseAndExtent)s.setBaseAndExtent(t.dom,o,n.dom,r);else if(s.extend)try{((e,t,o,n,r,s)=>{t.collapse(o.dom,n),t.extend(r.dom,s)})(0,s,t,o,n,r)}catch(s){ym(e,n,r,t,o)}else ym(e,n,r,t,o)}))}}),Cm=(e,t,o,n,r)=>{const s=((e,t,o,n)=>{const r=bm(e,t),s=bm(o,n);return pm.relative(r,s)})(t,o,n,r);xm(e,s)},Tm=(e,t,o)=>{const n=((e,t)=>{const o=e.fold(Xc.before,bm,Xc.after),n=t.fold(Xc.before,bm,Xc.after);return pm.relative(o,n)})(t,o);xm(e,n)},Sm=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return C.some(mi(xe.fromDom(t.startContainer),t.startOffset,xe.fromDom(o.endContainer),o.endOffset))}return C.none()},Rm=e=>{if(null===e.anchorNode||null===e.focusNode)return Sm(e);{const t=xe.fromDom(e.anchorNode),o=xe.fromDom(e.focusNode);return((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=ke(e).dom.createRange();return r.setStart(e.dom,t),r.setEnd(o.dom,n),r})(e,t,o,n),s=Re(e,o)&&t===n;return r.collapsed&&!s})(t,e.anchorOffset,o,e.focusOffset)?C.some(mi(t,e.anchorOffset,o,e.focusOffset)):Sm(e)}},Dm=(e,t,o=!0)=>{const n=(o?ti:ei)(e,t);vm(e,n)},Om=e=>(e=>wm(e).filter((e=>e.rangeCount>0)).bind(Rm))(e).map((e=>pm.exact(e.start,e.soffset,e.finish,e.foffset))),km=(e,t,o)=>((e,t,o)=>((e,t,o)=>e.caretPositionFromPoint?((e,t,o)=>{var n;return C.from(null===(n=e.caretPositionFromPoint)||void 0===n?void 0:n.call(e,t,o)).bind((t=>{if(null===t.offsetNode)return C.none();const o=e.createRange();return o.setStart(t.offsetNode,t.offset),o.collapse(),C.some(o)}))})(e,t,o):e.caretRangeFromPoint?((e,t,o)=>{var n;return C.from(null===(n=e.caretRangeFromPoint)||void 0===n?void 0:n.call(e,t,o))})(e,t,o):C.none())(e.document,t,o).map((e=>mi(xe.fromDom(e.startContainer),e.startOffset,xe.fromDom(e.endContainer),e.endOffset))))(e,t,o),Em=e=>({elementFromPoint:(t,o)=>xe.fromPoint(xe.fromDom(e.document),t,o),getRect:e=>e.dom.getBoundingClientRect(),getRangedRect:(t,o,n,r)=>{const s=pm.exact(t,o,n,r);return((e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?C.some(o).map(si):C.none()})(ii(e,t)))(e,s)},getSelection:()=>Om(e).map((t=>ui(e,t))),fromSitus:t=>{const o=pm.relative(t.start,t.finish);return ui(e,o)},situsFromPoint:(t,o)=>km(e,t,o).map((e=>di(e.start,e.soffset,e.finish,e.foffset))),clearSelection:()=>{(e=>{wm(e).each((e=>e.removeAllRanges()))})(e)},collapseSelection:(t=!1)=>{Om(e).each((o=>o.fold((e=>e.collapse(t)),((o,n)=>{const r=t?o:n;Tm(e,r,r)}),((o,n,r,s)=>{const l=t?o:r,a=t?n:s;Cm(e,l,a,l,a)}))))},setSelection:t=>{Cm(e,t.start,t.soffset,t.finish,t.foffset)},setRelativeSelection:(t,o)=>{Tm(e,t,o)},selectNode:t=>{Dm(e,t,!1)},selectContents:t=>{Dm(e,t)},getInnerHeight:()=>e.innerHeight,getScrollY:()=>(e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return gn(o,n)})(xe.fromDom(e.document)).top,scrollBy:(t,o)=>{((e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollBy(e,t)})(t,o,xe.fromDom(e.document))}}),Nm=(e,t)=>({rows:e,cols:t}),Bm=e=>dt(e,ae).exists(Kr),_m=(e,t)=>Bm(e)||Bm(t),zm=e=>void 0!==e.dom.classList,Am=(e,t)=>((e,t,o)=>{const n=((e,t)=>{const o=pe(e,t);return void 0===o||""===o?[]:o.split(" ")})(e,t).concat([o]);return ge(e,t,n.join(" ")),!0})(e,"class",t),Lm=(e,t)=>{zm(e)?e.dom.classList.add(t):Am(e,t)},Wm=(e,t)=>zm(e)&&e.dom.classList.contains(t),Mm=()=>({tag:"none"}),jm=e=>({tag:"multiple",elements:e}),Pm=e=>({tag:"single",element:e}),Im=e=>{const t=xe.fromDom((e=>{if(m(e.target)){const t=xe.fromDom(e.target);if(ce(t)&&m(t.dom.shadowRoot)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return H(t)}}return C.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),r=(s=n,l=o,(...e)=>s(l.apply(null,e)));var s,l;return((e,t,o,n,r,s,l)=>({target:e,x:t,y:o,stop:n,prevent:r,kill:s,raw:l}))(t,e.clientX,e.clientY,o,n,r,e)},Fm=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Hm=x,$m=(e,t,o)=>((e,t,o,n)=>((e,t,o,n,r)=>{const s=((e,t)=>o=>{e(o)&&t(Im(o))})(o,n);return e.dom.addEventListener(t,s,r),{unbind:b(Fm,e,t,s,r)}})(e,t,o,n,!1))(e,t,Hm,o),Vm=Im,qm=e=>!Wm(xe.fromDom(e.target),"ephox-snooker-resizer-bar"),Um=(e,t)=>{const o=(r=As.selectedSelector,{get:()=>Rs(xe.fromDom(e.getBody()),r).fold((()=>js(Zr(e),Qr(e)).fold(Mm,Pm)),jm)}),n=((e,t,o)=>{const n=t=>{we(t,e.selected),we(t,e.firstSelected),we(t,e.lastSelected)},r=t=>{ge(t,e.selected,"1")},s=e=>{l(e),o()},l=t=>{const o=ct(t,`${e.selectedSelector},${e.firstSelectedSelector},${e.lastSelectedSelector}`);N(o,n)};return{clearBeforeUpdate:l,clear:s,selectRange:(o,n,l,a)=>{s(o),N(n,r),ge(l,e.firstSelected,"1"),ge(a,e.lastSelected,"1"),t(n,l,a)},selectedSelector:e.selectedSelector,firstSelectedSelector:e.firstSelectedSelector,lastSelectedSelector:e.lastSelectedSelector}})(As,((t,o,n)=>{Vt(o).each((r=>{const s=E(t,(e=>e.dom)),l=zr(e),a=kr(f,xe.fromDom(e.getDoc()),l),c=((e,t,o)=>{const n=Yo(e);return Ol(n,t).map((e=>{const t=xl(n,o,!1),{rows:r}=Fo(t),s=((e,t)=>{const o=e.slice(0,t[t.length-1].row+1),n=Cl(o);return j(n,(e=>{const o=e.cells.slice(0,t[t.length-1].column+1);return E(o,(e=>e.element))}))})(r,e),l=((e,t)=>{const o=e.slice(t[0].row+t[0].rowspan-1,e.length),n=Cl(o);return j(n,(e=>{const o=e.cells.slice(t[0].column+t[0].colspan-1,e.cells.length);return E(o,(e=>e.element))}))})(r,e);return{upOrLeftCells:s,downOrRightCells:l}}))})(r,{selection:Ps(e)},a).map((e=>K(e,(e=>E(e,(e=>e.dom)))))).getOrUndefined();((e,t,o,n,r)=>{e.dispatch("TableSelectionChange",{cells:t,start:o,finish:n,otherCells:r})})(e,s,o.dom,n.dom,c)}))}),(()=>(e=>{e.dispatch("TableSelectionClear")})(e)));var r;return e.on("init",(o=>{const r=e.getWin(),s=Jr(e),l=Qr(e),a=((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=rm(),s=r.clear,l=s=>{r.on((r=>{n.clearBeforeUpdate(t),sm(s.target,o).each((l=>{xs(r,l,o).each((o=>{const r=o.boxes.getOr([]);if(1===r.length){const e=r[0],o="false"===Yr(e),l=pt(Gr(s.target),e,Re);o&&l&&n.selectRange(t,r,e,e)}else r.length>1&&(n.selectRange(t,r,o.start,o.finish),e.selectContents(l))}))}))}))};return{clearstate:s,mousedown:e=>{n.clear(t),sm(e.target,o).filter(lm).each(r.set)},mouseover:e=>{l(e)},mouseup:e=>{l(e),s()}}})(Em(e),t,o,n);return{clearstate:r.clearstate,mousedown:r.mousedown,mouseover:r.mouseover,mouseup:r.mouseup}})(r,s,l,n),c=((e,t,o,n)=>{const r=Em(e),s=()=>(n.clear(t),C.none());return{keydown:(e,l,a,c,i,m)=>{const d=e.raw,u=d.which,f=!0===d.shiftKey,g=Cs(t,n.selectedSelector).fold((()=>(um(u)&&!f&&n.clearBeforeUpdate(t),um(u)&&f&&!_m(l,c)?C.none:dm(u)&&f?b(om,r,t,o,am,c,l,n.selectRange):mm(u)&&f?b(om,r,t,o,cm,c,l,n.selectRange):dm(u)?b(Zi,r,o,am,c,l,tm):mm(u)?b(Zi,r,o,cm,c,l,em):C.none)),(e=>{const o=o=>()=>{const s=V(o,(o=>((e,t,o,n,r)=>Ss(n,e,t,r.firstSelectedSelector,r.lastSelectedSelector).map((e=>(r.clearBeforeUpdate(o),r.selectRange(o,e.boxes,e.start,e.finish),e.boxes))))(o.rows,o.cols,t,e,n)));return s.fold((()=>Ts(t,n.firstSelectedSelector,n.lastSelectedSelector).map((e=>{const o=dm(u)||m.isForward(u)?Xc.after:Xc.before;return r.setRelativeSelection(Xc.on(e.first,0),o(e.table)),n.clear(t),Zc(C.none(),!0)}))),(e=>C.some(Zc(C.none(),!0))))};return um(u)&&f&&!_m(l,c)?C.none:dm(u)&&f?o([Nm(1,0)]):mm(u)&&f?o([Nm(-1,0)]):m.isBackward(u)&&f?o([Nm(0,-1),Nm(-1,0)]):m.isForward(u)&&f?o([Nm(0,1),Nm(1,0)]):um(u)&&!f?s:C.none}));return g()},keyup:(e,r,s,l,a)=>Cs(t,n.selectedSelector).fold((()=>{const c=e.raw,i=c.which;return!0===c.shiftKey&&um(i)&&_m(r,l)?((e,t,o,n,r,s,l)=>Re(o,r)&&n===s?C.none():ht(o,"td,th",t).bind((o=>ht(r,"td,th",t).bind((n=>gi(e,t,o,n,l))))))(t,o,r,s,l,a,n.selectRange):C.none()}),C.none)}})(r,s,l,n),i=((e,t,o,n)=>{const r=Em(e);return(e,s)=>{n.clearBeforeUpdate(t),xs(e,s,o).each((e=>{const o=e.boxes.getOr([]);n.selectRange(t,o,e.start,e.finish),r.selectContents(s),r.collapseSelection()}))}})(r,s,l,n);e.on("TableSelectorChange",(e=>i(e.start,e.finish)));const m=(t,o)=>{(e=>!0===e.raw.shiftKey)(t)&&(o.kill&&t.kill(),o.selection.each((t=>{const o=pm.relative(t.start,t.finish),n=ii(r,o);e.selection.setRng(n)})))},d=e=>0===e.button,u=(()=>{const e=nm(xe.fromDom(s)),t=nm(0);return{touchEnd:o=>{const n=xe.fromDom(o.target);if(ue("td")(n)||ue("th")(n)){const r=e.get(),s=t.get();Re(r,n)&&o.timeStamp-s<300&&(o.preventDefault(),i(n,n))}e.set(n),t.set(o.timeStamp)}}})();e.on("dragstart",(e=>{a.clearstate()})),e.on("mousedown",(e=>{d(e)&&qm(e)&&a.mousedown(Vm(e))})),e.on("mouseover",(e=>{var t;void 0!==(t=e).buttons&&0==(1&t.buttons)||!qm(e)||a.mouseover(Vm(e))})),e.on("mouseup",(e=>{d(e)&&qm(e)&&a.mouseup(Vm(e))})),e.on("touchend",u.touchEnd),e.on("keyup",(t=>{const o=Vm(t);if(o.raw.shiftKey&&um(o.raw.which)){const t=e.selection.getRng(),n=xe.fromDom(t.startContainer),r=xe.fromDom(t.endContainer);c.keyup(o,n,t.startOffset,r,t.endOffset).each((e=>{m(o,e)}))}})),e.on("keydown",(o=>{const n=Vm(o);t.hide();const r=e.selection.getRng(),s=xe.fromDom(r.startContainer),l=xe.fromDom(r.endContainer),a=an(fm,gm)(xe.fromDom(e.selection.getStart()));c.keydown(n,s,r.startOffset,l,r.endOffset,a).each((e=>{m(n,e)})),t.show()})),e.on("NodeChange",(()=>{const t=e.selection,o=xe.fromDom(t.getStart()),r=xe.fromDom(t.getEnd());vs(Vt,[o,r]).fold((()=>n.clear(s)),f)}))})),e.on("PreInit",(()=>{e.serializer.addTempAttr(As.firstSelected),e.serializer.addTempAttr(As.lastSelected)})),{getSelectedCells:()=>((e,t,o,n)=>{switch(e.tag){case"none":return t();case"single":return(e=>[e.dom])(e.element);case"multiple":return(e=>E(e,(e=>e.dom)))(e.elements)}})(o.get(),g([])),clearSelectedCells:e=>n.clear(xe.fromDom(e))}},Gm=e=>{let t=[];return{bind:e=>{if(void 0===e)throw new Error("Event bind error: undefined handler");t.push(e)},unbind:e=>{t=_(t,(t=>t!==e))},trigger:(...o)=>{const n={};N(e,((e,t)=>{n[e]=o[t]})),N(t,(e=>{e(n)}))}}},Km=e=>({registry:K(e,(e=>({bind:e.bind,unbind:e.unbind}))),trigger:K(e,(e=>e.trigger))}),Ym=e=>e.slice(0).sort(),Jm=(e,t)=>{const o=_(t,(t=>!D(e,t)));o.length>0&&(e=>{throw new Error("Unsupported keys for object: "+Ym(e).join(", "))})(o)},Qm=e=>((e,t)=>((e,t,o)=>{if(0===t.length)throw new Error("You must specify at least one required field.");return((e,t)=>{if(!l(t))throw new Error("The "+e+" fields must be an array. Was: "+t+".");N(t,(t=>{if(!r(t))throw new Error("The value "+t+" in the "+e+" fields was not a string.")}))})("required",t),(e=>{const t=Ym(e);L(t,((e,o)=>o<t.length-1&&e===t[o+1])).each((e=>{throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}))})(t),n=>{const r=q(n);P(t,(e=>D(r,e)))||((e,t)=>{throw new Error("All required keys ("+Ym(e).join(", ")+") were not specified. Specified keys were: "+Ym(t).join(", ")+".")})(t,r),e(t,r);const s=_(t,(e=>!o.validate(n[e],e)));return s.length>0&&((e,t)=>{throw new Error("All values need to be of type: "+t+". Keys ("+Ym(e).join(", ")+") were not.")})(s,o.label),n}})(e,t,{validate:d,label:"function"}))(Jm,e),Xm=Qm(["compare","extract","mutate","sink"]),Zm=Qm(["element","start","stop","destroy"]),ed=Qm(["forceDrop","drop","move","delayDrop"]),td=()=>{const e=(()=>{const e=Km({move:Gm(["info"])});return{onEvent:f,reset:f,events:e.registry}})(),t=(()=>{let e=C.none();const t=Km({move:Gm(["info"])});return{onEvent:(o,n)=>{n.extract(o).each((o=>{const r=((t,o)=>{const n=e.map((e=>t.compare(e,o)));return e=C.some(o),n})(n,o);r.each((e=>{t.trigger.move(e)}))}))},reset:()=>{e=C.none()},events:t.registry}})();let o=e;return{on:()=>{o.reset(),o=t},off:()=>{o.reset(),o=e},isOn:()=>o===t,onEvent:(e,t)=>{o.onEvent(e,t)},events:t.events}},od=e=>{const t=e.replace(/\./g,"-");return{resolve:e=>t+"-"+e}},nd=od("ephox-dragster").resolve;var rd=Xm({compare:(e,t)=>gn(t.left-e.left,t.top-e.top),extract:e=>C.some(gn(e.x,e.y)),sink:(e,t)=>{const o=(e=>{const t={layerClass:nd("blocker"),...e},o=xe.fromTag("div");return ge(o,"role","presentation"),kt(o,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),Lm(o,nd("blocker")),Lm(o,t.layerClass),{element:g(o),destroy:()=>{qe(o)}}})(t),n=$m(o.element(),"mousedown",e.forceDrop),r=$m(o.element(),"mouseup",e.drop),s=$m(o.element(),"mousemove",e.move),l=$m(o.element(),"mouseout",e.delayDrop);return Zm({element:o.element,start:e=>{Ie(e,o.element())},stop:()=>{qe(o.element())},destroy:()=>{o.destroy(),r.unbind(),s.unbind(),l.unbind(),n.unbind()}})},mutate:(e,t)=>{e.mutate(t.left,t.top)}});const sd=od("ephox-snooker").resolve,ld=sd("resizer-bar"),ad=sd("resizer-rows"),cd=sd("resizer-cols"),id=e=>{const t=ct(e.parent(),"."+ld);N(t,qe)},md=(e,t,o)=>{const n=e.origin();N(t,(t=>{t.each((t=>{const r=o(n,t);Lm(r,ld),Ie(e.parent(),r)}))}))},dd=(e,t,o,n,r)=>{const s=pn(o),l=t.isResizable,a=n.length>0?kn.positions(n,o):[],c=a.length>0?((e,t)=>j(e.all,((e,o)=>t(e.element)?[o]:[])))(e,l):[];((e,t,o,n)=>{md(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=xe.fromTag("div");return kt(s,{position:"absolute",left:t+"px",top:o-3.5+"px",height:"7px",width:n+"px"}),he(s,{"data-row":e,role:"presentation"}),s})(t.row,o.left-e.left,t.y-e.top,n);return Lm(r,ad),r}))})(t,_(a,((e,t)=>O(c,(e=>t===e)))),s,_o(o));const i=r.length>0?Nn.positions(r,o):[],m=i.length>0?((e,t)=>{const o=[];return k(e.grid.columns,(n=>{nn(e,n).map((e=>e.element)).forall(t)&&o.push(n)})),_(o,(o=>{const n=Zo(e,(e=>e.column===o));return P(n,(e=>t(e.element)))}))})(e,l):[];((e,t,o,n)=>{md(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=xe.fromTag("div");return kt(s,{position:"absolute",left:t-3.5+"px",top:o+"px",height:r+"px",width:"7px"}),he(s,{"data-column":e,role:"presentation"}),s})(t.col,t.x-e.left,o.top-e.top,0,n);return Lm(r,cd),r}))})(t,_(i,((e,t)=>O(m,(e=>t===e)))),s,un(o))},ud=(e,t)=>{if(id(e),e.isResizable(t)){const o=Yo(t),n=ln(o),r=rn(o);dd(o,e,t,n,r)}},fd=(e,t)=>{const o=ct(e.parent(),"."+ld);N(o,t)},gd=e=>{fd(e,(e=>{Ot(e,"display","none")}))},hd=e=>{fd(e,(e=>{Ot(e,"display","block")}))},pd=sd("resizer-bar-dragging"),bd=e=>{const t=(()=>{const e=Km({drag:Gm(["xDelta","yDelta","target"])});let t=C.none();const o=(()=>{const e=Km({drag:Gm(["xDelta","yDelta"])});return{mutate:(t,o)=>{e.trigger.drag(t,o)},events:e.registry}})();return o.events.drag.bind((o=>{t.each((t=>{e.trigger.drag(o.xDelta,o.yDelta,t)}))})),{assign:e=>{t=C.some(e)},get:()=>t,mutate:o.mutate,events:e.registry}})(),o=((e,t={})=>{var o;return((e,t,o)=>{let n=!1;const r=Km({start:Gm([]),stop:Gm([])}),s=td(),l=()=>{m.stop(),s.isOn()&&(s.off(),r.trigger.stop())},c=((e,t)=>{let o=null;const n=()=>{a(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...t)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,t)}),200)}}})(l);s.events.move.bind((o=>{t.mutate(e,o.info)}));const i=e=>(...t)=>{n&&e.apply(null,t)},m=t.sink(ed({forceDrop:l,drop:i(l),move:i((e=>{c.cancel(),s.onEvent(e,t)})),delayDrop:i(c.throttle)}),o);return{element:m.element,go:e=>{m.start(e),s.on(),r.trigger.start()},on:()=>{n=!0},off:()=>{n=!1},isActive:()=>n,destroy:()=>{m.destroy()},events:r.registry}})(e,null!==(o=t.mode)&&void 0!==o?o:rd,t)})(t,{});let n=C.none();const r=(e,t)=>C.from(pe(e,t));t.events.drag.bind((e=>{r(e.target,"data-row").each((t=>{const o=Wt(e.target,"top");Ot(e.target,"top",o+e.yDelta+"px")})),r(e.target,"data-column").each((t=>{const o=Wt(e.target,"left");Ot(e.target,"left",o+e.xDelta+"px")}))}));const s=(e,t)=>Wt(e,t)-zt(e,"data-initial-"+t,0);o.events.stop.bind((()=>{t.get().each((t=>{n.each((o=>{r(t,"data-row").each((e=>{const n=s(t,"top");we(t,"data-initial-top"),d.trigger.adjustHeight(o,n,parseInt(e,10))})),r(t,"data-column").each((e=>{const n=s(t,"left");we(t,"data-initial-left"),d.trigger.adjustWidth(o,n,parseInt(e,10))})),ud(e,o)}))}))}));const l=(n,r)=>{d.trigger.startAdjust(),t.assign(n),ge(n,"data-initial-"+r,Wt(n,r)),Lm(n,pd),Ot(n,"opacity","0.2"),o.go(e.parent())},c=$m(e.parent(),"mousedown",(e=>{var t;t=e.target,Wm(t,ad)&&l(e.target,"top"),(e=>Wm(e,cd))(e.target)&&l(e.target,"left")})),i=t=>Re(t,e.view()),m=$m(e.view(),"mouseover",(t=>{var r;(r=t.target,ht(r,"table",i).filter(Kr)).fold((()=>{nt(t.target)&&id(e)}),(t=>{o.isActive()&&(n=C.some(t),ud(e,t))}))})),d=Km({adjustHeight:Gm(["table","delta","row"]),adjustWidth:Gm(["table","delta","column"]),startAdjust:Gm([])});return{destroy:()=>{c.unbind(),m.unbind(),o.destroy(),id(e)},refresh:t=>{ud(e,t)},on:o.on,off:o.off,hideBars:b(gd,e),showBars:b(hd,e),events:d.registry}},wd=e=>m(e)&&"TABLE"===e.nodeName,vd="bar-",yd=e=>"false"!==pe(e,"data-mce-resize"),xd=e=>{const t=rm(),o=rm(),n=rm();let r,s,l,a;const c=t=>uc(e,t),i=()=>Wr(e)?el():Zs(),m=(t,o,n,m)=>{const d=(e=>{return xt(t=e,"corner-")?((e,t)=>e.substring(7))(t):t;var t})(o),u=Ct(d,"e"),f=xt(d,"n");if(""===s&&Ec(t),""===a&&(e=>{const t=(e=>dn(e)+"px")(e);Sc(e,C.none(),C.some(t)),kc(e)})(t),n!==r&&""!==s){Ot(t,"width",s);const o=i(),l=c(t),a=Wr(e)||u?(e=>tl(e).columns)(t)-1:0;oa(t,n-r,a,o,l)}else if((e=>/^(\d+(\.\d+)?)%$/.test(e))(s)){const e=parseFloat(s.replace("%",""));Ot(t,"width",n*e/r+"%")}if((e=>/^(\d+(\.\d+)?)px$/.test(e))(s)&&(e=>{const t=Yo(e);on(t)||N(Ht(e),(e=>{const t=Et(e,"width");Ot(e,"width",t),we(e,"width")}))})(t),m!==l&&""!==a){Ot(t,"height",a);const e=f?0:(e=>tl(e).rows)(t)-1;na(t,m-l,e)}};return e.on("init",(()=>{const r=((e,t)=>e.inline?((e,t,o)=>({parent:g(t),view:g(e),origin:g(gn(0,0)),isResizable:o}))(xe.fromDom(e.getBody()),(()=>{const e=xe.fromTag("div");return kt(e,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),Ie(rt(xe.fromDom(document)),e),e})(),t):((e,t)=>{const o=me(e)?(e=>xe.fromDom(Ee(e).dom.documentElement))(e):e;return{parent:g(o),view:g(e),origin:g(gn(0,0)),isResizable:t}})(xe.fromDom(e.getDoc()),t))(e,yd);if(n.set(r),(e=>{const t=e.options.get("object_resizing");return D(t.split(","),"table")})(e)&&Hr(e)){const n=((e,t,o)=>{const n=kn,r=Nn,s=bd(e),l=Km({beforeResize:Gm(["table","type"]),afterResize:Gm(["table","type"]),startDrag:Gm([])});return s.events.adjustHeight.bind((e=>{const t=e.table;l.trigger.beforeResize(t,"row");const o=n.delta(e.delta,t);na(t,o,e.row),l.trigger.afterResize(t,"row")})),s.events.startAdjust.bind((e=>{l.trigger.startDrag()})),s.events.adjustWidth.bind((e=>{const n=e.table;l.trigger.beforeResize(n,"col");const s=r.delta(e.delta,n),a=o(n);oa(n,s,e.column,t,a),l.trigger.afterResize(n,"col")})),{on:s.on,off:s.off,refreshBars:s.refresh,hideBars:s.hideBars,showBars:s.showBars,destroy:s.destroy,events:l.registry}})(r,i(),c);n.on(),n.events.startDrag.bind((o=>{t.set(e.selection.getRng())})),n.events.beforeResize.bind((t=>{const o=t.table.dom;((e,t,o,n,r)=>{e.dispatch("ObjectResizeStart",{target:t,width:o,height:n,origin:r})})(e,o,es(o),ts(o),vd+t.type)})),n.events.afterResize.bind((o=>{const n=o.table,r=n.dom;Xr(n),t.on((t=>{e.selection.setRng(t),e.focus()})),((e,t,o,n,r)=>{e.dispatch("ObjectResized",{target:t,width:o,height:n,origin:r})})(e,r,es(r),ts(r),vd+o.type),e.undoManager.add()})),o.set(n)}})),e.on("ObjectResizeStart",(t=>{const o=t.target;if(wd(o)){const n=xe.fromDom(o);N(e.dom.select(".mce-clonedresizable"),(t=>{e.dom.addClass(t,"mce-"+Lr(e)+"-columns")})),!Dc(n)&&Ir(e)?Nc(n):!Rc(n)&&Pr(e)&&Ec(n),Oc(n)&&xt(t.origin,vd)&&Ec(n),r=t.width,s=Fr(e)?"":ns(e,o).getOr(""),l=t.height,a=rs(e,o).getOr("")}})),e.on("ObjectResized",(t=>{const o=t.target;if(wd(o)){const n=xe.fromDom(o),r=t.origin;(e=>xt(e,"corner-"))(r)&&m(n,r,t.width,t.height),Xr(n),cc(e,n.dom,ic)}})),e.on("SwitchMode",(()=>{o.on((t=>{e.mode.isReadOnly()?t.hideBars():t.showBars()}))})),e.on("dragstart dragend",(e=>{o.on((t=>{"dragstart"===e.type?(t.hideBars(),t.off()):(t.on(),t.showBars())}))})),e.on("remove",(()=>{o.on((e=>{e.destroy()})),n.on((t=>{((e,t)=>{e.inline&&qe(t.parent())})(e,t)}))})),{refresh:e=>{o.on((t=>t.refreshBars(xe.fromDom(e))))},hide:()=>{o.on((e=>e.hideBars()))},show:()=>{o.on((e=>e.showBars()))}}},Cd=e=>{(e=>{const t=e.options.register;t("table_clone_elements",{processor:"string[]"}),t("table_use_colgroups",{processor:"boolean",default:!0}),t("table_header_type",{processor:e=>{const t=D(["section","cells","sectionCells","auto"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: section, cells, sectionCells or auto."}},default:"section"}),t("table_sizing_mode",{processor:"string",default:"auto"}),t("table_default_attributes",{processor:"object",default:{border:"1"}}),t("table_default_styles",{processor:"object",default:{"border-collapse":"collapse"}}),t("table_column_resizing",{processor:e=>{const t=D(["preservetable","resizetable"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be preservetable, or resizetable."}},default:"preservetable"}),t("table_resize_bars",{processor:"boolean",default:!0}),t("table_style_by_css",{processor:"boolean",default:!0}),t("table_merge_content_on_paste",{processor:"boolean",default:!0})})(e);const t=xd(e),o=Um(e,t),n=fc(e,t,o);return Jc(e,n),((e,t)=>{const o=Qr(e),n=t=>js(Zr(e)).bind((n=>Vt(n,o).map((o=>{const r=Ls(Ps(e),o,n);return t(o,r)})))).getOr("");G({mceTableRowType:()=>n(t.getTableRowType),mceTableCellType:()=>n(t.getTableCellType),mceTableColType:()=>n(t.getTableColType)},((t,o)=>e.addQueryValueHandler(o,t)))})(e,n),Is(e,n),{getSelectedCells:o.getSelectedCells,clearSelectedCells:o.clearSelectedCells}};e.add("dom",(e=>({table:Cd(e)})))}();