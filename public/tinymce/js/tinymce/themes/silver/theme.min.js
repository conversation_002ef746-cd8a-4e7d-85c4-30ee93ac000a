/**
 * TinyMCE version 7.3.0 (2024-08-07)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=e=>()=>e(),y=(e,t)=>(...o)=>e(t.apply(null,o)),x=e=>()=>e,w=e=>e,S=(e,t)=>e===t;function C(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const k=e=>t=>!e(t),O=e=>()=>{throw new Error(e)},_=e=>e(),T=x(!1),E=x(!0);class A{constructor(e,t){this.tag=e,this.value=t}static some(e){return new A(!0,e)}static none(){return A.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?A.some(e(this.value)):A.none()}bind(e){return this.tag?e(this.value):A.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:A.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?A.some(e):A.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}A.singletonNone=new A(!1);const M=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,F=(e,t)=>D.call(e,t),I=(e,t)=>F(e,t)>-1,R=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},N=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},z=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},L=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},V=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},H=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},P=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},U=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),W=(e,t,o)=>(V(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return A.some(s);if(o(s,n))break}return A.none()})(e,t,T),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return A.some(o);return A.none()},G=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);B.apply(t,e[o])}return t},q=(e,t)=>G(L(e,t)),Y=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},X=e=>{const t=M.call(e,0);return t.reverse(),t},K=(e,t)=>P(e,(e=>!I(t,e))),J=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],Z=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},ee=(e,t)=>t>=0&&t<e.length?A.some(e[t]):A.none(),te=e=>ee(e,0),oe=e=>ee(e,e.length-1),ne=p(Array.from)?Array.from:e=>M.call(e),se=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return A.none()},re=Object.keys,ae=Object.hasOwnProperty,ie=(e,t)=>{const o=re(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},le=(e,t)=>ce(e,((e,o)=>({k:o,v:t(e,o)}))),ce=(e,t)=>{const o={};return ie(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},de=e=>(t,o)=>{e[o]=t},ue=(e,t,o,n)=>{ie(e,((e,s)=>{(t(e,s)?o:n)(e,s)}))},me=(e,t)=>{const o={};return ue(e,t,de(o),b),o},ge=(e,t)=>{const o=[];return ie(e,((e,n)=>{o.push(t(e,n))})),o},pe=(e,t)=>{const o=re(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return A.some(r)}return A.none()},he=e=>ge(e,w),fe=(e,t)=>be(e,t)?A.from(e[t]):A.none(),be=(e,t)=>ae.call(e,t),ve=(e,t)=>be(e,t)&&void 0!==e[t]&&null!==e[t],ye=(e,t,o=S)=>e.exists((e=>o(e,t))),xe=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},we=(e,t,o)=>e.isSome()&&t.isSome()?A.some(o(e.getOrDie(),t.getOrDie())):A.none(),Se=(e,t)=>null!=e?A.some(t(e)):A.none(),Ce=(e,t)=>e?A.some(t):A.none(),ke=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,Oe=(e,t)=>Te(e,t)?((e,t)=>e.substring(t))(e,t.length):e,_e=(e,t,o=0,n)=>{const s=e.indexOf(t,o);return-1!==s&&(!!u(n)||s+t.length<=n)},Te=(e,t)=>ke(e,t,0),Ee=(e,t)=>ke(e,t,e.length-t.length),Ae=(Eo=/^\s+|\s+$/g,e=>e.replace(Eo,"")),Me=e=>e.length>0,De=e=>!Me(e),Be=e=>void 0!==e.style&&p(e.style.getPropertyValue),Fe=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Ie=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Fe(o.childNodes[0])},Re=(e,t)=>{const o=(t||document).createElement(e);return Fe(o)},Ne=(e,t)=>{const o=(t||document).createTextNode(e);return Fe(o)},ze=Fe,Le="undefined"!=typeof window?window:Function("return this;")(),Ve=(e,t)=>((e,t)=>{let o=null!=t?t:Le;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),He=Object.getPrototypeOf,Pe=e=>{const t=Ve("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>Ve(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(He(e).constructor.name))},Ue=e=>e.dom.nodeName.toLowerCase(),We=e=>t=>(e=>e.dom.nodeType)(t)===e,je=e=>$e(e)&&Pe(e.dom),$e=We(1),Ge=We(3),qe=We(9),Ye=We(11),Xe=e=>t=>$e(t)&&Ue(t)===e,Ke=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Je=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Qe=(e,t)=>{const o=void 0===t?document:t.dom;return Je(o)?A.none():A.from(o.querySelector(e)).map(ze)},Ze=(e,t)=>e.dom===t.dom,et=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},tt=e=>ze(e.dom.ownerDocument),ot=e=>qe(e)?e:tt(e),nt=e=>ze(ot(e).dom.documentElement),st=e=>ze(ot(e).dom.defaultView),rt=e=>A.from(e.dom.parentNode).map(ze),at=e=>A.from(e.dom.parentElement).map(ze),it=e=>A.from(e.dom.offsetParent).map(ze),lt=e=>L(e.dom.childNodes,ze),ct=(e,t)=>{const o=e.dom.childNodes;return A.from(o[t]).map(ze)},dt=e=>ct(e,0),ut=(e,t)=>({element:e,offset:t}),mt=(e,t)=>{const o=lt(e);return o.length>0&&t<o.length?ut(o[t],0):ut(e,t)},gt=e=>Ye(e)&&g(e.dom.host),pt=e=>ze(e.dom.getRootNode()),ht=e=>gt(e)?e:ze(ot(e).dom.body),ft=e=>{const t=pt(e);return gt(t)?A.some(t):A.none()},bt=e=>ze(e.dom.host),vt=e=>{const t=Ge(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return ft(ze(t)).fold((()=>o.body.contains(t)),(n=vt,s=bt,e=>n(s(e))));var n,s},yt=()=>xt(ze(document)),xt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return ze(t)},wt=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},St=(e,t,o)=>{wt(e.dom,t,o)},Ct=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{wt(o,t,e)}))},kt=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},Ot=(e,t)=>A.from(kt(e,t)),_t=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},Tt=(e,t)=>{e.dom.removeAttribute(t)},Et=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Be(e)&&e.style.setProperty(t,o)},At=(e,t)=>{Be(e)&&e.style.removeProperty(t)},Mt=(e,t,o)=>{const n=e.dom;Et(n,t,o)},Dt=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{Et(o,t,e)}))},Bt=(e,t)=>{const o=e.dom;ie(t,((e,t)=>{e.fold((()=>{At(o,t)}),(e=>{Et(o,t,e)}))}))},Ft=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||vt(e)?n:It(o,t)},It=(e,t)=>Be(e)?e.style.getPropertyValue(t):"",Rt=(e,t)=>{const o=e.dom,n=It(o,t);return A.from(n).filter((e=>e.length>0))},Nt=e=>{const t={},o=e.dom;if(Be(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},zt=(e,t,o)=>{const n=Re(e);return Mt(n,t,o),Rt(n,t).isSome()},Lt=(e,t)=>{const o=e.dom;At(o,t),ye(Ot(e,"style").map(Ae),"")&&Tt(e,"style")},Vt=e=>e.dom.offsetWidth,Ht=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Ft(o,e);return parseFloat(t)||0}return n},n=(e,t)=>W(t,((t,o)=>{const n=Ft(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Be(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},Pt=Ht("height",(e=>{const t=e.dom;return vt(e)?t.getBoundingClientRect().height:t.offsetHeight})),Ut=e=>Pt.get(e),Wt=e=>Pt.getOuter(e),jt=(e,t)=>({left:e,top:t,translate:(o,n)=>jt(e+o,t+n)}),$t=jt,Gt=(e,t)=>void 0!==e?e:void 0!==t?t:0,qt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return $t(o.offsetLeft,o.offsetTop);const r=Gt(null==n?void 0:n.pageYOffset,s.scrollTop),a=Gt(null==n?void 0:n.pageXOffset,s.scrollLeft),i=Gt(s.clientTop,o.clientTop),l=Gt(s.clientLeft,o.clientLeft);return Yt(e).translate(a-l,r-i)},Yt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?$t(o.offsetLeft,o.offsetTop):vt(e)?(e=>{const t=e.getBoundingClientRect();return $t(t.left,t.top)})(t):$t(0,0)},Xt=Ht("width",(e=>e.dom.offsetWidth)),Kt=e=>Xt.get(e),Jt=e=>Xt.getOuter(e),Qt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},Zt=()=>eo(0,0),eo=(e,t)=>({major:e,minor:t}),to={nu:eo,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?Zt():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return eo(n(1),n(2))})(e,o)},unknown:Zt},oo=(e,t)=>{const o=String(t).toLowerCase();return j(e,(e=>e.search(o)))},no=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,so=e=>t=>_e(t,e),ro=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>_e(e,"edge/")&&_e(e,"chrome")&&_e(e,"safari")&&_e(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,no],search:e=>_e(e,"chrome")&&!_e(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>_e(e,"msie")||_e(e,"trident")},{name:"Opera",versionRegexes:[no,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:so("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:so("firefox")},{name:"Safari",versionRegexes:[no,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(_e(e,"safari")||_e(e,"mobile/"))&&_e(e,"applewebkit")}],ao=[{name:"Windows",search:so("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>_e(e,"iphone")||_e(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:so("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:so("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:so("linux"),versionRegexes:[]},{name:"Solaris",search:so("sunos"),versionRegexes:[]},{name:"FreeBSD",search:so("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:so("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],io={browsers:x(ro),oses:x(ao)},lo="Edge",co="Chromium",uo="Opera",mo="Firefox",go="Safari",po=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(lo),isChromium:n(co),isIE:n("IE"),isOpera:n(uo),isFirefox:n(mo),isSafari:n(go)}},ho=()=>po({current:void 0,version:to.unknown()}),fo=po,bo=(x(lo),x(co),x("IE"),x(uo),x(mo),x(go),"Windows"),vo="Android",yo="Linux",xo="macOS",wo="Solaris",So="FreeBSD",Co="ChromeOS",ko=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(bo),isiOS:n("iOS"),isAndroid:n(vo),isMacOS:n(xo),isLinux:n(yo),isSolaris:n(wo),isFreeBSD:n(So),isChromeOS:n(Co)}},Oo=()=>ko({current:void 0,version:to.unknown()}),_o=ko,To=(x(bo),x("iOS"),x(vo),x(yo),x(xo),x(wo),x(So),x(Co),e=>window.matchMedia(e).matches);var Eo;let Ao=Qt((()=>((e,t,o)=>{const n=io.browsers(),s=io.oses(),r=t.bind((e=>((e,t)=>se(t.brands,(t=>{const o=t.brand.toLowerCase();return j(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:to.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>oo(e,t).map((e=>{const o=to.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(ho,fo),a=((e,t)=>oo(e,t).map((e=>{const o=to.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(Oo,_o),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:x(s),isiPhone:x(r),isTablet:x(l),isPhone:x(c),isTouch:x(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:x(d),isDesktop:x(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(window.navigator.userAgent,A.from(window.navigator.userAgentData),To)));const Mo=()=>Ao(),Do=e=>{const t=ze((e=>{if(g(e.target)){const t=ze(e.target);if($e(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return te(t)}}return A.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=y(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},Bo=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(Do(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:C(Fo,e,t,r,s)}},Fo=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Io=(e,t)=>{rt(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},Ro=(e,t)=>{const o=(e=>A.from(e.dom.nextSibling).map(ze))(e);o.fold((()=>{rt(e).each((e=>{zo(e,t)}))}),(e=>{Io(e,t)}))},No=(e,t)=>{dt(e).fold((()=>{zo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},zo=(e,t)=>{e.dom.appendChild(t.dom)},Lo=(e,t)=>{V(t,(t=>{zo(e,t)}))},Vo=e=>{e.dom.textContent="",V(lt(e),(e=>{Ho(e)}))},Ho=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Po=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return $t(o,n)},Uo=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},Wo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),jo=e=>{const t=void 0===e?window:e,o=t.document,n=Po(ze(o));return(e=>{const t=void 0===e?window:e;return Mo().browser.isFirefox()?A.none():A.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return Wo(n.left,n.top,o,s)}),(e=>Wo(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},$o=()=>ze(document),Go=(e,t)=>e.view(t).fold(x([]),(t=>{const o=e.owner(t),n=Go(e,o);return[t].concat(n)}));var qo=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?A.none():A.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(ze)},owner:e=>tt(e)});const Yo=e=>{const t=$o(),o=Po(t),n=((e,t)=>{const o=t.owner(e),n=Go(t,o);return A.some(n)})(e,qo);return n.fold(C(qt,e),(t=>{const n=Yt(e),s=U(t,((e,t)=>{const o=Yt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return $t(s.left+n.left+o.left,s.top+n.top+o.top)}))},Xo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Ko=e=>{const t=qt(e),o=Jt(e),n=Wt(e);return Xo(t.left,t.top,o,n)},Jo=e=>{const t=Yo(e),o=Jt(e),n=Wt(e);return Xo(t.left,t.top,o,n)},Qo=(e,t)=>{const o=Math.max(e.x,t.x),n=Math.max(e.y,t.y),s=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return Xo(o,n,s-o,r-n)},Zo=()=>jo(window),en=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},tn=e=>{const t=en(A.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(A.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(A.some(e))}}},on=()=>tn((e=>e.unbind())),nn=()=>{const e=tn(b);return{...e,on:t=>e.get().each(t)}};var sn=tinymce.util.Tools.resolve("tinymce.ThemeManager");const rn=e=>{const t=t=>t(e),o=x(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:E,isError:T,map:t=>ln.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>A.some(e)};return s},an=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:T,isError:E,map:t,mapError:t=>ln.error(t(e)),bind:t,exists:T,forall:E,getOr:w,or:w,getOrThunk:_,orThunk:_,getOrDie:O(String(e)),each:b,toOptional:A.none};return o},ln={value:rn,error:an,fromOption:(e,t)=>e.fold((()=>an(t)),rn)};var cn;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(cn||(cn={}));const dn=(e,t,o)=>e.stype===cn.Error?t(e.serror):o(e.svalue),un=e=>({stype:cn.Value,svalue:e}),mn=e=>({stype:cn.Error,serror:e}),gn=un,pn=mn,hn=dn,fn=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),bn=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},vn=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)be(s,t)&&(o[t]=e(o[t],s[t]))}return o},yn=vn(((e,t)=>i(e)&&i(t)?yn(e,t):t)),xn=vn(((e,t)=>t)),wn=e=>({tag:"defaultedThunk",process:e}),Sn=e=>wn(x(e)),Cn=e=>({tag:"mergeWithThunk",process:e}),kn=e=>{const t=(e=>{const t=[],o=[];return V(e,(e=>{dn(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,y(pn,G)(o)):gn(t.values);var o},On=e=>a(e)&&re(e).length>100?" removed due to size":JSON.stringify(e,null,2),_n=(e,t)=>pn([{path:e,getErrorInfo:t}]),Tn=e=>({extract:(t,o)=>((e,t)=>e.stype===cn.Error?t(e.serror):e)(e(o),(e=>((e,t)=>_n(e,x(t)))(t,e))),toString:x("val")}),En=Tn(gn),An=(e,t,o,n)=>n(fe(e,t).getOrThunk((()=>o(e)))),Mn=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>gn(A.none())),(e=>((e,t)=>e.stype===cn.Value?{stype:cn.Value,svalue:t(e.svalue)}:e)(s.extract(t.concat([n]),e),A.some)));switch(e.tag){case"required":return((e,t,o,n)=>fe(t,o).fold((()=>((e,t,o)=>_n(e,(()=>'Could not find valid *required* value for "'+t+'" in '+On(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return An(o,n,e.process,r);case"option":return((e,t,o)=>o(fe(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(fe(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return An(o,n,x({}),(t=>{const n=yn(e.process(o),t);return r(n)}))}},Dn=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),Bn=e=>re(me(e,g)),Fn=e=>{const t=In(e),o=U(e,((e,t)=>bn(t,(t=>yn(e,{[t]:!0})),x(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:Bn(n),r=P(s,(e=>!ve(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>_n(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},In=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)bn(r,((o,r,a,i)=>{const l=Mn(a,e,t,o,i);hn(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?pn(s):gn(n)})(t,o,e),toString:()=>{const t=L(e,(e=>bn(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),Rn=e=>({extract:(t,o)=>{const n=L(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return kn(n)},toString:()=>"array("+e.toString()+")"}),Nn=(e,t)=>{const o=void 0!==t?t:w;return{extract:(t,n)=>{const s=[];for(const r of e){const e=r.extract(t,n);if(e.stype===cn.Value)return{stype:cn.Value,svalue:o(e.svalue)};s.push(e)}return kn(s)},toString:()=>"oneOf("+L(e,(e=>e.toString())).join(", ")+")"}},zn=(e,t)=>({extract:(o,n)=>{const s=re(n),r=((t,o)=>Rn(Tn(e)).extract(t,o))(o,s);return((e,t)=>e.stype===cn.Value?t(e.svalue):e)(r,(e=>{const s=L(e,(e=>fn(e,e,{tag:"required",process:{}},t)));return In(s).extract(o,n)}))},toString:()=>"setOf("+t.toString()+")"}),Ln=y(Rn,In),Vn=x(En),Hn=(e,t)=>Tn((o=>{const n=typeof o;return e(o)?gn(o):pn(`Expected type: ${t} but got: ${n}`)})),Pn=Hn(h,"number"),Un=Hn(r,"string"),Wn=Hn(d,"boolean"),jn=Hn(p,"function"),$n=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>$n(e[t])));default:return!1}},Gn=Tn((e=>$n(e)?gn(e):pn("Expected value to be acceptable for sending via postMessage"))),qn=(e,t)=>({extract:(o,n)=>fe(n,e).fold((()=>((e,t)=>_n(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>fe(o,n).fold((()=>((e,t,o)=>_n(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+On(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+re(t)}),Yn=e=>Tn((t=>e(t).fold(pn,gn))),Xn=(e,t)=>zn((t=>e(t).fold(mn,un)),t),Kn=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===cn.Error?{stype:cn.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),dn(n,ln.error,ln.value);var n},Jn=e=>e.fold((e=>{throw new Error(Zn(e))}),w),Qn=(e,t,o)=>Jn(Kn(e,t,o)),Zn=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:x("... (only showing first ten failures)")}]):e;return L(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+On(e.input),es=(e,t)=>qn(e,le(t,In)),ts=(e,t)=>((e,t)=>{const o=Qt(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,t),os=fn,ns=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),ss=e=>Yn((t=>I(e,t)?ln.value(t):ln.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),rs=e=>os(e,e,{tag:"required",process:{}},Vn()),as=(e,t)=>os(e,e,{tag:"required",process:{}},t),is=e=>as(e,Pn),ls=e=>as(e,Un),cs=(e,t)=>os(e,e,{tag:"required",process:{}},ss(t)),ds=e=>as(e,jn),us=(e,t)=>os(e,e,{tag:"required",process:{}},In(t)),ms=(e,t)=>os(e,e,{tag:"required",process:{}},Ln(t)),gs=(e,t)=>os(e,e,{tag:"required",process:{}},Rn(t)),ps=e=>os(e,e,{tag:"option",process:{}},Vn()),hs=(e,t)=>os(e,e,{tag:"option",process:{}},t),fs=e=>hs(e,Pn),bs=e=>hs(e,Un),vs=(e,t)=>hs(e,ss(t)),ys=e=>hs(e,jn),xs=(e,t)=>hs(e,Rn(t)),ws=(e,t)=>hs(e,In(t)),Ss=(e,t)=>os(e,e,Sn(t),Vn()),Cs=(e,t,o)=>os(e,e,Sn(t),o),ks=(e,t)=>Cs(e,t,Pn),Os=(e,t)=>Cs(e,t,Un),_s=(e,t,o)=>Cs(e,t,ss(o)),Ts=(e,t)=>Cs(e,t,Wn),Es=(e,t)=>Cs(e,t,jn),As=(e,t,o)=>Cs(e,t,Rn(o)),Ms=(e,t,o)=>Cs(e,t,In(o)),Ds=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return V(e,((n,s)=>{const r=re(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=re(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!Y(t,(e=>I(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};Ds([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Bs=(e,t)=>((e,t)=>({[e]:t}))(e,t),Fs=e=>(e=>{const t={};return V(e,(e=>{t[e.key]=e.value})),t})(e),Is=e=>p(e)?e:T,Rs=(e,t,o)=>{let n=e.dom;const s=Is(o);for(;n.parentNode;){n=n.parentNode;const e=ze(n),o=t(e);if(o.isSome())return o;if(s(e))break}return A.none()},Ns=(e,t,o)=>{const n=t(e),s=Is(o);return n.orThunk((()=>s(e)?A.none():Rs(e,t,s)))},zs=(e,t)=>Ze(e.element,t.event.target),Ls={can:E,abort:T,run:b},Vs=e=>{if(!ve(e,"can")&&!ve(e,"abort")&&!ve(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...Ls,...e}},Hs=x,Ps=Hs("touchstart"),Us=Hs("touchmove"),Ws=Hs("touchend"),js=Hs("touchcancel"),$s=Hs("mousedown"),Gs=Hs("mousemove"),qs=Hs("mouseout"),Ys=Hs("mouseup"),Xs=Hs("mouseover"),Ks=Hs("focusin"),Js=Hs("focusout"),Qs=Hs("keydown"),Zs=Hs("keyup"),er=Hs("input"),tr=Hs("change"),or=Hs("click"),nr=Hs("transitioncancel"),sr=Hs("transitionend"),rr=Hs("transitionstart"),ar=Hs("selectstart"),ir=e=>x("alloy."+e),lr={tap:ir("tap")},cr=ir("focus"),dr=ir("blur.post"),ur=ir("paste.post"),mr=ir("receive"),gr=ir("execute"),pr=ir("focus.item"),hr=lr.tap,fr=ir("longpress"),br=ir("sandbox.close"),vr=ir("typeahead.cancel"),yr=ir("system.init"),xr=ir("system.touchmove"),wr=ir("system.touchend"),Sr=ir("system.scroll"),Cr=ir("system.resize"),kr=ir("system.attached"),Or=ir("system.detached"),_r=ir("system.dismissRequested"),Tr=ir("system.repositionRequested"),Er=ir("focusmanager.shifted"),Ar=ir("slotcontainer.visibility"),Mr=ir("system.external.element.scroll"),Dr=ir("change.tab"),Br=ir("dismiss.tab"),Fr=ir("highlight"),Ir=ir("dehighlight"),Rr=(e,t)=>{Vr(e,e.element,t,{})},Nr=(e,t,o)=>{Vr(e,e.element,t,o)},zr=e=>{Rr(e,gr())},Lr=(e,t,o)=>{Vr(e,t,o,{})},Vr=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},Hr=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Pr=e=>Fs(e),Ur=(e,t)=>({key:e,value:Vs({abort:t})}),Wr=e=>({key:e,value:Vs({run:(e,t)=>{t.event.prevent()}})}),jr=(e,t)=>({key:e,value:Vs({run:t})}),$r=(e,t,o)=>({key:e,value:Vs({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),Gr=e=>t=>({key:e,value:Vs({run:(e,o)=>{zs(e,o)&&t(e,o)}})}),qr=(e,t,o)=>((e,t)=>jr(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{Hr(t,t.element,e,n)}))})))(e,t.partUids[o]),Yr=(e,t)=>jr(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>Ns(n.target,(t=>e.getSystem().getByDom(t).toOptional()),T).getOr(e)));t(e,s,o)})),Xr=e=>jr(e,((e,t)=>{t.cut()})),Kr=e=>jr(e,((e,t)=>{t.stop()})),Jr=(e,t)=>Gr(e)(t),Qr=Gr(kr()),Zr=Gr(Or()),ea=Gr(yr()),ta=(Za=gr(),e=>jr(Za,e)),oa=e=>L(e,(e=>Ee(e,"/*")?e.substring(0,e.length-2):e)),na=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:oa(r)}),e},sa=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),ra=(e,t,o)=>ea(((n,s)=>{o(n,e,t)})),aa=e=>({key:e,value:void 0}),ia=(e,t,o,n,s,r,a)=>{const i=e=>ve(e,o)?e[o]():A.none(),l=le(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:oa(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:x(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...le(r,((e,t)=>na(e,t))),...l,revoke:C(aa,o),config:t=>{const n=Qn(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:Qt((()=>Qn(o+"-config",e,t))),initialConfig:t,state:a}}},schema:x(t),exhibit:(e,t)=>we(i(e),fe(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>sa({}))),name:x(o),handlers:e=>i(e).map((e=>fe(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},la={init:()=>ca({readState:x("No State required")})},ca=e=>e,da=e=>Fs(e),ua=Fn([rs("fields"),rs("name"),Ss("active",{}),Ss("apis",{}),Ss("state",la),Ss("extra",{})]),ma=e=>{const t=Qn("Creating behaviour: "+e.name,ua,e);return((e,t,o,n,s,r)=>{const a=Fn(e),i=ws(t,[("config",l=e,hs("config",Fn(l)))]);var l;return ia(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},ga=Fn([rs("branchKey"),rs("branches"),rs("name"),Ss("active",{}),Ss("apis",{}),Ss("state",la),Ss("extra",{})]),pa=e=>{const t=Qn("Creating behaviour: "+e.name,ga,e);return((e,t,o,n,s,r)=>{const a=e,i=ws(t,[hs("config",e)]);return ia(a,i,t,o,n,s,r)})(es(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},ha=x(void 0),fa=(e,t)=>{const o=kt(e,t);return void 0===o||""===o?[]:o.split(" ")},ba=e=>void 0!==e.dom.classList,va=e=>fa(e,"class"),ya=(e,t)=>((e,t,o)=>{const n=fa(e,t).concat([o]);return St(e,t,n.join(" ")),!0})(e,"class",t),xa=(e,t)=>((e,t,o)=>{const n=P(fa(e,t),(e=>e!==o));return n.length>0?St(e,t,n.join(" ")):Tt(e,t),!1})(e,"class",t),wa=(e,t)=>{ba(e)?e.dom.classList.add(t):ya(e,t)},Sa=e=>{0===(ba(e)?e.dom.classList:va(e)).length&&Tt(e,"class")},Ca=(e,t)=>{ba(e)?e.dom.classList.remove(t):xa(e,t),Sa(e)},ka=(e,t)=>ba(e)&&e.dom.classList.contains(t),Oa=(e,t)=>{V(t,(t=>{wa(e,t)}))},_a=(e,t)=>{V(t,(t=>{Ca(e,t)}))},Ta=e=>ba(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):va(e),Ea=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},Aa=(e,t)=>{Bt(e,(e=>({...e,position:A.some(e.position)}))(t))},Ma=e=>(ye(Rt(e,"position"),"fixed")?A.none():it(e)).orThunk((()=>{const t=Re("span");return rt(e).bind((e=>{zo(e,t);const o=it(t);return Ho(t),o}))})),Da=e=>Ma(e).map(qt).getOrThunk((()=>$t(0,0))),Ba=(e,t)=>{const o=e.element;wa(o,t.transitionClass),Ca(o,t.fadeOutClass),wa(o,t.fadeInClass),t.onShow(e)},Fa=(e,t)=>{const o=e.element;wa(o,t.transitionClass),Ca(o,t.fadeInClass),wa(o,t.fadeOutClass),t.onHide(e)},Ia=(e,t)=>e.y>=t.y,Ra=(e,t)=>e.bottom<=t.bottom,Na=(e,t,o)=>({location:"top",leftX:t,topY:o.bounds.y-e.y}),za=(e,t,o)=>({location:"bottom",leftX:t,bottomY:e.bottom-o.bounds.bottom}),La=e=>e.box.x-e.win.x,Va=(e,t,o)=>o.getInitialPos().map((o=>{const n=((e,t)=>{const o=t.optScrollEnv.fold(x(e.bounds.y),(t=>t.scrollElmTop+(e.bounds.y-t.currentScrollTop)));return $t(e.bounds.x,o)})(o,t);return{box:Xo(n.left,n.top,Kt(e),Ut(e)),location:o.location}})),Ha=(e,t,o,n,s)=>{const r=((e,t)=>{const o=t.optScrollEnv.fold(x(e.y),(t=>e.y+t.currentScrollTop-t.scrollElmTop));return $t(e.x,o)})(t,o),a=Xo(r.left,r.top,t.width,t.height);n.setInitialPos({style:Nt(e),position:Ft(e,"position")||"static",bounds:a,location:s.location})},Pa=(e,t,o)=>o.getInitialPos().bind((n=>{var s;switch(o.clearInitialPos(),n.position){case"static":return A.some({morph:"static"});case"absolute":const o=Ma(e).getOr(yt()),r=Ko(o),a=null!==(s=o.dom.scrollTop)&&void 0!==s?s:0;return A.some({morph:"absolute",positionCss:Ea("absolute",fe(n.style,"left").map((e=>t.x-r.x)),fe(n.style,"top").map((e=>t.y-r.y+a)),fe(n.style,"right").map((e=>r.right-t.right)),fe(n.style,"bottom").map((e=>r.bottom-t.bottom)))});default:return A.none()}})),Ua=e=>{switch(e.location){case"top":return A.some({morph:"fixed",positionCss:Ea("fixed",A.some(e.leftX),A.some(e.topY),A.none(),A.none())});case"bottom":return A.some({morph:"fixed",positionCss:Ea("fixed",A.some(e.leftX),A.none(),A.none(),A.some(e.bottomY))});default:return A.none()}},Wa=(e,t,o)=>{const n=e.element;return ye(Rt(n,"position"),"fixed")?((e,t,o)=>((e,t,o)=>Va(e,t,o).filter((({box:e})=>((e,t,o)=>Y(e,(e=>{switch(e){case"bottom":return Ra(t,o.bounds);case"top":return Ia(t,o.bounds)}})))(o.getModes(),e,t))).bind((({box:t})=>Pa(e,t,o))))(e,t,o).orThunk((()=>t.optScrollEnv.bind((n=>Va(e,t,o))).bind((({box:e,location:o})=>{const n=Zo(),s=La({win:n,box:e}),r="top"===o?Na(n,s,t):za(n,s,t);return Ua(r)})))))(n,t,o):((e,t,o)=>{const n=Ko(e),s=Zo(),r=((e,t,o)=>{const n=t.win,s=t.box,r=La(t);return se(e,(e=>{switch(e){case"bottom":return Ra(s,o.bounds)?A.none():A.some(za(n,r,o));case"top":return Ia(s,o.bounds)?A.none():A.some(Na(n,r,o));default:return A.none()}})).getOr({location:"no-dock"})})(o.getModes(),{win:s,box:n},t);return"top"===r.location||"bottom"===r.location?(Ha(e,n,t,o,r),Ua(r)):A.none()})(n,t,o)},ja=(e,t,o)=>{o.setDocked(!1),V(["left","right","top","bottom","position"],(t=>Lt(e.element,t))),t.onUndocked(e)},$a=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),Aa(e.element,n),(s?t.onDocked:t.onUndocked)(e)},Ga=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n.bounds);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(Oa(e.element,[t.fadeOutClass]),t.onHide(e)):(a?Ba:Fa)(e,t))}))}))},qa=(e,t,o,n,s)=>{Ga(e,t,o,n,!0),$a(e,t,o,s.positionCss)},Ya=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);Ga(e,t,o,n),Wa(e,n,o).each((s=>{((e,t,o,n,s)=>{switch(s.morph){case"static":return ja(e,t,o);case"absolute":return $a(e,t,o,s.positionCss);case"fixed":qa(e,t,o,n,s)}})(e,t,o,n,s)}))})(e,t,o)},Xa=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1);const s=t.lazyViewport(e);((e,t,o)=>{const n=e.element;return Va(n,t,o).bind((({box:e})=>Pa(n,e,o)))})(e,s,o).each((n=>{switch(n.morph){case"static":ja(e,t,o);break;case"absolute":$a(e,t,o,n.positionCss)}})),o.setVisible(!0),t.contextual.each((t=>{_a(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),Ya(e,t,o)})(e,t,o)},Ka=e=>(t,o,n)=>{const s=o.lazyViewport(t);((e,t,o,n)=>{const s=Ko(e),r=Zo(),a=n(r,La({win:r,box:s}),t);return"bottom"===a.location||"top"===a.location?(((e,t,o,n,s)=>{n.getInitialPos().fold((()=>Ha(e,t,o,n,s)),(()=>b))})(e,s,t,o,a),Ua(a)):A.none()})(t.element,s,n,e).each((e=>{qa(t,o,n,s,e)}))},Ja=Ka(Na),Qa=Ka(za);var Za,ei=Object.freeze({__proto__:null,refresh:Ya,reset:Xa,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n),forceDockToTop:Ja,forceDockToBottom:Qa}),ti=Object.freeze({__proto__:null,events:(e,t)=>Pr([Jr(sr(),((o,n)=>{e.contextual.each((e=>{ka(o.element,e.transitionClass)&&(_a(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),jr(Sr(),((o,n)=>{Ya(o,e,t)})),jr(Mr(),((o,n)=>{Ya(o,e,t)})),jr(Cr(),((o,n)=>{Xa(o,e,t)}))])});const oi=e=>e.dom.innerHTML,ni=(e,t)=>{const o=tt(e).dom,n=ze(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,lt(ze(o))})(t,o);Lo(n,s),Vo(e),zo(e,n)},si=(e,t)=>ze(e.dom.cloneNode(t)),ri=e=>(e=>{if(gt(e))return"#shadow-root";{const t=(e=>si(e,!1))(e);return(e=>{const t=Re("div"),o=ze(e.dom.cloneNode(!0));return zo(t,o),oi(t)})(t)}})(e);var ai;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(ai||(ai={}));const ii=en({}),li=["alloy/data/Fields","alloy/debugging/Debugging"],ci=(e,t,o)=>((e,t,o)=>{switch(fe(ii.get(),e).orThunk((()=>{const t=re(ii.get());return se(t,(t=>e.indexOf(t)>-1?A.some(ii.get()[t]):A.none()))})).getOr(ai.NORMAL)){case ai.NORMAL:return o(di());case ai.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();I(["mousemove","mouseover","mouseout",yr()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:L(o,(e=>I(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+ri(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case ai.STOP:return!0}})(e,t,o),di=x({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),ui=x([rs("menu"),rs("selectedMenu")]),mi=x([rs("item"),rs("selectedItem")]);x(In(mi().concat(ui())));const gi=x(In(mi())),pi=us("initSize",[rs("numColumns"),rs("numRows")]),hi=()=>us("markers",[rs("backgroundMenu")].concat(ui()).concat(mi())),fi=e=>us("markers",L(e,rs)),bi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");j(t,(e=>e.indexOf("alloy")>0&&!R(li,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),os(t,t,o,Yn((e=>ln.value(((...t)=>e.apply(void 0,t))))))),vi=e=>bi(0,e,Sn(b)),yi=e=>bi(0,e,Sn(A.none)),xi=e=>bi(0,e,{tag:"required",process:{}}),wi=e=>bi(0,e,{tag:"required",process:{}}),Si=(e,t)=>ns(e,x(t)),Ci=e=>ns(e,w),ki=x(pi);var Oi=[ws("contextual",[ls("fadeInClass"),ls("fadeOutClass"),ls("transitionClass"),ds("lazyContext"),vi("onShow"),vi("onShown"),vi("onHide"),vi("onHidden")]),Es("lazyViewport",(()=>({bounds:Zo(),optScrollEnv:A.none()}))),As("modes",["top","bottom"],Un),vi("onDocked"),vi("onUndocked")];const _i=ma({fields:Oi,name:"docking",active:ti,apis:ei,state:Object.freeze({__proto__:null,init:e=>{const t=en(!1),o=en(!0),n=nn(),s=en(e.modes);return ca({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),Ti=Pr([((e,t)=>({key:e,value:Vs({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>Ze(t,e.element)&&!Ze(t,o))(e,n,s)||(console.warn(cr()+" did not get interpreted by the desired target. \nOriginator: "+ri(n)+"\nTarget: "+ri(s)+"\nCheck the "+cr()+" event handlers"),!1)}})}))(cr())]);var Ei=Object.freeze({__proto__:null,events:Ti});const Ai=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Mi=(e,t,o)=>Math.min(Math.max(e,t),o);let Di=0;const Bi=e=>{const t=(new Date).getTime(),o=Math.floor(window.crypto.getRandomValues(new Uint32Array(1))[0]/4294967295*1e9);return Di++,e+"_"+o+Di+String(t)},Fi=x("alloy-id-"),Ii=x("data-alloy-id"),Ri=Fi(),Ni=Ii(),zi=(e,t)=>{Object.defineProperty(e.dom,Ni,{value:t,writable:!0})},Li=e=>{const t=$e(e)?e.dom[Ni]:null;return A.from(t)},Vi=e=>Bi(e),Hi=w,Pi=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+ri(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:x("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:T}},Ui=Pi(),Wi=Bi("alloy-premade"),ji=e=>(Object.defineProperty(e.element.dom,Wi,{value:e.uid,writable:!0}),Bs(Wi,e)),$i=e=>fe(e,Wi),Gi=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:oa(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),qi=(e,t)=>{const o={};return ie(e,((e,n)=>{ie(e,((e,s)=>{const r=fe(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},Yi=e=>e.cHandler,Xi=(e,t)=>({name:e,handler:t}),Ki=(e,t)=>{const o={};return V(e,(e=>{o[e.name()]=e.handlers(t)})),o},Ji=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const s=Z(o,((o,s)=>{const r=o[t],a=s[t],i=n.indexOf(r),l=n.indexOf(a);if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===l)throw new Error("The ordering for "+e+" does not have an entry for "+a+".\nOrder specified: "+JSON.stringify(n,null,2));return i<l?-1:l<i?1:0}));return ln.value(s)}catch(e){return ln.error([e])}})("Event: "+o,"name",e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>W(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>W(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{V(e,(e=>{e.run.apply(void 0,t)}))}}})(L(e,(e=>e.handler))))):((e,t)=>ln.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(L(t,(e=>e.name)),null,2)]))(o,e)},Qi=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return V(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,ln.error(G(n))):((e,t)=>0===e.length?ln.value(t):ln.value(yn(t,xn.apply(void 0,e))))(o.values,t);var n})(ge(e,((e,o)=>(1===e.length?ln.value(e[0].handler):Ji(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:E,abort:T,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?P(t[o],(t=>R(e,(e=>e.name===t)))).join(" > "):e[0].name;return Bs(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),Zi="alloy.base.behaviour",el=In([os("dom","dom",{tag:"required",process:{}},In([rs("tag"),Ss("styles",{}),Ss("classes",[]),Ss("attributes",{}),ps("value"),ps("innerHtml")])),rs("components"),rs("uid"),Ss("events",{}),Ss("apis",{}),os("eventOrder","eventOrder",(bl={[gr()]:["disabling",Zi,"toggling","typeaheadevents"],[cr()]:[Zi,"focusing","keying"],[yr()]:[Zi,"disabling","toggling","representing","tooltipping"],[er()]:[Zi,"representing","streaming","invalidating"],[Or()]:[Zi,"representing","item-events","toolbar-button-events","tooltipping"],[$s()]:["focusing",Zi,"item-type-events"],[Ps()]:["focusing",Zi,"item-type-events"],[Xs()]:["item-type-events","tooltipping"],[mr()]:["receiving","reflecting","tooltipping"]},Cn(x(bl))),Vn()),ps("domModification")]),tl=e=>e.events,ol=e=>e.dom.value,nl=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},sl=(e,t,o)=>{o.fold((()=>zo(e,t)),(e=>{Ze(e,t)||(Io(e,t),Ho(e))}))},rl=(e,t,o)=>{const n=L(t,o),s=lt(e);return V(s.slice(n.length),Ho),n},al=(e,t,o,n)=>{const s=ct(e,t),r=n(o,s),a=((e,t,o)=>ct(e,t).map((e=>{if(o.exists((t=>!Ze(t,e)))){const t=o.map(Ue).getOr("span"),n=Re(t);return Io(e,n),n}return e})))(e,t,s);return sl(e,r.element,a),r},il=(e,t)=>{const o=re(e),n=re(t),s=K(n,o),r=((e,o)=>{const n={},s={};return ue(e,((e,o)=>!be(t,o)||e!==t[o]),de(n),de(s)),{t:n,f:s}})(e).t;return{toRemove:s,toSet:r}},ll=(e,t)=>{const o=t.filter((t=>Ue(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>be(e.dom,Wi))(t))).bind((t=>((e,t)=>{try{const o=((e,t)=>{const{class:o,style:n,...s}=(e=>W(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=il(e.attributes,s),i=Nt(t),{toSet:l,toRemove:c}=il(e.styles,i),d=Ta(t),u=K(d,e.classes),m=K(e.classes,d);return V(a,(e=>Tt(t,e))),Ct(t,r),Oa(t,m),_a(t,u),V(c,(e=>Lt(t,e))),Dt(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{rl(e,t,((t,o)=>{const n=ct(e,o);return sl(e,t,n),t}))})(t,o)}),(e=>{ni(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==ol(o)&&nl(o,null!=n?n:"")})(),t})(e,t);return A.some(o)}catch(e){return A.none()}})(e,t))).getOrThunk((()=>(e=>{const t=Re(e.tag);Ct(t,e.attributes),Oa(t,e.classes),Dt(t,e.styles),e.innerHtml.each((e=>ni(t,e)));const o=e.domChildren;return Lo(t,o),e.value.each((e=>{nl(t,e)})),t})(e)));return zi(o,e.uid),o},cl=e=>{const t=(e=>{const t=fe(e,"behaviours").getOr({});return q(re(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=L(t,(e=>ws(e.name(),[rs("config"),Ss("state",la)]))),n=Kn("component.behaviours",In(o),e.behaviours).fold((t=>{throw new Error(Zn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),w);return{list:t,data:le(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return x(t)}))}})(e,t))(e,t)},dl=(e,t)=>{const o=()=>m,n=en(Ui),s=Jn((e=>Kn("custom.definition",el,e))(e)),r=cl(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:L(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>sa({})),sa))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};V(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=qi(s,((e,t)=>({name:e,modification:t}))),a=e=>U(e,((e,t)=>({...t.modification,...e})),{}),i=U(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return sa({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=ll(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":tl(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...Ki(t,e)};return qi(n,Xi)})(e,o,n);return Qi(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=en(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(Pi(o))},element:c,syncComponents:()=>{const e=lt(c),t=q(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},ul=e=>{const t=Ne(e);return ml({element:t})},ml=e=>{const t=Qn("external.component",Fn([rs("element"),ps("uid")]),e),o=en(Pi()),n=t.uid.getOrThunk((()=>Vi("external")));zi(t.element,n);const s={uid:n,getSystem:o.get,config:A.none,hasConfigured:T,connect:e=>{o.set(e)},disconnect:()=>{o.set(Pi((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:x("No state"),syncComponents:b,components:x([]),events:{}};return ji(s)},gl=Vi,pl=(e,t)=>$i(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=Hi(e),s=((e,t)=>{const o=fe(e,"components").getOr([]);return t.fold((()=>L(o,hl)),(e=>L(o,((t,o)=>pl(t,ct(e,o))))))})(n,t),r={...n,events:{...Ei,...o},components:s};return ln.value(dl(r,t))})((e=>be(e,"uid"))(e)?e:{uid:gl(""),...e},t).getOrDie())),hl=e=>pl(e,A.none()),fl=ji;var bl,vl=(e,t,o,n,s)=>e(o,n)?A.some(o):p(s)&&s(o)?A.none():t(o,n,s);const yl=(e,t,o)=>{let n=e.dom;const s=p(o)?o:T;for(;n.parentNode;){n=n.parentNode;const e=ze(n);if(t(e))return A.some(e);if(s(e))break}return A.none()},xl=(e,t,o)=>vl(((e,t)=>t(e)),yl,e,t,o),wl=(e,t)=>j(e.dom.childNodes,(e=>t(ze(e)))).map(ze),Sl=(e,t,o)=>xl(e,t,o).isSome(),Cl=(e,t,o)=>yl(e,(e=>Ke(e,t)),o),kl=(e,t)=>((e,o)=>{const n=e.dom;return n.parentNode?wl(ze(n.parentNode),(o=>!Ze(e,o)&&Ke(o,t))):A.none()})(e),Ol=(e,t)=>wl(e,(e=>Ke(e,t))),_l=(e,t)=>Qe(t,e),Tl=(e,t,o)=>vl(((e,t)=>Ke(e,t)),Cl,e,t,o),El="aria-controls",Al=()=>{const e=Bi(El);return{id:e,link:t=>{St(t,El,e)},unlink:e=>{Tt(e,El)}}},Ml=(e,t)=>Sl(t,(t=>Ze(t,e.element)),T)||((e,t)=>(e=>xl(e,(e=>{if(!$e(e))return!1;const t=kt(e,"id");return void 0!==t&&t.indexOf(El)>-1})).bind((e=>{const t=kt(e,"id"),o=pt(e);return _l(o,`[${El}="${t}"]`)})))(t).exists((t=>Ml(e,t))))(e,t),Dl=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),Bl=Ds([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Fl=Bl.southeast,Il=Bl.southwest,Rl=Bl.northeast,Nl=Bl.northwest,zl=Bl.south,Ll=Bl.north,Vl=Bl.east,Hl=Bl.west,Pl=(e,t)=>J(["left","right","top","bottom"],(o=>fe(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),Ul="layout",Wl=e=>e.x,jl=(e,t)=>e.x+e.width/2-t.width/2,$l=(e,t)=>e.x+e.width-t.width,Gl=(e,t)=>e.y-t.height,ql=e=>e.y+e.height,Yl=(e,t)=>e.y+e.height/2-t.height/2,Xl=(e,t,o)=>Dl(Wl(e),ql(e),o.southeast(),Fl(),"southeast",Pl(e,{left:1,top:3}),Ul),Kl=(e,t,o)=>Dl($l(e,t),ql(e),o.southwest(),Il(),"southwest",Pl(e,{right:0,top:3}),Ul),Jl=(e,t,o)=>Dl(Wl(e),Gl(e,t),o.northeast(),Rl(),"northeast",Pl(e,{left:1,bottom:2}),Ul),Ql=(e,t,o)=>Dl($l(e,t),Gl(e,t),o.northwest(),Nl(),"northwest",Pl(e,{right:0,bottom:2}),Ul),Zl=(e,t,o)=>Dl(jl(e,t),Gl(e,t),o.north(),Ll(),"north",Pl(e,{bottom:2}),Ul),ec=(e,t,o)=>Dl(jl(e,t),ql(e),o.south(),zl(),"south",Pl(e,{top:3}),Ul),tc=(e,t,o)=>Dl((e=>e.x+e.width)(e),Yl(e,t),o.east(),Vl(),"east",Pl(e,{left:0}),Ul),oc=(e,t,o)=>Dl(((e,t)=>e.x-t.width)(e,t),Yl(e,t),o.west(),Hl(),"west",Pl(e,{right:1}),Ul),nc=()=>[Xl,Kl,Jl,Ql,ec,Zl,tc,oc],sc=()=>[Kl,Xl,Ql,Jl,ec,Zl,tc,oc],rc=()=>[Jl,Ql,Xl,Kl,Zl,ec],ac=()=>[Ql,Jl,Kl,Xl,Zl,ec],ic=()=>[Xl,Kl,Jl,Ql,ec,Zl],lc=()=>[Kl,Xl,Ql,Jl,ec,Zl];var cc=Object.freeze({__proto__:null,events:e=>Pr([jr(mr(),((t,o)=>{const n=e.channels,s=re(n),r=o,a=((e,t)=>t.universal?e:P(e,(e=>I(t.channels,e))))(s,r);V(a,(e=>{const o=n[e],s=o.schema,a=Qn("channel["+e+"] data\nReceiver: "+ri(t.element),s,r.data);o.onReceive(t,a)}))}))])}),dc=[as("channels",Xn(ln.value,Fn([xi("onReceive"),Ss("schema",Vn())])))];const uc=ma({fields:dc,name:"receiving",active:cc});var mc=Object.freeze({__proto__:null,exhibit:(e,t)=>sa({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const gc=(e,t=!1)=>e.dom.focus({preventScroll:t}),pc=e=>e.dom.blur(),hc=e=>{const t=pt(e).dom;return e.dom===t.activeElement},fc=(e=$o())=>A.from(e.dom.activeElement).map(ze),bc=e=>fc(pt(e)).filter((t=>e.dom.contains(t.dom))),vc=(e,t)=>{const o=pt(t),n=fc(o).bind((e=>{const o=t=>Ze(e,t);return o(t)?A.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=ze(e.childNodes[n]);if(t(s))return A.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return A.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{fc(o).filter((t=>Ze(t,e))).fold((()=>{gc(e)}),b)})),s},yc=Ds([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),xc=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=A.some(i),m=A.some(l),g=A.some(c),p=A.some(d),h=A.none();return t.direction.fold((()=>Ea(e,u,m,h,h)),(()=>Ea(e,h,m,g,h)),(()=>Ea(e,u,h,h,p)),(()=>Ea(e,h,h,g,p)),(()=>Ea(e,u,m,h,h)),(()=>Ea(e,u,h,h,p)),(()=>Ea(e,u,m,h,h)),(()=>Ea(e,h,m,g,h)))},wc=(e,t)=>e.fold((()=>{const e=t.rect;return Ea("absolute",A.some(e.x),A.some(e.y),A.none(),A.none())}),((e,o,n,s)=>xc("absolute",t,e,o,n,s)),((e,o,n,s)=>xc("fixed",t,e,o,n,s))),Sc=(e,t)=>{const o=C(Yo,t),n=e.fold(o,o,(()=>{const e=Po();return Yo(t).translate(-e.left,-e.top)})),s=Jt(t),r=Wt(t);return Xo(n.left,n.top,s,r)},Cc=(e,t)=>t.fold((()=>e.fold(Zo,Zo,Xo)),(t=>e.fold(x(t),x(t),(()=>{const o=kc(e,t.x,t.y);return Xo(o.left,o.top,t.width,t.height)})))),kc=(e,t,o)=>{const n=$t(t,o);return e.fold(x(n),x(n),(()=>{const e=Po();return n.translate(-e.left,-e.top)}))};yc.none;const Oc=yc.relative,_c=yc.fixed,Tc="data-alloy-placement",Ec=e=>Ot(e,Tc),Ac=Ds([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Mc=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Mi(i,e.y,e.bottom):Mi(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Xo(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Xo(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Mi(a,o,d),g=Mi(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Xo(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=x(t.bottom-o.y),s=x(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=x(t.right-o.x),i=x(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),y={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?Ac.fit(y):Ac.nofit(y,m,g,f)},Dc=E,Bc=(e,t,o)=>((e,t,o,n)=>Bo(e,t,o,n,!1))(e,t,Dc,o),Fc=(e,t,o)=>((e,t,o,n)=>Bo(e,t,o,n,!0))(e,t,Dc,o),Ic=Do,Rc=["top","bottom","right","left"],Nc="data-alloy-transition-timer",zc=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>((e,t)=>Y(t,(t=>ka(e,t))))(e,t.classes))(e,n)){Mt(e,"position",o.position);const a=Sc(t,e),l=wc(t,{...s,rect:a}),c=J(Rc,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return pe(t,((t,n)=>!((e,t,o=S)=>we(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Bt(e,c),i&&((e,t)=>{Oa(e,t.classes),Ot(e,Nc).each((t=>{clearTimeout(parseInt(t,10)),Tt(e,Nc)})),((e,t)=>{const o=on(),n=on();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return Ze(t.target,e)&&De(n)&&I(Rc,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===sr())&&(clearTimeout(s),Tt(e,Nc),_a(e,t.classes))}},l=Bc(e,rr(),(t=>{a(t)&&(l.unbind(),o.set(Bc(e,sr(),i)),n.set(Bc(e,nr(),i)))})),c=(e=>{const t=t=>{const o=Ft(e,t).split(/\s*,\s*/);return P(o,Me)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Ee(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return W(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),St(e,Nc,s)}))})(e,t)})(e,n),Vt(e))}else _a(e,n.classes)},Lc=(e,t)=>{((e,t)=>{const o=Pt.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);Mt(e,"max-height",o+"px")})(e,Math.floor(t))},Vc=x(((e,t)=>{Lc(e,t),Dt(e,{"overflow-x":"hidden","overflow-y":"auto"})})),Hc=x(((e,t)=>{Lc(e,t)})),Pc=(e,t,o)=>void 0===e[t]?o:e[t],Uc=(e,t,o,n)=>{const s=((e,t,o,n)=>{Lt(t,"max-height"),Lt(t,"max-width");const s={width:Jt(r=t),height:Wt(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=Mc(m,a,i,r);return g.fold(x(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:Ac.nofit(l,c,d,u)))};return W(t,((e,t)=>{const o=C(l,t);return e.fold(x(e),o)}),Ac.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Fl(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(w,w)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=wc(o.origin,t);o.transition.each((s=>{zc(e,o.origin,n,s,t,o.lastPlacement)})),Aa(e,n)})(t,s,n),((e,t)=>{((e,t)=>{St(e,Tc,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;_a(e,o.off),Oa(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},Wc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],jc=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>fe(o,e).getOr([]),i=(e,t,o)=>{const n=K(Wc,o);return{offset:$t(e,t),classesOn:q(o,a),classesOff:q(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},$c=()=>jc(0,0,{}),Gc=w,qc=(e,t)=>o=>"rtl"===Yc(o)?t:e,Yc=e=>"rtl"===Ft(e,"direction")?"rtl":"ltr";var Xc;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(Xc||(Xc={}));const Kc="data-alloy-vertical-dir",Jc=e=>Sl(e,(e=>$e(e)&&kt(e,"data-alloy-vertical-dir")===Xc.BottomToTop)),Qc=()=>ws("layouts",[rs("onLtr"),rs("onRtl"),ps("onBottomLtr"),ps("onBottomRtl")]),Zc=(e,t,o,n,s,r,a)=>{const i=a.map(Jc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return qc(d,u)(e)};var ed=[rs("hotspot"),ps("bubble"),Ss("overrides",{}),Qc(),Si("placement",((e,t,o)=>{const n=t.hotspot,s=Sc(o,n.element),r=Zc(e.element,t,ic(),lc(),rc(),ac(),A.some(t.hotspot.element));return A.some(Gc({anchorBox:s,bubble:t.bubble.getOr($c()),overrides:t.overrides,layouts:r}))}))],td=[rs("x"),rs("y"),Ss("height",0),Ss("width",0),Ss("bubble",$c()),Ss("overrides",{}),Qc(),Si("placement",((e,t,o)=>{const n=kc(o,t.x,t.y),s=Xo(n.left,n.top,t.width,t.height),r=Zc(e.element,t,nc(),sc(),nc(),sc(),A.none());return A.some(Gc({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r}))}))];const od=Ds([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),nd=e=>e.fold(w,((e,t,o)=>e.translate(-t,-o))),sd=e=>e.fold(w,w),rd=e=>W(e,((e,t)=>e.translate(t.left,t.top)),$t(0,0)),ad=e=>{const t=L(e,sd);return rd(t)},id=od.screen,ld=od.absolute,cd=(e,t,o)=>{const n=tt(e.element),s=Po(n),r=((e,t,o)=>{const n=st(o.root).dom;return A.from(n.frameElement).map(ze).filter((t=>{const o=tt(t),n=tt(e.element);return Ze(o,n)})).map(qt)})(e,0,o).getOr(s);return ld(r,s.left,s.top)},dd=(e,t,o,n)=>{const s=id($t(e,t));return A.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},ud=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>ad(r),l=()=>ad(r),c=()=>(e=>{const t=L(e,nd);return rd(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?rc():ic(),m=o.showAbove?ac():lc(),g=Zc(s,o,u,m,u,m,A.none());var p,h,f,b;return Gc({anchorBox:d,bubble:o.bubble.getOr($c()),overrides:o.overrides,layouts:g})}));var md=[rs("node"),rs("root"),ps("bubble"),Qc(),Ss("overrides",{}),Ss("showAbove",!1),Si("placement",((e,t,o)=>{const n=cd(e,0,t);return t.node.filter(vt).bind((s=>{const r=s.dom.getBoundingClientRect(),a=dd(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return ud(a,n,t,o,i)}))}))];const gd=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),pd=Ds([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),hd=(pd.before,pd.on,pd.after,e=>e.fold(w,w,w)),fd=Ds([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),bd={domRange:fd.domRange,relative:fd.relative,exact:fd.exact,exactFromRange:e=>fd.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>ze(e.startContainer),relative:(e,t)=>hd(e),exact:(e,t,o,n)=>e}))(e);return st(t)},range:gd},vd=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},yd=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},xd=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),wd=Ds([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Sd=(e,t,o)=>t(ze(o.startContainer),o.startOffset,ze(o.endContainer),o.endOffset),Cd=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:x(e),rtl:A.none}),relative:(t,o)=>({ltr:Qt((()=>vd(e,t,o))),rtl:Qt((()=>A.some(vd(e,o,t))))}),exact:(t,o,n,s)=>({ltr:Qt((()=>yd(e,t,o,n,s))),rtl:Qt((()=>A.some(yd(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>wd.rtl(ze(e.endContainer),e.endOffset,ze(e.startContainer),e.startOffset))).getOrThunk((()=>Sd(0,wd.ltr,o))):Sd(0,wd.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});wd.ltr,wd.rtl;const kd=(e,t,o)=>P(((e,t)=>{const o=p(t)?t:T;let n=e.dom;const s=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=ze(e);if(s.push(t),!0===o(t))break;n=e}return s})(e,o),t),Od=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Je(o)?[]:L(o.querySelectorAll(e),ze)})(t,e),_d=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return A.some(gd(ze(t.startContainer),t.startOffset,ze(o.endContainer),o.endOffset))}return A.none()},Td=e=>{if(null===e.anchorNode||null===e.focusNode)return _d(e);{const t=ze(e.anchorNode),o=ze(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=tt(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=Ze(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?A.some(gd(t,e.anchorOffset,o,e.focusOffset)):_d(e)}},Ed=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?A.some(o).map(xd):A.none()})(Cd(e,t)),Ad=((e,t)=>{const o=t=>e(t)?A.from(t.dom.nodeValue):A.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(Ge),Md=(e,t)=>({element:e,offset:t}),Dd=(e,t)=>Ge(e)?Md(e,t):((e,t)=>{const o=lt(e);if(0===o.length)return Md(e,t);if(t<o.length)return Md(o[t],0);{const e=o[o.length-1],t=Ge(e)?(e=>Ad.get(e))(e).length:lt(e).length;return Md(e,t)}})(e,t),Bd=e=>void 0!==e.foffset,Fd=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>A.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(Td))(e)))().map((e=>{if(Bd(e)){const t=Dd(e.start,e.soffset),o=Dd(e.finish,e.foffset);return bd.range(t.element,t.offset,o.element,o.offset)}return e}));var Id=[ps("getSelection"),rs("root"),ps("bubble"),Qc(),Ss("overrides",{}),Ss("showAbove",!1),Si("placement",((e,t,o)=>{const n=st(t.root).dom,s=cd(e,0,t),r=Fd(n,t).bind((e=>{if(Bd(e)){const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?A.some(t).map(xd):A.none()})(Cd(e,t)))(n,bd.exactFromRange(e)).orThunk((()=>{const t=Ne("\ufeff");Io(e.start,t);const o=Ed(n,bd.exact(t,0,t,1));return Ho(t),o}));return t.bind((e=>dd(e.left,e.top,e.width,e.height)))}{const t=le(e,(e=>e.dom.getBoundingClientRect())),o={left:Math.min(t.firstCell.left,t.lastCell.left),right:Math.max(t.firstCell.right,t.lastCell.right),top:Math.min(t.firstCell.top,t.lastCell.top),bottom:Math.max(t.firstCell.bottom,t.lastCell.bottom)};return dd(o.left,o.top,o.right-o.left,o.bottom-o.top)}})),a=Fd(n,t).bind((e=>Bd(e)?$e(e.start)?A.some(e.start):at(e.start):A.some(e.firstCell))).getOr(e.element);return ud(r,s,t,o,a)}))];const Rd="link-layout",Nd=e=>e.x+e.width,zd=(e,t)=>e.x-t.width,Ld=(e,t)=>e.y-t.height+e.height,Vd=e=>e.y,Hd=(e,t,o)=>Dl(Nd(e),Vd(e),o.southeast(),Fl(),"southeast",Pl(e,{left:0,top:2}),Rd),Pd=(e,t,o)=>Dl(zd(e,t),Vd(e),o.southwest(),Il(),"southwest",Pl(e,{right:1,top:2}),Rd),Ud=(e,t,o)=>Dl(Nd(e),Ld(e,t),o.northeast(),Rl(),"northeast",Pl(e,{left:0,bottom:3}),Rd),Wd=(e,t,o)=>Dl(zd(e,t),Ld(e,t),o.northwest(),Nl(),"northwest",Pl(e,{right:1,bottom:3}),Rd),jd=()=>[Hd,Pd,Ud,Wd],$d=()=>[Pd,Hd,Wd,Ud];var Gd=[rs("item"),Qc(),Ss("overrides",{}),Si("placement",((e,t,o)=>{const n=Sc(o,t.item.element),s=Zc(e.element,t,jd(),$d(),jd(),$d(),A.none());return A.some(Gc({anchorBox:n,bubble:$c(),overrides:t.overrides,layouts:s}))}))],qd=es("type",{selection:Id,node:md,hotspot:ed,submenu:Gd,makeshift:td});const Yd=[gs("classes",Un),_s("mode","all",["all","layout","placement"])],Xd=[Ss("useFixed",T),ps("getBounds")],Kd=[as("anchor",qd),ws("transition",Yd)],Jd=(e,t,o,n,s,r)=>{const a=Qn("placement.info",In(Kd),s),i=a.anchor,l=n.element,c=o.get(n.uid);vc((()=>{Mt(l,"position","fixed");const s=Rt(l,"visibility");Mt(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return _c(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=qt(e.element),o=e.element.dom.getBoundingClientRect();return Oc(t.left,t.top,o.width,o.height)})(e);i.placement(e,i,d).each((e=>{const s=r.orThunk((()=>t.getBounds.map(_))),i=((e,t,o,n,s,r)=>((e,t,o,n,s,r,a,i)=>{const l=Pc(a,"maxHeightFunction",Vc()),c=Pc(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:Cc(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return Uc(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(t.anchorBox,e),n.element,t.bubble,t.layouts,s,o,t.overrides,r))(d,e,s,n,c,a.transition);o.set(n.uid,i)})),s.fold((()=>{Lt(l,"visibility")}),(e=>{Mt(l,"visibility",e)})),Rt(l,"left").isNone()&&Rt(l,"top").isNone()&&Rt(l,"right").isNone()&&Rt(l,"bottom").isNone()&&ye(Rt(l,"position"),"fixed")&&Lt(l,"position")}),l)};var Qd=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{const r=A.none();Jd(e,t,o,n,s,r)},positionWithinBounds:Jd,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;V(["position","left","right","top","bottom"],(e=>Lt(s,e))),(e=>{Tt(e,Tc)})(s),o.clear(n.uid)}});const Zd=ma({fields:Xd,name:"positioning",active:mc,apis:Qd,state:Object.freeze({__proto__:null,init:()=>{let e={};return ca({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>fe(e,t)})}})}),eu=e=>e.getSystem().isConnected(),tu=e=>{Rr(e,Or());const t=e.components();V(t,tu)},ou=e=>{const t=e.components();V(t,ou),Rr(e,kr())},nu=(e,t)=>{e.getSystem().addToWorld(t),vt(e.element)&&ou(t)},su=e=>{tu(e),e.getSystem().removeFromWorld(e)},ru=(e,t)=>{zo(e.element,t.element)},au=(e,t)=>{iu(e,t,zo)},iu=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),vt(e.element)&&ou(t),e.syncComponents()},lu=e=>{tu(e),Ho(e.element),e.getSystem().removeFromWorld(e)},cu=e=>{const t=rt(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));lu(e),t.each((e=>{e.syncComponents()}))},du=e=>{const t=e.components();V(t,lu),Vo(e.element),e.syncComponents()},uu=(e,t)=>{gu(e,t,zo)},mu=(e,t)=>{gu(e,t,Ro)},gu=(e,t,o)=>{o(e,t.element);const n=lt(t.element);V(n,(e=>{t.getByDom(e).each(ou)}))},pu=e=>{const t=lt(e.element);V(t,(t=>{e.getByDom(t).each(tu)})),Ho(e.element)},hu=(e,t,o,n)=>{o.get().each((t=>{du(e)}));const s=t.getAttachPoint(e);au(s,e);const r=e.getSystem().build(n);return au(e,r),o.set(r),r},fu=(e,t,o,n)=>{const s=hu(e,t,o,n);return t.onOpen(e,s),s},bu=(e,t,o)=>{o.get().each((n=>{du(e),cu(e),t.onClose(e,n),o.clear()}))},vu=(e,t,o)=>o.isOpen(),yu=(e,t,o)=>{const n=t.getAttachPoint(e);Mt(e.element,"position",Zd.getMode(n)),((e,t,o,n)=>{Rt(e.element,t).fold((()=>{Tt(e.element,o)}),(t=>{St(e.element,o,t)})),Mt(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},xu=(e,t,o)=>{(e=>R(["top","left","right","bottom"],(t=>Rt(e,t).isSome())))(e.element)||Lt(e.element,"position"),((e,t,o)=>{Ot(e.element,o).fold((()=>Lt(e.element,t)),(o=>Mt(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var wu=Object.freeze({__proto__:null,cloak:yu,decloak:xu,open:fu,openWhileCloaked:(e,t,o,n,s)=>{yu(e,t),fu(e,t,o,n),s(),xu(e,t)},close:bu,isOpen:vu,isPartOf:(e,t,o,n)=>vu(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>hu(e,t,o,n)))}),Su=Object.freeze({__proto__:null,events:(e,t)=>Pr([jr(br(),((o,n)=>{bu(o,e,t)}))])}),Cu=[vi("onOpen"),vi("onClose"),rs("isPartOf"),rs("getAttachPoint"),Ss("cloakVisibilityAttr","data-precloak-visibility")],ku=Object.freeze({__proto__:null,init:()=>{const e=nn(),t=x("not-implemented");return ca({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Ou=ma({fields:Cu,name:"sandboxing",active:Su,apis:wu,state:ku}),_u=x("dismiss.popups"),Tu=x("reposition.popups"),Eu=x("mouse.released"),Au=Fn([Ss("isExtraPart",T),ws("fireEventInstead",[Ss("event",_r())])]),Mu=e=>{const t=Qn("Dismissal",Au,e);return{[_u()]:{schema:Fn([rs("target")]),onReceive:(e,o)=>{Ou.isOpen(e)&&(Ou.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Ou.close(e)),(t=>Rr(e,t.event))))}}}},Du=Fn([ws("fireEventInstead",[Ss("event",Tr())]),ds("doReposition")]),Bu=e=>{const t=Qn("Reposition",Du,e);return{[Tu()]:{onReceive:e=>{Ou.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>Rr(e,t.event)))}}}},Fu=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},Iu=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var Ru=Object.freeze({__proto__:null,onLoad:Fu,onUnload:Iu,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),Nu=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Qr(((o,n)=>{Fu(o,e,t)})),Zr(((o,n)=>{Iu(o,e,t)}))]:[ra(e,t,Fu)];return Pr(o)}});const zu=()=>{const e=en(null);return ca({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},Lu=()=>{const e=en({}),t=en({});return ca({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>fe(e.get(),o).orThunk((()=>fe(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};V(o,(e=>{r[e.value]=e,fe(e,"meta").each((t=>{fe(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var Vu=Object.freeze({__proto__:null,memory:zu,dataset:Lu,manual:()=>ca({readState:b}),init:e=>e.store.manager.state(e)});const Hu=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var Pu=[ps("initialValue"),rs("getFallbackEntry"),rs("getDataKey"),rs("setValue"),Si("manager",{setValue:Hu,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{Hu(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:Lu})],Uu=[rs("getValue"),Ss("setValue",b),ps("initialValue"),Si("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:la.init})],Wu=[ps("initialValue"),Si("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:zu})],ju=[Cs("store",{mode:"memory"},es("mode",{memory:Wu,manual:Uu,dataset:Pu})),vi("onSetValue"),Ss("resetOnDom",!1)];const $u=ma({fields:ju,name:"representing",active:Nu,apis:Ru,extra:{setValueFrom:(e,t)=>{const o=$u.getValue(t);$u.setValue(e,o)}},state:Vu}),Gu=(e,t)=>Ms(e,{},L(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,os(o,o,{tag:"option",process:{}},Tn((e=>pn("The field: "+o+" is forbidden. "+n))));var o,n})).concat([ns("dump",w)])),qu=e=>e.dump,Yu=(e,t)=>({...da(t),...e.dump}),Xu=Gu,Ku=Yu,Ju="placeholder",Qu=Ds([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Zu=e=>be(e,"uiType"),em=(e,t,o,n)=>((e,t,o,n)=>Zu(o)&&o.uiType===Ju?((e,t,o,n)=>e.exists((e=>e!==o.owner))?Qu.single(!0,x(o)):fe(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+re(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):Qu.single(!1,x(o)))(e,0,o,n).fold(((s,r)=>{const a=Zu(o)?r(t,o.config,o.validated):r(t),i=fe(a,"components").getOr([]),l=q(i,(o=>em(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(Zu(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(w)(e)}return n(t)})),tm=Qu.single,om=Qu.multiple,nm=x(Ju),sm=Ds([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),rm=Ss("factory",{sketch:w}),am=Ss("schema",[]),im=rs("name"),lm=os("pname","pname",wn((e=>"<alloy."+Bi(e.name)+">")),Vn()),cm=ns("schema",(()=>[ps("preprocess")])),dm=Ss("defaults",x({})),um=Ss("overrides",x({})),mm=In([rm,am,im,lm,dm,um]),gm=In([rm,am,im,dm,um]),pm=In([rm,am,im,lm,dm,um]),hm=In([rm,cm,im,rs("unit"),lm,dm,um]),fm=e=>e.fold(A.some,A.none,A.some,A.some),bm=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},vm=(e,t)=>o=>{const n=Qn("Converting part type",t,o);return e(n)},ym=vm(sm.required,mm),xm=vm(sm.external,gm),wm=vm(sm.optional,pm),Sm=vm(sm.group,hm),Cm=x("entirety");var km=Object.freeze({__proto__:null,required:ym,external:xm,optional:wm,group:Sm,asNamedPart:fm,name:bm,asCommon:e=>e.fold(w,w,w,w),original:Cm});const Om=(e,t,o,n)=>yn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),_m=(e,t)=>{const o={};return V(t,(t=>{fm(t).each((t=>{const n=Tm(e,t.pname);o[t.name]=o=>{const s=Qn("Part: "+t.name+" in "+e,In(t.schema),o);return{...n,config:o,validated:s}}}))})),o},Tm=(e,t)=>({uiType:nm(),owner:e,name:t}),Em=(e,t,o)=>({uiType:nm(),owner:e,name:t,config:o,validated:{}}),Am=e=>q(e,(e=>e.fold(A.none,A.some,A.none,A.none).map((e=>us(e.name,e.schema.concat([Ci(Cm())])))).toArray())),Mm=e=>L(e,bm),Dm=(e,t,o)=>((e,t,o)=>{const n={},s={};return V(o,(e=>{e.fold((e=>{n[e.pname]=tm(!0,((t,o,n)=>e.factory.sketch(Om(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=x(e.factory.sketch(Om(t,e,o[Cm()]),o))}),(e=>{n[e.pname]=tm(!1,((t,o,n)=>e.factory.sketch(Om(t,e,o,n))))}),(e=>{n[e.pname]=om(!0,((t,o,n)=>{const s=t[e.name];return L(s,(o=>e.factory.sketch(yn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:x(n),externals:x(s)}})(0,t,o),Bm=(e,t,o)=>((e,t,o,n)=>{const s=le(n,((e,t)=>((e,t)=>{let o=!1;return{name:x(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>q(o,(o=>em(e,t,o,n))))(e,t,o,s);return ie(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(A.some(e),t,t.components,o),Fm=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},Im=(e,t,o)=>Fm(e,t,o).getOrDie("Could not find part: "+o),Rm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return V(o,(e=>{n[e]=x(r.getByUid(s[e]))})),n},Nm=(e,t)=>{const o=e.getSystem();return le(t.partUids,((e,t)=>x(o.getByUid(e))))},zm=e=>re(e.partUids),Lm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return V(o,(e=>{n[e]=x(r.getByUid(s[e]).getOrDie())})),n},Vm=(e,t)=>{const o=Mm(t);return Fs(L(o,(t=>({key:t,value:e+"-"+t}))))},Hm=e=>os("partUids","partUids",Cn((t=>Vm(t.uid,e))),Vn());var Pm=Object.freeze({__proto__:null,generate:_m,generateOne:Em,schemas:Am,names:Mm,substitutes:Dm,components:Bm,defaultUids:Vm,defaultUidsSchema:Hm,getAllParts:Nm,getAllPartNames:zm,getPart:Fm,getPartOrDie:Im,getParts:Rm,getPartsOrDie:Lm});const Um=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[us("parts",e)]:[]).concat([rs("uid"),Ss("dom",{}),Ss("components",[]),Ci("originalSpec"),Ss("debug.sketcher",{})]).concat(t))(n,s);return Qn(e+" [SpecSchema]",Fn(r.concat(t)),o)},Wm=(e,t,o,n,s)=>{const r=jm(s),a=Am(o),i=Hm(o),l=Um(e,t,r,a,[i]),c=Dm(0,l,o);return n(l,Bm(e,l,c.internals()),r,c.externals())},jm=e=>(e=>be(e,"uid"))(e)?e:{...e,uid:Vi("uid")},$m=Fn([rs("name"),rs("factory"),rs("configFields"),Ss("apis",{}),Ss("extraApis",{})]),Gm=Fn([rs("name"),rs("factory"),rs("configFields"),rs("partFields"),Ss("apis",{}),Ss("extraApis",{})]),qm=e=>{const t=Qn("Sketcher for "+e.name,$m,e),o=le(t.apis,Gi),n=le(t.extraApis,((e,t)=>na(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=jm(n);return o(Um(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},Ym=e=>{const t=Qn("Sketcher for "+e.name,Gm,e),o=_m(t.name,t.partFields),n=le(t.apis,Gi),s=le(t.extraApis,((e,t)=>na(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>Wm(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},Xm=e=>Xe("input")(e)&&"radio"!==kt(e,"type")||Xe("textarea")(e);var Km=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const Jm=[rs("find")],Qm=ma({fields:Jm,name:"composing",apis:Km}),Zm=["input","button","textarea","select"],eg=(e,t,o)=>{(t.disabled()?ag:ig)(e,t)},tg=(e,t)=>!0===t.useNative&&I(Zm,Ue(e.element)),og=e=>{St(e.element,"disabled","disabled")},ng=e=>{Tt(e.element,"disabled")},sg=e=>{St(e.element,"aria-disabled","true")},rg=e=>{St(e.element,"aria-disabled","false")},ag=(e,t,o)=>{t.disableClass.each((t=>{wa(e.element,t)})),(tg(e,t)?og:sg)(e),t.onDisabled(e)},ig=(e,t,o)=>{t.disableClass.each((t=>{Ca(e.element,t)})),(tg(e,t)?ng:rg)(e),t.onEnabled(e)},lg=(e,t)=>tg(e,t)?(e=>_t(e.element,"disabled"))(e):(e=>"true"===kt(e.element,"aria-disabled"))(e);var cg=Object.freeze({__proto__:null,enable:ig,disable:ag,isDisabled:lg,onLoad:eg,set:(e,t,o,n)=>{(n?ag:ig)(e,t)}}),dg=Object.freeze({__proto__:null,exhibit:(e,t)=>sa({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Pr([Ur(gr(),((t,o)=>lg(t,e))),ra(e,t,eg)])}),ug=[Es("disabled",T),Ss("useNative",!0),ps("disableClass"),vi("onDisabled"),vi("onEnabled")];const mg=ma({fields:ug,name:"disabling",active:dg,apis:cg}),gg=(e,t,o,n)=>{const s=Od(e.element,"."+t.highlightClass);V(s,(o=>{R(n,(e=>Ze(e.element,o)))||(Ca(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),Rr(o,Ir())})))}))},pg=(e,t,o,n)=>{gg(e,t,0,[n]),hg(e,t,o,n)||(wa(n.element,t.highlightClass),t.onHighlight(e,n),Rr(n,Fr()))},hg=(e,t,o,n)=>ka(n.element,t.highlightClass),fg=(e,t,o)=>_l(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),bg=(e,t,o)=>{const n=Od(e.element,"."+t.itemClass);return(n.length>0?A.some(n[n.length-1]):A.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},vg=(e,t,o,n)=>{const s=Od(e.element,"."+t.itemClass);return $(s,(e=>ka(e,t.highlightClass))).bind((t=>{const o=Ai(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},yg=(e,t,o)=>{const n=Od(e.element,"."+t.itemClass);return xe(L(n,(t=>e.getSystem().getByDom(t).toOptional())))};var xg=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>gg(e,t,0,[]),dehighlight:(e,t,o,n)=>{hg(e,t,o,n)&&(Ca(n.element,t.highlightClass),t.onDehighlight(e,n),Rr(n,Ir()))},highlight:pg,highlightFirst:(e,t,o)=>{fg(e,t).each((n=>{pg(e,t,o,n)}))},highlightLast:(e,t,o)=>{bg(e,t).each((n=>{pg(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=Od(e.element,"."+t.itemClass);return A.from(s[n]).fold((()=>ln.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{pg(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=yg(e,t);j(s,n).each((n=>{pg(e,t,o,n)}))},isHighlighted:hg,getHighlighted:(e,t,o)=>_l(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:fg,getLast:bg,getPrevious:(e,t,o)=>vg(e,t,0,-1),getNext:(e,t,o)=>vg(e,t,0,1),getCandidates:yg}),wg=[rs("highlightClass"),rs("itemClass"),vi("onHighlight"),vi("onDehighlight")];const Sg=ma({fields:wg,name:"highlighting",apis:xg}),Cg=[8],kg=[9],Og=[13],_g=[27],Tg=[32],Eg=[37],Ag=[38],Mg=[39],Dg=[40],Bg=(e,t,o)=>{const n=X(e.slice(0,t)),s=X(e.slice(t+1));return j(n.concat(s),o)},Fg=(e,t,o)=>{const n=X(e.slice(0,t));return j(n,o)},Ig=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return j(s.concat(n),o)},Rg=(e,t,o)=>{const n=e.slice(t+1);return j(n,o)},Ng=e=>t=>{const o=t.raw;return I(e,o.which)},zg=e=>t=>Y(e,(e=>e(t))),Lg=e=>!0===e.raw.shiftKey,Vg=e=>!0===e.raw.ctrlKey,Hg=k(Lg),Pg=(e,t)=>({matches:e,classification:t}),Ug=(e,t,o)=>{t.exists((e=>o.exists((t=>Ze(t,e)))))||Nr(e,Er(),{prevFocus:t,newFocus:o})},Wg=()=>{const e=e=>bc(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);Ug(t,n,s)}}},jg=()=>{const e=e=>Sg.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Sg.highlight(t,e)}));const s=e(t);Ug(t,n,s)}}};var $g;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}($g||($g={}));const Gg=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,j(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([Ss("focusManager",Wg()),Cs("focusInside","onFocus",Yn((e=>I(["onFocus","onEnterOrSpace","onApi"],e)?ln.value(e):ln.error("Invalid value for focusInside")))),Si("handler",a),Si("state",t),Si("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==$g.OnFocusMode?A.none():s(e).map((o=>jr(cr(),((n,s)=>{o(n,e,t),s.stop()})))),i=[jr(Qs(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=Ng(Tg.concat(Og))(n.event);e.focusInside===$g.OnEnterOrSpaceMode&&r&&zs(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),jr(Zs(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Pr(a.toArray().concat(i))}};return a},qg=e=>{const t=[ps("onEscape"),ps("onEnter"),Ss("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),Ss("firstTabstop",0),Ss("useTabstopAt",E),ps("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>Tl(t,e))).getOr(t);return Ut(o)>0},n=(e,t)=>t.focusManager.get(e).bind((e=>Tl(e,t.selector))),s=(e,t,n)=>{((e,t)=>{const n=Od(e.element,t.selector),s=P(n,(e=>o(t,e)));return A.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},r=(e,t,s,r)=>{const a=Od(e.element,s.selector);return n(e,s).bind((t=>$(a,C(Ze,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?A.some(!0):A.none()),(t=>(s.focusManager.set(e,t),A.some(!0)))))(e,a,t,s,r)))))},a=(e,t,o)=>{const n=o.cyclic?Bg:Fg;return r(e,0,o,n)},i=(e,t,o)=>{const n=o.cyclic?Ig:Rg;return r(e,0,o,n)},l=x([Pg(zg([Lg,Ng(kg)]),a),Pg(Ng(kg),i),Pg(zg([Hg,Ng(Og)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),c=x([Pg(Ng(_g),((e,t,o)=>o.onEscape.bind((o=>o(e,t))))),Pg(Ng(kg),((e,t,o)=>n(e,o).filter((e=>!o.useTabstopAt(e))).bind((n=>((e=>(e=>rt(e))(e).bind(dt).exists((t=>Ze(t,e))))(n)?a:i)(e,t,o)))))]);return Gg(t,la.init,l,c,(()=>A.some(s)))};var Yg=qg(ns("cyclic",T)),Xg=qg(ns("cyclic",E));const Kg=(e,t,o)=>Xm(o)&&Ng(Tg)(t.event)?A.none():((e,t,o)=>(Lr(e,o,gr()),A.some(!0)))(e,0,o),Jg=(e,t)=>A.some(!0),Qg=[Ss("execute",Kg),Ss("useSpace",!1),Ss("useEnter",!0),Ss("useControlEnter",!1),Ss("useDown",!1)],Zg=(e,t,o)=>o.execute(e,t,e.element);var ep=Gg(Qg,la.init,((e,t,o,n)=>{const s=o.useSpace&&!Xm(e.element)?Tg:[],r=o.useEnter?Og:[],a=o.useDown?Dg:[],i=s.concat(r).concat(a);return[Pg(Ng(i),Zg)].concat(o.useControlEnter?[Pg(zg([Vg,Ng(Og)]),Zg)]:[])}),((e,t,o,n)=>o.useSpace&&!Xm(e.element)?[Pg(Ng(Tg),Jg)]:[]),(()=>A.none()));const tp=()=>{const e=nn();return ca({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var op=Object.freeze({__proto__:null,flatgrid:tp,init:e=>e.state(e)});const np=e=>(t,o,n,s)=>{const r=e(t.element);return ip(r,t,o,n,s)},sp=(e,t)=>{const o=qc(e,t);return np(o)},rp=(e,t)=>{const o=qc(t,e);return np(o)},ap=e=>(t,o,n,s)=>ip(e,t,o,n,s),ip=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),lp=ap,cp=ap,dp=ap,up=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),mp=(e,t,o)=>{const n=Od(e,o);return((e,o)=>$(e,(e=>Ze(e,t))).map((t=>({index:t,candidates:e}))))(P(n,up))},gp=(e,t)=>$(e,(e=>Ze(t,e))),pp=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?A.some(e[n]):A.none()})),hp=(e,t,o,n,s)=>pp(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Ai(r,s,0,a-1);return A.some({row:t,column:i})})),fp=(e,t,o,n,s)=>pp(e,t,n,((t,r)=>{const a=Ai(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Mi(r,0,i-1);return A.some({row:a,column:l})})),bp=[rs("selector"),Ss("execute",Kg),yi("onEscape"),Ss("captureTab",!1),ki()],vp=(e,t,o)=>{_l(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},yp=e=>(t,o,n,s)=>mp(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),xp=(e,t,o)=>o.captureTab?A.some(!0):A.none(),wp=yp(((e,t,o,n)=>hp(e,t,o,n,-1))),Sp=yp(((e,t,o,n)=>hp(e,t,o,n,1))),Cp=yp(((e,t,o,n)=>fp(e,t,o,n,-1))),kp=yp(((e,t,o,n)=>fp(e,t,o,n,1))),Op=x([Pg(Ng(Eg),sp(wp,Sp)),Pg(Ng(Mg),rp(wp,Sp)),Pg(Ng(Ag),lp(Cp)),Pg(Ng(Dg),cp(kp)),Pg(zg([Lg,Ng(kg)]),xp),Pg(zg([Hg,Ng(kg)]),xp),Pg(Ng(Tg.concat(Og)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>Tl(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),_p=x([Pg(Ng(_g),((e,t,o)=>o.onEscape(e,t))),Pg(Ng(Tg),Jg)]);var Tp=Gg(bp,tp,Op,_p,(()=>A.some(vp)));const Ep=(e,t,o,n,s)=>{const r=(e,t,o)=>s(e,t,n,0,o.length-1,o[t],(t=>{return n=o[t],"button"===Ue(n)&&"disabled"===kt(n,"disabled")?r(e,t,o):A.from(o[t]);var n}));return mp(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return r(t,t,o)}))},Ap=(e,t,o,n)=>Ep(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Mi(t+o,n,s);return i===e?A.from(r):a(i)})),Mp=(e,t,o,n)=>Ep(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Ai(t,o,n,s);return i===e?A.none():a(i)})),Dp=[rs("selector"),Ss("getInitial",A.none),Ss("execute",Kg),yi("onEscape"),Ss("executeOnMove",!1),Ss("allowVertical",!0),Ss("allowHorizontal",!0),Ss("cycles",!0)],Bp=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>Tl(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),Fp=(e,t,o)=>{t.getInitial(e).orThunk((()=>_l(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},Ip=(e,t,o)=>(o.cycles?Mp:Ap)(e,o.selector,t,-1),Rp=(e,t,o)=>(o.cycles?Mp:Ap)(e,o.selector,t,1),Np=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?Bp(t,o,n):A.some(!0))),zp=x([Pg(Ng(Tg),Jg),Pg(Ng(_g),((e,t,o)=>o.onEscape(e,t)))]);var Lp=Gg(Dp,la.init,((e,t,o,n)=>{const s=[...o.allowHorizontal?Eg:[]].concat(o.allowVertical?Ag:[]),r=[...o.allowHorizontal?Mg:[]].concat(o.allowVertical?Dg:[]);return[Pg(Ng(s),Np(sp(Ip,Rp))),Pg(Ng(r),Np(rp(Ip,Rp))),Pg(Ng(Og),Bp),Pg(Ng(Tg),Bp)]}),zp,(()=>A.some(Fp)));const Vp=(e,t,o)=>A.from(e[t]).bind((e=>A.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),Hp=(e,t,o,n)=>{const s=e[t].length,r=Ai(o,n,0,s-1);return Vp(e,t,r)},Pp=(e,t,o,n)=>{const s=Ai(o,n,0,e.length-1),r=e[s].length,a=Mi(t,0,r-1);return Vp(e,s,a)},Up=(e,t,o,n)=>{const s=e[t].length,r=Mi(o+n,0,s-1);return Vp(e,t,r)},Wp=(e,t,o,n)=>{const s=Mi(o+n,0,e.length-1),r=e[s].length,a=Mi(t,0,r-1);return Vp(e,s,a)},jp=[us("selectors",[rs("row"),rs("cell")]),Ss("cycles",!0),Ss("previousSelector",A.none),Ss("execute",Kg)],$p=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return _l(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},Gp=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return Tl(n,s.selectors.row).bind((e=>{const t=Od(e,s.selectors.cell);return gp(t,n).bind((t=>{const n=Od(o,s.selectors.row);return gp(n,e).bind((e=>{const o=((e,t)=>L(e,(e=>Od(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},qp=Gp(((e,t,o)=>Hp(e,t,o,-1)),((e,t,o)=>Up(e,t,o,-1))),Yp=Gp(((e,t,o)=>Hp(e,t,o,1)),((e,t,o)=>Up(e,t,o,1))),Xp=Gp(((e,t,o)=>Pp(e,o,t,-1)),((e,t,o)=>Wp(e,o,t,-1))),Kp=Gp(((e,t,o)=>Pp(e,o,t,1)),((e,t,o)=>Wp(e,o,t,1))),Jp=x([Pg(Ng(Eg),sp(qp,Yp)),Pg(Ng(Mg),rp(qp,Yp)),Pg(Ng(Ag),lp(Xp)),Pg(Ng(Dg),cp(Kp)),Pg(Ng(Tg.concat(Og)),((e,t,o)=>bc(e.element).bind((n=>o.execute(e,t,n)))))]),Qp=x([Pg(Ng(Tg),Jg)]);var Zp=Gg(jp,la.init,Jp,Qp,(()=>A.some($p)));const eh=[rs("selector"),Ss("execute",Kg),Ss("moveOnTab",!1)],th=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),oh=(e,t,o)=>{_l(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},nh=(e,t,o)=>Mp(e,o.selector,t,-1),sh=(e,t,o)=>Mp(e,o.selector,t,1),rh=x([Pg(Ng(Ag),dp(nh)),Pg(Ng(Dg),dp(sh)),Pg(zg([Lg,Ng(kg)]),((e,t,o,n)=>o.moveOnTab?dp(nh)(e,t,o,n):A.none())),Pg(zg([Hg,Ng(kg)]),((e,t,o,n)=>o.moveOnTab?dp(sh)(e,t,o,n):A.none())),Pg(Ng(Og),th),Pg(Ng(Tg),th)]),ah=x([Pg(Ng(Tg),Jg)]);var ih=Gg(eh,la.init,rh,ah,(()=>A.some(oh)));const lh=[yi("onSpace"),yi("onEnter"),yi("onShiftEnter"),yi("onLeft"),yi("onRight"),yi("onTab"),yi("onShiftTab"),yi("onUp"),yi("onDown"),yi("onEscape"),Ss("stopSpaceKeyup",!1),ps("focusIn")];var ch=Gg(lh,la.init,((e,t,o)=>[Pg(Ng(Tg),o.onSpace),Pg(zg([Hg,Ng(Og)]),o.onEnter),Pg(zg([Lg,Ng(Og)]),o.onShiftEnter),Pg(zg([Lg,Ng(kg)]),o.onShiftTab),Pg(zg([Hg,Ng(kg)]),o.onTab),Pg(Ng(Ag),o.onUp),Pg(Ng(Dg),o.onDown),Pg(Ng(Eg),o.onLeft),Pg(Ng(Mg),o.onRight),Pg(Ng(Tg),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[Pg(Ng(Tg),Jg)]:[],Pg(Ng(_g),o.onEscape)]),(e=>e.focusIn));const dh=Yg.schema(),uh=Xg.schema(),mh=Lp.schema(),gh=Tp.schema(),ph=Zp.schema(),hh=ep.schema(),fh=ih.schema(),bh=ch.schema(),vh=pa({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:dh,cyclic:uh,flow:mh,flatgrid:gh,matrix:ph,execution:hh,menu:fh,special:bh}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ve(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:op}),yh=(e,t)=>{vc((()=>{((e,t,o)=>{const n=e.components();(e=>{V(e.components(),(e=>Ho(e.element))),Vo(e.element),e.syncComponents()})(e);const s=o(t),r=K(n,s);V(r,(t=>{tu(t),e.getSystem().removeFromWorld(t)})),V(s,(t=>{eu(t)?ru(e,t):(e.getSystem().addToWorld(t),ru(e,t),vt(e.element)&&ou(t))})),e.syncComponents()})(e,t,(()=>L(t,e.getSystem().build)))}),e.element)},xh=(e,t)=>{vc((()=>{((o,n,s)=>{const r=o.components(),a=q(n,(e=>$i(e).toArray()));V(r,(e=>{I(a,e)||su(e)}));const i=((e,t,o)=>rl(e,t,((t,n)=>al(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=K(r,i);V(l,(e=>{eu(e)&&su(e)})),V(i,(e=>{eu(e)||nu(o,e)})),o.syncComponents()})(e,t)}),e.element)},wh=(e,t,o,n)=>{su(t);const s=al(e.element,o,n,e.getSystem().buildOrPatch);nu(e,s),e.syncComponents()},Sh=(e,t,o)=>{const n=e.getSystem().build(o);iu(e,n,t)},Ch=(e,t,o,n)=>{cu(t),Sh(e,((e,t)=>((e,t,o)=>{ct(e,o).fold((()=>{zo(e,t)}),(e=>{Io(e,t)}))})(e,t,o)),n)},kh=(e,t)=>e.components(),Oh=(e,t,o,n,s)=>{const r=kh(e);return A.from(r[n]).map((o=>(s.fold((()=>cu(o)),(s=>{(t.reuseDom?wh:Ch)(e,o,n,s)})),o)))};var _h=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Sh(e,zo,n)},prepend:(e,t,o,n)=>{Sh(e,No,n)},remove:(e,t,o,n)=>{const s=kh(e),r=j(s,(e=>Ze(n.element,e.element)));r.each(cu)},replaceAt:Oh,replaceBy:(e,t,o,n,s)=>{const r=kh(e);return $(r,n).bind((o=>Oh(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?xh:yh)(e,n),contents:kh});const Th=ma({fields:[Ts("reuseDom",!0)],name:"replacing",apis:_h}),Eh=(e,t)=>{const o=((e,t)=>{const o=Pr(t);return ma({fields:[rs("enabled")],name:e,active:{events:x(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:x({}),initialConfig:{},state:la}}},Ah=(e,t)=>{t.ignore||(gc(e.element),t.onFocus(e))};var Mh=Object.freeze({__proto__:null,focus:Ah,blur:(e,t)=>{t.ignore||pc(e.element)},isFocused:e=>hc(e.element)}),Dh=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return sa(o)},events:e=>Pr([jr(cr(),((t,o)=>{Ah(t,e),o.stop()}))].concat(e.stopMousedown?[jr($s(),((e,t)=>{t.event.prevent()}))]:[]))}),Bh=[vi("onFocus"),Ss("stopMousedown",!1),Ss("ignore",!1)];const Fh=ma({fields:Bh,name:"focusing",active:Dh,apis:Mh}),Ih=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?wa(e.element,t):Ca(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},Rh=(e,t,o)=>{Ih(e,t,o,!o.get())},Nh=(e,t,o)=>{Ih(e,t,o,t.selected)};var zh=Object.freeze({__proto__:null,onLoad:Nh,toggle:Rh,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{Ih(e,t,o,!0)},off:(e,t,o)=>{Ih(e,t,o,!1)},set:Ih}),Lh=Object.freeze({__proto__:null,exhibit:()=>sa({}),events:(e,t)=>{const o=(n=e,s=t,r=Rh,ta((e=>{r(e,n,s)})));var n,s,r;const a=ra(e,t,Nh);return Pr(G([e.toggleOnExecute?[o]:[],[a]]))}});const Vh=(e,t,o)=>{St(e.element,"aria-expanded",o)};var Hh=[Ss("selected",!1),ps("toggleClass"),Ss("toggleOnExecute",!0),vi("onToggled"),Cs("aria",{mode:"none"},es("mode",{pressed:[Ss("syncWithExpanded",!1),Si("update",((e,t,o)=>{St(e.element,"aria-pressed",o),t.syncWithExpanded&&Vh(e,0,o)}))],checked:[Si("update",((e,t,o)=>{St(e.element,"aria-checked",o)}))],expanded:[Si("update",Vh)],selected:[Si("update",((e,t,o)=>{St(e.element,"aria-selected",o)}))],none:[Si("update",b)]}))];const Ph=ma({fields:Hh,name:"toggling",active:Lh,apis:zh,state:(!1,{init:()=>{const e=en(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const Uh=()=>{const e=(e,t)=>{t.stop(),zr(e)};return[jr(or(),e),jr(hr(),e),Xr(Ps()),Xr($s())]},Wh=e=>Pr(G([e.map((e=>ta(((t,o)=>{e(t),o.stop()})))).toArray(),Uh()])),jh="alloy.item-hover",$h="alloy.item-focus",Gh="alloy.item-toggled",qh=e=>{(bc(e.element).isNone()||Fh.isFocused(e))&&(Fh.isFocused(e)||Fh.focus(e),Nr(e,jh,{item:e}))},Yh=e=>{Nr(e,$h,{item:e})},Xh=x(jh),Kh=x($h),Jh=x(Gh),Qh=e=>e.role.fold((()=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem")),w),Zh=[rs("data"),rs("components"),rs("dom"),Ss("hasSubmenu",!1),ps("toggling"),ps("role"),Xu("itemBehaviours",[Ph,Fh,vh,$u]),Ss("ignoreFocus",!1),Ss("domModification",{}),Si("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:Qh(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:Ku(e.itemBehaviours,[e.toggling.fold(Ph.revoke,(t=>Ph.config(((e,t)=>({aria:{mode:t?"selected":"checked"},...me(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Nr(e,Gh,{item:e,state:t})})(t,o)}}))(t,e.role.exists((e=>"option"===e)))))),Fh.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{Yh(e)}}),vh.config({mode:"execution"}),$u.config({store:{mode:"memory",initialValue:e.data}}),Eh("item-type-events",[...Uh(),jr(Xs(),qh),jr(pr(),Fh.focus)])]),components:e.components,eventOrder:e.eventOrder}))),Ss("eventOrder",{})],ef=[rs("dom"),rs("components"),Si("builder",(e=>({dom:e.dom,components:e.components,events:Pr([Kr(pr())])})))],tf=x("item-widget"),of=x([ym({name:"widget",overrides:e=>({behaviours:da([$u.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),nf=[rs("uid"),rs("data"),rs("components"),rs("dom"),Ss("autofocus",!1),Ss("ignoreFocus",!1),Xu("widgetBehaviours",[$u,Fh,vh]),Ss("domModification",{}),Hm(of()),Si("builder",(e=>{const t=Dm(tf(),e,of()),o=Bm(tf(),e,t.internals()),n=t=>Fm(t,e,"widget").map((e=>(vh.focusIn(e),e))),s=(t,o)=>Xm(o.event.target)?A.none():e.autofocus?(o.setSource(t.element),A.none()):A.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Pr([ta(((e,t)=>{n(e).each((e=>{t.stop()}))})),jr(Xs(),qh),jr(pr(),((t,o)=>{e.autofocus?n(t):Fh.focus(t)}))]),behaviours:Ku(e.widgetBehaviours,[$u.config({store:{mode:"memory",initialValue:e.data}}),Fh.config({ignore:e.ignoreFocus,onFocus:e=>{Yh(e)}}),vh.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:ha(),onLeft:s,onRight:s,onEscape:(t,o)=>Fh.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),A.none()):A.none():(Fh.focus(t),A.some(!0))})])}}))],sf=es("type",{widget:nf,item:Zh,separator:ef}),rf=x([Sm({factory:{sketch:e=>{const t=Qn("menu.spec item",sf,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>be(t,"uid")?t:{...t,uid:Vi("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),af=x([bs("role"),rs("value"),rs("items"),rs("dom"),rs("components"),Ss("eventOrder",{}),Gu("menuBehaviours",[Sg,$u,Qm,vh]),Cs("movement",{mode:"menu",moveOnTab:!0},es("mode",{grid:[ki(),Si("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[Si("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},previousSelector:t.previousSelector,focusManager:e.focusManager}))),rs("rowSelector"),Ss("previousSelector",A.none)],menu:[Ss("moveOnTab",!0),Si("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),as("markers",gi()),Ss("fakeFocus",!1),Ss("focusManager",Wg()),vi("onHighlight"),vi("onDehighlight"),Ss("showMenuRole",!0)]),lf=x("alloy.menu-focus"),cf=Ym({name:"Menu",configFields:af(),partFields:rf(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Yu(e.menuBehaviours,[Sg.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),$u.config({store:{mode:"memory",initialValue:e.value}}),Qm.config({find:A.some}),vh.config(e.movement.config(e,e.movement))]),events:Pr([jr(Kh(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Sg.highlight(e,o),t.stop(),Nr(e,lf(),{menu:e,item:o})}))})),jr(Xh(),((e,t)=>{const o=t.event.item;Sg.highlight(e,o)})),jr(Jh(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===kt(o.element,"role")&&((e,t)=>{const o=Od(e.element,'[role="menuitemradio"][aria-checked="true"]');V(o,(o=>{Ze(o,t.element)||e.getSystem().getByDom(o).each((e=>{Ph.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,...e.showMenuRole?{domModification:{attributes:{role:e.role.getOr("menu")}}}:{}})}),df=(e,t,o,n)=>fe(o,n).bind((n=>fe(e,n).bind((n=>{const s=df(e,t,o,n);return A.some([n].concat(s))})))).getOr([]),uf=e=>"prepared"===e.type?A.some(e.menu):A.none(),mf=()=>{const e=en({}),t=en({}),o=en({}),n=nn(),s=en({}),r=e=>a(e).bind(uf),a=e=>fe(t.get(),e),i=t=>fe(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};ie(e,((e,t)=>{V(e,(e=>{o[e]=t}))}));const n=t,s=ce(t,((e,t)=>({k:e,v:t}))),r=le(s,((e,t)=>[t].concat(df(o,n,s,t))));return le(o,(e=>fe(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>fe(e.get(),t).map((e=>{const n=fe(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>fe(o.get(),e),collapse:e=>fe(o.get(),e).bind((e=>e.length>1?A.some(e.slice(1)):A.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return K(re(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=P(i(t).toArray(),(e=>r(e).isSome()));return fe(o.get(),t).bind((t=>{const o=X(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return A.none();t.push(n.getOrDie())}return A.some(t)})(q(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>pe(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>ye(n.get(),t)?[]:[A.none()]),(e=>[A.some(e)])))))}))}}},gf=uf,pf=Bi("tiered-menu-item-highlight"),hf=Bi("tiered-menu-item-dehighlight");var ff;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(ff||(ff={}));const bf=x("collapse-item"),vf=qm({name:"TieredMenu",configFields:[wi("onExecute"),wi("onEscape"),xi("onOpenMenu"),xi("onOpenSubmenu"),vi("onRepositionMenu"),vi("onCollapseMenu"),Ss("highlightOnOpen",ff.HighlightMenuAndItem),us("data",[rs("primary"),rs("menus"),rs("expansions")]),Ss("fakeFocus",!1),vi("onHighlightItem"),vi("onDehighlightItem"),vi("onHover"),hi(),rs("dom"),Ss("navigateOnHover",!0),Ss("stayInDom",!1),Gu("tmenuBehaviours",[vh,Sg,Qm,Th]),Ss("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=nn(),n=mf(),s=e=>$u.getValue(e).value,r=t=>le(e.data.menus,((e,t)=>q(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Sg.highlight,i=(t,o)=>{a(t,o),Sg.getHighlighted(o).orThunk((()=>Sg.getFirst(o))).each((n=>{e.fakeFocus?Sg.highlight(o,n):Lr(t,n.element,pr())}))},l=(e,t)=>xe(L(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?A.some(e.menu):A.none()))))),c=(t,o,n)=>{const s=l(o,o.otherMenus(n));V(s,(o=>{_a(o.element,[e.markers.backgroundMenu]),e.stayInDom||Th.remove(t,o)}))},d=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=Od(t.element,`.${e.markers.item}`),a=P(r,(e=>"true"===kt(e,"aria-haspopup")));return V(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);ie(r,((e,t)=>{const o=I(n,t);St(e.element,"aria-expanded",o)}))},u=(t,o,n)=>A.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return A.none();{const r=s.menu,a=l(o,n.slice(1));return V(a,(t=>{wa(t.element,e.markers.backgroundMenu)})),vt(r.element)||Th.append(t,fl(r)),_a(r.element,[e.markers.backgroundMenu]),i(t,r),c(t,o,n),A.some(r)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,r=m.HighlightSubmenu)=>{if(o.hasConfigured(mg)&&mg.isDisabled(o))return A.some(o);{const a=s(o);return n.expand(a).bind((s=>(d(t,s),A.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return vt(l.element)||Th.append(t,fl(l)),e.onOpenSubmenu(t,o,l,X(s)),r===m.HighlightSubmenu?(Sg.highlightFirst(l),u(t,n,s)):(Sg.dehighlightAll(l),A.some(o))})))))))}},p=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(d(t,s),u(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>Tl(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(E))))),f=Pr([jr(lf(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Sg.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>c(e,n,t)))}))})),ta(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Qr(((t,o)=>{(t=>{const o=((t,o,n)=>le(n,((n,s)=>{const r=()=>cf.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Nr(e,pf,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Nr(e,hf,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?jg():Wg()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{Th.append(t,fl(o)),e.onOpenMenu(t,o),e.highlightOnOpen===ff.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===ff.HighlightJustMenu&&a(t,o)}))})),jr(pf,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),jr(hf,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[jr(Xh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,r),g(t,r,m.HighlightParent),e.onHover(t,r)}))]:[]]),v=e=>Sg.getHighlighted(e).bind(Sg.getHighlighted),y={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=s(e),o=he(n.getMenus()),r=xe(L(o,gf));return n.getTriggeringPath(t,(e=>((e,t,o)=>se(t,(e=>{if(!e.getSystem().isConnected())return A.none();const t=Sg.getCandidates(e);return j(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>A.from(e.components()[0]).filter((e=>"menu"===kt(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:Yu(e.tmenuBehaviours,[vh.config({mode:"special",onRight:h(((e,t)=>Xm(t.element)?A.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>Xm(t.element)?A.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{Lr(e,t.element,pr())}))}}),Sg.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),Qm.config({find:e=>Sg.getHighlighted(e)}),Th.config({})]),eventOrder:e.eventOrder,apis:y,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:Bs(e,t),expansions:{}}),collapseItem:e=>({value:Bi(bf()),meta:{text:e}})}}),yf=qm({name:"InlineView",configFields:[rs("lazySink"),vi("onShow"),vi("onHide"),ys("onEscape"),Gu("inlineBehaviours",[Ou,$u,uc]),ws("fireDismissalEventInstead",[Ss("event",_r())]),ws("fireRepositionEventInstead",[Ss("event",Tr())]),Ss("getRelated",A.none),Ss("isExtraPart",T),Ss("eventOrder",A.none)],factory:(e,t)=>{const o=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();Ou.openWhileCloaked(t,o,(()=>Zd.positionWithinBounds(r,t,n,s()))),$u.setValue(t,A.some({mode:"position",config:n,getBounds:s}))},n=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>ic(),onRtl:()=>lc()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return vf.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(Ou.close(t),e.onEscape.map((e=>e(t))),A.some(!0)),onExecute:()=>A.some(!0),onOpenMenu:(e,t)=>{Zd.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();Zd.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();Zd.positionWithinBounds(a,t,o,s()),V(n,(e=>{const t=i(e.triggeringPath);Zd.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);Ou.open(t,r),$u.setValue(t,A.some({mode:"menu",menu:r}))},s=t=>{Ou.isOpen(t)&&$u.getValue(t).each((o=>{switch(o.mode){case"menu":Ou.getState(t).each(vf.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();Zd.positionWithinBounds(n,t,o.config,o.getBounds())}}))},r={setContent:(e,t)=>{Ou.setContent(e,t)},showAt:(e,t,n)=>{const s=A.none;o(e,t,n,s)},showWithinBounds:o,showMenuAt:(e,t,o)=>{n(e,t,o,A.none)},showMenuWithinBounds:n,hide:e=>{Ou.isOpen(e)&&($u.setValue(e,A.none()),Ou.close(e))},getContent:e=>Ou.getState(e),reposition:s,isOpen:Ou.isOpen};return{uid:e.uid,dom:e.dom,behaviours:Yu(e.inlineBehaviours,[Ou.config({isPartOf:(t,o,n)=>Ml(o,n)||((t,o)=>e.getRelated(t).exists((e=>Ml(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),$u.config({store:{mode:"memory",initialValue:A.none()}}),uc.config({channels:{...Mu({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Bu({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:s})}})]),eventOrder:e.eventOrder,apis:r}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var xf,wf,Sf=tinymce.util.Tools.resolve("tinymce.util.Delay"),Cf=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),kf=tinymce.util.Tools.resolve("tinymce.EditorManager"),Of=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(xf||(xf={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(wf||(wf={}));const _f=e=>t=>t.options.get(e),Tf=e=>t=>A.from(e(t)),Ef=e=>{const t=Of.deviceType.isPhone(),o=Of.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:Cf.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("font_size_input_default_unit",{processor:"string",default:"pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),N(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:wf.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("ui_mode",{processor:"string",default:"combined"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!Of.deviceType.isTouch()}),n("sidebar_show",{processor:"string"}),n("help_accessibility",{processor:"boolean",default:e.hasPlugin("help")}),n("default_font_stack",{processor:"string[]",default:[]})},Af=_f("readonly"),Mf=_f("height"),Df=_f("width"),Bf=Tf(_f("min_width")),Ff=Tf(_f("min_height")),If=Tf(_f("max_width")),Rf=Tf(_f("max_height")),Nf=Tf(_f("style_formats")),zf=_f("style_formats_merge"),Lf=_f("style_formats_autohide"),Vf=_f("content_langs"),Hf=_f("removed_menuitems"),Pf=_f("toolbar_mode"),Uf=_f("toolbar_groups"),Wf=_f("toolbar_location"),jf=_f("fixed_toolbar_container"),$f=_f("fixed_toolbar_container_target"),Gf=_f("toolbar_persist"),qf=_f("toolbar_sticky_offset"),Yf=_f("menubar"),Xf=_f("toolbar"),Kf=_f("file_picker_callback"),Jf=_f("file_picker_validator_handler"),Qf=_f("font_size_input_default_unit"),Zf=_f("file_picker_types"),eb=_f("typeahead_urls"),tb=_f("anchor_top"),ob=_f("anchor_bottom"),nb=_f("draggable_modal"),sb=_f("statusbar"),rb=_f("elementpath"),ab=_f("branding"),ib=_f("resize"),lb=_f("paste_as_text"),cb=_f("sidebar_show"),db=_f("promotion"),ub=_f("help_accessibility"),mb=_f("default_font_stack"),gb=e=>!1===e.options.get("skin"),pb=e=>!1!==e.options.get("menubar"),hb=e=>{const t=e.options.get("skin_url");if(gb(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return kf.baseURL+"/skins/ui/"+t}},fb=e=>A.from(e.options.get("skin_url")),bb=e=>e.options.get("line_height_formats").split(" "),vb=e=>{const t=Xf(e),o=r(t),n=l(t)&&t.length>0;return!xb(e)&&(n||o||!0===t)},yb=e=>{const t=N(9,(t=>e.options.get("toolbar"+(t+1)))),o=P(t,r);return Ce(o.length>0,o)},xb=e=>yb(e).fold((()=>{const t=Xf(e);return f(t,r)&&t.length>0}),E),wb=e=>Wf(e)===wf.bottom,Sb=e=>{var t;if(!e.inline)return A.none();const o=null!==(t=jf(e))&&void 0!==t?t:"";if(o.length>0)return _l(yt(),o);const n=$f(e);return g(n)?A.some(ze(n)):A.none()},Cb=e=>e.inline&&Sb(e).isSome(),kb=e=>Sb(e).getOrThunk((()=>ht(pt(ze(e.getElement()))))),Ob=e=>e.inline&&!pb(e)&&!vb(e)&&!xb(e),_b=e=>(e.options.get("toolbar_sticky")||e.inline)&&!Cb(e)&&!Ob(e),Tb=e=>!Cb(e)&&"split"===e.options.get("ui_mode"),Eb=e=>{const t=e.options.get("menu");return le(t,(e=>({...e,items:e.items})))};var Ab=Object.freeze({__proto__:null,get ToolbarMode(){return xf},get ToolbarLocation(){return wf},register:Ef,getSkinUrl:hb,getSkinUrlOption:fb,isReadOnly:Af,isSkinDisabled:gb,getHeightOption:Mf,getWidthOption:Df,getMinWidthOption:Bf,getMinHeightOption:Ff,getMaxWidthOption:If,getMaxHeightOption:Rf,getUserStyleFormats:Nf,shouldMergeStyleFormats:zf,shouldAutoHideStyleFormats:Lf,getLineHeightFormats:bb,getContentLanguages:Vf,getRemovedMenuItems:Hf,isMenubarEnabled:pb,isMultipleToolbars:xb,isToolbarEnabled:vb,isToolbarPersist:Gf,getMultipleToolbarsOption:yb,getUiContainer:kb,useFixedContainer:Cb,isSplitUiMode:Tb,getToolbarMode:Pf,isDraggableModal:nb,isDistractionFree:Ob,isStickyToolbar:_b,getStickyToolbarOffset:qf,getToolbarLocation:Wf,isToolbarLocationBottom:wb,getToolbarGroups:Uf,getMenus:Eb,getMenubar:Yf,getToolbar:Xf,getFilePickerCallback:Kf,getFilePickerTypes:Zf,useTypeaheadUrls:eb,getAnchorTop:tb,getAnchorBottom:ob,getFilePickerValidatorHandler:Jf,getFontSizeInputDefaultUnit:Qf,useStatusBar:sb,useElementPath:rb,promotionEnabled:db,useBranding:ab,getResize:ib,getPasteAsText:lb,getSidebarShow:cb,useHelpAccessibility:ub,getDefaultFontStack:mb});const Mb=["visible","hidden","clip"],Db=e=>Ae(e).length>0&&!I(Mb,e),Bb=e=>{if(je(e)){const t=Ft(e,"overflow-x"),o=Ft(e,"overflow-y");return Db(t)||Db(o)}return!1},Fb=(e,t)=>Tb(e)?((e,t)=>{const o=kd(t,Bb),n=0===o.length?ft(t).map(bt).map((e=>kd(e,Bb))).getOr([]):o;return te(n).map((t=>({element:t,others:n.slice(1),isFullscreen:()=>(e=>e.plugins.fullscreen&&e.plugins.fullscreen.isFullscreen())(e)})))})(e,t):A.none(),Ib=e=>{const t=[...L(e.others,Ko),Zo()];return e.isFullscreen()?Zo():((e,t)=>W(t,((e,t)=>Qo(e,t)),e))(Ko(e.element),t)},Rb=qm({name:"Button",factory:e=>{const t=Wh(e.action),o=e.dom.tag,n=t=>fe(e.dom,"attributes").bind((e=>fe(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:Ku(e.buttonBehaviours,[Fh.config({}),vh.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:e.role.getOr(n("role").getOr("button"))}},eventOrder:e.eventOrder}},configFields:[Ss("uid",void 0),rs("dom"),Ss("components",[]),Xu("buttonBehaviours",[Fh,vh]),ps("action"),ps("role"),Ss("eventOrder",{})]}),Nb=e=>{const t=Ie(e),o=lt(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return W(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:oi(t)};return{tag:Ue(t),classes:s,attributes:n,...r}},zb=e=>{const t=(e=>void 0!==e.uid)(e)&&ve(e,"uid")?e.uid:Vi("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}};var Lb=Object.freeze({__proto__:null,exhibit:(e,t)=>sa({attributes:Fs([{key:t.tabAttr,value:"true"}])})}),Vb=[Ss("tabAttr","data-alloy-tabstop")];const Hb=ma({fields:Vb,name:"tabstopping",active:Lb}),Pb=Bi("tooltip.exclusive"),Ub=Bi("tooltip.show"),Wb=Bi("tooltip.hide"),jb=Bi("tooltip.immediateHide"),$b=Bi("tooltip.immediateShow"),Gb=(e,t,o)=>{e.getSystem().broadcastOn([Pb],{})};var qb=Object.freeze({__proto__:null,hideAllExclusive:Gb,immediateOpenClose:(e,t,o,n)=>Rr(e,n?$b:jb),isEnabled:(e,t,o)=>o.isEnabled(),setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&Th.set(e,n)}))},setEnabled:(e,t,o,n)=>o.setEnabled(n)}),Yb=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{n.getSystem().isConnected()&&(cu(n),e.onHide(o,n),t.clearTooltip())})),t.clearTimer()},n=o=>{if(!t.isShowing()&&t.isEnabled()){Gb(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Pr("normal"===e.mode?[jr(Xs(),(e=>{Rr(o,Ub)})),jr(qs(),(e=>{Rr(o,Wb)}))]:[]),behaviours:da([Th.config({})])});t.setTooltip(s),au(n,s),e.onShow(o,s),Zd.position(n,s,{anchor:e.anchor(o)})}},s=o=>{t.getTooltip().each((t=>{const n=e.lazySink(o).getOrDie();Zd.position(n,t,{anchor:e.anchor(o)})}))};return Pr(G([[ea((t=>{e.onSetup(t)})),jr(Ub,(o=>{t.resetTimer((()=>{n(o)}),e.delayForShow())})),jr(Wb,(n=>{t.resetTimer((()=>{o(n)}),e.delayForHide())})),jr($b,(e=>{t.resetTimer((()=>{n(e)}),0)})),jr(jb,(e=>{t.resetTimer((()=>{o(e)}),0)})),jr(mr(),((e,t)=>{const n=t;n.universal||I(n.channels,Pb)&&o(e)})),Zr((e=>{o(e)}))],(()=>{switch(e.mode){case"normal":return[jr(Ks(),(e=>{Rr(e,$b)})),jr(dr(),(e=>{Rr(e,jb)})),jr(Xs(),(e=>{Rr(e,Ub)})),jr(qs(),(e=>{Rr(e,Wb)}))];case"follow-highlight":return[jr(Fr(),((e,t)=>{Rr(e,Ub)})),jr(Ir(),(e=>{Rr(e,Wb)}))];case"children-normal":return[jr(Ks(),((o,n)=>{bc(o.element).each((r=>{Ke(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{Rr(o,$b)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),jr(dr(),(e=>{bc(e.element).fold((()=>{Rr(e,jb)}),b)})),jr(Xs(),(o=>{_l(o.element,"[data-mce-tooltip]:hover").each((n=>{t.getTooltip().fold((()=>{Rr(o,Ub)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),jr(qs(),(e=>{_l(e.element,"[data-mce-tooltip]:hover").fold((()=>{Rr(e,Wb)}),b)}))];default:return[jr(Ks(),((o,n)=>{bc(o.element).each((r=>{Ke(n.event.target,"[data-mce-tooltip]")&&t.getTooltip().fold((()=>{Rr(o,$b)}),(n=>{t.isShowing()&&(e.onShow(o,n),s(o))}))}))})),jr(dr(),(e=>{bc(e.element).fold((()=>{Rr(e,jb)}),b)}))]}})()]))}}),Xb=[rs("lazySink"),rs("tooltipDom"),Ss("exclusive",!0),Ss("tooltipComponents",[]),Es("delayForShow",x(300)),Es("delayForHide",x(300)),Es("onSetup",b),_s("mode","normal",["normal","follow-highlight","children-keyboard-focus","children-normal"]),Ss("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:x([ec,Zl,Xl,Jl,Kl,Ql]),onRtl:x([ec,Zl,Xl,Jl,Kl,Ql])},bubble:jc(0,-2,{})}))),vi("onHide"),vi("onShow")],Kb=Object.freeze({__proto__:null,init:()=>{const e=en(!0),t=nn(),o=nn(),n=()=>{t.on(clearTimeout)},s=x("not-implemented");return ca({getTooltip:o.get,isShowing:o.isSet,setTooltip:o.set,clearTooltip:o.clear,clearTimer:n,resetTimer:(e,o)=>{n(),t.set(setTimeout(e,o))},readState:s,isEnabled:()=>e.get(),setEnabled:t=>e.set(t)})}});const Jb=ma({fields:Xb,name:"tooltipping",active:Yb,state:Kb,apis:qb}),{entries:Qb,setPrototypeOf:Zb,isFrozen:ev,getPrototypeOf:tv,getOwnPropertyDescriptor:ov}=Object;let{freeze:nv,seal:sv,create:rv}=Object,{apply:av,construct:iv}="undefined"!=typeof Reflect&&Reflect;av||(av=function(e,t,o){return e.apply(t,o)}),nv||(nv=function(e){return e}),sv||(sv=function(e){return e}),iv||(iv=function(e,t){return new e(...t)});const lv=xv(Array.prototype.forEach),cv=xv(Array.prototype.pop),dv=xv(Array.prototype.push),uv=xv(String.prototype.toLowerCase),mv=xv(String.prototype.toString),gv=xv(String.prototype.match),pv=xv(String.prototype.replace),hv=xv(String.prototype.indexOf),fv=xv(String.prototype.trim),bv=xv(RegExp.prototype.test),vv=(yv=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return iv(yv,t)});var yv;function xv(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),s=1;s<o;s++)n[s-1]=arguments[s];return av(e,t,n)}}function wv(e,t,o){var n;o=null!==(n=o)&&void 0!==n?n:uv,Zb&&Zb(e,null);let s=t.length;for(;s--;){let n=t[s];if("string"==typeof n){const e=o(n);e!==n&&(ev(t)||(t[s]=e),n=e)}e[n]=!0}return e}function Sv(e){const t=rv(null);for(const[o,n]of Qb(e))t[o]=n;return t}function Cv(e,t){for(;null!==e;){const o=ov(e,t);if(o){if(o.get)return xv(o.get);if("function"==typeof o.value)return xv(o.value)}e=tv(e)}return function(e){return console.warn("fallback value for",e),null}}const kv=nv(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ov=nv(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),_v=nv(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Tv=nv(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ev=nv(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Av=nv(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Mv=nv(["#text"]),Dv=nv(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),Bv=nv(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Fv=nv(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Iv=nv(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Rv=sv(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Nv=sv(/<%[\w\W]*|[\w\W]*%>/gm),zv=sv(/\${[\w\W]*}/gm),Lv=sv(/^data-[\-\w.\u00B7-\uFFFF]/),Vv=sv(/^aria-[\-\w]+$/),Hv=sv(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Pv=sv(/^(?:\w+script|data):/i),Uv=sv(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Wv=sv(/^html$/i);var jv=Object.freeze({__proto__:null,MUSTACHE_EXPR:Rv,ERB_EXPR:Nv,TMPLIT_EXPR:zv,DATA_ATTR:Lv,ARIA_ATTR:Vv,IS_ALLOWED_URI:Hv,IS_SCRIPT_OR_DATA:Pv,ATTR_WHITESPACE:Uv,DOCTYPE_NAME:Wv});const $v=()=>"undefined"==typeof window?null:window;var Gv=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:$v();const o=t=>e(t);if(o.version="3.0.5",o.removed=[],!t||!t.document||9!==t.document.nodeType)return o.isSupported=!1,o;const n=t.document,s=n.currentScript;let{document:r}=t;const{DocumentFragment:a,HTMLTemplateElement:i,Node:l,Element:c,NodeFilter:d,NamedNodeMap:u=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:m,DOMParser:g,trustedTypes:p}=t,h=c.prototype,f=Cv(h,"cloneNode"),b=Cv(h,"nextSibling"),v=Cv(h,"childNodes"),y=Cv(h,"parentNode");if("function"==typeof i){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let x,w="";const{implementation:S,createNodeIterator:C,createDocumentFragment:k,getElementsByTagName:O}=r,{importNode:_}=n;let T={};o.isSupported="function"==typeof Qb&&"function"==typeof y&&S&&void 0!==S.createHTMLDocument;const{MUSTACHE_EXPR:E,ERB_EXPR:A,TMPLIT_EXPR:M,DATA_ATTR:D,ARIA_ATTR:B,IS_SCRIPT_OR_DATA:F,ATTR_WHITESPACE:I}=jv;let{IS_ALLOWED_URI:R}=jv,N=null;const z=wv({},[...kv,...Ov,..._v,...Ev,...Mv]);let L=null;const V=wv({},[...Dv,...Bv,...Fv,...Iv]);let H=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),P=null,U=null,W=!0,j=!0,$=!1,G=!0,q=!1,Y=!1,X=!1,K=!1,J=!1,Q=!1,Z=!1,ee=!0,te=!1,oe=!0,ne=!1,se={},re=null;const ae=wv({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ie=null;const le=wv({},["audio","video","img","source","image","track"]);let ce=null;const de=wv({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ue="http://www.w3.org/1998/Math/MathML",me="http://www.w3.org/2000/svg",ge="http://www.w3.org/1999/xhtml";let pe=ge,he=!1,fe=null;const be=wv({},[ue,me,ge],mv);let ve;const ye=["application/xhtml+xml","text/html"];let xe,we=null;const Se=r.createElement("form"),Ce=function(e){return e instanceof RegExp||e instanceof Function},ke=function(e){if(!we||we!==e){if(e&&"object"==typeof e||(e={}),e=Sv(e),ve=ve=-1===ye.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,xe="application/xhtml+xml"===ve?mv:uv,N="ALLOWED_TAGS"in e?wv({},e.ALLOWED_TAGS,xe):z,L="ALLOWED_ATTR"in e?wv({},e.ALLOWED_ATTR,xe):V,fe="ALLOWED_NAMESPACES"in e?wv({},e.ALLOWED_NAMESPACES,mv):be,ce="ADD_URI_SAFE_ATTR"in e?wv(Sv(de),e.ADD_URI_SAFE_ATTR,xe):de,ie="ADD_DATA_URI_TAGS"in e?wv(Sv(le),e.ADD_DATA_URI_TAGS,xe):le,re="FORBID_CONTENTS"in e?wv({},e.FORBID_CONTENTS,xe):ae,P="FORBID_TAGS"in e?wv({},e.FORBID_TAGS,xe):{},U="FORBID_ATTR"in e?wv({},e.FORBID_ATTR,xe):{},se="USE_PROFILES"in e&&e.USE_PROFILES,W=!1!==e.ALLOW_ARIA_ATTR,j=!1!==e.ALLOW_DATA_ATTR,$=e.ALLOW_UNKNOWN_PROTOCOLS||!1,G=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,q=e.SAFE_FOR_TEMPLATES||!1,Y=e.WHOLE_DOCUMENT||!1,J=e.RETURN_DOM||!1,Q=e.RETURN_DOM_FRAGMENT||!1,Z=e.RETURN_TRUSTED_TYPE||!1,K=e.FORCE_BODY||!1,ee=!1!==e.SANITIZE_DOM,te=e.SANITIZE_NAMED_PROPS||!1,oe=!1!==e.KEEP_CONTENT,ne=e.IN_PLACE||!1,R=e.ALLOWED_URI_REGEXP||Hv,pe=e.NAMESPACE||ge,H=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&Ce(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(H.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Ce(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(H.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(H.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),q&&(j=!1),Q&&(J=!0),se&&(N=wv({},[...Mv]),L=[],!0===se.html&&(wv(N,kv),wv(L,Dv)),!0===se.svg&&(wv(N,Ov),wv(L,Bv),wv(L,Iv)),!0===se.svgFilters&&(wv(N,_v),wv(L,Bv),wv(L,Iv)),!0===se.mathMl&&(wv(N,Ev),wv(L,Fv),wv(L,Iv))),e.ADD_TAGS&&(N===z&&(N=Sv(N)),wv(N,e.ADD_TAGS,xe)),e.ADD_ATTR&&(L===V&&(L=Sv(L)),wv(L,e.ADD_ATTR,xe)),e.ADD_URI_SAFE_ATTR&&wv(ce,e.ADD_URI_SAFE_ATTR,xe),e.FORBID_CONTENTS&&(re===ae&&(re=Sv(re)),wv(re,e.FORBID_CONTENTS,xe)),oe&&(N["#text"]=!0),Y&&wv(N,["html","head","body"]),N.table&&(wv(N,["tbody"]),delete P.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw vv('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw vv('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');x=e.TRUSTED_TYPES_POLICY,w=x.createHTML("")}else void 0===x&&(x=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let o=null;const n="data-tt-policy-suffix";t&&t.hasAttribute(n)&&(o=t.getAttribute(n));const s="dompurify"+(o?"#"+o:"");try{return e.createPolicy(s,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+s+" could not be created."),null}}(p,s)),null!==x&&"string"==typeof w&&(w=x.createHTML(""));nv&&nv(e),we=e}},Oe=wv({},["mi","mo","mn","ms","mtext"]),_e=wv({},["foreignobject","desc","title","annotation-xml"]),Te=wv({},["title","style","font","a","script"]),Ee=wv({},Ov);wv(Ee,_v),wv(Ee,Tv);const Ae=wv({},Ev);wv(Ae,Av);const Me=function(e){dv(o.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){e.remove()}},De=function(e,t){try{dv(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){dv(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!L[e])if(J||Q)try{Me(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Be=function(e){let t,o;if(K)e="<remove></remove>"+e;else{const t=gv(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===ve&&pe===ge&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const n=x?x.createHTML(e):e;if(pe===ge)try{t=(new g).parseFromString(n,ve)}catch(e){}if(!t||!t.documentElement){t=S.createDocument(pe,"template",null);try{t.documentElement.innerHTML=he?w:n}catch(e){}}const s=t.body||t.documentElement;return e&&o&&s.insertBefore(r.createTextNode(o),s.childNodes[0]||null),pe===ge?O.call(t,Y?"html":"body")[0]:Y?t.documentElement:s},Fe=function(e){return C.call(e.ownerDocument||e,e,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT,null,!1)},Ie=function(e){return"object"==typeof l?e instanceof l:e&&"object"==typeof e&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},Re=function(e,t,n){T[e]&&lv(T[e],(e=>{e.call(o,t,n,we)}))},Ne=function(e){let t;if(Re("beforeSanitizeElements",e,null),(n=e)instanceof m&&("string"!=typeof n.nodeName||"string"!=typeof n.textContent||"function"!=typeof n.removeChild||!(n.attributes instanceof u)||"function"!=typeof n.removeAttribute||"function"!=typeof n.setAttribute||"string"!=typeof n.namespaceURI||"function"!=typeof n.insertBefore||"function"!=typeof n.hasChildNodes))return Me(e),!0;var n;const s=xe(e.nodeName);if(Re("uponSanitizeElement",e,{tagName:s,allowedTags:N}),e.hasChildNodes()&&!Ie(e.firstElementChild)&&(!Ie(e.content)||!Ie(e.content.firstElementChild))&&bv(/<[/\w]/g,e.innerHTML)&&bv(/<[/\w]/g,e.textContent))return Me(e),!0;if(!N[s]||P[s]){if(!P[s]&&Le(s)){if(H.tagNameCheck instanceof RegExp&&bv(H.tagNameCheck,s))return!1;if(H.tagNameCheck instanceof Function&&H.tagNameCheck(s))return!1}if(oe&&!re[s]){const t=y(e)||e.parentNode,o=v(e)||e.childNodes;if(o&&t)for(let n=o.length-1;n>=0;--n)t.insertBefore(f(o[n],!0),b(e))}return Me(e),!0}return e instanceof c&&!function(e){let t=y(e);t&&t.tagName||(t={namespaceURI:pe,tagName:"template"});const o=uv(e.tagName),n=uv(t.tagName);return!!fe[e.namespaceURI]&&(e.namespaceURI===me?t.namespaceURI===ge?"svg"===o:t.namespaceURI===ue?"svg"===o&&("annotation-xml"===n||Oe[n]):Boolean(Ee[o]):e.namespaceURI===ue?t.namespaceURI===ge?"math"===o:t.namespaceURI===me?"math"===o&&_e[n]:Boolean(Ae[o]):e.namespaceURI===ge?!(t.namespaceURI===me&&!_e[n])&&!(t.namespaceURI===ue&&!Oe[n])&&!Ae[o]&&(Te[o]||!Ee[o]):!("application/xhtml+xml"!==ve||!fe[e.namespaceURI]))}(e)?(Me(e),!0):"noscript"!==s&&"noembed"!==s&&"noframes"!==s||!bv(/<\/no(script|embed|frames)/i,e.innerHTML)?(q&&3===e.nodeType&&(t=e.textContent,t=pv(t,E," "),t=pv(t,A," "),t=pv(t,M," "),e.textContent!==t&&(dv(o.removed,{element:e.cloneNode()}),e.textContent=t)),Re("afterSanitizeElements",e,null),!1):(Me(e),!0)},ze=function(e,t,o){if(ee&&("id"===t||"name"===t)&&(o in r||o in Se))return!1;if(j&&!U[t]&&bv(D,t));else if(W&&bv(B,t));else if(!L[t]||U[t]){if(!(Le(e)&&(H.tagNameCheck instanceof RegExp&&bv(H.tagNameCheck,e)||H.tagNameCheck instanceof Function&&H.tagNameCheck(e))&&(H.attributeNameCheck instanceof RegExp&&bv(H.attributeNameCheck,t)||H.attributeNameCheck instanceof Function&&H.attributeNameCheck(t))||"is"===t&&H.allowCustomizedBuiltInElements&&(H.tagNameCheck instanceof RegExp&&bv(H.tagNameCheck,o)||H.tagNameCheck instanceof Function&&H.tagNameCheck(o))))return!1}else if(ce[t]);else if(bv(R,pv(o,I,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==hv(o,"data:")||!ie[e])if($&&!bv(F,pv(o,I,"")));else if(o)return!1;return!0},Le=function(e){return e.indexOf("-")>0},Ve=function(e){let t,o,n,s;Re("beforeSanitizeAttributes",e,null);const{attributes:r}=e;if(!r)return;const a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:L};for(s=r.length;s--;){t=r[s];const{name:i,namespaceURI:l}=t;o="value"===i?t.value:fv(t.value);const c=o;if(n=xe(i),a.attrName=n,a.attrValue=o,a.keepAttr=!0,a.forceKeepAttr=void 0,Re("uponSanitizeAttribute",e,a),o=a.attrValue,a.forceKeepAttr)continue;if(!a.keepAttr){De(i,e);continue}if(!G&&bv(/\/>/i,o)){De(i,e);continue}q&&(o=pv(o,E," "),o=pv(o,A," "),o=pv(o,M," "));const d=xe(e.nodeName);if(ze(d,n,o)){if(!te||"id"!==n&&"name"!==n||(De(i,e),o="user-content-"+o),x&&"object"==typeof p&&"function"==typeof p.getAttributeType)if(l);else switch(p.getAttributeType(d,n)){case"TrustedHTML":o=x.createHTML(o);break;case"TrustedScriptURL":o=x.createScriptURL(o)}if(o!==c)try{l?e.setAttributeNS(l,i,o):e.setAttribute(i,o)}catch(t){De(i,e)}}else De(i,e)}Re("afterSanitizeAttributes",e,null)},He=function e(t){let o;const n=Fe(t);for(Re("beforeSanitizeShadowDOM",t,null);o=n.nextNode();)Re("uponSanitizeShadowNode",o,null),Ne(o)||(o.content instanceof a&&e(o.content),Ve(o));Re("afterSanitizeShadowDOM",t,null)};return o.sanitize=function(e){let t,s,r,i,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(he=!e,he&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Ie(e)){if("function"!=typeof e.toString)throw vv("toString is not a function");if("string"!=typeof(e=e.toString()))throw vv("dirty is not a string, aborting")}if(!o.isSupported)return e;if(X||ke(c),o.removed=[],"string"==typeof e&&(ne=!1),ne){if(e.nodeName){const t=xe(e.nodeName);if(!N[t]||P[t])throw vv("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)t=Be("\x3c!----\x3e"),s=t.ownerDocument.importNode(e,!0),1===s.nodeType&&"BODY"===s.nodeName||"HTML"===s.nodeName?t=s:t.appendChild(s);else{if(!J&&!q&&!Y&&-1===e.indexOf("<"))return x&&Z?x.createHTML(e):e;if(t=Be(e),!t)return J?null:Z?w:""}t&&K&&Me(t.firstChild);const d=Fe(ne?e:t);for(;r=d.nextNode();)Ne(r)||(r.content instanceof a&&He(r.content),Ve(r));if(ne)return e;if(J){if(Q)for(i=k.call(t.ownerDocument);t.firstChild;)i.appendChild(t.firstChild);else i=t;return(L.shadowroot||L.shadowrootmode)&&(i=_.call(n,i,!0)),i}let u=Y?t.outerHTML:t.innerHTML;return Y&&N["!doctype"]&&t.ownerDocument&&t.ownerDocument.doctype&&t.ownerDocument.doctype.name&&bv(Wv,t.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+t.ownerDocument.doctype.name+">\n"+u),q&&(u=pv(u,E," "),u=pv(u,A," "),u=pv(u,M," ")),x&&Z?x.createHTML(u):u},o.setConfig=function(e){ke(e),X=!0},o.clearConfig=function(){we=null,X=!1},o.isValidAttribute=function(e,t,o){we||ke({});const n=xe(e),s=xe(t);return ze(n,s,o)},o.addHook=function(e,t){"function"==typeof t&&(T[e]=T[e]||[],dv(T[e],t))},o.removeHook=function(e){if(T[e])return cv(T[e])},o.removeHooks=function(e){T[e]&&(T[e]=[])},o.removeAllHooks=function(){T={}},o}();const qv=e=>Gv().sanitize(e);var Yv=tinymce.util.Tools.resolve("tinymce.util.I18n");const Xv={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},Kv="temporary-placeholder",Jv=e=>()=>fe(e,Kv).getOr("!not found!"),Qv=(e,t)=>{const o=e.toLowerCase();if(Yv.isRtl()){const e=((e,t)=>Ee(e,t)?e:((e,t)=>e+t)(e,t))(o,"-rtl");return be(t,e)?e:o}return o},Zv=(e,t)=>fe(t,Qv(e,t)),ey=(e,t)=>{const o=t();return Zv(e,o).getOrThunk(Jv(o))},ty=()=>Eh("add-focusable",[Qr((e=>{Ol(e.element,"svg").each((e=>St(e,"focusable","false")))}))]),oy=(e,t,o,n)=>{var s,r;const a=(e=>!!Yv.isRtl()&&be(Xv,e))(t)?["tox-icon--flip"]:[],i=fe(o,Qv(t,o)).or(n).getOrThunk(Jv(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:da([...null!==(r=e.behaviours)&&void 0!==r?r:[],ty()])}},ny=(e,t,o,n=A.none())=>oy(t,e,o(),n),sy={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},ry=qm({name:"Notification",factory:e=>{const t=Bi("notification-text"),o=zb({dom:Nb(`<p id=${t}>${qv(e.backstageProvider.translate(e.text))}</p>`),behaviours:da([Th.config({})])}),n=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),s=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),r=zb({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[n(0)]},s(0)],behaviours:da([Th.config({})])}),a={updateProgress:(e,t)=>{e.getSystem().isConnected()&&r.getOpt(e).each((e=>{Th.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[n(t)]},s(t)])}))},updateText:(e,t)=>{if(e.getSystem().isConnected()){const n=o.get(e);Th.set(n,[ul(t)])}}},i=G([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>A.from(sy[e]))).toArray()]),l=zb(Rb.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":e.backstageProvider.translate("Close")}},components:[ny("close",{tag:"span",classes:["tox-icon"]},e.iconProvider)],buttonBehaviours:da([Hb.config({}),Jb.config({...e.backstageProvider.tooltips.getConfig({tooltipText:e.backstageProvider.translate("Close")})})]),action:t=>{e.onAction(t)}})),c=((e,t,o)=>{const n=o(),s=j(e,(e=>be(n,Qv(e,n))));return oy({tag:"div",classes:["tox-notification__icon"]},s.getOr(Kv),n,A.none())})(i,0,e.iconProvider),d=[c,{dom:{tag:"div",classes:["tox-notification__body"]},components:[o.asSpec()],behaviours:da([Th.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert","aria-labelledby":t},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:da([Hb.config({}),Fh.config({}),vh.config({mode:"special",onEscape:t=>(e.onAction(t),A.some(!0))})]),components:d.concat(e.progress?[r.asSpec()]:[]).concat([l.asSpec()]),apis:a}},configFields:[ps("level"),rs("progress"),ps("icon"),rs("onAction"),rs("text"),rs("iconProvider"),rs("backstageProvider")],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var ay=(e,t,o,n)=>{const s=t.backstage.shared,r=()=>ze(""===e.queryCommandValue("ToggleView")?e.getContentAreaContainer():e.getContainer()),a=()=>{const e=Ko(r());return A.some(e)},i=e=>{a().each((t=>{V(e,(e=>{Lt(e.element,"width"),Kt(e.element)>t.width&&Mt(e.element,"width",t.width+"px")}))}))};return{open:(t,l,c)=>{const d=()=>{n.on((t=>{l();const o=c();(e=>{Th.remove(e,u),m()})(t),((t,o)=>{0===lt(t.element).length?((t,o)=>{yf.hide(t),n.clear(),o&&e.focus()})(t,o):((e,t)=>{t&&vh.focusIn(e)})(t,o)})(t,o)}))},u=hl(ry.sketch({text:t.text,level:I(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,onAction:d,iconProvider:s.providers.icons,backstageProvider:s.providers}));if(n.isSet()){const e=fl(u);n.on((t=>{Th.append(t,e),yf.reposition(t),_i.refresh(t),i(t.components())}))}else{const t=hl(yf.sketch({dom:{tag:"div",classes:["tox-notifications-container"],attributes:{"aria-label":"Notifications",role:"region"}},lazySink:s.getSink,fireDismissalEventInstead:{},...s.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}},inlineBehaviours:da([vh.config({mode:"cyclic",selector:".tox-notification, .tox-notification a, .tox-notification button"}),Th.config({}),..._b(e)&&!s.header.isPositionedAtTop()?[]:[_i.config({contextual:{lazyContext:()=>A.some(Ko(r())),fadeInClass:"tox-notification-container-dock-fadein",fadeOutClass:"tox-notification-container-dock-fadeout",transitionClass:"tox-notification-container-dock-transition"},modes:["top"],lazyViewport:t=>Fb(e,t.element).map((e=>({bounds:Ib(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:qt(e.element).top})}))).getOrThunk((()=>({bounds:Zo(),optScrollEnv:A.none()})))})]])})),i=fl(u),l={maxHeightFunction:Hc()},c={...s.anchors.banner(),overrides:l};n.set(t),o.add(t),yf.showWithinBounds(t,i,{anchor:c},a)}h(t.timeout)&&t.timeout>0&&Sf.setEditorTimeout(e,(()=>{d()}),t.timeout);const m=()=>{n.on((e=>{yf.reposition(e),_i.refresh(e),i(e.components())}))};return{close:d,reposition:m,text:e=>{ry.updateText(u,e)},settings:t,getEl:()=>u.element.dom,progressBar:{value:e=>{ry.updateProgress(u,e)}}}},close:e=>{e.close()},getArgs:e=>e.settings}};var iy;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(iy||(iy={}));var ly=iy;const cy="tox-menu-nav__js",dy="tox-collection__item",uy="tox-swatch",my={normal:cy,color:uy},gy="tox-collection__item--enabled",py="tox-collection__item-icon",hy="tox-collection__item-label",fy="tox-collection__item-caret",by="tox-collection__item--active",vy="tox-collection__item-container",yy="tox-collection__item-container--row",xy=e=>fe(my,e).getOr(cy),wy=e=>"color"===e?"tox-swatches":"tox-menu",Sy=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:wy(e),tieredMenu:"tox-tiered-menu"}),Cy=e=>{const t=Sy(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:xy(e)}},ky=(e,t,o)=>{const n=Sy(o);return{tag:"div",classes:G([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},Oy=[cf.parts.items({})],_y=(e,t,o)=>{const n=Sy(o);return{dom:{tag:"div",classes:G([[n.tieredMenu]])},markers:Cy(o)}},Ty=x([ps("data"),Ss("inputAttributes",{}),Ss("inputStyles",{}),Ss("tag","input"),Ss("inputClasses",[]),vi("onSetValue"),Ss("styles",{}),Ss("eventOrder",{}),Gu("inputBehaviours",[$u,Fh]),Ss("selectOnFocus",!0)]),Ey=e=>da([Fh.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=ol(t);t.dom.setSelectionRange(0,o.length)}:b})]),Ay=e=>({...Ey(e),...Yu(e.inputBehaviours,[$u.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>ol(e.element),setValue:(e,t)=>{ol(e.element)!==t&&nl(e.element,t)}},onSetValue:e.onSetValue})])}),My=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),Dy=qm({name:"Input",configFields:Ty(),factory:(e,t)=>({uid:e.uid,dom:My(e),components:[],behaviours:Ay(e),eventOrder:e.eventOrder})}),By=Bi("refetch-trigger-event"),Fy=Bi("redirect-menu-item-interaction"),Iy="tox-menu__searcher",Ry=e=>_l(e.element,`.${Iy}`).bind((t=>e.getSystem().getByDom(t).toOptional())),Ny=Ry,zy=e=>({fetchPattern:$u.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),Ly=e=>{const t=(e,t)=>(t.cut(),A.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Nr(e,Fy,o),A.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[dy]},components:[Dy.sketch({inputClasses:[Iy,"tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:da([Eh(n,[jr(er(),(e=>{Rr(e,By)})),jr(Qs(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),vh.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,vh.name()]}})]}},Vy="tox-collection--results__js",Hy=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:Bi("aria-item-search-result-id"),"aria-selected":"false"}}}:e},Py=(e,t)=>o=>{const n=z(o,t);return L(n,(t=>({dom:e,components:t})))},Uy=(e,t)=>{const o=[];let n=[];return V(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(be(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),L(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},Wy=(e,t,o)=>cf.parts.items({preprocess:n=>{const s=L(n,o);return"auto"!==e&&e>1?Py({tag:"div",classes:["tox-collection__group"]},e)(s):Uy(s,((e,o)=>"separator"===t[o].type))}}),jy=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Wy(e,t,w)]}),$y=e=>R(e,(e=>"icon"in e&&void 0!==e.icon)),Gy=e=>(console.error(Zn(e)),console.log(e),A.none()),qy=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[cf.parts.items({preprocess:e=>Uy(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},Yy=(e,t,o,n,s)=>{if("color"===s.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[cf.parts.items({preprocess:"auto"!==e?Py({tag:"div",classes:["tox-swatches__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType&&"auto"===n){const t=jy(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType||"searchable"===s.menuType){const t="searchable"!==s.menuType?jy(n,o):"search-with-field"===s.searchMode.searchMode?((e,t,o)=>{const n=Bi("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Ly({i18n:Yv.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],Vy],attributes:{id:n}},components:[Wy(e,t,Hy)]}]}})(n,o,s.searchMode):((e,t,o=!0)=>{const n=Bi("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",Vy].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:n}},components:[Wy(e,t,Hy)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[cf.parts.items({preprocess:Py({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:ky(t,n,s.menuType),components:Oy,items:o}},Xy=ls("type"),Ky=ls("name"),Jy=ls("label"),Qy=ls("text"),Zy=ls("title"),ex=ls("icon"),tx=ls("value"),ox=ds("fetch"),nx=ds("getSubmenuItems"),sx=ds("onAction"),rx=ds("onItemAction"),ax=Es("onSetup",(()=>b)),ix=bs("name"),lx=bs("text"),cx=bs("role"),dx=bs("icon"),ux=bs("tooltip"),mx=bs("label"),gx=bs("shortcut"),px=ys("select"),hx=Ts("active",!1),fx=Ts("borderless",!1),bx=Ts("enabled",!0),vx=Ts("primary",!1),yx=e=>Ss("columns",e),xx=Ss("meta",{}),wx=Es("onAction",b),Sx=e=>Os("type",e),Cx=e=>os("name","name",wn((()=>Bi(`${e}-name`))),Un),kx=In([Xy,lx]),Ox=In([Sx("autocompleteitem"),hx,bx,xx,tx,lx,dx]),_x=[bx,ux,dx,lx,ax],Tx=In([Xy,sx,gx].concat(_x)),Ex=e=>Kn("toolbarbutton",Tx,e),Ax=[hx].concat(_x),Mx=In(Ax.concat([Xy,sx,gx])),Dx=e=>Kn("ToggleButton",Mx,e),Bx=[Es("predicate",T),_s("scope","node",["node","editor"]),_s("position","selection",["node","selection","line"])],Fx=_x.concat([Sx("contextformbutton"),vx,sx,ns("original",w)]),Ix=Ax.concat([Sx("contextformbutton"),vx,sx,ns("original",w)]),Rx=_x.concat([Sx("contextformbutton")]),Nx=Ax.concat([Sx("contextformtogglebutton")]),zx=es("type",{contextformbutton:Fx,contextformtogglebutton:Ix}),Lx=In([Sx("contextform"),Es("initValue",x("")),mx,gs("commands",zx),hs("launch",es("type",{contextformbutton:Rx,contextformtogglebutton:Nx}))].concat(Bx)),Vx=In([Sx("contexttoolbar"),ls("items")].concat(Bx)),Hx=[Xy,ls("src"),bs("alt"),As("classes",[],Un)],Px=In(Hx),Ux=[Xy,Qy,ix,As("classes",["tox-collection__item-label"],Un)],Wx=In(Ux),jx=Dn((()=>qn("type",{cardimage:Px,cardtext:Wx,cardcontainer:$x}))),$x=In([Xy,Os("direction","horizontal"),Os("align","left"),Os("valign","middle"),gs("items",jx)]),Gx=[bx,lx,cx,gx,("menuitem",os("value","value",wn((()=>Bi("menuitem-value"))),Vn())),xx];const qx=In([Xy,mx,gs("items",jx),ax,wx].concat(Gx)),Yx=In([Xy,hx,dx].concat(Gx)),Xx=[Xy,ls("fancytype"),wx],Kx=[Ss("initData",{})].concat(Xx),Jx=[ys("select"),Ms("initData",{},[Ts("allowCustomColors",!0),Os("storageKey","default"),xs("colors",Vn())])].concat(Xx),Qx=es("fancytype",{inserttable:Kx,colorswatch:Jx}),Zx=In([Xy,ax,wx,dx].concat(Gx)),ew=In([Xy,nx,ax,dx].concat(Gx)),tw=In([Xy,dx,hx,ax,sx].concat(Gx)),ow=(e,t,o)=>{const n=Od(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return A.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return A.none()},nw=e=>((e,t)=>da([Eh(e,t)]))(Bi("unnamed-events"),e),sw="silver.readonly",rw=In([("readonly",as("readonly",Wn))]);const aw=(e,t)=>{const o=e.mainUi.outerContainer.element,n=[e.mainUi.mothership,...e.uiMotherships];t&&V(n,(e=>{e.broadcastOn([_u()],{target:o})})),V(n,(e=>{e.broadcastOn([sw],{readonly:t})}))},iw=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&aw(t,!0)})),e.on("SwitchMode",(()=>aw(t,e.mode.isReadOnly()))),Af(e)&&e.mode.set("readonly")},lw=()=>uc.config({channels:{[sw]:{schema:rw,onReceive:(e,t)=>{mg.set(e,t.readonly)}}}}),cw=e=>mg.config({disabled:e}),dw=e=>mg.config({disabled:e,disableClass:"tox-tbtn--disabled"}),uw=e=>mg.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),mw=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},gw=(e,t)=>Qr((o=>{mw(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),pw=(e,t)=>Zr((o=>mw(e,o)(t.get()))),hw=(e,t)=>ta(((o,n)=>{mw(e,o)(e.onAction),e.triggersSubmenu||t!==ly.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&Rr(o,br()),n.stop())})),fw={[gr()]:["disabling","alloy.base.behaviour","toggling","item-events"]},bw=xe,vw=(e,t,o,n)=>{const s=en(b);return{type:"item",dom:t.dom,components:bw(t.optComponents),data:e.data,eventOrder:fw,hasSubmenu:e.triggersSubmenu,itemBehaviours:da([Eh("item-events",[hw(e,o),gw(e,s),pw(e,s)]),(r=()=>!e.enabled||n.isDisabled(),mg.config({disabled:r,disableClass:"tox-collection__item--state-disabled"})),lw(),Th.config({})].concat(e.itemBehaviours))};var r},yw=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),xw=e=>{const t=Of.os.isMacOS()||Of.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=L(n,(e=>{const t=e.toLowerCase().trim();return be(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},ww=(e,t,o=[py])=>ny(e,{tag:"div",classes:o},t),Sw=e=>({dom:{tag:"div",classes:[hy]},components:[ul(Yv.translate(e))]}),Cw=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),kw=(e,t)=>({dom:{tag:"div",classes:[hy]},components:[{dom:{tag:e.tag,styles:e.styles},components:[ul(Yv.translate(t))]}]}),Ow=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[ul(xw(e))]}),_w=e=>ww("checkmark",e,["tox-collection__item-checkmark"]),Tw=e=>{const t=e.map((e=>({attributes:{id:Bi("menu-item"),"aria-label":Yv.translate(e)}}))).getOr({});return{tag:"div",classes:[cy,dy],...t}},Ew=(e,t,o,n=A.none())=>"color"===e.presets?((e,t,o)=>{const n=e.value,s=e.iconContent.map((e=>((e,t,o)=>{const n=t();return Zv(e,n).or(o).getOrThunk(Jv(n))})(e,t.icons,o))),r=e.ariaLabel.map((e=>({"aria-label":t.translate(e),"data-mce-name":e}))).getOr({});return{dom:(()=>{const e=uy,t=s.getOr(""),o={tag:"div",attributes:r,classes:[e]};return"custom"===n?{...o,tag:"button",classes:[...o.classes,"tox-swatches__picker-btn"],innerHtml:t}:"remove"===n?{...o,classes:[...o.classes,"tox-swatch--remove"],innerHtml:t}:g(n)?{...o,attributes:{...o.attributes,"data-mce-color":n},styles:{"background-color":n},innerHtml:t}:o})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const s={tag:"div",classes:[py]},r=o?e.iconContent.map((e=>ny(e,s,t.icons,n))).orThunk((()=>A.some({dom:s}))):A.none(),a=e.checkMark,i=A.from(e.meta).fold((()=>Sw),(e=>be(e,"style")?C(kw,e.style):Sw)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>A.some(Cw(e,[hy]))));return{dom:Tw(e.ariaLabel),optComponents:[r,l,e.shortcutContent.map(Ow),a,e.caret]}})(e,t,o,n),Aw=(e,t,o)=>fe(e,"tooltipWorker").map((e=>[Jb.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:Hc}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{Jb.setComponents(t,[ml({element:ze(e)})])}))}})])).getOrThunk((()=>o.map((e=>[Jb.config({...t.providers.tooltips.getConfig({tooltipText:e}),mode:"follow-highlight"})])).getOr([]))),Mw=(e,t)=>{const o=(e=>Cf.DOM.encode(e))(Yv.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},Dw=(e,t)=>L(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":yy,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[vy,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,Dw(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>I(t.cardText.highlightOn,e))),n=o?A.from(t.cardText.matchText).getOr(""):"";return Cw(Mw(e.text,n),e.classes)}})),Bw=_m(tf(),of()),Fw=e=>({value:zw(e)}),Iw=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Rw=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Nw=e=>Iw.test(e)||Rw.test(e),zw=e=>Oe(e,"#").toUpperCase(),Lw=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},Vw=e=>{const t=Lw(e.red)+Lw(e.green)+Lw(e.blue);return Fw(t)},Hw=Math.min,Pw=Math.max,Uw=Math.round,Ww=/^\s*rgb\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*\)\s*$/i,jw=/^\s*rgba\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*((?:\d?\.\d+|\d+)%?)\s*\)\s*$/i,$w=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),Gw=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},qw=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=Pw(0,Hw(r,1)),a=Pw(0,Hw(a,1)),0===r)return t=o=n=Uw(255*a),$w(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=Uw(255*(t+d)),o=Uw(255*(o+d)),n=Uw(255*(n+d)),$w(t,o,n,1)},Yw=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(Iw,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=Rw.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return $w(o,n,s,1)},Xw=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return $w(s,r,a,i)},Kw=e=>{const t=Ww.exec(e);if(null!==t)return A.some(Xw(t[1],t[2],t[3],"1"));const o=jw.exec(e);return null!==o?A.some(Xw(o[1],o[2],o[3],o[4])):A.none()},Jw=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,Qw=$w(255,0,0,1),Zw=(e,t)=>{e.dispatch("ResizeContent",t)},eS=(e,t)=>{e.dispatch("TextColorChange",t)},tS=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t}),oS=(e,t)=>()=>{e(),t()},nS=e=>rS(e,"NodeChange",(t=>{t.setEnabled(e.selection.isEditable())})),sS=(e,t)=>o=>{const n=nS(e)(o),s=((e,t)=>o=>{const n=on(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}})(e,t)(o);return()=>{n(),s()}},rS=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},aS=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},iS=(e,t)=>()=>e.execCommand(t);var lS=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const cS={},dS=e=>fe(cS,e).getOrThunk((()=>{const t=`tinymce-custom-colors-${e}`,o=lS.getItem(t);if(m(o)){const e=lS.getItem("tinymce-custom-colors");lS.setItem(t,g(e)?e:"[]")}const n=((e,t=10)=>{const o=lS.getItem(e),n=r(o)?JSON.parse(o):[],s=t-(a=n).length<0?a.slice(0,t):a;var a;const i=e=>{s.splice(e,1)};return{add:o=>{((e,t)=>{const o=F(e,t);return-1===o?A.none():A.some(o)})(s,o).each(i),s.unshift(o),s.length>t&&s.pop(),lS.setItem(e,JSON.stringify(s))},state:()=>s.slice(0)}})(t,10);return cS[e]=n,n})),uS=(e,t)=>{dS(e).add(t)},mS=(e,t,o)=>({hue:e,saturation:t,value:o}),gS=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,mS(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,mS(Math.round(t),Math.round(100*o),Math.round(100*n)))},pS=e=>Vw(qw(e)),hS=e=>{return(t=e,Nw(t)?A.some({value:zw(t)}):A.none()).orThunk((()=>Kw(e).map(Vw))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return Vw($w(s,r,a,i))}));var t},fS="forecolor",bS="hilitecolor",vS=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+hS(e[o]).value,icon:"checkmark",type:"choiceitem"});return t},yS=e=>t=>t.options.get(e),xS="#000000",wS=(e,t)=>t===fS&&e.options.isSet("color_map_foreground")?yS("color_map_foreground")(e):t===bS&&e.options.isSet("color_map_background")?yS("color_map_background")(e):yS("color_map")(e),SS=(e,t="default")=>Math.max(5,Math.ceil(Math.sqrt(wS(e,t).length))),CS=(e,t)=>{const o=yS("color_cols")(e),n=SS(e,t);return o===SS(e)?n:o},kS=(e,t="default")=>Math.round(t===fS?yS("color_cols_foreground")(e):t===bS?yS("color_cols_background")(e):yS("color_cols")(e)),OS=yS("custom_colors"),_S=yS("color_default_foreground"),TS=yS("color_default_background"),ES=(e,t)=>{const o=ze(e.selection.getStart()),n="hilitecolor"===t?Ns(o,(e=>{if($e(e)){const t=Ft(e,"background-color");return Ce(Kw(t).exists((e=>0!==e.alpha)),t)}return A.none()})).getOr("rgba(0, 0, 0, 0)"):Ft(o,"color");return Kw(n).map((e=>"#"+Vw(e).value))},AS=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},MS=(e,t,o,n)=>{"custom"===o?VS(e)((o=>{o.each((o=>{uS(t,o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),ES(e,t).getOr(xS)):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},DS=(e,t,o)=>e.concat((e=>L(dS(e).state(),(e=>({type:"choiceitem",text:e,icon:"checkmark",value:e}))))(t).concat(AS(o))),BS=(e,t,o)=>n=>{n(DS(e,t,o))},FS=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},IS=(e,t)=>{e.setTooltip(t)},RS=(e,t)=>o=>{const n=ES(e,t);return ye(n,o.toUpperCase())},NS=(e,t,o)=>{if(De(o))return"forecolor"===t?"Text color":"Background color";const n="forecolor"===t?"Text color {0}":"Background color {0}",s=DS(wS(e,t),t,!1),r=j(s,(e=>e.value===o)).getOr({text:""}).text;return e.translate([n,e.translate(r)])},zS=(e,t,o,n)=>{e.ui.registry.addSplitButton(t,{tooltip:NS(e,o,n.get()),presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:RS(e,o),columns:kS(e,o),fetch:BS(wS(e,o),o,OS(e)),onAction:t=>{MS(e,o,n.get(),b)},onItemAction:(s,r)=>{MS(e,o,r,(o=>{n.set(o),eS(e,{name:t,color:o})}))},onSetup:s=>{FS(s,t,n.get());const r=n=>{n.name===t&&(FS(s,n.name,n.color),IS(s,NS(e,o,n.color)))};return e.on("TextColorChange",r),oS(nS(e)(s),(()=>{e.off("TextColorChange",r)}))}})},LS=(e,t,o,n,s)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",onSetup:n=>(IS(n,NS(e,o,s.get())),FS(n,t,s.get()),nS(e)(n)),getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",select:RS(e,o),initData:{storageKey:o},onAction:n=>{MS(e,o,n.value,(o=>{s.set(o),eS(e,{name:t,color:o})}))}}]})},VS=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(A.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(A.none())}})},HS=(e,t,o,n,s,r,a,i)=>{const l=$y(t),c=PS(t,o,n,"color"!==s?"normal":"color",r,a,i);return Yy(e,l,c,n,{menuType:s})},PS=(e,t,o,n,s,r,a)=>xe(L(e,(i=>{return"choiceitem"===i.type?(l=i,Kn("choicemenuitem",Yx,l)).fold(Gy,(i=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Ew({presets:o,textContent:t?e.text:A.none(),htmlContent:A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:A.none(),checkMark:t?A.some(_w(a.icons)):A.none(),caret:A.none(),value:e.value},a,i),c=e.text.filter(x(!t)).map((e=>Jb.config(a.tooltips.getConfig({tooltipText:a.translate(e)}))));return yn(vw({data:yw(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Ph.set(e,t)},isActive:()=>Ph.isOn(e),isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[...c.toArray()]},l,r,a),{toggling:{toggleClass:gy,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(i,1===o,n,t,r(i.value),s,a,$y(e))))):A.none();var l}))),US=(e,t)=>{const o=Cy(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group"),previousSelector:e=>"color"===t?_l(e.element,"[aria-checked=true]"):A.none()}},WS=Bi("cell-over"),jS=Bi("cell-execute"),$S=(e,t,o)=>{const n=o=>Nr(o,jS,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return hl({dom:{tag:"div",attributes:{role:"button","aria-label":o}},behaviours:da([Eh("insert-table-picker-cell",[jr(Xs(),Fh.focus),jr(gr(),n),jr(or(),s),jr(hr(),s)]),Ph.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Fh.config({onFocus:o=>Nr(o,WS,{row:e,col:t})})])})},GS=e=>q(e,(e=>L(e,fl))),qS=(e,t)=>ul(`${t}x${e}`),YS={inserttable:(e,t)=>{const o=(e=>(t,o)=>e.shared.providers.translate(["{0} columns, {1} rows",o,t]))(t),n=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++){const s=e(t+1,n+1);o.push($S(t,n,s))}n.push(o)}return n})(o),s=qS(0,0),r=zb({dom:{tag:"span",classes:["tox-insert-table-picker__label"]},components:[s],behaviours:da([Th.config({})])});return{type:"widget",data:{value:Bi("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Bw.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:GS(n).concat(r.asSpec()),behaviours:da([Eh("insert-table-picker",[Qr((e=>{Th.set(r.get(e),[s])})),Yr(WS,((e,t,o)=>{const{row:s,col:a}=o.event;((e,t,o,n,s)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)Ph.set(e[n][s],n<=t&&s<=o)})(n,s,a),Th.set(r.get(e),[qS(s+1,a+1)])})),Yr(jS,((t,o,n)=>{const{row:s,col:r}=n.event;Rr(t,br()),e.onAction({numRows:s+1,numColumns:r+1})}))]),vh.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>DS(t.colorinput.getColors(e.initData.storageKey),e.initData.storageKey,o)),(e=>e.concat(AS(o))))})(e,t),n=t.colorinput.getColorCols(e.initData.storageKey),s="color",r={...HS(Bi("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,ly.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),markers:Cy(s),movement:US(n,s),showMenuRole:!1};return{type:"widget",data:{value:Bi("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Bw.widget(cf.sketch(r))]}}},XS=e=>({type:"separator",dom:{tag:"div",classes:[dy,"tox-collection__group-heading"]},components:e.text.map(ul).toArray()});var KS=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),JS=[as("others",Xn(ln.value,Vn()))],QS=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===re(t.others).length)throw new Error("Cannot find any known coupled components");return fe(e,o)},o=x({});return ca({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(fe(o.others,n).getOrDie("No information found for coupled component: "+n),A.none()))),getOrCreate:(o,n,s)=>t(n,s).getOrThunk((()=>{const t=fe(n.others,s).getOrDie("No information found for coupled component: "+s)(o),r=o.getSystem().build(t);return e[s]=r,r}))})}});const ZS=ma({fields:JS,name:"coupling",apis:KS,state:QS}),eC=e=>{let t=A.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=A.some(e),V(o,r),o=[])})),{get:n,map:e=>eC((t=>{n((o=>{t(e(o))}))})),isReady:s}},tC={nu:eC,pure:e=>eC((t=>{t(e)}))},oC=e=>{setTimeout((()=>{throw e}),0)},nC=e=>{const t=t=>{e().then(t,oC)};return{map:t=>nC((()=>e().then(t))),bind:t=>nC((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>nC((()=>e().then((()=>t.toPromise())))),toLazy:()=>tC.nu(t),toCached:()=>{let t=null;return nC((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},sC=e=>nC((()=>new Promise(e))),rC=e=>nC((()=>Promise.resolve(e))),aC=x("sink"),iC=x(wm({name:aC(),overrides:x({dom:{tag:"div"},behaviours:da([Zd.config({useFixed:E})]),events:Pr([Xr(Qs()),Xr($s()),Xr(or())])})})),lC=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},cC=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=mC(n,e);return i.map((t=>t.bind((t=>{const i=t.menus[t.primary];return A.from(i).each((t=>{e.listRole.each((e=>{t.role=e}))})),A.from(vf.sketch({...r.menu(),uid:Vi(""),data:t,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();Zd.position(n,t,{anchor:o}),Ou.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();Zd.position(n,o,{anchor:{type:"submenu",item:t}}),Ou.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();Zd.position(s,t,{anchor:o}),V(n,(e=>{Zd.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(Fh.focus(n),Ou.close(s),A.some(!0))}))}))))})(e,t,lC(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{Ou.isOpen(n)&&Ou.close(n)}),(e=>{Ou.cloak(n),Ou.open(n,e),r(n)})),n)))},dC=(e,t,o,n,s,r,a)=>(Ou.close(n),rC(n)),uC=(e,t,o,n,s,r)=>{const a=ZS.getCoupled(o,"sandbox");return(Ou.isOpen(a)?dC:cC)(e,t,o,a,n,s,r)},mC=(e,t)=>e.getSystem().getByUid(t.uid+"-"+aC()).map((e=>()=>ln.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>ln.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),gC=e=>{Ou.getState(e).each((e=>{vf.repositionMenus(e)}))},pC=(e,t,o)=>{const n=Al(),s=mC(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id}},behaviours:Ku(e.sandboxBehaviours,[$u.config({store:{mode:"memory",initialValue:t}}),Ou.config({onOpen:(s,r)=>{const a=lC(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=Qm.getCurrent(t).getOr(t),s=Kt(e.element);o?Mt(n.element,"min-width",s+"px"):((e,t)=>{Xt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,r)=>{n.unlink(t.element),s().getOr(r).element.dom.dispatchEvent(new window.FocusEvent("focusout")),void 0!==o&&void 0!==o.onClose&&o.onClose(e,r)},isPartOf:(e,o,n)=>Ml(o,n)||Ml(t,n),getAttachPoint:()=>s().getOrDie()}),Qm.config({find:e=>Ou.getState(e).bind((e=>Qm.getCurrent(e)))}),uc.config({channels:{...Mu({isExtraPart:T}),...Bu({doReposition:gC})}})])}},hC=e=>{const t=ZS.getCoupled(e,"sandbox");gC(t)},fC=()=>[Ss("sandboxClasses",[]),Xu("sandboxBehaviours",[Qm,uc,Ou,$u])],bC=x([rs("dom"),rs("fetch"),vi("onOpen"),yi("onExecute"),Ss("getHotspot",A.some),Ss("getAnchorOverrides",x({})),Qc(),Gu("dropdownBehaviours",[Ph,ZS,vh,Fh]),rs("toggleClass"),Ss("eventOrder",{}),ps("lazySink"),Ss("matchWidth",!1),Ss("useMinWidth",!1),ps("role"),ps("listRole")].concat(fC())),vC=x([xm({schema:[hi(),Ss("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),iC()]),yC=Ym({name:"Dropdown",configFields:bC(),partFields:vC(),factory:(e,t,o,n)=>{const s=e=>{Ou.getState(e).each((e=>{vf.highlightPrimary(e)}))},r=(t,o,s)=>uC(e,w,t,n,o,s),a={expand:e=>{Ph.isOn(e)||r(e,b,ff.HighlightNone).get(b)},open:e=>{Ph.isOn(e)||r(e,b,ff.HighlightMenuAndItem).get(b)},refetch:t=>ZS.getExistingCoupled(t,"sandbox").fold((()=>r(t,b,ff.HighlightMenuAndItem).map(b)),(o=>cC(e,w,t,o,n,b,ff.HighlightMenuAndItem).map(b))),isOpen:Ph.isOn,close:e=>{Ph.isOn(e)&&r(e,b,ff.HighlightMenuAndItem).get(b)},repositionMenus:e=>{Ph.isOn(e)&&hC(e)}},i=(e,t)=>(zr(e),A.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:Yu(e.dropdownBehaviours,[Ph.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),ZS.config({others:{sandbox:t=>pC(e,t,{onOpen:()=>Ph.on(t),onClose:()=>Ph.off(t)})}}),vh.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if(yC.isOpen(e)){const t=ZS.getCoupled(e,"sandbox");s(t)}else yC.open(e);return A.some(!0)},onEscape:(e,t)=>yC.isOpen(e)?(yC.close(e),A.some(!0)):A.none()}),Fh.config({})]),events:Wh(A.some((e=>{r(e,s,ff.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[gr()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":e.listRole.getOr("true"),...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",fe(e.dom,"attributes").bind((e=>fe(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),xC=(e,t,o)=>{Ny(e).each((e=>{var n;((e,t)=>{Ot(t.element,"id").each((t=>St(e.element,"aria-activedescendant",t)))})(e,o),(ka((n=t).element,Vy)?A.some(n.element):_l(n.element,"."+Vy)).each((t=>{Ot(t,"id").each((t=>St(e.element,"aria-controls",t)))}))})),St(o.element,"aria-selected","true")},wC=(e,t,o)=>{St(o.element,"aria-selected","false")},SC=e=>ZS.getExistingCoupled(e,"sandbox").bind(Ry).map(zy).map((e=>e.fetchPattern)).getOr("");var CC;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(CC||(CC={}));const kC=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:A.none(),icon:e.text.isSome()?A.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,Kn("menuitem",Zx,i)).fold(Gy,(e=>A.some(((e,t,o,n=!0)=>{const s=Ew({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.none(),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return vw({data:yw(e),getApi:e=>({isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>Kn("nestedmenuitem",ew,e))(e).fold(Gy,(e=>A.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,ww("chevron-down",a,[fy])):(e=>ww("chevron-right",e,[fy]))(o.icons);var a;const i=Ew({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.some(r),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return vw({data:yw(e),getApi:e=>({isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t),setIconFill:(t,o)=>{_l(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{St(e,"fill",o)}))},setTooltip:t=>{const n=o.translate(t);St(e.element,"aria-label",n)}}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>Kn("togglemenuitem",tw,e))(e).fold(Gy,(e=>A.some(((e,t,o,n=!0)=>{const s=Ew({iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,checkMark:A.some(_w(o.icons)),caret:A.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return yn(vw({data:yw(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Ph.set(e,t)},isActive:()=>Ph.isOn(e),isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:gy,toggleOnExecute:!1,selected:e.active},role:e.role.getOrUndefined()})})(a(e),t,r,n))));case"separator":return(e=>Kn("separatormenuitem",kx,e))(e).fold(Gy,(e=>A.some(XS(e))));case"fancymenuitem":return(e=>Kn("fancymenuitem",Qx,e))(e).fold(Gy,(e=>((e,t)=>fe(YS,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),A.none()}var i},OC=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||$y(e);return xe(L(e,(e=>{switch(e.type){case"separator":return(n=e,Kn("Autocompleter.Separator",kx,n)).fold(Gy,(e=>A.some(XS(e))));case"cardmenuitem":return(e=>Kn("cardmenuitem",qx,e))(e).fold(Gy,(e=>A.some(((e,t,o,n)=>{const s={dom:Tw(e.label),optComponents:[A.some({dom:{tag:"div",classes:[vy,yy]},components:Dw(e.items,n)})]};return vw({data:yw({text:A.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>{mg.set(e,!t),V(Od(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(mg)&&mg.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:A.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:Aw(e.meta,r,A.none()),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>Kn("Autocompleter.Item",Ox,e))(e).fold(Gy,(e=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Ew({presets:n,textContent:A.none(),htmlContent:o?e.text.map((e=>Mw(e,t))):A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:A.none(),checkMark:A.none(),caret:A.none(),value:e.value},a.providers,i,e.icon),c=e.text.filter((e=>!o&&""!==e));return vw({data:yw(e),enabled:e.enabled,getApi:x({}),onAction:t=>s(e.value,e.meta),onSetup:x(b),triggersSubmenu:!1,itemBehaviours:Aw(e,a,c)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},_C=(e,t,o,n,s,r)=>{const a=$y(t),i=xe(L(t,(e=>{const t=e=>kC(e,o,n,(e=>s?!be(e,"text"):a)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(r);return(s?qy:Yy)(e,a,i,1,l)},TC=e=>vf.singleData(e.value,e),EC=e=>gd(ze(e.startContainer),e.startOffset,ze(e.endContainer),e.endOffset),AC=(e,t)=>{const o=Bi("autocompleter"),n=en(!1),s=en(!1),r=nn(),a=hl(yf.sketch({dom:{tag:"div",classes:["tox-autocompleter"],attributes:{id:o}},components:[],fireDismissalEventInstead:{},inlineBehaviours:da([Eh("dismissAutocompleter",[jr(_r(),(()=>u())),jr(Fr(),((t,o)=>{Ot(o.event.target,"id").each((t=>St(ze(e.getBody()),"aria-activedescendant",t)))}))])]),lazySink:t.getSink})),i=()=>yf.isOpen(a),l=s.get,c=()=>{if(i()){yf.hide(a),e.dom.remove(o,!1);const t=ze(e.getBody());Ot(t,"aria-owns").filter((e=>e===o)).each((()=>{Tt(t,"aria-owns"),Tt(t,"aria-activedescendant")}))}},d=()=>yf.getContent(a).bind((e=>ee(e.components(),0))),u=()=>e.execCommand("mceAutocompleterClose"),m=s=>{const i=(o=>{const s=se(o,(e=>A.from(e.columns))).getOr(1);return q(o,(o=>{const a=o.items;return OC(a,o.matchText,((t,s)=>{const a={hide:()=>u(),reload:t=>{c(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};e.execCommand("mceAutocompleterRefreshActiveRange"),r.get().each((e=>{n.set(!0),o.onAction(a,e,t,s),n.set(!1)}))}),s,ly.BUBBLE_TO_SANDBOX,t,o.highlightOn)}))})(s);i.length>0?(((t,o)=>{const n=se(t,(e=>A.from(e.columns))).getOr(1);yf.showMenuAt(a,{anchor:{type:"selection",getSelection:()=>r.get().map(EC),root:ze(e.getBody())}},((e,t,o,n)=>{const s=US(t,n),r=Cy(n);return{data:TC({...e,movement:s,menuBehaviours:nw("auto"!==t?[]:[Qr(((e,t)=>{ow(e,4,r.item).each((({numColumns:t,numRows:o})=>{vh.setGridSize(e,o,t)}))}))])}),menu:{markers:Cy(n),fakeFocus:o===CC.ContentFocus}}})(Yy("autocompleter-value",!0,o,n,{menuType:"normal"}),n,CC.ContentFocus,"normal")),d().each(Sg.highlightFirst)})(s,i),St(ze(e.getBody()),"aria-owns",o),e.inline||g()):c()},g=()=>{e.dom.get(o)&&e.dom.remove(o,!1);const t=e.getDoc().documentElement,n=e.selection.getNode(),s=(e=>si(e,!0))(a.element);Dt(s,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px",top:`${n.offsetTop}px`,left:`${n.offsetLeft}px`}),e.dom.add(t,s.dom),_l(s,'[role="menu"]').each((e=>{Lt(e,"position"),Lt(e,"max-height")}))};e.on("AutocompleterStart",(({lookupData:e})=>{s.set(!0),n.set(!1),m(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>m(e))),e.on("AutocompleterUpdateActiveRange",(({range:e})=>r.set(e))),e.on("AutocompleterEnd",(()=>{c(),s.set(!1),n.set(!1),r.clear()}));((e,t)=>{const o=(e,t)=>{Nr(e,Qs(),{raw:t})},n=()=>e.getMenu().bind(Sg.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(zr),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Sg.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(()=>{!e.isActive()||e.isProcessingAction()||t.queryCommandState("mceAutoCompleterInRange")||e.cancelIfNecessary()}))})({cancelIfNecessary:u,isMenuOpen:i,isActive:l,isProcessingAction:n.get,getMenu:d},e)},MC=(e,t,o)=>Tl(e,t,o).isSome(),DC=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},BC=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?A.none():A.some(t.touches[0])},FC=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=nn(),o=en(!1),n=DC((t=>{e.triggerEvent(fr(),t),o.set(!0)}),400),s=Fs([{key:Ps(),value:e=>(BC(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),A.none())},{key:Us(),value:e=>(n.cancel(),BC(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),A.none())},{key:Ws(),value:s=>(n.cancel(),t.get().filter((e=>Ze(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(hr(),s))))}]);return{fireIfReady:(e,t)=>fe(s,t).bind((t=>t(e)))}})(o),s=L(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>Bc(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=nn(),a=Bc(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(ur(),e)}),0))})),i=Bc(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===Cg[0]&&!I(["input","textarea"],Ue(e.target))&&!MC(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=Bc(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=nn(),d=Bc(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(dr(),e)}),0))}));return{unbind:()=>{V(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},IC=(e,t)=>{const o=fe(e,"target").getOr(t);return en(o)},RC=Ds([{stopped:[]},{resume:["element"]},{complete:[]}]),NC=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=en(!1),n=en(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),RC.complete())),(e=>{const o=e.descHandler;return Yi(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),RC.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),RC.complete()):rt(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),RC.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),RC.resume(n))))}))},zC=(e,t,o,n,s,r)=>NC(e,t,o,n,s,r).fold(E,(n=>zC(e,t,o,n,s,r)),T),LC=(e,t,o,n,s)=>{const r=IC(o,n);return zC(e,t,o,n,r,s)},VC=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{ie(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:C.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{ie(e,((e,o)=>{be(e,t)&&delete e[t]}))},filterByType:t=>fe(e,t).map((e=>ge(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>fe(e,o).bind((e=>Ns(n,(t=>((e,t)=>Li(t).bind((t=>fe(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{Li(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return Li(t).getOrThunk((()=>((e,t)=>{const o=Bi(Ri+"uid-");return zi(t,o),o})(0,e.element)))})(n);ve(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+ri(s.element)+"\nCannot use it for: "+ri(e.element)+"\nThe conflicting element is"+(vt(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>fe(t,e)}},HC=qm({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:qu(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[Ss("components",[]),Gu("containerBehaviours",[]),Ss("events",{}),Ss("domModification",{}),Ss("eventOrder",{})]}),PC=e=>{const t=t=>rt(e.element).fold(E,(e=>Ze(t,e))),o=VC(),n=(e,n)=>o.find(t,e,n),s=FC(e.element,{triggerEvent:(e,t)=>ci(e,t.target,(o=>((e,t,o,n)=>LC(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:x("real"),triggerEvent:(e,t,o)=>{ci(e,t,(s=>LC(n,e,o,t,s)))},triggerFocus:(e,t)=>{Li(e).fold((()=>{gc(e)}),(o=>{ci(cr(),e,(o=>(((e,t,o,n,s)=>{const r=IC(o,n);NC(e,t,o,n,r,s)})(n,cr(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:hl,buildOrPatch:pl,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:E},a=e=>{e.connect(r),Ge(e.element)||(o.register(e),V(e.components(),a),r.triggerEvent(yr(),e.element,{target:e.element}))},i=e=>{Ge(e.element)||(V(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{au(e,t)},c=e=>{cu(e)},d=e=>{const t=o.filter(mr());V(t,(t=>{const o=t.descHandler;Yi(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=en(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:T,event:e,setSource:O("Cannot set source of a broadcasted event"),getSource:O("Cannot get source of a broadcasted event")}})(t);return V(e,(e=>{const t=e.descHandler;Yi(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>ln.error(new Error('Could not find component with uid: "'+e+'" in system.'))),ln.value),h=e=>{const t=Li(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Ho(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},UC=x([Ss("prefix","form-field"),Gu("fieldBehaviours",[Qm,$u])]),WC=x([wm({schema:[rs("dom")],name:"label"}),wm({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[rs("text")],name:"aria-descriptor"}),ym({factory:{sketch:e=>{const t=((e,t)=>{const o={};return ie(e,((e,n)=>{I(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[rs("factory")],name:"field"})]),jC=Ym({name:"FormField",configFields:UC(),partFields:WC(),factory:(e,t,o,n)=>{const s=Yu(e.fieldBehaviours,[Qm.config({find:t=>Fm(t,e,"field")}),$u.config({store:{mode:"manual",getValue:e=>Qm.getCurrent(e).bind($u.getValue),setValue:(e,t)=>{Qm.getCurrent(e).each((e=>{$u.setValue(e,t)}))}}})]),r=Pr([Qr(((t,o)=>{const n=Rm(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=Bi(e.prefix);n.label().each((e=>{St(e.element,"for",o),St(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=Bi(e.prefix);St(o.element,"id",n),St(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>Fm(t,e,"field"),getLabel:t=>Fm(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var $C=tinymce.util.Tools.resolve("tinymce.html.Entities");const GC=(e,t,o,n)=>{const s=qC(e,t,o,n);return jC.sketch(s)},qC=(e,t,o,n)=>({dom:YC(o),components:e.toArray().concat([t]),fieldBehaviours:da(n)}),YC=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),XC=(e,t)=>jC.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ul(t.translate(e))]}),KC=Bi("form-component-change"),JC=Bi("form-close"),QC=Bi("form-cancel"),ZC=Bi("form-action"),ek=Bi("form-submit"),tk=Bi("form-block"),ok=Bi("form-unblock"),nk=Bi("form-tabchange"),sk=Bi("form-resize"),rk=(e,t,o)=>{const n=e.label.map((e=>XC(e,t))),s=t.icons(),r=e=>(t,o)=>{Tl(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,kt(n,"data-collection-item-value"))}))},a=r(((o,n,s,r)=>{n.stop(),t.isDisabled()||Nr(o,ZC,{name:e.name,value:r})})),i=[jr(Xs(),r(((e,t,o)=>{gc(o)}))),jr(or(),a),jr(hr(),a),jr(Ks(),r(((e,t,o)=>{_l(e.element,"."+by).each((e=>{Ca(e,by)})),wa(o,by)}))),jr(Js(),r((e=>{_l(e.element,"."+by).each((e=>{Ca(e,by),pc(e)}))}))),ta(r(((t,o,n,s)=>{Nr(t,ZC,{name:e.name,value:s})})))],l=(e,t)=>L(Od(e.element,".tox-collection__item"),t),c=jC.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:w},behaviours:da([mg.config({disabled:t.isDisabled,onDisabled:e=>{l(e,(e=>{wa(e,"tox-collection__item--state-disabled"),St(e,"aria-disabled",!0)}))},onEnabled:e=>{l(e,(e=>{Ca(e,"tox-collection__item--state-disabled"),Tt(e,"aria-disabled")}))}}),lw(),Th.config({}),Jb.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{_l(e.element,"."+by+"[data-mce-tooltip]").each((o=>{Ot(o,"data-mce-tooltip").each((o=>{Jb.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-keyboard-focus",anchor:e=>({type:"node",node:_l(e.element,"."+by).orThunk((()=>Qe(".tox-collection__item"))),root:e.element,layouts:{onLtr:x([ec,Zl,Xl,Jl,Kl,Ql]),onRtl:x([ec,Zl,Xl,Jl,Kl,Ql])},bubble:jc(0,-2,{})})}),$u.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const r=L(n,(o=>{const n=Yv.translate(o.text),r=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",a=`<div class="tox-collection__item-icon">${(e=>{var t;return null!==(t=s[e])&&void 0!==t?t:e})(o.icon)}</div>`,i={_:" "," - ":" ","-":" "},l=n.replace(/\_| \- |\-/g,(e=>i[e]));return`<div data-mce-tooltip="${l}" class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${$C.encodeAllRaw(o.value)}" aria-label="${l}">${a}${r}</div>`})),a="auto"!==e.columns&&e.columns>1?z(r,e.columns):[r],i=L(a,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));ni(o.element,i.join(""))})(o,n),"auto"===e.columns&&ow(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{vh.setGridSize(o,e,t)})),Rr(o,sk)}}),Hb.config({}),vh.config((d=e.columns,"normal",1===d?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===d?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${dy}`}})),Eh("collection-events",i)]),eventOrder:{[gr()]:["disabling","alloy.base.behaviour","collection-events"],[Ks()]:["collection-events","tooltipping"]}});var d;return GC(n,c,["tox-form__group--collection"],[])},ak=["input","textarea"],ik=e=>{const t=Ue(e);return I(ak,t)},lk=(e,t)=>{const o=t.getRoot(e).getOr(e.element);Ca(o,t.invalidClass),t.notify.each((t=>{ik(e.element)&&St(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{ni(e,t.validHtml)})),t.onValid(e)}))},ck=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);wa(s,t.invalidClass),t.notify.each((t=>{ik(e.element)&&St(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{ni(e,n)})),t.onInvalid(e,n)}))},dk=(e,t,o)=>t.validator.fold((()=>rC(ln.value(!0))),(t=>t.validate(e))),uk=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),dk(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(ck(e,t,0,o),ln.error(o))),(o=>(lk(e,t),ln.value(o)))):ln.error("No longer in system"))));var mk=Object.freeze({__proto__:null,markValid:lk,markInvalid:ck,query:dk,run:uk,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return ka(o,t.invalidClass)}}),gk=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Pr([jr(t.onEvent,(t=>{uk(t,e).get(w)}))].concat(t.validateOnLoad?[Qr((t=>{uk(t,e).get(b)}))]:[])))).getOr({})}),pk=[rs("invalidClass"),Ss("getRoot",A.none),ws("notify",[Ss("aria","alert"),Ss("getContainer",A.none),Ss("validHtml",""),vi("onValid"),vi("onInvalid"),vi("onValidate")]),ws("validator",[rs("validate"),Ss("onEvent","input"),Ss("validateOnLoad",!0)])];const hk=ma({fields:pk,name:"invalidating",active:gk,apis:mk,extra:{validation:e=>t=>{const o=$u.getValue(t);return rC(e(o))}}}),fk=ma({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Pr([Ur(ar(),E)]),exhibit:()=>sa({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),bk=Bi("color-input-change"),vk=Bi("color-swatch-change"),yk=Bi("color-picker-cancel"),xk=wm({schema:[rs("dom")],name:"label"}),wk=e=>wm({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Pr([$r(Ps(),((t,o,n)=>e(t,n)),[t]),$r($s(),((t,o,n)=>e(t,n)),[t]),$r(Gs(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),Sk=wk("top-left"),Ck=wk("top"),kk=wk("top-right"),Ok=wk("right"),_k=wk("bottom-right"),Tk=wk("bottom"),Ek=wk("bottom-left"),Ak=wk("left"),Mk=ym({name:"thumb",defaults:x({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Pr([qr(Ps(),e,"spectrum"),qr(Us(),e,"spectrum"),qr(Ws(),e,"spectrum"),qr($s(),e,"spectrum"),qr(Gs(),e,"spectrum"),qr(Ys(),e,"spectrum")])})}),Dk=e=>Lg(e.event);var Bk=[xk,Ak,Ok,Ck,Tk,Sk,kk,Ek,_k,Mk,ym({schema:[ns("mouseIsDown",(()=>en(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:da([vh.config({mode:"special",onLeft:(o,n)=>t.onLeft(o,e,Dk(n)),onRight:(o,n)=>t.onRight(o,e,Dk(n)),onUp:(o,n)=>t.onUp(o,e,Dk(n)),onDown:(o,n)=>t.onDown(o,e,Dk(n))}),Hb.config({}),Fh.config({})]),events:Pr([jr(Ps(),o),jr(Us(),o),jr($s(),o),jr(Gs(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const Fk=x("slider.change.value"),Ik=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?A.some(e.touches[0]).map((e=>$t(e.clientX,e.clientY))):A.none()}{const e=t;return void 0!==e.clientX?A.some(e).map((e=>$t(e.clientX,e.clientY))):A.none()}},Rk=e=>e.model.minX,Nk=e=>e.model.minY,zk=e=>e.model.minX-1,Lk=e=>e.model.minY-1,Vk=e=>e.model.maxX,Hk=e=>e.model.maxY,Pk=e=>e.model.maxX+1,Uk=e=>e.model.maxY+1,Wk=(e,t,o)=>t(e)-o(e),jk=e=>Wk(e,Vk,Rk),$k=e=>Wk(e,Hk,Nk),Gk=e=>jk(e)/2,qk=e=>$k(e)/2,Yk=(e,t)=>t?e.stepSize*e.speedMultiplier:e.stepSize,Xk=e=>e.snapToGrid,Kk=e=>e.snapStart,Jk=e=>e.rounded,Qk=(e,t)=>void 0!==e[t+"-edge"],Zk=e=>Qk(e,"left"),eO=e=>Qk(e,"right"),tO=e=>Qk(e,"top"),oO=e=>Qk(e,"bottom"),nO=e=>e.model.value.get(),sO=(e,t)=>({x:e,y:t}),rO=(e,t)=>{Nr(e,Fk(),{value:t})},aO=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),iO=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),lO=(e,t,o)=>Math.max(t,Math.min(o,e)),cO=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=lO(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return lO(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},dO=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},uO="top",mO="right",gO="bottom",pO="left",hO=e=>e.element.dom.getBoundingClientRect(),fO=(e,t)=>e[t],bO=e=>{const t=hO(e);return fO(t,pO)},vO=e=>{const t=hO(e);return fO(t,mO)},yO=e=>{const t=hO(e);return fO(t,uO)},xO=e=>{const t=hO(e);return fO(t,gO)},wO=e=>{const t=hO(e);return fO(t,"width")},SO=e=>{const t=hO(e);return fO(t,"height")},CO=(e,t,o)=>(e+t)/2-o,kO=(e,t)=>{const o=hO(e),n=hO(t),s=fO(o,pO),r=fO(o,mO),a=fO(n,pO);return CO(s,r,a)},OO=(e,t)=>{const o=hO(e),n=hO(t),s=fO(o,uO),r=fO(o,gO),a=fO(n,uO);return CO(s,r,a)},_O=(e,t)=>{Nr(e,Fk(),{value:t})},TO=(e,t,o)=>{const n={min:Rk(t),max:Vk(t),range:jk(t),value:o,step:Yk(t),snap:Xk(t),snapStart:Kk(t),rounded:Jk(t),hasMinEdge:Zk(t),hasMaxEdge:eO(t),minBound:bO(e),maxBound:vO(e),screenRange:wO(e)};return cO(n)},EO=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?iO:aO)(nO(o),Rk(o),Vk(o),Yk(o,n));return _O(t,s),A.some(s)})(e,t,o,n).map(E),AO=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=wO(e),a=n.bind((t=>A.some(kO(t,e)))).getOr(0),i=s.bind((t=>A.some(kO(t,e)))).getOr(r),l={min:Rk(t),max:Vk(t),range:jk(t),value:o,hasMinEdge:Zk(t),hasMaxEdge:eO(t),minBound:bO(e),minOffset:0,maxBound:vO(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return dO(l)})(t,r,o,n,s);return bO(t)-bO(e)+a},MO=EO(-1),DO=EO(1),BO=A.none,FO=A.none,IO={"top-left":A.none(),top:A.none(),"top-right":A.none(),right:A.some(((e,t)=>{rO(e,Pk(t))})),"bottom-right":A.none(),bottom:A.none(),"bottom-left":A.none(),left:A.some(((e,t)=>{rO(e,zk(t))}))};var RO=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=TO(e,t,o);return _O(e,n),n},setToMin:(e,t)=>{const o=Rk(t);_O(e,o)},setToMax:(e,t)=>{const o=Vk(t);_O(e,o)},findValueOfOffset:TO,getValueFromEvent:e=>Ik(e).map((e=>e.left)),findPositionOfValue:AO,setPositionFromValue:(e,t,o,n)=>{const s=nO(o),r=AO(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Kt(t.element)/2;Mt(t.element,"left",r-a+"px")},onLeft:MO,onRight:DO,onUp:BO,onDown:FO,edgeActions:IO});const NO=(e,t)=>{Nr(e,Fk(),{value:t})},zO=(e,t,o)=>{const n={min:Nk(t),max:Hk(t),range:$k(t),value:o,step:Yk(t),snap:Xk(t),snapStart:Kk(t),rounded:Jk(t),hasMinEdge:tO(t),hasMaxEdge:oO(t),minBound:yO(e),maxBound:xO(e),screenRange:SO(e)};return cO(n)},LO=e=>(t,o,n)=>((e,t,o,n)=>{const s=(e>0?iO:aO)(nO(o),Nk(o),Hk(o),Yk(o,n));return NO(t,s),A.some(s)})(e,t,o,n).map(E),VO=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=SO(e),a=n.bind((t=>A.some(OO(t,e)))).getOr(0),i=s.bind((t=>A.some(OO(t,e)))).getOr(r),l={min:Nk(t),max:Hk(t),range:$k(t),value:o,hasMinEdge:tO(t),hasMaxEdge:oO(t),minBound:yO(e),minOffset:0,maxBound:xO(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return dO(l)})(t,r,o,n,s);return yO(t)-yO(e)+a},HO=A.none,PO=A.none,UO=LO(-1),WO=LO(1),jO={"top-left":A.none(),top:A.some(((e,t)=>{rO(e,Lk(t))})),"top-right":A.none(),right:A.none(),"bottom-right":A.none(),bottom:A.some(((e,t)=>{rO(e,Uk(t))})),"bottom-left":A.none(),left:A.none()};var $O=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=zO(e,t,o);return NO(e,n),n},setToMin:(e,t)=>{const o=Nk(t);NO(e,o)},setToMax:(e,t)=>{const o=Hk(t);NO(e,o)},findValueOfOffset:zO,getValueFromEvent:e=>Ik(e).map((e=>e.top)),findPositionOfValue:VO,setPositionFromValue:(e,t,o,n)=>{const s=nO(o),r=VO(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=Ut(t.element)/2;Mt(t.element,"top",r-a+"px")},onLeft:HO,onRight:PO,onUp:UO,onDown:WO,edgeActions:jO});const GO=(e,t)=>{Nr(e,Fk(),{value:t})},qO=(e,t)=>({x:e,y:t}),YO=(e,t)=>(o,n,s)=>((e,t,o,n,s)=>{const r=e>0?iO:aO,a=t?nO(n).x:r(nO(n).x,Rk(n),Vk(n),Yk(n,s)),i=t?r(nO(n).y,Nk(n),Hk(n),Yk(n,s)):nO(n).y;return GO(o,qO(a,i)),A.some(a)})(e,t,o,n,s).map(E),XO=YO(-1,!1),KO=YO(1,!1),JO=YO(-1,!0),QO=YO(1,!0),ZO={"top-left":A.some(((e,t)=>{rO(e,sO(zk(t),Lk(t)))})),top:A.some(((e,t)=>{rO(e,sO(Gk(t),Lk(t)))})),"top-right":A.some(((e,t)=>{rO(e,sO(Pk(t),Lk(t)))})),right:A.some(((e,t)=>{rO(e,sO(Pk(t),qk(t)))})),"bottom-right":A.some(((e,t)=>{rO(e,sO(Pk(t),Uk(t)))})),bottom:A.some(((e,t)=>{rO(e,sO(Gk(t),Uk(t)))})),"bottom-left":A.some(((e,t)=>{rO(e,sO(zk(t),Uk(t)))})),left:A.some(((e,t)=>{rO(e,sO(zk(t),qk(t)))}))};var e_=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=TO(e,t,o.left),s=zO(e,t,o.top),r=qO(n,s);return GO(e,r),r},setToMin:(e,t)=>{const o=Rk(t),n=Nk(t);GO(e,qO(o,n))},setToMax:(e,t)=>{const o=Vk(t),n=Hk(t);GO(e,qO(o,n))},getValueFromEvent:e=>Ik(e),setPositionFromValue:(e,t,o,n)=>{const s=nO(o),r=AO(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=VO(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Kt(t.element)/2,l=Ut(t.element)/2;Mt(t.element,"left",r-i+"px"),Mt(t.element,"top",a-l+"px")},onLeft:XO,onRight:KO,onUp:JO,onDown:QO,edgeActions:ZO});const t_=Ym({name:"Slider",configFields:[Ss("stepSize",1),Ss("speedMultiplier",10),Ss("onChange",b),Ss("onChoose",b),Ss("onInit",b),Ss("onDragStart",b),Ss("onDragEnd",b),Ss("snapToGrid",!1),Ss("rounded",!0),ps("snapStart"),as("model",es("mode",{x:[Ss("minX",0),Ss("maxX",100),ns("value",(e=>en(e.mode.minX))),rs("getInitialValue"),Si("manager",RO)],y:[Ss("minY",0),Ss("maxY",100),ns("value",(e=>en(e.mode.minY))),rs("getInitialValue"),Si("manager",$O)],xy:[Ss("minX",0),Ss("maxX",100),Ss("minY",0),Ss("maxY",100),ns("value",(e=>en({x:e.mode.minX,y:e.mode.minY}))),rs("getInitialValue"),Si("manager",e_)]})),Gu("sliderBehaviours",[vh,$u]),ns("mouseIsDown",(()=>en(!1)))],partFields:Bk,factory:(e,t,o,n)=>{const s=t=>Im(t,e,"thumb"),r=t=>Im(t,e,"spectrum"),a=t=>Fm(t,e,"left-edge"),i=t=>Fm(t,e,"right-edge"),l=t=>Fm(t,e,"top-edge"),c=t=>Fm(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&Fm(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)},b=t=>{Fm(t,e,"spectrum").map(vh.focusIn)};return{uid:e.uid,dom:e.dom,components:t,behaviours:Yu(e.sliderBehaviours,[vh.config({mode:"special",focusIn:b}),$u.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),uc.config({channels:{[Eu()]:{onReceive:p}}})]),events:Pr([jr(Fk(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),A.some(!0)})(t,o.event.value)})),Qr(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),jr(Ps(),h),jr(Ws(),f),jr($s(),((e,t)=>{b(e),h(e,t)})),jr(Ys(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),o_=Bi("rgb-hex-update"),n_=Bi("slider-update"),s_=Bi("palette-update"),r_="form",a_=[Gu("formBehaviours",[$u])],i_=e=>"<alloy.field."+e+">",l_=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Yu(e.formBehaviours,[$u.config({store:{mode:"manual",getValue:t=>{const o=Nm(t,e);return le(o,((e,t)=>e().bind((e=>{return o=Qm.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+ri(e.element)),o.fold((()=>ln.error(n)),ln.value);var o,n})).map($u.getValue)))},setValue:(t,o)=>{ie(o,((o,n)=>{Fm(t,e,n).each((e=>{Qm.getCurrent(e).each((e=>{$u.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>Fm(t,e,o).bind(Qm.getCurrent)}}),c_={getField:Gi(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),Em(r_,i_(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=L(n,(e=>ym({name:e,pname:i_(e)})));return Wm(r_,a_,s,l_,o)}},d_=Bi("valid-input"),u_=Bi("invalid-input"),m_=Bi("validating-input"),g_="colorcustom.rgb.",p_={isEnabled:E,setEnabled:b,immediatelyShow:b,immediatelyHide:b},h_=(e,t,o,n,s,r)=>{const a=(e,t)=>{const o=t.get();e!==o.isEnabled()&&(o.setEnabled(e),e?o.immediatelyShow():o.immediatelyHide())},i=(o,n,s)=>hk.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Nr(e,m_,{type:o})},onValid:e=>{a(!1,s),Nr(e,d_,{type:o,value:$u.getValue(e)})},onInvalid:e=>{a(!0,s),Nr(e,u_,{type:o,value:$u.getValue(e)})}},validator:{validate:t=>{const o=$u.getValue(t),s=n(o)?ln.value(!0):ln.error(e("aria.input.invalid"));return rC(s)},validateOnLoad:!1}}),l=(o,n,a,l,c)=>{const d=en(p_),u=e(g_+"range"),m=jC.parts.label({dom:{tag:"label",attributes:{"aria-label":l}},components:[ul(a)]}),g=jC.parts.field({data:c,factory:Dy,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:da([i(n,o,d),Hb.config({}),Jb.config({...s({tooltipText:"",onSetup:e=>{d.set({isEnabled:()=>Jb.isEnabled(e),setEnabled:t=>Jb.setEnabled(e,t),immediatelyShow:()=>Jb.immediateOpenClose(e,!0),immediatelyHide:()=>Jb.immediateOpenClose(e,!1)}),Jb.setEnabled(e,!1)},onShow:(o,s)=>{Jb.setComponents(o,[{dom:{tag:"p",classes:[t("rgb-warning-note")]},components:[ul(e("hex"===n?"colorcustom.rgb.invalidHex":"colorcustom.rgb.invalid"))]}])}})})]),onSetValue:e=>{hk.isInvalid(e)&&hk.run(e).get(b)}}),p=Bi("aria-invalid"),h=zb(r("invalid",A.some(p),"warning")),f=[m,g,zb({dom:{tag:"div",classes:[t("invalid-icon")]},components:[h.asSpec()]}).asSpec()],v="hex"!==n?[jC.parts["aria-descriptor"]({text:u})]:[],y=f.concat(v);return{dom:{tag:"div",attributes:{role:"presentation"},classes:[t("rgb-container")]},components:y}},c=(e,t)=>{const o=t.red,n=t.green,s=t.blue;$u.setValue(e,{red:o,green:n,blue:s})},d=zb({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),u=(e,t)=>{d.getOpt(e).each((e=>{Mt(e.element,"background-color","#"+t.value)}))},m=qm({factory:()=>{const s={red:en(A.some(255)),green:en(A.some(255)),blue:en(A.some(255)),hex:en(A.some("ffffff"))},r=e=>s[e].get(),a=(e,t)=>{s[e].set(t)},i=e=>{const t=e.red,o=e.green,n=e.blue;a("red",A.some(t)),a("green",A.some(o)),a("blue",A.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?a(o.type,A.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=Fw(t);a("hex",A.some(n.value));const s=Yw(n);c(e,s),i(s),Nr(e,o_,{hex:n}),u(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);a(t,A.some(n)),r("red").bind((e=>r("green").bind((t=>r("blue").map((o=>$w(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=Vw(t);return c_.getField(e,"hex").each((t=>{Fh.isFocused(t)||$u.setValue(e,{hex:o.value})})),o})(e,t);Nr(e,o_,{hex:o}),u(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(g_+t+".label"),description:e(g_+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return yn(c_.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",jC.sketch(l(Gw,"red",h.label,h.description,255))),o.field("green",jC.sketch(l(Gw,"green",f.label,f.description,255))),o.field("blue",jC.sketch(l(Gw,"blue",b.label,b.description,255))),o.field("hex",jC.sketch(l(Nw,"hex",v.label,v.description,"ffffff"))),d.asSpec()],formBehaviours:da([hk.config({invalidClass:t("form-invalid")}),Eh("rgb-form-events",[jr(d_,g),jr(u_,m),jr(m_,m)])])}))),{apis:{updateHex:(e,t)=>{$u.setValue(e,{hex:t.value}),((e,t)=>{const o=Yw(t);c(e,o),i(o)})(e,t),u(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return m},f_=(e,t,o,n)=>{const s=qm({name:"ColourPicker",configFields:[rs("dom"),Ss("onValidHex",b),Ss("onInvalidHex",b)],factory:s=>{const r=h_(e,t,s.onValidHex,s.onInvalidHex,o,n),a=((e,t)=>{const o=t_.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=t_.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return qm({factory:r=>{const a=x({x:0,y:0}),i=da([Qm.config({find:A.some}),Fh.config({})]);return t_.sketch({dom:{tag:"div",attributes:{role:"slider","aria-valuetext":e(["Saturation {0}%, Brightness {1}%",0,0])},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:a},rounded:!1,components:[o,n],onChange:(t,o,n)=>{h(n)||St(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",Math.floor(n.x),Math.floor(100-n.y)])),Nr(t,s_,{value:n})},onInit:(e,t,o,n)=>{s(o.element.dom,Jw(Qw))},sliderBehaviours:i})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=mS(t,100,100),r=qw(n);s(o,Jw(r))})(t,o)},setThumb:(t,o,n)=>{((t,o)=>{const n=gS(Yw(o));t_.setValue(t,{x:n.saturation,y:100-n.value}),St(t.element,"aria-valuetext",e(["Saturation {0}%, Brightness {1}%",n.saturation,n.value]))})(o,n)}},extraApis:{}})})(e,t),i={paletteRgba:en(Qw),paletteHue:en(0)},l=zb(((e,t)=>{const o=t_.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=t_.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return t_.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"slider","aria-valuemin":0,"aria-valuemax":360,"aria-valuenow":120}},rounded:!1,model:{mode:"y",getInitialValue:x(0)},components:[o,n],sliderBehaviours:da([Fh.config({})]),onChange:(e,t,o)=>{St(e.element,"aria-valuenow",Math.floor(360-3.6*o)),Nr(e,n_,{value:o})}})})(0,t)),c=zb(a.sketch({})),d=zb(r.sketch({})),u=(e,t,o)=>{c.getOpt(e).each((e=>{a.setHue(e,o)}))},m=(e,t)=>{d.getOpt(e).each((e=>{r.updateHex(e,t)}))},g=(e,t,o)=>{l.getOpt(e).each((e=>{t_.setValue(e,(e=>100-e/360*100)(o))}))},p=(e,t)=>{c.getOpt(e).each((e=>{a.setThumb(e,t)}))},f=(e,t,o,n)=>{((e,t)=>{const o=Yw(e);i.paletteRgba.set(o),i.paletteHue.set(t)})(t,o),V(n,(n=>{n(e,t,o)}))};return{uid:s.uid,dom:s.dom,components:[c.asSpec(),l.asSpec(),d.asSpec()],behaviours:da([Eh("colour-picker-events",[jr(o_,(()=>{const e=[u,g,p];return(t,o)=>{const n=o.event.hex,s=(e=>gS(Yw(e)))(n);f(t,n,s.hue,e)}})()),jr(s_,(()=>{const e=[m];return(t,o)=>{const n=o.event.value,s=i.paletteHue.get(),r=mS(s,n.x,100-n.y),a=pS(r);f(t,a,s,e)}})()),jr(n_,(()=>{const e=[u,m];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=i.paletteRgba.get(),r=gS(s),a=mS(n,r.saturation,r.value),l=pS(a);f(t,l,n,e)}})())]),Qm.config({find:e=>d.getOpt(e)}),vh.config({mode:"acyclic"})])}}});return s},b_=()=>Qm.config({find:A.some}),v_=e=>Qm.config({find:t=>ct(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),y_=In([Ss("preprocess",w),Ss("postprocess",w)]),x_=(e,t)=>{const o=Qn("RepresentingConfigs.memento processors",y_,t);return $u.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=$u.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);$u.setValue(r,s)}}})},w_=(e,t,o)=>$u.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),S_=(e,t,o)=>w_(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),C_=e=>$u.config({store:{mode:"memory",initialValue:e}}),k_={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.rgb.invalid":"Numbers only, 0 to 255","colorcustom.rgb.invalidHex":"Hexadecimal only, 000000 to FFFFFF","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var O_=tinymce.util.Tools.resolve("tinymce.Resource");const __=e=>be(e,"init");var T_=tinymce.util.Tools.resolve("tinymce.util.Tools");const E_=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},A_=Bi("alloy-fake-before-tabstop"),M_=Bi("alloy-fake-after-tabstop"),D_=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:da([Fh.config({ignore:!0}),Hb.config({})])}),B_=(e,t)=>({dom:{tag:"div",classes:["tox-navobj",...e.getOr([])]},components:[D_([A_]),t,D_([M_])],behaviours:da([v_(1)])}),F_=(e,t)=>{Nr(e,Qs(),{raw:{which:9,shiftKey:t}})},I_=(e,t)=>{const o=t.element;ka(o,A_)?F_(e,!0):ka(o,M_)&&F_(e,!1)},R_=e=>MC(e,["."+A_,"."+M_].join(","),T),N_=Bi("update-dialog"),z_=Bi("update-title"),L_=Bi("update-body"),V_=Bi("update-footer"),H_=Bi("body-send-message"),P_=Bi("dialog-focus-shifted"),U_=Mo().browser,W_=U_.isSafari(),j_=U_.isFirefox(),$_=W_||j_,G_=U_.isChromium(),q_=({scrollTop:e,scrollHeight:t,clientHeight:o})=>Math.ceil(e)+o>=t,Y_=(e,t)=>e.scrollTo(0,"bottom"===t?99999999:t),X_=(e,t,o)=>{const n=e.dom;A.from(n.contentDocument).fold(o,(e=>{let o=0;const s=((e,t)=>{const o=e.body;return A.from(!/^<!DOCTYPE (html|HTML)/.test(t)&&(!G_&&!W_||g(o)&&(0!==o.scrollTop||Math.abs(o.scrollHeight-o.clientHeight)>1))?o:e.documentElement)})(e,t).map((e=>(o=e.scrollTop,e))).forall(q_),r=()=>{const e=n.contentWindow;g(e)&&(s?Y_(e,"bottom"):!s&&$_&&0!==o&&Y_(e,o))};W_&&n.addEventListener("load",r,{once:!0}),e.open(),e.write(t),e.close(),W_||r()}))},K_=Ce($_,W_?500:200).map((e=>((e,t)=>{let o=null,n=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null,n=null)},throttle:(...s)=>{n=s,c(o)&&(o=setTimeout((()=>{const t=n;o=null,n=null,e.apply(null,t)}),t))}}})(X_,e))),J_=Bi("toolbar.button.execute"),Q_=Bi("common-button-display-events"),Z_={[gr()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events","tooltipping"],[kr()]:["toolbar-button-events",Q_],[Or()]:["toolbar-button-events","dropdown-events","tooltipping"],[$s()]:["focusing","alloy.base.behaviour",Q_]},eT=e=>Mt(e.element,"width",Ft(e.element,"width")),tT=(e,t,o)=>ny(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),oT=(e,t)=>tT(e,t,[]),nT=(e,t)=>tT(e,t,[Th.config({})]),sT=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[ul(o.translate(e))],behaviours:da([Th.config({})])}),rT=Bi("update-menu-text"),aT=Bi("update-menu-icon"),iT=(e,t,o,n)=>{const s=en(b),r=e.text.map((e=>zb(sT(e,t,o.providers)))),a=e.icon.map((e=>zb(nT(e,o.providers.icons)))),i=(e,t)=>{const o=$u.getValue(e);return Fh.focus(o),Nr(o,"keydown",{raw:t.event.raw}),yC.close(o),A.some(!0)},l=e.role.fold((()=>({})),(e=>({role:e}))),c=A.from(e.listRole).map((e=>({listRole:e}))).getOr({}),d=e.ariaLabel.fold((()=>({})),(e=>({"aria-label":o.providers.translate(e)}))),u=ny("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),m=Bi("common-button-display-events"),p="dropdown-events",h=zb(yC.sketch({...e.uid?{uid:e.uid}:{},...l,...c,dom:{tag:"button",classes:[t,`${t}--select`].concat(L(e.classes,(e=>`${t}--${e}`))),attributes:{...d,...g(n)?{"data-mce-name":n}:{}}},components:bw([a.map((e=>e.asSpec())),r.map((e=>e.asSpec())),A.some(u)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{Ny(e).each((e=>Fh.focus(e)))})(n)},dropdownBehaviours:da([...e.dropdownBehaviours,cw((()=>e.disabled||o.providers.isDisabled())),lw(),fk.config({}),Th.config({}),...e.tooltip.map((e=>Jb.config(o.providers.tooltips.getConfig({tooltipText:o.providers.translate(e)})))).toArray(),Eh(p,[gw(e,s),pw(e,s)]),Eh(m,[Qr(((t,o)=>"listbox"===e.listRole?b:eT(t)))]),Eh("menubutton-update-display-text",[jr(rT,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Th.set(e,[ul(o.providers.translate(t.event.text))])}))})),jr(aT,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{Th.set(e,[nT(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:yn(Z_,{[$s()]:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"],[kr()]:["toolbar-button-events",Jb.name(),p,m]}),sandboxBehaviours:da([vh.config({mode:"special",onLeft:i,onRight:i}),Eh("dropdown-sandbox-events",[jr(By,((e,t)=>{(e=>{const t=$u.getValue(e),o=Ry(e).map(zy);yC.refetch(t).get((()=>{const e=ZS.getCoupled(t,"sandbox");o.each((t=>Ry(e).each((e=>((e,t)=>{$u.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),jr(Fy,((e,t)=>{((e,t)=>{(e=>Ou.getState(e).bind(Sg.getHighlighted).bind(Sg.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const s={...n,target:t};e.getSystem().triggerEvent(o,t,s)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{..._y(0,e.columns,e.presets),fakeFocus:e.searchable,..."listbox"===e.listRole?{}:{onHighlightItem:xC,onCollapseMenu:(e,t,o)=>{Sg.getHighlighted(o).each((t=>{xC(e,o,t)}))},onDehighlightItem:wC}}},getAnchorOverrides:()=>({maxHeightFunction:(e,t)=>{Vc()(e,t-10)}}),fetch:t=>sC(C(e.fetch,t))}));return h.asSpec()},lT=e=>"separator"===e.type,cT={type:"separator"},dT=(e,t)=>{const o=((e,t)=>{const o=W(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!lT(e[e.length-1])?e.concat([cT]):e:be(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&lT(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return U(o,((e,o)=>{if((e=>be(e,"getSubmenuItems"))(o)){const n=(e=>{const t=fe(e,"value").getOrThunk((()=>Bi("generated-menu-item")));return yn({value:t},e)})(o),s=((e,t)=>{const o=e.getSubmenuItems(),n=dT(o,t);return{item:e,menus:yn(n.menus,{[e.value]:n.items}),expansions:yn(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:yn(e.menus,s.menus),items:[s.item,...e.items],expansions:yn(e.expansions,s.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},uT=(e,t,o,n)=>{const s=Bi("primary-menu"),r=dT(e,o.shared.providers.menuItems());if(0===r.items.length)return A.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=_C(s,r.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=le(r.menus,((e,n)=>_C(n,e,t,o,!1,l))),d=yn(c,Bs(s,i));return A.from(vf.tieredData(s,d,r.expansions))},mT=e=>!be(e,"items"),gT="data-value",pT=(e,t,o,n,s)=>L(o,(o=>mT(o)?{type:"togglemenuitem",...s?{}:{role:"option"},text:o.text,value:o.value,active:o.value===n,onAction:()=>{$u.setValue(e,o.value),Nr(e,KC,{name:t}),Fh.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>pT(e,t,o.items,n,s)})),hT=(e,t)=>se(e,(e=>mT(e)?Ce(e.value===t,e):hT(e.items,t))),fT=qm({name:"HtmlSelect",configFields:[rs("options"),Gu("selectBehaviours",[Fh,$u]),Ss("selectClasses",[]),Ss("selectAttributes",{}),ps("data")],factory:(e,t)=>{const o=L(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>Bs("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:Yu(e.selectBehaviours,[Fh.config({}),$u.config({store:{mode:"manual",getValue:e=>ol(e.element),setValue:(t,o)=>{const n=te(e.options);j(e.options,(e=>e.value===o)).isSome()?nl(t.element,o):-1===t.element.dom.selectedIndex&&""===o&&n.each((e=>nl(t.element,e.value)))},...n}})])}}}),bT=x([Ss("field1Name","field1"),Ss("field2Name","field2"),xi("onLockedChange"),fi(["lockClass"]),Ss("locked",!1),Xu("coupledFieldBehaviours",[Qm,$u])]),vT=(e,t)=>ym({factory:jC,name:e,overrides:e=>({fieldBehaviours:da([Eh("coupled-input-behaviour",[jr(er(),(o=>{((e,t,o)=>Fm(e,t,o).bind(Qm.getCurrent))(o,e,t).each((t=>{Fm(o,e,"lock").each((n=>{Ph.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),yT=x([vT("field1","field2"),vT("field2","field1"),ym({factory:Rb,schema:[rs("dom")],name:"lock",overrides:e=>({buttonBehaviours:da([Ph.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),xT=Ym({name:"FormCoupledInputs",configFields:bT(),partFields:yT(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Ku(e.coupledFieldBehaviours,[Qm.config({find:A.some}),$u.config({store:{mode:"manual",getValue:t=>{const o=Lm(t,e,["field1","field2"]);return{[e.field1Name]:$u.getValue(o.field1()),[e.field2Name]:$u.getValue(o.field2())}},setValue:(t,o)=>{const n=Lm(t,e,["field1","field2"]);ve(o,e.field1Name)&&$u.setValue(n.field1(),o[e.field1Name]),ve(o,e.field2Name)&&$u.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>Fm(t,e,"field1"),getField2:t=>Fm(t,e,"field2"),getLock:t=>Fm(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),wT=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return ln.value({value:e,unit:o})}return ln.error(e)},ST=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>be(o,e);return e.unit===t?A.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?A.some(e.value):A.some(e.value/o[e.unit]*o[t]):A.none()},CT=e=>A.none(),kT=(e,t)=>{const o=e.label.map((e=>XC(e,t))),n=[mg.config({disabled:()=>e.disabled||t.isDisabled()}),lw(),vh.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(Rr(e,ek),A.some(!0))}),Eh("textfield-change",[jr(er(),((t,o)=>{Nr(t,KC,{name:e.name})})),jr(ur(),((t,o)=>{Nr(t,KC,{name:e.name})}))]),Hb.config({})],s=e.validation.map((e=>hk.config({getRoot:e=>at(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=$u.getValue(t),n=e.validator(o);return rC(!0===n?ln.value(o):ln.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(x({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(x({}),(e=>({inputmode:e}))),"data-mce-name":e.name},a=jC.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:da(G([n,s])),selectOnFocus:!1,factory:Dy}),i=e.multiline?{dom:{tag:"div",classes:["tox-textarea-wrap"]},components:[a]}:a,l=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),c=[mg.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{jC.getField(e).each(mg.disable)},onEnabled:e=>{jC.getField(e).each(mg.enable)}}),lw()];return GC(o,i,l,c)},OT=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),_T=e=>e.dimension.property,TT=(e,t)=>e.dimension.getDimension(t),ET=(e,t)=>{const o=OT(e,t);_a(o,[t.shrinkingClass,t.growingClass])},AT=(e,t)=>{Ca(e.element,t.openClass),wa(e.element,t.closedClass),Mt(e.element,_T(t),"0px"),Vt(e.element)},MT=(e,t)=>{Ca(e.element,t.closedClass),wa(e.element,t.openClass),Lt(e.element,_T(t))},DT=(e,t,o,n)=>{o.setCollapsed(),Mt(e.element,_T(t),TT(t,e.element)),ET(e,t),AT(e,t),t.onStartShrink(e),t.onShrunk(e)},BT=(e,t,o,n)=>{const s=n.getOrThunk((()=>TT(t,e.element)));o.setCollapsed(),Mt(e.element,_T(t),s),Vt(e.element);const r=OT(e,t);Ca(r,t.growingClass),wa(r,t.shrinkingClass),AT(e,t),t.onStartShrink(e)},FT=(e,t,o)=>{const n=TT(t,e.element);("0px"===n?DT:BT)(e,t,o,A.some(n))},IT=(e,t,o)=>{const n=OT(e,t),s=ka(n,t.shrinkingClass),r=TT(t,e.element);MT(e,t);const a=TT(t,e.element);(s?()=>{Mt(e.element,_T(t),r),Vt(e.element)}:()=>{AT(e,t)})(),Ca(n,t.shrinkingClass),wa(n,t.growingClass),MT(e,t),Mt(e.element,_T(t),a),o.setExpanded(),t.onStartGrow(e)},RT=(e,t,o)=>{const n=OT(e,t);return!0===ka(n,t.growingClass)},NT=(e,t,o)=>{const n=OT(e,t);return!0===ka(n,t.shrinkingClass)};var zT=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){Lt(e.element,_T(t));const o=TT(t,e.element);Mt(e.element,_T(t),o)}},grow:(e,t,o)=>{o.isExpanded()||IT(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&FT(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&DT(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:RT,isShrinking:NT,isTransitioning:(e,t,o)=>RT(e,t)||NT(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?FT:IT)(e,t,o)},disableTransitions:ET,immediateGrow:(e,t,o)=>{o.isExpanded()||(MT(e,t),Mt(e.element,_T(t),TT(t,e.element)),ET(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),LT=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return sa(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Bs(t.dimension.property,"0px")})},events:(e,t)=>Pr([Jr(sr(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(ET(o,e),t.isExpanded()&&Lt(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),VT=[rs("closedClass"),rs("openClass"),rs("shrinkingClass"),rs("growingClass"),ps("getAnimationRoot"),vi("onShrunk"),vi("onStartShrink"),vi("onGrown"),vi("onStartGrow"),Ss("expanded",!1),as("dimension",es("property",{width:[Si("property","width"),Si("getDimension",(e=>Kt(e)+"px"))],height:[Si("property","height"),Si("getDimension",(e=>Ut(e)+"px"))]}))];const HT=ma({fields:VT,name:"sliding",active:LT,apis:zT,state:Object.freeze({__proto__:null,init:e=>{const t=en(e.expanded);return ca({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:C(t.set,!1),setExpanded:C(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),PT=e=>({isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t),setActive:t=>{const o=e.element;t?(wa(o,"tox-tbtn--enabled"),St(o,"aria-pressed",!0)):(Ca(o,"tox-tbtn--enabled"),Tt(o,"aria-pressed"))},isActive:()=>ka(e.element,"tox-tbtn--enabled"),setText:t=>{Nr(e,rT,{text:t})},setIcon:t=>Nr(e,aT,{icon:t})}),UT=(e,t,o,n,s=!0,r)=>iT({text:e.text,icon:e.icon,tooltip:e.tooltip,ariaLabel:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const s={pattern:e.search.isSome()?SC(t):""};e.fetch((t=>{n(uT(t,ly.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),s,PT(t))},onSetup:e.onSetup,getApi:PT,columns:1,presets:"normal",classes:[],dropdownBehaviours:[...s?[Hb.config({})]:[]]},t,o.shared,r),WT=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{gc(t.element),Nr(t,ZC,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(L(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:s(e)}})))}},jT=e=>({dom:{tag:"span",classes:["tox-tree__label"],attributes:{"aria-label":e}},components:[ul(e)]}),$T=Bi("leaf-label-event-id"),GT=({leaf:e,onLeafAction:t,visible:o,treeId:n,selectedId:s,backstage:r})=>{const a=e.menu.map((e=>UT(e,"tox-mbtn",r,A.none(),o))),i=[jT(e.title)];return a.each((e=>i.push(e))),Rb.sketch({dom:{tag:"div",classes:["tox-tree--leaf__label","tox-trbtn"].concat(o?["tox-tree--leaf__label--visible"]:[])},components:i,role:"treeitem",action:o=>{t(e.id),o.getSystem().broadcastOn([`update-active-item-${n}`],{value:e.id})},eventOrder:{[Qs()]:[$T,"keying"]},buttonBehaviours:da([...o?[Hb.config({})]:[],Ph.config({toggleClass:"tox-trbtn--enabled",toggleOnExecute:!1,aria:{mode:"selected"}}),uc.config({channels:{[`update-active-item-${n}`]:{onReceive:(t,o)=>{(o.value===e.id?Ph.on:Ph.off)(t)}}}}),Eh($T,[Qr(((t,o)=>{s.each((o=>{(o===e.id?Ph.on:Ph.off)(t)}))})),jr(Qs(),((e,t)=>{const o="ArrowLeft"===t.event.raw.code,n="ArrowRight"===t.event.raw.code;o?(Cl(e.element,".tox-tree--directory").each((t=>{e.getSystem().getByDom(t).each((e=>{Ol(t,".tox-tree--directory__label").each((t=>{e.getSystem().getByDom(t).each(Fh.focus)}))}))})),t.stop()):n&&t.stop()}))])])})},qT=Bi("directory-label-event-id"),YT=({directory:e,visible:t,noChildren:o,backstage:n})=>{const s=e.menu.map((e=>UT(e,"tox-mbtn",n,A.none()))),r=[{dom:{tag:"div",classes:["tox-chevron"]},components:[(a="chevron-right",i=n.shared.providers.icons,((e,t,o)=>ny(e,{tag:"span",classes:["tox-tree__icon-wrap","tox-icon"],behaviours:[]},t))(a,i))]},jT(e.title)];var a,i;s.each((e=>{r.push(e)}));const l=t=>{Cl(t.element,".tox-tree--directory").each((o=>{t.getSystem().getByDom(o).each((o=>{const n=!Ph.isOn(o);Ph.toggle(o),Nr(t,"expand-tree-node",{expanded:n,node:e.id})}))}))};return Rb.sketch({dom:{tag:"div",classes:["tox-tree--directory__label","tox-trbtn"].concat(t?["tox-tree--directory__label--visible"]:[])},components:r,action:l,eventOrder:{[Qs()]:[qT,"keying"]},buttonBehaviours:da([...t?[Hb.config({})]:[],Eh(qT,[jr(Qs(),((e,t)=>{const n="ArrowRight"===t.event.raw.code,s="ArrowLeft"===t.event.raw.code;n&&o&&t.stop(),(n||s)&&Cl(e.element,".tox-tree--directory").each((o=>{e.getSystem().getByDom(o).each((o=>{!Ph.isOn(o)&&n||Ph.isOn(o)&&s?(l(e),t.stop()):s&&!Ph.isOn(o)&&(Cl(o.element,".tox-tree--directory").each((e=>{Ol(e,".tox-tree--directory__label").each((e=>{o.getSystem().getByDom(e).each(Fh.focus)}))})),t.stop())}))}))}))])])})},XT=({children:e,onLeafAction:t,visible:o,treeId:n,expandedIds:s,selectedId:r,backstage:a})=>({dom:{tag:"div",classes:["tox-tree--directory__children"]},components:e.map((e=>"leaf"===e.type?GT({leaf:e,selectedId:r,onLeafAction:t,visible:o,treeId:n,backstage:a}):JT({directory:e,expandedIds:s,selectedId:r,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:a}))),behaviours:da([HT.config({dimension:{property:"height"},closedClass:"tox-tree--directory__children--closed",openClass:"tox-tree--directory__children--open",growingClass:"tox-tree--directory__children--growing",shrinkingClass:"tox-tree--directory__children--shrinking",expanded:o}),Th.config({})])}),KT=Bi("directory-event-id"),JT=({directory:e,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:s,expandedIds:r,selectedId:a})=>{const{children:i}=e,l=en(r),c=r.includes(e.id);return{dom:{tag:"div",classes:["tox-tree--directory"],attributes:{role:"treeitem"}},components:[YT({directory:e,visible:o,noChildren:0===e.children.length,backstage:s}),XT({children:i,expandedIds:r,selectedId:a,onLeafAction:t,visible:c,treeId:n,backstage:s})],behaviours:da([Eh(KT,[Qr(((e,t)=>{Ph.set(e,c)})),jr("expand-tree-node",((e,t)=>{const{expanded:o,node:n}=t.event;l.set(o?[...l.get(),n]:l.get().filter((e=>e!==n)))}))]),Ph.config({...e.children.length>0?{aria:{mode:"expanded"}}:{},toggleClass:"tox-tree--directory--expanded",onToggled:(e,o)=>{const r=e.components()[1],c=(d=o,i.map((e=>"leaf"===e.type?GT({leaf:e,selectedId:a,onLeafAction:t,visible:d,treeId:n,backstage:s}):JT({directory:e,expandedIds:l.get(),selectedId:a,onLeafAction:t,labelTabstopping:d,treeId:n,backstage:s}))));var d;o?HT.grow(r):HT.shrink(r),Th.set(r,c)}})])}},QT=Bi("tree-event-id");var ZT=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Pr([jr(e.event,o),Zr((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[jr(e,(()=>t.cancel()))])).getOr([])))}});const eE=e=>{const t=en(null);return ca({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var tE=Object.freeze({__proto__:null,throttle:eE,init:e=>e.stream.streams.state(e)}),oE=[as("stream",es("mode",{throttle:[rs("delay"),Ss("stopEvent",!0),Si("streams",{setup:(e,t)=>{const o=e.stream,n=E_(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:eE})]})),Ss("event","input"),ps("cancelEvent"),xi("onStream")];const nE=ma({fields:oE,name:"streaming",active:ZT,state:tE}),sE=(e,t,o)=>{const n=$u.getValue(o);$u.setValue(t,n),aE(t)},rE=(e,t)=>{const o=e.element,n=ol(o),s=o.dom;"number"!==kt(o,"type")&&t(s,n)},aE=e=>{rE(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},iE=x("alloy.typeahead.itemexecute"),lE=x([ps("lazySink"),rs("fetch"),Ss("minChars",5),Ss("responseTime",1e3),vi("onOpen"),Ss("getHotspot",A.some),Ss("getAnchorOverrides",x({})),Ss("layouts",A.none()),Ss("eventOrder",{}),Ms("model",{},[Ss("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),Ss("selectsOver",!0),Ss("populateFromBrowse",!0)]),vi("onSetValue"),yi("onExecute"),vi("onItemExecute"),Ss("inputClasses",[]),Ss("inputAttributes",{}),Ss("inputStyles",{}),Ss("matchWidth",!0),Ss("useMinWidth",!1),Ss("dismissOnBlur",!0),fi(["openClass"]),ps("initialData"),ps("listRole"),Gu("typeaheadBehaviours",[Fh,$u,nE,vh,Ph,ZS]),ns("lazyTypeaheadComp",(()=>en(A.none))),ns("previewing",(()=>en(!0)))].concat(Ty()).concat(fC())),cE=x([xm({schema:[hi()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=$u.getValue(t),s=e.getDisplayText(n),r=$u.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?A.some((()=>{sE(0,t,o),((e,t)=>{rE(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):A.none()}return A.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Sg.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&sE(e.model,t,n),Ot(n.element,"id").each((e=>St(t.element,"aria-activedescendant",e)))}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Nr(e,iE(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&sE(e.model,t,o)}))}})})]),dE=Ym({name:"Typeahead",configFields:lE(),partFields:cE(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=ZS.getCoupled(t,"sandbox");if(Ou.isOpen(r))Qm.getCurrent(r).each((e=>{Sg.getHighlighted(e).fold((()=>{s(e)}),(()=>{Hr(r,e.element,"keydown",o)}))}));else{const o=e=>{Qm.getCurrent(e).each(s)};cC(e,a(t),t,r,n,o,ff.HighlightMenuAndItem).get(b)}},r=Ey(e),a=e=>t=>t.map((t=>{const o=he(t.menus),n=q(o,(e=>P(e.items,(e=>"item"===e.type))));return $u.getState(e).update(L(n,(e=>e.data))),t})),i=e=>Qm.getCurrent(e),l="typeaheadevents",c=[Fh.config({}),$u.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>ol(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{nl(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>Bs("initialValue",e))).getOr({})}}),nE.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=ZS.getCoupled(t,"sandbox");if(Fh.isFocused(t)&&ol(t.element).length>=e.minChars){const o=i(s).bind((e=>Sg.getHighlighted(e).map($u.getValue)));e.previewing.set(!0);const r=t=>{i(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Sg.highlightFirst(t)}),(e=>{Sg.highlightBy(t,(t=>$u.getValue(t).value===e.value)),Sg.getHighlighted(t).orThunk((()=>(Sg.highlightFirst(t),A.none())))}))}))};cC(e,a(t),t,s,n,r,ff.HighlightJustMenu).get(b)}},cancelEvent:vr()}),vh.config({mode:"special",onDown:(e,t)=>(s(e,t,Sg.highlightFirst),A.some(!0)),onEscape:e=>{const t=ZS.getCoupled(e,"sandbox");return Ou.isOpen(t)?(Ou.close(t),A.some(!0)):A.none()},onUp:(e,t)=>(s(e,t,Sg.highlightLast),A.some(!0)),onEnter:t=>{const o=ZS.getCoupled(t,"sandbox"),n=Ou.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Sg.getHighlighted(e))).map((e=>(Nr(t,iE(),{item:e}),!0)));{const s=$u.getValue(t);return Rr(t,vr()),e.onExecute(o,t,s),n&&Ou.close(o),A.some(!0)}}}),Ph.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),ZS.config({others:{sandbox:t=>pC(e,t,{onOpen:()=>Ph.on(t),onClose:()=>{e.lazyTypeaheadComp.get().each((e=>Tt(e.element,"aria-activedescendant"))),Ph.off(t)}})}}),Eh(l,[Qr((t=>{e.lazyTypeaheadComp.set(A.some(t))})),Zr((t=>{e.lazyTypeaheadComp.set(A.none())})),ta((t=>{const o=b;uC(e,a(t),t,n,o,ff.HighlightMenuAndItem).get(b)})),jr(iE(),((t,o)=>{const n=ZS.getCoupled(t,"sandbox");sE(e.model,t,o.event.item),Rr(t,vr()),e.onItemExecute(t,n,o.event.item,$u.getValue(t)),Ou.close(n),aE(t)}))].concat(e.dismissOnBlur?[jr(dr(),(e=>{const t=ZS.getCoupled(e,"sandbox");bc(t.element).isNone()&&Ou.close(t)}))]:[]))],d={[Or()]:[$u.name(),nE.name(),l],...e.eventOrder};return{uid:e.uid,dom:My(yn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...Yu(e.typeaheadBehaviours,c)},eventOrder:d}}}),uE=e=>({...e,toCached:()=>uE(e.toCached()),bindFuture:t=>uE(e.bind((e=>e.fold((e=>rC(ln.error(e))),(e=>t(e)))))),bindResult:t=>uE(e.map((e=>e.bind(t)))),mapResult:t=>uE(e.map((e=>e.map(t)))),mapError:t=>uE(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>uE(sC((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(ln.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),mE=e=>uE(sC(e)),gE=(e,t,o=[],n,s,r,a)=>{const i=t.fold((()=>({})),(e=>({action:e}))),l={buttonBehaviours:da([cw((()=>!e.enabled||a.isDisabled())),lw(),Hb.config({}),...r.map((e=>Jb.config(a.tooltips.getConfig({tooltipText:a.translate(e)})))).toArray(),Eh("button press",[Wr("click")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...i},c=yn(l,{dom:n});return yn(c,{components:s})},pE=(e,t,o,n=[],s)=>{const r={tag:"button",classes:["tox-tbtn"],attributes:{...e.tooltip.map((e=>({"aria-label":o.translate(e)}))).getOr({}),"data-mce-name":s}},a=e.icon.map((e=>oT(e,o.icons))),i=bw([a]);return gE(e,t,n,r,i,e.tooltip,o)},hE=e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}},fE=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>oT(e,o.icons))),i=[a.getOrThunk((()=>ul(r)))],l=e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary"),c={tag:"button",classes:[...hE(l),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s],attributes:{"aria-label":r,"data-mce-name":e.text}},d=e.icon.map(x(r));return gE(e,t,n,c,i,d,o)},bE=(e,t,o,n=[],s=[])=>{const r=fE(e,A.some(t),o,n,s);return Rb.sketch(r)},vE=(e,t)=>o=>{"custom"===t?Nr(o,ZC,{name:e,value:{}}):"submit"===t?Rr(o,ek):"cancel"===t?Rr(o,QC):console.error("Unknown button type: ",t)},yE=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,type:"menubutton",search:A.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:WT(n.items,t,o)},r=zb(UT(s,"tox-tbtn",o,A.none(),!0,e.text.or(e.tooltip).getOrUndefined()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=vE(e.name,t),s={...e,borderless:!1};return bE(s,n,o.shared.providers,[])}if(((e,t)=>"togglebutton"===t)(0,t))return((e,t,o)=>{var n,s;const r=e.icon.map((e=>nT(e,t.icons))).map(zb),a=e.buttonType.getOr(e.primary?"primary":"secondary"),i={...e,name:null!==(n=e.name)&&void 0!==n?n:"",primary:"primary"===a,tooltip:e.tooltip,enabled:null!==(s=e.enabled)&&void 0!==s&&s,borderless:!1},l=i.tooltip.or(e.text).map((e=>({"aria-label":t.translate(e)}))).getOr({}),c=hE(null!=a?a:"secondary"),d=e.icon.isSome()&&e.text.isSome(),u={tag:"button",classes:[...c.concat(e.icon.isSome()?["tox-button--icon"]:[]),...e.active?["tox-button--enabled"]:[],...d?["tox-button--icon-and-text"]:[]],attributes:{...l,...g(o)?{"data-mce-name":o}:{}}},m=t.translate(e.text.getOr("")),p=ul(m),h=[...bw([r.map((e=>e.asSpec()))]),...e.text.isSome()?[p]:[]],f=gE(i,A.some((o=>{Nr(o,ZC,{name:e.name,value:{setIcon:e=>{r.map((n=>n.getOpt(o).each((o=>{Th.set(o,[nT(e,t.icons)])}))))}}})})),[],u,h,e.tooltip,t);return Rb.sketch(f)})(e,o.shared.providers,e.text.or(e.tooltip).getOrUndefined());throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},xE={type:"separator"},wE=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),SE=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),CE=(e,t)=>(e=>L(e,wE))(((e,t)=>P(t,(t=>t.type===e)))(e,t)),kE=e=>CE("header",e.targets),OE=e=>CE("anchor",e.targets),_E=e=>A.from(e.anchorTop).map((e=>SE("<top>",e))).toArray(),TE=e=>A.from(e.anchorBottom).map((e=>SE("<bottom>",e))).toArray(),EE=(e,t)=>{const o=e.toLowerCase();return P(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,s=null!==(t=e.value)&&void 0!==t?t:"";return _e(n.toLowerCase(),o)||_e(s.toLowerCase(),o)}))},AE=Bi("aria-invalid"),ME=(e,t)=>{e.dom.checked=t},DE=e=>e.dom.checked,BE=e=>(t,o,n,s,r)=>fe(o,"name").fold((()=>e(o,s,A.none(),r)),(a=>t.field(a,e(o,s,fe(n,a),r)))),FE={bar:BE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:L(e.items,t.interpreter)}))(e,t.shared))),collection:BE(((e,t,o)=>rk(e,t.shared.providers,o))),alertbanner:BE(((e,t)=>((e,t)=>{const o=ey(e.icon,t.icons);return HC.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:e.url?void 0:o},components:e.url?[Rb.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:o,attributes:{title:t.translate(e.iconTooltip)}},action:t=>Nr(t,ZC,{name:"alert-banner",value:e.url}),buttonBehaviours:da([ty()])})]:void 0},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]})})(e,t.shared.providers))),input:BE(((e,t,o)=>((e,t,o)=>kT({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:BE(((e,t,o)=>((e,t,o)=>kT({name:e.name,multiline:!0,label:e.label,inputMode:A.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:BE(((e,t,o,n)=>((e,t,o)=>{const n="tox-label",s="center"===e.align?[`${n}--center`]:[],r="end"===e.align?[`${n}--end`]:[],a=zb({dom:{tag:"label",classes:[n,...s,...r]},components:[ul(t.providers.translate(e.label))]}),i=L(e.items,t.interpreter);return{dom:{tag:"div",classes:["tox-form__group"]},components:[a.asSpec(),...i],behaviours:da([b_(),Th.config({}),(l=A.none(),S_(l,oi,ni)),vh.config({mode:"acyclic"}),Eh("label",[Qr((t=>{e.for.each((e=>{o(e).each((e=>{a.getOpt(t).each((t=>{var o;const n=null!==(o=kt(e.element,"id"))&&void 0!==o?o:Bi("form-field");St(e.element,"id",n),St(t.element,"for",n)}))}))}))}))])])};var l})(e,t.shared,n))),iframe:(aM=(e,t,o)=>((e,t,o)=>{const n="tox-dialog__iframe",s=e.transparent?[]:[`${n}--opaque`],r=e.border?["tox-navobj-bordered"]:[],a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...e.sandboxed?{sandbox:"allow-scripts allow-same-origin"}:{}},i=((e,t)=>{const o=en(e.getOr(""));return{getValue:e=>o.get(),setValue:(e,n)=>{if(o.get()!==n){const o=e.element,s=()=>St(o,"srcdoc",n);t?K_.fold(x(X_),(e=>e.throttle))(o,n,s):s()}o.set(n)}}})(o,e.streamContent),l=e.label.map((e=>XC(e,t))),c=jC.parts.field({factory:{sketch:e=>B_(A.from(r),{uid:e.uid,dom:{tag:"iframe",attributes:a,classes:[n,...s]},behaviours:da([Hb.config({}),Fh.config({}),w_(o,i.getValue,i.setValue),uc.config({channels:{[P_]:{onReceive:(e,t)=>{t.newFocus.each((t=>{at(e.element).each((o=>{(Ze(e.element,t)?wa:Ca)(o,"tox-navobj-bordered-focus")}))}))}}}})])})}});return GC(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n,s)=>{const r=yn(t,{source:"dynamic"});return BE(aM)(e,r,o,n,s)}),button:BE(((e,t)=>((e,t)=>{const o=vE(e.name,"custom");return n=A.none(),s=jC.parts.field({factory:Rb,...fE(e,A.some(o),t,[C_(""),b_()])}),GC(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:BE(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),A.some(!0)),s=jC.parts.field({factory:{sketch:w},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:da([b_(),mg.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{at(e.element).each((e=>wa(e,"tox-checkbox--disabled")))},onEnabled:e=>{at(e.element).each((e=>Ca(e,"tox-checkbox--disabled")))}}),Hb.config({}),Fh.config({}),S_(o,DE,ME),vh.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Eh("checkbox-events",[jr(tr(),((t,o)=>{Nr(t,KC,{name:e.name})}))])])}),r=jC.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[ul(t.translate(e.label))],behaviours:da([fk.config({})])}),a=e=>ny("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=zb({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return jC.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:da([mg.config({disabled:()=>!e.enabled||t.isDisabled()}),lw()])})})(e,t.shared.providers,o))),colorinput:BE(((e,t,o)=>((e,t,o,n)=>{const s=jC.parts.field({factory:Dy,inputClasses:["tox-textfield"],data:n,onSetValue:e=>hk.run(e).get(b),inputBehaviours:da([mg.config({disabled:t.providers.isDisabled}),lw(),Hb.config({}),hk.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>at(e.element),notify:{onValid:e=>{const t=$u.getValue(e);Nr(e,bk,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=$u.getValue(e);if(0===t.length)return rC(ln.value(!0));{const e=Re("span");Mt(e,"background-color",t);const o=Rt(e,"background-color").fold((()=>ln.error("blah")),(e=>ln.value(t)));return rC(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>XC(e,t.providers))),a=(e,t)=>{Nr(e,vk,{value:t})},i=zb(((e,t)=>yC.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:da([cw(t.providers.isDisabled),lw(),fk.config({}),Hb.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>sC((t=>e.fetch(t))).map((n=>A.from(TC(yn(HS(Bi("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,ly.CLOSE_ON_EXECUTE,T,t.providers),{movement:US(e.columns,e.presets)}))))),parts:{menu:_y(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[Kl,Xl,ec],onLtr:()=>[Xl,Kl,ec]},components:[],fetch:BS(o.getColors(e.storageKey),e.storageKey,o.hasCustomColors()),columns:o.getColorCols(e.storageKey),presets:"color",onItemAction:(t,n)=>{i.getOpt(t).each((t=>{"custom"===n?o.colorPicker((o=>{o.fold((()=>Rr(t,yk)),(o=>{a(t,o),uS(e.storageKey,o)}))}),"#ffffff"):a(t,"remove"===n?"":n)}))}},t));return jC.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:da([Eh("form-field-events",[jr(bk,((t,o)=>{i.getOpt(t).each((e=>{Mt(e.element,"background-color",o.event.color)})),Nr(t,KC,{name:e.name})})),jr(vk,((e,t)=>{jC.getField(e).each((o=>{$u.setValue(o,t.event.value),Qm.getCurrent(e).each(Fh.focus)}))})),jr(yk,((e,t)=>{jC.getField(e).each((t=>{Qm.getCurrent(e).each(Fh.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:BE(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=f_((e=>t=>r(t)?e.translate(k_[t]):e.translate(t))(t),n,t.tooltips.getConfig,((e,o,n=e,s=e)=>ny(n,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:t.translate(s),"aria-live":"polite",...o.fold((()=>({})),(e=>({id:e})))}},t.icons))),a=zb(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Nr(e,ZC,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Nr(e,ZC,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[a.asSpec()],behaviours:da([w_(o,(e=>{const t=a.get(e);return Qm.getCurrent(t).bind((e=>$u.getValue(e).hex)).map((e=>"#"+Oe(e,"#"))).getOr("")}),((e,t)=>{const o=A.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>ee(e,1))),n=a.get(e);Qm.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{$u.setValue(e,{hex:o.getOr("")}),c_.getField(e,"hex").each((e=>{Rr(e,er())}))}))})),b_()])}})(0,t.shared.providers,o))),dropzone:BE(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{V(e,(e=>{e(t,o)}))},r=(e,t)=>{var o;if(!mg.isDisabled(e)){const n=t.event.raw;i(e,null===(o=n.dataTransfer)||void 0===o?void 0:o.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{n&&($u.setValue(o,((e,t)=>{const o=T_.explode(t.getOption("images_file_types"));return P(ne(e),(e=>R(o,(t=>Ee(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),Nr(o,KC,{name:e.name}))},l=zb({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:da([Eh("input-file-events",[Xr(or()),Xr(hr())])])}),c=e.label.map((e=>XC(e,t))),d=jC.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:da([C_(o.getOr([])),b_(),mg.config({}),Ph.config({toggleClass:"dragenter",toggleOnExecute:!1}),Eh("dropzone-events",[jr("dragenter",s([n,Ph.toggle])),jr("dragleave",s([n,Ph.toggle])),jr("dragover",n),jr("drop",s([n,r])),jr(tr(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[ul(t.translate("Drop an image here"))]},Rb.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[ul(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:da([Hb.config({}),cw(t.isDisabled),lw()])})]}]})}});return GC(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:BE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:L(e.items,t.interpreter)}))(e,t.shared))),listbox:BE(((e,t,o)=>((e,t,o)=>{const n=R(e.items,(e=>!mT(e))),s=t.shared.providers,r=o.bind((t=>hT(e.items,t))).orThunk((()=>te(e.items).filter(mT))),a=e.label.map((e=>XC(e,s))),i=jC.parts.field({dom:{},factory:{sketch:o=>iT({uid:o.uid,text:r.map((e=>e.text)),icon:A.none(),tooltip:A.none(),role:Ce(!n,"combobox"),...n?{}:{listRole:"listbox"},ariaLabel:e.label,fetch:(o,s)=>{const r=pT(o,e.name,e.items,$u.getValue(o),n);s(uT(r,ly.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:A.none()}))},onSetup:x(b),getApi:x({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[Hb.config({}),w_(r.map((e=>e.value)),(e=>kt(e.element,gT)),((t,o)=>{hT(e.items,o).each((e=>{St(t.element,gT,e.value),Nr(t,rT,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),l={dom:{tag:"div",classes:["tox-listboxfield"]},components:[i]};return jC.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:G([a.toArray(),[l]]),fieldBehaviours:da([mg.config({disabled:x(!e.enabled),onDisabled:e=>{jC.getField(e).each(mg.disable)},onEnabled:e=>{jC.getField(e).each(mg.enable)}})])})})(e,t,o))),selectbox:BE(((e,t,o)=>((e,t,o)=>{const n=L(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>XC(e,t))),r=jC.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:fT,selectBehaviours:da([mg.config({disabled:()=>!e.enabled||t.isDisabled()}),Hb.config({}),Eh("selectbox-change",[jr(tr(),((t,o)=>{Nr(t,KC,{name:e.name})}))])])}),a=e.size>1?A.none():A.some(ny("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:G([[r],a.toArray()])};return jC.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:G([s.toArray(),[i]]),fieldBehaviours:da([mg.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{jC.getField(e).each(mg.disable)},onEnabled:e=>{jC.getField(e).each(mg.enable)}}),lw()])})})(e,t.shared.providers,o))),sizeinput:BE(((e,t)=>((e,t)=>{let o=CT;const n=Bi("ratio-event"),s=e=>ny(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=e.label.getOr("Constrain proportions"),a=t.translate(r),i=xT.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{"aria-label":a,"data-mce-name":r}},components:[s("lock"),s("unlock")],buttonBehaviours:da([mg.config({disabled:()=>!e.enabled||t.isDisabled()}),lw(),Hb.config({}),Jb.config(t.tooltips.getConfig({tooltipText:a}))])}),l=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),c=o=>jC.parts.field({factory:Dy,inputClasses:["tox-textfield"],inputBehaviours:da([mg.config({disabled:()=>!e.enabled||t.isDisabled()}),lw(),Hb.config({}),Eh("size-input-events",[jr(Ks(),((e,t)=>{Nr(e,n,{isField1:o})})),jr(tr(),((t,o)=>{Nr(t,KC,{name:e.name})}))])]),selectOnFocus:!1}),d=e=>({dom:{tag:"label",classes:["tox-label"]},components:[ul(t.translate(e))]}),u=xT.parts.field1(l([jC.parts.label(d("Width")),c(!0)])),m=xT.parts.field2(l([jC.parts.label(d("Height")),c(!1)]));return xT.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[u,m,l([d("\xa0"),i])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{wT($u.getValue(e)).each((e=>{o(e).each((e=>{$u.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:da([mg.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{xT.getField1(e).bind(jC.getField).each(mg.disable),xT.getField2(e).bind(jC.getField).each(mg.disable),xT.getLock(e).each(mg.disable)},onEnabled:e=>{xT.getField1(e).bind(jC.getField).each(mg.enable),xT.getField2(e).bind(jC.getField).each(mg.enable),xT.getLock(e).each(mg.enable)}}),lw(),Eh("size-input-events2",[jr(n,((e,t)=>{const n=t.event.isField1,s=n?xT.getField1(e):xT.getField2(e),r=n?xT.getField2(e):xT.getField1(e),a=s.map($u.getValue).getOr(""),i=r.map($u.getValue).getOr("");o=((e,t)=>{const o=wT(e).toOptional(),n=wT(t).toOptional();return we(o,n,((e,t)=>ST(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>ST(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(CT))).getOr(CT)})(a,i)}))])])})})(e,t.shared.providers))),slider:BE(((e,t,o)=>((e,t,o)=>{const n=t_.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ul(t.translate(e.label))]}),s=t_.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=t_.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return t_.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:x(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:da([b_(),Fh.config({})]),onChoose:(t,o,n)=>{Nr(t,KC,{name:e.name,value:n})},onChange:(t,o,n)=>{Nr(t,KC,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:BE(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=$u.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":AE,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{var n,s;const r=$u.getValue(t),a=null!==(s=null===(n=null==r?void 0:r.meta)||void 0===n?void 0:n.text)&&void 0!==s?s:r.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=EE(a,(e=>L(e,(e=>SE(e,e))))(o.getHistory(e)));return"file"===e?(s=[n,EE(a,kE(t)),EE(a,G([_E(t),OE(t),TE(t)]))],W(s,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(xE,t)),[])):n;var s}))})(e.filetype,n,o),r=uT(s,ly.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:A.none()});return rC(r)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(hk)&&hk.run(e).get(b)},typeaheadBehaviours:da([...o.getValidationHandler().map((t=>hk.config({getRoot:e=>at(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{St(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=$u.getValue(o);return mE((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=ln.error(e.message);o(t)}else{const t=ln.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),mg.config({disabled:()=>!e.enabled||s.isDisabled()}),Hb.config({}),Eh("urlinput-events",[jr(er(),(t=>{const o=ol(t.element),n=o.trim();n!==o&&nl(t.element,n),"file"===e.filetype&&Nr(t,KC,{name:e.name})})),jr(tr(),(t=>{Nr(t,KC,{name:e.name}),r(t)})),jr(ur(),(t=>{Nr(t,KC,{name:e.name}),r(t)}))])]),eventOrder:{[er()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:_y(0,0,"normal")},onExecute:(e,t,o)=>{Nr(t,ek,{})},onItemExecute:(t,o,n,s)=>{r(t),Nr(t,KC,{name:e.name})}},i=jC.parts.field({...a,factory:dE}),l=e.label.map((e=>XC(e,s))),c=zb(((e,t,o=e,n=e)=>ny(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",A.some(AE),"warning")),d=zb({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=Bi("browser.url.event"),g=zb({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:da([mg.config({disabled:()=>!e.enabled||s.isDisabled()})])}),p=zb(bE({name:e.name,icon:A.some("browse"),text:e.picker_text.or(e.label).getOr(""),enabled:e.enabled,primary:!1,buttonType:A.none(),borderless:!0},(e=>Rr(e,m)),s,[],["tox-browse-url"]));return jC.sketch({dom:YC([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:G([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:da([mg.config({disabled:()=>!e.enabled||s.isDisabled(),onDisabled:e=>{jC.getField(e).each(mg.disable),p.getOpt(e).each(mg.disable)},onEnabled:e=>{jC.getField(e).each(mg.enable),p.getOpt(e).each(mg.enable)}}),lw(),Eh("url-input-events",[jr(m,(t=>{Qm.getCurrent(t).each((o=>{const n=$u.getValue(o),s={fieldname:e.name,...n};u.each((n=>{n(s).get((n=>{$u.setValue(o,n),Nr(t,KC,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:BE((e=>{const t=nn(),o=zb({dom:{tag:e.tag}}),n=nn(),s=!__(e)&&e.onFocus.isSome()?[Fh.config({onFocus:t=>{e.onFocus.each((e=>{e(t.element.dom)}))}}),Hb.config({})]:[];return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:da([Eh("custom-editor-events",[Qr((s=>{o.getOpt(s).each((o=>{(__(e)?e.init(o.element.dom):O_.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),w_(A.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),b_()].concat(s)),components:[o.asSpec()]}})),htmlpanel:BE(((e,t)=>((e,t)=>{const o=["tox-form__group",...e.stretched?["tox-form__group--stretched"]:[]],n=Eh("htmlpanel",[Qr((t=>{e.onInit(t.element.dom)}))]);return"presentation"===e.presets?HC.sketch({dom:{tag:"div",classes:o,innerHtml:e.html},containerBehaviours:da([Jb.config({...t.tooltips.getConfig({tooltipText:"",onShow:e=>{_l(e.element,"[data-mce-tooltip]:hover").orThunk((()=>bc(e.element))).each((o=>{Ot(o,"data-mce-tooltip").each((o=>{Jb.setComponents(e,t.tooltips.getComponents({tooltipText:o}))}))}))}}),mode:"children-normal",anchor:e=>({type:"node",node:_l(e.element,"[data-mce-tooltip]:hover").orThunk((()=>bc(e.element).filter((e=>Ot(e,"data-mce-tooltip").isSome())))),root:e.element,layouts:{onLtr:x([ec,Zl,Xl,Jl,Kl,Ql]),onRtl:x([ec,Zl,Xl,Jl,Kl,Ql])},bubble:jc(0,-2,{})})}),n])}):HC.sketch({dom:{tag:"div",classes:o,innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:da([Hb.config({}),Fh.config({}),n])})})(e,t.shared.providers))),imagepreview:BE(((e,t,o)=>((e,t)=>{const o=en(t.getOr({url:""})),n=zb({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=zb({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:A.from(e.zoom),cachedWidth:A.from(e.cachedWidth),cachedHeight:A.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:da([b_(),w_(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=r;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=Kt(e),s=Ut(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const a=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Kt(e.element),Ut(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{Dt(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;var s;t.url!==kt(n,"src")&&(St(n,"src",t.url),Ca(e.element,"tox-imagepreview__loaded")),a(),(s=n,new Promise(((e,t)=>{const o=()=>{r(),e(s)},n=[Bc(s,"load",o),Bc(s,"error",(()=>{r(),t("Unable to load data from image: "+s.dom.src)}))],r=()=>V(n,(e=>e.unbind()));s.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(wa(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:BE(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:L(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:L(n,(e=>({dom:{tag:"tr"},components:L(e,o)})))})],behaviours:da([Hb.config({}),Fh.config({})])};var n,s})(e,t.shared.providers))),tree:BE(((e,t)=>((e,t)=>{const o=e.onLeafAction.getOr(b),n=e.onToggleExpand.getOr(b),s=e.defaultExpandedIds,r=en(s),a=en(e.defaultSelectedId),i=Bi("tree-id"),l=(n,s)=>e.items.map((e=>"leaf"===e.type?GT({leaf:e,selectedId:n,onLeafAction:o,visible:!0,treeId:i,backstage:t}):JT({directory:e,selectedId:n,onLeafAction:o,expandedIds:s,labelTabstopping:!0,treeId:i,backstage:t})));return{dom:{tag:"div",classes:["tox-tree"],attributes:{role:"tree"}},components:l(a.get(),r.get()),behaviours:da([vh.config({mode:"flow",selector:".tox-tree--leaf__label--visible, .tox-tree--directory__label--visible",cycles:!1}),Eh(QT,[jr("expand-tree-node",((e,t)=>{const{expanded:o,node:s}=t.event;r.set(o?[...r.get(),s]:r.get().filter((e=>e!==s))),n(r.get(),{expanded:o,node:s})}))]),uc.config({channels:{[`update-active-item-${i}`]:{onReceive:(e,t)=>{a.set(A.some(t.value)),Th.set(e,l(A.some(t.value),r.get()))}}}}),Th.config({})])}})(e,t))),panel:BE(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:L(e.items,t.shared.interpreter)}))(e,t)))},IE={field:(e,t)=>t,record:x([])},RE=(e,t,o,n,s)=>{const r=yn(n,{shared:{interpreter:t=>NE(e,t,o,r,s)}});return NE(e,t,o,r,s)},NE=(e,t,o,n,s)=>fe(FE,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(r=>r(e,t,o,n,s))),zE=(e,t,o,n)=>NE(IE,e,t,o,n),LE="layout-inset",VE=e=>e.x,HE=(e,t)=>e.x+e.width/2-t.width/2,PE=(e,t)=>e.x+e.width-t.width,UE=e=>e.y,WE=(e,t)=>e.y+e.height-t.height,jE=(e,t)=>e.y+e.height/2-t.height/2,$E=(e,t,o)=>Dl(PE(e,t),WE(e,t),o.insetSouthwest(),Nl(),"southwest",Pl(e,{right:0,bottom:3}),LE),GE=(e,t,o)=>Dl(VE(e),WE(e,t),o.insetSoutheast(),Rl(),"southeast",Pl(e,{left:1,bottom:3}),LE),qE=(e,t,o)=>Dl(PE(e,t),UE(e),o.insetNorthwest(),Il(),"northwest",Pl(e,{right:0,top:2}),LE),YE=(e,t,o)=>Dl(VE(e),UE(e),o.insetNortheast(),Fl(),"northeast",Pl(e,{left:1,top:2}),LE),XE=(e,t,o)=>Dl(HE(e,t),UE(e),o.insetNorth(),zl(),"north",Pl(e,{top:2}),LE),KE=(e,t,o)=>Dl(HE(e,t),WE(e,t),o.insetSouth(),Ll(),"south",Pl(e,{bottom:3}),LE),JE=(e,t,o)=>Dl(PE(e,t),jE(e,t),o.insetEast(),Hl(),"east",Pl(e,{right:0}),LE),QE=(e,t,o)=>Dl(VE(e),jE(e,t),o.insetWest(),Vl(),"west",Pl(e,{left:1}),LE),ZE=e=>{switch(e){case"north":return XE;case"northeast":return YE;case"northwest":return qE;case"south":return KE;case"southeast":return GE;case"southwest":return $E;case"east":return JE;case"west":return QE}},eA=(e,t,o,n,s)=>Ec(n).map(ZE).getOr(XE)(e,t,o,n,s),tA=e=>{switch(e){case"north":return KE;case"northeast":return GE;case"northwest":return $E;case"south":return XE;case"southeast":return YE;case"southwest":return qE;case"east":return QE;case"west":return JE}},oA=(e,t,o,n,s)=>Ec(n).map(tA).getOr(XE)(e,t,o,n,s),nA={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},sA=(e,t,o)=>{const n={maxHeightFunction:Hc()};return()=>o()?{type:"node",root:ht(pt(e())),node:A.from(e()),bubble:jc(12,12,nA),layouts:{onRtl:()=>[YE],onLtr:()=>[qE]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:jc(-12,12,nA),layouts:{onRtl:()=>[Xl,Kl,ec],onLtr:()=>[Kl,Xl,ec]},overrides:n}},rA=(e,t,o,n)=>{const s={maxHeightFunction:Hc()};return()=>n()?{type:"node",root:ht(pt(t())),node:A.from(t()),bubble:jc(12,12,nA),layouts:{onRtl:()=>[XE],onLtr:()=>[XE]},overrides:s}:e?{type:"node",root:ht(pt(t())),node:A.from(t()),bubble:jc(0,-Wt(t()),nA),layouts:{onRtl:()=>[Zl],onLtr:()=>[Zl]},overrides:s}:{type:"hotspot",hotspot:o(),bubble:jc(0,0,nA),layouts:{onRtl:()=>[Zl],onLtr:()=>[Zl]},overrides:s}},aA=(e,t,o)=>()=>o()?{type:"node",root:ht(pt(e())),node:A.from(e()),layouts:{onRtl:()=>[XE],onLtr:()=>[XE]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[ec],onLtr:()=>[ec]}},iA=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng(),o=e.model.table.getSelectedCells();if(o.length>1){const e=o[0],t=o[o.length-1],n={firstCell:ze(e),lastCell:ze(t)};return A.some(n)}return A.some(bd.range(ze(t.startContainer),t.startOffset,ze(t.endContainer),t.endOffset))}}),lA=e=>t=>({type:"node",root:e(),node:t}),cA=(e,t,o,n)=>{const s=Cb(e),r=()=>ze(e.getBody()),a=()=>ze(e.getContentAreaContainer()),i=()=>s||!n();return{inlineDialog:sA(a,t,i),inlineBottomDialog:rA(e.inline,a,o,i),banner:aA(a,t,i),cursor:iA(e,r),node:lA(r)}},dA=e=>(t,o)=>{VS(e)(t,o)},uA=e=>()=>OS(e),mA=e=>t=>wS(e,t),gA=e=>t=>kS(e,t),pA=e=>()=>nb(e),hA=e=>ve(e,"items"),fA=e=>ve(e,"format"),bA=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],vA=e=>W(e,((e,t)=>{if(be(t,"items")){const o=vA(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(be(t,"inline")||(e=>be(e,"block"))(t)||(e=>be(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),yA=e=>Nf(e).map((t=>{const o=((e,t)=>{const o=vA(t),n=t=>{V(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return zf(e)?bA.concat(o):o})).getOr(bA),xA=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),wA=(e,t,o,n)=>{const s=t=>L(t,(t=>hA(t)?(e=>{const t=s(e.items);return{...e,type:"submenu",getStyleItems:x(t)}})(t):fA(t)?(e=>xA(e,o,n))(t):(e=>{const t=re(e);return 1===t.length&&I(t,"title")})(t)?{...t,type:"separator"}:(t=>{const s=r(t.name)?t.name:Bi(t.title),a=`custom-${s}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(s,i),i})(t)));return s(t)},SA=e=>{let t=0;const o=e=>[{dom:{tag:"div",classes:["tox-tooltip__body"]},components:[ul(e.tooltipText)]}];return{getConfig:n=>({delayForShow:()=>t>0?60:300,delayForHide:x(300),exclusive:!0,lazySink:e,tooltipDom:{tag:"div",classes:["tox-tooltip","tox-tooltip--up"]},tooltipComponents:o(n),onShow:(e,o)=>{t++,n.onShow&&n.onShow(e,o)},onHide:(e,o)=>{t--,n.onHide&&n.onHide(e,o)},onSetup:n.onSetup}),getComponents:o}},CA=T_.trim,kA=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},OA=kA("true"),_A=kA("false"),TA=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),EA=e=>e.innerText||e.textContent,AA=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&DA(e),MA=e=>e&&/^(H[1-6])$/.test(e.nodeName),DA=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return OA(t)}return!1})(e)&&!_A(e),BA=e=>MA(e)&&DA(e),FA=e=>{var t;const o=(e=>e.id?e.id:Bi("h"))(e);return TA("header",null!==(t=EA(e))&&void 0!==t?t:"","#"+o,(e=>MA(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},IA=e=>{const t=e.id||e.name,o=EA(e);return TA("anchor",o||"#"+t,"#"+t,0,b)},RA=e=>CA(e.title).length>0,NA=e=>{const t=(e=>{const t=L(Od(ze(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return P((e=>L(P(e,BA),FA))(t).concat((e=>L(P(e,AA),IA))(t)),RA)},zA="tinymce-url-history",LA=e=>r(e)&&/^https?/.test(e),VA=e=>a(e)&&pe(e,(e=>{return!(l(t=e)&&t.length<=5&&Y(t,LA));var t})).isNone(),HA=()=>{const e=lS.getItem(zA);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+zA+" was not valid JSON",e),{};throw e}return VA(t)?t:(console.log("Local storage "+zA+" was not valid format",t),{})},PA=e=>{const t=HA();return fe(t,e).getOr([])},UA=(e,t)=>{if(!LA(e))return;const o=HA(),n=fe(o,t).getOr([]),s=P(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!VA(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));lS.setItem(zA,JSON.stringify(e))})(o)},WA=e=>!!e,jA=e=>le(T_.makeMap(e,/[, ]/),WA),$A=e=>A.from(Kf(e)),GA=e=>A.from(e).filter(r).getOrUndefined(),qA=e=>({getHistory:PA,addToHistory:UA,getLinkInformation:()=>(e=>eb(e)?A.some({targets:NA(e.getBody()),anchorTop:GA(tb(e)),anchorBottom:GA(ob(e))}):A.none())(e),getValidationHandler:()=>(e=>A.from(Jf(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=A.from(Zf(e)).filter(WA).map(jA);return $A(e).fold(T,(e=>t.fold(E,(e=>re(e).length>0&&e))))})(e);return d(o)?o?$A(e):A.none():o[t]?$A(e):A.none()})(e,t).map((o=>n=>sC((s=>{const i={filetype:t,fieldname:n.fieldname,...A.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),YA=Pm,XA=km,KA=x([Ss("shell",!1),rs("makeItem"),Ss("setupItem",b),Xu("listBehaviours",[Th])]),JA=wm({name:"items",overrides:()=>({behaviours:da([Th.config({})])})}),QA=x([JA]),ZA=Ym({name:x("CustomList")(),configFields:KA(),partFields:QA(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Th.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Yu(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?A.some(n):Fm(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=Th.contents(n),r=o.length,a=r-s.length,i=a>0?N(a,(()=>e.makeItem())):[],l=s.slice(r);V(l,(e=>Th.remove(n,e))),V(i,(e=>Th.append(n,e)));const c=Th.contents(n);V(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),eM=x([rs("dom"),Ss("shell",!0),Gu("toolbarBehaviours",[Th])]),tM=x([wm({name:"groups",overrides:()=>({behaviours:da([Th.config({})])})})]),oM=Ym({name:"Toolbar",configFields:eM(),partFields:tM(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Th.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:Yu(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?A.some(n):Fm(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{Th.set(e,o)}))},refresh:b},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),nM=b,sM=T,rM=x([]);var aM,iM=Object.freeze({__proto__:null,setup:nM,isDocked:sM,getBehaviours:rM});const lM=x(Bi("toolbar-height-change")),cM={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},dM="tox-tinymce--toolbar-sticky-on",uM="tox-tinymce--toolbar-sticky-off",mM=(e,t)=>I(_i.getModes(e),t),gM=e=>{const t=e.element;at(t).each((o=>{const n="padding-"+_i.getModes(e)[0];if(_i.isDocked(e)){const e=Kt(o);Mt(t,"width",e+"px"),Mt(o,n,(e=>Wt(e)+(parseInt(Ft(e,"margin-top"),10)||0)+(parseInt(Ft(e,"margin-bottom"),10)||0))(t)+"px")}else Lt(t,"width"),Lt(o,n)}))},pM=(e,t)=>{t?(Ca(e,cM.fadeOutClass),Oa(e,[cM.transitionClass,cM.fadeInClass])):(Ca(e,cM.fadeInClass),Oa(e,[cM.fadeOutClass,cM.transitionClass]))},hM=(e,t)=>{const o=ze(e.getContainer());t?(wa(o,dM),Ca(o,uM)):(wa(o,uM),Ca(o,dM))},fM=(e,t)=>{const o=nn(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||gM(t),hM(e,_i.isDocked(t)),t.getSystem().broadcastOn([Tu()],{}),n().each((e=>e.getSystem().broadcastOn([Tu()],{})))},a=e.inline?[]:[uc.config({channels:{[lM()]:{onReceive:gM}}})];return[Fh.config({}),_i.config({contextual:{lazyContext:t=>{const o=Wt(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer();return A.from(n).map((n=>{const s=Ko(ze(n));return Fb(e,t.element).fold((()=>{const e=s.height-o,n=s.y+(mM(t,"top")?0:o);return Xo(s.x,n,s.width,e)}),(e=>{const n=Qo(s,Ib(e)),r=mM(t,"top")?n.y:n.y+o;return Xo(n.x,r,n.width,n.height-o)}))}))},onShow:()=>{s((e=>pM(e,!0)))},onShown:e=>{s((e=>_a(e,[cM.transitionClass,cM.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=tt(t);fc(o).filter((e=>!Ze(t,e))).filter((t=>Ze(t,ze(o.dom.body))||et(e,t))).each((()=>gc(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>bc(e).orThunk((()=>t().toOptional().bind((e=>bc(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>pM(e,!1)))},onHidden:()=>{s((e=>_a(e,[cM.transitionClass])))},...cM},lazyViewport:t=>Fb(e,t.element).fold((()=>{const o=Zo(),n=qf(e),s=o.y+(mM(t,"top")?n:0),r=o.height-(mM(t,"bottom")?n:0);return{bounds:Xo(o.x,s,o.width,r),optScrollEnv:A.none()}}),(e=>({bounds:Ib(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:qt(e.element).top})}))),modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var bM=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(_i.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(gM)})),e.on("SkinLoaded",(()=>{o().each((e=>{_i.isDocked(e)?_i.reset(e):_i.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(_i.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{_i.refresh(t);const o=t.element;up(o)&&((e,t)=>{const o=tt(t),n=st(t).dom.innerHeight,s=Po(o),r=ze(e.elm),a=Jo(r),i=Ut(r),l=a.y,c=l+i,d=qt(t),u=Ut(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Uo(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Uo(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{hM(e,!1)}))},isDocked:e=>e().map(_i.isDocked).getOr(!1),getBehaviours:fM});const vM=In([Xy,as("items",Nn([Ln([Ky,gs("items",Un)]),Un]))].concat(_x)),yM=[bs("text"),bs("tooltip"),bs("icon"),Cs("search",!1,Nn([Wn,In([bs("placeholder")])],(e=>d(e)?e?A.some({placeholder:A.none()}):A.none():A.some(e)))),ds("fetch"),Es("onSetup",(()=>b))],xM=In([Xy,...yM]),wM=e=>Kn("menubutton",xM,e),SM=In([Xy,ux,dx,lx,px,ox,ax,_s("presets","normal",["normal","color","listpreview"]),yx(1),sx,rx]);var CM=qm({factory:(e,t)=>{const o={focus:vh.focusIn,setMenus:(e,o)=>{const n=L(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=wM(o).mapError((e=>Zn(e))).getOrDie();return UT(n,"tox-mbtn",t.backstage,A.some("menuitem"))}));Th.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:da([Th.config({}),Eh("menubar-events",[Qr((t=>{e.onSetup(t)})),jr(Xs(),((e,t)=>{_l(e.element,".tox-mbtn--active").each((o=>{Tl(t.event.target,".tox-mbtn").each((t=>{Ze(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{yC.expand(e),yC.close(o),Fh.focus(e)}))}))}))}))})),jr(Er(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{yC.isOpen(o)&&(yC.expand(e),yC.close(o))}))}))}))]),vh.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),A.some(!0))}),Hb.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[rs("dom"),rs("uid"),rs("onEscape"),rs("backstage"),Ss("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const kM="container",OM=[Gu("slotBehaviours",[])],_M=e=>"<alloy.field."+e+">",TM=(e,t)=>{const o=t=>zm(e),n=(t,o)=>(n,s)=>Fm(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==kt(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;Mt(o,"display","none"),St(o,"aria-hidden","true"),Nr(e,Ar(),{name:t,visible:!1})}})),i=(e=>(t,o)=>{V(o,(o=>e(t,o)))})(a),l=n(((e,t)=>{if(!s(e)){const o=e.element;Lt(o,"display"),Tt(o,"aria-hidden"),Nr(e,Ar(),{name:t,visible:!0})}})),c={getSlotNames:o,getSlot:(t,o)=>Fm(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:l};return{uid:e.uid,dom:e.dom,components:t,behaviours:qu(e.slotBehaviours),apis:c}},EM=le({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>Gi(e))),AM={...EM,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),Em(kM,_M(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=L(n,(e=>ym({name:e,pname:_M(e)})));return Wm(kM,OM,s,TM,o)}},MM=In([dx,ux,Es("onShow",b),Es("onHide",b),ax]),DM=e=>({element:()=>e.element.dom}),BM=(e,t)=>{const o=L(re(t),(e=>{const o=t[e],n=Jn((e=>Kn("sidebar",MM,e))(o));return{name:e,getApi:DM,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return L(o,(t=>{const n=en(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:nw([gw(t,n),pw(t,n),jr(Ar(),((e,t)=>{const n=t.event,s=j(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},FM=e=>AM.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:BM(t,e),slotBehaviours:nw([Qr((e=>AM.hideAllSlots(e)))])}))),IM=(e,t)=>{St(e,"role",t)},RM=e=>Qm.getCurrent(e).bind((e=>HT.isGrowing(e)||HT.hasGrown(e)?Qm.getCurrent(e).bind((e=>j(AM.getSlotNames(e),(t=>AM.isShowing(e,t))))):A.none())),NM=Bi("FixSizeEvent"),zM=Bi("AutoSizeEvent");var LM=Object.freeze({__proto__:null,block:(e,t,o,n)=>{St(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=da([vh.config({mode:"special",onTab:()=>A.some(!0),onShiftTab:()=>A.some(!0)}),Fh.config({})]),a=n(s,r),i=s.getSystem().build(a);Th.append(s,fl(i)),i.hasConfigured(vh)&&t.focus&&vh.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>Th.remove(s,i)))},unblock:(e,t,o)=>{Tt(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()},isBlocked:(e,t,o)=>o.isBlocked()}),VM=[Es("getRoot",A.none),Ts("focus",!0),vi("onBlock"),vi("onUnblock")];const HM=ma({fields:VM,name:"blocking",apis:LM,state:Object.freeze({__proto__:null,init:()=>{const e=tn((e=>e.destroy()));return ca({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),PM=e=>Qm.getCurrent(e).each((e=>gc(e.element,!0))),UM=(e,t,o)=>{const n=en(!1),s=nn(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?te(s.composedPath()):A.from(s.target)).map(ze).filter($e).exists((e=>ka(e,"mce-pastebin"))))&&(o.preventDefault(),PM(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n=`data-mce-${o}`;A.from(e.iframeElement).map(ze).each((e=>{t?(Ot(e,o).each((t=>St(e,n,t))),St(e,o,-1)):(Tt(e,o),Ot(e,n).each((t=>{St(e,o,t),Tt(e,n)})))}))})(e,o),o)HM.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:Nb('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),Lt(s,"display"),Tt(s,"aria-hidden"),e.hasFocus()&&PM(t);else{const o=Qm.getCurrent(t).exists((e=>hc(e.element)));HM.unblock(t),Mt(s,"display","none"),St(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,s))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=Sf.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},WM=(e,t,o)=>({within:e,extra:t,withinWidth:o}),jM=(e,t,o)=>{const n=W(e,((e,t)=>((e,t)=>{const n=o(e);return A.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(x(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=P(n,(e=>e.finish<=t)),r=U(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},$M=e=>L(e,(e=>e.element)),GM=(e,t)=>{const o=L(t,(e=>fl(e)));oM.setGroups(e,o)},qM=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=Im(e,t,"primary"),r=ZS.getCoupled(e,"overflowGroup");Mt(s.element,"visibility","hidden");const a=n.concat([r]),i=se(a,(e=>bc(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),GM(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=jM(t,e,o);return 0===n.extra.length?A.some(n):A.none()})(e,t,o).getOrThunk((()=>jM(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=$M(e.concat(t));return WM(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=$M(e).concat([o]);return WM(s,$M(t),n)})(r,a,n,i):((e,t,o)=>WM($M(e),[],o))(r,0,i)})(Kt(s.element),t.builtGroups.get(),(e=>Math.ceil(e.element.dom.getBoundingClientRect().width)),r);0===l.extra.length?(Th.remove(s,r),o([])):(GM(s,l.within),o(l.extra)),Lt(s.element,"visibility"),Vt(s.element),i.each(Fh.focus)},YM=x([Gu("splitToolbarBehaviours",[ZS]),ns("builtGroups",(()=>en([])))]),XM=x([fi(["overflowToggledClass"]),ys("getOverflowBounds"),rs("lazySink"),ns("overflowGroups",(()=>en([]))),vi("onOpened"),vi("onClosed")].concat(YM())),KM=x([ym({factory:oM,schema:eM(),name:"primary"}),xm({schema:eM(),name:"overflow"}),xm({name:"overflow-button"}),xm({name:"overflow-group"})]),JM=x(((e,t)=>{((e,t)=>{const o=Xt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);Mt(e,"max-width",o+"px")})(e,Math.floor(t))})),QM=x([fi(["toggledClass"]),rs("lazySink"),ds("fetch"),ys("getBounds"),ws("fireDismissalEventInstead",[Ss("event",_r())]),Qc(),vi("onToggled")]),ZM=x([xm({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:da([Ph.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1,onToggled:e.onToggled})])})}),xm({factory:oM,schema:eM(),name:"toolbar",overrides:e=>({toolbarBehaviours:da([vh.config({mode:"cyclic",onEscape:t=>(Fm(t,e,"button").each(Fh.focus),A.none())})])})})]),eD=nn(),tD=(e,t)=>{const o=ZS.getCoupled(e,"toolbarSandbox");Ou.isOpen(o)?Ou.close(o):Ou.open(o,t.toolbar())},oD=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();Zd.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:JM()}}},s)},nD=(e,t,o,n,s)=>{oM.setGroups(t,s),oD(e,t,o,n),Ph.on(e)},sD=Ym({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...Rb.sketch({...n.button(),action:e=>{tD(e,n)},buttonBehaviours:Ku({dump:n.button().buttonBehaviours},[ZS.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=Al();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:da([vh.config({mode:"special",onEscape:e=>(Ou.close(e),A.some(!0))}),Ou.config({onOpen:(s,r)=>{const a=eD.get().getOr(!1);o.fetch().get((s=>{nD(e,r,o,t.layouts,s),n.link(e.element),a||vh.focusIn(r)}))},onClose:()=>{Ph.off(e),eD.get().getOr(!1)||Fh.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>Ml(o,n)||Ml(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),uc.config({channels:{...Mu({isExtraPart:T,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Bu({doReposition:()=>{Ou.getState(ZS.getCoupled(e,"toolbarSandbox")).each((n=>{oD(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Ou.getState(ZS.getCoupled(t,"toolbarSandbox")).each((s=>{nD(t,s,e,o.layouts,n)}))},reposition:t=>{Ou.getState(ZS.getCoupled(t,"toolbarSandbox")).each((n=>{oD(t,n,e,o.layouts)}))},toggle:e=>{tD(e,n)},toggleWithoutFocusing:e=>{((e,t)=>{eD.set(!0),tD(e,t),eD.clear()})(e,n)},getToolbar:e=>Ou.getState(ZS.getCoupled(e,"toolbarSandbox")),isOpen:e=>Ou.isOpen(ZS.getCoupled(e,"toolbarSandbox"))}}),configFields:QM(),partFields:ZM(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggleWithoutFocusing(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),rD=x([rs("items"),fi(["itemSelector"]),Gu("tgroupBehaviours",[vh])]),aD=x([Sm({name:"items",unit:"item"})]),iD=Ym({name:"ToolbarGroup",configFields:rD(),partFields:aD(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:Yu(e.tgroupBehaviours,[vh.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),lD=e=>L(e,(e=>fl(e))),cD=(e,t,o)=>{qM(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{sD.setGroups(e,lD(n))}))}))},dD=Ym({name:"SplitFloatingToolbar",configFields:XM(),partFields:KM(),factory:(e,t,o,n)=>{const s=zb(sD.sketch({fetch:()=>sC((t=>{t(lD(e.overflowGroups.get()))})),layouts:{onLtr:()=>[Kl,Xl],onRtl:()=>[Xl,Kl],onBottomLtr:()=>[Ql,Jl],onBottomRtl:()=>[Jl,Ql]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()},onToggled:(t,o)=>e[o?"onOpened":"onClosed"](t)}));return{uid:e.uid,dom:e.dom,components:t,behaviours:Yu(e.splitToolbarBehaviours,[ZS.config({others:{overflowGroup:()=>iD.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(L(o,t.getSystem().build)),cD(t,s,e)},refresh:t=>cD(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{sD.toggle(e)}))},toggleWithoutFocusing:e=>{s.getOpt(e).each(sD.toggleWithoutFocusing)},isOpen:e=>s.getOpt(e).map(sD.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{sD.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(sD.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),uD=x([fi(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),vi("onOpened"),vi("onClosed")].concat(YM())),mD=x([ym({factory:oM,schema:eM(),name:"primary"}),ym({factory:oM,schema:eM(),name:"overflow",overrides:e=>({toolbarBehaviours:da([HT.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{Fm(t,e,"overflow-button").each((e=>{Ph.off(e)})),e.onClosed(t)},onGrown:t=>{e.onOpened(t)},onStartGrow:t=>{Fm(t,e,"overflow-button").each(Ph.on)}}),vh.config({mode:"acyclic",onEscape:t=>(Fm(t,e,"overflow-button").each(Fh.focus),A.some(!0))})])})}),xm({name:"overflow-button",overrides:e=>({buttonBehaviours:da([Ph.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])})}),xm({name:"overflow-group"})]),gD=(e,t,o)=>{Fm(e,t,"overflow-button").each((n=>{Fm(e,t,"overflow").each((s=>{if(pD(e,t),HT.hasShrunk(s)){const e=t.onOpened;t.onOpened=n=>{o||vh.focusIn(s),e(n),t.onOpened=e}}else{const e=t.onClosed;t.onClosed=s=>{o||Fh.focus(n),e(s),t.onClosed=e}}HT.toggleGrow(s)}))}))},pD=(e,t)=>{Fm(e,t,"overflow").each((o=>{qM(e,t,(e=>{const t=L(e,(e=>fl(e)));oM.setGroups(o,t)})),Fm(e,t,"overflow-button").each((e=>{HT.hasGrown(o)&&Ph.on(e)})),HT.refresh(o)}))},hD=Ym({name:"SplitSlidingToolbar",configFields:uD(),partFields:mD(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:Yu(e.splitToolbarBehaviours,[ZS.config({others:{overflowGroup:e=>iD.sketch({...n["overflow-group"](),items:[Rb.sketch({...n["overflow-button"](),action:t=>{Rr(e,s)}})]})}}),Eh("toolbar-toggle-events",[jr(s,(t=>{gD(t,e,!1)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=L(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),pD(t,e)},refresh:t=>pD(t,e),toggle:t=>{gD(t,e,!1)},toggleWithoutFocusing:t=>{gD(t,e,!0)},isOpen:t=>((e,t)=>Fm(e,t,"overflow").map(HT.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),fD=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[iD.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled]), .tox-number-input:not([disabled])"},tgroupBehaviours:da([Hb.config({}),Fh.config({})])}},bD=e=>iD.sketch(fD(e)),vD=(e,t)=>{const o=Qr((t=>{const o=L(e.initGroups,bD);oM.setGroups(t,o)}));return da([uw(e.providers.isDisabled),lw(),vh.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Eh("toolbar-events",[o])])},yD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":fD({title:A.none(),items:[]}),"overflow-button":pE({name:"more",icon:A.some("more-drawer"),enabled:!0,tooltip:A.some("Reveal or hide additional toolbar items"),primary:!1,buttonType:A.none(),borderless:!1},A.none(),e.providers,[],"overflow-button")},splitToolbarBehaviours:vD(e,t)}},xD=e=>{const t=yD(e),o=dD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return dD.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=Jo(t),n=nt(t),s=Jo(n),r=Math.max(n.dom.scrollHeight,s.height);return Xo(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>e.onToggled(t,!0),onClosed:t=>e.onToggled(t,!1)})},wD=e=>{const t=hD.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=hD.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=yD(e);return hD.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>{t.getSystem().broadcastOn([lM()],{type:"opened"}),e.onToggled(t,!0)},onClosed:t=>{t.getSystem().broadcastOn([lM()],{type:"closed"}),e.onToggled(t,!1)}})},SD=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return oM.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===xf.scrolling?["tox-toolbar--scrolling"]:[])},components:[oM.parts.groups({})],toolbarBehaviours:vD(e,t)})},CD=[lx,dx,bs("tooltip"),_s("buttonType","secondary",["primary","secondary"]),Ts("borderless",!1),ds("onAction")],kD={button:[...CD,Qy,cs("type",["button"])],togglebutton:[...CD,Ts("active",!1),cs("type",["togglebutton"])]},OD=[cs("type",["group"]),As("buttons",[],es("type",kD))],_D=es("type",{...kD,group:OD}),TD=In([As("buttons",[],_D),ds("onShow"),ds("onHide")]),ED=(e,t)=>((e,t)=>{var o,n;const s="togglebutton"===e.type,r=e.icon.map((e=>nT(e,t.icons))).map(zb),a={...e,name:s?e.text.getOr(e.icon.getOr("")):null!==(o=e.text)&&void 0!==o?o:e.icon.getOr(""),primary:"primary"===e.buttonType,buttonType:A.from(e.buttonType),tooltip:e.tooltip,icon:e.icon,enabled:!0,borderless:e.borderless},i=hE(null!==(n=e.buttonType)&&void 0!==n?n:"secondary"),l=s?e.text.map(t.translate):A.some(t.translate(e.text)),c=l.map(ul),d=a.tooltip.or(l).map((e=>({"aria-label":t.translate(e)}))).getOr({}),u=r.map((e=>e.asSpec())),m=bw([u,c]),g=e.icon.isSome()&&c.isSome(),p={tag:"button",classes:i.concat(...e.icon.isSome()&&!g?["tox-button--icon"]:[]).concat(...g?["tox-button--icon-and-text"]:[]).concat(...e.borderless?["tox-button--naked"]:[]).concat(..."togglebutton"===e.type&&e.active?["tox-button--enabled"]:[]),attributes:d},h=gE(a,A.some((o=>{const n=e=>{r.map((n=>n.getOpt(o).each((o=>{Th.set(o,[nT(e,t.icons)])}))))};return s?e.onAction({setIcon:n,setActive:e=>{const t=o.element;e?(wa(t,"tox-button--enabled"),St(t,"aria-pressed",!0)):(Ca(t,"tox-button--enabled"),Tt(t,"aria-pressed"))},isActive:()=>ka(o.element,"tox-button--enabled")}):"button"===e.type?e.onAction({setIcon:n}):void 0})),[],p,m,e.tooltip,t);return Rb.sketch(h)})(e,t),AD=Mo().deviceType,MD=AD.isPhone(),DD=AD.isTablet();var BD=Ym({name:"silver.View",configFields:[rs("viewConfig")],partFields:[wm({factory:{sketch:e=>{let t=!1;const o=L(e.buttons,(o=>"group"===o.type?(t=!0,((e,t)=>({dom:{tag:"div",classes:["tox-view__toolbar__group"]},components:L(e.buttons,(e=>ED(e,t)))}))(o,e.providers)):ED(o,e.providers)));return{uid:e.uid,dom:{tag:"div",classes:[t?"tox-view__toolbar":"tox-view__header",...MD||DD?["tox-view--mobile","tox-view--scrolling"]:[]]},behaviours:da([Fh.config({}),vh.config({mode:"flow",selector:"button, .tox-button",focusInside:$g.OnEnterOrSpaceMode})]),components:t?o:[HC.sketch({dom:{tag:"div",classes:["tox-view__header-start"]},components:[]}),HC.sketch({dom:{tag:"div",classes:["tox-view__header-end"]},components:o})]}}},schema:[rs("buttons"),rs("providers")],name:"header"}),wm({factory:{sketch:e=>({uid:e.uid,behaviours:da([Fh.config({}),Hb.config({})]),dom:{tag:"div",classes:["tox-view__pane"]}})},schema:[],name:"pane"})],factory:(e,t,o,n)=>{const s={getPane:t=>YA.getPart(t,e,"pane"),getOnShow:t=>e.viewConfig.onShow,getOnHide:t=>e.viewConfig.onHide};return{uid:e.uid,dom:e.dom,components:t,behaviours:da([Fh.config({}),vh.config({mode:"cyclic",focusInside:$g.OnEnterOrSpaceMode})]),apis:s}},apis:{getPane:(e,t)=>e.getPane(t),getOnShow:(e,t)=>e.getOnShow(t),getOnHide:(e,t)=>e.getOnHide(t)}});const FD=(e,t,o)=>ge(t,((t,n)=>{const s=Jn(Kn("view",TD,t));return e.slot(n,BD.sketch({dom:{tag:"div",classes:["tox-view"]},viewConfig:s,components:[...s.buttons.length>0?[BD.parts.header({buttons:s.buttons,providers:o})]:[],BD.parts.pane({})]}))})),ID=(e,t)=>AM.sketch((o=>({dom:{tag:"div",classes:["tox-view-wrap__slot-container"]},components:FD(o,e,t),slotBehaviours:nw([Qr((e=>AM.hideAllSlots(e)))])}))),RD=e=>j(AM.getSlotNames(e),(t=>AM.isShowing(e,t))),ND=(e,t,o)=>{AM.getSlot(e,t).each((e=>{BD.getPane(e).each((t=>{var n;o(e)((n=t.element.dom,{getContainer:x(n)}))}))}))};var zD=qm({factory:(e,t)=>{const o={setViews:(e,o)=>{Th.set(e,[ID(o,t.backstage.shared.providers)])},whichView:e=>Qm.getCurrent(e).bind(RD),toggleView:(e,t,o,n)=>Qm.getCurrent(e).exists((s=>{const r=RD(s),a=r.exists((e=>n===e)),i=AM.getSlot(s,n).isSome();return i&&(AM.hideAllSlots(s),a?((e=>{const t=e.element;Mt(t,"display","none"),St(t,"aria-hidden","true")})(e),t()):(o(),(e=>{const t=e.element;Lt(t,"display"),Tt(t,"aria-hidden")})(e),AM.showSlot(s,n),((e,t)=>{ND(e,t,BD.getOnShow)})(s,n)),r.each((e=>((e,t)=>ND(e,t,BD.getOnHide))(s,e)))),i}))};return{uid:e.uid,dom:{tag:"div",classes:["tox-view-wrap"],attributes:{"aria-hidden":"true"},styles:{display:"none"}},components:[],behaviours:da([Th.config({}),Qm.config({find:e=>{const t=Th.contents(e);return te(t)}})]),apis:o}},name:"silver.ViewWrapper",configFields:[rs("backstage")],apis:{setViews:(e,t,o)=>e.setViews(t,o),toggleView:(e,t,o,n,s)=>e.toggleView(t,o,n,s),whichView:(e,t)=>e.whichView(t)}});const LD=XA.optional({factory:CM,name:"menubar",schema:[rs("backstage")]}),VD=XA.optional({factory:{sketch:e=>ZA.sketch({uid:e.uid,dom:e.dom,listBehaviours:da([vh.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>SD({type:e.type,uid:Bi("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),A.some(!0))}),setupItem:(e,t,o,n)=>{oM.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[rs("dom"),rs("onEscape")]}),HD=XA.optional({factory:{sketch:e=>{const t=(e=>e.type===xf.sliding?wD:e.type===xf.floating?xD:SD)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),A.some(!0)),onToggled:(t,o)=>e.onToolbarToggled(o),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[rs("dom"),rs("onEscape"),rs("getSink")]}),PD=XA.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?fM:rM;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:da(o(t,e.sharedBackstage))}}},name:"header",schema:[rs("dom")]}),UD=XA.optional({factory:{sketch:e=>({uid:e.uid,dom:e.dom,components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-self-hosted-premium-features/?utm_campaign=self_hosted_upgrade_promo&utm_source=tiny&utm_medium=referral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u26a1\ufe0fUpgrade"}}]})},name:"promotion",schema:[rs("dom")]}),WD=XA.optional({name:"socket",schema:[rs("dom")]}),jD=XA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"presentation"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:da([Hb.config({}),Fh.config({}),HT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{Qm.getCurrent(e).each(AM.hideAllSlots),Rr(e,zM)},onGrown:e=>{Rr(e,zM)},onStartGrow:e=>{Nr(e,NM,{width:Rt(e.element,"width").getOr("")})},onStartShrink:e=>{Nr(e,NM,{width:Kt(e.element)+"px"})}}),Th.config({}),Qm.config({find:e=>{const t=Th.contents(e);return te(t)}})])}],behaviours:da([v_(0),Eh("sidebar-sliding-events",[jr(NM,((e,t)=>{Mt(e.element,"width",t.event.width)})),jr(zM,((e,t)=>{Lt(e.element,"width")}))])])})},name:"sidebar",schema:[rs("dom")]}),$D=XA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:da([Th.config({}),HM.config({focus:!1}),Qm.config({find:e=>te(e.components())})]),components:[]})},name:"throbber",schema:[rs("dom")]}),GD=XA.optional({factory:zD,name:"viewWrapper",schema:[rs("backstage")]}),qD=XA.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-editor-container"]},components:e.components})},name:"editorContainer",schema:[]});var YD=Ym({name:"OuterContainer",factory:(e,t,o)=>{let n=!1;const s=e=>{kl(e,".tox-statusbar").each((e=>{"none"===Ft(e,"display")&&"true"===kt(e,"aria-hidden")?(Lt(e,"display"),Tt(e,"aria-hidden")):(Mt(e,"display","none"),St(e,"aria-hidden","true"))}))},a={getSocket:t=>YA.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{YA.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{Qm.getCurrent(e).each((n=>{Th.set(n,[FM(t)]);const s=null==o?void 0:o.toLowerCase();r(s)&&be(t,s)&&Qm.getCurrent(n).each((t=>{AM.showSlot(t,s),HT.immediateGrow(n),Lt(n.element,"width"),IM(e.element,"region")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{YA.getPart(t,e,"sidebar").each((e=>((e,t)=>{Qm.getCurrent(e).each((o=>{Qm.getCurrent(o).each((n=>{HT.hasGrown(o)?AM.isShowing(n,t)?(HT.shrink(o),IM(e.element,"presentation")):(AM.hideAllSlots(n),AM.showSlot(n,t),IM(e.element,"region")):(AM.hideAllSlots(n),AM.showSlot(n,t),HT.grow(o),IM(e.element,"region"))}))}))})(e,o)))},whichSidebar:t=>YA.getPart(t,e,"sidebar").bind(RM).getOrNull(),getHeader:t=>YA.getPart(t,e,"header"),getToolbar:t=>YA.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{YA.getPart(t,e,"toolbar").each((e=>{const t=L(o,bD);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{YA.getPart(t,e,"multiple-toolbar").each((e=>{const t=L(o,(e=>L(e,bD)));ZA.setItems(e,t)}))},refreshToolbar:t=>{YA.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{YA.getPart(t,e,"toolbar").each((e=>{Se(e.getApis().toggle,(t=>t(e)))}))},toggleToolbarDrawerWithoutFocusing:t=>{YA.getPart(t,e,"toolbar").each((e=>{Se(e.getApis().toggleWithoutFocusing,(t=>t(e)))}))},isToolbarDrawerToggled:t=>YA.getPart(t,e,"toolbar").bind((e=>A.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>YA.getPart(t,e,"throbber"),focusToolbar:t=>{YA.getPart(t,e,"toolbar").orThunk((()=>YA.getPart(t,e,"multiple-toolbar"))).each((e=>{vh.focusIn(e)}))},setMenubar:(t,o)=>{YA.getPart(t,e,"menubar").each((e=>{CM.setMenus(e,o)}))},focusMenubar:t=>{YA.getPart(t,e,"menubar").each((e=>{CM.focus(e)}))},setViews:(t,o)=>{YA.getPart(t,e,"viewWrapper").each((e=>{zD.setViews(e,o)}))},toggleView:(t,o)=>YA.getPart(t,e,"viewWrapper").exists((e=>zD.toggleView(e,(()=>a.showMainView(t)),(()=>a.hideMainView(t)),o))),whichView:t=>YA.getPart(t,e,"viewWrapper").bind(zD.whichView).getOrNull(),hideMainView:t=>{n=a.isToolbarDrawerToggled(t),n&&a.toggleToolbarDrawer(t),YA.getPart(t,e,"editorContainer").each((e=>{const t=e.element;s(t),Mt(t,"display","none"),St(t,"aria-hidden","true")}))},showMainView:t=>{n&&a.toggleToolbarDrawer(t),YA.getPart(t,e,"editorContainer").each((e=>{const t=e.element;s(t),Lt(t,"display"),Tt(t,"aria-hidden")}))}};return{uid:e.uid,dom:e.dom,components:t,apis:a,behaviours:e.behaviours}},configFields:[rs("dom"),rs("behaviours")],partFields:[PD,LD,HD,VD,WD,jD,UD,$D,GD,qD],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},toggleToolbarDrawerWithoutFocusing:(e,t)=>{e.toggleToolbarDrawerWithoutFocusing(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)},setViews:(e,t,o)=>{e.setViews(t,o)},toggleView:(e,t,o)=>e.toggleView(t,o),whichView:(e,t)=>e.whichView(t)}});const XD={file:{title:"File",items:"newdocument restoredraft | preview | importword exportpdf exportword | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code revisionhistory | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed inserttemplate codesample inserttable accordion math | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"aidialog aishortcuts | spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code typography wordcount addtemplate"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},KD=e=>e.split(" "),JD=(e,t)=>{const o={...XD,...t.menus},n=re(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?KD("file edit view insert format tools table help"):KD(!1===t.menubar?"":t.menubar),a=P(s,(e=>{const o=be(XD,e);return n?o||fe(t.menus,e).exists((e=>be(e,"items"))):o})),i=L(a,(n=>{const s=o[n];return((e,t,o)=>{const n=Hf(o).split(/[ ,]/);return{text:e.title,getItems:()=>q(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||R(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:KD(s.items)},t,e)}));return P(i,(e=>e.getItems().length>0&&R(e.getItems(),(e=>r(e)||"separator"!==e.type))))},QD=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),ZD=(e,t,o,n)=>(e.on("remove",(()=>n.unloadRawCss(t))),n.loadRawCss(t,o)),eB=async(e,t)=>{const o="ui/"+fb(e).getOr("default")+"/skin.css",n=tinymce.Resource.get(o);if(!r(n)){const o=e.editorManager.suffix;return QD(e,t+`/skin${o}.css`,e.ui.styleSheetLoader)}ZD(e,o,n,e.ui.styleSheetLoader)},tB=async(e,t)=>{var o;if(o=ze(e.getElement()),ft(o).isSome()){const o="ui/"+fb(e).getOr("default")+"/skin.shadowdom.css",n=tinymce.Resource.get(o);if(!r(n)){const o=e.editorManager.suffix;return QD(e,t+`/skin.shadowdom${o}.css`,Cf.DOM.styleSheetLoader)}ZD(e,o,n,Cf.DOM.styleSheetLoader)}},oB=(e,t)=>(async(e,t)=>{const o=()=>{const o=hb(t),n=t.editorManager.suffix;o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+`${n}.css`)};fb(t).fold(o,(n=>{const s="ui/"+n+(e?"/content.inline":"/content")+".css",a=tinymce.Resource.get(s);r(a)?ZD(t,s,a,t.ui.styleSheetLoader):o()}));const n=hb(t);if(!gb(t)&&r(n))return Promise.all([eB(t,n),tB(t,n)]).then()})(e,t).then((e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}})(t),((e,t)=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)),nB=C(oB,!1),sB=C(oB,!0),rB=(e,t,o)=>De(o)?e.translate(t):e.translate([t,e.translate(o)]),aB=(e,t)=>{const o=(o,s,r,a)=>{const i=e.shared.providers.translate(o.title);if("separator"===o.type)return A.some({type:"separator",text:i});if("submenu"===o.type){const e=q(o.getStyleItems(),(e=>n(e,s,a)));return 0===s&&e.length<=0?A.none():A.some({type:"nestedmenuitem",text:i,enabled:e.length>0,getSubmenuItems:()=>q(o.getStyleItems(),(e=>n(e,s,a)))})}return A.some({type:"togglemenuitem",text:i,icon:o.icon,active:o.isSelected(a),enabled:!r,onAction:t.onAction(o),...o.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},n=(e,n,s)=>{const r="formatter"===e.type&&t.isInvalid(e);return 0===n?r?[]:o(e,n,!1,s).toArray():o(e,n,r,s).toArray()},s=e=>{const o=t.getCurrentValue(),s=t.shouldHide?0:1;return q(e,(e=>n(e,s,o)))};return{validateItems:s,getFetch:(e,t)=>(o,n)=>{const r=t(),a=s(r);n(uT(a,ly.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:A.none()}))}}},iB=(e,t)=>{const o=t.dataset,n="basic"===o.type?()=>L(o.data,(e=>xA(e,t.isSelectedFor,t.getPreviewFor))):o.getData;return{items:aB(e,t),getStyleItems:n}},lB=(e,t,o,n,s,r)=>{const{items:a,getStyleItems:i}=iB(t,o),l=en(o.tooltip);return iT({text:o.icon.isSome()?A.none():o.text,icon:o.icon,ariaLabel:A.some(o.tooltip),tooltip:A.none(),role:A.none(),fetch:a.getFetch(t,i),onSetup:t=>{const r=o=>t.setTooltip(rB(e,n(o.value),o.value));return e.on(s,r),oS(rS(e,"NodeChange",(t=>{const n=t.getComponent();o.updateText(n),mg.set(t.getComponent(),!e.selection.isEditable())}))(t),(()=>e.off(s,r)))},getApi:e=>({getComponent:x(e),setTooltip:o=>{const n=t.shared.providers.translate(o);St(e.element,"aria-label",n),l.set(o)}}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[Jb.config({...t.shared.providers.tooltips.getConfig({tooltipText:t.shared.providers.translate(o.tooltip),onShow:e=>{if(o.tooltip!==l.get()){const o=t.shared.providers.translate(l.get());Jb.setComponents(e,t.shared.providers.tooltips.getComponents({tooltipText:o}))}}})})]},"tox-tbtn",t.shared,r)};var cB;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(cB||(cB={}));const dB=(e,t,o)=>{const n=(s=((e,t)=>t===cB.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),L(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},uB=x("Alignment {0}"),mB="left",gB=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],pB=e=>{const t={type:"basic",data:gB};return{tooltip:rB(e,uB(),mB),text:A.none(),icon:A.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:e=>A.none,onAction:t=>()=>j(gB,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=j(gB,(t=>e.formatter.match(t.format))).fold(x(mB),(e=>e.title.toLowerCase()));Nr(t,aT,{icon:`align-${o}`}),((e,t)=>{e.dispatch("AlignTextUpdate",t)})(e,{value:o})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},hB=(e,t)=>{const o=t(),n=L(o,(e=>e.format));return A.from(e.formatter.closest(n)).bind((e=>j(o,(t=>t.format===e))))},fB=x("Block {0}"),bB="Paragraph",vB=e=>{const t=dB(e,"block_formats",cB.SemiColon);return{tooltip:rB(e,fB(),bB),text:A.some(bB),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:aS(e),updateText:o=>{const n=hB(e,(()=>t.data)).fold(x(bB),(e=>e.title));Nr(o,rT,{text:n}),((e,t)=>{e.dispatch("BlocksTextUpdate",t)})(e,{value:n})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},yB=x("Font {0}"),xB="System Font",wB=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],SB=e=>{const t=e.split(/\s*,\s*/);return L(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},CB=(e,t)=>t.length>0&&Y(t,(t=>e.indexOf(t.toLowerCase())>-1)),kB=e=>{const t=()=>{const t=e=>e?SB(e)[0]:"",n=e.queryCommandValue("FontName"),s=o.data,r=n?n.toLowerCase():"",a=mb(e),i=j(s,(e=>{const o=e.format;return o.toLowerCase()===r||t(o).toLowerCase()===t(r).toLowerCase()})).orThunk((()=>Ce(((e,t)=>{if(0===e.indexOf("-apple-system")||t.length>0){const o=SB(e.toLowerCase());return CB(o,wB)||CB(o,t)}return!1})(r,a),{title:xB,format:r})));return{matchOpt:i,font:n}},o=dB(e,"font_family_formats",cB.SemiColon);return{tooltip:rB(e,yB(),xB),text:A.some(xB),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=t();return e},getPreviewFor:e=>()=>A.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:o=>{const{matchOpt:n,font:s}=t(),r=n.fold(x(s),(e=>e.title));Nr(o,rT,{text:r}),((e,t)=>{e.dispatch("FontFamilyTextUpdate",t)})(e,{value:r})},dataset:o,shouldHide:!1,isInvalid:T}},OB={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},_B=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),TB=(e,t)=>A.from(_B.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>R(t,(t=>R(OB[t],(t=>e===t)))))(n,t)?A.some({value:o,unit:n}):A.none()})),EB={tab:x(9),escape:x(27),enter:x(13),backspace:x(8),delete:x(46),left:x(37),up:x(38),right:x(39),down:x(40),space:x(32),home:x(36),end:x(35),pageUp:x(33),pageDown:x(34)},AB=x("Font size {0}"),MB="12pt",DB={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},BB={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},FB=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":fe(BB,e).getOr(e),IB=e=>fe(DB,e).getOr(""),RB=e=>{const t=()=>{let t=A.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=FB(s,e),r=IB(n);t=j(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=x(A.none),n=dB(e,"font_size_formats",cB.Space);return{tooltip:rB(e,AB(),MB),text:A.some(MB),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:o=>{const{matchOpt:n,size:s}=t(),r=n.fold(x(s),(e=>e.title));Nr(o,rT,{text:r}),((e,t)=>{e.dispatch("FontSizeTextUpdate",t)})(e,{value:r})},dataset:n,shouldHide:!1,isInvalid:T}},NB=e=>De(e)?"Formats":"Format {0}",zB=(e,t)=>{const o="Formats";return{tooltip:rB(e,NB(""),""),text:A.some(o),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:aS(e),updateText:t=>{const n=e=>hA(e)?q(e.items,n):fA(e)?[{title:e.title,format:e.format}]:[],s=q(yA(e),n),r=hB(e,x(s)).fold(x({title:o,tooltipLabel:""}),(e=>({title:e.title,tooltipLabel:e.title})));Nr(t,rT,{text:r.title}),((e,t)=>{e.dispatch("StylesTextUpdate",t)})(e,{value:r.tooltipLabel})},shouldHide:Lf(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}},LB=x([rs("toggleClass"),rs("fetch"),xi("onExecute"),Ss("getHotspot",A.some),Ss("getAnchorOverrides",x({})),Qc(),xi("onItemExecute"),ps("lazySink"),rs("dom"),vi("onOpen"),Gu("splitDropdownBehaviours",[ZS,vh,Fh]),Ss("matchWidth",!1),Ss("useMinWidth",!1),Ss("eventOrder",{}),ps("role"),ps("listRole")].concat(fC())),VB=ym({factory:Rb,schema:[rs("dom")],name:"arrow",defaults:()=>({buttonBehaviours:da([Fh.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(zr)},buttonBehaviours:da([Ph.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),HB=ym({factory:Rb,schema:[rs("dom")],name:"button",defaults:()=>({buttonBehaviours:da([Fh.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),PB=x([VB,HB,wm({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[rs("text")],name:"aria-descriptor"}),xm({schema:[hi()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),iC()]),UB=Ym({name:"SplitDropdown",configFields:LB(),partFields:PB(),factory:(e,t,o,n)=>{const s=e=>{Qm.getCurrent(e).each((e=>{Sg.highlightFirst(e),vh.focusIn(e)}))},r=t=>{uC(e,w,t,n,s,ff.HighlightMenuAndItem).get(b)},a=t=>{const o=Im(t,e,"button");return zr(o),A.some(!0)},i={...Pr([Qr(((t,o)=>{Fm(t,e,"aria-descriptor").each((e=>{const o=Bi("aria");St(e.element,"id",o),St(t.element,"aria-describedby",o)}))}))]),...Wh(A.some(r))},l={repositionMenus:e=>{Ph.isOn(e)&&hC(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[gr()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:Yu(e.splitDropdownBehaviours,[ZS.config({others:{sandbox:t=>{const o=Im(t,e,"arrow");return pC(e,t,{onOpen:()=>{Ph.on(o),Ph.on(t)},onClose:()=>{Ph.off(o),Ph.off(t)}})}}}),vh.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),A.some(!0))}),Fh.config({}),Ph.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),WB=e=>({isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t),setText:t=>Nr(e,rT,{text:t}),setIcon:t=>Nr(e,aT,{icon:t})}),jB=e=>({setActive:t=>{Ph.set(e,t)},isActive:()=>Ph.isOn(e),isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t),setText:t=>Nr(e,rT,{text:t}),setIcon:t=>Nr(e,aT,{icon:t})}),$B=(e,t)=>e.map((e=>({"aria-label":t.translate(e)}))).getOr({}),GB=Bi("focus-button"),qB=(e,t,o,n,s,r)=>{const a=t.map((e=>zb(sT(e,"tox-tbtn",s)))),i=e.map((e=>zb(nT(e,s.icons))));return{dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:{...$B(o,s),...g(r)?{"data-mce-name":r}:{}}},components:bw([i.map((e=>e.asSpec())),a.map((e=>e.asSpec()))]),eventOrder:{[$s()]:["focusing","alloy.base.behaviour",Q_],[kr()]:[Q_,"toolbar-group-button-events"]},buttonBehaviours:da([uw(s.isDisabled),lw(),Eh(Q_,[Qr(((e,t)=>eT(e))),jr(rT,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{Th.set(e,[ul(s.translate(t.event.text))])}))})),jr(aT,((e,t)=>{i.bind((t=>t.getOpt(e))).each((e=>{Th.set(e,[nT(t.event.icon,s.icons)])}))})),jr($s(),((e,t)=>{t.event.prevent(),Rr(e,GB)}))])].concat(n.getOr([])))}},YB=(e,t,o,n)=>{var s;const r=en(b),a=qB(e.icon,e.text,e.tooltip,A.none(),o,n);return Rb.sketch({dom:a.dom,components:a.components,eventOrder:Z_,buttonBehaviours:{...da([Eh("toolbar-button-events",[(i={onAction:e.onAction,getApi:t.getApi},ta(((e,t)=>{mw(i,e)((t=>{Nr(e,J_,{buttonApi:t}),i.onAction(t)}))}))),gw(t,r),pw(t,r)]),...e.tooltip.map((t=>Jb.config(o.tooltips.getConfig({tooltipText:o.translate(t)+e.shortcut.map((e=>` (${xw(e)})`)).getOr("")})))).toArray(),uw((()=>!e.enabled||o.isDisabled())),lw()].concat(t.toolbarButtonBehaviours)),[Q_]:null===(s=a.buttonBehaviours)||void 0===s?void 0:s[Q_]}});var i},XB=(e,t,o,n)=>YB(e,{toolbarButtonBehaviours:o.length>0?[Eh("toolbarButtonWith",o)]:[],getApi:WB,onSetup:e.onSetup},t,n),KB=(e,t,o,n)=>YB(e,{toolbarButtonBehaviours:[Th.config({}),Ph.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[Eh("toolbarToggleButtonWith",o)]:[]),getApi:jB,onSetup:e.onSetup},t,n),JB=(e,t,o)=>n=>sC((e=>t.fetch(e))).map((s=>A.from(TC(yn(HS(Bi("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,ly.CLOSE_ON_EXECUTE,t.select.getOr(T),o),{movement:US(t.columns,t.presets),menuBehaviours:nw("auto"!==t.columns?[]:[Qr(((e,o)=>{ow(e,4,xy(t.presets)).each((({numRows:t,numColumns:o})=>{vh.setGridSize(e,t,o)}))}))])}))))),QB=[{name:"history",items:["undo","redo"]},{name:"ai",items:["aidialog","aishortcuts"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],ZB=(e,t)=>(o,n,s,r)=>{const a=e(o).mapError((e=>Zn(e))).getOrDie();return t(a,n,s,r)},eF={button:ZB(Ex,((e,t,o,n)=>((e,t,o)=>XB(e,t,[],o))(e,t.shared.providers,n))),togglebutton:ZB(Dx,((e,t,o,n)=>((e,t,o)=>KB(e,t,[],o))(e,t.shared.providers,n))),menubutton:ZB(wM,((e,t,o,n)=>UT(e,"tox-tbtn",t,A.none(),!1,n))),splitbutton:ZB((e=>Kn("SplitButton",SM,e)),((e,t,o,n)=>((e,t,o)=>{const n=en(e.tooltip.getOr("")),s=e=>({isEnabled:()=>!mg.isDisabled(e),setEnabled:t=>mg.set(e,!t),setIconFill:(t,o)=>{_l(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{St(e,"fill",o)}))},setActive:t=>{St(e.element,"aria-pressed",t),_l(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>Ph.set(e,t)))}))},isActive:()=>_l(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(Ph.isOn))),setText:t=>_l(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Nr(e,rT,{text:t}))))),setIcon:t=>_l(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Nr(e,aT,{icon:t}))))),setTooltip:o=>{const s=t.providers.translate(o);St(e.element,"aria-label",s),n.set(o)}}),r=en(b),a={getApi:s,onSetup:e.onSetup};return UB.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...$B(e.tooltip,t.providers),...g(o)?{"data-mce-name":o}:{}}},onExecute:t=>{const o=s(t);o.isEnabled()&&e.onAction(o)},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:da([dw(t.providers.isDisabled),lw(),Eh("split-dropdown-events",[Qr(((e,t)=>eT(e))),jr(GB,Fh.focus),gw(a,r),pw(a,r)]),fk.config({}),...e.tooltip.map((e=>Jb.config({...t.providers.tooltips.getConfig({tooltipText:t.providers.translate(e),onShow:o=>{if(n.get()!==e){const e=t.providers.translate(n.get());Jb.setComponents(o,t.providers.tooltips.getComponents({tooltipText:e}))}}})}))).toArray()]),eventOrder:{[kr()]:["alloy.base.behaviour","split-dropdown-events","tooltipping"],[Or()]:["split-dropdown-events","tooltipping"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:JB(s,e,t.providers),parts:{menu:_y(0,e.columns,e.presets)},components:[UB.parts.button(qB(e.icon,e.text,A.none(),A.some([Ph.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),UB.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:ey("chevron-down",t.providers.icons)},buttonBehaviours:da([dw(t.providers.isDisabled),lw(),ty()])}),UB.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.shared,n))),grouptoolbarbutton:ZB((e=>Kn("GroupToolbarButton",vM,e)),((e,t,o,n)=>{const s=o.ui.registry.getAll().buttons,r={[Kc]:t.shared.header.isPositionedAtTop()?Xc.TopToBottom:Xc.BottomToTop};if(Pf(o)===xf.floating)return((e,t,o,n,s)=>{const r=t.shared,a=en(b),i={toolbarButtonBehaviours:[],getApi:WB,onSetup:e.onSetup},l=[Eh("toolbar-group-button-events",[gw(i,a),pw(i,a)])];return sD.sketch({lazySink:r.getSink,fetch:()=>sC((t=>{t(L(o(e.items),bD))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:qB(e.icon,e.text,e.tooltip,A.some(l),r.providers,s),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>oF(o,{buttons:s,toolbar:e,allowToolbarGroups:!1},t,A.none())),r,n);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},tF={styles:(e,t)=>{const o={type:"advanced",...t.styles};return lB(e,t,zB(e,o),NB,"StylesTextUpdate","styles")},fontsize:(e,t)=>lB(e,t,RB(e),AB,"FontSizeTextUpdate","fontsize"),fontsizeinput:(e,t)=>((e,t,o,n)=>{let s=A.none();const r=rS(e,"NodeChange SwitchMode",(t=>{const n=t.getComponent();s=A.some(n),o.updateInputValue(n),mg.set(n,!e.selection.isEditable())})),a=e=>({getComponent:x(e)}),i=en(b),l=Bi("custom-number-input-events"),c=(e,t,n)=>{const r=s.map((e=>$u.getValue(e))).getOr(""),a=o.getNewValue(r,e),i=r.length-`${a}`.length,l=s.map((e=>e.element.dom.selectionStart-i)),c=s.map((e=>e.element.dom.selectionEnd-i));o.onAction(a,n),s.each((e=>{$u.setValue(e,a),t&&(l.each((t=>e.element.dom.selectionStart=t)),c.each((t=>e.element.dom.selectionEnd=t)))}))},d=(e,t)=>c(((e,t)=>e-t),e,t),u=(e,t)=>c(((e,t)=>e+t),e,t),m=e=>at(e.element).fold(A.none,(e=>(gc(e),A.some(!0)))),p=e=>hc(e.element)?(dt(e.element).each((e=>gc(e))),A.some(!0)):A.none(),h=(o,n,s,r)=>{const i=en(b),l=t.shared.providers.translate(s),c=Bi("altExecuting"),d=rS(e,"NodeChange SwitchMode",(t=>{mg.set(t.getComponent(),!e.selection.isEditable())})),u=e=>{mg.isDisabled(e)||o(!0)};return Rb.sketch({dom:{tag:"button",attributes:{"aria-label":l,"data-mce-name":n},classes:r.concat(n)},components:[oT(n,t.shared.providers.icons)],buttonBehaviours:da([mg.config({}),Jb.config(t.shared.providers.tooltips.getConfig({tooltipText:l})),Eh(c,[gw({onSetup:d,getApi:a},i),pw({getApi:a},i),jr(Qs(),((e,t)=>{t.event.raw.keyCode!==EB.space()&&t.event.raw.keyCode!==EB.enter()||mg.isDisabled(e)||o(!1)})),jr(or(),u),jr(Ws(),u)])]),eventOrder:{[Qs()]:[c,"keying"],[or()]:[c,"alloy.base.behaviour"],[Ws()]:[c,"alloy.base.behaviour"],[kr()]:["alloy.base.behaviour",c,"tooltipping"],[Or()]:[c,"tooltipping"]}})},f=zb(h((e=>d(!1,e)),"minus","Decrease font size",[])),v=zb(h((e=>u(!1,e)),"plus","Increase font size",[])),y=zb({dom:{tag:"div",classes:["tox-input-wrapper"]},components:[Dy.sketch({inputBehaviours:da([mg.config({}),Eh(l,[gw({onSetup:r,getApi:a},i),pw({getApi:a},i)]),Eh("input-update-display-text",[jr(rT,((e,t)=>{$u.setValue(e,t.event.text)})),jr(Js(),(e=>{o.onAction($u.getValue(e))})),jr(tr(),(e=>{o.onAction($u.getValue(e))}))]),vh.config({mode:"special",onEnter:e=>(c(w,!0,!0),A.some(!0)),onEscape:m,onUp:e=>(u(!0,!1),A.some(!0)),onDown:e=>(d(!0,!1),A.some(!0)),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})],behaviours:da([Fh.config({}),vh.config({mode:"special",onEnter:p,onSpace:p,onEscape:m}),Eh("input-wrapper-events",[jr(Xs(),(e=>{V([f,v],(t=>{const o=ze(t.get(e).element.dom);hc(o)&&pc(o)}))}))])])});return{dom:{tag:"div",classes:["tox-number-input"],attributes:{...g(n)?{"data-mce-name":n}:{}}},components:[f.asSpec(),y.asSpec(),v.asSpec()],behaviours:da([Fh.config({}),vh.config({mode:"flow",focusInside:$g.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-input-wrapper",onEscape:e=>hc(e.element)?A.none():(gc(e.element),A.some(!0))})])}})(e,t,(e=>{const t=()=>e.queryCommandValue("FontSize");return{updateInputValue:e=>Nr(e,rT,{text:t()}),onAction:(t,o)=>e.execCommand("FontSize",!1,t,{skip_focus:!o}),getNewValue:(o,n)=>{TB(o,["unsupportedLength","empty"]);const s=t(),r=TB(o,["unsupportedLength","empty"]).or(TB(s,["unsupportedLength","empty"])),a=r.map((e=>e.value)).getOr(16),i=Qf(e),l=r.map((e=>e.unit)).filter((e=>""!==e)).getOr(i),c=n(a,(e=>{var t;return null!==(t={em:{step:.1},cm:{step:.1},in:{step:.1},pc:{step:.1},ch:{step:.1},rem:{step:.1}}[e])&&void 0!==t?t:{step:1}})(l).step),d=`${(e=>e>=0)(c)?c:a}${l}`;return d!==s&&((e,t)=>{e.dispatch("FontSizeInputTextUpdate",t)})(e,{value:d}),d}}})(e),"fontsizeinput"),fontfamily:(e,t)=>lB(e,t,kB(e),yB,"FontFamilyTextUpdate","fontfamily"),blocks:(e,t)=>lB(e,t,vB(e),fB,"BlocksTextUpdate","blocks"),align:(e,t)=>lB(e,t,pB(e),uB,"AlignTextUpdate","align")},oF=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=L(QB,(t=>{const o=P(t.items,(t=>be(e,t)||be(tF,t)));return{name:t.name,items:o}}));return P(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return L(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>be(e,"name")&&be(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=L(s,(s=>{const r=q(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>fe(t,o.toLowerCase()).orThunk((()=>r.bind((e=>se(e,(e=>fe(t,e+o.toLowerCase()))))))).fold((()=>fe(tF,o.toLowerCase()).map((t=>t(e,s)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o,n)=>fe(eF,e.type).fold((()=>(console.error("skipping button defined by",e),A.none())),(s=>A.some(s(e,t,o,n)))))(t,s,e,o.toLowerCase()):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),A.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:A.from(e.translate(s.name)),items:r}}));return P(a,(e=>e.items.length>0))},nF=(e,t,o,n)=>{const s=t.mainUi.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return oF(e,s,n,A.none())}));YD.setToolbars(s,t)}else YD.setToolbar(s,oF(e,o,n,A.none()))},sF=Mo(),rF=sF.os.isiOS()&&sF.os.version.major<=12;var aF=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=t,i=en(0),l=r.outerContainer;nB(e);const d=ze(s.targetNode),u=ht(pt(d));mu(d,r.mothership),((e,t,o)=>{Tb(e)&&mu(o.mainUi.mothership.element,o.popupUi.mothership),uu(t,o.dialogUi.mothership)})(e,u,t),e.on("SkinLoaded",(()=>{YD.setSidebar(l,o.sidebar,cb(e)),nF(e,t,o,n),i.set(e.getWin().innerWidth),YD.setMenubar(l,JD(e,o)),YD.setViews(l,o.views),((e,t)=>{const{uiMotherships:o}=t,n=e.dom;let s=e.getWin();const r=e.getDoc().documentElement,a=en($t(s.innerWidth,s.innerHeight)),i=en($t(r.offsetWidth,r.offsetHeight)),l=()=>{const t=a.get();t.left===s.innerWidth&&t.top===s.innerHeight||(a.set($t(s.innerWidth,s.innerHeight)),Zw(e))},c=()=>{const t=e.getDoc().documentElement,o=i.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(i.set($t(t.offsetWidth,t.offsetHeight)),Zw(e))},d=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};n.bind(s,"resize",l),n.bind(s,"scroll",d);const u=Fc(ze(e.getBody()),"load",c);e.on("hide",(()=>{V(o,(e=>{Mt(e.element,"display","none")}))})),e.on("show",(()=>{V(o,(e=>{Lt(e.element,"display")}))})),e.on("NodeChange",c),e.on("remove",(()=>{u.unbind(),n.unbind(s,"resize",l),n.unbind(s,"scroll",d),s=null}))})(e,t)}));const m=YD.getSocket(l).getOrDie("Could not find expected socket element");if(rF){Dt(m.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=Bc(m.element,"scroll",t.throttle);e.on("remove",o.unbind)}iw(e,t),e.addCommand("ToggleSidebar",((t,o)=>{YD.toggleSidebar(l,o),(e=>{e.dispatch("ToggleSidebar")})(e)})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=YD.whichSidebar(l))&&void 0!==e?e:""})),e.addCommand("ToggleView",((t,o)=>{if(YD.toggleView(l,o)){const t=l.element;r.mothership.broadcastOn([_u()],{target:t}),V(a,(e=>{e.broadcastOn([_u()],{target:t})})),c(YD.whichView(l))&&(e.focus(),e.nodeChanged(),YD.refreshToolbar(l)),(e=>{e.dispatch("ToggleView")})(e)}})),e.addQueryValueHandler("ToggleView",(()=>{var e;return null!==(e=YD.whichView(l))&&void 0!==e?e:""}));const g=Pf(e);g!==xf.sliding&&g!==xf.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==i.get()&&(YD.refreshToolbar(t.mainUi.outerContainer),i.set(o))}));const p={setEnabled:e=>{aw(t,!e)},isEnabled:()=>!mg.isDisabled(l)};return{iframeContainer:m.element.dom,editorContainer:l.element.dom,api:p}}});const iF=e=>/^[0-9\.]+(|px)$/i.test(""+e)?A.some(parseInt(""+e,10)):A.none(),lF=e=>h(e)?e+"px":e,cF=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},dF=e=>{const t=Df(e),o=Bf(e),n=If(e);return iF(t).map((e=>cF(e,o,n)))},{ToolbarLocation:uF,ToolbarMode:mF}=Ab,gF=(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=o,i=Cf.DOM,l=Cb(e),c=_b(e),d=If(e).or(dF(e)),u=n.shared.header,m=u.isPositionedAtTop,g=Pf(e),p=g===mF.sliding||g===mF.floating,h=en(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(x(0),(e=>e.components().length>1?Ut(e.components()[1].element):0)):0,v=()=>{V(a,(e=>{e.broadcastOn([Tu()],{})}))},y=o=>{if(!f())return;l||s.on((e=>{const o=d.getOrThunk((()=>jo().width-Yt(t).left-10));Mt(e.element,"max-width",o+"px")}));const n=Po(),a=!(l||l||!(qt(r.outerContainer.element).left+Jt(r.outerContainer.element)>=window.innerWidth-40||Rt(r.outerContainer.element,"width").isSome())||(Mt(r.outerContainer.element,"position","absolute"),Mt(r.outerContainer.element,"left","0px"),Lt(r.outerContainer.element,"width"),0));if(p&&YD.refreshToolbar(r.outerContainer),!l){const o=Po(),i=Ce(n.left!==o.left,n);((o,n)=>{s.on((s=>{const a=YD.getToolbar(r.outerContainer),i=b(a),l=Ko(t),c=((e,t)=>Tb(e)?Ma(t):A.none())(e,r.outerContainer.element),d=c.fold((()=>l.x),(e=>{const t=Ko(e);return Ze(e,yt())?l.x:l.x-t.x})),u=Ce(o,Math.ceil(r.outerContainer.element.dom.getBoundingClientRect().width)).filter((e=>e>150)).map((e=>{const t=n.getOr(Po()),o=window.innerWidth-(d-t.left),s=Math.max(Math.min(e,o),150);return o<e&&Mt(r.outerContainer.element,"width",s+"px"),{width:s+"px"}})).getOr({width:"max-content"}),g={position:"absolute",left:Math.round(d)+"px",top:c.fold((()=>m()?Math.max(l.y-Ut(s.element)+i,0):l.bottom),(e=>{var t;const o=Ko(e),n=null!==(t=e.dom.scrollTop)&&void 0!==t?t:0,r=Ze(e,yt())?Math.max(l.y-Ut(s.element)+i,0):l.y-o.y+n-Ut(s.element)+i;return m()?r:l.bottom}))+"px"};Dt(r.outerContainer.element,{...g,...u})}))})(a,i),i.each((e=>{Uo(e.left,o.top)}))}c&&s.on(o),v()},w=()=>!(l||!c||!f())&&s.get().exists((o=>{const n=u.getDockingMode(),a=(o=>{switch(Wf(e)){case uF.auto:const e=YD.getToolbar(r.outerContainer),n=b(e),s=Ut(o.element)-n,a=Ko(t);if(a.y>s)return"top";{const e=nt(t),o=Math.max(e.dom.scrollHeight,Ut(e));return a.bottom<o-s||Zo().bottom<a.bottom-s?"bottom":"top"}case uF.bottom:return"bottom";case uF.top:default:return"top"}})(o);return a!==n&&(i=a,s.on((e=>{_i.setModes(e,[i]),u.setDockingMode(i);const t=m()?Xc.TopToBottom:Xc.BottomToTop;St(e.element,Kc,t)})),!0);var i}));return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),Mt(r.outerContainer.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),V(a,(e=>{Lt(e.element,"display")})),w(),Tb(e)?y((e=>_i.isDocked(e)?_i.reset(e):_i.refresh(e))):y(_i.refresh)},hide:()=>{h.set(!1),Mt(r.outerContainer.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus"),V(a,(e=>{Mt(e.element,"display","none")}))},update:y,updateMode:()=>{w()&&y(_i.reset)},repositionPopups:v}},pF=(e,t)=>{const o=Ko(e);return{pos:t?o.y:o.bottom,bounds:o}};var hF=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mainUi:r}=t,a=nn(),i=ze(s.targetNode),l=gF(e,i,t,n,a),c=Gf(e);sB(e);const d=()=>{if(a.isSet())return void l.show();a.set(YD.getHeader(r.outerContainer).getOrDie());const s=kb(e);Tb(e)?(mu(i,r.mothership),mu(i,t.popupUi.mothership)):uu(s,r.mothership),uu(s,t.dialogUi.mothership);const d=()=>{nF(e,t,o,n),YD.setMenubar(r.outerContainer,JD(e,o)),l.show(),((e,t,o,n)=>{const s=en(pF(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=pF(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&Zw(e,n),o.isVisible()&&(i!==r?o.update(_i.reset):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(_i.reset))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))}));let a=0;const i=E_((()=>o.update(_i.refresh)),33);e.on("ScrollWindow",(()=>{const e=Po().left;e!==a&&(a=e,i.throttle()),o.updateMode()})),Tb(e)&&e.on("ElementScroll",(e=>{o.update(_i.refresh)}));const l=on();l.set(Fc(ze(e.getBody()),"load",(e=>r(e.raw)))),e.on("remove",(()=>{l.clear()}))})(e,i,l,c),e.nodeChanged()};c?e.once("SkinLoaded",d):d()};e.on("show",d),e.on("hide",l.hide),c||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",(()=>{(e.hasFocus()||c)&&d()})),iw(e,t);const u={show:d,hide:l.hide,setEnabled:e=>{aw(t,!e)},isEnabled:()=>!mg.isDisabled(r.outerContainer)};return{editorContainer:r.outerContainer.element.dom,api:u}}});const fF="contexttoolbar-hide",bF=(e,t)=>jr(J_,((o,n)=>{const s=(e=>({hide:()=>Rr(e,br()),getValue:()=>$u.getValue(e)}))(e.get(o));t.onAction(s,n.event.buttonApi)})),vF=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=zb(Dy.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:da([vh.config({mode:"special",onEnter:e=>s.findPrimary(e).map((e=>(zr(e),!0))),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})),s=((e,t,o)=>{const n=L(t,(t=>zb(((e,t,o)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o)=>{const{primary:n,...s}=t.original,r=Jn(Dx({...s,type:"togglebutton",onAction:b}));return KB(r,o,[bF(e,t)])})(e,t,o):((e,t,o)=>{const{primary:n,...s}=t.original,r=Jn(Ex({...s,type:"button",onAction:b}));return XB(r,o,[bF(e,t)])})(e,t,o))(e,t,o))));return{asSpecs:()=>L(n,(e=>e.asSpec())),findPrimary:e=>se(t,((t,o)=>t.primary?A.from(n[o]).bind((t=>t.getOpt(e))).filter(k(mg.isDisabled)):A.none()))}})(n,e.commands,t);return[{title:A.none(),items:[n.asSpec()]},{title:A.none(),items:s.asSpecs()}]},yF=(e,t,o)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,xF=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=mt(ze(e.startContainer),e.startOffset).element;return(Ge(o)?rt(o):A.some(o)).filter($e).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Po();return Xo(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=Jo(ze(e.getBody()));return Xo(o.x+t.left,o.y+t.top,t.width,t.height)}},wF=(e,t,o,n=0)=>{const s=jo(window),r=Ko(ze(e.getContentAreaContainer())),a=pb(e)||vb(e)||xb(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Xo(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=ze(e.getContainer()),i=_l(a,".tox-editor-header").getOr(a),l=Ko(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Ko(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Xo(i,c,l,d-c)}},SF={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},CF={maxHeightFunction:Hc(),maxWidthFunction:JM()},kF=e=>"node"===e,OF=(e,t,o,n,s)=>{const r=xF(e),a=n.lastElement().exists((e=>Ze(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=mt(ze(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&Ze(n.element,t)})(e,o)?a?eA:XE:a?((e,o,s)=>{const a=Rt(e,"position");Mt(e,"position",o);const i=yF(r,Ko(t),-20)&&!n.isReposition()?oA:eA;return a.each((t=>Mt(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?s.y+Po().top:s.y)+(Ut(t)+12)<=r.y?XE:KE},_F=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...OF(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>kF(n)?[s(e)]:[];return t?{onLtr:e=>[ec,Xl,Kl,Jl,Ql,Zl].concat(r(e)),onRtl:e=>[ec,Kl,Xl,Ql,Jl,Zl].concat(r(e))}:{onLtr:e=>[Zl,ec,Jl,Xl,Ql,Kl].concat(r(e)),onRtl:e=>[Zl,ec,Ql,Kl,Jl,Xl].concat(r(e))}},TF=(e,t)=>{const o=P(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=H(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},EF=(e,t)=>{const o={},n=[],s=[],r={},a={},i=re(e);return V(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=Jn(Kn("ContextForm",Lx,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,Kn("ContextToolbar",Vx,o)).each((o=>{"editor"===t.scope?s.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},AF=Bi("forward-slide"),MF=Bi("backward-slide"),DF=Bi("change-slide-event"),BF="tox-pop--resizing",FF="tox-pop--transition",IF=(e,t,o,n)=>{const s=n.backstage,r=s.shared,a=Mo().deviceType.isTouch,i=nn(),l=nn(),c=nn(),d=hl((e=>{const t=en([]);return yf.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),yf.getContent(e).each((e=>{Lt(e.element,"visibility")})),Ca(e.element,BF),Lt(e.element,"width")},inlineBehaviours:da([Eh("context-toolbar-events",[Jr(sr(),((e,t)=>{"width"===t.event.raw.propertyName&&(Ca(e.element,BF),Lt(e.element,"width"))})),jr(DF,((e,t)=>{const o=e.element;Lt(o,"width");const n=Kt(o);yf.setContent(e,t.event.contents),wa(o,BF);const s=Kt(o);Mt(o,"width",n+"px"),yf.getContent(e).each((e=>{t.event.focus.bind((e=>(gc(e),bc(o)))).orThunk((()=>(vh.focusIn(e),fc(pt(o)))))})),setTimeout((()=>{Mt(e.element,"width",s+"px")}),0)})),jr(AF,((e,o)=>{yf.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:fc(pt(e.element))}]))})),Nr(e,DF,{contents:o.event.forwardContents,focus:A.none()})})),jr(MF,((e,o)=>{oe(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),Nr(e,DF,{contents:fl(o.bar),focus:o.focus})}))}))]),vh.config({mode:"special",onEscape:o=>oe(t.get()).fold((()=>e.onEscape()),(e=>(Rr(o,MF),A.some(!0))))})]),lazySink:()=>ln.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),A.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=kF(t)?1:0;return wF(e,r,t,o)},m=()=>!(e.removed||a()&&s.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=ye(c.get(),"node")?((e,t)=>t.filter((e=>vt(e)&&je(e))).map(Jo).getOrThunk((()=>xF(e))))(e,i.get()):xF(e);return t.height<=0||!yF(o,t,.01)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),yf.hide(d)},h=()=>{if(yf.isOpen(d)){const e=d.element;Lt(e,"display"),g()?Mt(e,"display","none"):(l.set(0),yf.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:da([vh.config({mode:"acyclic"}),Eh("pop-dialog-wrap-events",[Qr((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>vh.focusIn(t)))})),Zr((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=Qt((()=>EF(t,(e=>{const t=y([e]);Nr(d,AF,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...v().formNavigators},a=Pf(e)===xf.scrolling?xf.scrolling:xf.default,i=G(L(t,(t=>"contexttoolbar"===t.type?((t,o)=>oF(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,A.some(["form:"])))(s,t):((e,t)=>vF(e,t))(t,r.providers))));return SD({type:a,uid:Bi("context-toolbar"),initGroups:i,onEscape:A.none,cyclicKeying:!0,providers:r.providers})},x=(t,n)=>{if(S.cancel(),!m())return;const s=y(t),p=t[0].position,h=((t,n)=>{const s="node"===t?r.anchors.node(n):r.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:jc(12,0,SF),layouts:{onLtr:()=>[tc],onRtl:()=>[oc]},overrides:CF}:{bubble:jc(0,12,SF,1/12),layouts:_F(e,o,n,t),overrides:CF})(e,t,a(),{lastElement:i.get,isReposition:()=>ye(l.get(),0),getMode:()=>Zd.getMode(o)});return yn(s,c)})(p,n);c.set(p),l.set(1);const b=d.element;Lt(b,"display"),(e=>ye(we(e,i.get(),Ze),!0))(n)||(Ca(b,FF),Zd.reset(o,d)),yf.showWithinBounds(d,f(s),{anchor:h,transition:{classes:[FF],mode:"placement"}},(()=>A.some(u()))),n.fold(i.clear,i.set),g()&&Mt(b,"display","none")};let w=!1;const S=E_((()=>{!e.hasFocus()||e.removed||w||(ka(d.element,FF)?S.throttle():((e,t)=>{const o=ze(t.getBody()),n=e=>Ze(e,o),s=ze(t.selection.getNode());return(e=>!n(e)&&!et(o,e))(s)?A.none():((e,t,o)=>{const n=TF(e,t);if(n.contextForms.length>0)return A.some({elem:e,toolbars:[n.contextForms[0]]});{const t=TF(e,o);if(t.contextForms.length>0)return A.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>R(e,(e=>e.position===t)),o=t=>P(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=L(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return A.some({elem:e,toolbars:o})}return A.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?A.none():Rs(t,(e=>{if($e(e)){const{contextToolbars:t,contextForms:n}=TF(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>j(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>P(e,(e=>e.position===t))))}})(t);return s.length>0?A.some({elem:e,toolbars:s}):A.none()}return A.none()}),e))(n,s,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,A.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",S.throttle),e.on(fF,p),e.on("contexttoolbar-show",(t=>{const o=v();fe(o.lookupTable,t.toolbarKey).each((o=>{x([o],Ce(t.target!==e,t.target)),yf.getContent(d).each(vh.focusIn)}))})),e.on("focusout",(t=>{Sf.setEditorTimeout(e,(()=>{bc(o.element).isNone()&&bc(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("ExecCommand",(({command:e})=>{"toggleview"===e.toLowerCase()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&S.throttle()})),e.on("dragstart",(()=>{w=!0})),e.on("dragend drop",(()=>{w=!1})),e.on("NodeChange",(e=>{bc(d.element).fold(S.throttle,b)}))}))},RF=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=nn();return L(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(ye(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},NF=e=>{RF(e,(e=>({name:"lineheight",text:"Line height",icon:"line-height",getOptions:bb,hash:e=>((e,t)=>TB(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:w,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>A.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t),onToolbarSetup:nS(e),onMenuSetup:nS(e)}))(e)),(e=>A.from(Vf(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:x(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=ze(e.selection.getNode());return Ns(t,(e=>A.some(e).filter($e).bind((e=>Ot(e,"lang").map((t=>({code:t,customCode:Ot(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=on();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),oS(o.clear,nS(e)(t))},onMenuSetup:nS(e)}))))(e).each((t=>RF(e,t)))},zF=e=>rS(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent")&&e.selection.isEditable())})),LF=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),oS((()=>e.off("PastePlainTextToggle",n)),nS(e)(o))},VF=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},HF=e=>{(e=>{(e=>{T_.each([{name:"bold",text:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:sS(e,t.name),onAction:VF(e,t.name),shortcut:t.shortcut})}));for(let t=1;t<=6;t++){const o="h"+t,n=`Access+${t}`;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:sS(e,o),onAction:VF(e,o),shortcut:n})}})(e),(e=>{T_.each([{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"help",text:"Help",action:"mceHelp",icon:"help",shortcut:"Alt+0"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"print",text:"Print",action:"mcePrint",icon:"print",shortcut:"Meta+P"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:iS(e,t.action),shortcut:t.shortcut})})),T_.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:nS(e),onAction:iS(e,t.action)})}))})(e),(e=>{T_.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:iS(e,t.action),onSetup:sS(e,t.name)})}))})(e)})(e),(e=>{T_.each([{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:iS(e,t.action)})})),T_.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onSetup:nS(e),onAction:iS(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onSetup:nS(e),onAction:VF(e,"code")})})(e)},PF=(e,t)=>rS(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),UF=e=>rS(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),WF=(e,t)=>{(e=>{V([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:iS(e,t.cmd),onSetup:sS(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onSetup:nS(e),onAction:iS(e,"JustifyNone")})})(e),HF(e),((e,t)=>{((e,t)=>{const o=iB(t,pB(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),onSetup:nS(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=iB(t,kB(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),onSetup:nS(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=iB(t,zB(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",onSetup:nS(e),getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=iB(t,vB(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",onSetup:nS(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=iB(t,RB(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",onSetup:nS(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:PF(e,"hasUndo"),onAction:iS(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:PF(e,"hasRedo"),onAction:iS(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:PF(e,"hasUndo"),onAction:iS(e,"undo"),shortcut:"Meta+Z"}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:PF(e,"hasRedo"),onAction:iS(e,"redo"),shortcut:"Meta+Y"})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=_S(e),o=TS(e),n=en(t),s=en(o);zS(e,"forecolor","forecolor",n),zS(e,"backcolor","hilitecolor",s),LS(e,"forecolor","forecolor","Text color",n),LS(e,"backcolor","hilitecolor","Background color",s)})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:iS(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:UF(e),onAction:iS(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:zF(e),onAction:iS(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onSetup:nS(e),onAction:iS(e,"indent")})})(e)})(e),NF(e),(e=>{const t=en(lb(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:LF(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:LF(e,t)})})(e)},jF=e=>r(e)?e.split(/[ ,]/):e,$F=e=>t=>t.options.get(e),GF=$F("contextmenu_never_use_native"),qF=$F("contextmenu_avoid_overlap"),YF=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:P(o,(e=>be(t,e)))},XF=(e,t)=>({type:"makeshift",x:e,y:t}),KF=e=>"longpress"===e.type||0===e.type.indexOf("touch"),JF=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(KF(e)){const t=e.touches[0];return XF(t.pageX,t.pageY)}return XF(e.pageX,e.pageY)})(t):((e,t)=>{const o=Cf.DOM.getPos(e);return((e,t,o)=>XF(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(KF(e)){const t=e.touches[0];return XF(t.clientX,t.clientY)}return XF(e.clientX,e.clientY)})(t)):QF(e),QF=e=>({type:"selection",root:ze(e.selection.getNode())}),ZF=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:A.some(ze(e.selection.getNode())),root:ze(e.getBody())}))(e);case"point":return JF(e,t);case"selection":return QF(e)}},eI=(e,t,o,n,s,r)=>{const a=o(),i=ZF(e,t,r);uT(a,ly.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:A.none()}).map((e=>{t.preventDefault(),yf.showMenuAt(s,{anchor:i},{menu:{markers:Cy("normal")},data:e})}))},tI={onLtr:()=>[ec,Xl,Kl,Jl,Ql,Zl,XE,KE,YE,GE,qE,$E],onRtl:()=>[ec,Kl,Xl,Ql,Jl,Zl,XE,KE,qE,$E,YE,GE]},oI={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},nI=(e,t,o,n,s,r)=>{const a=Mo(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=ZF(e,t,o);return{bubble:jc(0,"point"===o?12:0,oI),layouts:tI,overrides:{maxWidthFunction:JM(),maxHeightFunction:Hc()},...n}})(e,t,r);uT(o,ly.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:A.none()}).map((o=>{t.preventDefault();const l=a?ff.HighlightMenuAndItem:ff.HighlightNone;yf.showMenuWithinBounds(s,{anchor:i},{menu:{markers:Cy("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>A.some(wF(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(fF)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{Sf.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return Ed(e.getWin(),bd.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},sI=e=>r(e)?"|"===e:"separator"===e.type,rI={type:"separator"},aI=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return rI;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:L(t,aI)}};default:const o=e;return{type:"menuitem",...t(o),onAction:v(o.onAction)}}},iI=(e,t)=>{if(0===t.length)return e;const o=oe(e).filter((e=>!sI(e))).fold((()=>[]),(e=>[rI]));return e.concat(o).concat(t).concat([rI])},lI=(e,t)=>!(e=>"longpress"===e.type||be(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),cI=(e,t)=>lI(e,t)?e.selection.getStart(!0):t.target,dI=(e,t,o)=>{const n=Mo().deviceType.isTouch,s=hl(yf.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:da([Eh("dismissContextMenu",[jr(_r(),((t,o)=>{Ou.close(t),e.focus()}))])])})),a=()=>yf.hide(s),i=t=>{if(GF(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!GF(e))(e,t)||(e=>0===YF(e).length)(e))return;const a=((e,t)=>{const o=qF(e),n=lI(e,t)?"selection":"point";if(Me(o)){const s=cI(e,t);return MC(ze(s),o)?"node":n}return n})(e,t);(n()?nI:eI)(e,t,(()=>{const o=cI(e,t),n=e.ui.registry.getAll(),s=YF(e);return((e,t,o)=>{const n=W(t,((t,n)=>fe(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n)&&Me(Ae(n)))return iI(t,n.split(" "));if(l(n)&&n.length>0){const e=L(n,aI);return iI(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&sI(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},uI=Ds([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),mI=e=>t=>t.translate(-e.left,-e.top),gI=e=>t=>t.translate(e.left,e.top),pI=e=>(t,o)=>W(e,((e,t)=>t(e)),$t(t,o)),hI=(e,t,o)=>e.fold(pI([gI(o),mI(t)]),pI([mI(t)]),pI([])),fI=(e,t,o)=>e.fold(pI([gI(o)]),pI([]),pI([gI(t)])),bI=(e,t,o)=>e.fold(pI([]),pI([mI(o)]),pI([gI(t),mI(o)])),vI=(e,t,o)=>{const n=e.fold(((e,t)=>({position:A.some("absolute"),left:A.some(e+"px"),top:A.some(t+"px")})),((e,t)=>({position:A.some("absolute"),left:A.some(e-o.left+"px"),top:A.some(t-o.top+"px")})),((e,t)=>({position:A.some("fixed"),left:A.some(e+"px"),top:A.some(t+"px")})));return{right:A.none(),bottom:A.none(),...n}},yI=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(bI,xI),s(fI,wI),s(hI,SI))},xI=uI.offset,wI=uI.absolute,SI=uI.fixed,CI=(e,t)=>{const o=kt(e,t);return u(o)?NaN:parseInt(o,10)},kI=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=CI(o,t.leftAttr),s=CI(o,t.topAttr);return isNaN(n)||isNaN(s)?A.none():A.some($t(n,s))})(e,t).fold((()=>o),(e=>SI(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?_I(e,t,a,s,r):TI(e,t,a,s,r),l=hI(a,s,r);return((e,t,o)=>{const n=e.element;St(n,t.leftAttr,o.left+"px"),St(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:SI(l.left,l.top),extra:A.none()})),(e=>({coord:e.output,extra:e.extra})))},OI=(e,t,o,n)=>se(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=fI(e,s,r),i=fI(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?A.some({output:yI(e.output,t,o,n),extra:e.extra}):A.none()})),_I=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return OI(r,o,n,s).orThunk((()=>{const e=W(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=fI(e,s,r),i=fI(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return $t(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:A.some(a),snap:A.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:A.some(a),snap:A.some(t)}:e))}),{deltas:A.none(),snap:A.none()});return e.snap.map((e=>({output:yI(e.output,o,n,s),extra:e.extra})))}))},TI=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return OI(r,o,n,s)};var EI=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=tt(e.element),o=Po(t),r=Da(s),a=((e,t,o)=>({coord:yI(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=vI(a.coord,0,r);Bt(s,i)}}});const AI="data-initial-z-index",MI=(e,t)=>{e.getSystem().addToGui(t),(e=>{rt(e.element).filter($e).each((t=>{Rt(t,"z-index").each((e=>{St(t,AI,e)})),Mt(t,"z-index",Ft(e.element,"z-index"))}))})(t)},DI=e=>{(e=>{rt(e.element).filter($e).each((e=>{Ot(e,AI).fold((()=>Lt(e,"z-index")),(t=>Mt(e,"z-index",t))),Tt(e,AI)}))})(e),e.getSystem().removeFromGui(e)},BI=(e,t,o)=>e.getSystem().build(HC.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var FI=ws("snaps",[rs("getSnapPoints"),vi("onSensor"),rs("leftAttr"),rs("topAttr"),Ss("lazyViewport",Zo),Ss("mustSnap",!1)]);const II=[Ss("useFixed",T),rs("blockerClass"),Ss("getTarget",w),Ss("onDrag",b),Ss("repositionTarget",!0),Ss("onDrop",b),Es("getBounds",Zo),FI],RI=e=>{return(t=Rt(e,"left"),o=Rt(e,"top"),n=Rt(e,"position"),t.isSome()&&o.isSome()&&n.isSome()?A.some(((e,t,o)=>("fixed"===o?SI:xI)(parseInt(e,10),parseInt(t,10)))(t.getOrDie(),o.getOrDie(),n.getOrDie())):A.none()).getOrThunk((()=>{const t=qt(e);return wI(t.left,t.top)}));var t,o,n},NI=(e,t)=>({bounds:e.getBounds(),height:Wt(t.element),width:Jt(t.element)}),zI=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>NI(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=tt(e.element),a=Po(r),i=Da(s),l=RI(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=fI(t,o,n),i=Mi(a.left,r.x,r.x+r.width-s.width),l=Mi(a.top,r.y,r.y+r.height-s.height),c=wI(i,l);return t.fold((()=>{const e=bI(c,o,n);return xI(e.left,e.top)}),x(c),(()=>{const e=hI(c,o,n);return SI(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>xI(e+a,t+i)),((e,t)=>wI(e+a,t+i)),((e,t)=>SI(e+a,t+i))));var t,a,i;const l=hI(e,n,s);return SI(l.left,l.top)}),(t=>{const a=kI(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=vI(c,0,i);Bt(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},LI=(e,t,o,n)=>{t.each(DI),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;Tt(o,t.leftAttr),Tt(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},VI=e=>(t,o)=>{const n=e=>{o.setStartData(NI(t,e))};return Pr([jr(Sr(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var HI=Object.freeze({__proto__:null,getData:e=>A.from($t(e.x,e.y)),getDelta:(e,t)=>$t(t.left-e.left,t.top-e.top)});const PI=(e,t,o)=>[jr($s(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>LI(n,A.some(l),e,t),a=DC(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),zI(n,e,t,HI,o)}},l=BI(n,e.blockerClass,(e=>Pr([jr($s(),e.forceDrop),jr(Ys(),e.drop),jr(Gs(),((t,o)=>{e.move(o.event)})),jr(qs(),e.delayDrop)]))(i));o(n),MI(n,l)}))],UI=[...II,Si("dragger",{handlers:VI(PI)})];var WI=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return A.some($t(t.clientX,t.clientY))})(t):A.none()},getDelta:(e,t)=>$t(t.left-e.left,t.top-e.top)});const jI=(e,t,o)=>{const n=nn(),s=o=>{LI(o,n.get(),e,t),n.clear()};return[jr(Ps(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{zI(r,e,t,WI,o)}},c=BI(r,e.blockerClass,(e=>Pr([jr(Ps(),e.forceDrop),jr(Ws(),e.drop),jr(js(),e.drop),jr(Us(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),MI(r,c)})),jr(Us(),((o,n)=>{n.stop(),zI(o,e,t,WI,n.event)})),jr(Ws(),((e,t)=>{t.stop(),s(e)})),jr(js(),s)]},$I=UI,GI=[...II,Si("dragger",{handlers:VI(jI)})],qI=[...II,Si("dragger",{handlers:VI(((e,t,o)=>[...PI(e,t,o),...jI(e,t,o)]))})];var YI=Object.freeze({__proto__:null,mouse:$I,touch:GI,mouseOrTouch:qI}),XI=Object.freeze({__proto__:null,init:()=>{let e=A.none(),t=A.none();const o=x({});return ca({readState:o,reset:()=>{e=A.none(),t=A.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=A.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=A.some(e)}})}});const KI=pa({branchKey:"mode",branches:YI,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:A.from(e.extra)})},state:XI,apis:EI}),JI=(e,t,o,n,s,r)=>e.fold((()=>KI.snap({sensor:wI(o-20,n-20),range:$t(s,r),output:wI(A.some(o),A.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return KI.snap({sensor:wI(s,r),range:$t(40,40),output:wI(A.some(o-a.width/2),A.some(n-a.height/2)),extra:{td:t}})})),QI=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>Ze(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),ZI=e=>zb(Rb.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:da([KI.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),fk.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),eR=(e,t)=>{const o=en([]),n=en([]),s=en(!1),r=nn(),a=nn(),i=e=>{const o=Jo(e);return JI(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=Jo(e);return JI(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=QI((()=>L(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=QI((()=>L(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=ZI(c),m=ZI(d),g=hl(u.asSpec()),p=hl(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);KI.snapTo(t,r),((t,o,n,r)=>{const a=o.dom.getBoundingClientRect();Lt(t.element,"display");const i=st(ze(e.getBody())).dom.innerHeight,l=a[s]<0,c=((e,t)=>e[s]>t)(a,i);(l||c)&&Mt(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");if(Mo().deviceType.isTouch()){const i=e=>L(e,ze);e.on("TableSelectionChange",(e=>{s.get()||(au(t,g),au(t,p),s.set(!0));const l=ze(e.start),c=ze(e.finish);r.set(l),a.set(c),A.from(e.otherCells).each((e=>{o.set(i(e.upOrLeftCells)),n.set(i(e.downOrRightCells)),f(l),b(c)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(cu(g),cu(p),s.set(!1)),r.clear(),a.clear()}))}},tR=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:da([vh.config({mode:"flow",selector:"div[role=button]"}),mg.config({disabled:o.isDisabled}),lw(),Hb.config({}),Th.config({}),Eh("elementPathEvents",[Qr(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>vh.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=tS(e,r);if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?W(r,((t,n,r)=>{const a=((t,n,s)=>Rb.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s}},components:[ul(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:da([Jb.config({...o.tooltips.getConfig({tooltipText:o.translate(["Select the {0} element",n.nodeName.toLowerCase()]),onShow:(e,t)=>{((e,t)=>{const o=A.from(kt(e,"id")).getOrThunk((()=>{const e=Bi("aria");return St(t,"id",e),e}));St(e,"aria-describedby",o)})(e.element,t.element)},onHide:e=>{var t;t=e.element,Tt(t,"aria-describedby")}})}),cw(o.isDisabled),lw()])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[ul(` ${s} `)]},a])}),[]):[];Th.set(t,a)}))}))])]),components:[]}};var oR;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(oR||(oR={}));const nR=(e,t,o)=>{const n=ze(e.getContainer()),s=((e,t,o,n,s)=>{const r={height:cF(n+t.top,Ff(e),Rf(e))};return o===oR.Both&&(r.width=cF(s+t.left,Bf(e),If(e))),r})(e,t,o,Ut(n),Kt(n));ie(s,((e,t)=>{h(e)&&Mt(n,t,lF(e))})),(e=>{e.dispatch("ResizeEditor")})(e)},sR=(e,t,o,n)=>{const s=$t(20*o,20*n);return nR(e,s,t),A.some(!0)},rR=(e,t)=>{const o=()=>{const o=[],n=ub(e),s=rb(e),r=ab(e)||e.hasPlugin("wordcount");return s&&o.push(tR(e,{},t)),n&&o.push((()=>{const e=xw("Alt+0");return{dom:{tag:"div",classes:["tox-statusbar__help-text"]},components:[ul(Yv.translate(["Press {0} for help",e]))]}})()),r&&o.push((()=>{const o=[];return e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>Th.set(e,[ul(t.translate(["{0} "+n,o[n]]))]);return Rb.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:da([cw(t.isDisabled),lw(),Hb.config({}),Th.config({}),$u.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Eh("wordcount-events",[ta((e=>{const t=$u.getValue(e),n="words"===t.mode?"characters":"words";$u.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Qr((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=$u.getValue(t);$u.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[gr()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),ab(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=poweredby&utm_source=tiny&utm_medium=referral&utm_content=v7",rel:"noopener",target:"_blank","aria-label":e.translate(["Build with {0}","TinyMCE"])},innerHtml:e.translate(["Build with {0}",'<svg height="16" viewBox="0 0 80 16" width="80" xmlns="http://www.w3.org/2000/svg"><g opacity=".8"><path d="m80 3.537v-2.202h-7.976v11.585h7.976v-2.25h-5.474v-2.621h4.812v-2.069h-4.812v-2.443zm-10.647 6.929c-.493.217-1.13.337-1.864.337s-1.276-.156-1.805-.47a3.732 3.732 0 0 1 -1.3-1.298c-.324-.554-.48-1.191-.48-1.877s.156-1.335.48-1.877a3.635 3.635 0 0 1 1.3-1.299 3.466 3.466 0 0 1 1.805-.481c.65 0 .914.06 1.263.18.36.12.698.277.986.47.289.192.578.384.842.6l.12.085v-2.586l-.023-.024c-.385-.35-.855-.614-1.384-.818-.53-.205-1.155-.313-1.877-.313-.721 0-1.6.144-2.333.445a5.773 5.773 0 0 0 -1.937 1.251 5.929 5.929 0 0 0 -1.324 1.9c-.324.735-.48 1.565-.48 2.455s.156 1.72.48 2.454c.325.734.758 1.383 1.324 1.913.553.53 1.215.938 1.937 1.25a6.286 6.286 0 0 0 2.333.434c.819 0 1.384-.108 1.961-.313.59-.216 1.083-.505 1.468-.866l.024-.024v-2.49l-.12.096c-.41.337-.878.626-1.396.866zm-14.869-4.15-4.8-5.04-.024-.025h-.902v11.67h2.502v-6.847l2.827 3.08.385.409.397-.41 2.791-3.067v6.845h2.502v-11.679h-.902l-4.788 5.052z"/><path clip-rule="evenodd" d="m15.543 5.137c0-3.032-2.466-5.113-4.957-5.137-.36 0-.745.024-1.094.096-.157.024-3.85.758-3.85.758-3.032.602-4.62 2.466-4.704 4.788-.024.89-.024 4.27-.024 4.27.036 3.165 2.406 5.138 5.017 5.126.337 0 1.119-.109 1.287-.145.144-.024.385-.084.746-.144.661-.12 1.684-.325 3.067-.602 2.37-.409 4.103-2.009 4.44-4.33.156-1.023.084-4.692.084-4.692zm-3.213 3.308-2.346.457v2.31l-5.859 1.143v-5.75l2.346-.458v3.441l3.513-.686v-3.44l-3.513.685v-2.297l5.859-1.143v5.75zm20.09-3.296-.083-1.023h-2.13v8.794h2.346v-4.884c0-1.107.95-1.985 2.057-1.997 1.095 0 1.901.89 1.901 1.997v4.884h2.346v-5.245c-.012-2.105-1.588-3.777-3.67-3.765a3.764 3.764 0 0 0 -2.778 1.25l.012-.011zm-6.014-4.102 2.346-.458v2.298l-2.346.457z" fill-rule="evenodd"/><path d="m28.752 4.126h-2.346v8.794h2.346z"/><path clip-rule="evenodd" d="m43.777 15.483 4.043-11.357h-2.418l-1.54 4.355-.445 1.324-.36-1.324-1.54-4.355h-2.418l3.151 8.794-1.083 3.08zm-21.028-5.51c0 .722.541 1.034.878 1.034s.638-.048.95-.144l.518 1.708c-.217.145-.879.518-2.13.518a2.565 2.565 0 0 1 -2.562-2.587c-.024-1.082-.024-2.49 0-4.21h-1.54v-2.142h1.54v-1.912l2.346-.458v2.37h2.201v2.142h-2.2v3.693-.012z" fill-rule="evenodd"/></g></svg>\n'.trim()])},behaviours:da([Fh.config({})])}]}),{dom:{tag:"div",classes:["tox-statusbar__right-container"]},components:o}})()),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container",...(()=>{const e="tox-statusbar__text-container--flex-start",t="tox-statusbar__text-container--flex-end";if(n){const o="tox-statusbar__text-container-3-cols";return r||s?r&&!s?[o,t]:[o,e]:[o,"tox-statusbar__text-container--space-around"]}return[r&&!s?t:e]})()]},components:o}]:[]};return{dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const n=o(),s=((e,t)=>{const o=(e=>{const t=ib(e);return!1===t?oR.None:"both"===t?oR.Both:oR.Vertical})(e);if(o===oR.None)return A.none();const n=o===oR.Both?"Press the arrow keys to resize the editor.":"Press the Up and Down arrow keys to resize the editor.";return A.some(ny("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{"aria-label":t.translate(n),"data-mce-name":"resize-handle"},behaviours:[KI.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>nR(e,s,o),blockerClass:"tox-blocker"}),vh.config({mode:"special",onLeft:()=>sR(e,o,-1,0),onRight:()=>sR(e,o,1,0),onUp:()=>sR(e,o,0,-1),onDown:()=>sR(e,o,0,1)}),Hb.config({}),Fh.config({}),Jb.config(t.tooltips.getConfig({tooltipText:t.translate("Resize")}))]},t.icons))})(e,t);return n.concat(s.toArray())})()}},aR=(e,t)=>t.get().getOrDie(`UI for ${e} has not been rendered`),iR=(e,t)=>{const o=e.inline,n=o?hF:aF,s=_b(e)?bM:iM,r=(()=>{const e=nn(),t=nn(),o=nn();return{dialogUi:e,popupUi:t,mainUi:o,getUiMotherships:()=>{const o=e.get().map((e=>e.mothership)),n=t.get().map((e=>e.mothership));return o.fold((()=>n.toArray()),(e=>n.fold((()=>[e]),(t=>Ze(e.element,t.element)?[e]:[e,t]))))},lazyGetInOuterOrDie:(e,t)=>()=>o.get().bind((e=>t(e.outerContainer))).getOrDie(`Could not find ${e} element in OuterContainer`)}})(),a=nn(),i=nn(),l=nn(),c=Mo().deviceType.isTouch()?["tox-platform-touch"]:[],d=wb(e),u=Pf(e),m=zb({dom:{tag:"div",classes:["tox-anchorbar"]}}),g=zb({dom:{tag:"div",classes:["tox-bottom-anchorbar"]}}),p=()=>r.mainUi.get().map((e=>e.outerContainer)).bind(YD.getHeader),h=r.lazyGetInOuterOrDie("anchor bar",m.getOpt),f=r.lazyGetInOuterOrDie("bottom anchor bar",g.getOpt),b=r.lazyGetInOuterOrDie("toolbar",YD.getToolbar),v=r.lazyGetInOuterOrDie("throbber",YD.getThrobber),y=((e,t,o,n)=>{const s=en(!1),r=(e=>{const t=en(wb(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),a={icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:Yv.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get,tooltips:SA(e.dialog)},i=qA(t),l=(e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},n=en([]),s=en([]),r=en(!1);return e.on("PreInit",(s=>{const r=yA(e),a=wA(e,r,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=wA(e,n.items,t,o);s.set(a),r.set(n.replace)})),{getData:()=>{const e=r.get()?[]:n.get(),t=s.get();return e.concat(t)}}})(t),c=(e=>({colorPicker:dA(e),hasCustomColors:uA(e),getColors:mA(e),getColorCols:gA(e)}))(t),d=(e=>({isDraggableModal:pA(e)}))(t),u={shared:{providers:a,anchors:cA(t,o,n,r.isPositionedAtTop),header:r},urlinput:i,styles:l,colorinput:c,dialog:d,isContextMenuOpen:()=>s.get(),setContextMenuState:e=>s.set(e)},m=e=>A.none(),g={...u,shared:{...u.shared,interpreter:e=>zE(e,{},g,m),getSink:e.popup}},p={...u,shared:{...u.shared,interpreter:e=>zE(e,{},p,m),getSink:e.dialog}};return{popup:g,dialog:p}})({popup:()=>ln.fromOption(r.popupUi.get().map((e=>e.sink)),"(popup) UI has not been rendered"),dialog:()=>ln.fromOption(r.dialogUi.get().map((e=>e.sink)),"UI has not been rendered")},e,h,f),x=()=>{const t=(()=>{const t={attributes:{[Kc]:d?Xc.BottomToTop:Xc.TopToBottom}},o=YD.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:y.popup,onEscape:()=>{e.focus()}}),n=YD.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:y.popup.shared.getSink,providers:y.popup.shared.providers,onEscape:()=>{e.focus()},onToolbarToggled:t=>{((e,t)=>{e.dispatch("ToggleToolbarDrawer",{state:t})})(e,t)},type:u,lazyToolbar:b,lazyHeader:()=>p().getOrDie("Could not find header element"),...t}),s=YD.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:y.popup.shared.providers,onEscape:()=>{e.focus()},type:u}),r=xb(e),a=vb(e),i=pb(e),l=db(e),c=YD.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]}}),g=r||a||i,h=l?[c,o]:[o];return YD.parts.header({dom:{tag:"div",classes:["tox-editor-header"].concat(g?[]:["tox-editor-header--empty"]),...t},components:G([i?h:[],r?[s]:a?[n]:[],Cb(e)?[]:[m.asSpec()]]),sticky:_b(e),editor:e,sharedBackstage:y.popup.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[YD.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),YD.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=YD.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:y.popup}),r=YD.parts.viewWrapper({backstage:y.popup}),i=sb(e)&&!o?A.some(rR(e,y.popup.shared.providers)):A.none(),l=G([d?[]:[t],o?[]:[n],d?[t]:[]]),h=YD.parts.editorContainer({components:G([l,o?[]:[g.asSpec()]])}),f=Ob(e),v={role:"application",...Yv.isRtl()?{dir:"rtl"}:{},...f?{"aria-hidden":"true"}:{}},x=hl(YD.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(o?["tox-tinymce-inline"]:[]).concat(d?["tox-tinymce--toolbar-bottom"]:[]).concat(c),styles:{visibility:"hidden",...f?{opacity:"0",border:"0"}:{}},attributes:v},components:[h,...o?[]:[r,...i.toArray()],s],behaviours:da([lw(),mg.config({disableClass:"tox-tinymce--disabled"}),vh.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=PC(x);return a.set(w),{mothership:w,outerContainer:x}},w=t=>{const o=lF((e=>{const t=(e=>{const t=Mf(e),o=Ff(e),n=Rf(e);return iF(t).map((e=>cF(e,o,n)))})(e);return t.getOr(Mf(e))})(e)),n=lF((e=>dF(e).getOr(Df(e)))(e));return e.inline||(zt("div","width",n)&&Mt(t.element,"width",n),zt("div","height",o)?Mt(t.element,"height",o):Mt(t.element,"height","400px")),o};return{popups:{backstage:y.popup,getMothership:()=>aR("popups",l)},dialogs:{backstage:y.dialog,getMothership:()=>aR("dialogs",i)},renderUI:()=>{const o=x(),a=(()=>{const t=kb(e),o=Ze(yt(),t)&&"grid"===Ft(t,"display"),n={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(c),attributes:{...Yv.isRtl()?{dir:"rtl"}:{}}},behaviours:da([Zd.config({useFixed:()=>s.isDocked(p)})])},r={dom:{styles:{width:document.body.clientWidth+"px"}},events:Pr([jr(Cr(),(e=>{Mt(e.element,"width",document.body.clientWidth+"px")}))])},a=hl(yn(n,o?r:{})),l=PC(a);return i.set(l),{sink:a,mothership:l}})(),d=Tb(e)?(()=>{const e={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-silver-popup-sink","tox-tinymce-aux"].concat(c),attributes:{...Yv.isRtl()?{dir:"rtl"}:{}}},behaviours:da([Zd.config({useFixed:()=>s.isDocked(p),getBounds:()=>t.getPopupSinkBounds()})])},o=hl(e),n=PC(o);return l.set(n),{sink:o,mothership:n}})():(e=>(l.set(e.mothership),e))(a);r.dialogUi.set(a),r.popupUi.set(d),r.mainUi.set(o);return(t=>{const{mainUi:o,popupUi:r,uiMotherships:a}=t;le(Uf(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:i,menuItems:l,contextToolbars:c,sidebars:d,views:m}=e.ui.registry.getAll(),g=yb(e),h={menuItems:l,menus:Eb(e),menubar:Yf(e),toolbar:g.getOrThunk((()=>Xf(e))),allowToolbarGroups:u===xf.floating,buttons:i,sidebar:d,views:m};var f;f=o.outerContainer,e.addShortcut("alt+F9","focus menubar",(()=>{YD.focusMenubar(f)})),e.addShortcut("alt+F10","focus toolbar",(()=>{YD.focusToolbar(f)})),e.addCommand("ToggleToolbarDrawer",((e,t)=>{(null==t?void 0:t.skipFocus)?YD.toggleToolbarDrawerWithoutFocusing(f):YD.toggleToolbarDrawer(f)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>YD.isToolbarDrawerToggled(f))),((e,t,o)=>{const n=(e,n)=>{V([t,...o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{V([t,...o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(_u(),{target:e.target}),a=$o(),i=Bc(a,"touchstart",r),l=Bc(a,"touchmove",(e=>n(xr(),e))),c=Bc(a,"touchend",(e=>n(wr(),e))),d=Bc(a,"mousedown",r),u=Bc(a,"mouseup",(e=>{0===e.raw.button&&s(Eu(),{target:e.target})})),m=e=>s(_u(),{target:ze(e.target)}),g=e=>{0===e.button&&s(Eu(),{target:ze(e.target)})},p=()=>{V(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(Sr(),Ic(e)),f=e=>{s(Tu(),{}),n(Cr(),Ic(e))},b=pt(ze(e.getElement())),v=Fc(b,"scroll",(o=>{requestAnimationFrame((()=>{if(null!=e.getContainer()){const s=Fb(e,t.element).map((e=>[e.element,...e.others])).getOr([]);R(s,(e=>Ze(e,o.target)))&&(e.dispatch("ElementScroll",{target:o.target.dom}),n(Mr(),o))}}))})),y=()=>s(Tu(),{}),x=t=>{t.state&&s(_u(),{target:ze(e.getContainer())})},w=e=>{s(_u(),{target:ze(e.relatedTarget.getContainer())})},S=t=>e.dispatch("focusin",t),C=t=>e.dispatch("focusout",t);e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",y),e.on("AfterProgressState",x),e.on("DismissPopups",w),V([t,...o],(e=>{e.element.dom.addEventListener("focusin",S),e.element.dom.addEventListener("focusout",C)}))})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",y),e.off("AfterProgressState",x),e.off("DismissPopups",w),V([t,...o],(e=>{e.element.dom.removeEventListener("focusin",S),e.element.dom.removeEventListener("focusout",C)})),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind(),v.unbind()})),e.on("detach",(()=>{V([t,...o],pu),V([t,...o],(e=>e.destroy()))}))})(e,o.mothership,a),s.setup(e,y.popup.shared,p),WF(e,y.popup),dI(e,y.popup.shared.getSink,y.popup),(e=>{const{sidebars:t}=e.ui.registry.getAll();V(re(t),(o=>{const n=t[o],s=()=>ye(A.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{t.setActive(s());const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),UM(e,v,y.popup.shared),IF(e,c,r.sink,{backstage:y.popup}),eR(e,r.sink);const b={targetNode:e.getElement(),height:w(o.outerContainer)};return n.render(e,t,h,y.popup,b)})({popupUi:d,dialogUi:a,mainUi:o,uiMotherships:r.getUiMotherships()})}}},lR=x([rs("lazySink"),ps("dragBlockClass"),Es("getBounds",Zo),Ss("useTabstopAt",E),Ss("firstTabstop",0),Ss("eventOrder",{}),Gu("modalBehaviours",[vh]),yi("onExecute"),wi("onEscape")]),cR={sketch:w},dR=x([wm({name:"draghandle",overrides:(e,t)=>({behaviours:da([KI.config({mode:"mouse",getTarget:e=>Cl(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),ym({schema:[rs("dom")],name:"title"}),ym({factory:cR,schema:[rs("dom")],name:"close"}),ym({factory:cR,schema:[rs("dom")],name:"body"}),wm({factory:cR,schema:[rs("dom")],name:"footer"}),xm({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[Ss("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),Ss("components",[])],name:"blocker"})]),uR=Ym({name:"ModalDialog",configFields:lR(),partFields:dR(),factory:(e,t,o,n)=>{const s=nn(),r=Bi("modal-events"),a={...e.eventOrder,[kr()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])},i=Mo();return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([fl(t)]),behaviours:da([Fh.config({}),Eh("dialog-blocker-events",[Jr(Ks(),(()=>{HM.isBlocked(t)||vh.focusIn(t)}))])])});au(o,a),vh.focusIn(t)},hide:e=>{s.clear(),rt(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{cu(e)}))}))},getBody:t=>Im(t,e,"body"),getFooter:t=>Fm(t,e,"footer"),setIdle:e=>{HM.unblock(e)},setBusy:(e,t)=>{HM.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Yu(e.modalBehaviours,[Th.config({}),vh.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt,firstTabstop:e.firstTabstop}),HM.config({getRoot:s.get}),Eh(r,[Qr((t=>{const o=Im(t,e,"title").element,n=(e=>e.dom.textContent)(o);i.os.isMacOS()&&g(n)?St(t.element,"aria-label",n):((e,t)=>{const o=Ot(e,"id").fold((()=>{const e=Bi("dialog-label");return St(t,"id",e),e}),w);St(e,"aria-labelledby",o)})(t.element,o)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),mR=In([Xy,Ky].concat(Gx)),gR=Wn,pR=[Cx("button"),dx,_s("align","end",["start","end"]),vx,bx,vs("buttonType",["primary","secondary"])],hR=[...pR,Qy],fR=[cs("type",["submit","cancel","custom"]),...hR],bR=[cs("type",["menu"]),lx,ux,dx,gs("items",mR),...pR],vR=[...pR,cs("type",["togglebutton"]),ux,dx,lx,Ts("active",!1)],yR=es("type",{submit:fR,cancel:fR,custom:fR,menu:bR,togglebutton:vR}),xR=[Xy,Qy,cs("level",["info","warn","error","success"]),ex,Ss("url","")],wR=In(xR),SR=[Xy,Qy,bx,Cx("button"),dx,fx,vs("buttonType",["primary","secondary","toolbar"]),vx],CR=In(SR),kR=[Xy,Ky],OR=kR.concat([mx]),_R=kR.concat([Jy,bx]),TR=In(_R),ER=Wn,AR=OR.concat([yx("auto")]),MR=In(AR),DR=Ln([tx,Qy,ex]),BR=OR.concat([Os("storageKey","default")]),FR=In(BR),IR=Un,RR=In(OR),NR=Un,zR=kR.concat([Os("tag","textarea"),ls("scriptId"),ls("scriptUrl"),ys("onFocus"),Cs("settings",void 0,Gn)]),LR=kR.concat([Os("tag","textarea"),ds("init")]),VR=Yn((e=>Kn("customeditor.old",Fn(LR),e).orThunk((()=>Kn("customeditor.new",Fn(zR),e))))),HR=Un,PR=In(OR),UR=Rn(En),WR=e=>[Xy,is("columns"),e],jR=[Xy,ls("html"),_s("presets","presentation",["presentation","document"]),Es("onInit",b),Ts("stretched",!1)],$R=In(jR),GR=OR.concat([Ts("border",!1),Ts("sandboxed",!0),Ts("streamContent",!1),Ts("transparent",!0)]),qR=In(GR),YR=Un,XR=In(kR.concat([bs("height")])),KR=In([ls("url"),fs("zoom"),fs("cachedWidth"),fs("cachedHeight")]),JR=OR.concat([bs("inputMode"),bs("placeholder"),Ts("maximized",!1),bx]),QR=In(JR),ZR=Un,eN=e=>[Xy,Jy,e,_s("align","start",["start","center","end"]),bs("for")],tN=[Qy,tx],oN=[Qy,gs("items",ts(0,(()=>nN)))],nN=Nn([In(tN),In(oN)]),sN=OR.concat([gs("items",nN),bx]),rN=In(sN),aN=Un,iN=OR.concat([ms("items",[Qy,tx]),ks("size",1),bx]),lN=In(iN),cN=Un,dN=OR.concat([Ts("constrain",!0),bx]),uN=In(dN),mN=In([ls("width"),ls("height")]),gN=kR.concat([Jy,ks("min",0),ks("max",0)]),pN=In(gN),hN=Pn,fN=[Xy,gs("header",Un),gs("cells",Rn(Un))],bN=In(fN),vN=OR.concat([bs("placeholder"),Ts("maximized",!1),bx]),yN=In(vN),xN=Un,wN=[cs("type",["directory","leaf"]),Zy,ls("id"),hs("menu",xM)],SN=In(wN),CN=wN.concat([gs("children",ts(0,(()=>qn("type",{directory:kN,leaf:SN}))))]),kN=In(CN),ON=qn("type",{directory:kN,leaf:SN}),_N=[Xy,gs("items",ON),ys("onLeafAction"),ys("onToggleExpand"),As("defaultExpandedIds",[],Un),bs("defaultSelectedId")],TN=In(_N),EN=OR.concat([_s("filetype","file",["image","media","file"]),bx,bs("picker_text")]),AN=In(EN),MN=In([tx,xx]),DN=e=>os("items","items",{tag:"required",process:{}},Rn(Yn((t=>Kn(`Checking item of ${e}`,BN,t).fold((e=>ln.error(Zn(e))),(e=>ln.value(e))))))),BN=Dn((()=>{return qn("type",{alertbanner:wR,bar:In((e=DN("bar"),[Xy,e])),button:CR,checkbox:TR,colorinput:FR,colorpicker:RR,dropzone:PR,grid:In(WR(DN("grid"))),iframe:qR,input:QR,listbox:rN,selectbox:lN,sizeinput:uN,slider:pN,textarea:yN,urlinput:AN,customeditor:VR,htmlpanel:$R,imagepreview:XR,collection:MR,label:In(eN(DN("label"))),table:bN,tree:TN,panel:IN});var e})),FN=[Xy,Ss("classes",[]),gs("items",BN)],IN=In(FN),RN=[Cx("tab"),Zy,gs("items",BN)],NN=[Xy,ms("tabs",RN)],zN=In(NN),LN=hR,VN=yR,HN=In([ls("title"),as("body",qn("type",{panel:IN,tabpanel:zN})),Os("size","normal"),As("buttons",[],VN),Ss("initialData",{}),Es("onAction",b),Es("onChange",b),Es("onSubmit",b),Es("onClose",b),Es("onCancel",b),Es("onTabChange",b)]),PN=In([cs("type",["cancel","custom"]),...LN]),UN=In([ls("title"),ls("url"),fs("height"),fs("width"),xs("buttons",PN),Es("onAction",b),Es("onCancel",b),Es("onClose",b),Es("onMessage",b)]),WN=e=>a(e)?[e].concat(q(he(e),WN)):l(e)?q(e,WN):[],jN=e=>r(e.type)&&r(e.name),$N={checkbox:ER,colorinput:IR,colorpicker:NR,dropzone:UR,input:ZR,iframe:YR,imagepreview:KR,selectbox:cN,sizeinput:mN,slider:hN,listbox:aN,size:mN,textarea:xN,urlinput:MN,customeditor:HR,collection:DR,togglemenuitem:gR},GN=e=>{const t=(e=>P(WN(e),jN))(e),o=q(t,(e=>(e=>A.from($N[e.type]))(e).fold((()=>[]),(t=>[as(e.name,t)]))));return In(o)},qN=e=>{var t;return{internalDialog:Jn(Kn("dialog",HN,e)),dataValidator:GN(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},YN={open:(e,t)=>{const o=qN(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Jn(Kn("dialog",UN,t))),redial:e=>qN(e)};var XN=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?xh:yh)(o,r)}))};return Pr([jr(mr(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;I(s.channels,n)&&o(t,s.data)}})),Qr(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),KN=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),JN=[rs("channel"),ps("renderComponents"),ps("updateState"),ps("initialData"),Ts("reuseDom",!0)];const QN=ma({fields:JN,name:"reflecting",active:XN,apis:KN,state:Object.freeze({__proto__:null,init:()=>{const e=en(A.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(A.none())}}})}),ZN=e=>{const t=[],o={};return ie(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?ln.error(t):ln.value(o)},ez=(e,t,o,n)=>{const s=zb(c_.sketch((s=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:L(e.items,(e=>RE(s,e,t,o,n)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[s.asSpec()]}],behaviours:da([vh.config({mode:"acyclic",useTabstopAt:k(R_)}),(r=s,Qm.config({find:r.getOpt})),x_(s,{postprocess:e=>ZN(e).fold((e=>(console.error(e),{})),w)}),Eh("dialog-body-panel",[jr(Ks(),((e,t)=>{e.getSystem().broadcastOn([P_],{newFocus:A.some(t.event.target)})}))])])};var r},tz=qm({name:"TabButton",configFields:[Ss("uid",void 0),rs("value"),os("dom","dom",Cn((()=>({attributes:{role:"tab",id:Bi("aria"),"aria-selected":"false"}}))),Vn()),ps("action"),Ss("domModification",{}),Gu("tabButtonBehaviours",[Fh,vh,$u]),rs("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:Wh(e.action),behaviours:Yu(e.tabButtonBehaviours,[Fh.config({}),vh.config({mode:"execution",useSpace:!0,useEnter:!0}),$u.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),oz=x([rs("tabs"),rs("dom"),Ss("clickToDismiss",!1),Gu("tabbarBehaviours",[Sg,vh]),fi(["tabClass","selectedClass"])]),nz=Sm({factory:tz,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Sg.dehighlight(e,t),Nr(e,Br(),{tabbar:e,button:t})},o=(e,t)=>{Sg.highlight(e,t),Nr(e,Dr(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Sg.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),sz=x([nz]),rz=Ym({name:"Tabbar",configFields:oz(),partFields:sz(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Yu(e.tabbarBehaviours,[Sg.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{St(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{St(t.element,"aria-selected","false")}}),vh.config({mode:"flow",getInitial:e=>Sg.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),az=qm({name:"Tabview",configFields:[Gu("tabviewBehaviours",[Th])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:Yu(e.tabviewBehaviours,[Th.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),iz=x([Ss("selectFirst",!0),vi("onChangeTab"),vi("onDismissTab"),Ss("tabs",[]),Gu("tabSectionBehaviours",[])]),lz=ym({factory:rz,schema:[rs("dom"),us("markers",[rs("tabClass"),rs("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),cz=ym({factory:az,name:"tabview"}),dz=x([lz,cz]),uz=Ym({name:"TabSection",configFields:iz(),partFields:dz(),factory:(e,t,o,n)=>{const s=(t,o)=>{Fm(t,e,"tabbar").each((e=>{o(e).each(zr)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:qu(e.tabSectionBehaviours),events:Pr(G([e.selectFirst?[Qr(((e,t)=>{s(e,Sg.getFirst)}))]:[],[jr(Dr(),((t,o)=>{(t=>{const o=$u.getValue(t);Fm(t,e,"tabview").each((n=>{j(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();Ot(t.element,"id").each((e=>{St(n.element,"aria-labelledby",e)})),Th.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),jr(Br(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>Fm(t,e,"tabview").map((e=>Th.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Sg.getCandidates(e);return j(o,(e=>$u.getValue(e)===t)).filter((t=>!Sg.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),mz=(e,t)=>{Mt(e,"height",t+"px"),Mt(e,"flex-basis",t+"px")},gz=(e,t,o)=>{Cl(e,'[role="dialog"]').each((e=>{_l(e,'[role="tablist"]').each((n=>{o.get().map((o=>(Mt(t,"height","0"),Mt(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=nt(e).dom,s=Cl(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===Ft(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=Ut(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Kt(o)?Math.max(Ut(o),a):a,l=parseInt(Ft(e,"margin-top"),10)||0,c=parseInt(Ft(e,"margin-bottom"),10)||0;return r-(Ut(e)+l+c-i)})(e,t,n))))).each((e=>{mz(t,e)}))}))}))},pz=e=>_l(e,'[role="tabpanel"]'),hz="send-data-to-section",fz="send-data-to-view",bz=(e,t,o,n)=>{const s=en({}),r=e=>{const t=$u.getValue(e),o=ZN(t).getOr({}),n=s.get(),r=yn(n,o);s.set(r)},a=e=>{const t=s.get();$u.setValue(e,t)},i=en(null),l=L(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[ul(o.shared.providers.translate(e.title))],view:()=>[c_.sketch((s=>({dom:{tag:"div",classes:["tox-form"]},components:L(e.items,(e=>RE(s,e,t,o,n))),formBehaviours:da([vh.config({mode:"acyclic",useTabstopAt:k(R_)}),Eh("TabView.form.events",[Qr(a),Zr(r)]),uc.config({channels:Fs([{key:hz,value:{onReceive:r}},{key:fz,value:{onReceive:a}}])})])})))]}))),c=(e=>{const t=nn(),o=[Qr((o=>{const n=o.element;pz(n).each((s=>{Mt(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>L(e,((n,s)=>{Th.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return Th.set(o,[]),r.height})))(e,s,o),r=(e=>te(Z(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),gz(n,s,t),Lt(s,"visibility"),((e,t)=>{te(e).each((e=>uz.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{gz(n,s,t)}))}))})),jr(Cr(),(e=>{const o=e.element;pz(o).each((e=>{gz(o,e,t)}))})),jr(sk,((e,o)=>{const n=e.element;pz(n).each((e=>{const o=fc(pt(e));Mt(e,"visibility","hidden");const s=Rt(e,"height").map((e=>parseInt(e,10)));Lt(e,"height"),Lt(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),gz(n,e,t)):s.each((t=>{mz(e,t)})),Lt(e,"visibility"),o.each(gc)}))}))];return{extraEvents:o,selectFirst:!1}})(l);return uz.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=$u.getValue(t);Nr(e,nk,{name:n,oldName:i.get()}),i.set(n)},tabs:l,components:[uz.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[rz.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:da([Hb.config({})])}),uz.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:c.selectFirst,tabSectionBehaviours:da([Eh("tabpanel",c.extraEvents),vh.config({mode:"acyclic"}),Qm.config({find:e=>te(uz.getViewItems(e))}),w_(A.none(),(e=>(e.getSystem().broadcastOn([hz],{}),s.get())),((e,t)=>{s.set(t),e.getSystem().broadcastOn([fz],{})}))])})},vz=(e,t,o,n,s,r)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:da([v_(0),QN.config({channel:`${L_}-${t}`,updateState:(e,t)=>A.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[bz(t,e.initialData,n,r)]:[ez(t,e.initialData,n,r)]},initialData:e})])}),yz=Of.deviceType.isTouch(),xz=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),wz=(e,t)=>uR.parts.close(Rb.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:da([Hb.config({})])})),Sz=()=>uR.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),Cz=(e,t)=>uR.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:Nb(`<p>${qv(t.translate(e))}</p>`)}]}]}),kz=e=>uR.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),Oz=(e,t)=>[HC.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),HC.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],_z=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return uR.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),A.some(!0)),useTabstopAt:e=>!R_(e),firstTabstop:e.firstTabstop,dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:Nb(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:yz?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:da([Fh.config({}),Eh("dialog-events",e.dialogEvents.concat([Jr(Ks(),((e,t)=>{HM.isBlocked(e)||vh.focusIn(e)})),jr(Er(),((e,t)=>{e.getSystem().broadcastOn([P_],{newFocus:t.event.newFocus})}))])),Eh("scroll-lock",[Qr((()=>{wa(yt(),s)})),Zr((()=>{Ca(yt(),s)}))]),...e.extraBehaviours]),eventOrder:{[gr()]:["dialog-events"],[kr()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[Or()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},Tz=e=>Rb.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),"data-mce-name":"close"}},buttonBehaviours:da([Hb.config({}),Jb.config(e.tooltips.getConfig({tooltipText:e.translate("Close")}))]),components:[ny("close",{tag:"span",classes:["tox-icon"]},e.icons)],action:e=>{Rr(e,QC)}}),Ez=(e,t,o,n)=>({dom:{tag:"h1",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:da([QN.config({channel:`${z_}-${t}`,initialData:e,renderComponents:e=>[ul(n.translate(e.title))]})])}),Az=()=>({dom:Nb('<div class="tox-dialog__draghandle"></div>')}),Mz=(e,t,o)=>((e,t,o)=>{const n=uR.parts.title(Ez(e,t,A.none(),o)),s=uR.parts.draghandle(Az()),r=uR.parts.close(Tz(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return HC.sketch({dom:Nb('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),Dz=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:`${n.getOr(0)}px`,position:"absolute"}},behaviours:t,components:[{dom:Nb('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),Bz=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{const n=_l(e().element,".tox-dialog__header").map((e=>Ut(e)));uR.setBusy(e(),((e,s)=>Dz(o.message,s,t,n)))},onUnblock:()=>{uR.setIdle(e())}}),Fz="tox-dialog--fullscreen",Iz="tox-dialog--width-lg",Rz="tox-dialog--width-md",Nz=e=>{switch(e){case"large":return A.some(Iz);case"medium":return A.some(Rz);default:return A.none()}},zz=(e,t)=>{const o=ze(t.element.dom);ka(o,Fz)||(_a(o,[Iz,Rz]),Nz(e).each((e=>wa(o,e))))},Lz=(e,t)=>{const o=ze(e.element.dom),n=Ta(o),s=j(n,(e=>e===Iz||e===Rz)).or(Nz(t));((e,t)=>{V(t,(t=>{((e,t)=>{const o=ba(e)?e.dom.classList.toggle(t):((e,t)=>I(va(e),t)?xa(e,t):ya(e,t))(e,t);Sa(e)})(e,t)}))})(o,[Fz,...s.toArray()])},Vz=(e,t,o)=>hl(_z({...e,firstTabstop:1,lazySink:o.shared.getSink,extraBehaviours:[C_({}),...e.extraBehaviours],onEscape:e=>{Rr(e,QC)},dialogEvents:t,eventOrder:{[mr()]:[QN.name(),uc.name()],[kr()]:["scroll-lock",QN.name(),"messages","dialog-events","alloy.base.behaviour"],[Or()]:["alloy.base.behaviour","dialog-events","messages",QN.name(),"scroll-lock"]}})),Hz=(e,t={})=>L(e,(e=>"menu"===e.type?(e=>{const o=L(e.items,(e=>{const o=fe(t,e.name).getOr(en(!1));return{...e,storage:o}}));return{...e,items:o}})(e):e)),Pz=e=>W(e,((e,t)=>"menu"===t.type?W(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),Uz=(e,t)=>[Yr(Ks(),I_),e(JC,((e,o,n,s)=>{fc(pt(s.element)).fold(b,pc),t.onClose(),o.onClose()})),e(QC,((e,t,o,n)=>{t.onCancel(e),Rr(n,JC)})),jr(ok,((e,o)=>t.onUnblock())),jr(tk,((e,o)=>t.onBlock(o.event)))],Wz=(e,t,o)=>{const n=(t,o)=>jr(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{QN.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...Uz(n,t),n(ek,((e,t)=>t.onSubmit(e))),n(KC,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(ZC,((e,t,n,s)=>{const r=()=>s.getSystem().isConnected()?vh.focusIn(s):void 0,a=e=>_t(e,"disabled")||Ot(e,"aria-disabled").exists((e=>"true"===e)),i=pt(s.element),l=fc(i);t.onAction(e,{name:n.name,value:n.value}),fc(i).fold(r,(e=>{a(e)||l.exists((t=>et(e,t)&&a(t)))?r():o().toOptional().filter((t=>!et(t.element,e))).each(r)}))})),n(nk,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Zr((t=>{const o=e();$u.setValue(t,o.getData())}))]},jz=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=H(o,(e=>"start"===e.align)),s=(e,t)=>HC.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:L(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},$z=(e,t,o)=>({dom:Nb('<div class="tox-dialog__footer"></div>'),components:[],behaviours:da([QN.config({channel:`${V_}-${t}`,initialData:e,updateState:(e,t)=>{const n=L(t.buttons,(e=>{const t=zb(((e,t)=>yE(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return A.some({lookupByName:t=>((e,t,o)=>j(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:jz})])}),Gz=(e,t,o)=>uR.parts.footer($z(e,t,o)),qz=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=Qm.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return c_.getField(o,t).orThunk((()=>{const o=e.getFooter().bind((e=>QN.getState(e).get()));return o.bind((e=>e.lookupByName(t)))}))}return A.none()},Yz=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...$u.getValue(n),...le(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=yn(r,t),i=((e,t)=>{const o=e.getRoot();return QN.getState(o).get().map((e=>Jn(Kn("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();$u.setValue(l,i),ie(o,((e,t)=>{be(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{qz(e,t).each(o?mg.enable:mg.disable)},focus:t=>{qz(e,t).each(Fh.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Nr(t,tk,{message:e})}))},unblock:()=>{n((e=>{Rr(e,ok)}))},showTab:t=>{n((o=>{const n=e.getBody();QN.getState(n).get().exists((e=>e.isTabPanel()))&&Qm.getCurrent(n).each((e=>{uz.showTab(e,t)}))}))},redial:r=>{n((n=>{const a=e.getId(),i=t(r),l=Hz(i.internalDialog.buttons,o);n.getSystem().broadcastOn([`${N_}-${a}`],i),n.getSystem().broadcastOn([`${z_}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${L_}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${V_}-${a}`],{...i.internalDialog,buttons:l}),s.setData(i.initialData)}))},close:()=>{n((e=>{Rr(e,JC)}))},toggleFullscreen:e.toggleFullscreen};return s},Xz=(e,t,o,n=!1,s)=>{const r=Bi("dialog"),a=Bi("dialog-label"),i=Bi("dialog-content"),l=e.internalDialog,c=en(l.size),d=Nz(c.get()).toArray(),u=zb(((e,t,o,n)=>HC.sketch({dom:Nb('<div class="tox-dialog__header"></div>'),components:[Ez(e,t,A.some(o),n),Az(),Tz(n)],containerBehaviours:da([KI.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>Tl(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:l.title,draggable:!0},r,a,o.shared.providers)),m=zb(((e,t,o,n,s,r)=>vz(e,t,A.some(o),n,s,r))({body:l.body,initialData:l.initialData},r,i,o,n,(e=>qz(y,e)))),g=Hz(l.buttons),p=Pz(g),h=Ce(0!==g.length,zb(((e,t,o)=>$z(e,t,o))({buttons:g},r,o))),f=Wz((()=>w),{onBlock:e=>{HM.block(v,((t,n)=>{const s=u.getOpt(v).map((e=>Ut(e.element)));return Dz(e.message,n,o.shared.providers,s)}))},onUnblock:()=>{HM.unblock(v)},onClose:()=>t.closeWindow()},o.shared.getSink),b=Mo().os,v=hl({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline",...d],attributes:{role:"dialog",...b.isMacOS()?{"aria-label":l.title}:{"aria-labelledby":a}}},eventOrder:{[mr()]:[QN.name(),uc.name()],[gr()]:["execute-on-form"],[kr()]:["reflecting","execute-on-form"]},behaviours:da([vh.config({mode:"cyclic",onEscape:e=>(Rr(e,JC),A.some(!0)),useTabstopAt:e=>!R_(e)&&("button"!==Ue(e)||"disabled"!==kt(e,"disabled")),firstTabstop:1}),QN.config({channel:`${N_}-${r}`,updateState:(e,t)=>(c.set(t.internalDialog.size),zz(t.internalDialog.size,e),s(),A.some(t)),initialData:e}),Fh.config({}),Eh("execute-on-form",f.concat([Jr(Ks(),((e,t)=>{vh.focusIn(e)})),jr(Er(),((e,t)=>{e.getSystem().broadcastOn([P_],{newFocus:t.event.newFocus})}))])),HM.config({getRoot:()=>A.some(v)}),Th.config({}),C_({})]),components:[u.asSpec(),m.asSpec(),...h.map((e=>e.asSpec())).toArray()]}),y={getId:x(r),getRoot:x(v),getFooter:()=>h.map((e=>e.get(v))),getBody:()=>m.get(v),getFormWrapper:()=>{const e=m.get(v);return Qm.getCurrent(e).getOr(e)},toggleFullscreen:()=>{Lz(v,c.get())}},w=Yz(y,t.redial,p);return{dialog:v,instanceApi:w}};var Kz=tinymce.util.Tools.resolve("tinymce.util.URI");const Jz=["insertContent","setContent","execCommand","close","block","unblock"],Qz=e=>a(e)&&-1!==Jz.indexOf(e.mceAction),Zz=(e,t,o,n)=>{const s=Bi("dialog"),i=Mz(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[B_(A.none(),{dom:{tag:"iframe",attributes:{src:e.url}},behaviours:da([Hb.config({}),Fh.config({})])})]}],behaviours:da([vh.config({mode:"acyclic",useTabstopAt:k(R_)})])};return uR.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?A.none():A.some(Gz({buttons:e},s,n)))),u=((e,t)=>{const o=(e,t)=>jr(e,((e,o)=>{n(e,((n,s)=>{t(x,n,o.event,e)}))})),n=(e,t)=>{QN.getState(e).get().each((o=>{t(o,e)}))};return[...Uz(o,t),o(ZC,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})(0,Bz((()=>y),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new Kz(e.url,{base_uri:new Kz(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=on(),v=[QN.config({channel:`${N_}-${s}`,updateState:(e,t)=>A.some(t),initialData:e}),Eh("messages",[Qr((()=>{const t=Bc(ze(window),"message",(t=>{if(h.isSameOrigin(new Kz(t.raw.origin))){const n=t.raw.data;Qz(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,x,n):(e=>!Qz(e)&&a(e)&&be(e,"mceAction"))(n)&&e.onMessage(x,n)}}));b.set(t)})),Zr(b.clear)]),uc.config({channels:{[H_]:{onReceive:(e,t)=>{_l(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],y=Vz({id:s,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},u,n),x=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Nr(t,tk,{message:e})}))},unblock:()=>{t((e=>{Rr(e,ok)}))},close:()=>{t((e=>{Rr(e,JC)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([H_],e)}))}}})(y);return{dialog:y,instanceApi:x}},eL=(e,t)=>Jn(Kn("data",t,e)),tL=e=>MC(e,".tox-alert-dialog")||MC(e,".tox-confirm-dialog"),oL=(e,t,o)=>t&&o?[]:[_i.config({contextual:{lazyContext:()=>A.some(Ko(ze(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"],lazyViewport:t=>Fb(e,t.element).map((e=>({bounds:Ib(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:qt(e.element).top})}))).getOrThunk((()=>({bounds:Zo(),optScrollEnv:A.none()})))})],nL=e=>{const t=e.editor,o=_b(t),n=(e=>{const t=e.shared;return{open:(o,n)=>{const s=()=>{uR.hide(l),n()},r=zb(yE({name:"close-alert",text:"OK",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"cancel",e)),a=Sz(),i=wz(s,t.providers),l=hl(_z({lazySink:()=>t.getSink(),header:xz(a,i),body:Cz(o,t.providers),footer:A.some(kz(Oz([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[jr(QC,s)],eventOrder:{}}));uR.show(l);const c=r.get(l);Fh.focus(c)}}})(e.backstages.dialog),s=(e=>{const t=e.shared;return{open:(o,n)=>{const s=e=>{uR.hide(c),n(e)},r=zb(yE({name:"yes",text:"Yes",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"submit",e)),a=yE({name:"no",text:"No",primary:!1,buttonType:A.some("secondary"),align:"end",enabled:!0,icon:A.none()},"cancel",e),i=Sz(),l=wz((()=>s(!1)),t.providers),c=hl(_z({lazySink:()=>t.getSink(),header:xz(i,l),body:Cz(o,t.providers),footer:A.some(kz(Oz([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[jr(QC,(()=>s(!1))),jr(ek,(()=>s(!0)))],eventOrder:{}}));uR.show(c);const d=r.get(c);Fh.focus(d)}}})(e.backstages.dialog),r=(t,o)=>YN.open(((t,n,s)=>{const r=n,a=((e,t,o)=>{const n=Bi("dialog"),s=e.internalDialog,r=Mz(s.title,n,o),a=en(s.size),i=Nz(a.get()).toArray(),l=((e,t,o,n)=>{const s=vz(e,t,A.none(),o,!1,n);return uR.parts.body(s)})({body:s.body,initialData:s.initialData},n,o,(e=>qz(h,e))),c=Hz(s.buttons),d=Pz(c),u=Ce(0!==c.length,Gz({buttons:c},n,o)),m=Wz((()=>f),Bz((()=>p),o.shared.providers,t),o.shared.getSink),g={id:n,header:r,body:l,footer:u,extraClasses:i,extraBehaviours:[QN.config({channel:`${N_}-${n}`,updateState:(e,t)=>(a.set(t.internalDialog.size),zz(t.internalDialog.size,e),A.some(t)),initialData:e})],extraStyles:{}},p=Vz(g,m,o),h={getId:x(n),getRoot:x(p),getBody:()=>uR.getBody(p),getFooter:()=>uR.getFooter(p),getFormWrapper:()=>{const e=uR.getBody(p);return Qm.getCurrent(e).getOr(e)},toggleFullscreen:()=>{Lz(p,a.get())}},f=Yz(h,t.redial,d);return{dialog:p,instanceApi:f}})({dataValidator:s,initialData:r,internalDialog:t},{redial:YN.redial,closeWindow:()=>{uR.hide(a.dialog),o(a.instanceApi)}},e.backstages.dialog);return uR.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),t),a=(n,s,r,a)=>YN.open(((n,i,l)=>{const c=eL(i,l),d=nn(),u=e.backstages.popup.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{yf.reposition(e),o&&u||_i.refresh(e)})),g=Xz({dataValidator:l,initialData:c,internalDialog:n},{redial:YN.redial,closeWindow:()=>{d.on(yf.hide),t.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},e.backstages.popup,a.ariaAttrs,m),p=hl(yf.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:a.persistent?{event:"doNotDismissYet"}:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:da([Eh("window-manager-inline-events",[jr(_r(),((e,t)=>{Rr(g.dialog,QC)}))]),...oL(t,o,u)]),isExtraPart:(e,t)=>tL(t)}));return d.set(p),yf.showWithinBounds(p,fl(g.dialog),{anchor:s},(()=>{const e=t.inline?yt():ze(t.getContainer()),o=Ko(e);return A.some(o)})),o&&u||(_i.refresh(p),t.on("ResizeEditor",m)),g.instanceApi.setData(c),vh.focusIn(g.dialog),g.instanceApi}),n),i=(o,n,s,r)=>YN.open(((o,a,i)=>{const l=eL(a,i),c=nn(),d=e.backstages.popup.shared.header.isPositionedAtTop(),u=()=>c.on((e=>{yf.reposition(e),_i.refresh(e)})),m=Xz({dataValidator:i,initialData:l,internalDialog:o},{redial:YN.redial,closeWindow:()=>{c.on(yf.hide),t.off("ResizeEditor ScrollWindow ElementScroll",u),c.clear(),s(m.instanceApi)}},e.backstages.popup,r.ariaAttrs,u),g=hl(yf.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:r.persistent?{event:"doNotDismissYet"}:{},...d?{}:{fireRepositionEventInstead:{}},inlineBehaviours:da([Eh("window-manager-inline-events",[jr(_r(),((e,t)=>{Rr(m.dialog,QC)}))]),_i.config({contextual:{lazyContext:()=>A.some(Ko(ze(t.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top","bottom"],lazyViewport:e=>Fb(t,e.element).map((e=>({bounds:Ib(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:qt(e.element).top})}))).getOrThunk((()=>({bounds:Zo(),optScrollEnv:A.none()})))})]),isExtraPart:(e,t)=>tL(t)}));return c.set(g),yf.showWithinBounds(g,fl(m.dialog),{anchor:n},(()=>e.backstages.popup.shared.getSink().toOptional().bind((e=>{const o=Fb(t,e.element).map((e=>Ib(e))).getOr(Zo()),n=Ko(ze(t.getContentAreaContainer())),s=Qo(n,o);return A.some(Xo(s.x,s.y,s.width,s.height-15))})))),_i.refresh(g),t.on("ResizeEditor ScrollWindow ElementScroll ResizeWindow",u),m.instanceApi.setData(l),vh.focusIn(m.dialog),m.instanceApi}),o);return{open:(t,o,n)=>{if(!u(o)){if("toolbar"===o.inline)return a(t,e.backstages.popup.shared.anchors.inlineDialog(),n,o);if("bottom"===o.inline)return i(t,e.backstages.popup.shared.anchors.inlineBottomDialog(),n,o);if("cursor"===o.inline)return a(t,e.backstages.popup.shared.anchors.cursor(),n,o)}return r(t,n)},openUrl:(o,n)=>((o,n)=>YN.openUrl((o=>{const s=Zz(o,{closeWindow:()=>{uR.hide(s.dialog),n(s.instanceApi)}},t,e.backstages.dialog);return uR.show(s.dialog),s.instanceApi}),o))(o,n),alert:(e,t)=>{n.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{s.open(e,t)}}};sn.add("silver",(e=>{(e=>{Ef(e),(e=>{const t=e.options.register,o=e=>f(e,r)?{value:vS(e),valid:!0}:{valid:!1,message:"Must be an array of strings."},n=e=>h(e)&&e>0?{value:e,valid:!0}:{valid:!1,message:"Must be a positive number."};t("color_map",{processor:o,default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_map_background",{processor:o}),t("color_map_foreground",{processor:o}),t("color_cols",{processor:n,default:SS(e)}),t("color_cols_foreground",{processor:n,default:CS(e,fS)}),t("color_cols_background",{processor:n,default:CS(e,bS)}),t("custom_colors",{processor:"boolean",default:!0}),t("color_default_foreground",{processor:"string",default:xS}),t("color_default_background",{processor:"string",default:xS})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:jF(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);let t=()=>Zo();const{dialogs:o,popups:n,renderUI:s}=iR(e,{getPopupSinkBounds:()=>t()});AC(e,n.backstage.shared);const a=nL({editor:e,backstages:{popup:n.backstage,dialog:o.backstage}}),i=nn();return{renderUI:()=>{const o=s();return Fb(e,n.getMothership().element).each((e=>{t=()=>Ib(e)})),o},getWindowManagerImpl:x(a),getNotificationManagerImpl:()=>ay(e,{backstage:n.backstage},n.getMothership(),i)}}))}();