import { parse, resolve } from "path";
import type { ConfigEnv, UserConfig } from "vite";
import { loadEnv } from "vite";
import Vue from "@vitejs/plugin-vue";
import VueJsx from "@vitejs/plugin-vue-jsx";
import EslintPlugin from "vite-plugin-eslint";
import VueI18n from "@intlify/unplugin-vue-i18n/vite";
import { createStyleImportPlugin, ElementPlusResolve, VxeTableResolve } from "vite-plugin-style-import";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import PurgeIcons from "vite-plugin-purge-icons";
import { createHtmlPlugin } from "vite-plugin-html";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import AutoImport from "unplugin-auto-import/vite";
import { createProxy } from "./proxy";
import RefreshPlugin from "./lib/RefreshPlugin";
import I18nTransformer from "@higgins-mmt/vite-plugin-i18n-transformer";
import { SentryVite } from "./lib/VitePluginSentry";
import { visualizer } from "rollup-plugin-visualizer";
import { existsSync, readdirSync, statSync } from "fs";
// https://vitejs.dev/config/
const root = process.cwd()

function pathResolve(dir: string) {
  return resolve(root, '.', dir)
}

async function readElementPlusComponents(): Promise<string[]> {
  const viteDevIncludeList: string[] = [
    'element-plus/es',
  ];
  const elementPlusDir = resolve('node_modules', 'element-plus', 'es', 'components');

  try {
    if (!existsSync(elementPlusDir)) {
      console.warn(`Directory ${elementPlusDir} does not exist.`);
      return viteDevIncludeList;
    }

    const files = readdirSync(elementPlusDir);

    if (files.length === 0) {
      console.warn(`Directory ${elementPlusDir} is empty.`);
      return viteDevIncludeList;
    }

    await Promise.all(
      files.map(async (file) => {
        const filePath = resolve(elementPlusDir, file);
        const stats = statSync(filePath);

        if (stats.isDirectory()) {
          const directoryPath = `element-plus/es/components/${file}/style/index`;
          const dtsPath = resolve('node_modules', `${directoryPath}.d.ts`);

          if (existsSync(dtsPath)) {
            addPathsToIncludeList(viteDevIncludeList, directoryPath, file);
          }
        }
      }),
    );

    return viteDevIncludeList;
  } catch (err) {
    console.error('Error reading Element Plus components:', err);
    throw err;
  }
}

function addPathsToIncludeList(list: string[], directoryPath: string, file: string) {
  list.push(directoryPath);
  list.push(`element-plus/es/components/${file}/style/css`);
}

const now = new Date().getTime(); // 定义一个时间戳

export default async ({command, mode}: ConfigEnv): Promise<UserConfig> => {
  const viteDevIncludeList: string[] = await readElementPlusComponents()
  let env: Record<string, string>
  const isBuild = command === 'build'
  if (!isBuild) {
    env = loadEnv((process.argv[3] === '--mode' ? process.argv[4] : process.argv[3]), root)
  } else {
    env = loadEnv(mode, root)
  }
  const isVisualizer = isBuild && env.VITE_VISUALIZER === "true"

  const proxy = createProxy(env)
  return {
    base: env.VITE_BASE_PATH,
    define: {
      __APP_VERSION__: now,
    },
    plugins: [
      Vue(),
      VueJsx(),
      isVisualizer ? visualizer({
        emitFile: true,
        open: true,
      }) : null,
      I18nTransformer({
        include: ['**.js', '**.jsx', '**.ts', '**.tsx', '**.vue'],
        exclude: ['src/locales/**', 'node_modules/**', 'src/store/modules/locale.ts'],
        i18nCallee: 'useI18n().t',
        dependency: {
          name: 'useI18n',
          path: '@/hooks/web/useI18n',
          objectPattern: true,
        },
        output: {
          filename: 'zh-CN.json',
          langList: ['en-US.json'],
          path: pathResolve('public/assets/locales'),
        },
      }),
      SentryVite(env, isBuild || mode !== 'uat'),
      RefreshPlugin({version: now}),
      createStyleImportPlugin({
        resolves: [ElementPlusResolve(), VxeTableResolve()],
        libs: [{
          libraryName: 'element-plus',
          esModule: true,
          resolveStyle: (name) => {
            return `element-plus/es/components/${name.substring(3)}/style/css`
          }
        }]
      }),
      EslintPlugin({
        cache: false,
        include: ['src/**/*.vue', 'src/**/*.ts', 'src/**/*.tsx'] // 检查的文件
      }),
      VueI18n({
        runtimeOnly: true,
        compositionOnly: true,
        include: [resolve(__dirname, 'src/locales/**')]
      }),
      createSvgIconsPlugin({
        iconDirs: [pathResolve('src/assets/svgs')],
        symbolId: 'icon-[dir]-[name]',
        svgoOptions: true
      }),
      PurgeIcons(),
      createHtmlPlugin({
        inject: {
          data: {
            title: env.VITE_APP_TITLE,
            injectScript: `<script src="./inject.js"></script>`,
          }
        }
      }),
      Components({
        dirs: pathResolve('src/components'),
        resolvers: [
          ElementPlusResolver({
            exclude: /(ElCascader)/,
          }),
        ],
        dts: resolve('src/components.d.ts'),
      }),
      AutoImport({
        resolvers: [
          ElementPlusResolver()
        ],
        imports: ['vue', 'vue-router', 'vue-i18n', '@vueuse/head', '@vueuse/core'],
        dirs: ['src/hooks/autoImport', 'src/constants'],
        dts: 'src/auto-import.d.ts',
        eslintrc: {
          enabled: false,
          filepath: './.eslintrc-auto-import.json',
          globalsPropValue: true,
        },
      }),
    ],
    css: {
      preprocessorOptions: {
        less: {
          additionalData: '@import "./src/styles/variables.module.less";',
          javascriptEnabled: true
        }
      }
    },
    resolve: {
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.less', '.css'],
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js'
        },
        {
          find: /@\//,
          replacement: `${pathResolve('src')}/`
        },
        {
          find: 'path',
          replacement: 'path-browserify'
        }
      ]
    },
    build: {
      minify: 'esbuild',
      outDir: 'dist',
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes('xlsx')) {
              return 'xlsx';
            }
            if (id.includes('sortablejs')) {
              return 'sortablejs';
            }
            if (id.includes('jsondiffpatch') || id.includes('diff-match-patch')) {
              return 'json-diff-patch';
            }
            if (id.includes('zxcvbn-ts') ||
              id.includes('src/components/Form') ||
              id.includes('src/components/Search') ||
              id.includes('@wangeditor/editor') ||
              id.includes('@wangeditor/editor-for-vue') ||
              id.includes('src/components/Editor') ||
              id.includes('src/components/InputPassword') ||
              id.includes('src/components/ZoneTime')) {
              return 'search-form';
            }
            if (id.includes('vue-cropper')) {
              return 'vue-cropper';
            }
            if (id.includes('src/store') ||
              id.includes('src/hooks') ||
              id.includes('src/utils') ||
              id.includes('src/views/basic-library-manage/store/dict.ts') ||
              id.includes('src/views/basic-library-manage/api/common.ts')) {
              return 'common'
            }

            const productInfo = ([
              'src/views/basic-library-manage/product-library/api/productInfo.ts',
              'src/views/basic-library-manage/product-library/components/BillingInfo.vue',
              'src/views/basic-library-manage/product-library/components/ColorSampleInfo.vue',
              'src/views/basic-library-manage/product-library/components/DesignInfo.vue',
              'src/views/basic-library-manage/product-library/components/DistributeInfo.vue',
              'src/views/basic-library-manage/product-library/components/InfringementInfo.vue',
              'src/views/basic-library-manage/product-library/components/MaterialInfo.vue',
              'src/views/basic-library-manage/product-library/components/OperationInfo.vue',
              'src/views/basic-library-manage/product-library/components/PackingInfo.vue',
              'src/views/basic-library-manage/product-library/components/SelectionInfo.vue',
              'src/views/basic-library-manage/product-library/components/SkcInfo.vue',
              'src/views/basic-library-manage/product-library/ProductInfo.vue'
            ]);
            if (productInfo.some(e => id.includes(e))) {
              return 'product-info';
            }

            if (
              id.includes('vxe-table') ||
              id.includes('vxe-pc-ui') ||
              id.includes('@vxe-ui') ||
              id.includes('xe-utils') ||
              id.includes('src/components') ||
              id.includes('src/main.ts') ||
              id.includes('src/utils/propTypes.ts') ||
              id.includes('src/hooks/autoImport/useDictAll') ||
              id.includes('echarts')
            ) {
              return 'common';
            }

            if (id.includes('node_modules')) {
              return 'vendors';
            }

            return null;
          },
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const {ext} = parse(assetInfo.name);
            const img = ['.jpg', '.svg', '.png', '.gif', '.jpeg'];
            const font = ['.woff', '.woff2', '.ttf'];
            if (img.includes(ext)) {
              return `assets/images/[name]-[hash].[ext]`;
            }
            if (font.includes(ext)) {
              return `assets/fonts/[name]-[hash].[ext]`;
            }
            return 'assets/[ext]/[name]-[hash].[ext]';
          },
        }
      }
    },
    server: {
      port: 4000,
      proxy,
      hmr: {
        overlay: false
      },
      host: '0.0.0.0'
    },
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'vue-types',
        '@iconify/iconify',
        '@vueuse/core',
        'axios',
        'qs',
        'echarts',
        'echarts-wordcloud',
        '@wangeditor/editor',
        '@wangeditor/editor-for-vue',
        ...viteDevIncludeList,
      ]
    }
  }
}
