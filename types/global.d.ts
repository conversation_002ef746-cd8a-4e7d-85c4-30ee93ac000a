import { LocaleEnum } from '@/store/modules/locale'
import { VNode } from 'vue'

declare global {
  declare interface Fn<T = any> {
    (...arg: T[]): T
  }

  namespace JSX {
    type Element = VNode
    interface ElementClass {
      $props: any
    }
    interface IntrinsicElements {
      [elem: string]: any
    }
  }

  declare const __APP_VERSION__: string

  declare const tinymce: import('tinymce').TinyMCE

  declare type Nullable<T> = T | null

  declare type ElRef<T extends HTMLElement = HTMLDivElement> = Nullable<T>

  declare type Recordable<T = any, K = string> = Record<K extends null | undefined ? string : K, T>

  declare type ComponentRef<T> = InstanceType<T>

  declare type LocaleType = LocaleEnum.ZH_CN | LocaleEnum.EN_US

  declare type AxiosHeaders =
    | 'application/json'
    | 'application/x-www-form-urlencoded'
    | 'multipart/form-data'

  declare type AxiosMethod = 'get' | 'post' | 'delete' | 'put'

  declare type AxiosResponseType = 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream'

  declare interface AxiosConfig {
    params?: any
    data?: any
    url?: string
    method?: AxiosMethod
    headersType?: string
    responseType?: AxiosResponseType
  }

  declare interface IResponse<T = any> {
    /**
     * 返回标记：成功标记=0，其余都是失败
     */
    code: string
    data: T extends any ? T : T & any
    msg?: string
  }

  declare interface TempIResponse<T = any> {
    code: string
    records: T extends any ? T : T & any
  }
  declare interface pager {
    current?: number
    size?: number
    pages?: number
    total?: number
  }

  declare interface ErrorTip {
    code?: string
    datas?: any
    isSuccess?: boolean
    msg?: string
  }
  declare interface Window {
    _paq: any
  }

  /**
   * 普通请求
   */
  declare type ResponseDataType<T> = IResponse & { datas: T extends any ? T : T & any }
  /**
   * 分页请求
   */
  declare type ResponsePageDataType<L, T = Record<string, number | string>> = IResponse<page<L, T>>
}

export {}
