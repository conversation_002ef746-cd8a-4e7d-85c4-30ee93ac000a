# PDM - 产品信息管理系统

## 基础框架

基于 [vue-element-plus-admin v1.8.1](https://github.com/kailong321200875/vue-element-plus-admin)

目录结构参考 [官方文档](https://element-plus-admin-doc.cn/guide/introduction.html)

---

## 开发环境

- **Node.js**: 18.18.0
- **pnpm**: ≥ 8.6.13

---

## 接口文档

https://app.apifox.com/project/3741260

## 发版构建

[测试环境](https://devops.wangoon.cn/jenkins/job/TEST.front/job/pdm-admin/)

[UAT 环境](https://flow.aliyun.com/pipelines/2788104/current)

[生产环境](https://flow.aliyun.com/pipelines/2795125/current)

## 常见问题

### Q: 页面出现乱码

**A:** 项目使用 [@kapo/vite-plugin-i18n-transformer](https://codeup.aliyun.com/61ee8bb874c3f3b55073ffd0/JS/i18n-transformer) 自动国际化插件，翻译资源在 `src/permission.ts` 的路由守卫中从[国际化平台](https://higgins-console.test.wangoon.cn/international/translate-manage)加载。如果某些组件先于路由守卫加载，可能会导致页面乱码。

解决方案：

- 一般发生在和 `<RouterView />` 同级或父级的布局组件中，可以手动翻译组件内容。
- 待官方稳定 Suspense 组件后，可使用 Suspense 组件。

---

### Q: 路由配置

**A:** 项目使用 [PMS](https://pms.test.wangoon.cn/menu) 动态生成路由，无需手动配置。

---

### Q: 接口代理

**A:** 根目录下的 `proxy.ts` 文件用于开发环境接口代理配置，解决跨域问题。正式环境通过 nginx 配置跨域。

---

## 常用组件和 Hooks

### 组件

#### 1. `src/components/Business/SelectPlus`

- 基于 [el-select](https://element-plus.org/zh-CN/component/select.html) 封装，实现下拉框数据接口调用、缓存和联动功能。
- 添加新接口步骤：
  1. 在 `src/components/Business/SelectPlus/src/api/Enum.ts` 中新增接口方法。
  2. 在 `src/components/Business/SelectPlus/src/api/types.ts` 中定义接口类型。
  3. 在 `src/components/Business/SelectPlus/src/apiMapList.ts` 中新增配置：
     - **key**: 下拉数据的 key。
     - **label**: 显示值。
     - **value**: 对应值。
     - **disabled**: 禁用值。
  4. 如果接口返回的不是对象数组类型，在 `apiMapList.ts` 中将 `oneDimension` 配置为 `true`。

---

#### 2. `src/components/Upload/OssUpload.vue`

- 基于 [el-upload](https://element-plus.org/zh-CN/component/upload.html) 实现文件上传至 OSS 的功能。
- 双向绑定的值为 `BaseFileDTO[]` 类型，必传参数为 `signatureUrl`。
- 其他使用方法参考官方文档和项目示例。

---

#### 3. `src/components/Tinymce`

- 基于 [tinymce-vue](https://www.tiny.cloud/docs/tinymce/latest/getting-started/) 实现图片上传至 OSS 的功能。

---

#### 4. `src/components/TooltipClamp`

- 文本超长时显示省略号，鼠标悬停时展示 Tooltip。
- 基于 [vue-clamp](https://github.com/Justineo/vue-clamp) 实现。

---

#### 5. `src/plugins/vxeTable/renderer`

- 表格渲染器，封装了一些常用的单元格 Render。
- `image.tsx`，图片渲染器，图片预览，支持数据结构为 `string | string[] | BaseFileDTO | BaseFileDTO[]`。
  - 参考 `src/views/ProductSkcInfo/index.vue`
- `file.tsx`，文件渲染器，文件预览，支持数据结构为 `string | string[] | BaseFileDTO | BaseFileDTO[]`。
  - 参考 `src/views/product-calendar/product-dev-progress/index.vue`
- `dict.tsx`，字典渲染器，展示字典对应的展示值。
  - 参考 `src/views/basic-library-manage/last-library/last-list.vue`

---

### Hooks

#### 1. `src/hooks/autoImport/useLocaleConfig.ts`

- 处理不同语言下的配置，例如表单 label 宽度。
- 按照 `localeConfigList` 中语言顺序传入配置。

---

#### 2. `src/hooks/web/useTableHeight.ts`

- 表格高度根据页面自适应调整。

---

#### 3. `src/views/basic-library-manage/store/dict.ts`

- 产品基础库模块的字典数据。
- 通过 `ListKeyEnum` 自动生成 “**小驼峰 + List/Map**” 字典数据。
- **注意**: 不推荐使用 `storeToRefs` 解构，这会导致全量请求所有字典数据，建议使用 `computed` 计算属性替代。

---

异步错误处理规范推荐使用 [await-to-js](https://github.com/scopsy/await-to-js#readme) 库将异步方法包裹一层，无需 `try ... catch`，更优雅的处理异步错误。在 `src/config/fetch/Enum.ts` 中封装了四个通用 [Axios](https://axios-http.com/zh/) 请求方法，推荐使用统一的方法调用接口。

## 快速开发

系统支持三种报表开发模式：

1. **配置生成模式**

   - 使用 `src/hooks/web/useCrudSchemas` 通过配置 JSON 生成表单和表格。
   - 示例：`src/views/ProductList/index.vue`

2. **Hook 封装模式**

   - 使用 `src/hooks/autoImport/usePage.ts`，提供常见操作的封装，类型完善，自定义 DOM 结构。
   - 示例：`src/views/ProductSkcInfo/index.vue`

3. **自定义模式**
   - 不使用以上两种模式，自行编写 DOM 结构和逻辑。

根据需求选择合适的开发模式即可。
